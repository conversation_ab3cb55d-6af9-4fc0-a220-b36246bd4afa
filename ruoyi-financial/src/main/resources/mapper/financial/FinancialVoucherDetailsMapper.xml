<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.financial.mapper.FinancialVoucherDetailsMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.financial.domain.FinancialVoucherDetails">
        <!--@mbg.generated-->
        <!--@Table financial_voucher_details-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="voucher_id" jdbcType="INTEGER" property="voucherId"/>
        <result column="summary" jdbcType="VARCHAR" property="summary"/>
        <result column="subject_id" jdbcType="INTEGER" property="subjectId"/>
        <result column="subject_name" jdbcType="VARCHAR" property="subjectName"/>
        <result column="subject_code" jdbcType="VARCHAR" property="subjectCode"/>
        <result column="debit_amount" jdbcType="DOUBLE" property="debitAmount"/>
        <result column="credit_amount" jdbcType="DOUBLE" property="creditAmount"/>
        <result column="auxiliary_title" jdbcType="VARCHAR" property="auxiliaryTitle"/>
        <result column="num" jdbcType="DOUBLE" property="num"/>
        <result column="price" jdbcType="DOUBLE" property="price"/>
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="cumulative_debit" jdbcType="DOUBLE" property="cumulativeDebit"/>
        <result column="cumulative_credit" jdbcType="DOUBLE" property="cumulativeCredit"/>
        <result column="cumulative_debit_num" jdbcType="DOUBLE" property="cumulativeDebitNum"/>
        <result column="cumulative_credit_num" jdbcType="DOUBLE" property="cumulativeCreditNum"/>
        <result column="balance_direction" jdbcType="VARCHAR" property="balanceDirection"/>
        <result column="carry_forward" jdbcType="BIT" property="carryForward"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, voucher_id, summary, subject_id, subject_name, subject_code, debit_amount, credit_amount,
        auxiliary_title, num, price, account_sets_id, cumulative_debit, cumulative_credit,
        cumulative_debit_num, cumulative_credit_num, carry_forward
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into financial_voucher_details
        (voucher_id, summary, subject_id, subject_name, subject_code, debit_amount, credit_amount,
        auxiliary_title, num, price, account_sets_id, cumulative_debit, cumulative_credit,
        cumulative_debit_num, cumulative_credit_num, carry_forward)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.voucherId,jdbcType=INTEGER}, #{item.summary,jdbcType=VARCHAR}, #{item.subjectId,jdbcType=INTEGER},
            #{item.subjectName,jdbcType=VARCHAR}, #{item.subjectCode,jdbcType=VARCHAR}, #{item.debitAmount,jdbcType=DOUBLE},
            #{item.creditAmount,jdbcType=DOUBLE}, #{item.auxiliaryTitle,jdbcType=VARCHAR},
            #{item.num,jdbcType=DOUBLE}, #{item.price,jdbcType=DOUBLE}, #{item.accountSetsId,jdbcType=INTEGER},
            #{item.cumulativeDebit,jdbcType=DOUBLE}, #{item.cumulativeCredit,jdbcType=DOUBLE},
            #{item.cumulativeDebitNum,jdbcType=DOUBLE}, #{item.cumulativeCreditNum,jdbcType=DOUBLE},
            #{item.carryForward,jdbcType=BIT})
        </foreach>
    </insert>
    <resultMap id="FullResultMap" type="com.ruoyi.financial.domain.FinancialVoucherDetails">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="voucher_id" jdbcType="INTEGER" property="voucherId"/>
        <result column="summary" jdbcType="VARCHAR" property="summary"/>
        <result column="subject_id" jdbcType="INTEGER" property="subjectId"/>
        <result column="subject_name" jdbcType="VARCHAR" property="subjectName"/>
        <result column="subject_code" jdbcType="VARCHAR" property="subjectCode"/>
        <result column="debit_amount" jdbcType="DOUBLE" property="debitAmount"/>
        <result column="credit_amount" jdbcType="DOUBLE" property="creditAmount"/>
        <result column="auxiliary_title" jdbcType="VARCHAR" property="auxiliaryTitle"/>
        <result column="num" jdbcType="DOUBLE" property="num"/>
        <result column="price" jdbcType="DOUBLE" property="price"/>
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="cumulative_debit" jdbcType="DOUBLE" property="cumulativeDebit"/>
        <result column="cumulative_credit" jdbcType="DOUBLE" property="cumulativeCredit"/>
        <result column="cumulative_debit_num" jdbcType="DOUBLE" property="cumulativeDebitNum"/>
        <result column="cumulative_credit_num" jdbcType="DOUBLE" property="cumulativeCreditNum"/>
        <association column="subject_id" javaType="com.ruoyi.financial.domain.FinancialSubject" property="subject" select="com.ruoyi.financial.mapper.FinancialSubjectMapper.selectById"/>
    </resultMap>

    <select id="selectCarryForwardMoney" resultMap="BaseResultMap">
        select
        ffvd.voucher_id,
        ffs.balance_direction,
        sum(ffvd.debit_amount) debit_amount,
        sum(ffvd.credit_amount) credit_amount
        from financial_voucher_details ffvd
        left join financial_voucher ffv on ffvd.voucher_id = ffv.id
        left join financial_subject ffs on ffvd.subject_id = ffs.id
        where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year=#{years,jdbcType=INTEGER}
        and ffv.voucher_month=#{month,jdbcType=INTEGER}
        and ffvd.subject_code like #{code,jdbcType=VARCHAR}
        and ffv.valid=1
    </select>

    <select id="selectFinalCheckData" resultMap="BaseResultMap">
        select sum(ffvd.credit_amount) credit_amount,sum(ffvd.debit_amount) debit_amount
        from financial_voucher_details ffvd
        left join financial_voucher ffv on ffvd.voucher_id = ffv.id and ffv.valid=1
        where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffv.voucher_year = #{year,jdbcType=INTEGER} and ffv.voucher_month = #{month,jdbcType=INTEGER}
    </select>

    <select id="assetStatistics" resultMap="BaseResultMap">
        select ffs.type subject_name, sum(ffvd.credit_amount) credit_amount, sum(ffvd.debit_amount) debit_amount
        from financial_voucher_details ffvd
        left join financial_voucher ffv on ffvd.voucher_id = ffv.id and ffv.valid=1
        left join financial_subject ffs on ffvd.subject_id = ffs.id
        where
        ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffv.voucher_date <![CDATA[<=]]> #{voucherDate,jdbcType=TIMESTAMP} and (ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER} or ffvd.voucher_id is null)
        and ffs.type in ('资产','负债','权益') group by ffs.type
    </select>

    <select id="selectTopSummary" resultType="java.lang.String">
        select t.summary from (select trim(summary) summary,count(1) num from financial_voucher_details where account_sets_id = #{accountSetsId,jdbcType=INTEGER} group by trim(summary)) t order by t.num desc limit 10
    </select>

    <select id="selectBalanceData" resultType="com.ruoyi.financial.vo.VoucherDetailVo">
        select sum(ffvd.credit_amount) credit_amount,sum(ffvd.debit_amount) debit_amount,ffs.balance_direction
        from financial_voucher_details ffvd
        left join financial_voucher ffv on ffvd.voucher_id = ffv.id and ffv.valid= 1
        left join financial_subject ffs on ffvd.subject_id = ffs.id
        <if test="categoryId != null">
            left join financial_voucher_details_auxiliary ffvda on ffvda.voucher_details_id = ffvd.id
        </if>
        where
        ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffvd.subject_id = #{subjectId,jdbcType=INTEGER}
        <if test="categoryId != null">
            and ffvda.accounting_category_id = #{categoryId,jdbcType=INTEGER}
            and ffvda.accounting_category_details_id = #{categoryDetailsId,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectListInitialCheckData" resultType="java.util.Map">
        select ROUND(ifnull(sum(ffvd.debit_amount),0.00), 2) debit_amount, ROUND(ifnull(sum(ffvd.credit_amount),0.00), 2) credit_amount
        from financial_voucher_details ffvd
        left join financial_subject ffs on ffvd.subject_id = ffs.id
        where ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffvd.voucher_id is null
        and ( ffvd.debit_amount is not null or ffvd.credit_amount is not null)
        group by ffvd.account_sets_id
    </select>

    <select id="selectBalanceList" resultMap="BaseResultMap">
        select ffvd.*
        from financial_voucher_details ffvd
        left join financial_subject ffs on ffvd.subject_id = ffs.id
        where ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffvd.voucher_id is null and ffs.type = #{type,jdbcType=VARCHAR}
    </select>

    <select id="selectBassetsAndLiabilities" resultType="java.util.Map">
        select IF(ffs.type = '资产', '资产', '权益') as type, ffs.balance_direction, ROUND(sum(ffvd.debit_amount), 2) debit_amount, ROUND(sum(ffvd.credit_amount), 2) credit_amount
        from financial_voucher_details ffvd
        left join financial_subject ffs on ffvd.subject_id = ffs.id
        where ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffvd.voucher_id is null
        and (ffvd.debit_amount is not null or ffvd.credit_amount is not null)
        and ffs.type in ('资产', '负债', '权益')
        group by IF(ffs.type = '资产', '资产', '权益'), ffs.balance_direction
    </select>

    <select id="selectAuxiliaryList" resultMap="FullResultMap">
        select ffvd.*
        from financial_voucher_details ffvd
        left join financial_subject ffs on ffvd.subject_id = ffs.id
        where ffvd.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffvd.voucher_id is null
        and length(ffvd.auxiliary_title) &gt; 0 and ffs.type = #{type,jdbcType=VARCHAR}
    </select>

    <select id="selectAggregateAmount" resultMap="BaseResultMap">
        select ffvd.subject_code,sum(ffvd.debit_amount) debit_amount,sum(ffvd.credit_amount) credit_amount,sum(num) num from financial_voucher_details ffvd
        left join financial_voucher ffv on ffvd.voucher_id = ffv.id and ffv.valid=1
        where
        ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffv.voucher_year = #{year}
        and ffv.voucher_month = #{month}
        and ffvd.subject_code in
        <foreach close=")" collection="codeList" index="index" item="id" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        group by ffvd.subject_code
    </select>

    <select id="selectVoucherDetails" resultMap="BaseResultMap">
        select *
        from financial_voucher_details
        where voucher_id = #{voucherId}
    </select>

    <update id="updateVoucherBySubjectId">
        update financial_voucher_details fvd, financial_subject fs
        set fvd.subject_code=fs.`code`, fvd.subject_name=if(fs.parent_id=0 || fvd.summary='期初',concat(fs.`code`,'-',fs.`name`), concat(fs.`code`, '-',(select `name` from financial_subject where id= fs.parent_id),'-',fs.`name`))
        where fvd.subject_id = fs.id
        and fs.id=#{subjectId}
    </update>

    <select id="checkUse" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        select count(1) from financial_voucher fv
        inner join financial_voucher_details fvd on fv.id=fvd.voucher_id
        where fv.valid=1 and fvd.subject_id=#{subjectId}
    </select>

    <select id="selectBySubjectId" parameterType="java.lang.Integer" resultType="com.ruoyi.financial.po.CheckUsePo">
        select fv.valid, fvd.id AS voucherDetailId, fvd.summary
        from financial_voucher_details fvd
        left join financial_voucher fv on fv.id=fvd.voucher_id
        where fvd.subject_id=#{subjectId}
    </select>

    <!--更新凭证对应科目信息-->
    <update id="updateVoucherMapSub">
        update financial_voucher_details fvd, financial_subject fs
        set fvd.subject_code=fs.`code`, fvd.subject_name=
            CASE
                WHEN fs.parent_id= 0 OR fvd.summary='期初' THEN concat(fs.`code`,'-',fs.`name`)
                WHEN fs.`level` = 2 THEN concat(fs.`code`, '-',(select `name` from financial_subject where id= fs.parent_id),'-',fs.`name`)
                WHEN fs.`level` = 3 THEN concat(fs.`code`, '-',(select `name` from financial_subject where id= (select parent_id from financial_subject where id= fs.parent_id)),'-', (select `name` from financial_subject where id= fs.parent_id),'-',fs.`name`)
            END
        where fvd.account_sets_id = #{accountSetsId} and fvd.subject_id = fs.id;
    </update>

    <!--科目日统计-->
    <select id="selectSubjectDaySts" resultType="com.ruoyi.financial.po.SubjectDayStsPo">
        select fv.account_sets_id accountSetsId, fas.company_name accountSetsName, fv.voucher_date voucherDate, fv.voucher_year voucherYear, fv.voucher_month voucherMonth,
               fvd.subject_id subjectId, fvd.subject_code subjectCode, fvd.subject_name subjectName, sum(ifnull(fvd.debit_amount,0.00)) debitAmount, sum(ifnull(fvd.credit_amount,0.00)) creditAmount
        from financial_voucher fv
        inner join financial_voucher_details fvd on fv.id = fvd.voucher_id and fv.valid=1
        inner join financial_account_sets fas on fvd.account_sets_id = fas.id
        where fv.voucher_date = #{dateStat}
        group by fvd.account_sets_id, fvd.subject_id
    </select>

    <!--根据凭证ID删除凭证明细-->
    <delete id="deleteByVoucherId">
        delete from financial_voucher_details where voucher_id = #{voucherId}
    </delete>
</mapper>
