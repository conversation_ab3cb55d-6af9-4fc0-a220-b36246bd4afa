<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.financial.mapper.FinancialU8CVoucherSetMapper">

    <resultMap type="FinancialU8CVoucherSet" id="FinancialU8CVoucherSetResult">
        <result property="voucherId"    column="voucher_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="pkVoucher"    column="pk_voucher"    />
        <result property="u8cVouchertypeName"    column="u8c_vouchertype_name"    />
        <result property="u8cNo"    column="u8c_no"    />
        <result property="pushStatus"    column="push_status"    />
        <result property="u8cReturnMessage"    column="u8c_return_message"    />
        <result property="pushTime"    column="push_time"    />
        <result property="pushBy"    column="push_by"    />
    </resultMap>

    <resultMap type="FinancialVoucher" id="FinancialVoucherResult">
        <result property="id"    column="id"    />
        <result property="source"    column="source"    />
        <result property="word"    column="word"    />
        <result property="code"    column="code"    />
        <result property="remark"    column="remark"    />
        <result property="receiptNum"    column="receipt_num"    />
        <result property="createMember"    column="create_member"    />
        <result property="createDate"    column="create_date"    />
        <result property="debitAmount"    column="debit_amount"    />
        <result property="creditAmount"    column="credit_amount"    />
        <result property="accountSetsId"    column="account_sets_id"    />
        <result property="voucherYear"    column="voucher_year"    />
        <result property="voucherMonth"    column="voucher_month"    />
        <result property="voucherDate"    column="voucher_date"    />
        <result property="auditMemberId"    column="audit_member_id"    />
        <result property="auditMemberName"    column="audit_member_name"    />
        <result property="auditDate"    column="audit_date"    />
        <result property="carryForward"    column="carry_forward"    />
        <result property="valid"    column="valid"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="FinancialVoucherDetails" id="FinancialVoucherDetailsResult">
        <result property="id"    column="id"    />
        <result property="voucherId"    column="voucher_id"    />
        <result property="summary"    column="summary"    />
        <result property="subjectId"    column="subject_id"    />
        <result property="subjectName"    column="subject_name"    />
        <result property="subjectCode"    column="subject_code"    />
        <result property="debitAmount"    column="debit_amount"    />
        <result property="creditAmount"    column="credit_amount"    />
        <result property="auxiliaryTitle"    column="auxiliary_title"    />
        <result property="num"    column="num"    />
        <result property="price"    column="price"    />
        <result property="accountSetsId"    column="account_sets_id"    />
        <result property="cumulativeDebit"    column="cumulative_debit"    />
        <result property="cumulativeCredit"    column="cumulative_credit"    />
        <result property="cumulativeDebitNum"    column="cumulative_debit_num"    />
        <result property="cumulativeCreditNum"    column="cumulative_credit_num"    />
        <result property="carryForward"    column="carry_forward"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFinancialU8CVoucherSetVo">
        select voucher_id, rule_id, pk_voucher, u8c_vouchertype_name, u8c_no, push_status, u8c_return_message, push_time, push_by from financial_u8c_voucher_set
    </sql>

    <select id="selectFinancialU8CVoucherSetList" parameterType="FinancialU8CVoucherSet" resultMap="FinancialU8CVoucherSetResult">
        <include refid="selectFinancialU8CVoucherSetVo"/>
        <where>
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
            <if test="u8cVouchertypeName != null  and u8cVouchertypeName != ''"> and u8c_vouchertype_name like concat('%', #{u8cVouchertypeName}, '%')</if>
        </where>
    </select>

    <select id="selectFinancialVoucherList" parameterType="FinancialVoucher" resultMap="FinancialVoucherResult">
        SELECT
        vou.id,
        vou.source,
        vou.word,
        vou.CODE,
        vou.remark,
        vou.receipt_num,
        vou.create_member,
        vou.create_date,
        vou.debit_amount,
        vou.credit_amount,
        vou.account_sets_id,
        vou.voucher_year,
        vou.voucher_month,
        vou.voucher_date,
        vou.audit_member_id,
        vou.audit_member_name,
        vou.audit_date,
        vou.carry_forward,
        vou.valid,
        vou.create_time,
        vou.update_time
        FROM
        financial_voucher vou
        LEFT JOIN
        financial_voucher_details det on det.voucher_id = vou.id
        LEFT JOIN
        financial_subject sub on det.subject_id = sub.id
       <where>
            vou.valid = 1 and vou.audit_member_id is not null
            <if test="accountSetsId != null "> and vou.account_sets_id = #{accountSetsId}</if>
            <if test="auditMemberId != null "> and vou.audit_member_id = #{auditMemberId}</if>
            <if test="auditMemberName != null  and auditMemberName != ''"> and vou.audit_member_name like concat('%', #{auditMemberName}, '%')</if>
            <if test="auditDate != null "> and vou.audit_date = #{auditDate}</if>
            <if test="carryForward != null "> and vou.carry_forward = #{carryForward}</if>
            <if test="valid != null "> and vou.valid = #{valid}</if>
            <if test="subjectName != null  and subjectName != ''"> and det.subject_name like concat('%', #{subjectName}, '%')</if>
            <if test="subjectCode != null  and subjectCode != ''"> and det.subject_code like concat('%', #{subjectCode}, '%')</if>
            <if test="summary != null  and summary != ''"> and det.summary like concat('%', #{summary}, '%')</if>
            <if test="type != null  and type != ''"> and sub.type = #{type}</if>
            <if test="startTime != null and endTime != null "> and vou.voucher_date between #{startTime} and #{endTime}</if>
        </where>
        group by vou.id
    </select>

    <select id="selectDetDataByVoucher" parameterType="FinancialVoucherDetails" resultMap="FinancialVoucherDetailsResult">
        SELECT
            id,
            voucher_id,
            summary,
            subject_id,
            subject_name,
            subject_code,
            debit_amount,
            credit_amount,
            auxiliary_title,
            num,
            price,
            account_sets_id,
            cumulative_debit,
            cumulative_credit,
            cumulative_debit_num,
            cumulative_credit_num,
            carry_forward,
            create_time,
            update_time
        FROM
            financial_voucher_details
        <where>
            voucher_id in (
            <foreach item="item" index="index" collection="vou" separator=",">
                #{item.id}
            </foreach>)
        </where>
    </select>

    <select id="selectFinancialU8CVoucherSetByVoucherId" parameterType="Long" resultType="FinancialU8CVoucherSet">
        SELECT
            vou.voucher_id voucherId,
            vou.push_type pushType,
            vou.rule_id ruleId,
            vou.push_status pushStatus,
            vou.failure_reason failureReason,
            vou.u8c_no u8cNo,
            us.nick_name pushBy,
            vou.push_time pushTime
        FROM
            financial_u8c_voucher_set vou left join
            sys_user us on us.user_name = vou.push_by
        WHERE
            vou.voucher_id = #{voucherId} order by vou.push_time desc
    </select>

    <select id="selectOaTrader" resultType="FinancialU8CRuleDetails">
        SELECT
            bank_of_deposit bankOfDeposit,
            account_number accountNumber,
            abbreviation
        FROM
            oa_trader
        WHERE
            account_number = #{vor} and del_flag = '0'
    </select>

    <select id="selectSubjectBySubjectId" parameterType="FinancialVoucherDetails" resultType="FinancialVoucherDetails">
        SELECT
            id subjectId,
            balance_direction balanceDirection,
            type
        FROM
            financial_subject
        WHERE
            id IN
        <foreach item="voucherDetails" collection="voucherDetails" open="(" separator="," close=")">
            #{voucherDetails.subjectId}
        </foreach>
    </select>

    <select id="selectU8CGlorgbook" parameterType="Integer" resultType="PushU8CLedger">
        SELECT
            pk_glorgbook pkGlorgbook,
            glorgbook_code glorgbookCode,
            u8c_corp pkEntityorg
        FROM
            financial_u8c_account_glorgbook
        WHERE
            account_id = #{accountSetsId}
          AND `state` = '0'
    </select>

    <select id="selectPhoneByUserName" parameterType="String" resultType="String">
        select phone_num from rs_personnel_archives where sys_name = #{userName} limit 1
    </select>

    <select id="selectCreateTimeByVoucherId" parameterType="Integer" resultType="String">
        SELECT
            fo.create_name
        FROM
            oa_pfd_vouchar_details pfdvo left join proc_form_data fo on pfdvo.pfd_id = fo.business_id
        WHERE pfdvo.vouchar_id = #{id}
        group by pfdvo.vouchar_id limit 1
    </select>

    <select id="selectPushRecords" parameterType="FinancialVoucher" resultType="com.ruoyi.financial.domain.FinancialU8CVoucherSet">
        SELECT
            voucher_id voucherId,
            SUM(CASE WHEN push_type = 1 AND push_status != 1 THEN 1 ELSE 0 END) AS aloneCount,
            SUM(CASE WHEN push_type = 2 AND push_status != 1 THEN 1 ELSE 0 END) AS mergeCount,
            SUM(CASE WHEN push_status = 1 THEN 1 ELSE 0 END) AS failureCount
        FROM
            financial_u8c_voucher_set
        where voucher_id in
        <foreach item="item" collection="vou" open="(" separator="," close=")">
            #{item.id}
        </foreach>
        GROUP BY
            voucher_id;
    </select>
    <select id="selectProjectBySubject" resultType="com.ruoyi.financial.domain.FinancialU8CRuleDetails">
        select project_name projectName from fc_subject_sts where subject_id = #{subjectId}
    </select>
    <select id="selectProjectBySubjectName" resultType="com.ruoyi.financial.domain.FinancialU8CRuleDetails">
        SELECT project_name projectName,id projectId
        FROM oa_project_deploy
        WHERE INSTR(#{subjectName}, project_name) > 0 limit 1;
    </select>

    <select id="selectVouByIds" resultType="com.ruoyi.financial.domain.FinancialVoucher">
        SELECT
        vou.id,
        vou.source,
        vou.word,
        vou.CODE,
        vou.remark,
        vou.receipt_num,
        vou.create_member,
        vou.create_date,
        vou.debit_amount,
        vou.credit_amount,
        vou.account_sets_id,
        vou.voucher_year,
        vou.voucher_month,
        vou.voucher_date,
        vou.audit_member_id,
        vou.audit_member_name,
        vou.audit_date,
        vou.carry_forward,
        vou.valid,
        vou.create_time,
        vou.update_time
        FROM
        financial_voucher vou
        LEFT JOIN
        financial_voucher_details det on det.voucher_id = vou.id
        LEFT JOIN
        financial_subject sub on det.subject_id = sub.id
        <where>
            vou.id in
            <foreach item="item" index="index" collection="vou" open="(" separator="," close=")">
            #{item}
            </foreach>
        </where>
        group by vou.id
    </select>

    <select id="selectBankByNumber" resultType="com.ruoyi.financial.domain.FinancialU8CRuleDetails">
        SELECT u8c_account accountNumber FROM financial_u8c_bankaccount where account_number = #{accountNumber} and account_id = #{accountSetsId} and state = '0'
    </select>

    <select id="selectKSByKSName" resultType="com.ruoyi.financial.domain.FinancialU8CCustomersSuppliers">
        SELECT
            u8c_merchant_code u8cMerchantCode,
            u8c_merchant_name u8cMerchantName,
            u8c_merchant_id u8cMerchantId
        FROM
            financial_u8c_customers_suppliers
        WHERE
            merchant_name = #{ks} AND state = '0'
    </select>

    <insert id="insertFinancialU8CVoucherSet" parameterType="FinancialU8CVoucherSet">
        insert into financial_u8c_voucher_set
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="voucherId != null">voucher_id,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="pkVoucher != null">pk_voucher,</if>
            <if test="u8cVouchertypeName != null">u8c_vouchertype_name,</if>
            <if test="u8cNo != null">u8c_no,</if>
            <if test="pushStatus != null and pushStatus != ''">push_status,</if>
            <if test="u8cReturnMessage != null and u8cReturnMessage != ''">u8c_return_message,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="pushBy != null and pushBy != ''">push_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="voucherId != null">#{voucherId},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="pkVoucher != null">#{pkVoucher},</if>
            <if test="u8cVouchertypeName != null">#{u8cVouchertypeName},</if>
            <if test="u8cNo != null">#{u8cNo},</if>
            <if test="pushStatus != null and pushStatus != ''">#{pushStatus},</if>
            <if test="u8cReturnMessage != null and u8cReturnMessage != ''">#{u8cReturnMessage},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="pushBy != null and pushBy != ''">#{pushBy},</if>
        </trim>
    </insert>

    <update id="updateFinancialU8CVoucherSet" parameterType="FinancialU8CVoucherSet">
        update financial_u8c_voucher_set
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="pkVoucher != null">pk_voucher = #{pkVoucher},</if>
            <if test="u8cVouchertypeName != null">u8c_vouchertype_name = #{u8cVouchertypeName},</if>
            <if test="u8cNo != null">u8c_no = #{u8cNo},</if>
            <if test="pushStatus != null and pushStatus != ''">push_status = #{pushStatus},</if>
            <if test="u8cReturnMessage != null and u8cReturnMessage != ''">u8c_return_message = #{u8cReturnMessage},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="pushBy != null and pushBy != ''">push_by = #{pushBy},</if>
        </trim>
        where voucher_id = #{voucherId}
    </update>

    <delete id="deleteFinancialU8CVoucherSetByVoucherId" parameterType="Long">
        delete from financial_u8c_voucher_set where voucher_id = #{voucherId}
    </delete>

    <delete id="deleteFinancialU8CVoucherSetByVoucherIds" parameterType="String">
        delete from financial_u8c_voucher_set where voucher_id in
        <foreach item="voucherId" collection="array" open="(" separator="," close=")">
            #{voucherId}
        </foreach>
    </delete>

    <insert id="insertRecord">
        insert into financial_u8c_voucher_set( voucher_id,failure_reason, rule_id, pk_voucher, u8c_vouchertype_name, u8c_no, push_status, u8c_return_message, push_time, push_by,push_type) values
        <foreach item="item" index="index" collection="setData" separator=",">
            ( #{item.voucherId},#{item.errorMsg}, #{item.ruleId}, #{item.pk_voucher}, #{item.vouchertype_code}, #{item.no}, #{item.pushStatus}, #{item.u8cReturnMessage}, #{date}, #{userName},#{type})
        </foreach>
    </insert>

    <insert id="insertRecordTOMerge">
        insert into financial_u8c_voucher_set( voucher_id,failure_reason, rule_id, pk_voucher, u8c_vouchertype_name, u8c_no, push_status, u8c_return_message, push_time, push_by,push_type) values
        <foreach item="item" index="index" collection="vouIds" separator=",">
            ( #{item},#{vo.errorMsg}, #{vo.ruleId}, #{vo.pk_voucher}, #{vo.vouchertype_code}, #{vo.no}, #{vo.pushStatus}, #{vo.u8cReturnMessage}, #{date}, #{userName},#{type})
        </foreach>
    </insert>

    <insert id="insertCustomerByU8C">
        insert into financial_u8c_customers_suppliers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merchantType != null and merchantType != ''">merchant_type,</if>
            <if test="merchantId != null">merchant_id,</if>
            <if test="merchantName != null and merchantName != ''">merchant_name,</if>
            <if test="merchantIdBefor != null">merchant_id_befor,</if>
            <if test="merchantNameBefor != null">merchant_name_befor,</if>
            <if test="isInterior != null">is_interior,</if>
            <if test="u8cInteriorType != null and u8cInteriorType != ''">u8c_interior_type,</if>
            <if test="u8cLedger != null">u8c_ledger,</if>
            <if test="u8cMerchantCode != null">u8c_merchant_code,</if>
            <if test="u8cMerchantName != null and u8cMerchantName != ''">u8c_merchant_name,</if>
            <if test="u8cMerchantId != null and u8cMerchantId != ''">u8c_merchant_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="state != null and state != ''">state,</if>
            <if test="version != null">version,</if>
            <if test="updateReason != null">update_reason,</if>
            <if test="relevanceType != null">relevance_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merchantType != null and merchantType != ''">#{merchantType},</if>
            <if test="merchantId != null">#{merchantId},</if>
            <if test="merchantName != null and merchantName != ''">#{merchantName},</if>
            <if test="merchantIdBefor != null">#{merchantIdBefor},</if>
            <if test="merchantNameBefor != null">#{merchantNameBefor},</if>
            <if test="isInterior != null">#{isInterior},</if>
            <if test="u8cInteriorType != null and u8cInteriorType != ''">#{u8cInteriorType},</if>
            <if test="u8cLedger != null">#{u8cLedger},</if>
            <if test="u8cMerchantCode != null">#{u8cMerchantCode},</if>
            <if test="u8cMerchantName != null and u8cMerchantName != ''">#{u8cMerchantName},</if>
            <if test="u8cMerchantId != null and u8cMerchantId != ''">#{u8cMerchantId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="state != null and state != ''">#{state},</if>
            <if test="version != null">#{version},</if>
            <if test="updateReason != null">#{updateReason},</if>
            <if test="relevanceType != null">#{relevanceType},</if>
        </trim>
    </insert>

</mapper>
