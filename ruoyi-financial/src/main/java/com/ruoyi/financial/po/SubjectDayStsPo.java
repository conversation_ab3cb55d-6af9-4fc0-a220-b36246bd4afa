package com.ruoyi.financial.po;

import lombok.Data;

import java.math.BigDecimal;

/**
 *@Authoer: huoruidong
 *@Description: 科目日统计PO类
 *@Date: 2023/7/20 10:30
 **/
@Data
public class SubjectDayStsPo {

    /** 账套id **/
    private Integer accountSetsId;

    /** 账套名称 **/
    private String accountSetsName;

    /** 凭证日期 **/
    private String voucherDate;

    /** 凭证年 **/
    private Integer voucherYear;

    /** 凭证年 **/
    private Integer voucherMonth;

    /** 科目ID **/
    private Integer subjectId;

    /** 科目编码 **/
    private String subjectCode;

    /** 科目名称 **/
    private String subjectName;

    /** 借方金额 **/
    private BigDecimal debitAmount;

    /** 贷方金额 **/
    private BigDecimal creditAmount;
}
