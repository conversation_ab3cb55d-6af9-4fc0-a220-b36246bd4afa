package com.ruoyi.financial.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "financial_user_account_sets")
public class FinancialUserAccountSets implements Serializable{
    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    @TableField(value = "user_id")
    private Long userId;

    /**
     * 账套角色
     */
    @TableField(value = "role_type")
    private String roleType;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private Long createUser;

    private static final long serialVersionUID = 1L;

    public static final String COL_ACCOUNT_SETS_ID = "account_sets_id";

    public static final String COL_USER_ID = "user_id";

    public static final String COL_ROLE_TYPE = "role_type";
}