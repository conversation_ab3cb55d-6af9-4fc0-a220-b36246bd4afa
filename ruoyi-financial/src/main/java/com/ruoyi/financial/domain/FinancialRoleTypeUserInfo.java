package com.ruoyi.financial.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description：账套下用户权限及信息
 * CreateTime：2024/6/13
 * Author：yu-qiang
 */
@Data
public class FinancialRoleTypeUserInfo implements Serializable {

    /** 授权用户ID */
    private Long userId;

    /** 授权用户姓名 */
    private String nickName;

    /** 授权用户权限ID */
    private String roleType;

    /** 授权账套所属公司 */
    private Long companyId;

    /** 授权用户ID集合 */
    private List<Long> userIds;

    /** 授权用户ID */
    private List<String> nikeNameList;
}
