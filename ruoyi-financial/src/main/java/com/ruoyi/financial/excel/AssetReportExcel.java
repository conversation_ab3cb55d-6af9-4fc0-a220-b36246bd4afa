package com.ruoyi.financial.excel;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 *@Authoer: huoruidong
 *@Description: 明细账导出
 *@Date: 2023/6/21 13:40
 **/
@Data
public class AssetReportExcel {

    @Excel(name = "资产")
    private String title;

    @Excel(name = "行次")
    private Integer lineNum;

    @Excel(name = "期末余额", scale = 2)
    private BigDecimal currentPeriodAmount;

    @Excel(name = "年初余额", scale = 2)
    private BigDecimal currentYearAmount;

    @Excel(name = "负债和所有者权益")
    private String fsTitle;

    @Excel(name = "行次")
    private Integer fsLineNum;

    @Excel(name = "期末余额", scale = 2)
    private BigDecimal fsCurrentPeriodAmount;

    @Excel(name = "年初余额", scale = 2)
    private BigDecimal fsCurrentYearAmount;
}
