package com.ruoyi.financial.excel;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *@Authoer: huoruidong
 *@Description: 总账导出
 *@Date: 2023/6/21 13:40
 **/
@Data
public class SubjectBalanceExcel {

    @Excel(name = "科目编码")
    private String code;

    @Excel(name = "科目名称")
    private String name;

    @Excel(name = "期初余额-借方", scale = 2)
    private BigDecimal beginningDebitBalance;

    @Excel(name = "期初余额-贷方", scale = 2)
    private BigDecimal beginningCreditBalance;

    @Excel(name = "本期发生额-借方", scale = 2)
    private BigDecimal currentDebitAmount;

    @Excel(name = "本期发生额-贷方", scale = 2)
    private BigDecimal currentCreditAmount;

    @Excel(name = "期末余额-借方", scale = 2)
    private BigDecimal endingDebitBalance;

    @Excel(name = "期末余额-贷方", scale = 2)
    private BigDecimal endingCreditBalance;
}
