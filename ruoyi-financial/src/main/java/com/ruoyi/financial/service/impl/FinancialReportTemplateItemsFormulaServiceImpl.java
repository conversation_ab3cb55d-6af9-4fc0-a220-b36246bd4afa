package com.ruoyi.financial.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.financial.domain.FinancialReportTemplateItemsFormula;
import com.ruoyi.financial.mapper.FinancialReportTemplateItemsFormulaMapper;
import com.ruoyi.financial.service.IFinancialReportTemplateItemsFormulaService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.kernel.service.impl</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年09月05日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Service
public class FinancialReportTemplateItemsFormulaServiceImpl extends ServiceImpl<FinancialReportTemplateItemsFormulaMapper, FinancialReportTemplateItemsFormula> implements IFinancialReportTemplateItemsFormulaService {

    @Override
    public int batchInsert(List<FinancialReportTemplateItemsFormula> list) {
        return baseMapper.batchInsert(list);
    }
}
