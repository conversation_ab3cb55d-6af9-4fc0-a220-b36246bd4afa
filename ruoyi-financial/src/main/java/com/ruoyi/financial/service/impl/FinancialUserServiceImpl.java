package com.ruoyi.financial.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.financial.domain.FinancialAccountSets;
import com.ruoyi.financial.domain.FinancialUserAccountSets;
import com.ruoyi.financial.domain.FinancialUser;
import com.ruoyi.financial.mapper.FinancialUserMapper;
import com.ruoyi.financial.service.IFinancialAccountSetsService;
import com.ruoyi.financial.service.IFinancialCheckoutService;
import com.ruoyi.financial.service.IFinancialUserAccountSetsService;
import com.ruoyi.financial.service.IFinancialUserService;
import com.ruoyi.financial.vo.FinancialUserVo;
import com.ruoyi.system.service.ISysUserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 *@Authoer: huoruidong
 *@Description: 用户与账套关联实现类
 *@Date: 2023/5/18 11:14
 **/
@Service
@AllArgsConstructor
public class FinancialUserServiceImpl extends ServiceImpl<FinancialUserMapper, FinancialUser> implements IFinancialUserService {

    private IFinancialUserAccountSetsService userAccountSetsService;

    private IFinancialAccountSetsService accountSetsService;

    private IFinancialCheckoutService checkoutService;

    private ISysUserService sysUserService;

    /**
     * <AUTHOR>
     * @Description 根据用户ID获取账套信息
     * @Date 2023/5/18 11:18
     * @Param [userId]
     * @return com.ruoyi.financial.domain.FinancialUser
     **/
    @Override
    public SysUser selectByUserId(Long userId){
        SysUser sysUser = sysUserService.selectUserById(userId);
        return sysUser;
    }

    @Override
    public FinancialUserVo getUserVo(Long userId, Integer accountSetsId) {
        SysUser sysUser = sysUserService.selectUserById(userId);
        return getUserVo(userId, accountSetsId, sysUser);
    }

    @Override
    public FinancialAccountSets getAccountSets(Integer accountSetsId){
        return accountSetsService.getById(accountSetsId);
    }

    @Override
    public List<FinancialUserVo> listByAccountSetId(Integer accountSetsId) {
        return baseMapper.selectByAccountSetId(accountSetsId);
    }

    private FinancialUserVo getUserVo(Long userId, Integer accountSetsId, SysUser sysUser) {
        //查询当前用户使用账套
        FinancialUser financialUser = this.baseMapper.selectByUserId(userId);

        List<FinancialAccountSets> accountSetsList = accountSetsService.myAccountSets(userId);

        FinancialUserVo financialUserVo = new FinancialUserVo();
        if (!CollectionUtils.isEmpty(accountSetsList)) {

            FinancialAccountSets  accountSets = new FinancialAccountSets();
            //判断用户当前使用账套信息是否存在
            if(null != accountSetsId){
                accountSets = accountSetsService.getById(accountSetsId);
            }else{
                if(null != financialUser){
                    accountSets = accountSetsService.getById(financialUser.getAccountSetsId());
                }else{
                    accountSets = accountSetsList.get(0);
                }
            }
            financialUserVo.setAccountSets(accountSets);

            //获取角色信息
            List<SysRole> sysRoles = sysUser.getRoles().stream()
                    .filter(e -> e.getRoleKey().equals("accounting") || e.getRoleKey().equals("accountingManager"))
                    .collect(Collectors.toList());
            if(null == accountSetsId){
                accountSetsId = accountSets.getId();
            }
            if(sysRoles.size()>0){
                List<String> roleKeyList = sysRoles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
                if(roleKeyList.contains("accountingManager")){
                    financialUserVo.setRole("accountingManager");
                }else{
                    financialUserVo.setRole("accounting");
                }
            }else{
                LambdaQueryWrapper<FinancialUserAccountSets> qw = Wrappers.lambdaQuery();
                qw.eq(FinancialUserAccountSets::getAccountSetsId, accountSetsId);
                qw.eq(FinancialUserAccountSets::getUserId, userId);
                List<FinancialUserAccountSets> userAccountSets = userAccountSetsService.list(qw);
                String roleType = null;
                if (userAccountSets.stream().anyMatch(row -> row.getRoleType().equals("admin"))) {
                    roleType = "admin";
                } else if (userAccountSets.stream().anyMatch(row -> row.getRoleType().equals("accountingManager"))) {
                    roleType = "accountingManager";
                } else if (userAccountSets.stream().anyMatch(row -> row.getRoleType().equals("accounting"))) {
                    roleType = "accounting";
                } else if (userAccountSets.stream().anyMatch(row -> row.getRoleType().equals("onlyView"))) {
                    roleType = "onlyView";
                }
                financialUserVo.setRole(roleType);
            }

            //插入用户当前使用账套
            if(null == financialUser){
                financialUser = new FinancialUser();
                financialUser.setUserId(userId);
                financialUser.setAccountSetsId(accountSetsId);
                this.baseMapper.insert(financialUser);
            }else{
                financialUser.setAccountSetsId(accountSetsId);
                this.baseMapper.updateByUserId(financialUser);
            }

            QueryWrapper cqw = Wrappers.query();
            cqw.eq("account_sets_id", financialUser.getAccountSetsId());
            financialUserVo.setCheckoutList(checkoutService.list(cqw));
            financialUserVo.setUserId(userId);
            financialUserVo.setAccountSetsId(financialUser.getAccountSetsId());
        }
        financialUserVo.setAccountSetsList(accountSetsList);


        //用户信息
        financialUserVo.setEmail(sysUser.getEmail());
        financialUserVo.setMobile(sysUser.getPhonenumber());
        financialUserVo.setNickname(sysUser.getRemark());//昵称
        financialUserVo.setRealName(sysUser.getNickName());//真实姓名
        financialUserVo.setCreateDate(sysUser.getCreateTime());//用户创建时间

        return financialUserVo;
    }
}
