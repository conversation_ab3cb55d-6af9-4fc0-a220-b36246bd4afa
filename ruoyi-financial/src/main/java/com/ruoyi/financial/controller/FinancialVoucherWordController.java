package com.ruoyi.financial.controller;

import com.ruoyi.financial.controller.base.BaseCrudController;
import com.ruoyi.financial.domain.FinancialVoucherWord;
import com.ruoyi.financial.service.IFinancialVoucherWordService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.controller</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@RestController
@RequestMapping("/voucher-word")
public class FinancialVoucherWordController extends BaseCrudController<IFinancialVoucherWordService, FinancialVoucherWord> {

}
