package com.ruoyi.financial.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.financial.domain.PushU8CLedger;
import com.ruoyi.financial.service.IFinancialPushU8CToLedgerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 智慧财务系统关联用友U8C账簿Controller
 * lichen ********
 */
@RestController
@RequestMapping("/U8C/Ledger")
public class FinancialPushU8CToLedgerController extends BaseController {

    @Autowired
    private IFinancialPushU8CToLedgerService service;

    /**
     * 关联凭证账套列表
     * @param pushU8CLedger 账套关联函数
     * @return
     */
    @GetMapping("/getLedgerList")
    public TableDataInfo getAccountList(PushU8CLedger pushU8CLedger){
        startPage();
        List<PushU8CLedger> list = service.getAccountList(pushU8CLedger);
        return getDataTable(list);
    }

    /**
     * 保存账套配置
     * @param pushU8CLedger 账套数据
     * @return
     * @throws Exception
     */
    @Log(title = "保存账套配置", businessType = BusinessType.INSERT)
    @PostMapping("/saveLedger")
    public AjaxResult saveLedger(@RequestBody PushU8CLedger pushU8CLedger) throws Exception {
        return toAjax(service.saveLedger(pushU8CLedger));
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected TableDataInfo getDataTable(List<?> list)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 查询修改记录
     * @return
     */
    @GetMapping("/selectUpdate")
    public TableDataInfo selectUpdate(PushU8CLedger pushU8CLedger){
        startPage();
        List<PushU8CLedger> list = service.selectUpdate(pushU8CLedger.getId());
        return getDataTable(list);
    }

    /**
     * 查询U8C所有账套
     * @return
     */
    @PostMapping("/getU8CGlorgbook")
    public TableDataInfo getU8CGlorgbook(String glorgbookName){
        startPage();
        List<PushU8CLedger> list = service.getU8CGlorgbook(glorgbookName);
        return getDataTable(list);
    }

}
