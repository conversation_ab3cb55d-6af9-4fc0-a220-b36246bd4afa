package com.ruoyi.financial.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : 凭证科目明细</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月25日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Data
public class BindProjectVo {

    /** 项目id */
    private Integer projectId;
    /** 项目名称 */
    private String projectName;
    /** 账套id */
    private Integer accountSetsId;
    /** 科目id */
    private Integer subjectId;
    /** 科目编码 */
    private String subjectCode;
    /** 科目名称 */
    private String subjectName;
}
