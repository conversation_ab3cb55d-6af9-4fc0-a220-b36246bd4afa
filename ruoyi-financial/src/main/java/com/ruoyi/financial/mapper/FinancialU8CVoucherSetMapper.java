package com.ruoyi.financial.mapper;

import com.ruoyi.financial.domain.*;
import com.ruoyi.financial.domain.vo.VoucherVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 凭证推送至用友U8CMapper接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Mapper
public interface FinancialU8CVoucherSetMapper
{
    /**
     * 查询凭证推送至用友U8C
     *
     * @param voucherId 凭证推送至用友U8C主键
     * @return 凭证推送至用友U8C
     */
    public List<FinancialU8CVoucherSet> selectFinancialU8CVoucherSetByVoucherId(Long voucherId);

    /**
     * 查询凭证推送至用友U8C列表
     *
     * @param financialU8CVoucherSet 凭证推送至用友U8C
     * @return 凭证推送至用友U8C集合
     */
    public List<FinancialU8CVoucherSet> selectFinancialU8CVoucherSetList(FinancialU8CVoucherSet financialU8CVoucherSet);

    /**
     * 新增凭证推送至用友U8C
     *
     * @param financialU8CVoucherSet 凭证推送至用友U8C
     * @return 结果
     */
    public int insertFinancialU8CVoucherSet(FinancialU8CVoucherSet financialU8CVoucherSet);

    /**
     * 修改凭证推送至用友U8C
     *
     * @param financialU8CVoucherSet 凭证推送至用友U8C
     * @return 结果
     */
    public int updateFinancialU8CVoucherSet(FinancialU8CVoucherSet financialU8CVoucherSet);

    /**
     * 删除凭证推送至用友U8C
     *
     * @param voucherId 凭证推送至用友U8C主键
     * @return 结果
     */
    public int deleteFinancialU8CVoucherSetByVoucherId(Long voucherId);

    /**
     * 批量删除凭证推送至用友U8C
     *
     * @param voucherIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinancialU8CVoucherSetByVoucherIds(Long[] voucherIds);

    /**
     * 通过银行账号查找银行账户信息
     * @param vor   银行账号
     * @return
     */
    List<FinancialU8CRuleDetails> selectOaTrader(@Param("vor") String vor, @Param("accountSetsId") Integer accountSetsId);

    /**
     * 通过科目获取科目对应的项目
     * @param subjectId 科目ID
     * @return
     */
    List<FinancialU8CRuleDetails> selectProjectBySubject(Integer subjectId);

    /**
     * 通过科目编号，找科目借贷方向
     * @param voucherDetails
     * @return
     */
    List<FinancialVoucherDetails> selectSubjectBySubjectId(@Param("voucherDetails") List<FinancialVoucherDetails> voucherDetails);

    PushU8CLedger selectU8CGlorgbook(Integer accountSetsId);

    /**
     * 通过用户名查询用户手机号
     * @param userName 用户名
     * @return
     */
    String selectPhoneByUserName(String userName);

    /**
     * 增加推送记录
     * @param setData   数据
     * @param userName  用户名
     * @param date  推送时间
     * @return
     */
    int insertRecord(@Param("setData") List<VoucherVO> setData, @Param("userName") String userName, @Param("date") Date date,@Param("type") String type);

    /**
     * 合并推送凭证增加推送记录
     * @param voucherVO
     * @param userName
     * @param date
     * @param vouIds
     * @return
     */
    int insertRecordTOMerge(@Param("vo") VoucherVO voucherVO,@Param("userName") String userName,@Param("date") Date date,@Param("vouIds") List<Integer> vouIds, @Param("type") String type);

    /**
     * 查询凭证列表
     * @param financialVoucher
     * @return
     */
    List<FinancialVoucher> selectFinancialVoucherList(FinancialVoucher financialVoucher);

    /**
     * 查询凭证子数据
     * @param financialVouchers
     * @return
     */
    List<FinancialVoucherDetails> selectDetDataByVoucher(@Param("vou") List<FinancialVoucher> financialVouchers);

    /**
     * 批量查询推送次数
     * @param financialVouchers
     * @return
     */
    List<FinancialU8CVoucherSet> selectPushRecords(@Param("vou") List<FinancialVoucher> financialVouchers);

    /**
     * 查询凭证关联的流程发起人
     * @param id     凭证ID
     * @return
     */
    String selectCreateTimeByVoucherId(Integer id);

    /**
     * 通过科目名称查询项目
     * @param subjectName
     * @return
     */
    List<FinancialU8CRuleDetails> selectProjectBySubjectName(String subjectName);

    /**
     * 通过凭证ID查询凭证
     * @param vouIds
     * @return
     */
    List<FinancialVoucher> selectVouByIds(@Param("vou") List<Integer> vouIds);

    /**
     * 查询银行账户辅助核算项关联信息
     * @param accountNumber
     * @param accountSetsId
     * @return
     */
    List<FinancialU8CRuleDetails> selectBankByNumber(@Param("accountNumber") String accountNumber,@Param("accountSetsId") Integer accountSetsId);

    /**
     * 查询客商档案辅助核算项关联信息
     * @param ks
     * @param accountSetsId
     * @return
     */
    List<FinancialU8CCustomersSuppliers> selectKSByKSName(@Param("ks") String ks,@Param("accountSetsId") Integer accountSetsId);

    /**
     * 新增智慧财务系统与用友U8C客商关联
     * @param sup
     * @return
     */
    int insertCustomerByU8C(FinancialU8CCustomersSuppliers sup);

    /**
     * 根据科目名称匹配规则
     * @param subjectName
     * @return
     */
    List<FinancialU8CRule> selectRuleBySubjectName(String subjectName);
}
