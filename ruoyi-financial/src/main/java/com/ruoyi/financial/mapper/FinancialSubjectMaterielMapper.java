package com.ruoyi.financial.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.financial.domain.FinancialSubjectMateriel;
import com.ruoyi.financial.vo.SubjectMaterielVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2020 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : financial</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2022/6/17 9:38</li>
 * <li><AUTHOR> ____′↘TangSheng</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface FinancialSubjectMaterielMapper extends BaseMapper<FinancialSubjectMateriel> {

    List<SubjectMaterielVo> selectSubjectMaterielVo(@Param("accountSetsId") Integer accountSetsId);
}
