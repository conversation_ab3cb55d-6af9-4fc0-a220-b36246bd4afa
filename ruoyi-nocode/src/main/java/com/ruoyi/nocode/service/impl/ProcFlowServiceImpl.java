package com.ruoyi.nocode.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysPostAO;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.ProcFormDataStatusEnums;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.qiyeVX.AccessTokenUtils;
import com.ruoyi.common.utils.*;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.nocode.domain.*;
import com.ruoyi.nocode.domain.bo.*;
import com.ruoyi.nocode.domain.dto.ActTaskDTO;
import com.ruoyi.nocode.domain.dto.FlowFileInfoDTO;
import com.ruoyi.nocode.domain.dto.FlowInfoDTO;
import com.ruoyi.nocode.domain.dto.FlowUserDTO;
import com.ruoyi.nocode.domain.vo.*;
import com.ruoyi.nocode.mapper.ProcFormDataMapper;
import com.ruoyi.nocode.mapper.ProcFormDefMapper;
import com.ruoyi.nocode.mapper.ProcWorkflowFormdataMapper;
import com.ruoyi.nocode.service.GenerateVoucherService;
import com.ruoyi.nocode.service.IDDaArchivistFilesService;
import com.ruoyi.nocode.service.IProcFlowService;
import com.ruoyi.nocode.service.IProcWorkflowFormdataService;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.mapper.SysConfigMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysPostMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import de.odysseus.el.ExpressionFactoryImpl;
import de.odysseus.el.util.SimpleContext;
//import javafx.scene.input.DataFormat;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.activiti.api.process.model.ProcessInstance;
import org.activiti.api.process.model.builders.ProcessPayloadBuilder;
import org.activiti.api.process.runtime.ProcessRuntime;
import org.activiti.api.runtime.shared.NotFoundException;
import org.activiti.api.task.model.Task;
import org.activiti.api.task.model.builders.TaskPayloadBuilder;
import org.activiti.api.task.runtime.TaskRuntime;
import org.activiti.bpmn.model.Process;
import org.activiti.bpmn.model.*;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.task.TaskQuery;
import org.ruoyi.core.OASettingUp.domain.NoaNeedRemind;
import org.ruoyi.core.OASettingUp.domain.NoaRemindPerson;
import org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind;
import org.ruoyi.core.OASettingUp.service.INoaWorkflowRemindService;
import org.ruoyi.core.cwproject.service.impl.CwProjectCustServiceImpl;
import org.ruoyi.core.information.domain.InformationUsed;
import org.ruoyi.core.information.service.IInformationUsedService;
import org.ruoyi.core.license.constant.ZzConstant;
import org.ruoyi.core.license.domain.ZzPendingDetail;
import org.ruoyi.core.license.mapper.ZzPendingDetailMapper;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.domain.vo.FlowUser;
import org.ruoyi.core.oasystem.domain.vo.OaVoucherMainVo;
import org.ruoyi.core.oasystem.domain.vo.OaVoucherRulesViceVo;
import org.ruoyi.core.oasystem.mapper.OaProcessParsingMapper;
import org.ruoyi.core.oasystem.mapper.OaTemplateTypeMapper;
import org.ruoyi.core.oasystem.service.impl.*;
import org.ruoyi.core.oasystem.domain.OaFormField;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.ruoyi.core.oasystem.mapper.OaProcessTemplateMapper;
import org.ruoyi.core.oasystem.service.impl.OaProcessTemplateServiceImpl;
import org.ruoyi.core.qiyeVX.domain.VxUser;
import org.ruoyi.core.qiyeVX.mapper.VXMapper;
import org.ruoyi.core.spycx.domain.YspProcessTemplate;
import org.ruoyi.core.spycx.domain.YspUrgentReviewRecord;
import org.ruoyi.core.spycx.mapper.YspProcessTemplateMapper;
import org.ruoyi.core.spycx.mapper.YspUrgentReviewRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.el.ExpressionFactory;
import javax.el.ValueExpression;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;
import static com.ruoyi.nocode.constant.FlowConstants.*;

@Slf4j
@Service
public class ProcFlowServiceImpl implements IProcFlowService {

    private static final Logger logger = LoggerFactory.getLogger(ProcFlowServiceImpl.class);

    @Autowired
    private ProcessRuntime processRuntime;
    @Autowired
    private TaskRuntime taskRuntime;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private ProcFormDataMapper procFormDataMapper;
    @Autowired
    private ProcFormDefMapper procFormDefMapper;
    @Autowired
    private IProcWorkflowFormdataService ProcWorkflowFormdataService;
    @Autowired
    private ProcWorkflowFormdataMapper procWorkflowFormdataMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysPostMapper sysPostMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private VXMapper vxMapper;

    @Autowired
    private AccessTokenUtils accessTokenUtils;

    @Autowired
    private OaProcessTemplateMapper oaProcessTemplateMapper;

    @Autowired
    private OaProcessTemplateServiceImpl oaProcessTemplateService;

    @Autowired
    private IDDaArchivistFilesService daArchivistFilesService;
    @Autowired
    private IInformationUsedService informationUsedService;

    @Autowired
    private INoaWorkflowRemindService iNoaWorkflowRemindService;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private GenerateVoucherService generateVoucherService;

    @Autowired
    private ZzPendingDetailMapper zzPendingDetailMapper;

    @Autowired
    private OaVoucherRulesMainServiceImpl oaVoucherRulesMainServiceImpl;


    @Autowired
    private OaProjectFlowMainServiceImpl oaProjectFlowMainService;

    @Autowired
    private CwProjectCustServiceImpl cwProjectCustService;

    @Autowired
    private OaProcessParsingMapper oaProcessParsingMapper;

    @Autowired
    private YspProcessTemplateMapper yspProcessTemplateMapper;

    @Autowired
    private OaTemplateTypeMapper oaTemplateTypeMapper;

    @Autowired
    private YspUrgentReviewRecordMapper yspUrgentReviewRecordMapper;

    @Override
    public AjaxResult startFlow(StartBO startBO) {
        String id = UUID.randomUUID().toString();
        ProcFormData procFormData = new ProcFormData();
        procFormData.setOid(id);
        procFormData.setFormId(startBO.getFormId());
        procFormData.setDataJson(startBO.getData());
        procFormData.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
        procFormData.setCreateBy(SecurityUtils.getUsername());
        procFormData.setCreateTime(DateUtils.getNowDate());
        procFormData.setFormName(startBO.getFormName());
        if (!StringUtils.isEmpty(startBO.getProcKey())) {
            JSONObject formJson = JSON.parseObject(startBO.getData());
            //设置动态变量
            HashMap<String, Object> variables = new HashMap<String, Object>();
            variables.put(LOGINUSER, SecurityUtils.getUsername());
            variables.put(APPROVER, startBO.getApprover());
            variables.put("customParam", formJson);
            ProcessInstance processInstance = processRuntime.start(ProcessPayloadBuilder
                    .start()
                    .withProcessDefinitionKey(startBO.getProcKey())
                    .withName(SecurityUtils.getLoginUser().getUser().getNickName() + "的" + startBO.getFormName() + "申请")
                    .withBusinessKey(id)
                    .withVariables(variables)
                    .build());

            //查询第一个任务,并自动审批通过
            TaskQuery taskQuery = taskService.createTaskQuery().active().taskAssignee(SecurityUtils.getUsername()).processDefinitionKey(startBO.getProcKey());
            taskQuery.processInstanceId(processInstance.getId());
            org.activiti.engine.task.Task task = taskQuery.singleResult();
            String taskDefinitionKey = task.getTaskDefinitionKey();
            taskService.complete(task.getId());

            ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
            procWorkflowFormdata.setId(UUID.randomUUID().toString());
            procWorkflowFormdata.setBusinessKey(id);
            procWorkflowFormdata.setComment("提交申请");
            procWorkflowFormdata.setPass("1");//同意
            procWorkflowFormdata.setTaskNodeName(task.getName());
            procWorkflowFormdata.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
            procWorkflowFormdata.setCreateBy(SecurityUtils.getUsername());
            procWorkflowFormdata.setStepId(taskDefinitionKey);
            ProcWorkflowFormdataService.insertProcWorkflowFormdata(procWorkflowFormdata);

            procFormData.setBusinessId(id);
            procFormData.setProcKey(startBO.getProcKey());
            procFormData.setInstanceId(processInstance.getId());
            procFormData.setWithProc(PROC_YES);
            procFormData.setStatus(RUNNING);
        } else {
            procFormData.setWithProc(PROC_NO);
            procFormData.setStatus(PASS);
        }
        procFormDataMapper.insertProcFormData(procFormData);
        return AjaxResult.success("流程启动成功", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult execFlowNode(ExecBO execBO,LoginUser loginUser) {


        //查询时已经限制必须为财务流程
        ProcFormData procFormData = procFormDataMapper.getDataByBusindess(execBO.getBusinessKey());
        //这是发布流程填的表单
        String dataJson = procFormData.getDataJson();
        JSONObject data = JSONObject.parseObject(dataJson);
        //获取表单定义ID
        execBO.setFormID(procFormData.getFormId());

        if(null !=execBO.getCopyFlag() && execBO.getCopyFlag()){
            addCopyTask(execBO.getBusinessKey(),procFormData,execBO.getCopySignUser());
        }
        ProcWorkflowFormdata lastProcWorkflowFormdata = ProcWorkflowFormdataService.selectLastByBusinessKey(execBO.getBusinessKey());
        //判断操作类型
        switch (execBO.getPass()){
            case "6"://加签
                taskService.delegateTask(execBO.getTaskID(), execBO.getCountersignUser());
                break;
            case "5"://转签
                if(lastProcWorkflowFormdata.getPass().equals("6")){
                    taskService.resolveTask(execBO.getTaskID());//加签解决任务
                }
                detailTransferNode(execBO);
                break;
            case "4"://驳回
                //数据状态改为已驳回
//                procFormDataMapper.updateProcFormData(execBO.getBusinessKey(),"4",null,false);
            case "3"://返回上一级
                AjaxResult ajaxResult = revokeFlowNode(execBO);
                String code = ajaxResult.get("code").toString();
                if(!code.equals("200")){
                    return ajaxResult;
                }
                break;
            case "2"://提交下一级
                completeTask(execBO,dataJson);
                break;
            case "0"://不同意
                stopFlowByBusinessId(execBO.getBusinessKey(),loginUser,"4");
                break;
            case "10":
                //审批不通过
                stopFlowByBusinessId(execBO.getBusinessKey(),loginUser,"10");
                break;
            case "1"://同意
            case APPROVAL_REJECTED://会签审批拒绝
                if(lastProcWorkflowFormdata.getPass().equals("6")){
                    taskService.resolveTask(execBO.getTaskID());//加签解决任务
                }else{
                    completeTask(execBO,dataJson);
                }
                //修改时间为结束时间
                procFormDataMapper.updateTimeByKey(execBO.getBusinessKey(),DateUtils.getNowDate());
                break;
        }

        ProcFormData procFormData2 = procFormDataMapper.getDataByBusindess(execBO.getBusinessKey());
        //查找下一个处理人并且企业微信通知
        String procWorkflowFormdataId = execBO.getSoleFlag();
        if (procWorkflowFormdataId == null || StringUtils.EMPTY.equals(procWorkflowFormdataId)) {
            procWorkflowFormdataId = UUID.randomUUID().toString();
        }
        ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
        procWorkflowFormdata.setId(procWorkflowFormdataId);
        procWorkflowFormdata.setBusinessKey(execBO.getBusinessKey());
        procWorkflowFormdata.setComment(execBO.getComment());
        procWorkflowFormdata.setPass(execBO.getPass());
        procWorkflowFormdata.setTaskNodeName(execBO.getTaskNodeName());
        procWorkflowFormdata.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
        procWorkflowFormdata.setAssociationName(execBO.getUserName());
        procWorkflowFormdata.setCreateBy(SecurityUtils.getUsername());
        procWorkflowFormdata.setStepId(execBO.getStepId());
        procWorkflowFormdata.setCreateTime(DateUtils.getNowDate());
        ProcWorkflowFormdataService.insertProcWorkflowFormdata(procWorkflowFormdata);

        //判断审核结果 根据business和节点名称更新审核状态
        YspUrgentReviewRecord yspUrgentReviewRecord = new YspUrgentReviewRecord();
        yspUrgentReviewRecord.setBusinessId(execBO.getBusinessKey());
        yspUrgentReviewRecord.setNoticeNodesNane(execBO.getTaskNodeName());
        List<YspUrgentReviewRecord> yspUrgentReviewRecords = yspUrgentReviewRecordMapper.getDataByBusiness(yspUrgentReviewRecord);
        //根据流程的businessid和节点名称查询是否有催审记录，如果没有则不作操作 如果有则根据审核结果的状态修改审核状态
        if (yspUrgentReviewRecords.size()>0){
            String checkStatus = null;
            YspUrgentReviewRecord yspUrgentReviewRecord1 = yspUrgentReviewRecords.get(0);
            if(execBO.getComment().contains("同意")){
                checkStatus = "1";
            }else if(execBO.getComment().contains("驳回")){
                checkStatus = "2";
            }else if(execBO.getPass().equals("0")){
                checkStatus = "3";
            }
            if(null!=checkStatus){
                yspUrgentReviewRecord1.setNodesCheckStatus(checkStatus);
                yspUrgentReviewRecordMapper.updateYspUrgentByTaskNoneName(yspUrgentReviewRecord1);
            }

        }
        //todo 自动跳过审批有问题，问题是岗位的时候，再指定同一个人会出问题
        //看看是否有相同的审批流程。  如果有则再次调一下
        Map<String, Object> buttonResultMap = getButtonPermissions(execBO.getBusinessKey());
        if ("1".equals(execBO.getPass()) && null !=buttonResultMap.get("automaticSkip") && (boolean) buttonResultMap.get("automaticSkip")) {
            //下一节点跟本节点是否有相同的审核人，如果有，则再次审批一下。如果没有，跳过
            findNextNodeByBusinessId(execBO, dataJson);
        }

        //查找下一个处理人并且企业微信通知,调整通知位置，在自动审批完成之后，取最后一个代办人
        List<org.activiti.engine.task.Task> taskList = taskService.createTaskQuery().processInstanceId(procFormData2.getInstanceId()).list();
        if (taskList != null) {
            for (org.activiti.engine.task.Task task : taskList) {
                String taskDefinitionKey = task.getTaskDefinitionKey();
                if (execBO.getStepId().equalsIgnoreCase(taskDefinitionKey)) {
                    break;
                }
                //查询下一节点是否经过转办
                ProcApprovalRecord record =ProcApprovalRecord.builder()
                        .businessKey(execBO.getBusinessKey()).stepId(taskDefinitionKey).pass("5")
                        .build();
                List<ProcApprovalRecord> resultList = procFormDataMapper.queryProcApprovalRecord(record);
                if(resultList.size()>0){
                    taskService.setAssignee(task.getId(), resultList.get(0).getDetailBy());
                    task.setAssignee(resultList.get(0).getDetailBy());
                }
                //通过taskId获取taskDefinitionKey
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(procFormData2.getInstanceId()).singleResult();
                //获取bpmnModel对象
                BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
                //因为我们这里只定义了一个Process 所以获取集合中的第一个即可
                Process process = bpmnModel.getProcesses().get(0);
                //获取所有的FlowElement信息
                List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);

                List<String> candidateUsers = new ArrayList<>();
                List<Long> userList = new ArrayList<>();
                for (UserTask userTask:userTaskList) {
                    //获取当前任务节点Id
                    String id = userTask.getId();
                    if (id.equals(taskDefinitionKey)) {
                        String assignee = task.getAssignee();
                        candidateUsers = userTask.getCandidateUsers();
                        List<String> candidateGroups = userTask.getCandidateGroups();
                        if (assignee != null) {
                            if (candidateUsers.size() > 0) {
                                String s = candidateUsers.get(0);
                                if (!s.equals(assignee)) {
                                    List<String> newCandidateUsers = new ArrayList<>();
                                    newCandidateUsers.add(assignee);
                                    candidateUsers = newCandidateUsers;
                                }
                            } else {
                                List<String> newCandidateUsers = new ArrayList<>();
                                newCandidateUsers.add(assignee);
                                candidateUsers = newCandidateUsers;
                            }
                        }else if(null==assignee && candidateUsers.size()==0 && candidateGroups.size() >0){
                            List<SysUser> list = getUserListByPostCode(candidateGroups.get(0));
                            if(list.size()==1){
                                List<String> newCandidateUsers = new ArrayList<>();
                                newCandidateUsers.add(list.get(0).getUserName());
                                candidateUsers = newCandidateUsers;
                                taskService.setAssignee(task.getId(),list.get(0).getUserName());
                            }
                        }
                    }
                }

                for (String candidateUser : candidateUsers) {
                    if(!"4".equals(procFormData2.getStatus())){
                        ProcFormReadRecodeVo vo = ProcFormReadRecodeVo.builder().businessKey(execBO.getBusinessKey()).businessStatus("2").detailBy(candidateUser).readFlag("F")
                                .build();
                        procFormDataMapper.addProcReadRecode(vo);
                    }
                    SysUser sysUser = sysUserMapper.selectUserByUserName(candidateUser);
                    userList.add(sysUser.getUserId());
                }

                //根据用户id查询要发送的企业微信账号
                if(userList.size()>0){
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    String msg =
                            "<div class=\"gray\">"+procFormData2.getTheme()+"</div>" +
                                    "<div class=\"gray\">流程模板："+procFormData2.getTemplateName()+"</div>" +
                                    "<div class=\"gray\">发起人："+procFormData2.getNickName()+"</div>" +
                                    "<div class=\"gray\">发起时间："+format.format(procFormData2.getCreateTime())+"</div>";
                    String sendUser = "";
                    List<VxUser>  vxUserList =  vxMapper.selectByUserId(userList);
                    for (VxUser vxUser : vxUserList) {
                        sendUser = sendUser+"|"+vxUser.getVxId();
                    }
                    try {
                        accessTokenUtils.sendMsg(sendUser,msg,execBO.getRejectFlag());
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        //查询流程结束是否需要生成凭证开关，若为true,且流程已结束，则需要生成凭证，后续配置监听，可关闭开关
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique(Constants.PROCESS_END_SWITCH);
        if(null != sysConfig && "true".equalsIgnoreCase(sysConfig.getConfigValue())){
            ProcFormData procFormData3 = procFormDataMapper.getDataByBusindess(execBO.getBusinessKey());
            if(null != procFormData3 && "1".equals(procFormData3.getStatus())){
                //捕获生成财务凭证异常，不影响整个审批流程
                try{
                    generateVoucherService.GenerateVoucher(execBO.getBusinessKey(),null);
                }catch (Exception e){
                    logger.error("流程结束后财务流程生成凭证报错，{}",e);
                }
            }
        }

        //流程提醒配置--判断是否需要发送代办，审批通过后发送代办提醒  2024/1/15  lichen
        if("1".equals(execBO.getPass())){
            String BusinessId = procFormDataMapper.selectBusinessId(execBO.getBusinessKey());
            String busId = execBO.getBusinessKey();
            execBO.setBusinessKeyToTS(BusinessId);
            setRemind(execBO,busId);
        }
        Map result = new LinkedHashMap();
        result.put("lastNodeFlag",execBO.getLastNodeFlag());
        return AjaxResult.success("处理成功",result);
    }

    /**
     * 设置推送代办
     * @param execBO
     */
    private void setRemind(ExecBO execBO,String busId) {
        List<NoaWorkflowRemind> date = iNoaWorkflowRemindService.selectWorkFlowByBusinessIdAndNodeId(execBO.getBusinessKeyToTS(), execBO.getStepId());
        for (int i = 0; i < date.size(); i++) {
            if(date.get(i).getFlowId() != null){
                String id = IdUtils.fastSimpleUUID();
                NoaNeedRemind bill = new NoaNeedRemind();
                bill.setFlowId(date.get(i).getFlowId());
                bill.setNodeId(date.get(i).getNodeId());
                bill.setId(id);
                bill.setRemindId(date.get(i).getId());
                bill.setBusinessId(execBO.getBusinessKeyToTS());
                bill.setState("2");
                bill.setCreateTime(DateUtils.getNowDate());
                bill.setCreateBy(getUsername());
                //获取提醒群组，增加代办内容
                int flag = iNoaWorkflowRemindService.insertNeedRemind(bill);
                if(flag>0){
                    List<NoaRemindPerson> personDate = iNoaWorkflowRemindService.selectAuthority(date.get(i).getFlowId(),date.get(i).getNodeId(),id);
                    if(personDate.size()>0){
                        flag = iNoaWorkflowRemindService.insertRemindPerson(personDate,busId);
                    }
                }
            }
        }
    }

    private void findNextNodeByBusinessId(ExecBO execBO, String dataJson) {
        String businessId = execBO.getBusinessKey();
        //------------开始------------
        //找这次的审批人
        //获取发起的流程表单信息
        String userId = SecurityUtils.getUsername();
        ProcFormData procFormData = new ProcFormData();
        procFormData.setBusinessId(businessId);
        procFormData.setCreateBy(userId);
        procFormData.setWithProc(PROC_YES);
        ProcFormData procFormData1 = procFormDataMapper.selectProcFormDataByBusinessId(procFormData);
        if (procFormData1 == null) {
            procFormData.setOid(businessId);
            procFormData.setBusinessId(null);
            procFormData1 = procFormDataMapper.selectProcFormDataByBusinessId(procFormData);
        }
        procFormData1.setData(JSON.parseObject(procFormData1.getDataJson()));
        List<String> candidateUsers = null;
        String assignee = null;
        String name = StringUtils.EMPTY;
//        ProcessInstance processInstance = processRuntime.processInstance(procFormData1.getInstanceId());
        org.activiti.engine.task.Task task = null;
        if (procFormData1.getInstanceId() != null) {
            task = taskService.createTaskQuery().processInstanceId(procFormData1.getInstanceId()).singleResult();
        }
        String taskDefinitionKey = null;
        if (task != null) {
            taskDefinitionKey = task.getTaskDefinitionKey();
            //通过taskId获取taskDefinitionKey
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(procFormData1.getInstanceId()).singleResult();
            //获取bpmnModel对象
            BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
            //因为我们这里只定义了一个Process 所以获取集合中的第一个即可
            Process process = bpmnModel.getProcesses().get(0);
            //获取所有的FlowElement信息
            List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);
            for (UserTask userTask : userTaskList) {
                //获取当前任务节点Id
                String id = userTask.getId();
                if (id.equals(taskDefinitionKey)) {
                    name = userTask.getName();
                    //获取当前处理人和岗位
                    assignee = task.getAssignee();
                    candidateUsers = userTask.getCandidateUsers();
                    List<String> candidateGroups = userTask.getCandidateGroups();
                    if (assignee != null) {
                        if (candidateUsers.size() > 0) {
                            String s = candidateUsers.get(0);
                            if (!s.equals(assignee)) {
                                List<String> newCandidateUsers = new ArrayList<>();
                                newCandidateUsers.add(assignee);
                                candidateUsers = newCandidateUsers;
                            }
                        } else {
                            List<String> newCandidateUsers = new ArrayList<>();
                            newCandidateUsers.add(assignee);
                            candidateUsers = newCandidateUsers;
                        }
                    }else if(null==assignee && candidateUsers.size()==0 && candidateGroups.size() >0){
                        List<SysUser> list = getUserListByPostCode(candidateGroups.get(0));
                        List<String> newCandidateUsers = new ArrayList<>();
                        newCandidateUsers.add(list.get(0).getUserName());
                        candidateUsers = newCandidateUsers;
                    }
                }
            }
        } else {
            candidateUsers = null;
        }
        //------------结束------------
        if (candidateUsers != null) {
            String s = StringUtils.EMPTY;
            if (candidateUsers.size() == 0) {
                s = assignee;
            } else {
                s = candidateUsers.get(0);
            }
            if (s != null && !StringUtils.EMPTY.equals(s)) {
                if (s.equals(execBO.getUserName())) {
                    AjaxResult resultData = getNextFlowInfo(task.getProcessInstanceId(), task.getId());
                    Map<String,Object> resultMap = (Map<String, Object>) resultData.get("data");
                    List<String> candidateUsersNext = resultMap.get("candidateUsers") == null ? new LinkedList<>() : (List<String>) resultMap.get("candidateUsers");
                    List<String> candidateGroupsNext = resultMap.get("candidateGroups") == null ? new LinkedList<>() : (List<String>) resultMap.get("candidateGroups");
                    boolean groupsNextFlag = false;
                    if(candidateGroupsNext.size()==0){
                        groupsNextFlag = true;
                    }else {
                        List<SysUser> userList = getUserListByPostCode(candidateGroupsNext.get(0));
                        if (userList.size() == 1) {
                            groupsNextFlag = true;
                        }
                    }
                    //自动审批下一节点能取到代理人或者获选人均可自动审批
                    if (null != resultMap.get("assignee") || candidateUsersNext.size() > 0 || (null == resultMap.get("assignee") && candidateUsersNext.size() == 0 && groupsNextFlag)) {
                        //说明两次审批人是一样的，那么就完成本次的审批操作
                        ExecBO execBO1 = new ExecBO();
                        BeanUtil.copyProperties(execBO, execBO1);
                        execBO1.setTaskID(task.getId());
                        execBO1.setTaskNodeName(name);
                        //清空下一次审批人信息
                        execBO1.setNextFlowApproveUserName(null);
                        completeTask(execBO1, dataJson);
                        //保存记录
                        ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
                        procWorkflowFormdata.setId(UUID.randomUUID().toString());
                        procWorkflowFormdata.setBusinessKey(execBO1.getBusinessKey());
                        procWorkflowFormdata.setComment("与上一流程审批人一致，跳过本次审批");
                        procWorkflowFormdata.setPass(execBO1.getPass());
                        procWorkflowFormdata.setTaskNodeName(execBO1.getTaskNodeName());
                        procWorkflowFormdata.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
                        procWorkflowFormdata.setAssociationName(execBO1.getUserName());
                        procWorkflowFormdata.setCreateBy(SecurityUtils.getUsername());
                        procWorkflowFormdata.setStepId(taskDefinitionKey);
                        Calendar nowTime = Calendar.getInstance();
                        nowTime.add(Calendar.SECOND,1);
                        Date nowDate = nowTime.getTime();
                        procWorkflowFormdata.setCreateTime(nowDate);
                        ProcWorkflowFormdataService.insertProcWorkflowFormdata(procWorkflowFormdata);
                        //修改时间为结束时间
                        procFormDataMapper.updateTimeByKey(execBO.getBusinessKey(), DateUtils.getNowDate());
                        //获取当前审批节点是否可跳过审批
                        Map<String, Object> buttonResultMap = getButtonPermissions(execBO1.getBusinessKey());
                        if(null !=buttonResultMap.get("automaticSkip") && (boolean) buttonResultMap.get("automaticSkip")){
                            findNextNodeByBusinessId(execBO1, dataJson);
                        }
                    }
                }
            }
        }

    }

    @Override
    public AjaxResult getDataByBusinessId(String businessId) {
        ProcFormData procFormData = new ProcFormData();
        procFormData.setBusinessId(businessId);
        List<ProcFormData> procFormDataList = procFormDataMapper.selectProcFormDataList(procFormData);
        if (CollectionUtils.isEmpty(procFormDataList))
            return new AjaxResult(HttpStatus.ERROR, "未查询到对应数据");
        else
            return new AjaxResult(HttpStatus.SUCCESS, "ok", JSON.parseObject(procFormDataList.get(0).getDataJson()));
    }

    @Override
    public TableDataInfo getDataListByFormId(String formId, HttpServletRequest request) {
        ProcFormData procFormData = new ProcFormData();
        procFormData.setFormId(formId);
        procFormData.setStatus(PASS);

        if (!StringUtils.isEmpty(ServletUtils.getParameter("params[beginTime]")) && !StringUtils.isEmpty(ServletUtils.getParameter("params[endTime]"))) {
            Date beginTime = DateUtils.parseDate(ServletUtils.getParameter("params[beginTime]"));
            Date endTime = DateUtils.parseDate(ServletUtils.getParameter("params[endTime]"));
            procFormData.setBeginTime(beginTime);
            procFormData.setEndTime(endTime);
        }
        List<ProcFormData> procFormDataList = procFormDataMapper.selectProcFormDataList(procFormData);
        long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData);
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(procFormDataList.stream().map(item -> {
            item.setData(JSON.parseObject(item.getDataJson()));
            item.getData().put("_formId", item.getFormId());
            item.getData().put("_createName", item.getCreateName());
            item.getData().put("_createBy", item.getCreateBy());
            item.getData().put("_createTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getCreateTime()));
            return item.getData();
        }).collect(Collectors.toList()));
        rspData.setTotal(totalCount);
        return rspData;
    }

    @Override
    public TableDataInfo getDataListByUser() {
        String userId = SecurityUtils.getUsername();
        ProcFormData procFormData = new ProcFormData();
        procFormData.setCreateBy(userId);
        procFormData.setWithProc(PROC_YES);
        List<Map<String, Object>> procFormDataList = procFormDataMapper.selectProcFormDataListNew(procFormData);

        procFormDataList.stream().forEach(e -> {
            e.put("data", JSON.parseObject(e.get("data").toString()));

        });


//        long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData);
        long count = procFormDataMapper.selectDataCount(procFormData);

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(procFormDataList);
        rspData.setTotal(count);
        return rspData;
    }

    @Override
    public AjaxResult getFlowhistoryByBusinessId(String businessId) {
        ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
        procWorkflowFormdata.setBusinessKey(businessId);
        List<ProcWorkflowFormdata> list = ProcWorkflowFormdataService.selectProcWorkflowFormdataList(procWorkflowFormdata);
        return new AjaxResult(HttpStatus.SUCCESS, "ok", list);
    }

    @Override
    public AjaxResult getAllUsers(String userName) {
        SysUser sysUser = new SysUser();
        sysUser.setStatus("0");
        sysUser.setNickName(userName);
        List<SysUser> list = sysUserMapper.selectUserList(sysUser);
        return new AjaxResult(HttpStatus.SUCCESS, "ok", list);
    }

    @Override
    public AjaxResult getAllPosts(String postName,String postCode) {
        SysPost post = new SysPost();
        post.setPostName(postName);
        post.setPostCode(postCode);
        post.setStatus("0");
        List<SysPost> list = sysPostMapper.selectPostList(post);
        return new AjaxResult(HttpStatus.SUCCESS, "ok", list);
    }

    private Map<String, Object> selectParam(OaSystemUtil oaSystemUtil) {
        //发起人昵称
        String initiatorNickName = oaSystemUtil.getInitiatorNickName();
        List<Long> userIdList = null;
        //查找发起人的id集合
        if (initiatorNickName != null && !StringUtils.EMPTY.equals(initiatorNickName)) {
            PageHelper.clearPage();
            List<SysUser> userList = sysUserMapper.selectUserByNickName(initiatorNickName);
            if (userList.size() > 0) {
                userIdList = userList.stream().map(SysUser::getUserId).collect(Collectors.toList());
            }
        }
        //流程类型
        String flowType = oaSystemUtil.getFlowType();
        List<Long> templateIdList = null;
        if (flowType != null && !StringUtils.EMPTY.equals(flowType)) {
            PageHelper.clearPage();
            OaProcessTemplate oaProcessTemplate = new OaProcessTemplate();
            oaProcessTemplate.setTemplateType(flowType);
            List<OaProcessTemplate> oaProcessTemplates = oaProcessTemplateMapper.selectOaProcessTemplateList(oaProcessTemplate);
            if (oaProcessTemplates.size() > 0) {
                templateIdList = oaProcessTemplates.stream().map(OaProcessTemplate::getId).collect(Collectors.toList());
            }
        }
        //发起时间
        String initiatorStartTime = oaSystemUtil.getStartTime();
        String initiatorEndTime = oaSystemUtil.getEndTime();
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("userIdList", userIdList);
        resultMap.put("templateIdList", templateIdList);
        resultMap.put("initiatorStartTime", initiatorStartTime);
        resultMap.put("initiatorEndTime", initiatorEndTime);
        return resultMap;
    }


    @Override
    public TableDataInfo byCheckGetData(OaSystemUtil oaSystemUtil) {
        TableDataInfo rspData = new TableDataInfo();
        Map<String, Object> paramMap = this.selectParam(oaSystemUtil);
        List<Long> userIdList = (List<Long>) paramMap.get("userIdList");
        List<Long> templateIdList = (List<Long>) paramMap.get("templateIdList");
        String initiatorStartTime = (String) paramMap.get("initiatorStartTime");
        String initiatorEndTime = (String) paramMap.get("initiatorEndTime");
        //基本条件
        String userId = SecurityUtils.getUsername();
        ProcFormData procFormData = new ProcFormData();
        procFormData.setCreateBy(userId);
        procFormData.setWithProc(PROC_YES);
        procFormData.setTheme(oaSystemUtil.getTheme());

        ProcFormData procFormData2 = new ProcFormData();
        procFormData2.setWithProc(PROC_YES);
        procFormData2.setOnlyReadable("1");
        procFormData2.setReadableUserId(SecurityUtils.getUserId());
        procFormData2.setReadableUserName(SecurityUtils.getUsername());
        //OA四期 我的流程-增加搜索 条件
        procFormData.setUserIdList(userIdList);
        procFormData.setTemplateIdList(templateIdList);
        procFormData.setInitiatorStartTime(initiatorStartTime);
        procFormData.setInitiatorEndTime(initiatorEndTime);
        procFormData2.setUserIdList(userIdList);
        procFormData2.setTemplateIdList(templateIdList);
        procFormData2.setInitiatorStartTime(initiatorStartTime);
        procFormData2.setInitiatorEndTime(initiatorEndTime);


        //判断选择的是哪个页面 并调用相应的接口获取数据
        String activeNameFirst = oaSystemUtil.getActiveNameFirst();
        String activeNameSecend = oaSystemUtil.getActiveNameSecend();

        //activeNameFirst为first都是我的流程下面的
        if (activeNameFirst.equals("first") && activeNameSecend.equals("first")) {
            //审批中
            procFormData.setStatus("0");
            PageUtils.startPage();
            List<Map<String, Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);
            Set<Object> seenBusinessIds = new HashSet<>();
            List<Map<String, Object>> filteredList = new ArrayList<>();
            for (Map<String, Object> row : procFormDataList) {
                Object businessId = row.get("businessId");
                if (!seenBusinessIds.contains(businessId)) {
                    seenBusinessIds.add(businessId);
                    filteredList.add(row);
                }
            }
            procFormDataList = filteredList;
            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            rspData.setTotal(totalCount);
            } else if (activeNameFirst.equals("first") && activeNameSecend.equals("second")) {
            //驳回
            procFormData.setStatus("4");
            //驳回列表显示 审核不通过+驳回两个状态
            List<String>  statusList = new ArrayList<>();
            statusList.add("4");
            statusList.add("10");
            procFormData.setStatusList(statusList);
            PageUtils.startPage();
            List<Map<String, Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);
            long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData);
            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            rspData.setTotal(totalCount);

        } else if (activeNameFirst.equals("first") && activeNameSecend.equals("third")) {
            //草稿
            procFormData.setStatus("5");
            procFormData.setWithProc(PROC_NO);
            PageUtils.startPage();
            List<Map<String, Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);

            long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            rspData.setTotal(totalCount);
        } else if (activeNameFirst.equals("first") && activeNameSecend.equals("fourth")) {
            //完成
            procFormData.setStatus("1");
            //已结束列表显示  已结束+已知悉(审核不通过)
            List<String>  statusList = new ArrayList<>();
            statusList.add("1");
            statusList.add("11");
            procFormData.setStatusList(statusList);
            PageUtils.startPage();
            List<Map<String, Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);
            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            rspData.setTotal(totalCount);
        }else  if(activeNameFirst.equals("first") && activeNameSecend.equals("fieth")){
            //终止
            procFormData.setStatus("3");
            PageUtils.startPage();
            List<Map<String,Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);
            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            rspData.setTotal(totalCount);
        }else  if(activeNameFirst.equals("second") && activeNameSecend.equals("first")){
            //审批
            PageDomain pageDomain = new PageDomain();
            pageDomain.setPageNum(oaSystemUtil.getPageNum());
            pageDomain.setPageSize(oaSystemUtil.getPageSize());
            pageDomain.setOrderByColumn("createdDate");
//            Page<ActTaskDTO> actTaskDTOS = this.selectTaskList(pageDomain);
            procFormData.setStatus("4");
            List<Map<String,Object>> rejectList = procFormDataMapper.selectSPing(procFormData);

            ProcFormData procFormData1 = new ProcFormData();
            procFormData1.setCreateBy(userId);
            procFormData1.setWithProc(PROC_YES);
            procFormData1.setTheme(oaSystemUtil.getTheme());
            procFormData1.setUserIdList(userIdList);
            procFormData1.setTemplateIdList(templateIdList);
            procFormData1.setInitiatorStartTime(initiatorStartTime);
            procFormData1.setInitiatorEndTime(initiatorEndTime);
            procFormData1.setStatus("0");

            Page<ActTaskDTO> actTaskDTOS = this.selectTaskList1(pageDomain, rejectList, procFormData1, oaSystemUtil, false);


            int size = actTaskDTOS.size();
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(actTaskDTOS);
            rspData.setTotal(actTaskDTOS.getTotal());
        }else  if(activeNameFirst.equals("second") && activeNameSecend.equals("second")){

            //我已审批 从审核记录表获取审核人是我，并且记录不为提交申请的记录
            PageUtils.startPage();
            List<Map<String, Object>> yishenpi = procFormDataMapper.yishenpi(procFormData.getTheme(),SecurityUtils.getUsername(), "提交申请", procFormData);
            for (Map<String, Object> map : yishenpi) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            long yishenpiCount = procFormDataMapper.yishenpiCount(procFormData.getTheme(),SecurityUtils.getUsername(), "提交申请", procFormData);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(yishenpi);
            rspData.setTotal(yishenpiCount);

        }else  if(activeNameFirst.equals("second") && activeNameSecend.equals("third")){
            //我已驳回 从审核记录表获取审核人是我，并且记录不为提交申请的记录
            PageUtils.startPage();
            List<Map<String, Object>> yibohui = procFormDataMapper.yibohui(procFormData.getTheme(),SecurityUtils.getUsername(), procFormData);
            for (Map<String, Object> map : yibohui) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            long yibohuiCount = procFormDataMapper.yibohuiCount(procFormData.getTheme(),SecurityUtils.getUsername(), procFormData);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(yibohui);
            rspData.setTotal(yibohuiCount);

        }else  if(activeNameFirst.equals("second") && activeNameSecend.equals("fourth")){
            //抄送给我
            PageUtils.startPage();
            List<Map<String, Object>> copyToMe = procFormDataMapper.copyToMe(SecurityUtils.getUsername());
            for (Map<String, Object> map : copyToMe) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            long copyToMeCount = procFormDataMapper.copyToMeNum(SecurityUtils.getUsername());
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(copyToMe);
            rspData.setTotal(copyToMeCount);

        }else  if(activeNameFirst.equals("third") && activeNameSecend.equals("first")){
            //审批中
            procFormData2.setStatus("0");
            PageUtils.startPage();
            List<Map<String,Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData2);
            long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData2);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            rspData.setTotal(totalCount);
        }else  if(activeNameFirst.equals("third") && activeNameSecend.equals("second")){
            //驳回
            procFormData2.setStatus("4");
            PageUtils.startPage();
            List<Map<String,Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData2);
            long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData2);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            rspData.setTotal(totalCount);
        }else  if(activeNameFirst.equals("third") && activeNameSecend.equals("third")){
            procFormData2.setStatus("1");
            PageUtils.startPage();
            List<Map<String,Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData2);
            long totalCount = procFormDataMapper.selectProcFormDataCount(procFormData2);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            rspData.setTotal(totalCount);
        }

        return rspData;
    }



    public Map<String, String> getFlowUser(String instanceId) {

        String name = "";
        String status = "";
        if(null!= instanceId || !("".equals(instanceId))){
            List<org.activiti.engine.task.Task> taskList = taskService.createTaskQuery().processInstanceId(instanceId).list();
            if (taskList != null && taskList.size() > 0) {
                List<String> candidateUsers = new ArrayList<>();
                if (taskList.size() > 1) {
                    for (org.activiti.engine.task.Task task : taskList) {
                        candidateUsers.add(task.getAssignee());
                    }
                } else {
                    org.activiti.engine.task.Task task = taskList.get(0);
                    String taskDefinitionKey = task.getTaskDefinitionKey();
                    //通过taskId获取taskDefinitionKey
                    HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                            .processInstanceId(instanceId).singleResult();
                    //获取bpmnModel对象
                    BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
                    //因为我们这里只定义了一个Process 所以获取集合中的第一个即可
                    Process process = bpmnModel.getProcesses().get(0);
                    //获取所有的FlowElement信息
                    List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);
                    for (UserTask userTask : userTaskList) {
                        //获取当前任务节点Id
                        String id = userTask.getId();
                        if (id.equals(taskDefinitionKey)) {
                            String assignee = task.getAssignee();
                            candidateUsers = userTask.getCandidateUsers();
                            List<String> candidateGroups = userTask.getCandidateGroups();
                            if (assignee != null) {
                                if (candidateUsers.size() > 0) {
                                    String s = candidateUsers.get(0);
                                    if (!s.equals(assignee)) {
                                        List<String> newCandidateUsers = new ArrayList<>();
                                        newCandidateUsers.add(assignee);
                                        candidateUsers = newCandidateUsers;
                                    }
                                } else {
                                    List<String> newCandidateUsers = new ArrayList<>();
                                    newCandidateUsers.add(assignee);
                                    candidateUsers = newCandidateUsers;
                                }
                            }else if(null==assignee && candidateUsers.size()==0 && candidateGroups.size() >0){
                                List<SysUser> list = getUserListByPostCode(candidateGroups.get(0));
                                List<String> newCandidateUsers = new ArrayList<>();
                                list.forEach(row ->{
                                    newCandidateUsers.add(row.getUserName());
                                });
                                candidateUsers = newCandidateUsers;
                            }
                        }
                    }
                }
                for (String candidateUser : candidateUsers) {
                    String nickName = "没有找到处理人";
                    if(null!=candidateUser){
                        SysUser sysUser = sysUserMapper.selectUserByUserName(candidateUser);
                        if(null != sysUser){
                            nickName = sysUser.getNickName();
                            if ("1".equals(sysUser.getStatus())) {
                                status = "(停用)";
                            }
                        }

                    }
                    //根据账号查询nickname
                    name = name+" "+nickName;
                }

            }
        }
        Map<String, String> resMap = new HashMap<>();
        resMap.put("processor", name);
        resMap.put("userStatus1", status);

        return resMap;
    }



    @Override
    public int deleteFlowByid(String oid) {
        //先删除流程记录表 2024-12-27 不删除流程审批记录
//       int o =  procWorkflowFormdataMapper.deleteByBusinessId(oid);
        //在删除主表   2024-12-27 修改状态为别的状态 新增状态 删除代表9
        int i =  procFormDataMapper.updateStatus(oid,"9");

        return i;
    }
    /**
     * 已知悉流程 2025-03-11 状态 11为已知悉
     * @param businessId
     * @return
     */
    @Override
    public int knownFlowByBusinessId(String businessId) {
        return procFormDataMapper.updateStatus(businessId,"11");
    }

    @Override
    public Map<String, Object> getTabsDataNums(OaSystemUtil oaSystemUtil) {
        HashMap<String, Object> returnMap = new HashMap<>();
        Map<String, Object> paramMap = this.selectParam(oaSystemUtil);
        List<Long> userIdList = (List<Long>) paramMap.get("userIdList");
        List<Long> templateIdList = (List<Long>) paramMap.get("templateIdList");
        String initiatorStartTime = (String) paramMap.get("initiatorStartTime");
        String initiatorEndTime = (String) paramMap.get("initiatorEndTime");
        //基本条件
        String userId = SecurityUtils.getUsername();
        ProcFormData procFormData = new ProcFormData();
        procFormData.setCreateBy(userId);
        procFormData.setWithProc(PROC_YES);
        procFormData.setTheme(oaSystemUtil.getTheme());

        /** procFormData2 用于待我阅览封装查询条件*/
        ProcFormData procFormData2 = new ProcFormData();
        procFormData2.setWithProc(PROC_YES);
        procFormData2.setOnlyReadable("1");
        procFormData2.setReadableUserId(SecurityUtils.getUserId());
        procFormData2.setTheme(oaSystemUtil.getTheme());
        procFormData2.setReadableUserName(SecurityUtils.getUsername());

        ProcFormData procFormData3 = new ProcFormData();
        procFormData3.setCreateBy(userId);
        procFormData3.setWithProc(PROC_NO);
        procFormData3.setTheme(oaSystemUtil.getTheme());

        //OA四期 我的流程-增加搜索 条件
        procFormData.setUserIdList(userIdList);
        procFormData.setTemplateIdList(templateIdList);
        procFormData.setInitiatorStartTime(initiatorStartTime);
        procFormData.setInitiatorEndTime(initiatorEndTime);
        procFormData2.setUserIdList(userIdList);
        procFormData2.setTemplateIdList(templateIdList);
        procFormData2.setInitiatorStartTime(initiatorStartTime);
        procFormData2.setInitiatorEndTime(initiatorEndTime);
        procFormData3.setUserIdList(userIdList);
        procFormData3.setTemplateIdList(templateIdList);
        procFormData3.setInitiatorStartTime(initiatorStartTime);
        procFormData3.setInitiatorEndTime(initiatorEndTime);

        //审批中
        procFormData.setStatus("0");
        long t11 = procFormDataMapper.selectProcFormDataCount(procFormData);
        //驳回
        procFormData.setStatus("4");
        long t12 = procFormDataMapper.selectProcFormDataCount(procFormData);
        //草稿
        procFormData3.setStatus("5");
        long t13 = procFormDataMapper.selectProcFormDataCount(procFormData3);
//        long t13 = procFormDataMapper.selectProcFormDataCount(procFormData);
//        //完成
//        procFormData.setStatus("1");
//        long t14 = procFormDataMapper.selectProcFormDataCount(procFormData);
//        //终止
//        procFormData.setStatus("3");
//        long t15 = procFormDataMapper.selectProcFormDataCount(procFormData);

        //待我阅览 -- 审批中
        procFormData2.setStatus("0");
        long readOnlyApproveNum = procFormDataMapper.selectProcFormDataCount(procFormData2);
        //待我阅览 -- 驳回
        procFormData2.setStatus("4");
        long readOnlyRejectNum = procFormDataMapper.selectProcFormDataCount(procFormData2);
        //待我阅览 -- 结束
        procFormData2.setStatus("1");
        long readOnlyEndNum = procFormDataMapper.selectProcFormDataCount(procFormData2);

        //审批
        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNum(1);
        pageDomain.setPageSize(100);
        pageDomain.setOrderByColumn("createdDate");
        procFormData.setStatus("4");
        List<Map<String, Object>> rejectList = procFormDataMapper.selectSPing(procFormData);
        ProcFormData procFormData1 = new ProcFormData();
        procFormData1.setCreateBy(userId);
        procFormData1.setWithProc(PROC_YES);
        procFormData1.setTheme(oaSystemUtil.getTheme());
        procFormData1.setUserIdList(userIdList);
        procFormData1.setTemplateIdList(templateIdList);
        procFormData1.setInitiatorStartTime(initiatorStartTime);
        procFormData1.setInitiatorEndTime(initiatorEndTime);
        procFormData1.setStatus("0");
        Page<ActTaskDTO> actTaskDTOS = this.selectTaskList1(pageDomain, rejectList, procFormData1, oaSystemUtil, true);

        long t21 = actTaskDTOS.getTotal();
        //我已审批 从审核记录表获取审核人是我，并且记录不为提交申请的记录
//        long t22 = procFormDataMapper.yishenpiCount(procFormData.getTheme(), SecurityUtils.getUsername(), "提交申请", procFormData);
        //我已驳回 从审核记录表获取审核人是我，并且记录不为提交申请的记录
        long t23 = procFormDataMapper.yibohuiCount(procFormData.getTheme(), SecurityUtils.getUsername(), procFormData);
        //抄送给我
        long t24 = procFormDataMapper.copyToMeNum(SecurityUtils.getUsername());

        long t1 = t11+t12+t13;
        long t2 =  t21;
        long readOnlyTotal = readOnlyApproveNum + readOnlyRejectNum + readOnlyEndNum;

        returnMap.put("t1",t1);
        returnMap.put("t2",t2);
        returnMap.put("readOnlyTotal",readOnlyTotal);
        returnMap.put("t11",t11);
        returnMap.put("t12",t12);
        returnMap.put("t13",t13);
//        returnMap.put("t14",t14);
//        returnMap.put("t15",t15);
        returnMap.put("t21",t21);
//        returnMap.put("t22",t22);
        returnMap.put("t23",t23);
        returnMap.put("t24",t24);
        returnMap.put("readOnlyApproveNum",readOnlyApproveNum);
        returnMap.put("readOnlyRejectNum",readOnlyRejectNum);
        returnMap.put("readOnlyEndNum",readOnlyEndNum);
        return returnMap;
    }

    @Override
    public List<String> getDeptUserList(LoginUser loginUser){
//        Long deptId = loginUser.getDeptId();

        List<String> userIds = new ArrayList<>();
        String deptid =  sysUserMapper.getMainPostId(loginUser.getUserId());
        List<SysRole> roles = loginUser.getUser().getRoles();

        boolean b = false;
        for (SysRole role : roles) {
            if(role.getRoleKey().equals("admin")){
                b = true;
                break;
            }
        }
        if(!b){
            userIds =  sysUserMapper.getNameByDept(deptid);
//            userIds = procFormDataMapper.getDeptUserIds(deptId);
        }
        return userIds;
    }

    /**
     * 获取本部门的流程
     *
     * @param oaSystemUtil oa系统实效
     * @return {@link TableDataInfo}
     */
    @Override
    public TableDataInfo myDeptByCheckGetData(OaSystemUtil oaSystemUtil, List<String> userIds) {



        TableDataInfo rspData = new TableDataInfo();

        String withProc = PROC_YES;
        long status = new Long("0");
        //判断选择的是哪个页面 并调用相应的接口获取数据
        String activeNameFirst = oaSystemUtil.getActiveNameFirst();
        String activeNameSecend = oaSystemUtil.getActiveNameSecend();
        String theme = oaSystemUtil.getTheme();
        //activeNameFirst为first都是我的流程下面的
        if(activeNameFirst.equals("first") && activeNameSecend.equals("first")){
            //审批中
            status = 0;
            List<Map<String,Object>> procFormDataList = procFormDataMapper.getDataByThisDept(theme,withProc,status,userIds);
            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            rspData.setCode(HttpStatus.SUCCESS);
            Map<String, Object> dataByThisDeptTotal = procFormDataMapper.getDataByThisDeptTotal(theme,withProc, status, userIds);
            rspData.setTotal(Long.valueOf(dataByThisDeptTotal.get("total").toString()));
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
        }else  if(activeNameFirst.equals("first") && activeNameSecend.equals("second")){
            //驳回
            status = 4;
            List<Map<String,Object>> procFormDataList = procFormDataMapper.getDataByThisDept(theme,withProc,status,userIds);
            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            Map<String, Object> dataByThisDeptTotal = procFormDataMapper.getDataByThisDeptTotal(theme,withProc, status, userIds);
            rspData.setTotal(Long.valueOf(dataByThisDeptTotal.get("total").toString()));
            rspData.setRows(procFormDataList);

        }else  if(activeNameFirst.equals("first") && activeNameSecend.equals("third")){
            //草稿
            status=5;
            List<Map<String,Object>> procFormDataList = procFormDataMapper.getDataByThisDept(theme,withProc,status,userIds);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            Map<String, Object> dataByThisDeptTotal = procFormDataMapper.getDataByThisDeptTotal(theme,withProc, status, userIds);
            rspData.setTotal(Long.valueOf(dataByThisDeptTotal.get("total").toString()));
            rspData.setRows(procFormDataList);
        }else  if(activeNameFirst.equals("first") && activeNameSecend.equals("fourth")){
            //完成
            status=1;
            List<Map<String,Object>> procFormDataList = procFormDataMapper.getDataByThisDept(theme,withProc,status,userIds);

            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            Map<String, Object> dataByThisDeptTotal = procFormDataMapper.getDataByThisDeptTotal(theme,withProc, status, userIds);
            rspData.setTotal(Long.valueOf(dataByThisDeptTotal.get("total").toString()));
            rspData.setRows(procFormDataList);
        }else  if(activeNameFirst.equals("first") && activeNameSecend.equals("fieth")){
            //终止
            status=3;
            List<Map<String,Object>> procFormDataList = procFormDataMapper.getDataByThisDept(theme,withProc,status,userIds);

            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            Map<String, Object> dataByThisDeptTotal = procFormDataMapper.getDataByThisDeptTotal(theme,withProc, status, userIds);
            rspData.setTotal(Long.valueOf(dataByThisDeptTotal.get("total").toString()));
            rspData.setRows(procFormDataList);
        }


        return rspData;
    }


    public Page<ActTaskDTO> selectTaskList(PageDomain pageDomain) {
        Page<ActTaskDTO> list = new Page<ActTaskDTO>();
        String userName = SecurityUtils.getUsername();
        int totalItems = taskService.createTaskQuery().taskCandidateOrAssigned(userName).list().size();
        List<org.activiti.engine.task.Task> tasks = taskService.createTaskQuery().taskCandidateOrAssigned(userName).orderByTaskCreateTime().desc().listPage((pageDomain.getPageNum() - 1) * pageDomain.getPageSize(), pageDomain.getPageSize());
        list.setTotal(totalItems);
        if (totalItems != 0) {
            Set<String> processInstanceIdIds = tasks.parallelStream().map(t -> t.getProcessInstanceId()).collect(Collectors.toSet());
            List<org.activiti.engine.runtime.ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery().processInstanceIds(processInstanceIdIds).list();
            List<ActTaskDTO> actTaskDTOS = tasks.stream()
                    .map(t -> new ActTaskDTO(t, processInstanceList.parallelStream().filter(pi -> t.getProcessInstanceId().equals(pi.getId())).findAny().get()))
                    .collect(Collectors.toList());

            //获取代办任务对应的表单定义
            actTaskDTOS.stream().forEach(actTaskDTO -> {
                ProcFormDef procFormDef = new ProcFormDef();
                procFormDef.setRefProcKey(actTaskDTO.getDefinitionKey());
                List<ProcFormDef> procFormDefs = procFormDefMapper.selectProcFormDefList(procFormDef);
                if (!CollectionUtils.isEmpty(procFormDefs)) {
                    actTaskDTO.setFormDef(procFormDefs.get(0).getDefination());
                }
                Map<String, String> dataMap = procFormDataMapper.selectByBusinessKey(actTaskDTO.getBusinessKey(),SecurityUtils.getUsername());


                actTaskDTO.setLoginUser(SecurityUtils.getUsername());
                //获取公司  流程所属分类 模板名称


                actTaskDTO.setLoginUser(SecurityUtils.getLoginUser().getUser().getNickName());
                actTaskDTO.setLoginUserStatus(StringUtils.EMPTY);
                if ("1".equals(SecurityUtils.getLoginUser().getUser().getStatus())) {
                    actTaskDTO.setLoginUserStatus("(停用)");
                }
                if(null != dataMap){
                    actTaskDTO.setCreateBy(dataMap.get("createBy"));
                    actTaskDTO.setLastPass(dataMap.get("pass"));
                    actTaskDTO.setNickName(dataMap.get("nickName"));
                    actTaskDTO.setTheme(dataMap.get("theme"));
                    actTaskDTO.setTemplateName(dataMap.get("templateName"));
                    actTaskDTO.setOpcName(dataMap.get("opcName"));
                    actTaskDTO.setShortName(dataMap.get("shortName"));
                    actTaskDTO.setUrgency(dataMap.get("urgency"));
                    actTaskDTO.setUserStatus(dataMap.get("userStatus"));
                }else if(null == dataMap ||dataMap.get("status").equals("3")) {
                    //
                    //        //根据businessId获取实例id  因为可能存在删掉pdf表中的数据 所以去 act_ru_execution  查询
                    String instanceId =  oaProcessTemplateMapper.getDataByActRuExecution(actTaskDTO.getBusinessKey());
                    List<String> taskIds =  oaProcessTemplateMapper.getTaskIds(instanceId);
                    for (String taskId : taskIds) {
                        oaProcessTemplateService.endTask(null,taskId,false);
                    }
                }

            });

            list.addAll(actTaskDTOS);

        }
        return list;
    }

    public Page<ActTaskDTO> selectTaskList1(PageDomain pageDomain, List<Map<String,Object>> rejectList, ProcFormData procFormData, OaSystemUtil oaSystemUtil, boolean countFlag) {
        Page<ActTaskDTO> list = new Page<ActTaskDTO>();
        String userName = SecurityUtils.getUsername();
        int totalItems = taskService.createTaskQuery().taskCandidateOrAssigned(userName).taskCreatedAfter(DateUtils.parseDate(procFormData.getInitiatorStartTime())).taskCreatedBefore(DateUtils.parseDate(procFormData.getInitiatorEndTime())).list().size();
        List<org.activiti.engine.task.Task> tasks = taskService.createTaskQuery().taskCandidateOrAssigned(userName).taskCandidateOrAssigned(userName).taskCreatedAfter(DateUtils.parseDate(procFormData.getInitiatorStartTime())).taskCreatedBefore(DateUtils.parseDate(procFormData.getInitiatorEndTime())).orderByTaskCreateTime().desc().list();
//                .listPage((pageDomain.getPageNum() - 1) * pageDomain.getPageSize(), pageDomain.getPageSize());
        //筛选的条件
        List<Long> templateIdList = procFormData.getTemplateIdList();
        List<Long> userIdList = procFormData.getUserIdList();
        String theme = procFormData.getTheme();
        if (theme != null && !StringUtils.EMPTY.equals(theme)) {
            theme = ".*" + theme + ".*";
        }
        //要筛选删除的taskId集合，后续删除用
        Set<String> taskIdList= new HashSet<>();
        for (org.activiti.engine.task.Task task : tasks) {
            Map<String, Object> variables = taskService.getVariables(task.getId());
            Long templateId = (Long) variables.get("templateId");
            Long initiatorUserId = (Long) variables.get("initiatorUserId");
            String initiatorTheme = (String) variables.get("initiatorTheme");
            if (templateIdList != null) {
                //得到的模板筛选集合，看与本流程的模板是否对应
                boolean templateFlag = templateIdList.stream().anyMatch(t -> t.equals(templateId));
                if (!templateFlag) {
                    taskIdList.add(task.getId());
                }
            }
            if (userIdList != null) {
                //得到的模板筛选集合，看与本流程的模板是否对应
                boolean userIdFlag = userIdList.stream().anyMatch(t -> t.equals(initiatorUserId));
                if (!userIdFlag) {
                    taskIdList.add(task.getId());
                }
            }
            if (theme != null && !StringUtils.EMPTY.equals(theme)) {
                //模糊查询主题
                boolean matchesFlag = false;
                if (initiatorTheme != null && !StringUtils.EMPTY.equals(initiatorTheme)) {
                    matchesFlag= initiatorTheme.matches(theme);
                }
                if (!matchesFlag) {
                    taskIdList.add(task.getId());
                }
            }
            //进行驳回的插入条件
            String processInstanceId = task.getProcessInstanceId();
            String id = task.getId();
            org.activiti.engine.runtime.ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            for (Map<String, Object> map : rejectList) {
                if (processInstance.getBusinessKey().equals(map.get("businessId"))) {
                    taskIdList.add(id);
                }
            }
        }
        //清楚所有数据标识
        boolean cleanFlag = false;
        if (userIdList == null && oaSystemUtil.getInitiatorNickName() != null && !StringUtils.EMPTY.equals(oaSystemUtil.getInitiatorNickName())) {
            cleanFlag = true;
        }
//        if (userIdList == null && oaSystemUtil.getInitiatorNickName() != null && !StringUtils.EMPTY.equals(oaSystemUtil.getInitiatorNickName())) {
//            cleanFlag = true;
//        }
        //进行条件的过滤
        if (cleanFlag) {
            //没查出来，直接清空tasks
            tasks = new ArrayList<>();
        } else {
            //查出来结果，把不符合的都剔除掉
            for (String taskId:taskIdList) {
                tasks.removeIf(e -> e.getId().equals(taskId));
            }
        }
        int size = taskIdList.size();
        int i = totalItems - size;
        if (cleanFlag) {
            i = 0;
        }
        list.setTotal(i);
        if (countFlag) {
            return list;
        }
        if (tasks.size() > pageDomain.getPageSize()) {
            //如果过滤后的size大于分页size，那么就切割
            //起始索引
            int startIndex = (pageDomain.getPageNum() - 1) * pageDomain.getPageSize();
            //结束索引
            int endIndex = startIndex + pageDomain.getPageSize();
            if (endIndex > tasks.size()) {
                endIndex = tasks.size();
            }
            tasks = tasks.subList(startIndex, endIndex);
        }
        if (i != 0) {
            Set<String> processInstanceIdIds = tasks.parallelStream().map(t -> t.getProcessInstanceId()).collect(Collectors.toSet());
            List<org.activiti.engine.runtime.ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery().processInstanceIds(processInstanceIdIds).list();
            List<ActTaskDTO> actTaskDTOS = tasks.stream()
                    .map(t -> new ActTaskDTO(t, processInstanceList.parallelStream().filter(pi -> t.getProcessInstanceId().equals(pi.getId())).findAny().get()))
                    .collect(Collectors.toList());

            //获取代办任务对应的表单定义
            actTaskDTOS.stream().forEach(actTaskDTO -> {
                ProcFormDef procFormDef = new ProcFormDef();
                procFormDef.setRefProcKey(actTaskDTO.getDefinitionKey());
                List<ProcFormDef> procFormDefs = procFormDefMapper.selectProcFormDefList(procFormDef);
                if (!CollectionUtils.isEmpty(procFormDefs)) {
                    actTaskDTO.setFormDef(procFormDefs.get(0).getDefination());
                }
                Map<String, String> dataMap = procFormDataMapper.selectByBusinessKey(actTaskDTO.getBusinessKey(),SecurityUtils.getUsername());


                actTaskDTO.setLoginUser(SecurityUtils.getUsername());
                //获取公司  流程所属分类 模板名称


                actTaskDTO.setLoginUser(SecurityUtils.getLoginUser().getUser().getNickName());
                actTaskDTO.setLoginUserStatus(StringUtils.EMPTY);
                if ("1".equals(SecurityUtils.getLoginUser().getUser().getStatus())) {
                    actTaskDTO.setLoginUserStatus("(停用)");
                }
                if(null != dataMap){
                    actTaskDTO.setCreateBy(dataMap.get("createBy"));
                    actTaskDTO.setLastPass(dataMap.get("pass"));
                    actTaskDTO.setNickName(dataMap.get("nickName"));
                    actTaskDTO.setTheme(dataMap.get("theme"));
                    actTaskDTO.setTemplateName(dataMap.get("templateName"));
                    actTaskDTO.setOpcName(dataMap.get("opcName"));
                    actTaskDTO.setShortName(dataMap.get("shortName"));
                    actTaskDTO.setUrgency(dataMap.get("urgency"));
                    actTaskDTO.setUserStatus(dataMap.get("userStatus"));
                    actTaskDTO.setReadFlag(dataMap.get("readFlag"));
                }else if(null == dataMap ||dataMap.get("status").equals("3")) {
                    //
                    //        //根据businessId获取实例id  因为可能存在删掉pdf表中的数据 所以去 act_ru_execution  查询
                    String instanceId =  oaProcessTemplateMapper.getDataByActRuExecution(actTaskDTO.getBusinessKey());
                    List<String> taskIds =  oaProcessTemplateMapper.getTaskIds(instanceId);
                    for (String taskId : taskIds) {
                        oaProcessTemplateService.endTask(null,taskId,false);
                    }
                }

            });

            list.addAll(actTaskDTOS);

        }
        return list;
    }

    @Override
    public AjaxResult getFlowInfoByBusinessId(String businessId) {
        //获取发起的流程表单信息
        String userId = SecurityUtils.getUsername();
        Map<String, Object> map = new HashMap<>();
        ProcFormData procFormData = new ProcFormData();
        procFormData.setBusinessId(businessId);
        procFormData.setCreateBy(userId);
        procFormData.setWithProc(PROC_YES);
        ProcFormData procFormData1 = procFormDataMapper.selectProcFormDataByBusinessId(procFormData);
        if (procFormData1 == null) {
            procFormData.setOid(businessId);
            procFormData.setBusinessId(null);
            procFormData1 = procFormDataMapper.selectProcFormDataByBusinessId(procFormData);
        }
        if(!Objects.isNull(procFormData1)) {
            procFormData1.setData(JSON.parseObject(procFormData1.getDataJson()));
            //查询流程的状态
            String flowStatus = procFormDataMapper.selectProcFormDataStatusByBusinessId(businessId);
            //获取审批记录
            ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
            procWorkflowFormdata.setBusinessKey(businessId);
            List<ProcWorkflowFormdata> list = ProcWorkflowFormdataService.selectProcWorkflowFormdataList(procWorkflowFormdata);
            //获取审批记录文件信息
            List<FlowFileInfoDTO> uploadInfo = procWorkflowFormdataMapper.selectProcWorkflowFormdataFilesListByBusinessId(businessId);
            //对所有信息进行分组
            for (ProcWorkflowFormdata procWorkflowFormdata1:list) {
                String stepId = procWorkflowFormdata1.getStepId();
                String pwfId = procWorkflowFormdata1.getId();
                //根据步骤id找到所有审批记录文件信息集合中属于他的信息
                procWorkflowFormdata1.setUploadInfo(uploadInfo.stream().filter(t -> t.getStepId().equals(stepId) && t.getSoleFlag().equals(pwfId)).collect(Collectors.toList()));
            }

            String currentPass = "";
            if (list.size() > 0 && !CollectionUtils.isEmpty(list)){
                currentPass = list.get(list.size()-1).getPass();
            }
            boolean isChangeSoleFlag = false;
            if(StringUtils.isNotEmpty(currentPass) && ("5".equals(currentPass) || "6".equals(currentPass))){
                isChangeSoleFlag = true;
            }
            if ("1".equals(flowStatus)) {
                ProcWorkflowFormdata procWorkflowFormdata1 = list.stream().max(Comparator.comparing(ProcWorkflowFormdata::getCreateTime)).get();
                procWorkflowFormdata1.setPass("999");
                map.put("approveAlreadyPass" , true);
            } else if("2".equals(flowStatus)) {
                map.put("approveAlreadyPass" , true);
            }else{
                map.put("approveAlreadyPass" , false);
            }

            //基本信息和审批记录放到map里返给页面
            map.put("flowInfo", procFormData1);
            map.put("examineAndApproveRecord", list);
            map.put("isChangeSoleFlag",isChangeSoleFlag);

//        ProcessInstance processInstance = processRuntime.processInstance(procFormData1.getInstanceId());
            List<org.activiti.engine.task.Task> taskList = new LinkedList<>();
            if (procFormData1.getInstanceId() != null) {
                taskList = taskService.createTaskQuery().processInstanceId(procFormData1.getInstanceId()).list();
            }
            List< Map<String, Object>> currentNodeInfoList = new LinkedList<>();
            if (taskList.size()>0) {
                for (org.activiti.engine.task.Task task : taskList) {
                    Map<String, Object> currentNodeInfo = new HashMap<>();
                    List<String> candidateUsers = null;
                    List<String> candidateGroups = null;
                    String assignee = null;
                    String taskDefinitionKey = task.getTaskDefinitionKey();
                    //通过taskId获取taskDefinitionKey
                    HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                            .processInstanceId(procFormData1.getInstanceId()).singleResult();
                    //获取bpmnModel对象
                    BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
                    //因为我们这里只定义了一个Process 所以获取集合中的第一个即可
                    Process process = bpmnModel.getProcesses().get(0);
                    //获取所有的FlowElement信息
                    List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);
                    for (UserTask userTask:userTaskList) {
                        //获取当前任务节点Id
                        String id = userTask.getId();
                        if (id.equals(taskDefinitionKey)) {
                            String name = userTask.getName();
                            //获取当前处理人和岗位
                            currentNodeInfo.put("currentNode", name);
                            assignee = task.getAssignee();
                            candidateUsers = userTask.getCandidateUsers();
                            candidateGroups = userTask.getCandidateGroups();
                            if (assignee != null) {
                                if (candidateUsers.size() > 0) {
                                    String s = candidateUsers.get(0);
                                    if (!s.equals(assignee)) {
                                        List<String> newCandidateUsers = new ArrayList<>();
                                        newCandidateUsers.add(assignee);
                                        candidateUsers = newCandidateUsers;
                                    }
                                } else {
                                    List<String> newCandidateUsers = new ArrayList<>();
                                    newCandidateUsers.add(assignee);
                                    candidateUsers = newCandidateUsers;
                                }
                            }else if(null==assignee && candidateUsers.size()==0 && candidateGroups.size() >0){
                                List<SysUser> userList = getUserListByPostCode(candidateGroups.get(0));
                                List<String> newCandidateUsers = new ArrayList<>();
                                userList.forEach(row ->{
                                    newCandidateUsers.add(row.getUserName());
                                });
                                candidateUsers = newCandidateUsers;
                            }
                        }
                    }
                    List<String> candidateUsersOfUserName = new ArrayList<>();
                    for (String candidateUser : candidateUsers) {
                        String nickName = StringUtils.EMPTY;
                        if (null != candidateUser) {
                            SysUser sysUser = sysUserMapper.selectUserByUserName(candidateUser);
                            if (sysUser != null) {
                                nickName = sysUser.getNickName();
                                String status = sysUser.getStatus();
                                if ("1".equals(status)) {
                                    nickName = "<span>" + nickName + "</span>" + "<span style=\"color: #CCCCCC\">(停用)</span>";
                                }
                                candidateUsersOfUserName.add(nickName);
                            }
                        }
                    }
                    //是否存在驳回后屎尿屁通过后回到指定节点
                    boolean specifiedNode = false;
                    Map variables2 = taskService.getVariables(task.getId());
                    JsonNode rejectFlowNodeSequence = (JsonNode) variables2.get("rejectFlowNodeSequence");
                    if(null != rejectFlowNodeSequence){
                        specifiedNode= true;
                    }
                    currentNodeInfo.put("specifiedNode",specifiedNode);
                    currentNodeInfo.put("taskId", task.getId());
                    currentNodeInfo.put("stepId", taskDefinitionKey);
                    currentNodeInfo.put("currentCandidateUsers", candidateUsers);
                    currentNodeInfo.put("currentCandidateUsersOfUserName", candidateUsersOfUserName);
                    currentNodeInfo.put("currentCandidateGroups", candidateGroups);
                    currentNodeInfo.put("assignee", assignee);
                    currentNodeInfoList.add(currentNodeInfo);
                }
                map.put("currentNodeInfo", currentNodeInfoList);
                //获取终稿扫描件list
                List<DaArchivistFilesVo> daArchivistFilesVoList = daArchivistFilesService.selectDaArchivistFilesListByFlowId(businessId);
                if (daArchivistFilesVoList.size() > 0 && daArchivistFilesVoList != null){
                    map.put("fileArray",daArchivistFilesVoList);
                }
            } else {
                Map<String, Object> currentNodeInfo = new HashMap<>();
                currentNodeInfo.put("taskId", null);
                currentNodeInfo.put("stepId", null);
                currentNodeInfo.put("currentNode", null);
                currentNodeInfo.put("currentCandidateUsers", null);
                currentNodeInfo.put("currentCandidateGroups", null);
                currentNodeInfo.put("assignee", null);
                currentNodeInfo.put("specifiedNode",false);
                currentNodeInfoList.add(currentNodeInfo);
                map.put("currentNodeInfo", currentNodeInfoList);
                //获取终稿扫描件list
                List<DaArchivistFilesVo> daArchivistFilesVoList = daArchivistFilesService.selectDaArchivistFilesListByFlowId(businessId);
                if (daArchivistFilesVoList.size() > 0 && daArchivistFilesVoList != null){
                    map.put("fileArray",daArchivistFilesVoList);
                }else {
                    map.put("fileArray",null);
                }
            }
        }
        return new AjaxResult(HttpStatus.SUCCESS, "ok", map);
    }

    @Override
    public AjaxResult saveFlow(SaveDataBo startBO, LoginUser loginUser) {
        String data = startBO.getData();
        JSONObject jsonObject = JSON.parseObject(data);
        jsonObject.put("userId", loginUser.getUserId());
        String dataJsonNew = JSONObject.toJSONString(jsonObject);
        //add by ny 保存流程草稿时校验借用时间
        OaProcessTemplate oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateById(startBO.getTemplateId());
        List<ZzPendingDetail> zzPendingDetailList = this.licenseDateVerify(oaProcessTemplate, startBO);
        if (!CollectionUtils.isEmpty(zzPendingDetailList)){
            return AjaxResult.success("false",zzPendingDetailList);
        }
        startBO.setData(dataJsonNew);
        if (startBO.getOid() != null) {
            FlowInfoDTO flowInfoDTO = new FlowInfoDTO();
            flowInfoDTO.setOid(startBO.getOid());
            flowInfoDTO.setFormId(startBO.getFormId());
            flowInfoDTO.setFormName(startBO.getFormName());
            flowInfoDTO.setData(startBO.getData());
            flowInfoDTO.setWithProc(PROC_NO);
            flowInfoDTO.setStatus(startBO.getStatus());
            flowInfoDTO.setProcKey(startBO.getProcKey());
            flowInfoDTO.setUpdateTime(DateUtils.getNowDate());
            flowInfoDTO.setCurrentStatus(startBO.getStatus());
            flowInfoDTO.setUrgency(startBO.getUrgency());
            flowInfoDTO.setTheme(startBO.getTheme());

//            ProcFormData procFormData = new ProcFormData();
//            procFormData.setOid(startBO.getOid());
//            procFormData.setFormId(startBO.getFormId());
//            procFormData.setFormName(startBO.getFormName());
//            procFormData.setDataJson(startBO.getData());
//
//            procFormData.setUpdateTime(DateUtils.getNowDate());
//
//
//            procFormData.setWithProc(PROC_NO);
//            procFormData.setStatus(startBO.getStatus());
//            procFormData.setProcKey(startBO.getProcKey());
//            procFormData.setTheme(startBO.getTheme());
//            procFormData.setUrgency(startBO.getUrgency());
            procFormDataMapper.updateProcFormData1(flowInfoDTO);
            return AjaxResult.success("草稿保存成功", startBO.getOid());
        } else {
            String id = UUID.randomUUID().toString();
            ProcFormData procFormData = new ProcFormData();
            procFormData.setOid(id);
            procFormData.setFormId(startBO.getFormId());
            procFormData.setFormName(startBO.getFormName());
            procFormData.setDataJson(startBO.getData());

            procFormData.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
            procFormData.setCreateBy(SecurityUtils.getUsername());
            procFormData.setCreateTime(DateUtils.getNowDate());


            procFormData.setWithProc(PROC_NO);
            procFormData.setStatus(startBO.getStatus());
            procFormData.setProcKey(startBO.getProcKey());
            procFormData.setTheme(startBO.getTheme());
            procFormData.setCompanyId(startBO.getCompanyId());
            procFormData.setClassificationId(startBO.getClassificationId());
            procFormData.setTemplateId(startBO.getTemplateId());
            procFormData.setUserId(loginUser.getUserId());
            procFormData.setUrgency(startBO.getUrgency());
            procFormDataMapper.insertProcFormData(procFormData);
            return AjaxResult.success("草稿保存成功", id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult startFlowNew(SaveDataBo saveDataBo, LoginUser loginUser) {
        OaProcessTemplate oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateById(saveDataBo.getTemplateId());
        if(null == oaProcessTemplate || "N".equalsIgnoreCase(oaProcessTemplate.getIsEnable())){
            return AjaxResult.error("流程启动失败，该模板已作废，请重新选择模板");
        }
        String id = UUID.randomUUID().toString();
        List<ZzPendingDetail> zzPendingDetailList = this.licenseDateVerify(oaProcessTemplate, saveDataBo);
        if (!CollectionUtils.isEmpty(zzPendingDetailList)){
            return AjaxResult.success("false",zzPendingDetailList);
        }
        ProcFormData procFormData = new ProcFormData();
        procFormData.setOid(id);
        procFormData.setFormId(saveDataBo.getFormId());
        String data = saveDataBo.getData();
        JSONObject jsonObject = JSON.parseObject(data);
        jsonObject.put("userId", loginUser.getUserId());
        String dataJsonNew = JSONObject.toJSONString(jsonObject);
        saveDataBo.setData(dataJsonNew);
        procFormData.setDataJson(dataJsonNew);
        procFormData.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
        procFormData.setCreateBy(SecurityUtils.getUsername());
        procFormData.setCreateTime(DateUtils.getNowDate());
        procFormData.setFormName(saveDataBo.getFormName());
        String taskDefinitionKey = "";
        if (!StringUtils.isEmpty(saveDataBo.getProcKey())) {
            JSONObject formJson = JSON.parseObject(saveDataBo.getData());
            //设置动态变量
            HashMap<String, Object> variables = new HashMap<String, Object>();
            variables.put(LOGINUSER, SecurityUtils.getUsername());
//            variables.put(APPROVER, saveDataBo.getApprover());
            variables.put("customParam", formJson);
            //新加入参数，后续进行查看的时候进行判断
            variables.put("templateId", saveDataBo.getTemplateId());
            variables.put("templateType", oaProcessTemplate.getTemplateType());
            variables.put("initiatorUserId", loginUser.getUserId());
            variables.put("initiatorTheme", saveDataBo.getTheme());
            if(null != saveDataBo.getCustomFlag() && saveDataBo.getCustomFlag()){
                variables.put("voucherDate",saveDataBo.getVoucherDate());
            }
            ProcessInstance processInstance = processRuntime.start(ProcessPayloadBuilder
                    .start()
                    .withProcessDefinitionKey(saveDataBo.getProcKey())
                    .withProcessDefinitionId(saveDataBo.getFlowFullId())
                    .withName(SecurityUtils.getLoginUser().getUser().getNickName() + "的" + saveDataBo.getFormName() + "申请")
                    .withBusinessKey(id)
                    .withVariables(variables)
                    .build());

            //查询第一个任务,并自动审批通过
//            TaskQuery taskQuery = taskService.createTaskQuery().active().taskAssignee(SecurityUtils.getUsername()).processDefinitionKey(saveDataBo.getProcKey());
            TaskQuery taskQuery1 = taskService.createTaskQuery().active().processInstanceId(processInstance.getId());
            org.activiti.engine.task.Task task1 = taskQuery1.singleResult();
            taskDefinitionKey = task1.getTaskDefinitionKey();
            BpmnModel bpmnModel1 = repositoryService.getBpmnModel(task1.getProcessDefinitionId());
            UserTask userTask = (UserTask) bpmnModel1.getMainProcess().getFlowElement(taskDefinitionKey);
            Integer i = getExclusiveGateway(SecurityUtils.getUsername(),userTask);
            if(null != i){
                formJson.put(IS_SKIP,i);
                variables.put("customParam", formJson);
            }
            //起草节点修改流程审批人为流程发起人
            if(StringUtils.isEmpty(task1.getAssignee()) || !SecurityUtils.getUsername().equals(task1.getAssignee())){
                taskService.setAssignee(task1.getId(),SecurityUtils.getUsername());
            }
            logger.info("发起流程,业务主键：{},审批流程procInstanceId：{},当前审批节点：{},下一审批节点：{}",id,processInstance.getId(),taskDefinitionKey);
            taskRuntime.complete(TaskPayloadBuilder.complete().withTaskId(task1.getId())
                    .withVariables(variables)
                    .build());

            ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
            procWorkflowFormdata.setId(UUID.randomUUID().toString());
            procWorkflowFormdata.setBusinessKey(id);
            procWorkflowFormdata.setComment("提交申请");
            procWorkflowFormdata.setPass("1");//同意
            procWorkflowFormdata.setTaskNodeName(task1.getName());
            procWorkflowFormdata.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
            procWorkflowFormdata.setCreateBy(SecurityUtils.getUsername());
            procWorkflowFormdata.setStepId(taskDefinitionKey);
            ProcWorkflowFormdataService.insertProcWorkflowFormdata(procWorkflowFormdata);

            procFormData.setBusinessId(id);
            procFormData.setProcKey(saveDataBo.getProcKey());
            procFormData.setInstanceId(processInstance.getId());
            procFormData.setWithProc(PROC_YES);
            procFormData.setTheme(saveDataBo.getTheme());
            procFormData.setStatus(saveDataBo.getStatus());
            procFormData.setCompanyId(saveDataBo.getCompanyId());
            procFormData.setClassificationId(saveDataBo.getClassificationId());
            procFormData.setTemplateId(saveDataBo.getTemplateId());
            procFormData.setUserId(loginUser.getUserId());
            procFormData.setUrgency(saveDataBo.getUrgency());
            Long processNumber = procFormDataMapper.selectMaxProcessNumber();
            if (processNumber == null) {
                procFormData.setProcessNumber(1L);
            } else {
                processNumber = processNumber + 1;
                procFormData.setProcessNumber(processNumber);
            }
            logger.info("发起流程,业务主键：{},审批流程procInstanceId：{},下一节点信息：{}", id, processInstance.getId(), taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult());

            //查找是否还存在下一节点审批，不存在直接修改状态为审批通过
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstance.getId()).singleResult();
            if (historicProcessInstance.getEndTime() != null) {
                procFormData.setStatus(PASS);
            }

            //找下一个节点，给下一个节点设置审批人
            if (saveDataBo.getNextFlowApproveUserName() != null) {
                BpmnModel bpmnModel = repositoryService.getBpmnModel(saveDataBo.getFlowFullId());
                UserTask nextFlow = (UserTask) bpmnModel.getMainProcess().getFlowElement(saveDataBo.getNodeId());
                org.activiti.engine.task.Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
                if (nextFlow.getCandidateUsers().size() > 1) {
                    taskService.setAssignee(task.getId(), saveDataBo.getNextFlowApproveUserName());
                }
                if (nextFlow.getCandidateGroups().size() != 0) {
                    taskService.setAssignee(task.getId(), saveDataBo.getNextFlowApproveUserName());
                }
            }
            //通知审核人
            NotifyReviewerDto notifyReviewerDto = NotifyReviewerDto.builder()
                    .processInstanceId(processInstance.getId()).taskDefinitionKey(taskDefinitionKey).templateId(saveDataBo.getTemplateId()).theme(saveDataBo.getTheme())
                    .startNickName(loginUser.getUser().getNickName()).createTime(procFormData.getCreateTime())
                    .build();
            notifyReviewer(notifyReviewerDto);
        } else {
            procFormData.setWithProc(PROC_NO);
//            procFormData.setStatus(PASS);
        }

        //先判断是否是财务流程 再进行金额2024-03-26 wangzeyu
//        OaProcessTemplate oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateById(procFormData.getTemplateId());
        if(oaProcessTemplate.getTemplateType().equals("1")){
            BigDecimal bigDecimal = this.addCaiWuQuery(procFormData);
            procFormData.setAmount(bigDecimal);
            //未审核
            procFormData.setChunaCheck("1");
            //未生成
            procFormData.setVoucharGenerate("3");
            procFormDataMapper.insertProcFormData(procFormData);
        }else {
            procFormDataMapper.insertProcFormData(procFormData);
        }
        //解析流程模版存入相应表中
        this.parsingFlowData(procFormData.getBusinessId(),procFormData.getTemplateId());
        //新增数据至预审批数据表
        YspProcessTemplate yspProcessTemplate = new YspProcessTemplate();
        //由于模版可能会有版本问题  所以这里的模版id使用的是模板的parentid
        OaProcessTemplate oaProcessTemplateById = oaProcessTemplateMapper.selectOaProcessTemplateById(procFormData.getTemplateId());


        OaTemplateType oaTemplateType = new OaTemplateType();
        oaTemplateType.setConfigurationItemsType("yspTemplate_type");
        oaTemplateType.setTemplateId(oaProcessTemplateById.getParentId());
        List<OaTemplateType> oaTemplateTypes = oaTemplateTypeMapper.selectOaTemplateTypeList(oaTemplateType);
        String processType = null;
        if(oaTemplateTypes.size()>0){
            for (OaTemplateType templateType : oaTemplateTypes) {
                processType = processType+","+templateType.getConfigurationItemsValue();
            }

        }
        if(null!=processType){
            yspProcessTemplate.setTemplateId(oaProcessTemplateById.getParentId());
            yspProcessTemplate.setBusinessId(procFormData.getBusinessId());
            yspProcessTemplate.setProcessType(processType);
            int i = yspProcessTemplateMapper.insertYspProcessTemplate(yspProcessTemplate);
        }else {
            logger.info("此流程没有配置流程类型，流程模板为：”"+oaProcessTemplateById.getTemplateName()+"“");
        }



        //
        //判断是否资料下载流程
        if ("2".equals(oaProcessTemplate.getOaModuleType())){
            Object isGuarantee = jsonObject.get("isGuarantee");
            Object projectList = jsonObject.get("projectList");
            if("1".equals(isGuarantee)){
                String theme = saveDataBo.getTheme();
                String user = "";
                String[] parts = theme.split("——");
                if (parts.length >= 2) {
                    user = parts[1].trim(); // 去除前后空格
                }
                InformationUsed informationUsed = new InformationUsed();
                informationUsed.setInformationUserName(saveDataBo.getTheme());
                informationUsed.setUser(user);
                informationUsed.setAuditTime(DateUtils.getNowDate());
                informationUsed.setSponsor(loginUser.getUser().getNickName());
                informationUsed.setProcessId(id);
                informationUsed.setSponsorName(loginUser.getUser().getUserName());
                informationUsed.setUsedCompanyId(saveDataBo.getCompanyId());

                informationUsed.setProjectId(projectList == null ? null : Long.valueOf(projectList.toString()));
                informationUsedService.insertInformationUsed(informationUsed);
            }
        }
        //流程提醒配置--判断是否需要发送代办，审批通过后发送代办提醒  2024/1/15  lichen
        ExecBO execBO = new ExecBO();
        execBO.setBusinessKeyToTS(saveDataBo.getFlowFullId());
        execBO.setStepId(taskDefinitionKey);
        setRemind(execBO,id);
        return AjaxResult.success("流程启动成功", id);
    }

    /**
     * wangzeyu
     * 获取此流程金额
     * 2024-03-26
     * @param procFormData
     * @return int
     */
    public BigDecimal addCaiWuQuery(ProcFormData procFormData){
        //填写的表单数据
        JSONObject data = JSONObject.parseObject(procFormData.getDataJson());
        BigDecimal amount = new BigDecimal("0.00");
        //获取金额 查询当前流程模板的记账凭证规则 获取其中金额
        OaVoucherRulesMain oaVoucherRulesMain = new OaVoucherRulesMain();
        oaVoucherRulesMain.setFlowModelId(procFormData.getTemplateId());
        List<OaVoucherMainVo> oaVoucherMainVos = oaVoucherRulesMainServiceImpl.selectOaVoucherRulesMainList(oaVoucherRulesMain, "0", null, null);
        if(oaVoucherMainVos.size()>0){
            OaVoucherMainVo oaVoucherMainVo = oaVoucherMainVos.get(0);
            List<OaVoucherRulesViceVo> oaVoucherRulesViceVos = oaVoucherMainVo.getOaVoucherRulesViceVos();
            if(oaVoucherRulesViceVos.size()>0){
                OaVoucherRulesViceVo oaVoucherRulesViceVo = oaVoucherRulesViceVos.get(0);
                List<OaVoucherRulesSubject> oaVoucherRulesSubjects = oaVoucherRulesViceVo.getOaVoucherRulesSubjects();
                if(oaVoucherRulesSubjects.size()>0){
                    OaVoucherRulesSubject oaVoucherRulesSubject = oaVoucherRulesSubjects.get(0);
                    //金额字段
                    String accountingField = oaVoucherRulesSubject.getAccountingField();
                    BigDecimal formAmount = new BigDecimal(data.get(accountingField).toString());
                    amount = formAmount;
                }
            }
        }
        return amount;
    }

    /**
     * 证照借用模板借用时间校验
     *
     * @param oaProcessTemplate
     * @param saveDataBo
     */
    @SneakyThrows
    private List<ZzPendingDetail> licenseDateVerify(OaProcessTemplate oaProcessTemplate, SaveDataBo saveDataBo) {
        List<String> keyList = new ArrayList<>();
        List<ZzPendingDetail> returnList = new ArrayList<>();
        if (!Objects.isNull(saveDataBo)) {
            if (!"10".equals(oaProcessTemplate.getOaModuleType())) {
                return returnList;
            }
            List<OaFormField> oaFormFieldList = zzPendingDetailMapper.selectFormFieldByFormId(saveDataBo.getFormId());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oaFormFieldList)) {
                oaFormFieldList.forEach(info -> {
                    if (info.getFieldName().contains(ZzConstant.JIEYUE_DATE) || info.getFieldName().contains(ZzConstant.JIEYONG_DATE)) {
                        String key = info.getFieldKey();
                        keyList.add(key);
                    }
                });
            }
            // 根据key查询json字符串
            String beginTime = "";
            String endTime = "";
            Date borrowStartTime = null;
            Date borrowEndTime = null;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
            String data = saveDataBo.getData();
            for (int i = 0; i < keyList.size(); i++) {
                int j = data.indexOf(keyList.get(i));
                int mm = j + keyList.get(i).length() + 3;
                String substring = data.substring(mm);
                int j1 = substring.indexOf("\"");
                if (i == ZzConstant.INTEGER_0) {
                    beginTime = substring.substring(0, j1);
                    //复制流程重新发起时，不修改借用日期会报错，此处直接重新拼接年月日时分秒
                    if (!beginTime.contains(":")){
                        beginTime = beginTime + " 00:00:00";
                    }
                    try {
                        borrowStartTime = sdf.parse(beginTime);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
                if (i == ZzConstant.INTEGER_1) {
                    endTime = substring.substring(0, j1);
                    //复制流程重新发起时，不修改借用日期会报错，此处直接重新拼接年月日时分秒
                    if (!endTime.contains(":")){
                        endTime = beginTime + " 00:00:00";
                    }
                    try {
                        borrowEndTime = sdf.parse(endTime);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            if (borrowStartTime.compareTo(borrowEndTime) >= 0) {
                throw new ServiceException("借用开始时间不能大于等于借用结束时间，请确认后重试");
            }
            String format1 = sdf2.format(borrowStartTime);
            String format2 = sdf2.format(DateUtils.getNowDate());
            if (format1.compareTo(format2) < 0){
                throw new ServiceException("借用开始日期不得小于当前日期，请确认后重试");
            }

            //校验证照借用时间是否冲突
            returnList = this.licenseBorrowExamine(oaProcessTemplate, saveDataBo, borrowStartTime, borrowEndTime);
        }
        return returnList;
    }

    /**
     * 发起流程时校验证照借用时间是否冲突
     * @param oaProcessTemplate
     * @param saveDataBo
     * @param borrowStartTime 借用开始时间
     * @param borrowEndTime 借用结束时间
     * @return
     */
    private List<ZzPendingDetail> licenseBorrowExamine(OaProcessTemplate oaProcessTemplate, SaveDataBo saveDataBo, Date borrowStartTime, Date borrowEndTime) {
        List<ZzPendingDetail> list = new ArrayList<>();
        Boolean flag = false;
        List<String> borrowLicIds = saveDataBo.getBorrowLicIds();
        if (CollectionUtils.isEmpty(borrowLicIds)){
            throw new ServiceException("未获取到要借用的证照信息，请重试");
        }
        //循环
        for (String borrowLicId : borrowLicIds) {
            //获取该证照大于等于当前时间的所有外借信息
            List<ZzPendingDetail> zzPendingDetailList = zzPendingDetailMapper.selectLicenseBorrowInfoListById(borrowLicId);
            //不为空，则校验借用开始时间和借用结束时间是否冲突
            if (!CollectionUtils.isEmpty(zzPendingDetailList)){
                for (ZzPendingDetail pendingDetail : zzPendingDetailList) {
                    flag = judge(pendingDetail,borrowStartTime,borrowEndTime);
                    if (flag){
                        list.add(pendingDetail);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 发起流程时校验证照借用时间是否冲突
     * @param pendingDetail
     * @param startTime
     * @param endDate
     * @return
     */
    public Boolean judge(ZzPendingDetail pendingDetail, Date startTime, Date endDate) {
        Date newStartDate = new Date();
        Date newEndDate = new Date();
        newStartDate = pendingDetail.getBorrowStartTime();
        newEndDate = pendingDetail.getBorrowEndTime();
        if(newStartDate == null || newEndDate == null || startTime == null || endDate == null) {
            return false;
        }
        if (newStartDate.getTime() >= startTime.getTime() && newEndDate.getTime() <= endDate.getTime()){
            return true;
        } else if (newStartDate.getTime() <= startTime.getTime() && newEndDate.getTime() >= startTime.getTime() && newEndDate.getTime() <= endDate.getTime()) {
            return true;
        } else if (newStartDate.getTime() >= startTime.getTime() && newStartDate.getTime() <= endDate.getTime() && endDate.getTime() <= newEndDate.getTime()) {
            return true;
        } else if (newStartDate.getTime() <= startTime.getTime() && newEndDate.getTime() >= endDate.getTime()) {
            return true;
        }else {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateFlowInfo(FlowInfoDTO flowInfoDTO, LoginUser loginUser) {
        String data = flowInfoDTO.getData();
        JSONObject jsonObject = JSON.parseObject(data);
        jsonObject.put("userId", loginUser.getUserId());
        String dataJsonNew = JSONObject.toJSONString(jsonObject);
        flowInfoDTO.setData(dataJsonNew);
        //先修改
        flowInfoDTO.setOid(flowInfoDTO.getBusinessId());
        if ("5".equals(flowInfoDTO.getCurrentStatus())) {
            ProcFormData procFormData2 = procFormDataMapper.getDataByOid(flowInfoDTO.getOid());
            OaProcessTemplate oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateById(procFormData2.getTemplateId());
            if(null == oaProcessTemplate || "N".equalsIgnoreCase(oaProcessTemplate.getIsEnable())){
                return AjaxResult.error("流程启动失败，该模板已作废，请重新选择模板");
            }
            //状态为草稿状态，发起流程
            JSONObject formJson = JSON.parseObject(flowInfoDTO.getData());
            //设置动态变量
            HashMap<String, Object> variables = new HashMap<String, Object>();
            variables.put(LOGINUSER, SecurityUtils.getUsername());
//            variables.put(APPROVER, saveDataBo.getApprover());
            variables.put("customParam", formJson);
            if(null != flowInfoDTO.getCustomFlag() && flowInfoDTO.getCustomFlag()){
                variables.put("voucherDate",flowInfoDTO.getVoucherDate());
            }
            ProcessInstance processInstance = processRuntime.start(ProcessPayloadBuilder
                    .start()
                    .withProcessDefinitionKey(flowInfoDTO.getProcKey())
                    .withProcessDefinitionId(flowInfoDTO.getFlowFullId())
                    .withName(SecurityUtils.getLoginUser().getUser().getNickName() + "的" + flowInfoDTO.getFormName() + "申请")
                    .withBusinessKey(flowInfoDTO.getBusinessId())
                    .withVariables(variables)
                    .build());
            //查询第一个任务,并自动审批通过
            TaskQuery taskQuery1 = taskService.createTaskQuery().active().processInstanceId(processInstance.getId());
            org.activiti.engine.task.Task task1 = taskQuery1.singleResult();
            //起草节点修改流程审批人为流程发起人
            if(StringUtils.isEmpty(task1.getAssignee()) || !SecurityUtils.getUsername().equals(task1.getAssignee())){
                taskService.setAssignee(task1.getId(),SecurityUtils.getUsername());
            }
            String taskDefinitionKey = task1.getTaskDefinitionKey();
            BpmnModel bpmnModel1 = repositoryService.getBpmnModel(task1.getProcessDefinitionId());
            UserTask userTask = (UserTask) bpmnModel1.getMainProcess().getFlowElement(taskDefinitionKey);
            Integer i = getExclusiveGateway(SecurityUtils.getUsername(),userTask);
            if(null != i){
                formJson.put(IS_SKIP,i);
                variables.put("customParam", formJson);
            }

            logger.info("草稿发起流程,业务主键：{},审批流程procInstanceId：{},当前审批节点：{},下一审批节点：{}",flowInfoDTO.getBusinessId(),processInstance.getId(),taskDefinitionKey);
            taskRuntime.complete(TaskPayloadBuilder.complete().withTaskId(task1.getId())
                    .withVariables(variables)
                    .build());

            ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
            procWorkflowFormdata.setId(UUID.randomUUID().toString());
            procWorkflowFormdata.setBusinessKey(flowInfoDTO.getBusinessId());
            procWorkflowFormdata.setComment("提交申请");
            procWorkflowFormdata.setPass("1");//同意
            procWorkflowFormdata.setTaskNodeName(task1.getName());
            procWorkflowFormdata.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
            procWorkflowFormdata.setCreateBy(SecurityUtils.getUsername());
            procWorkflowFormdata.setStepId(taskDefinitionKey);
            ProcWorkflowFormdataService.insertProcWorkflowFormdata(procWorkflowFormdata);
            flowInfoDTO.setInstanceId(processInstance.getId());
            flowInfoDTO.setWithProc(PROC_YES);
            Long processNumber = procFormDataMapper.selectMaxProcessNumber();
            if (processNumber == null) {
                flowInfoDTO.setProcessNumber(1L);
            } else {
                processNumber = processNumber + 1;
                flowInfoDTO.setProcessNumber(processNumber);
            }
            logger.info("草稿发起流程,业务主键：{},审批流程procInstanceId：{},下一节点信息：{}",flowInfoDTO.getBusinessId(),processInstance.getId(),taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult());

            //查找是否还存在下一节点审批，不存在直接修改状态为审批通过
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstance.getId()).singleResult();
            if (historicProcessInstance.getEndTime() != null) {
                flowInfoDTO.setStatus(PASS);
            }

            if (flowInfoDTO.getNextFlowApproveUserName() != null) {
                BpmnModel bpmnModel = repositoryService.getBpmnModel(flowInfoDTO.getFlowFullId());
                UserTask nextFlow = (UserTask) bpmnModel.getMainProcess().getFlowElement(flowInfoDTO.getNextNodeId());
                org.activiti.engine.task.Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
                if (nextFlow.getCandidateUsers().size() > 1) {
                    taskService.setAssignee(task.getId(), flowInfoDTO.getNextFlowApproveUserName());
                }
                if (nextFlow.getCandidateGroups().size() != 0) {
                    taskService.setAssignee(task.getId(), flowInfoDTO.getNextFlowApproveUserName());
                }
            }
            //通知审核人
            NotifyReviewerDto notifyReviewerDto = NotifyReviewerDto.builder()
                    .processInstanceId(processInstance.getId()).taskDefinitionKey(taskDefinitionKey).templateId(procFormData2.getTemplateId()).theme(procFormData2.getTheme())
                    .startNickName(loginUser.getUser().getNickName()).createTime(procFormData2.getCreateTime())
                    .build();
            notifyReviewer(notifyReviewerDto);
        }
        Date nowDate = DateUtils.getNowDate();
        flowInfoDTO.setUpdateTime(nowDate);
        int i = procFormDataMapper.updateProcFormData1(flowInfoDTO);
        //如果是驳回重新编辑，直接调取执行任务
        if ("4".equals(flowInfoDTO.getCurrentStatus())) {
            execFlowNodeFirst(flowInfoDTO,loginUser);
        }
        return AjaxResult.success("流程修改成功");
    }

    @Override
    public AjaxResult changeFlowPeopleByBusinessId(FlowUserDTO flowUserDTO) {
        ProcFormData procFormData = new ProcFormData();
        procFormData.setBusinessId(flowUserDTO.getBusinessId());
        ProcFormData procFormData1 = procFormDataMapper.selectProcFormDataByBusinessId(procFormData);
        org.activiti.engine.task.Task task = taskService.createTaskQuery().processInstanceId(procFormData1.getInstanceId()).singleResult();
        taskService.setAssignee(task.getId(), flowUserDTO.getUserName());

        ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
        procWorkflowFormdata.setId(UUID.randomUUID().toString());
        procWorkflowFormdata.setBusinessKey(flowUserDTO.getBusinessId());
        procWorkflowFormdata.setComment("同意");
        procWorkflowFormdata.setPass("9");
        procWorkflowFormdata.setTaskNodeName(task.getName());
        procWorkflowFormdata.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
        procWorkflowFormdata.setAssociationName(flowUserDTO.getUserName());
        procWorkflowFormdata.setCreateBy(SecurityUtils.getUsername());
        procWorkflowFormdata.setStepId(task.getTaskDefinitionKey());
        procWorkflowFormdata.setCreateTime(DateUtils.getNowDate());
        ProcWorkflowFormdataService.insertProcWorkflowFormdata(procWorkflowFormdata);
        return AjaxResult.success("变更流程人员成功");
    }

    @Override
    public AjaxResult updateProcFormDef(ProcFormBO procFormBO) {
        ProcFormDef procFormDef = new ProcFormDef();
        procFormDef.setId(procFormBO.getFormId());
        procFormDef.setRefProcKey(procFormBO.getProcKey());
        int i = procFormDefMapper.updateProcFormDef(procFormDef);
        if (i > 0) {
            return AjaxResult.success("修改表单定义成功");
        }
        return AjaxResult.error("变更流程人员失败");
    }

    @Override
    public AjaxResult getFlowButtonPermission(ButtonPermission buttonPermission, LoginUser loginUser) {
        String businessId = buttonPermission.getBusinessId();
        Integer pageFlag = buttonPermission.getPageFlag();
        if (pageFlag == 1) {
            //说明是查看流程页面
            Long userId = loginUser.getUserId();
            boolean present = loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "OA".equals(r.getRoleKey()));
            Map<String, Object> map = new HashMap<>();
            //OA管理员标识
            map.put("oaAdminFlag", present);
            //找通过businessId找模板的发起人和审批人
            ProcFormData p = new ProcFormData();
            p.setBusinessId(businessId);
            ProcFormData procFormData = procFormDataMapper.selectProcFormDataByBusinessId(p);
            if (procFormData == null) {
                p.setOid(businessId);
                p.setBusinessId(null);
                procFormData = procFormDataMapper.selectProcFormDataByBusinessId(p);
            }
            Long userId1 = procFormData.getUserId();
            //判断用户是否是发起人
            if (userId1.equals(userId)) {
                map.put("initiatorFlag", true);
            } else {
                map.put("initiatorFlag", false);
            }
            //查找部门负责人，看用户是否是部门负责人
            if (procFormData.getBusinessId() != null) {
                Long deptLeaderId = procFormDataMapper.selectDeptLeaderIdByFlowInitiatorId(procFormData.getBusinessId());
                if (deptLeaderId == null) {
                    map.put("deptLeaderFlag", false);
                } else {
                    if (deptLeaderId.equals(userId)) {
                        map.put("deptLeaderFlag", true);
                    } else {
                        map.put("deptLeaderFlag", false);
                    }
                }
            }
            return AjaxResult.success(map);
        }
        return AjaxResult.error();
    }

    @Override
    public AjaxResult findFile(FlowFileInfoDTO flowFileInfoDTO) {
//        flowFileInfoDTO.setCommitStatus("0");
        List<FlowFileInfoDTO> flowFileInfoDTOS = procWorkflowFormdataMapper.selectProcWorkflowFormdataFilesList(flowFileInfoDTO);
        //去获取不同的soleFlag
        Map<String, List<FlowFileInfoDTO>> collectMap = flowFileInfoDTOS.stream().collect(Collectors.groupingBy(FlowFileInfoDTO::getSoleFlag));
        //确保每一个soleFlag都不存在于审批记录表
        //文件的soleFlag等于审批文件的id
        List<String> idList = new ArrayList<>(collectMap.keySet());
        if (idList.size() > 0) {
            List<ProcWorkflowFormdata> determineList = procWorkflowFormdataMapper.selectProcWorkflowFormdataListByIdList(idList);
            if (determineList.size() == 0) {
                return AjaxResult.success(flowFileInfoDTOS);
            } else {
                return AjaxResult.success(new ArrayList<>());
            }
        }
        return AjaxResult.success(flowFileInfoDTOS);
    }

    @Override
    public AjaxResult findFlowSecondNode(String flowFullId,String reqStr) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(flowFullId);
        Process process = bpmnModel.getProcesses().get(0);
        List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);
        //todo 可能会再改造
        //第二个事件的id
        String secondNodeId = StringUtils.EMPTY;
        if (userTaskList.size() < 2) {
            //说明没有两个事件，则返回信息
//            Map<String, Object> stringObjectHashMap = new HashMap<>();
//            stringObjectHashMap.put("error", "999999");
            return AjaxResult.success("该流程图没有两个以上处理节点", null);
        }
        //找到初始的开始时事件，然后找他后面的所有
        StartEvent startEvent = process.findFlowElementsOfType(StartEvent.class).get(0);
        List<SequenceFlow> outgoingFlows = startEvent.getOutgoingFlows();
        SequenceFlow sequenceFlow = outgoingFlows.get(0);
        //找到了第一个
        FlowElement targetFlowElement1 = sequenceFlow.getTargetFlowElement();
        if (targetFlowElement1 instanceof UserTask) {
            List<SequenceFlow> outgoingFlows1 = ((UserTask) targetFlowElement1).getOutgoingFlows();
            SequenceFlow sequenceFlow1 = outgoingFlows1.get(0);
            FlowElement targetFlowElement2 = sequenceFlow1.getTargetFlowElement();
            if (targetFlowElement2 instanceof  UserTask) {
                secondNodeId = ((UserTask) targetFlowElement2).getId();
            }

            //处理起草节点后面为网关时，判断获取网关下一节点参数
            if(StringUtils.isNotEmpty(reqStr) && targetFlowElement2 instanceof ExclusiveGateway){
                //设置动态变量
                HashMap<String, Object> variables = new HashMap();
                JSONObject formJson = JSON.parseObject(reqStr);
                formJson.put(IS_SKIP,getCeoUserNameSkip(SecurityUtils.getUsername(),targetFlowElement2));
                variables.put("customParam", formJson);
                List<SequenceFlow> outgoingFlowsList = ((ExclusiveGateway) targetFlowElement2).getOutgoingFlows();
                for (SequenceFlow sequenceFlow2:outgoingFlowsList) {
                    String conditionExpression = sequenceFlow2.getConditionExpression();
                    FlowElement flowElement = sequenceFlow2.getTargetFlowElement();
                    if(!(flowElement instanceof UserTask)){
                        continue;
                    }
                    boolean result = calculateExpression(conditionExpression, variables);
                    if(result){
                        secondNodeId = flowElement.getId();
                        break;
                    }
                }
            }
        }

        if (userTaskList.size() > 1) {
            for (UserTask userTask:userTaskList) {
                if (userTask.getId().equals(secondNodeId)) {
                    String assignee = userTask.getAssignee();
                    List<String> candidateUsers = userTask.getCandidateUsers();
                    List<String> candidateGroups = userTask.getCandidateGroups();
                    Map<String, List<ExtensionAttribute>> attributes = userTask.getAttributes();
                    Map<String, Object> attributesMap = getAttributesMap(attributes);
                    String id = userTask.getId();
                    Map<String, Object> resultMap = new HashMap<>();
                    if (!CollectionUtils.isEmpty(candidateUsers) || !CollectionUtils.isEmpty(candidateGroups)) {
                        resultMap.put("candidateUsers", candidateUsers);
                        resultMap.put("candidateGroups", candidateGroups);
                        resultMap.put("assignee", assignee);
                        resultMap.put("nodeId", id);
                        resultMap.put("attributes",attributesMap);
                        return AjaxResult.success(resultMap);
                    } else {
                        return AjaxResult.success("操作成功", null);
                    }
                }
            }
//            UserTask userTask = userTaskList.get(1);
//            String assignee = userTask.getAssignee();
//            List<String> candidateUsers = userTask.getCandidateUsers();
//            List<String> candidateGroups = userTask.getCandidateGroups();
//            String id = userTask.getId();
//            Map<String, Object> resultMap = new HashMap<>();
//            if (!CollectionUtils.isEmpty(candidateUsers) || !CollectionUtils.isEmpty(candidateGroups)) {
//                resultMap.put("candidateUsers", candidateUsers);
//                resultMap.put("candidateGroups", candidateGroups);
//                resultMap.put("assignee", assignee);
//                resultMap.put("nodeId", id);
//                return AjaxResult.success(resultMap);
//            } else {
//                return AjaxResult.success("操作成功", null);
//            }
        }
        return AjaxResult.success("该流程缺少第二步节点");
    }

    @Override
    public AjaxResult getNextFlowInfo(String instanceId, String taskId) {
        //返回结果
        Map<String, Object> resMap = new HashMap<>();
        //给一个默认状态，用于控制页面是否显示组件
        resMap.put("controlFlag", false);
        //是否展示即将流向
        resMap.put("nextFlag",false);
        org.activiti.engine.runtime.ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(instanceId).singleResult();
        //查询当前实例ID
        if (!Objects.isNull(processInstance)) {
            String processDefinitionId = processInstance.getProcessDefinitionId();
            org.activiti.engine.task.Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (!Objects.isNull(task)){
                //查询当前节点任务对象
                String taskDefinitionKey = task.getTaskDefinitionKey();
                BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
                Process process = bpmnModel.getProcesses().get(0);
                //获取所有普通任务节点
                List<UserTask> UserTaskList = process.findFlowElementsOfType(UserTask.class);
                for(UserTask userTask:UserTaskList) {
                    //获取当前任务节点Id
                    String id  = userTask.getId();
                    if(id.equals(taskDefinitionKey)){
                        Map<String, Object> variables = taskService.getVariables(taskId);
                        Integer i = getExclusiveGateway(variables.get(LOGINUSER).toString(), userTask);
                        if(null !=i){
                            String dataJsonStr = variables.get("customParam").toString();
                            JSONObject dataJson = JSONObject.parseObject(dataJsonStr);
                            dataJson.put(IS_SKIP,i);
                            variables.put("customParam",dataJson);
                        }
                        //获取当前任务节点的所有出线信息
                        List<SequenceFlow> outgoingFlows = userTask.getOutgoingFlows();
                        for(SequenceFlow sequenceFlow:outgoingFlows) {
                            //获取出线连接的目标节点
                            FlowElement targetFlowElement = sequenceFlow.getTargetFlowElement();
                            //获取到了下一个节点的Id
                            String nextId = targetFlowElement.getId();
                            String nextNodeName = targetFlowElement.getName();
                            //走网关（有了条件分支）
                            List<Gateway> gatewayList = process.findFlowElementsOfType(Gateway.class);
                            List<Gateway> collect1 = gatewayList.stream().filter(t -> t.getId().equals(nextId)).collect(Collectors.toList());
                            //没有走网关（没有条件分支的话）
                            List<UserTask> UserTaskLists = process.findFlowElementsOfType(UserTask.class);
                            List<UserTask> collect = UserTaskLists.stream().filter(t -> t.getId().equals(nextId)).collect(Collectors.toList());
                            if (collect.size() > 0) {
                                UserTask userTask1 = collect.get(0);
                                String assignee = userTask1.getAssignee();
                                /**新增标识判断下一节点是否为会签节点*/
                                boolean nextParallelFlag = userTask1.getLoopCharacteristics() == null?false:true;
                                List<String> candidateUsers = userTask1.getCandidateUsers();
                                List<String> candidateGroups = userTask1.getCandidateGroups();
                                resMap.put("assignee",assignee);
                                resMap.put("candidateUsers", candidateUsers);
                                resMap.put("candidateGroups", candidateGroups);
                                resMap.put("nextNodeId", nextId);
                                resMap.put("nextNodeName",nextNodeName);
                                resMap.put("nextFlag",true);
                                resMap.put("nextParallelFlag",nextParallelFlag);
                                if(null !=assignee && !assignee.contains("${")){
                                    SysUser sysUser = sysUserMapper.selectUserByUserName(assignee);
                                    resMap.put("assigneeName",sysUser==null?assignee:sysUser.getNickName());
                                }else{
                                    List<SysUser> sysUserList = new LinkedList<>();
                                    for(String name:candidateUsers){
                                        SysUser sysUser = sysUserMapper.selectUserByUserName(name);
                                        sysUserList.add(sysUser);
                                    }

                                    if (candidateUsers.size() == 1) {
                                        resMap.put("assigneeName", sysUserList.get(0).getNickName());
                                    } else {
                                        resMap.put("candidateUsersList", sysUserList);
                                    }

                                    if (candidateUsers.size() > 1 || candidateGroups.size() > 0) {
                                        resMap.put("controlFlag", true);
                                    }

                                }
                            } else if (collect1.size() > 0) {
                                List<SequenceFlow> list = collect1.get(0).getOutgoingFlows();
                                for (SequenceFlow flow : list) {
                                    String conditionExpression = flow.getConditionExpression();
                                    FlowElement flowElement = flow.getTargetFlowElement();
                                    if (!(flowElement instanceof UserTask)) {
                                        continue;
                                    }
                                    UserTask targetUserTask = (UserTask) flowElement;

                                    boolean result = calculateExpression(conditionExpression, variables);
                                    if (result) {
                                        String assignee = targetUserTask.getAssignee();
                                        List<String> candidateUsers = targetUserTask.getCandidateUsers();
                                        List<String> candidateGroups = targetUserTask.getCandidateGroups();
                                        resMap.put("assignee", assignee);
                                        resMap.put("candidateUsers", candidateUsers);
                                        resMap.put("candidateGroups", candidateGroups);
                                        resMap.put("nextNodeId", targetUserTask.getId());
                                        resMap.put("nextNodeName", targetUserTask.getName());
                                        resMap.put("nextFlag", true);
                                        if (null != assignee) {
                                            SysUser sysUser = sysUserMapper.selectUserByUserName(assignee);
                                            resMap.put("assigneeName", sysUser == null ? assignee : sysUser.getNickName());
                                        } else {
                                            List<SysUser> sysUserList = new LinkedList<>();
                                            for (String name : candidateUsers) {
                                                SysUser sysUser = sysUserMapper.selectUserByUserName(name);
                                                sysUserList.add(sysUser);
                                            }

                                            if (candidateUsers.size() == 1) {
                                                resMap.put("assigneeName", sysUserList.get(0).getNickName());
                                            } else {
                                                resMap.put("candidateUsersList", sysUserList);
                                            }

                                            if (candidateUsers.size() > 1 || candidateGroups.size() > 0) {
                                                resMap.put("controlFlag", true);
                                            }

                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return AjaxResult.success(resMap);
    }

    @Override
    public List<ApprovedNodesInfo> getApproveNodesInfo(String businessId) {
        String taskDefinitionKey = "";
        org.activiti.engine.task.Task task = getTaskDefinitionKey(businessId);
        if (task != null) {
            taskDefinitionKey = task.getTaskDefinitionKey();
        }
        //获取审批已通过的节点
        List<ApprovedNodesInfo> procList = ProcWorkflowFormdataService.getApproveNodesInfo(businessId, PASS);
        List<ApprovedNodesInfo> resultList = new LinkedList<>();

        for (ApprovedNodesInfo row:procList) {
            ApprovedNodesInfo approvedNodesInfo = ApprovedNodesInfo.builder()
                    .stepId(row.getStepId()+";"+row.getCreateBy()).taskNodeName(row.getTaskNodeName()+"("+row.getCreateName()+")")
                    .build();
            if(taskDefinitionKey.equalsIgnoreCase(row.getStepId())){
                break;
            }
            resultList.add(approvedNodesInfo);
        }
        //去重
        Iterator<ApprovedNodesInfo> iterator = resultList.iterator();
        while (iterator.hasNext()){
            ApprovedNodesInfo item = iterator.next();
            if(resultList.indexOf(item) != resultList.lastIndexOf(item)){
                iterator.remove();
            }
        }
        return resultList;
    }

    /**
     * 完成任务
     *
     * @param execBO
     * @param dataJson
     */
    private void completeTask(ExecBO execBO, String dataJson){
        String taskID = execBO.getTaskID();
        String pass = execBO.getPass().equals("2")? VAR_VALUE_PASS: execBO.getPass();
        pass = pass.equals(APPROVAL_REJECTED)?VAR_VALUE_NOT_PASS:pass;
        Task task;
        try {
            task = taskRuntime.task(taskID);
        }catch (NotFoundException e){
            throw new RuntimeException("当前节点已审批");
        }
        org.activiti.engine.runtime.ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        String processInstanceId = processInstance.getProcessInstanceId();
        logger.info("处理审批流程节点,业务主键：{},审批流程procInstanceId：{},当前审批节点：{},下一审批节点：{}",execBO.getBusinessKey(),processInstanceId,execBO.getStepId(),execBO.getNextNodeId());
        //是否修改表单
        //如果节点可编辑，则更新表单数据
        if(execBO.getEditPayee()){
            dataJson =  updateFormData(dataJson,execBO.getData(),execBO.getFormID(),execBO.getEditPayeeList());
            procFormDataMapper.updateProcFormData(execBO.getBusinessKey(),null,dataJson,true);
        }
        Map variables2 = taskService.getVariables(task.getId());
        HashMap<String, Object> variables = new HashMap();
        JSONObject data = JSONObject.parseObject(dataJson);
        BpmnModel bpmnModel1 = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        UserTask userTask = (UserTask) bpmnModel1.getMainProcess().getFlowElement(execBO.getStepId());
        Integer i = getExclusiveGateway(variables2.get(LOGINUSER).toString(),userTask);
        if(null != i){
            data.put(IS_SKIP,i);
        }
        variables.put(VAR, pass);
        variables.put("customParam", data);
        variables.put(APPROVE_STATUS,PASS);
        if(null != execBO.getAssigneeList() && execBO.getAssigneeList().size() >0){
            variables.put("assigneeList",execBO.getAssigneeList());
        }
        if (task.getAssignee() == null) {
            taskRuntime.claim(TaskPayloadBuilder.claim().withTaskId(task.getId()).build());
        }
        JsonNode rejectFlowNodeSequence = (JsonNode) variables2.get("rejectFlowNodeSequence");
        if(null == rejectFlowNodeSequence){
            taskRuntime.complete(TaskPayloadBuilder.complete().withTaskId(taskID)
                    .withVariables(variables)
                    .build());
        }else{
            //驳回节点
            String rejectNodes = rejectFlowNodeSequence.get("rejectNodes").toString();
            rejectNodes = rejectNodes.substring(1,rejectNodes.length()-1);
            //驳回后需要连线的新节点
            String currentFlowNode =rejectFlowNodeSequence.get("currentFlowNode").toString();
            currentFlowNode = currentFlowNode.substring(1,currentFlowNode.length()-1);
            String currentFlowNodeAssignee =rejectFlowNodeSequence.get("currentFlowNodeAssignee").toString();
            currentFlowNodeAssignee = currentFlowNodeAssignee.substring(1,currentFlowNodeAssignee.length()-1);
            if(execBO.getStepId().equalsIgnoreCase(rejectNodes)){
                detailFlowNode(taskID,rejectNodes,currentFlowNode,currentFlowNodeAssignee,variables);
                execBO.setNextFlowApproveUserName(null);
            }else{
                taskRuntime.complete(TaskPayloadBuilder.complete().withTaskId(taskID)
                        .withVariables(variables)
                        .build());
            }
        }
        logger.info("流程complete完成,业务主键{},审批流程procInstanceId{},当前审批节点{},下一审批节点{}",execBO.getBusinessKey(),processInstanceId,execBO.getStepId(),execBO.getNextNodeId());
        String status = RUNNING;
        String lastNodeFlag = "0";
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (historicProcessInstance.getEndTime() != null) {
            status = PASS;
            if (execBO.getPass() != null && VAR_VALUE_NOT_PASS.equals(pass)) {
                status = NOTPASS;
            }
            lastNodeFlag = "1";
        }
        execBO.setLastNodeFlag(lastNodeFlag);
        logger.info("审批通过后流程状态,业务主键：{},审批流程procInstanceId：{},状态为：{}",execBO.getBusinessKey(),processInstanceId,status);
        procFormDataMapper.updateProcFormData(execBO.getBusinessKey(),status,dataJson, false);

        //找下一个节点，给下一个节点设置审批人
        List<org.activiti.engine.task.Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        if (execBO.getNextFlowApproveUserName() != null && taskList.size() == 1 && taskList.get(0).getTaskDefinitionKey().equals(execBO.getNextNodeId())) {
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
            UserTask nextFlow = (UserTask) bpmnModel.getMainProcess().getFlowElement(execBO.getNextNodeId());
            org.activiti.engine.task.Task task1 = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
            if (nextFlow.getCandidateUsers().size() > 1) {
                taskService.setAssignee(task1.getId(), execBO.getNextFlowApproveUserName());
            }
            if (nextFlow.getCandidateGroups().size() != 0) {
                taskService.setAssignee(task1.getId(), execBO.getNextFlowApproveUserName());
            }
        }
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        //上一个节点已经完成。那么execBO中的nextNodeId代表的就是当前节点id
        String currentNodeId = execBO.getNextNodeId();
        FlowElement flowElement = bpmnModel.getMainProcess().getFlowElement(currentNodeId);
        if (flowElement instanceof UserTask) {
            //如果当前节点没有代理人和候选人，只有代理组。那么进行一下逻辑处理
            if (((UserTask) flowElement).getAssignee() == null && ((UserTask) flowElement).getCandidateUsers().size() == 0) {
                //如果岗位只有一个人的时候，那么就选中该岗位下的这个人作为代理人和候选人
                List<String> candidateGroups = ((UserTask) flowElement).getCandidateGroups();
                if (candidateGroups.size() == 1) {
                    String postCode = candidateGroups.get(0);
                    List<String> userNameList = sysUserMapper.selectUserByUserPostCode(postCode);
                    if (userNameList.size() == 1) {
                        org.activiti.engine.task.Task task1 = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
                        List<HistoricTaskInstance> hisTaskList = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstance.getId()).orderByTaskCreateTime().asc().list();
                        if (hisTaskList.size() > 2) {
                            HistoricTaskInstance lastTask = hisTaskList.get(hisTaskList.size() -2);
                            FlowElement lastFlowNode = bpmnModel.getMainProcess().getFlowElement(lastTask.getTaskDefinitionKey());
                            if (lastFlowNode instanceof Gateway) {
                                taskService.setAssignee(task1.getId(), userNameList.get(0));
                            }
                        }
                    }
                }
            }
        } else if (flowElement instanceof Gateway) {
            //找一下当前已经走过gateway网关判断的当前节点
            org.activiti.engine.task.Task task1 = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
            String taskDefinitionKey = task1.getTaskDefinitionKey();
            FlowElement flowElement1 = bpmnModel.getMainProcess().getFlowElement(taskDefinitionKey);
            if (flowElement1 instanceof UserTask) {
                //如果当前节点没有代理人和候选人，只有代理组。那么进行一下逻辑处理
                if (((UserTask) flowElement1).getAssignee() == null && ((UserTask) flowElement1).getCandidateUsers().size() == 0) {
                    //如果岗位只有一个人的时候，那么就选中该岗位下的这个人作为代理人和候选人
                    List<String> candidateGroups = ((UserTask) flowElement1).getCandidateGroups();
                    if (candidateGroups.size() == 1) {
                        String postCode = candidateGroups.get(0);
                        List<String> userNameList = sysUserMapper.selectUserByUserPostCode(postCode);
                        if (userNameList.size() == 1) {
                            org.activiti.engine.task.Task task2 = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
                            taskService.setAssignee(task2.getId(), userNameList.get(0));
                        }
                    }
                }
            }
        }
    }

    /**
     * 回退或驳回
     * @param execBO
     * @return
     */
    public AjaxResult revokeFlowNode(ExecBO execBO) {
        Task task = taskRuntime.task(execBO.getTaskID());
        org.activiti.engine.runtime.ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        String processInstanceId = processInstance.getProcessInstanceId();
        //  获取所有历史任务（按创建时间降序）
        List<HistoricTaskInstance> hisTaskList = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByTaskCreateTime().asc().list();

        System.out.println("历史任务数量：" + hisTaskList.size());

        if (CollectionUtils.isEmpty(hisTaskList) || hisTaskList.size() < 2) {
            return AjaxResult.error("驳回失败");
        }

        //第一个任务
        HistoricTaskInstance startTask = hisTaskList.get(0);
        //上一个任务
        HistoricTaskInstance lastTask = hisTaskList.get(hisTaskList.size() - 2);
        //当前任务
        HistoricTaskInstance currentTask = hisTaskList.get(hisTaskList.size() - 1);

        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());

        //获取第一个活动节点
        FlowNode startFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(startTask.getTaskDefinitionKey());
        //获取上一个活动节点
        FlowNode lastFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(lastTask.getTaskDefinitionKey());
        //获取当前活动节点
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(currentTask.getTaskDefinitionKey());
        String[] rejectNodesInfo = execBO.getRejectNodesInfo().split(";");
        //驳回节点名
        String rejectNodes = rejectNodesInfo[0];
        //驳回节点处理人
        String rejectNodesUser = rejectNodesInfo.length > 1 ? rejectNodesInfo[1] : null;

        //获取驳回活动节点信息
        FlowNode rejectFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(rejectNodes);

        /**
         * 处理节点驳回，且驳回通过后回到本节点，新增流程变量
         */
        Map<String, Object> variables = new LinkedHashMap<>();
        variables.put(APPROVE_STATUS,REJECT);
        if (StringUtils.isNotEmpty(execBO.getApprovalSequence()) && "2".equalsIgnoreCase(execBO.getApprovalSequence())) {
            Map<String, String> rejectFlowNodeSequence = new LinkedHashMap<>();
            rejectFlowNodeSequence.put("rejectNodes", rejectNodes);
            rejectFlowNodeSequence.put("currentFlowNode", currentTask.getTaskDefinitionKey());
            rejectFlowNodeSequence.put("currentFlowNodeAssignee", execBO.getUserName());
            variables.put("rejectFlowNodeSequence", rejectFlowNodeSequence);
        }
        //临时保存当前活动的原始方向
        List<SequenceFlow> originalSequenceFlowList = new ArrayList<>();
        originalSequenceFlowList.addAll(currentFlowNode.getOutgoingFlows());
        try {
            //清理活动方向
            currentFlowNode.getOutgoingFlows().clear();

            //建立新方向
            SequenceFlow newSequenceFlow = new SequenceFlow();
            newSequenceFlow.setId("newSequenceFlowId");
            newSequenceFlow.setSourceFlowElement(currentFlowNode);
            if (execBO.getPass().equals("4")) {
                newSequenceFlow.setTargetFlowElement(rejectFlowNode);
            } else {
                newSequenceFlow.setTargetFlowElement(lastFlowNode);
            }

            List<SequenceFlow> newSequenceFlowList = new ArrayList<>();
            newSequenceFlowList.add(newSequenceFlow);
            //  当前节点指向新的方向
            currentFlowNode.setOutgoingFlows(newSequenceFlowList);

            ProcWorkflowFormdata lastProcWorkflowFormdata = ProcWorkflowFormdataService.selectLastByBusinessKey(execBO.getBusinessKey());
            if (lastProcWorkflowFormdata.getPass().equals("6")) {
                taskService.resolveTask(execBO.getTaskID(), variables);//加签解决任务
            }
            //  完成当前任务
            taskService.complete(task.getId(), variables, true);

            //  重新查询当前任务
            org.activiti.engine.task.Task nextTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
            if (null != nextTask) {
                //驳回到第一节点，赋值节点处理人
                if (execBO.getPass().equals("4")) {
                    if (startTask.getTaskDefinitionKey().equalsIgnoreCase(rejectNodes)) {
                        procFormDataMapper.updateProcFormData(execBO.getBusinessKey(), ProcFormDataStatusEnums.REJECTED.getCode(),null,false);
                        String startUserName = procFormDataMapper.selectInitiatorUserNameByBusinessId(execBO.getBusinessKey());
                        taskService.setAssignee(nextTask.getId(), startUserName);
                        ProcFormReadRecodeVo vo = ProcFormReadRecodeVo.builder().businessKey(execBO.getBusinessKey()).businessStatus("1").detailBy(startUserName).readFlag("F")
                                .build();
                        procFormDataMapper.addProcReadRecode(vo);
                        execBO.setRejectFlag(ProcFormDataStatusEnums.REJECTED.getCode());
                    } else {
                        taskService.setAssignee(nextTask.getId(), rejectNodesUser);
                    }
                } else {
                    taskService.setAssignee(nextTask.getId(), lastTask.getAssignee());
                }
            }
        } catch (Exception e) {
            throw e;
        } finally {
            //  恢复原始方向
            currentFlowNode.setOutgoingFlows(originalSequenceFlowList);
        }
        return AjaxResult.success("处理成功");
    }
    @Override
    public TableDataInfo byCheckGetMonitoringData(OaSystemUtil oaSystemUtil) {

        TableDataInfo rspData = new TableDataInfo();
        ProcFormData procFormData = new ProcFormData();
        procFormData.setWithProc(PROC_YES);
        procFormData.setTheme(oaSystemUtil.getTheme());
        procFormData.setCompanyId(oaSystemUtil.getCompanyId());
//        procFormData.setProcKey(oaSystemUtil.getProcKey());
        if(null!=oaSystemUtil.getProcKey() && !(oaSystemUtil.getProcKey().equals(""))){
            procFormData.setProcessNumber(Long.valueOf(oaSystemUtil.getProcKey()));
        }

        procFormData.setCreateName(oaSystemUtil.getCreateName());
        //判断选择的是哪个页面 并调用相应的接口获取数据
        String activeNameFirst = oaSystemUtil.getActiveNameFirst();
        String activeNameSecend = oaSystemUtil.getActiveNameSecend();
        if(activeNameFirst.equals("first") && activeNameSecend.equals("first")){
            //审批中
            procFormData.setStatus("0");
            List<Map<String,Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);
            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }

            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            Map<String, Object> map = procFormDataMapper.selectSPingTotal(procFormData);
            rspData.setTotal(Long.valueOf(map.get("total").toString()));
        }else  if(activeNameFirst.equals("first") && activeNameSecend.equals("second")){
            //驳回
            procFormData.setStatus("4");
            List<Map<String,Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);

            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            Map<String, Object> map = procFormDataMapper.selectSPingTotal(procFormData);
            rspData.setTotal(Long.valueOf(map.get("total").toString()));

        }else  if(activeNameFirst.equals("first") && activeNameSecend.equals("third")){
            //草稿
            procFormData.setStatus("5");
            procFormData.setWithProc(PROC_NO);
            List<Map<String,Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);


            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            Map<String, Object> map = procFormDataMapper.selectSPingTotal(procFormData);
            rspData.setTotal(Long.valueOf(map.get("total").toString()));
        }else  if(activeNameFirst.equals("first") && activeNameSecend.equals("fourth")){
            //完成
            procFormData.setStatus("1");
            List<Map<String,Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);
            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }

            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            Map<String, Object> map = procFormDataMapper.selectSPingTotal(procFormData);
            rspData.setTotal(Long.valueOf(map.get("total").toString()));
        }else  if(activeNameFirst.equals("first") && activeNameSecend.equals("fieth")){
            //终止
            procFormData.setStatus("3");
            List<Map<String,Object>> procFormDataList = procFormDataMapper.selectSPing(procFormData);
            for (Map<String, Object> map : procFormDataList) {
                Map<String, String> flowUser = this.getFlowUser(map.get("instanceId").toString());
                map.put("processor", flowUser.get("processor"));
                map.put("userStatus1", flowUser.get("userStatus1"));
            }

            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(procFormDataList);
            Map<String, Object> map = procFormDataMapper.selectSPingTotal(procFormData);
            rspData.setTotal(Long.valueOf(map.get("total").toString()));
        }
        return rspData;
    }

    /**
     * 获取当前节点信息
     * @param businessId
     * @return
     */
    private org.activiti.engine.task.Task getTaskDefinitionKey(String businessId){
        String userId = SecurityUtils.getUsername();
        ProcFormData procFormData = new ProcFormData();
        procFormData.setBusinessId(businessId);
        procFormData.setCreateBy(userId);
        procFormData.setWithProc(PROC_YES);
        ProcFormData procFormData1 = procFormDataMapper.selectProcFormDataByBusinessId(procFormData);
        if (procFormData1 == null) {
            procFormData.setOid(businessId);
            procFormData.setBusinessId(null);
            procFormData1 = procFormDataMapper.selectProcFormDataByBusinessId(procFormData);
        }
        org.activiti.engine.task.Task task = null;
        if (procFormData1.getInstanceId() != null) {
            List<org.activiti.engine.task.Task> taskList = taskService.createTaskQuery().processInstanceId(procFormData1.getInstanceId()).list();
            if(null != taskList && taskList.size()>0){
                task = taskList.get(0);
            }
        }
        return task;
    }

    /**
     * 获取历史节点信息信息
     * @param businessId
     * @return
     */
    private List<HistoricTaskInstance> getHistoryTaskDefinitionKey(String businessId){
        String userId = SecurityUtils.getUsername();
        ProcFormData procFormData = new ProcFormData();
        procFormData.setBusinessId(businessId);
        procFormData.setCreateBy(userId);
        procFormData.setWithProc(PROC_YES);
        ProcFormData procFormData1 = procFormDataMapper.selectProcFormDataByBusinessId(procFormData);
        if (procFormData1 == null) {
            procFormData.setOid(businessId);
            procFormData.setBusinessId(null);
            procFormData1 = procFormDataMapper.selectProcFormDataByBusinessId(procFormData);
        }
        List<HistoricTaskInstance> hisTaskList = null;
        if (procFormData1.getInstanceId() != null) {
            hisTaskList = historyService.createHistoricTaskInstanceQuery().processInstanceId(procFormData1.getInstanceId()).orderByTaskCreateTime().asc().list();
        }
        return hisTaskList;
    }

    /**
     *处理转签节点
     */
    private void detailTransferNode(ExecBO execBO){
        //修改节点处理人
        taskService.setAssignee(execBO.getTaskID(), execBO.getTransferUser());
        //当流转该节点需要装扮人再次处理，存入节点流转信息
        if(execBO.getTransferFlag()){
            ProcApprovalRecord record =ProcApprovalRecord.builder()
                    .id(UUID.randomUUID().toString()).businessKey(execBO.getBusinessKey()).stepId(execBO.getStepId()).taskNodeName(execBO.getTaskNodeName())
                    .createBy(SecurityUtils.getLoginUser().getUser().getUserName()).detailBy(execBO.getTransferUser()).pass("5")
                    .build();
            List<ProcApprovalRecord> resultList = procFormDataMapper.queryProcApprovalRecord(record);
            if(resultList.size()>0){
                procFormDataMapper.updateProcApprovalRecord(record);
            }else{
                //记录节点处理信息
                procFormDataMapper.insertProcApprovalRecord(record);
            }
        }
    }

    /**
     * 获取当前节点信息按钮展示信息
     * @param businessId
     * @return
     */
    @Override
    public Map<String,Object> getButtonPermissions(String businessId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        org.activiti.engine.task.Task task = getTaskDefinitionKey(businessId);
        if (task != null) {
            String taskDefinitionKey = task.getTaskDefinitionKey();
            BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
            UserTask userTask = (UserTask) bpmnModel.getMainProcess().getFlowElement(taskDefinitionKey);
            boolean parallelFlag = userTask.getLoopCharacteristics() == null?false:true;
            resultMap.put("parallelFlag", parallelFlag);
            Map<String, List<ExtensionAttribute>> attributes = userTask.getAttributes();
            Map<String, Object> attributesMap = getAttributesMap(attributes);
            resultMap.putAll(attributesMap);
        }
        return resultMap;
    }

    @Override
    public List<ProcessTableResultVo> getProcessTable(String businessId) {
        List<ProcessTableVo> resultList = new LinkedList<>();
        List<ProcessTableResultVo> resultVoList2 = new LinkedList<>();
        org.activiti.engine.task.Task task = getTaskDefinitionKey(businessId);
        BpmnModel bpmnModel = null;
        if (task != null) {
            bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        } else {
            List<HistoricTaskInstance> hisList = getHistoryTaskDefinitionKey(businessId);
            if (null != hisList) {
                bpmnModel = repositoryService.getBpmnModel(hisList.get(0).getProcessDefinitionId());
            }
        }
        //如果查询不到流程信息，直接返回空
        if (null == bpmnModel) {
            return resultVoList2;
        }
        //所有节点
        Collection<FlowElement> flowElements = bpmnModel.getMainProcess().getFlowElements();
        for (FlowElement flowElement : flowElements) {
            //流入方向
            List<String> incomingFlows = new LinkedList<>();
            //流出方向
            List<String> outgoingFlows = new LinkedList<>();
            if (flowElement instanceof UserTask) {
                UserTask userTask = (UserTask) flowElement;
                userTask.getIncomingFlows().forEach(row -> {
                    incomingFlows.add(row.getSourceRef());
                });
                userTask.getOutgoingFlows().forEach(row -> {
                    outgoingFlows.add(row.getTargetRef());
                });
                ProcessTableVo vo = ProcessTableVo.builder()
                        .nodeType("userTask").nodesId(userTask.getId()).nodesName(userTask.getName()).assignee(userTask.getAssignee()).candidateUsers(userTask.getCandidateUsers())
                        .candidateGroups(userTask.getCandidateGroups()).incomingFlows(incomingFlows).outgoingFlows(outgoingFlows).flowType("审批")
                        .build();
                resultList.add(vo);
            } else if (flowElement instanceof ExclusiveGateway) {
                ExclusiveGateway gateway = (ExclusiveGateway) flowElement;
                gateway.getIncomingFlows().forEach(row -> {
                    incomingFlows.add(row.getSourceRef());
                });
                gateway.getOutgoingFlows().forEach(row -> {
                    outgoingFlows.add(row.getTargetRef());
                });
                ProcessTableVo vo = ProcessTableVo.builder()
                        .nodeType("exclusiveGateway").nodesId(gateway.getId()).nodesName(gateway.getName()).assignee("自动").incomingFlows(incomingFlows).outgoingFlows(outgoingFlows).flowType("条件判断")
                        .build();
                resultList.add(vo);
            }
        }
        //查询已审批信息
        List<ApprovedNodesInfo> approvedNodesInfoList = ProcWorkflowFormdataService.getApproveNodesInfo(businessId, PASS);

        for (ProcessTableVo vo : resultList) {
            ProcessTableResultVo resultVo = new ProcessTableResultVo();
            resultVo.setAssignee(vo.getAssignee() == null ? approvedNodesInfoList.get(0).getCreateBy() : vo.getAssignee());
            resultVo.setNodesName(vo.getNodesName());
            resultVo.setFlowType(vo.getFlowType());
            resultVo.setStatus("未处理");
            resultVo.setIncomingFlows(vo.getIncomingFlows());
            resultVo.setNodesId(vo.getNodesId());
            resultVo.setNodeType(vo.getNodeType());
            List<String> outgoing = new LinkedList<>();
            if (vo.getAssignee() == null || vo.getAssignee().contains("${")) {
                if (vo.getCandidateUsers().size() > 0) {
                    List<String> nickNameList = new LinkedList<>();
                    for (String name : vo.getCandidateUsers()) {
                        SysUser sysUser = sysUserMapper.selectUserByUserName(name);
                        if (null == sysUser) {
                            nickNameList.add(name);
                        } else {
                            nickNameList.add(sysUser.getNickName());
                        }
                    }
                    resultVo.setAssignee(StringUtils.join(nickNameList, ","));
                } else if (vo.getCandidateGroups().size() > 0) {
                    //岗位默认处理人集合
                    List<String> postNameList = new LinkedList<>();
                    for (String postCode : vo.getCandidateGroups()) {
                        SysPost sysPost = sysPostMapper.checkPostCodeUnique(postCode);
                        List<String> nickNameList = new LinkedList<>();
                        List<SysUser> userList = getUserListByPostCode(postCode);
                        userList.forEach(row -> {
                            nickNameList.add(row.getNickName());
                        });
                        if (null != sysPost) {
                            postNameList.add(sysPost.getPostName() + "(" + StringUtils.join(nickNameList, ",") + ")");
                        }
                    }
                    resultVo.setAssignee(StringUtils.join(postNameList, ","));
                }
            }
            for (String outgoingFlow : vo.getOutgoingFlows()) {
                for (ProcessTableVo vo1 : resultList) {
                    if (vo1.getNodesId().equalsIgnoreCase(outgoingFlow)) {
                        outgoing.add(vo1.getNodesName());
                    }
                }
            }
            resultVo.setOutgoing(StringUtils.join(outgoing, ","));
            for (ApprovedNodesInfo info : approvedNodesInfoList) {
                if (vo.getNodesId().equals(info.getStepId())) {
                    resultVo.setDetailName(info.getCreateName());
                    resultVo.setStatus("已完成");
                }
            }
            SysUser sysUser = sysUserMapper.selectUserByUserName(resultVo.getAssignee());
            if (null != sysUser && sysUser.getNickName() != null) {
                resultVo.setAssignee(sysUser.getNickName());
            }
            resultVoList2.add(resultVo);
        }
        List<ProcessTableResultVo> userTaskList = resultVoList2.stream().filter(row -> "userTask".equals(row.getNodeType())).collect(Collectors.toList());
        for (ProcessTableResultVo resultVo : resultVoList2) {
            if ("exclusiveGateway".equals(resultVo.getNodeType())) {
                for (ProcessTableResultVo vo : userTaskList) {
                    if (resultVo.getIncomingFlows().contains(vo.getNodesId()) && "已完成".equals(vo.getStatus())) {
                        resultVo.setStatus("已完成");
                        break;
                    }
                }
            }
        }

        return resultVoList2;
    }

    @Override
    public Map<String, Object> statisticsData(String businessId) {

        Map<String, Object> approvalResultMap = new LinkedHashMap<>();
        //所有审批节点数量
        int totalUserTask =0;
        //已通过的节点数量
        int alreadyNum =0;
        //已驳回数量
        int rejectNum =0;
        org.activiti.engine.task.Task task = getTaskDefinitionKey(businessId);
        BpmnModel bpmnModel =null;
        if (task != null) {
            bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        }else{
            List<HistoricTaskInstance> hisList = getHistoryTaskDefinitionKey(businessId);
            if(null != hisList){
                bpmnModel = repositoryService.getBpmnModel(hisList.get(0).getProcessDefinitionId());
            }
        }
        //草稿节点，直接返回空
        if(null == bpmnModel){
            Map<String,Object> statisticsMap = new LinkedHashMap<>();
            statisticsMap.put("rejectNum",null);
            statisticsMap.put("alreadyNum",null);
            statisticsMap.put("totalTime",null);
            approvalResultMap.put("statisticsMap", statisticsMap);
            approvalResultMap.put("approvalDetails", new LinkedList<>());
            return approvalResultMap ;
        }
        //所有节点
        Process process = bpmnModel.getProcesses().get(0);
        List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);
        totalUserTask = userTaskList.size();

        //查询已审批所有节点信息
        Map<String, Object>  resultMap = (Map<String, Object>)getFlowInfoByBusinessId(businessId).get("data");
        //已审批节点信息
        List<ProcWorkflowFormdata> list = (List<ProcWorkflowFormdata>) resultMap.get("examineAndApproveRecord");
        List<ProcWorkflowFormdata> approvedList = list.stream().filter(row -> ("1".equals(row.getPass())) || "999".equals(row.getPass())).collect(Collectors.toList());
        //当前节点信息
        List<Map<String, Object>> currentNodeInfoList = (List<Map<String, Object>>) resultMap.get("currentNodeInfo");
        //审批统计信息
        Map<String,Object> statisticsMap = new LinkedHashMap<>();
        //去重后已处理数量
        List<ProcWorkflowFormdata> distinctList = approvedList.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(ProcWorkflowFormdata::getStepId))), ArrayList::new));
        for (ProcWorkflowFormdata vo :distinctList) {
            if(PASS.equals(vo.getPass()) || "999".equals(vo.getPass())){
                alreadyNum++;
            }
        }

        Date startTime = list.get(0).getCreateTime();
        Date endTime = list.get(list.size()-1).getCreateTime();
        String totalTime = DateUtils.getDatePoorForSec(endTime,startTime);
        statisticsMap.put("alreadyNum",alreadyNum + "/" + totalUserTask);
        statisticsMap.put("totalTime",totalTime);
        List<ApprovalDetailsBo> approvalDetails = new LinkedList<>();
        for (Map<String, Object> currentNodeInfo:currentNodeInfoList) {
            if(null != currentNodeInfo.get("taskId")){
                //待处理明细
                ApprovalDetailsBo waitDetailBo = ApprovalDetailsBo.builder().taskNodeName(currentNodeInfo.get("currentNode") == null ? "": currentNodeInfo.get("currentNode").toString())
                        .detailName(String.join(",",(List<String>)currentNodeInfo.get("currentCandidateUsersOfUserName"))).status("待处理").createTime(list.get(list.size()-1).getCreateTime())
                        .build();

                int num =  yspUrgentReviewRecordMapper.selectNumBybusIdAndNodeName(businessId,waitDetailBo.getTaskNodeName());
                waitDetailBo.setCsnum(num+"");
                approvalDetails.add(waitDetailBo);



            }
        }
        //已审批完成明细
        for (int i = list.size()-1; i >= 0; i--) {
            ApprovalDetailsBo bo = ApprovalDetailsBo.builder()
                    .taskNodeName(list.get(i).getTaskNodeName()).detailName(list.get(i).getCreateName()).detailTime(list.get(i).getCreateTime()).status("已完成")
                    .build();
            if(i>0){
                bo.setCreateTime(list.get(i-1).getCreateTime());
            }
            if(REJECT.equals(list.get(i).getPass())){
                rejectNum++;
            }
            if(null!=bo.getCreateTime() && null !=bo.getDetailTime()){
                String elapsedTime = DateUtils.getDatePoorForSec(bo.getDetailTime(),bo.getCreateTime());
                bo.setElapsedTime(elapsedTime);
            }
            int num =  yspUrgentReviewRecordMapper.selectNumBybusIdAndNodeName(businessId,bo.getTaskNodeName());
            bo.setCsnum(num+"");
            approvalDetails.add(bo);
        }
        statisticsMap.put("rejectNum",rejectNum);
        approvalResultMap.put("statisticsMap",statisticsMap);
        approvalResultMap.put("approvalDetails",approvalDetails);
        return approvalResultMap;
    }

    @Override
    public int updateReadFlag(String copyTaskId) {
        Long id = null;
        if(StringUtils.isNotEmpty(copyTaskId)){
            id = Long.valueOf(copyTaskId);
        }
        return procFormDataMapper.updateReadFlag(id,SecurityUtils.getUsername(),"1");
    }

    @Override
    public int addCopyCommon(long copyTaskId, String comment) {
        ActCopyTaskVo vo = procFormDataMapper.queryCopyTaskVo(copyTaskId);
        if(null !=vo){
            ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
            procWorkflowFormdata.setId(UUID.randomUUID().toString());
            procWorkflowFormdata.setBusinessKey(vo.getBusinessKey());
            procWorkflowFormdata.setComment(comment);
            // pass-8:抄送
            procWorkflowFormdata.setPass("8");
            procWorkflowFormdata.setTaskNodeName(vo.getTaskNodeName());
            procWorkflowFormdata.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
            procWorkflowFormdata.setAssociationName(vo.getCreateBy());
            procWorkflowFormdata.setCreateBy(SecurityUtils.getUsername());
            procWorkflowFormdata.setStepId(vo.getStepId());
            procWorkflowFormdata.setCreateTime(DateUtils.getNowDate());
            return ProcWorkflowFormdataService.insertProcWorkflowFormdata(procWorkflowFormdata);
        }
        return 0;
    }

    @Override
    public int updateReadFlagFormData(ProcFormReadRecodeVo vo) {
        vo.setDetailBy(SecurityUtils.getUsername());
        return procFormDataMapper.deleteReadFlagFormData(vo);
    }


    //2024-07-19 新增审核不通过状态 暂时归于驳回中 所以得把状态参数变为可动
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int stopFlowByBusinessId(String businessId, LoginUser loginUser,String status) {
//        //根据businessId获取实例id
        String instanceId =  oaProcessTemplateMapper.getDataByBusinessId(businessId);
        String taskId =  oaProcessTemplateMapper.getTaskId(instanceId);
        ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
        procWorkflowFormdata.setId(UUID.randomUUID().toString());
        procWorkflowFormdata.setBusinessKey(businessId);
        procWorkflowFormdata.setComment("废弃流程");
        procWorkflowFormdata.setPass("77");//废弃
        procWorkflowFormdata.setCreateName(SecurityUtils.getLoginUser().getUser().getNickName());
        procWorkflowFormdata.setCreateBy(SecurityUtils.getUsername());
        //废弃流程，true:校验是否为加签;20240422-yuqiang-新增判断审批节点信息是否存在，审批节点不存在直接废弃，不用完成审批任务
        if(StringUtils.isNotEmpty(taskId)){
            org.activiti.engine.task.Task task = getTaskDefinitionKey(businessId);
            procWorkflowFormdata.setTaskNodeName(task.getName());
            procWorkflowFormdata.setStepId(task.getTaskDefinitionKey());
            oaProcessTemplateService.endTask(businessId,taskId,true);
        }
        if (!status.equals("10")){
            ProcWorkflowFormdataService.insertProcWorkflowFormdata(procWorkflowFormdata);
        }
        return oaProcessTemplateMapper.stopFlowByBusinessId(businessId, loginUser.getUser().getNickName(), DateUtils.getNowDate(),status);
    }
    @Override
    public int addReadFlagFormData(String businessId) {
        List<FlowUser> flowUserList = oaProcessTemplateMapper.selectUserListForFlowByBusinessId(businessId);
        ProcFormReadRecodeVo readRecodeVo = ProcFormReadRecodeVo.builder().businessKey(businessId).businessStatus("3").readFlag("F").build();
        ProcFormData procFormData = procFormDataMapper.getDataByBusindess(businessId);
        List<Long> userIdList = new LinkedList<>();
        for (FlowUser user : flowUserList) {
            userIdList.add(user.getUserId());
            readRecodeVo.setDetailBy(user.getUserName());
            procFormDataMapper.addProcReadRecode(readRecodeVo);
        }

        if (userIdList.size() > 0) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            //查询模板名称
            OaProcessTemplate oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateHisById(procFormData.getTemplateId());
            String msg =
                    "<div class=\"gray\">" + procFormData.getTheme() + "</div>" +
                            "<div class=\"gray\">流程模板：" + oaProcessTemplate.getTemplateName() + "</div>" +
                            "<div class=\"gray\">发起人：" + procFormData.getNickName() + "</div>" +

                            "<div class=\"gray\">发起时间：" + format.format(procFormData.getCreateTime()) + "</div>";
            List<VxUser> vxUserList = vxMapper.selectByUserId(userIdList);
            for (VxUser vxUser : vxUserList) {
                String sendUser = "|" + vxUser.getVxId();
                try {
                    accessTokenUtils.sendMsg(sendUser, msg, "5");
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return 0;
    }

    @Override
    public int batchUpdateReadFlagFormData(List<String> businessIds){
        return procFormDataMapper.batchDeleteReadFlagFormData(SecurityUtils.getUsername(),businessIds);
    }

    @Override
    public int batchUpdateCopyReadFlag(List<Long> ids) {
        return procFormDataMapper.batchUpdateCopyReadFlag(ids);
    }

    /**
     * @param saveDataBo
     * @param loginUser
     * @return {@link Map}<{@link String}, {@link Object}>
     *     isok     返回 N 则表示此流程没有配置项目与流程关联或者此流程不是返费流程
     *              返回 D 表示是返费流程且金额>需要冲的金额
     *              返回 S 表示流程金额 = 要冲的金额
     *              返回 X 表示流程金额 < 要冲的金额
     *    amount 金额要冲的金额 isok为N时为0.00
     */
    @Override
    public Map<String, Object> checkFeeFlow(SaveDataBo saveDataBo, LoginUser loginUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        String formId = saveDataBo.getFormId();
        String data = saveDataBo.getData();
        JSONObject jsonObject = JSON.parseObject(data);

        OaProcessTemplate oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateById(saveDataBo.getTemplateId());
        OaProjectFlowMain oaProjectFlowMain = new OaProjectFlowMain();

        oaProjectFlowMain.setModelId(oaProcessTemplate.getParentId());
        oaProjectFlowMain.setIsLinkageCwxmgl("Y");
        List<OaProjectFlowMain> dataByParams = oaProjectFlowMainService.selectOaProjectFlowMainList(oaProjectFlowMain);

        if(dataByParams.size() == 0){
            returnMap.put("isok","N");
            returnMap.put("amount",0.00);
        }else {
            OaProjectFlowMain dataByParam = dataByParams.get(0);


            //项目类型
            Long projectTypeId = null;
            if (null == dataByParam.getProjectTypeField() || "".equals(dataByParam.getProjectTypeField())) {
                logger.info("此流程模版没有配置项目类型字段");
            } else {
                if(null == jsonObject.get(dataByParam.getProjectTypeField()) || "".equals(jsonObject.get(dataByParam.getProjectTypeField()))){
                    logger.info("此流程模版配置了项目类型字段，但是表单中项目类型为空");
                }else {
                    projectTypeId = Long.valueOf(jsonObject.get(dataByParam.getProjectTypeField()).toString());
                }
            }
            //金额
            BigDecimal bigDecimal = new BigDecimal(0.00);
            if (null == dataByParam.getAccountingField() || "".equals(dataByParam.getAccountingField())) {
                logger.info("此流程没有配置金额字段");
            } else {
                bigDecimal = new BigDecimal(jsonObject.get(dataByParam.getAccountingField()).toString());
            }
            //返费公司
            Long feeCompanyId = null;
            if(null == dataByParam.getFeeCompanyField()  || "".equals(dataByParam.getFeeCompanyField())){
                logger.info("此流程没有配置返费公司字段");
            }else {
                if(null == jsonObject.get(dataByParam.getFeeCompanyField()) || "".equals(jsonObject.get(dataByParam.getFeeCompanyField()))){
                    logger.info("此流程模版配置了返费公司字段，但是表单中返费公司为空");
                }else {
                    feeCompanyId = Long.valueOf(jsonObject.get(dataByParam.getFeeCompanyField()).toString());
                }

            }
            BigDecimal feeAmount = cwProjectCustService.checkPayFeeFromOaSystem(feeCompanyId, projectTypeId.toString(), loginUser.getUserId());
            int i = bigDecimal.compareTo(feeAmount);


            if(i<0){
                //表示流程金额<要冲的金额
                returnMap.put("isok","X");
            }else if(i==0){
                //表示流程金额=要冲的金额
                returnMap.put("isok","S");
            } else if (i>0) {
                //表示流程金额>要冲的金额
                returnMap.put("isok","D");
            }
            returnMap.put("amount",feeAmount);
        }


        return returnMap;
    }

    /**
     *根据部门code获取用户信息
     * @return
     */
    private List<SysUser> getUserListByPostCode(String postCode){
        SysUser sysUser = new SysUser();
        SysPostAO sysPostAO = new SysPostAO();
        sysPostAO.setPostCode(postCode);
        sysUser.setPost(sysPostAO);
        List<SysUser> userListByPostId = sysUserMapper.getUserListByPostId(sysUser);
        List<SysUser> userList = userListByPostId.stream().collect(Collectors.collectingAndThen(Collectors
                .toCollection(()->new TreeSet<>(Comparator.comparing(SysUser::getUserId))), ArrayList::new));
        return userList;
    }

    /**
     * 驳回起始节点后，编辑提交直接自动审批
     */
    private void execFlowNodeFirst(FlowInfoDTO flowInfoDTO,LoginUser loginUser){
        AjaxResult flowInfo = getFlowInfoByBusinessId(flowInfoDTO.getBusinessId());
        Map<String, Object> resultMap = (Map<String, Object>) flowInfo.get("data");
        List< Map<String, Object>>   currentNodeInfoList = (List<Map<String, Object>>) resultMap.get("currentNodeInfo");
        Map<String, Object> currentNodeInfo = currentNodeInfoList.get(0);
        ExecBO execBO = new ExecBO();
        execBO.setTaskID(currentNodeInfo.get("taskId").toString());
        execBO.setPass("1");
        execBO.setComment("驳回到起始节点自动审批");
        execBO.setBusinessKey(flowInfoDTO.getBusinessId());
        execBO.setAutomaticSkip(false);
        execBO.setTaskNodeName(currentNodeInfo.get("currentNode").toString());
        execBO.setStepId(currentNodeInfo.get("stepId").toString());
        execBO.setNextFlowApproveUserName(flowInfoDTO.getNextFlowApproveUserName());
        execBO.setNextNodeId(flowInfoDTO.getNextNodeId());
        execBO.setUserName(SecurityUtils.getUsername());
        execBO.setEditPayee(false);
        execFlowNode(execBO,loginUser);
    }


    /**
     * 驳回审批后重新连线
     * @param taskID,rejectNodes,currentFlowNode
     * @return
     */
    public void detailFlowNode(String taskID,String rejectNodes,String currentFlowNode,String currentFlowNodeAssignee, HashMap<String, Object> variables){
        Task task = taskRuntime.task(taskID);
        org.activiti.engine.runtime.ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        String processInstanceId = processInstance.getProcessInstanceId();
        //  获取所有历史任务（按创建时间降序）
        List<HistoricTaskInstance> hisTaskList = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByTaskCreateTime().asc().list();
        System.out.println("历史任务数量："+hisTaskList.size());
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        //获取当前活动节点
        FlowNode sourceFlow = (FlowNode) bpmnModel.getMainProcess().getFlowElement(rejectNodes);
        //目标节点
        FlowNode targetFlow = (FlowNode) bpmnModel.getMainProcess().getFlowElement(currentFlowNode);

        //临时保存当前活动的原始方向
        List<SequenceFlow> originalSequenceFlowList = new ArrayList<>();
        originalSequenceFlowList.addAll(sourceFlow.getOutgoingFlows());
        try {
            //清理活动方向
            sourceFlow.getOutgoingFlows().clear();

            //建立新方向
            SequenceFlow newSequenceFlow = new SequenceFlow();
            newSequenceFlow.setId("newSequenceFlowId");
            newSequenceFlow.setSourceFlowElement(sourceFlow);
            newSequenceFlow.setTargetFlowElement(targetFlow);

            List<SequenceFlow> newSequenceFlowList = new ArrayList<>();
            newSequenceFlowList.add(newSequenceFlow);
            //  当前节点指向新的方向
            sourceFlow.setOutgoingFlows(newSequenceFlowList);
            //清除驳回设置的变量
            variables.put("rejectFlowNodeSequence", null);
            //带参数完成任务
            taskRuntime.complete(TaskPayloadBuilder.complete().withTaskId(taskID)
                    .withVariables(variables)
                    .build());
            //  重新查询当前任务
            org.activiti.engine.task.Task nextTask = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
            if (null != nextTask) {
                taskService.setAssignee(nextTask.getId(), currentFlowNodeAssignee);
            }
        }catch (Exception e){
            throw e;
        }finally {
            //重新连线回本来活动方向
            sourceFlow.setOutgoingFlows(originalSequenceFlowList);
        }
    }

    /**
     * 计算boolean表达式的值
     * @param expression 表达式
     * @param varsMap2   参数
     * @return boolean值
     */
    public boolean calculateExpression(String expression, Map<String, Object> varsMap2) {
        boolean flag = true;
        ExpressionFactory factory = new ExpressionFactoryImpl();
        SimpleContext context = new SimpleContext();
        String dataJson = varsMap2.get("customParam").toString();
        Map<String,Object> mapType = JSON.parseObject(dataJson,Map.class);
        Map<String, Object> varsMap = new HashMap<>();
        varsMap.put("customParam",mapType);
        for (Object k : varsMap.keySet()) {
            if (varsMap.get(k) != null) {
                context.setVariable(k.toString(), factory.createValueExpression(varsMap.get(k), varsMap.get(k).getClass()));
            }
        }
        ValueExpression e = factory.createValueExpression(context, expression, Boolean.class);
        try{
            flag = (Boolean)e.getValue(context);
        }catch (Exception exception){
            logger.info("网关获取下一节点信息失败，默认为false",exception);
        }finally {
            return flag;
        }
    }

    /**
     * 通知审核人公共方法
     */
    public void notifyReviewer(NotifyReviewerDto notifyReviewerDto){
        //  重新查询当前任务
        org.activiti.engine.task.Task nextTask = taskService.createTaskQuery().processInstanceId(notifyReviewerDto.getProcessInstanceId()).singleResult();
        if (nextTask != null) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(nextTask.getProcessInstanceId()).singleResult();
            //获取bpmnModel对象
            BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
            //因为我们这里只定义了一个Process 所以获取集合中的第一个即可
            Process process = bpmnModel.getProcesses().get(0);
            //获取所有的FlowElement信息
            List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);


            List<String> candidateUsers = new ArrayList<>();
            List<Long> userList = new ArrayList<>();
            for (UserTask userTask:userTaskList) {
                //获取当前任务节点Id
                String id1 = userTask.getId();
                if (id1.equals(notifyReviewerDto.getTaskDefinitionKey())) {
                    String assignee = nextTask.getAssignee();
                    candidateUsers = userTask.getCandidateUsers();
                    if (assignee != null) {
                        if (candidateUsers.size() > 0) {
                            String s = candidateUsers.get(0);
                            if (!s.equals(assignee)) {
                                List<String> newCandidateUsers = new ArrayList<>();
                                newCandidateUsers.add(assignee);
                                candidateUsers = newCandidateUsers;
                            }
                        } else {
                            List<String> newCandidateUsers = new ArrayList<>();
                            newCandidateUsers.add(assignee);
                            candidateUsers = newCandidateUsers;
                        }
                    }

                }
            }

            for (String candidateUser : candidateUsers) {
                ProcFormReadRecodeVo vo = ProcFormReadRecodeVo.builder().businessKey(historicProcessInstance.getBusinessKey()).businessStatus("2").detailBy(candidateUser).readFlag("F")
                        .build();
                procFormDataMapper.addProcReadRecode(vo);
                SysUser sysUser = sysUserMapper.selectUserByUserName(candidateUser);
                userList.add(sysUser.getUserId());
            }

            //根据用户id查询要发送的企业微信账号
            if(userList.size()>0){
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                //查询模板名称
                OaProcessTemplate oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateHisById(notifyReviewerDto.getTemplateId());
                String msg =
                        "<div class=\"gray\">"+notifyReviewerDto.getTheme()+"</div>" +
                                "<div class=\"gray\">流程模板："+oaProcessTemplate.getTemplateName()+"</div>" +
                                "<div class=\"gray\">发起人："+notifyReviewerDto.getStartNickName()+"</div>" +

                                "<div class=\"gray\">发起时间："+format.format(notifyReviewerDto.getCreateTime())+"</div>";
                String sendUser = "";
                List<VxUser>  vxUserList =  vxMapper.selectByUserId(userList);
                for (VxUser vxUser : vxUserList) {
                    sendUser = sendUser+"|"+vxUser.getVxId();
                }
                try {
                    accessTokenUtils.sendMsg(sendUser,msg,"1");
                } catch (IOException e) {
                    e.printStackTrace();
                }

            }

        }
    }

    /**
     * 更新表单数据
     * @param dataJson
     * @param updateDataJson
     * @param formId
     * @return
     */
    private String updateFormData(String dataJson,String updateDataJson,String formId,List<String> editPayeeList){
        Map<String,Object> dataJsonMap = JSON.parseObject(dataJson,Map.class);
        Map<String,Object> updateDataJsonMap = JSON.parseObject(updateDataJson,Map.class);
        ProcFormDef procFormDef= procFormDefMapper.selectProcFormDefById(formId);
        JSONObject jsonObject = JSONObject.parseObject(procFormDef.getDefination());
        JSONArray list = jsonObject.getJSONArray("list");
        for (String editPayee:editPayeeList) {
            recursionFn(editPayee,dataJsonMap,updateDataJsonMap,list);
        }
        return JSONArray.toJSONString(dataJsonMap);
    }


    /**
     * 迭代循环表单获取可修改字段并更新map
     * @param dataJsonMap
     * @param updateDataJsonMap
     * @param list
     */
    private void recursionFn(String editPayee,Map<String,Object> dataJsonMap,Map<String,Object> updateDataJsonMap,JSONArray list){
        for (int i = 0; i < list.size(); i++) {
            boolean isEdit =false;
            if(null !=list.getJSONObject(i).getJSONObject("options")){
                isEdit = list.getJSONObject(i).getJSONObject("options").getBoolean(editPayee)==null?false:list.getJSONObject(i).getJSONObject("options").getBoolean(editPayee);
            }
            if(isEdit){
                String key =  list.getJSONObject(i).getString("key");
                dataJsonMap.put(key,updateDataJsonMap.get(key));
            }
            JSONArray columnsChildList = list.getJSONObject(i).getJSONArray("columns");
            JSONArray childList = list.getJSONObject(i).getJSONArray("list");
            if(columnsChildList !=null && childList !=null){
                childList.addAll(columnsChildList);
            }else if(columnsChildList != null && childList ==null){
                childList = columnsChildList;
            }
            if(null !=childList && childList.size()>0){
                recursionFn(editPayee,dataJsonMap,updateDataJsonMap,childList);
            }
        }
    }

    /**
     * 新增抄送人
     */
    private void addCopyTask(String businessKey,ProcFormData procFormData,List<String> userList){
        String processInstanceId = procFormData.getInstanceId();
        List<String> detailUserList = procFormDataMapper.queryCopyTaskUserName(businessKey, processInstanceId);
        detailUserList.forEach(row ->{
            userList.removeIf(e ->e.equals(row));
        });
        if(userList.size()>0){
            org.activiti.engine.task.Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
            ActCopyTaskVo vo = ActCopyTaskVo.builder().createBy(SecurityUtils.getUsername()).processDefinitionId(task.getProcessDefinitionId()).businessKey(businessKey)
                    .processInstanceId(task.getProcessInstanceId()).readFlag("0").executionId(task.getExecutionId()).stepId(task.getTaskDefinitionKey()).taskNodeName(task.getName())
                    .build();
            procFormDataMapper.addCopyTask(vo,userList);

            //企业微信通知
            List<Long> userIdList = new LinkedList<>();
            for (String candidateUser : userList) {
                SysUser sysUser = sysUserMapper.selectUserByUserName(candidateUser);
                userIdList.add(sysUser.getUserId());
            }

            //根据用户id查询要发送的企业微信账号
            if(userIdList.size()>0){
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                //查询模板名称
                OaProcessTemplate oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateHisById(procFormData.getTemplateId());
                String msg =
                        "<div class=\"gray\">"+procFormData.getTheme()+"</div>" +
                                "<div class=\"gray\">流程模板："+oaProcessTemplate.getTemplateName()+"</div>" +
                                "<div class=\"gray\">发起人："+procFormData.getNickName()+"</div>" +

                                "<div class=\"gray\">发起时间："+format.format(procFormData.getCreateTime())+"</div>";
                String sendUser = "";
                List<VxUser>  vxUserList =  vxMapper.selectByUserId(userIdList);
                for (VxUser vxUser : vxUserList) {
                    sendUser = sendUser+"|"+vxUser.getVxId();
                }
                try {
                    accessTokenUtils.sendMsg(sendUser,msg,"3");
                } catch (IOException e) {
                    e.printStackTrace();
                }

            }
        }
    }

    /**
     * 通过网关节点的扩展信息获取需跳过节点的审批信息，可能为审批人，也可能是审批组
     * @return
     */
    private Integer getCeoUserNameSkip(String loginUser,FlowElement flowElement){
        Map<String, String> properties = new HashMap<>();
        List<ExtensionElement> propertiesElements = flowElement.getExtensionElements().get("properties");
        if (propertiesElements != null && !propertiesElements.isEmpty()) {
            List<ExtensionElement> propertyElements = propertiesElements.get(0).getChildElements().get("property");
            if (propertyElements != null && !propertyElements.isEmpty()) {
                for (ExtensionElement propertyElement : propertyElements) {
                    String key = propertyElement.getAttributeValue(null, "name");
                    String val = propertyElement.getAttributeValue(null, "value");
                    properties.put(key, val);
                }
            }
        }

        String nextGroup = properties.get("nextGroup");
        String nextAssignee = properties.get("nextAssignee");
        if(StringUtils.isEmpty(nextGroup) && StringUtils.isEmpty(nextAssignee)){
            return null;
        }
        long deptId;
        if(StringUtils.isNotEmpty(nextAssignee)){
            SysPost sysPost = sysPostMapper.queryHomeSysPost(nextAssignee);
            deptId = sysPost.getDeptId();
        }else{
            SysPost sysPost = sysPostMapper.checkPostCodeUnique(nextGroup);
            deptId = sysPost.getDeptId();
        }

        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
        int isSkip = 0;
        if(null != sysDept.getLeaderId()){
            SysUser user = sysUserMapper.selectUserById(sysDept.getLeaderId());
            if(user.getUserName().equals(loginUser)){
                isSkip = 1;
            }
        }
        return isSkip;
    }

    /**
     * 通过当前节点信息判断下一节点是否为网关，若为网关，调用getCeoUserNameSkip方法判断是否赋值skip
     * @return
     */
    private Integer getExclusiveGateway(String loginUser,UserTask userTask){
        List<SequenceFlow> outgoingFlows = userTask.getOutgoingFlows();
        SequenceFlow sequenceFlow = outgoingFlows.get(0);
        FlowElement flowElement = sequenceFlow.getTargetFlowElement();
        if(!(flowElement instanceof ExclusiveGateway)){
            return null;
        }
        return getCeoUserNameSkip(loginUser,flowElement);
    }


    /**
     * @param businessId
     * @param templateId
     * 解析表单流程图
     * 通过判断isdelete 判断是否需要进行更新 Y 更新 N不更新
     */
    public void parsingFlowData(String businessId, Long templateId){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //首先查询解析表中是否有这个流程表单数据如果有则不解析  如果没有 则解析
        //由于模版可能会有版本问题  所以这里的模版id使用的是模板的parentid
        OaProcessTemplate oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateById(templateId);
        //查询解析数据
        OaProcessParsing oaProcessParsing = new OaProcessParsing();
        oaProcessParsing.setTemplateId(oaProcessTemplate.getParentId());
        List<OaProcessParsing> oaProcessParsings = oaProcessParsingMapper.selectOaProcessParsingList(oaProcessParsing);

        Date updateTime = oaProcessTemplate.getUpdateTime();

        logger.info("此次流程解析的模版名称为：”"+oaProcessTemplate.getTemplateName()+"“，使用的模版parentId为：”"+oaProcessTemplate.getParentId()+"”");
        //如果数据库中已存在解析过这个模版并且是需要更新  则删除后重新解析入库
        if(oaProcessParsings.size()>0){
            Boolean b = false;
            try {
                //判断模板的修改日期和数据的解析日期，如果修改日期大于解析日期 则要删除解析数据重新解析
                Date format = sdf.parse(oaProcessParsings.get(0).getParsingTime());
                int i = updateTime.compareTo(format);
                //如果修改日期大于解析日期则返回1 这时候就需要进行删除后重新解析  如果小于或者等于则会返回0或者-1 这时候证明解析的数据为最新的就不用进行操作
                if(i > 0){
                    b = true;
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            //如果为true则证明需要重新解析
            if(b){
                //删除原本数据
                oaProcessParsingMapper.deleteDataByemplateId(oaProcessTemplate.getParentId());
                this.getProcessParingData(oaProcessTemplate.getParentId(),businessId);
                logger.info("流程解析为删除原本解析的流程后重新解析");
            }

        }else //如果数据库中没有则解析
            if(oaProcessParsings.size()==0){
                this.getProcessParingData(oaProcessTemplate.getParentId(),businessId);
                logger.info("流程解析为第一次解析");
            }
        //如果数据库中没有解析 则执行else  if
        // isDelete传什么都无所谓  如果数据库中有解析并且isDelete传过来是N 表示不用作改变



    }

    /**
     * 解析并入库操作
     * @param templateId
     * @param businessId
     * @return int
     */
    public int getProcessParingData(Long templateId,String businessId){
        //当前日期
        Date nowDate = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //当前日期
        String format = sdf.format(nowDate);
        List<OaProcessParsing> processParsingList = new ArrayList<>();
        //获取需要解析的流程的数据
        List<ProcessTableResultVo> processTable = this.asda(businessId);
        for (int i = 0;i<processTable.size();i++){
            ProcessTableResultVo processTableResultVo = processTable.get(i);
            String outgoingID = "";
            if(!processTableResultVo.getOutgoing().equals("")){
                outgoingID = processTable.get(i).getNodesId();
            }
            OaProcessParsing addOaPropar = new OaProcessParsing();
            addOaPropar.setTemplateId(templateId);
            addOaPropar.setNodesId(processTableResultVo.getNodesId());
            addOaPropar.setNodesName(processTableResultVo.getNodesName());
            addOaPropar.setAssignee(this.processAssignee(processTableResultVo.getAssignee()));
            addOaPropar.setIncomingFlows(processTableResultVo.getIncomingFlows().toString());
            addOaPropar.setOutgoingId(outgoingID);
            addOaPropar.setOutgoing(processTableResultVo.getOutgoing());
            addOaPropar.setParsingTime(format);
            addOaPropar.setNodesSort((i+1)+"");
            processParsingList.add(addOaPropar);
        }

        return oaProcessParsingMapper.batchInsert(processParsingList);

    }

    /**
     * 把处理人处理成  用户id  并且前后拼接&  比如&1&
     * @param assignee
     * @return {@link String}
     */
    public String processAssignee(String assignee){
        String returnName = null;
        List<String> userNameList = null;
        if(assignee.contains("(") && assignee.contains(")")){
            String substring1 = assignee.substring(assignee.indexOf("(")+1, assignee.indexOf(")"));
            userNameList = Arrays.asList(substring1.split(","));
        }else {
            userNameList = Arrays.asList(assignee.split(","));
        }
        List<SysUser> sysUserList = sysUserMapper.queryDataByUserNames(userNameList);

        for (SysUser sysUser : sysUserList) {
            returnName = returnName+"&"+sysUser.getUserId()+"&";
        }
        return returnName;
    }


    public List<ProcessTableResultVo> asda(String businessId){
        List<ProcessTableVo> resultList = new LinkedList<>();
        List<ProcessTableVo> returnList = new LinkedList<>();
        List<ProcessTableResultVo> resultVoList2 = new LinkedList<>();
        org.activiti.engine.task.Task task = getTaskDefinitionKey(businessId);
        BpmnModel bpmnModel = null;
        // 判断节点
        List<ExclusiveGateway> exclusiveGateways = new ArrayList<>();
        String startNodeId = null;
        String endNodeId = null;
        if (task != null) {
            bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        } else {
            List<HistoricTaskInstance> hisList = getHistoryTaskDefinitionKey(businessId);
            if (null != hisList) {
                bpmnModel = repositoryService.getBpmnModel(hisList.get(0).getProcessDefinitionId());
            }
        }
        //如果查询不到流程信息，直接返回空
        if (null == bpmnModel) {
            return resultVoList2;
        }
        //所有节点
        Collection<FlowElement> flowElements = bpmnModel.getMainProcess().getFlowElements();
        for (FlowElement flowElement : flowElements) {
            //流入方向
            List<String> incomingFlows = new LinkedList<>();
            //流出方向
            List<String> outgoingFlows = new LinkedList<>();
            if (flowElement instanceof UserTask) {
                UserTask userTask = (UserTask) flowElement;
                userTask.getIncomingFlows().forEach(row -> {
                    incomingFlows.add(row.getSourceRef());
                });
                userTask.getOutgoingFlows().forEach(row -> {
                    outgoingFlows.add(row.getTargetRef());
                });
                ProcessTableVo vo = ProcessTableVo.builder()
                        .nodeType("userTask").nodesId(userTask.getId()).nodesName(userTask.getName()).assignee(userTask.getAssignee()).candidateUsers(userTask.getCandidateUsers())
                        .candidateGroups(userTask.getCandidateGroups()).incomingFlows(incomingFlows).outgoingFlows(outgoingFlows).flowType("审批")
                        .build();
                resultList.add(vo);
            } else if (flowElement instanceof ExclusiveGateway) {
                ExclusiveGateway gateway = (ExclusiveGateway) flowElement;
                gateway.getIncomingFlows().forEach(row -> {
                    incomingFlows.add(row.getSourceRef());
                });
                gateway.getOutgoingFlows().forEach(row -> {
                    outgoingFlows.add(row.getTargetRef());

                });
                ProcessTableVo vo = ProcessTableVo.builder()
                        .nodeType("exclusiveGateway").nodesId(gateway.getId()).nodesName(gateway.getName()).assignee("自动").incomingFlows(incomingFlows).outgoingFlows(outgoingFlows).flowType("条件判断")
                        .build();
                resultList.add(vo);
                exclusiveGateways.add(gateway);
            }else if (flowElement instanceof StartEvent) {
                StartEvent startEvent = (StartEvent) flowElement;
                startEvent.getIncomingFlows().forEach(row -> {
                    incomingFlows.add(row.getSourceRef());
                });
                startEvent.getOutgoingFlows().forEach(row -> {
                    outgoingFlows.add(row.getTargetRef());
                });
                ProcessTableVo vo = ProcessTableVo.builder()
                        .nodeType("exclusiveGateway").nodesId(startEvent.getId()).nodesName(startEvent.getName()).assignee("自动").incomingFlows(incomingFlows).outgoingFlows(outgoingFlows).flowType("开始节点")
                        .build();
                startNodeId = startEvent.getId();
                resultList.add(0,vo);
            }else if (flowElement instanceof EndEvent) {
                EndEvent endEvent = (EndEvent) flowElement;
                endEvent.getIncomingFlows().forEach(row -> {
                    incomingFlows.add(row.getSourceRef());
                });
                endEvent.getOutgoingFlows().forEach(row -> {
                    outgoingFlows.add(row.getTargetRef());
                });
                ProcessTableVo vo = ProcessTableVo.builder()
                        .nodeType("exclusiveGateway").nodesId(endEvent.getId()).nodesName(endEvent.getName()).assignee("自动").incomingFlows(incomingFlows).outgoingFlows(outgoingFlows).flowType("结束节点")
                        .build();
                endNodeId = endEvent.getId();
                resultList.add(vo);
            }
        }
        Map<String, ProcessTableVo> stringProcessTableVoMap = this.listToMap(resultList);
        //得到start节点的节点id

        String outNodeId = startNodeId;
        //获取开始节点信息 并覆盖下一节点id
        ProcessTableVo processTableVo = stringProcessTableVoMap.get(outNodeId);
        returnList.add(processTableVo);
        outNodeId =  processTableVo.getOutgoingFlows().get(0);

        for (int i = 0;i<resultList.size();i++){
            if(!outNodeId.equals(endNodeId)){

                ProcessTableVo mapdata = stringProcessTableVoMap.get(outNodeId);
                if( this.isChongfu(mapdata.getNodesId(),returnList)){
                    outNodeId = mapdata.getOutgoingFlows().get(0);
                    continue;
                }else {
                    returnList.add(mapdata);
                }
                if(mapdata.getOutgoingFlows().size()>1){
                    for (String outgoingFlow : mapdata.getOutgoingFlows()) {
                        ProcessTableVo data = stringProcessTableVoMap.get(outgoingFlow);
                        returnList.add(data);
                    }
                }
                outNodeId = mapdata.getOutgoingFlows().get(0);
            }else {
                break;
            }

        }

        List<ApprovedNodesInfo> approvedNodesInfoList = ProcWorkflowFormdataService.getApproveNodesInfo(businessId, PASS);

        for (ProcessTableVo vo : returnList) {
            ProcessTableResultVo resultVo = new ProcessTableResultVo();
            resultVo.setAssignee(vo.getAssignee() == null ? approvedNodesInfoList.get(0).getCreateBy() : vo.getAssignee());
            resultVo.setNodesName(vo.getNodesName());
            resultVo.setFlowType(vo.getFlowType());
            resultVo.setStatus("未处理");
            resultVo.setIncomingFlows(vo.getIncomingFlows());
            resultVo.setNodesId(vo.getNodesId());
            resultVo.setNodeType(vo.getNodeType());
            List<String> outgoing = new LinkedList<>();
            if (vo.getAssignee() == null || vo.getAssignee().contains("${")) {
                if (vo.getCandidateUsers().size() > 0) {
                    List<String> nickNameList = new LinkedList<>();
                    for (String name : vo.getCandidateUsers()) {
                        SysUser sysUser = sysUserMapper.selectUserByUserName(name);
                        if (null == sysUser) {
                            nickNameList.add(name);
                        } else {
                            nickNameList.add(sysUser.getNickName());
                        }
                    }
                    resultVo.setAssignee(StringUtils.join(nickNameList, ","));
                } else if (vo.getCandidateGroups().size() > 0) {
                    //岗位默认处理人集合
                    List<String> postNameList = new LinkedList<>();
                    for (String postCode : vo.getCandidateGroups()) {
                        SysPost sysPost = sysPostMapper.checkPostCodeUnique(postCode);
                        List<String> nickNameList = new LinkedList<>();
                        List<SysUser> userList = getUserListByPostCode(postCode);
                        userList.forEach(row -> {
                            nickNameList.add(row.getNickName());
                        });
                        if (null != sysPost) {
                            postNameList.add(sysPost.getPostName() + "(" + StringUtils.join(nickNameList, ",") + ")");
                        }
                    }
                    resultVo.setAssignee(StringUtils.join(postNameList, ","));
                }
            }
            for (String outgoingFlow : vo.getOutgoingFlows()) {
                for (ProcessTableVo vo1 : resultList) {
                    if (vo1.getNodesId().equalsIgnoreCase(outgoingFlow)) {
                        outgoing.add(vo1.getNodesName());
                    }
                }
            }
            resultVo.setOutgoing(StringUtils.join(outgoing, ","));
            for (ApprovedNodesInfo info : approvedNodesInfoList) {
                if (vo.getNodesId().equals(info.getStepId())) {
                    resultVo.setDetailName(info.getCreateName());
                    resultVo.setStatus("已完成");
                }
            }
            SysUser sysUser = sysUserMapper.selectUserByUserName(resultVo.getAssignee());
            if (null != sysUser && sysUser.getNickName() != null) {
                resultVo.setAssignee(sysUser.getNickName());
            }
            resultVoList2.add(resultVo);
        }
        List<ProcessTableResultVo> userTaskList = resultVoList2.stream().filter(row -> "userTask".equals(row.getNodeType())).collect(Collectors.toList());
        for (ProcessTableResultVo resultVo : resultVoList2) {
            if ("exclusiveGateway".equals(resultVo.getNodeType())) {
                for (ProcessTableResultVo vo : userTaskList) {
                    if (resultVo.getIncomingFlows().contains(vo.getNodesId()) && "已完成".equals(vo.getStatus())) {
                        resultVo.setStatus("已完成");
                        break;
                    }
                }
            }
        }
        //确认当前集合第一个数据是不是初始节点

        return resultVoList2;
    }



    public boolean isChongfu(String nodesId, List<ProcessTableVo> list){
        boolean b = false;

        for (ProcessTableVo processTableVo : list) {
            if(nodesId.equals(processTableVo.getNodesId())){
                b = true;
            }
        }

        return b;

    }





    public Map<String,ProcessTableVo> listToMap( List<ProcessTableVo> list){
        Map<String, ProcessTableVo> returnMap = new HashMap<>();
        for (ProcessTableVo processTableVo : list) {
            returnMap.put(processTableVo.getNodesId(),processTableVo);
        }

        return returnMap;

    }

    public Map<String, Object> getAttributesMap(Map<String, List<ExtensionAttribute>> attributes) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        //获取是否可撤回属性
        List<ExtensionAttribute> automaticApproval = attributes.get("automaticApproval");
        if (automaticApproval != null && ("true").equalsIgnoreCase(automaticApproval.get(0).getValue())) {
            resultMap.put("automaticApproval", true);
        }

        //是否自动填写审批意见
        List<ExtensionAttribute> automaticConsent = attributes.get("automaticConsent");
        if (automaticConsent != null && ("true").equalsIgnoreCase(automaticConsent.get(0).getValue())) {
            resultMap.put("automaticConsent", true);
        }

        //相邻节点审批人重复时，是否自动跳过
        List<ExtensionAttribute> automaticSkip = attributes.get("automaticSkip");
        if (automaticSkip != null && ("true").equalsIgnoreCase(automaticSkip.get(0).getValue())) {
            resultMap.put("automaticSkip", true);
        }

        //对审批人是否显示附件上传界面
        List<ExtensionAttribute> displayUpload = attributes.get("displayUpload");
        if (displayUpload != null && ("true").equalsIgnoreCase(displayUpload.get(0).getValue())) {
            resultMap.put("displayUpload", true);
        }

        //附件是否必传
        List<ExtensionAttribute> mustAttached = attributes.get("mustAttached");
        if (mustAttached != null && ("true").equalsIgnoreCase(mustAttached.get(0).getValue())) {
            resultMap.put("mustAttached", true);
        }

        //动态审批说明
        List<ExtensionAttribute> descriptionApproval = attributes.get("descriptionApproval");
        if (descriptionApproval != null) {
            resultMap.put("descriptionApproval", descriptionApproval.get(0).getValue());
        }

        //节点付款人是否可编辑
        List<ExtensionAttribute> editPayee = attributes.get("editPayee");
        if (editPayee != null && ("true").equalsIgnoreCase(editPayee.get(0).getValue())) {
            resultMap.put("editPayee", true);
        }

        //节点科目是否可编辑
        List<ExtensionAttribute> isEditSubject = attributes.get("isEditSubject");
        if (isEditSubject != null && ("true").equalsIgnoreCase(isEditSubject.get(0).getValue())) {
            resultMap.put("isEditSubject", true);
        }

        //流程是否归档
        List<ExtensionAttribute> isFiling = attributes.get("isFiling");
        if (isFiling != null && ("true").equalsIgnoreCase(isFiling.get(0).getValue())) {
            resultMap.put("isFiling", true);
        }

        //是否上传终稿扫描件
        List<ExtensionAttribute> isUltimately = attributes.get("isUltimately");
        if (isUltimately != null && ("true").equalsIgnoreCase(isUltimately.get(0).getValue())) {
            resultMap.put("isUltimately", true);
        }

        //证照签领节点
        List<ExtensionAttribute> licenseIssuance = attributes.get("licenseIssuance");
        if (licenseIssuance != null && ("true").equalsIgnoreCase(licenseIssuance.get(0).getValue())) {
            resultMap.put("licenseIssuance", true);
        }

        //证照收回节点
        List<ExtensionAttribute> licenseRetrieval = attributes.get("licenseRetrieval");
        if (licenseRetrieval != null && ("true").equalsIgnoreCase(licenseRetrieval.get(0).getValue())) {
            resultMap.put("licenseRetrieval", true);
        }

        //是否修改不可用印
        List<ExtensionAttribute> isEditUse = attributes.get("isEditUse");
        if (isEditUse != null && ("true").equalsIgnoreCase(isEditUse.get(0).getValue())) {
            resultMap.put("isEditUse", true);
        }

        //是否为人员信息修改节点
        List<ExtensionAttribute> personnelEntry = attributes.get("personnelEntry");
        if (personnelEntry != null && ("true").equalsIgnoreCase(personnelEntry.get(0).getValue())) {
            resultMap.put("personnelEntry", true);
        }

        //是否为人员信息修改节点
        List<ExtensionAttribute> personnelResign = attributes.get("personnelResign");
        if (personnelResign != null && ("true").equalsIgnoreCase(personnelResign.get(0).getValue())) {
            resultMap.put("personnelResign", true);
        }

        //发起人获取资料并填写
        List<ExtensionAttribute> isObtainFill = attributes.get("isObtainFill");
        if (isObtainFill != null && ("true").equalsIgnoreCase(isObtainFill.get(0).getValue())) {
            resultMap.put("isObtainFill", true);
        }

        //是否需要跳过选择下一个审批人
        List<ExtensionAttribute> isJumpNextHandler = attributes.get("isJumpNextHandler");
        if (isJumpNextHandler != null && ("true").equalsIgnoreCase(isJumpNextHandler.get(0).getValue())) {
            resultMap.put("isJumpNextHandler", true);
        }
        return resultMap;
    }
}
