package com.ruoyi.nocode.service;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;

public interface IPersonnelFlowService {
    AjaxResult getPersonnelOnboardingFlow(OaProcessTemplate oaProcessTemplate);

    public AjaxResult getPersonnelFormalFlow(OaProcessTemplate oaProcessTemplate);

    AjaxResult getPersonnelResignationFlow(OaProcessTemplate oaProcessTemplate);

    public AjaxResult getPersonnelTransferFlow(OaProcessTemplate oaProcessTemplate);

    AjaxResult getWorkOvertimeFlow(OaProcessTemplate oaProcessTemplate);

    AjaxResult getAskLeaveFlow(OaProcessTemplate oaProcessTemplate);

}
