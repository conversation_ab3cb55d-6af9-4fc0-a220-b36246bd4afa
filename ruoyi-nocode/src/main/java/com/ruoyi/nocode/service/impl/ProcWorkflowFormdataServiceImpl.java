package com.ruoyi.nocode.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.nocode.domain.ProcWorkflowFormdata;
import com.ruoyi.nocode.domain.vo.ApprovedNodesInfo;
import com.ruoyi.nocode.mapper.ProcWorkflowFormdataMapper;
import com.ruoyi.nocode.service.IProcWorkflowFormdataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 审批记录Service业务层处理
 *
 * @date 2022-07-29
 */
@Service
public class ProcWorkflowFormdataServiceImpl implements IProcWorkflowFormdataService
{
    @Autowired
    private ProcWorkflowFormdataMapper procWorkflowFormdataMapper;

    /**
     * 查询审批记录
     *
     * @param id 审批记录ID
     * @return 审批记录
     */
    @Override
    public ProcWorkflowFormdata selectProcWorkflowFormdataById(String id)
    {
        return procWorkflowFormdataMapper.selectProcWorkflowFormdataById(id);
    }

    /**
     * 查询审批记录列表
     *
     * @param procWorkflowFormdata 审批记录
     * @return 审批记录
     */
    @Override
    public List<ProcWorkflowFormdata> selectProcWorkflowFormdataList(ProcWorkflowFormdata procWorkflowFormdata)
    {
        return procWorkflowFormdataMapper.selectProcWorkflowFormdataList(procWorkflowFormdata);
    }

    /**
     * 新增审批记录
     *
     * @param procWorkflowFormdata 审批记录
     * @return 结果
     */
    @Override
    public int insertProcWorkflowFormdata(ProcWorkflowFormdata procWorkflowFormdata) {
        if (null == procWorkflowFormdata.getCreateTime()) {
            procWorkflowFormdata.setCreateTime(DateUtils.getNowDate());
        }
        return procWorkflowFormdataMapper.insertProcWorkflowFormdata(procWorkflowFormdata);
    }

    /**
     * 修改审批记录
     *
     * @param procWorkflowFormdata 审批记录
     * @return 结果
     */
    @Override
    public int updateProcWorkflowFormdata(ProcWorkflowFormdata procWorkflowFormdata)
    {
        procWorkflowFormdata.setUpdateTime(DateUtils.getNowDate());
        return procWorkflowFormdataMapper.updateProcWorkflowFormdata(procWorkflowFormdata);
    }

    /**
     * 批量删除审批记录
     *
     * @param ids 需要删除的审批记录ID
     * @return 结果
     */
    @Override
    public int deleteProcWorkflowFormdataByIds(String[] ids)
    {
        return procWorkflowFormdataMapper.deleteProcWorkflowFormdataByIds(ids);
    }

    /**
     * 删除审批记录信息
     *
     * @param id 审批记录ID
     * @return 结果
     */
    @Override
    public int deleteProcWorkflowFormdataById(String id)
    {
        return procWorkflowFormdataMapper.deleteProcWorkflowFormdataById(id);
    }

    /**
     * 根据businessKey获取最近一条记录
     * @param businessKey
     * @return
     */
    @Override
    public ProcWorkflowFormdata selectLastByBusinessKey(String businessKey)
    {
        return procWorkflowFormdataMapper.selectLastByBusinessKey(businessKey);
    }

    @Override
    public List<ApprovedNodesInfo> getApproveNodesInfo(String businessId, String pass) {
        return procWorkflowFormdataMapper.selectApproveNodesInfo(businessId,pass);
    }
}
