package com.ruoyi.nocode.service;


import com.ruoyi.nocode.domain.vo.DaArchivistFilesVo;

import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
public interface IDDaArchivistFilesService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    //public DaArchivistFiles selectDaArchivistFilesById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param daArchivistFiles 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    //public List<DaArchivistFiles> selectDaArchivistFilesList(DaArchivistFiles daArchivistFiles);

    /**
     * 新增【请填写功能名称】
     *
     * @param daArchivistFiles 【请填写功能名称】
     * @return 结果
     */
    //public int insertDaArchivistFiles(DaArchivistFiles daArchivistFiles);

    /**
     * 修改【请填写功能名称】
     *
     * @param daArchivistFiles 【请填写功能名称】
     * @return 结果
     */
    //public int updateDaArchivistFiles(DaArchivistFiles daArchivistFiles);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    //public int deleteDaArchivistFilesByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    //public int deleteDaArchivistFilesById(Long id);

    /**
     * 根据流程id查询终稿扫描件
     */
    List<DaArchivistFilesVo> selectDaArchivistFilesListByFlowId(String pertainFlowId);

    /**
     * 新增【请填写功能名称】
     *
     * @param fileArray 【请填写功能名称】
     * @return 结果
     */
    //public int addDaArchivistFiles(List<DaArchivistFiles> fileArray);

}
