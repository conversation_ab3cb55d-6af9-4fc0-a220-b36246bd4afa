package com.ruoyi.nocode.service;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.nocode.domain.ProcFormDef;

import java.util.List;
import java.util.Map;

/**
 * 单定义Service接口
 *
 * @date 2022-07-27
 */
public interface IProcFormDefService
{
    /**
     * 查询单定义
     *
     * @param id 单定义ID
     * @return 单定义
     */
    public ProcFormDef selectProcFormDefById(String id);

    /**
     * 查询单定义列表
     *
     * @param procFormDef 单定义
     * @return 单定义集合
     */
    public List<ProcFormDef> selectProcFormDefList(ProcFormDef procFormDef);

    /**
     * 新增单定义
     *
     * @param procFormDef 单定义
     * @return 结果
     */
    public int insertProcFormDef(ProcFormDef procFormDef);

    /**
     * 修改单定义
     *
     * @param procFormDef 单定义
     * @return 结果
     */
    public AjaxResult updateProcFormDef(ProcFormDef procFormDef);

    /**
     * 绑定流程
     *
     * @param procFormDef 单定义
     * @return 结果
     */
    public AjaxResult bindProcess(ProcFormDef procFormDef);

    /**
     * 批量删除单定义
     *
     * @param ids 需要删除的单定义ID
     * @return 结果
     */
    public int deleteProcFormDefByIds(String[] ids);

    /**
     * 删除单定义信息
     *
     * @param id 单定义ID
     * @return 结果
     */
    public int deleteProcFormDefById(String id);

    /**
     * 新增单定义并返回id
     *
     * @param procFormDef 单定义
     * @return 结果
     */
    Map<String, Object> insertProcFormDefReturnId(ProcFormDef procFormDef);

    List<Map<String, Object>> getSelectFieldData(String formId);
}
