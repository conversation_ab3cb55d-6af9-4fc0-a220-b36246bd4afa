package com.ruoyi.nocode.service.impl;

import com.ruoyi.nocode.domain.vo.DaArchivistFilesVo;
import com.ruoyi.nocode.mapper.DDaArchivistFilesMapper;
import com.ruoyi.nocode.service.IDDaArchivistFilesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 【终稿扫描件】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-08
 */
@Service
public class DDaArchivistFilesServiceImpl implements IDDaArchivistFilesService {
    @Autowired
    private DDaArchivistFilesMapper daArchivistFilesMapper;

    /**
     * 查询
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    /*@Override
    public DaArchivistFiles selectDaArchivistFilesById(Long id) {
        return daArchivistFilesMapper.selectDaArchivistFilesById(id);
    }*/

    /**
     * 查询列表
     *
     * @param daArchivistFiles 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    /*@Override
    public List<DaArchivistFiles> selectDaArchivistFilesList(DaArchivistFiles daArchivistFiles) {
        return daArchivistFilesMapper.selectDaArchivistFilesList(daArchivistFiles);
    }
*/
    /**
     * 新增
     *
     * @param daArchivistFiles 【请填写功能名称】
     * @return 结果
     */
    /*@Override
    public int insertDaArchivistFiles(DaArchivistFiles daArchivistFiles) {
        daArchivistFiles.setCreateTime(DateUtils.getNowDate());
        daArchivistFiles.setCreateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        int i = daArchivistFiles.getFileName().indexOf(".");
        String extension = daArchivistFiles.getFileName().substring(i);
        daArchivistFiles.setExtension(extension);//扩展名
        return daArchivistFilesMapper.insertDaArchivistFiles(daArchivistFiles);
    }*/

    /**
     * 修改
     *
     * @param daArchivistFiles
     * @return 结果
     */
    /*@Override
    public int updateDaArchivistFiles(DaArchivistFiles daArchivistFiles) {
        return daArchivistFilesMapper.updateDaArchivistFiles(daArchivistFiles);
    }*/

    /**
     * 批量删除
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    /*@Override
    public int deleteDaArchivistFilesByIds(Long[] ids) {
        return daArchivistFilesMapper.deleteDaArchivistFilesByIds(ids);
    }*/

    /**
     * 删除信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    /*@Override
     public int deleteDaArchivistFilesById(Long id) {
        return daArchivistFilesMapper.deleteDaArchivistFilesById(id);
    }*/

    /**
     * 根据流程id查询终稿扫描件
     *
     * @param pertainFlowId
     * @return
     */
    @Override
    public List<DaArchivistFilesVo> selectDaArchivistFilesListByFlowId(String pertainFlowId) {
        return daArchivistFilesMapper.selectDaArchivistFilesListByFlowId(pertainFlowId);
    }
}