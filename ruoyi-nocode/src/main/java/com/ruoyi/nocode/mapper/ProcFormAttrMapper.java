package com.ruoyi.nocode.mapper;

import java.util.List;
import com.ruoyi.nocode.domain.ProcFormAttr;

/**
 * 表单属性Mapper接口
 *
 * @date 2022-08-09
 */
public interface ProcFormAttrMapper
{
    /**
     * 查询表单属性
     *
     * @param id 表单属性主键
     * @return 表单属性
     */
    public ProcFormAttr selectProcFormAttrById(String id);

    /**
     * 查询表单属性列表
     *
     * @param procFormAttr 表单属性
     * @return 表单属性集合
     */
    public List<ProcFormAttr> selectProcFormAttrList(ProcFormAttr procFormAttr);

    public List<ProcFormAttr> selectShowAttrListByFormId(String formId);

    /**
     * 新增表单属性
     *
     * @param procFormAttr 表单属性
     * @return 结果
     */
    public int insertProcFormAttr(ProcFormAttr procFormAttr);

    /**
     * 修改表单属性
     *
     * @param procFormAttr 表单属性
     * @return 结果
     */
    public int updateProcFormAttr(ProcFormAttr procFormAttr);

    /**
     * 删除表单属性
     *
     * @param id 表单属性主键
     * @return 结果
     */
    public int deleteProcFormAttrById(String id);

    /**
     * 批量删除表单属性
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProcFormAttrByIds(String[] ids);

    public int deleteProcFormAttrByFormId(String formId);

    public int deleteProcFormAttrByFormIds(String[] formIds);
}
