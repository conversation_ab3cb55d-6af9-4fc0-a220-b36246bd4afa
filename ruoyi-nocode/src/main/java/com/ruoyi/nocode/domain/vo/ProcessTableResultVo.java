package com.ruoyi.nocode.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProcessTableResultVo implements Serializable {

    //节点名称
    private String nodesName;

    //默认处理人
    private String  assignee;

    //实际处理人
    private String detailName;

    //流转方式
    private String flowType;

    //流向
    private String outgoing;

    //处理状态
    private String status;

    //节点类型：userTask;exclusiveGateway
    private String nodeType;

    //节点ID
    private String nodesId;

    //流入方向
    private List<String> incomingFlows;


}
