package com.ruoyi.nocode.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 流程信息修改对象
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class FlowInfoDTO {
    private static final long serialVersionUID = 1L;

    private String oid;

    private String businessId;

    private String theme;

    private String status;

    private String data;

    private String urgency;

    private String currentStatus;

    private String formName;

    private String procKey;

    private String withProc;

    private String instanceId;

    private Date updateTime;

    private Long processNumber;

    private String flowFullId;

    private String formId;

    private String nextFlowApproveUserName;

    private String nextNodeId;

    //是否自定义凭证生成时间
    private Boolean customFlag;

    //凭证自定义生成时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date voucherDate;
}
