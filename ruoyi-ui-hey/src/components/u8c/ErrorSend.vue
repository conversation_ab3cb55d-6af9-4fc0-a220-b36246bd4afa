<template>
  <div>
    <el-dialog
      title="提示"
      :visible.sync="showDialog"
      width="1200"
      append-to-body
      :before-close="handleCancel"
    >
      <div style="display: flex; align-items: center">
        <i
          style="color: #f59a23; font-size: 24px; margin-right: 15px"
          class="h-icon-warn"
        ></i>
        <div>
          <div v-for="item in tableData[0].failureMsgList" :key="item">
            {{ item }}
          </div>
        </div>
      </div>
      <div style="margin-top: 12px; font-size: 15px; font-weight: bold">
        出现问题的凭证：
      </div>
      <div class="h-panel-body" style="padding: 0; margin-top: 16px">
        <table class="header">
          <tr>
            <th style="width: 50px">
              <input style="opacity: 0" type="checkbox" />
            </th>
            <td style="width: 215px">摘要</td>
            <td>科目</td>
            <td align="right" style="width: 130px">借方金额</td>
            <td align="right" style="width: 130px">贷方金额</td>
          </tr>
        </table>
        <table v-if="!datas.length">
          <tr>
            <td colspan="5" class="text-center padding">暂无数据</td>
          </tr>
        </table>
        <table class="details" v-for="data in tableData" :key="data.id">
          <tr class="details-header">
            <th style="width: 50px">
              <input
                style="opacity: 0"
                :class="{ display: data._checked }"
                v-model="data._checked"
                type="checkbox"
              />
            </th>
            <td colspan="2">
              日期：{{ data.voucherDate }} 凭证字号：{{ data.word }}-{{
                data.code
              }}
              状态：<span v-if="!data.valid" style="color: red">已作废</span
              ><span v-if="data.valid">{{
                data.auditMemberId ? "已审核" : "待审核"
              }}</span>
            </td>
            <td colspan="2" class="actions" align="right">
              <router-link
                tag="span"
                :to="{
                  name: 'VoucherForm',
                  params: { voucherId: data.id, showDetail: true },
                }"
                >查看</router-link
              >
            </td>
          </tr>
          <tr
            v-for="d in data.voucherDetails"
            :key="d.id"
            :class="{ 'un-valid': !data.valid }"
          >
            <th></th>
            <td style="width: 215px">
              <!-- <input style="width: 250px" v-model="d.summary" type="text" /> -->
              {{ d.summary }}
              <template v-if="d.subject && d.num && d.price">
                (数量:{{ d.num
                }}<span class="dark4-color">{{ d.subject.unit }}</span
                >，单价:{{ d.price }}<span class="dark4-color">元</span>)
              </template>
            </td>
            <td>{{ d.subjectName }}</td>
            <td align="right" style="width: 130px">
              {{ d.debitAmount | numFormat }}
            </td>
            <td align="right" style="width: 130px">
              {{ d.creditAmount | numFormat }}
            </td>
          </tr>
          <tr class="font-bold" :class="{ 'un-valid': !data.valid }">
            <td></td>
            <td>合计</td>
            <td>{{ data.debitAmount | dxMoney }}</td>
            <td align="right">{{ data.debitAmount | numFormat }}</td>
            <td align="right">{{ data.creditAmount | numFormat }}</td>
          </tr>
        </table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
    
    <script>
import Dialog from "./Dialog.vue";
export default {
  components: {
    Dialog,
  },
  props: {
    datas: Array,
  },
  data() {
    return {
      tableData: [],
      params: {
        date: "",
      },
      showDialog: true,
    };
  },
  computed: {},
  mounted() {
    this.tableData = JSON.parse(JSON.stringify(this.datas));
  },
  methods: {
    handleCancel() {
      this.$emit("close");
    },
    handleOk() {},
  },
};
</script>
    
    <style lang="less" scoped>
::deep .h-table td,
.h-table th {
  font-size: 15px !important;
}
.search {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .item {
    margin-right: 20px;

    display: flex;
    align-items: center;
    span {
      margin-right: 5px;
    }
  }
}
.h-panel-body {
  table {
    width: 100%;
    border-collapse: collapse;

    td {
      padding: 7px;
    }

    &.header {
      background-color: @primary-color;
      color: white;
    }
  }

  .details {
    font-size: 12px;
    margin: 15px 0;
    border: 1px solid @gray2-color;

    .actions {
      text-align: right;
      padding-right: 20px;

      span,
      a {
        display: none;
      }
    }

    input {
      &.display {
        display: inline-block;
      }
    }

    &-header {
      background-color: @gray3-color;
      color: @dark3-color;
    }

    td,
    th {
      border-bottom: 1px solid @gray2-color;
    }

    tr:hover:not(.details-header) {
      background-color: #dff7df;
      cursor: pointer;
    }

    &:hover {
      box-shadow: 0 0 10px 0 #dadada;
      border-color: #dadada;

      .actions {
        span,
        a {
          display: inline-block;
        }
      }

      input {
        display: inline-block;
      }
    }
  }
}
</style>