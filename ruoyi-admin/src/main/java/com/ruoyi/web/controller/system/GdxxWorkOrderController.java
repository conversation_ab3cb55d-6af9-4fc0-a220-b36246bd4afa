package com.ruoyi.web.controller.system;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.GdxxWorkOrderExport;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.mapper.GdxxWorkOrderMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.system.domain.GdxxWorkOrder;
import com.ruoyi.system.service.IGdxxWorkOrderService;
import com.ruoyi.common.core.domain.AjaxResult;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/system/workOrder")
public class GdxxWorkOrderController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(GdxxWorkOrderController.class);
    public static final SimpleDateFormat FORMAT = new SimpleDateFormat("yyyyy-MM-dd");

    @Autowired
    private IGdxxWorkOrderService workOrderService;
    @Autowired
    private GdxxWorkOrderMapper gdxxWorkOrderMapper;

    /**
     * 查询主表工单详情
     */
    @PreAuthorize("@ss.hasAnyPermi('myWorkOrder:edit,workOrder:edit,joinWorkOrder:edit,myWorkOrder:queryDetails,workOrder:queryDetails,joinWorkOrder:queryDetails')")
    @GetMapping("/newDetail/{id}")
    public GdxxWorkOrderDetailVO getWorkOrderDetail(@PathVariable("id") Long id) {
        log.info("查询主表工单详情, id: {}", id);
        return workOrderService.getWorkOrderDetail(id);
    }

    /**
     * 查询历史表工单详情
     */
    @PreAuthorize("@ss.hasAnyPermi('myWorkOrder:edit,workOrder:edit,joinWorkOrder:edit,myWorkOrder:queryDetails,workOrder:queryDetails,joinWorkOrder:queryDetails')")
    @GetMapping("/historyDetail/{id}")
    public GdxxWorkOrderHistoryDetailVO getWorkOrderHistoryDetail(@PathVariable("id") Long id) {
        log.info("查询历史表工单详情, id: {}", id);
        return workOrderService.getWorkOrderHistoryDetail(id);
    }

    /**
     * 查询工单列表
     */
    @PreAuthorize("@ss.hasAnyPermi('workOrder:listHd,myWorkOrder:list,joinWorkOrder:list')")
    @GetMapping("/detailList")
    public TableDataInfo getWorkOrderDetailList(GdxxWorkOrderQueryVo gdxxWorkOrderQueryVo) {
        log.info("查询工单列表, 查询条件: {}", JSON.toJSONString(gdxxWorkOrderQueryVo));
        GdxxWorkOrder query = new GdxxWorkOrder();
        BeanUtils.copyProperties(gdxxWorkOrderQueryVo, query);
        if (gdxxWorkOrderQueryVo != null && StringUtils.isNotEmpty(gdxxWorkOrderQueryVo.getRequirementSubmissionTime())) {
            try {
                query.setRequirementSubmissionTime(FORMAT.parse(gdxxWorkOrderQueryVo.getRequirementSubmissionTime()));
            } catch (ParseException e) {
                log.error("需求时间传输格式有误。", JSON.toJSONString(gdxxWorkOrderQueryVo));
                throw new RuntimeException("需求时间传输格式有误。");
            }
        }
        Set<Long> orderIdsNotCaoGao = workOrderService.getWorkOrderIds(query);
        Set<Long> orderIdsInCaoGao = workOrderService.getWorkOrderIdsInCaoGao(query);
        orderIdsNotCaoGao.addAll(orderIdsInCaoGao);
        if (CollectionUtil.isEmpty(orderIdsNotCaoGao)) {
            return getDataTableForTotal(null, 0);
        }
        Long l = gdxxWorkOrderMapper.selectGdxxWorkOrderListByIdsCount(orderIdsNotCaoGao);
        return getDataTableForTotal(workOrderService.getWorkOrderDetailList(orderIdsNotCaoGao,true), l);
    }

    /**
     * 创建工单
     */
    @PreAuthorize("@ss.hasAnyPermi('myWorkOrder:add,joinWorkOrder:add,workOrder:add,myWorkOrder:editStatusTjys,workOrder:editStatusTjys,joinWorkOrder:editStatusTjys')")
    @PostMapping("/create")
    public AjaxResult createWorkOrder(
            GdxxWorkOrderCreateVO gdxxWorkOrderCreateVO,
            @RequestPart(value = "files", required = false) MultipartFile[] files) {
        log.info("创建工单, 工单信息: {}", JSON.toJSONString(gdxxWorkOrderCreateVO));
        if (files != null && files.length > 0) {
            log.info("创建工单上传文件数量: {}, 文件大小: {} bytes", files.length, 
                Arrays.stream(files).mapToLong(MultipartFile::getSize).sum());
        }
        return workOrderService.createWorkOrder(gdxxWorkOrderCreateVO, files);
    }

    /**
     * 修改工单
     */
    @PreAuthorize("@ss.hasAnyPermi('myWorkOrder:edit,workOrder:edit,joinWorkOrder:edit,myWorkOrder:editStatusTjys,workOrder:editStatusTjys,joinWorkOrder:editStatusTjys,myWorkOrder:editStatusSl,workOrder:editStatusSl,joinWorkOrder:editStatusSl," +
            "myWorkOrder:editStatusYstg,workOrder:editStatusYstg,joinWorkOrder:editStatusYstg,myWorkOrder:editStatusBjcg,workOrder:editStatusBjcg,joinWorkOrder:editStatusBjcg,myWorkOrder:delete,workOrder:delete,joinWorkOrder:delete')")
    @PostMapping("/update")
    public AjaxResult updateWorkOrder(GdxxWorkOrderUpdateVO gdxxWorkOrderUpdateVO, @RequestPart(value = "files", required = false) MultipartFile[] files) {
        log.info("修改工单, 工单信息: {}", JSON.toJSONString(gdxxWorkOrderUpdateVO));
        if (files != null && files.length > 0) {
            log.info("修改工单上传文件数量: {}, 文件大小: {} bytes", files.length, 
                Arrays.stream(files).mapToLong(MultipartFile::getSize).sum());
        }
        if (gdxxWorkOrderUpdateVO != null && StringUtils.isNotEmpty(gdxxWorkOrderUpdateVO.getRequirementSubmissionTimeStr())) {
            gdxxWorkOrderUpdateVO.setRequirementSubmissionTime(DateUtils.dateTime("yyyy-MM-dd HH:mm:ss", gdxxWorkOrderUpdateVO.getRequirementSubmissionTimeStr()));
        }
        return workOrderService.updateWorkOrder(gdxxWorkOrderUpdateVO, files);
    }

    /**
     * 修改工单状态
     */
    @PreAuthorize("@ss.hasAnyPermi('myWorkOrder:editStatusXqwc,workOrder:editStatusXqwc,joinWorkOrder:editStatusXqwc,myWorkOrder:editStatusQxxq,workOrder:editStatusQxxq,joinWorkOrder:editStatusQxxq,myWorkOrder:editStatusZtxq,workOrder:editStatusZtxq,joinWorkOrder:editStatusZtxq" +
            "myWorkOrder:editStatusHfzt,workOrder:editStatusHfzt,joinWorkOrder:editStatusHfzt')")
    @PostMapping("/updateStatus")
    public AjaxResult updateWorkOrderStatusAndAddDynamic(@RequestParam("workOrderId") Long workOrderId,
                                                         @RequestParam("dynamicType") String dynamicType) {
        log.info("修改工单状态, workOrderId: {}, dynamicType: {}", workOrderId, dynamicType);
        return workOrderService.updateWorkOrderStatusAndAddDynamic(workOrderId, dynamicType);
    }

    /**
     * 查询工单动态列表
     */
    @PreAuthorize("@ss.hasAnyPermi('myWorkOrder:dongtaiList,workOrder:dongtaiList,joinWorkOrder:dongtaiList')")
    @GetMapping("/dynamic/{workOrderId}")
    public AjaxResult getWorkOrderDynamicList(@PathVariable("workOrderId") Long workOrderId) {
        log.info("查询工单动态列表, workOrderId: {}", workOrderId);
        return workOrderService.getWorkOrderDynamicList(workOrderId);
    }

    /**
     * 发布工单动态
     */
    @PreAuthorize("@ss.hasAnyPermi('myWorkOrder:sendDynamics,workOrder:sendDynamics,joinWorkOrder:sendDynamics')")
    @PostMapping("/dynamic/publish")
    public AjaxResult publishDynamic(
            GdxxWorkOrderDynamicAddVO gdxxWorkOrderDynamicAddVO,
            @RequestPart(value = "files", required = false) MultipartFile[] files) {
        log.info("发布工单动态, 动态信息: {}", JSON.toJSONString(gdxxWorkOrderDynamicAddVO));
        if (files != null && files.length > 0) {
            log.info("发布工单动态上传文件数量: {}, 文件大小: {} bytes", files.length, 
                Arrays.stream(files).mapToLong(MultipartFile::getSize).sum());
        }
        return workOrderService.publishDynamic(gdxxWorkOrderDynamicAddVO, files);
    }

    /**
     * 导出工单列表
     */
    @PreAuthorize("@ss.hasAnyPermi('myWorkOrder:export,workOrder:export,joinWorkOrder:export')")
    @GetMapping("/export")
    public void exportWorkOrderList(GdxxWorkOrderQueryVo gdxxWorkOrderQueryVo, HttpServletResponse response) {
        log.info("导出工单列表, 查询条件: {}", JSON.toJSONString(gdxxWorkOrderQueryVo));
        List<GdxxWorkOrderExport> list = workOrderService.getExportInfo(gdxxWorkOrderQueryVo);
        ExcelUtil<GdxxWorkOrderExport> util = new ExcelUtil<GdxxWorkOrderExport>(GdxxWorkOrderExport.class);
        util.exportExcel(response, list, "工单数据");
    }
}