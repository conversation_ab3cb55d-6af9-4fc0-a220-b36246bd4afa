package com.ruoyi.web.secret;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.utils.encryption.AESUtils;
import com.ruoyi.common.utils.encryption.RSAUtils;
import com.ruoyi.framework.web.service.EncryptService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.ruoyi.core.domain.DSecretKey;
import org.ruoyi.core.mapper.DSecretKeyMapper;
import org.ruoyi.core.service.DSecretKeyService;
import org.ruoyi.core.service.impl.DSecretKeyServiceImpl;
import org.ruoyi.core.tool.DataRequestTool;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @className: PrivateSecretTest
 * @author: zuo
 * @description: 私钥创建和加密的测试单例
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class PrivateSecretTest {

    @Resource
    private DSecretKeyMapper dSecretKeyMapper;

    @Test
    public void insertBranch() {
        // 注意联合唯一索引
        List<DSecretKey> list = new ArrayList<>();

        DSecretKey dSecretKey = new DSecretKey();
        dSecretKey.setPlatformNo("consumer_finance");
        dSecretKey.setUseType("1");
        dSecretKey.setKeyType("symmetrical_encryption");
        dSecretKey.setKeyAlgorithm("RSA");
        dSecretKey.setKeySignatureAlgorithm("双方使用");
        dSecretKey.setKeyLength(String.valueOf(233));
        dSecretKey.setKeyCharset("UTF-8");
        dSecretKey.setPrivateCustName1("国美电器");
        dSecretKey.setPrivateSysName1("电销平台");
        dSecretKey.setPrivateKey(String.valueOf(2 * 123789137 + 1371283));
        dSecretKey.setPublicCustName2("中信银行");
        dSecretKey.setPublicSysName2("数据平台");
        dSecretKey.setPublicKey(String.valueOf(2 * 123789137 + 137));
        dSecretKey.setRemark("双方使用");
        dSecretKey.setStatus("0");
        list.add(dSecretKey);

        dSecretKeyMapper.insertBatch(list);
    }


    @Test
    public void encrypt() throws ParseException, InterruptedException {
        Date dateOne = new Date();
        Thread.sleep(11231L);
        Date dateTwo = new Date();
        int i = dateOne.compareTo(dateTwo);
        System.out.println(i);

    }

    @Resource
    private DSecretKeyService dSecretKeyService;

    @Test
    public void get() throws NoSuchAlgorithmException, InvalidKeySpecException {

        DSecretKey dSecretKey = dSecretKeyService.queryById(228);

        String key = AESUtils.initSecretKey(DSecretKeyServiceImpl.AES_INTI_KEY_SIZE);//获取生成的秘钥（经过base64编码）

        System.out.println(dSecretKey.getPrivateKey());
        RSAPrivateKey privateKey1 = RSAUtils.getPrivateKey(dSecretKey.getPrivateKey().trim());
        RSAPublicKey publicKey = RSAUtils.getPublicKey(dSecretKey.getPublicKey());

        String s = RSAUtils.privateEncrypt(key, privateKey1, "UTF-8");
        String s1 = RSAUtils.publicDecrypt(s, publicKey);

        System.out.println(key.equals(s1));
    }


    @Resource
    private EncryptService encryptService;

    @Test
    public void testEncrypt() {
        String text = "woshi测试数据。。..";
        Map<String, Object> map = encryptService.commonToEncrypt("XFJR001:0", "001", text);
        System.out.println(JSONObject.toJSONString(map));
        JSONObject jsonObject = new JSONObject(map).getJSONObject("data");

        System.out.println("加密AES秘钥 ======" + jsonObject.getString("encryptAESKey"));
        System.out.println("加密好的数据 ======" + jsonObject.getString("encryptData"));
        System.out.println("签名 ======" + jsonObject.getString("sign1"));
        System.out.println("加密好的数据 ======" + jsonObject.getString("sign2"));
        System.out.println("加密好的数据 ======" + jsonObject.getString("sign3"));
        Map<String, Object> map1 = encryptService.commonToDecrypt("XFJR001:0", "001", jsonObject.getString("encryptData"), jsonObject.getString("sign1"), jsonObject.getString("sign2"), jsonObject.getString("sign3"), jsonObject.getString("encryptAESKey"));
        System.out.println(JSONObject.toJSONString(map1));

    }


    /**
     * {"msg":"成功","code":"00000","data":{"encryptData":"cYeTY2kMBVSSsl1DAGwPTg==","push_code":"001","encryptAESKey":"k62CfYMCb_AxWgi3E0f3Ig=="}}
     */
    @Test
    public void encryptSecretBySymmetricTest() {
        Map<String, Object> map = encryptService.commonToEncrypt("XFJR001:0", "001", "测试数据");
        System.out.println(JSONObject.toJSONString(map));
    }


    /**
     * msg  -->  成功
     * code  -->  00000
     * pushCode  -->  001
     * data  -->  测试数据
     * {"msg":"成功","code":"00000","pushCode":"001","data":"测试数据"}
     */
    @Test
    public void decryptSecretBySymmetricTest() {

        String json = "{\"msg\":\"成功\",\"code\":\"00000\",\"data\":{\"encryptData\":\"cYeTY2kMBVSSsl1DAGwPTg==\",\"push_code\":\"001\",\"encryptAESKey\":\"k62CfYMCb_AxWgi3E0f3Ig==\"}}\n";
        JSONObject jsonObject = JSONObject.parseObject(json);
        JSONObject data = jsonObject.getJSONObject("data");

        Map<String, Object> map = encryptService.commonToDecrypt("XFJR001:0", "001", data.getString("encryptData"), null, null, null, null);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            System.out.println(entry.getKey() + "  -->  " + entry.getValue());

        }

        System.out.println(JSONObject.toJSONString(map));
    }


    /**
     * {"msg":"成功","code":"00000","data":{"sign2":"MEUCIFWleaqQRyTbJNdM9SGIgJzS771jbYmWny3zM9ltgHpGAiEA39ssbqWELmJ7vA3NlJENKo4CBmB74uoXrLFFAXIOT4Y=","sign1":"MEUCIHxcrAO-x0iEOzBi7d824u-8jSifVdgUFZ95kmJyk462AiEA6y6hitg73_wHc8TNGMaRks3J7jn_cny1Hl0ZEwG_IAM=","sign3":"MEQCHxlxDE22-fAVGGSih5MBhBJ0uGW2cnL86eOTq9blsTUCIQCqSKAj5Rxssie9nFPL3kjD17XFOoAzjTnrDA7VH2GAOg==","encryptData":"2eq-iMaRv2IP5QB5Nruvvw==","push_code":"001","encryptAESKey":"BJJW19OX5fM5EyOmCp908gQMtotYFtGKEfnFx2wkg6SOgkkFafaqXay-HIMQa9WTy3TJGVIPx6Risb2uUqjDkUj1ndbZaOs8_yNRo4HTGlUQf1jOWlxc8hERyMSzjGnEH0-wF6U-v0E66lFdodciol1CpBqNXMKiFw=="}}
     */
    @Test
    public void encryptSecretByNoSymmetricTest() {
        Map<String, Object> map = encryptService.commonToEncrypt("DZBH001:0", "001", "测试数据");
        System.out.println(JSONObject.toJSONString(map));
    }


    /**
     * msg  -->  成功
     * code  -->  00000
     * data  -->  测试数据
     * {"msg":"成功","code":"00000","data":"测试数据"}
     */
    @Test
    public void decryptSecretByNoSymmetricTest() {

        String json = "{\"msg\":\"成功\",\"code\":\"00000\",\"data\":{\"sign2\":\"MEUCIFWleaqQRyTbJNdM9SGIgJzS771jbYmWny3zM9ltgHpGAiEA39ssbqWELmJ7vA3NlJENKo4CBmB74uoXrLFFAXIOT4Y=\",\"sign1\":\"MEUCIHxcrAO-x0iEOzBi7d824u-8jSifVdgUFZ95kmJyk462AiEA6y6hitg73_wHc8TNGMaRks3J7jn_cny1Hl0ZEwG_IAM=\",\"sign3\":\"MEQCHxlxDE22-fAVGGSih5MBhBJ0uGW2cnL86eOTq9blsTUCIQCqSKAj5Rxssie9nFPL3kjD17XFOoAzjTnrDA7VH2GAOg==\",\"encryptData\":\"2eq-iMaRv2IP5QB5Nruvvw==\",\"push_code\":\"001\",\"encryptAESKey\":\"BJJW19OX5fM5EyOmCp908gQMtotYFtGKEfnFx2wkg6SOgkkFafaqXay-HIMQa9WTy3TJGVIPx6Risb2uUqjDkUj1ndbZaOs8_yNRo4HTGlUQf1jOWlxc8hERyMSzjGnEH0-wF6U-v0E66lFdodciol1CpBqNXMKiFw==\"}}\n";

        JSONObject jsonObject = JSONObject.parseObject(json);
        JSONObject data = jsonObject.getJSONObject("data");

        Map<String, Object> map = encryptService.commonToDecrypt("DZBH001:0", "001", data.getString("encryptData"), data.getString("sign1"), data.getString("sign2"), data.getString("sign3"), data.getString("encryptAESKey"));
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            System.out.println(entry.getKey() + "  -->  " + entry.getValue());

        }

        System.out.println(JSONObject.toJSONString(map));
    }

    /**
     * 消金系统测试
     * <p>
     * 结果：{
     * "msg":"成功",
     * "code":"00000",
     * "pushCode":"001",
     * "data":"[{"pk":179429,"product_no":"GM5551","date_stat":"2021-08-15","loan_cnt":0,"loan_amt":0.00,"loan_contract_amt":0.00,"loan_fee1_amt":0.00,"loan_fee2_amt":0.00,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":0.00,"fund_balance_amt":0.00,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":0.00,"overdue_total_amt":0.00,"repay_cnt":0,"repay_total_amt":0.00,"repay_prin_amt":0.00,"repay_int_amt":0.00,"repay_oint_amt":0.00,"repay_fee1_amt":0.00,"repay_fee2_amt":0.00,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":0,"accum_loan_amt":0.00,"accum_loan_contract_amt":0.00,"accum_loan_fee1_amt":0.00,"accum_loan_fee2_amt":0.00,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":0,"accum_repay_total_amt":0.00,"accum_repay_prin_amt":0.00,"accum_repay_int_amt":0.00,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":0.00,"accum_repay_fee2_amt":0.00,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":0.00,"reduce_service_amt":0.00,"reduce_guarantee_amt":0.00,"create_time":1628586665000,"update_time":1628586665000},{"pk":179434,"product_no":"GM5551","date_stat":"2021-11-25","loan_cnt":1,"loan_amt":2450.00,"loan_contract_amt":2450.00,"loan_fee1_amt":130.71,"loan_fee2_amt":3.57,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":2450.00,"fund_balance_amt":2450.00,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":2640.87,"overdue_total_amt":0.00,"repay_cnt":0,"repay_total_amt":0.00,"repay_prin_amt":0.00,"repay_int_amt":0.00,"repay_oint_amt":0.00,"repay_fee1_amt":0.00,"repay_fee2_amt":0.00,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":1,"accum_loan_amt":2450.00,"accum_loan_contract_amt":2450.00,"accum_loan_fee1_amt":130.71,"accum_loan_fee2_amt":3.57,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":0,"accum_repay_total_amt":0.00,"accum_repay_prin_amt":0.00,"accum_repay_int_amt":0.00,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":0.00,"accum_repay_fee2_amt":0.00,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":0.00,"reduce_service_amt":0.00,"reduce_guarantee_amt":0.00,"create_time":1628586774000,"update_time":1628586776000},{"pk":179439,"product_no":"GM5551","date_stat":"2021-12-25","loan_cnt":1,"loan_amt":2450.00,"loan_contract_amt":2450.00,"loan_fee1_amt":130.51,"loan_fee2_amt":3.59,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":3693.20,"fund_balance_amt":3693.20,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":4401.45,"overdue_total_amt":0.00,"repay_cnt":0,"repay_total_amt":0.00,"repay_prin_amt":0.00,"repay_int_amt":0.00,"repay_oint_amt":0.00,"repay_fee1_amt":0.00,"repay_fee2_amt":0.00,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":2,"accum_loan_amt":4900.00,"accum_loan_contract_amt":4900.00,"accum_loan_fee1_amt":261.22,"accum_loan_fee2_amt":7.16,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":0,"accum_repay_total_amt":0.00,"accum_repay_prin_amt":0.00,"accum_repay_int_amt":0.00,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":0.00,"accum_repay_fee2_amt":0.00,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":0.00,"reduce_service_amt":0.00,"reduce_guarantee_amt":0.00,"create_time":1628586859000,"update_time":1628586860000},{"pk":179444,"product_no":"GM5551","date_stat":"2021-08-09","loan_cnt":0,"loan_amt":0.00,"loan_contract_amt":0.00,"loan_fee1_amt":0.00,"loan_fee2_amt":0.00,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":0.00,"fund_balance_amt":0.00,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":0.00,"overdue_total_amt":0.00,"repay_cnt":1,"repay_total_amt":401.72,"repay_prin_amt":401.72,"repay_int_amt":0.00,"repay_oint_amt":0.00,"repay_fee1_amt":0.00,"repay_fee2_amt":0.00,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":0,"accum_loan_amt":0.00,"accum_loan_contract_amt":0.00,"accum_loan_fee1_amt":0.00,"accum_loan_fee2_amt":0.00,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":1,"accum_repay_total_amt":401.72,"accum_repay_prin_amt":401.72,"accum_repay_int_amt":0.00,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":0.00,"accum_repay_fee2_amt":0.00,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":15.95,"reduce_service_amt":21.46,"reduce_guarantee_amt":1.01,"create_time":1628587057000,"update_time":1628587258000},{"pk":179445,"product_no":"GM5551","date_stat":"2021-08-10","loan_cnt":0,"loan_amt":0.00,"loan_contract_amt":0.00,"loan_fee1_amt":0.00,"loan_fee2_amt":0.00,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":0.00,"fund_balance_amt":0.00,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":0.00,"overdue_total_amt":0.00,"repay_cnt":1,"repay_total_amt":420.15,"repay_prin_amt":401.19,"repay_int_amt":16.15,"repay_oint_amt":0.00,"repay_fee1_amt":3.54,"repay_fee2_amt":2.08,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":0,"accum_loan_amt":0.00,"accum_loan_contract_amt":0.00,"accum_loan_fee1_amt":0.00,"accum_loan_fee2_amt":0.00,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":2,"accum_repay_total_amt":821.87,"accum_repay_prin_amt":802.91,"accum_repay_int_amt":16.15,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":1.77,"accum_repay_fee2_amt":1.04,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":0.33,"reduce_service_amt":19.66,"reduce_guarantee_amt":0.00,"create_time":1628587057000,"update_time":1628587258000},{"pk":179452,"product_no":"GM5551","date_stat":"2021-07-15","loan_cnt":0,"loan_amt":0.00,"loan_contract_amt":0.00,"loan_fee1_amt":0.00,"loan_fee2_amt":0.00,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":0.00,"fund_balance_amt":0.00,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":0.00,"overdue_total_amt":0.00,"repay_cnt":0,"repay_total_amt":0.00,"repay_prin_amt":0.00,"repay_int_amt":0.00,"repay_oint_amt":0.00,"repay_fee1_amt":0.00,"repay_fee2_amt":0.00,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":0,"accum_loan_amt":0.00,"accum_loan_contract_amt":0.00,"accum_loan_fee1_amt":0.00,"accum_loan_fee2_amt":0.00,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":0,"accum_repay_total_amt":0.00,"accum_repay_prin_amt":0.00,"accum_repay_int_amt":0.00,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":0.00,"accum_repay_fee2_amt":0.00,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":0.00,"reduce_service_amt":0.00,"reduce_guarantee_amt":0.00,"create_time":1628589359000,"update_time":1628589359000},{"pk":179457,"product_no":"GM5551","date_stat":"2022-01-02","loan_cnt":1,"loan_amt":2450.00,"loan_contract_amt":2450.00,"loan_fee1_amt":131.00,"loan_fee2_amt":3.56,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":5742.01,"fund_balance_amt":5742.01,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":6602.18,"overdue_total_amt":0.00,"repay_cnt":0,"repay_total_amt":0.00,"repay_prin_amt":0.00,"repay_int_amt":0.00,"repay_oint_amt":0.00,"repay_fee1_amt":0.00,"repay_fee2_amt":0.00,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":3,"accum_loan_amt":7350.00,"accum_loan_contract_amt":7350.00,"accum_loan_fee1_amt":392.22,"accum_loan_fee2_amt":10.72,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":0,"accum_repay_total_amt":0.00,"accum_repay_prin_amt":0.00,"accum_repay_int_amt":0.00,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":0.00,"accum_repay_fee2_amt":0.00,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":0.00,"reduce_service_amt":0.00,"reduce_guarantee_amt":0.00,"create_time":1628589410000,"update_time":1628589411000},{"pk":179462,"product_no":"GM5551","date_stat":"2022-02-05","loan_cnt":2,"loan_amt":4900.00,"loan_contract_amt":4900.00,"loan_fee1_amt":263.00,"loan_fee2_amt":7.06,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":617491184.13,"fund_balance_amt":617491184.13,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":680096545.00,"overdue_total_amt":533389949.37,"repay_cnt":0,"repay_total_amt":0.00,"repay_prin_amt":0.00,"repay_int_amt":0.00,"repay_oint_amt":0.00,"repay_fee1_amt":0.00,"repay_fee2_amt":0.00,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":5,"accum_loan_amt":12250.00,"accum_loan_contract_amt":12250.00,"accum_loan_fee1_amt":655.22,"accum_loan_fee2_amt":17.78,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":0,"accum_repay_total_amt":0.00,"accum_repay_prin_amt":0.00,"accum_repay_int_amt":0.00,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":0.00,"accum_repay_fee2_amt":0.00,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":0.00,"reduce_service_amt":0.00,"reduce_guarantee_amt":0.00,"create_time":1628589453000,"update_time":1648892394000},{"pk":179467,"product_no":"GM5551","date_stat":"2022-03-05","loan_cnt":0,"loan_amt":0.00,"loan_contract_amt":0.00,"loan_fee1_amt":0.00,"loan_fee2_amt":0.00,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":9836.45,"fund_balance_amt":9836.45,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":10563.52,"overdue_total_amt":1320.42,"repay_cnt":0,"repay_total_amt":0.00,"repay_prin_amt":0.00,"repay_int_amt":0.00,"repay_oint_amt":0.00,"repay_fee1_amt":0.00,"repay_fee2_amt":0.00,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":5,"accum_loan_amt":12250.00,"accum_loan_contract_amt":12250.00,"accum_loan_fee1_amt":655.22,"accum_loan_fee2_amt":17.78,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":0,"accum_repay_total_amt":0.00,"accum_repay_prin_amt":0.00,"accum_repay_int_amt":0.00,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":0.00,"accum_repay_fee2_amt":0.00,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":0.00,"reduce_service_amt":0.00,"reduce_guarantee_amt":0.00,"create_time":1628589469000,"update_time":1628589471000},{"pk":179472,"product_no":"DFQJYLQINJQB","date_stat":"2021-08-01","loan_cnt":0,"loan_amt":0.00,"loan_contract_amt":0.00,"loan_fee1_amt":0.00,"loan_fee2_amt":0.00,"loan_fee3_amt":0.00,"loan_fee4_amt":0.00,"plan_balance_amt":0.00,"fund_balance_amt":0.00,"compensate_total_amt":0.00,"compensate_total_prin_amt":0.00,"total_balance_amt":0.00,"overdue_total_amt":0.00,"repay_cnt":0,"repay_total_amt":0.00,"repay_prin_amt":0.00,"repay_int_amt":0.00,"repay_oint_amt":0.00,"repay_fee1_amt":0.00,"repay_fee2_amt":0.00,"repay_fee3_amt":0.00,"repay_fee4_amt":0.00,"repay_fee5_amt":0.00,"repay_fee6_amt":0.00,"accum_loan_cnt":0,"accum_loan_amt":0.00,"accum_loan_contract_amt":0.00,"accum_loan_fee1_amt":0.00,"accum_loan_fee2_amt":0.00,"accum_loan_fee3_amt":0.00,"accum_loan_fee4_amt":0.00,"day0_need_compensate":0.00,"day1_need_compensate":0.00,"day2_need_compensate":0.00,"accum_repay_cnt":0,"accum_repay_total_amt":0.00,"accum_repay_prin_amt":0.00,"accum_repay_int_amt":0.00,"accum_repay_oint_amt":0.00,"accum_repay_fee1_amt":0.00,"accum_repay_fee2_amt":0.00,"accum_repay_fee3_amt":0.00,"accum_repay_fee4_amt":0.00,"accum_repay_fee5_amt":0.00,"accum_repay_fee6_amt":0.00,"reduce_int_amt":0.00,"reduce_service_amt":0.00,"reduce_guarantee_amt":0.00,"create_time":1632289061000,"update_time":1632289061000}]"
     * }
     */
    @Test
    public void xjxtTest() throws JsonProcessingException {

        // String url = "http://192.168.110.137:11008/S2m/query";
        String url = "http://60.205.157.40:10082/S2m/query";
        String sql = "SELECT * FROM STS_LOAN_REPAY_DAY LIMIT 10";
        Map<String, Object> jsonRequest = encryptService.commonToEncrypt("XFJR001:1", "001", sql);

        Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(jsonRequest.get("data")), Map.class);

        Map<String, Object> result = DataRequestTool.toPostRequest(url, map, null);
        result = JSONObject.parseObject(JSONObject.toJSONString(result.get("data")), Map.class);

        String pushCode = String.valueOf(result.get("pushCode"));
        String encryptData = String.valueOf(result.get("encryptData"));
        String encryptAESKey = String.valueOf(result.get("encryptAESKey"));
        String sign1 = String.valueOf(result.get("sign1"));
        String sign2 = String.valueOf(result.get("sign2"));
        String sign3 = String.valueOf(result.get("sign3"));

        Map<String, Object> decryptMap = encryptService.commonToDecrypt("XFJR001:1", pushCode, encryptData, sign1, sign2, sign3, encryptAESKey);

        List<Map<String, Object>> list = JSONObject.parseObject(decryptMap.get("data").toString(), List.class);
        for (Map<String, Object> listMap : list) {
            for (Map.Entry<String, Object> entryList : listMap.entrySet()) {
                System.out.println(entryList.getKey() + " --- " + entryList.getValue());
            }
        }
        System.out.println(JSONObject.toJSONString(decryptMap));
    }
}
