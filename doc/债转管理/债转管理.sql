create schema debt_test collate utf8mb4_general_ci;

use debt_test;

create table dc_debt_conversion_file
(
    id                    bigint auto_increment comment '主键'
        primary key,
    cust_id               bigint                                null comment '担保公司',
    debt_conversion_theme varchar(64)                           null comment '债转主题',
    notice_launch_time    datetime                              null comment '债转通知发起时间',
    notice_complete_time  datetime                              null comment '债转通知完成时间',
    push_status           varchar(10) default '1'               null comment '推送状态 1.未推送 2.推送中 3.推送完成',
    create_by             varchar(64)                           null comment '创建者',
    create_time           datetime    default CURRENT_TIMESTAMP null comment '创建时间',
    update_by             varchar(64)                           null comment '更新人',
    update_time           datetime                              null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '债转文件表' row_format = DYNAMIC;
create table dc_debt_conversion
(
    id                   bigint auto_increment comment '主键'
        primary key,
    file_id              bigint                               null comment '关联上传文件',
    loan_code            varchar(100)                          null comment '借款申请编号',
    borrower             varchar(64)                          null comment '借款人',
    id_card              varchar(64)                          null comment '身份证号',
    phone_num            varchar(20)                          null comment '手机号',
    loan_time            datetime                             null comment '借款时间',
    guarantee_time       datetime                             null comment '担保时间',
    cust_id              bigint                               null comment '担保公司',
    partner_id           bigint                               null comment '资产方',
    fund_id              bigint                               null comment '资金方',
    loan_amount          decimal(18, 2)                       null comment '借款金额',
    debt_recipient_id    bigint                               null comment '债权接收方',
    debt_conversion_code varchar(20)                          null comment '债转通知编号',
    invoice_status       varchar(5) default '1'               null comment '申请开票状态 1.未申请 2.已申请',
    push_channel         varchar(5)                           null comment '推送渠道 1.小程序',
    is_read              varchar(5) default '1'               null comment '用户是否阅读  1.否 2.是 ',
    read_time            datetime                             null comment '阅读时间',
    create_by            varchar(64)                          null comment '创建者',
    create_time          datetime   default CURRENT_TIMESTAMP null comment '创建时间',
    update_by            varchar(64)                          null comment '更新人',
    update_time          datetime                             null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '债转通知明细表' row_format = DYNAMIC;

create table dc_invoicing_application_file
(
    id              bigint auto_increment comment '主键'
        primary key,
    main_body_id    bigint                             null comment '开票主体',
    invoicing_theme varchar(64)                        null comment '开票主题',
    create_by       varchar(64)                        null comment '创建者',
    create_time     datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by       varchar(64)                        null comment '更新人',
    update_time     datetime                           null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '开票申请文件表' row_format = DYNAMIC;

create table dc_invoicing_application
(
    id               bigint auto_increment comment '主键'
        primary key,
    file_id          bigint                             null comment '关联上传文件',
    loan_code        varchar(100)                        null comment '借款申请编号',
    borrower         varchar(64)                        null comment '借款人',
    phone_num        varchar(20)                        null comment '手机号',
    id_card          varchar(64)                        null comment '身份证号',
    invoicing_amount decimal(18, 2)                     null comment '申请开票金额(元)',
    fund_id          bigint                             null comment '资金方',
    receiving_email  varchar(500)                       null comment '接收邮箱',
    partner_id       bigint                             null comment '资产方',
    loan_amount      decimal(18, 2)                     null comment '借款金额(元)',
    loan_time        datetime                           null comment '借款时间',
    settle_time      datetime                           null comment '结清日期',
    create_by        varchar(64)                        null comment '创建者',
    create_time      datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by        varchar(64)                        null comment '更新人',
    update_time      datetime                           null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '开票申请明细表' row_format = DYNAMIC;

alter table dc_invoicing_application
    add invoicing_application_code varchar(20) null comment '开票编号';

alter table dc_invoicing_application
    add invoicing_application_time datetime null comment '开票时间';

create table dc_invoicing_business
(
    id                         bigint auto_increment comment '主键'
        primary key,
    invoicing_application_code varchar(20)                        null comment '开票编号',
    invoicing_application_time datetime                           null comment '开票时间',
    channel                    varchar(20)                        null comment '渠道',
    borrower                   varchar(64)                        null comment '借款人',
    phone_num                  varchar(20)                        null comment '手机号',
    id_card                    varchar(64)                        null comment '身份证号',
    invoicing_amount_total     decimal(18, 2)                     null comment '开票金额(元)',
    file_name                  varchar(200)                       null comment '文件名称',
    file_url                   varchar(200)                       null comment '文件地址',
    main_body_id               bigint                             null comment '开票主体',
    receiving_email            varchar(500)                       null comment '接收邮箱',
    push_time                  datetime                           null comment '邮箱推送时间',
    create_by                  varchar(64)                        null comment '创建者',
    create_time                datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by                  varchar(64)                        null comment '更新人',
    update_time                datetime                           null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '开票申请业务表' row_format = DYNAMIC;

alter table dc_invoicing_application_file
    add invoice_status varchar(3) default '1' null comment '开票状态(1.未开票 2.已开票)' after invoicing_theme;

alter table dc_invoicing_business
    add invoicing_status varchar(3) default '1' null comment '开票状态(1.开票中 2.开票成功 3.开票失败)' after push_time;

alter table dc_invoicing_business
    add invoicing_type varchar(3) null comment '发票类型(1.普通发票-pdf)' after invoicing_status;

alter table dc_invoicing_business
    add invoicing_head_type int null comment '抬头类型(1.个人)' after invoicing_type;

alter table dc_invoicing_business
    add reason_delete_file varchar(200) null comment '删除附件原因' after invoicing_head_type;

create table dc_invoicing_middle
(
    id                    bigint auto_increment comment '主键'
        primary key,
    invoicing_business_id bigint       null comment '开票业务 id',
    correlation_id        bigint       null comment '关联数据 id',
    correlation_type      varchar(255) null comment '关联数据类型'
)
    comment '开票申请业务关联数据中间表';

/******************************************/
/*   DatabaseName = prd_spark_db   */
/*   TableName = dim_bill_no_info   */
/******************************************/
CREATE TABLE `dim_bill_no_info`
(
    `bill_app_no`    varchar(100) NOT NULL COMMENT '借据编号',
    `loan_month`     varchar(7) COMMENT '放款月份',
    `product_no`     varchar(100) COMMENT '产品编号',
    `loan_date`      date COMMENT '放款日期',
    `due_date`       date COMMENT '到期日期',
    `loan_amt`       decimal(17, 2) COMMENT '放款金额',
    `loan_period`    int COMMENT '借款期限',
    `loan_term`      int COMMENT '分期期数',
    `guarantor_no`   varchar(10) COMMENT '担保公司',
    `id_card_type`   varchar(3) COMMENT '证件类型',
    `id_card`        varchar(21) COMMENT '证件号码',
    `mobile`         varchar(11) COMMENT '手机号',
    `name`           varchar(20) COMMENT '客户姓名',
    `farmer_flag`    varchar(10) COMMENT '农户标识',
    `data_source`    varchar(10) COMMENT '数据来源',
    `province_id`    varchar(10) COMMENT '用户所在省份',
    `city_id`        varchar(10) COMMENT '用户所在市',
    `dw_create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `dw_update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    KEY `idx_bill_app_no` (`bill_app_no`),
    PRIMARY KEY (`bill_app_no`)
) COMMENT ='借据信息表';



/******************************************/
/*   DatabaseName = prd_spark_db   */
/*   TableName = dim_base_prod_info   */
/******************************************/
CREATE TABLE `dim_base_prod_info`
(
    `id`                             bigint NOT NULL COMMENT '主键',
    `product_no`                     varchar(64) COMMENT '产品编号',
    `product_name`                   varchar(100) COMMENT '产品名称',
    `guarantor_no`                   varchar(50) COMMENT '担保方编号',
    `guarantor_name`                 varchar(50) COMMENT '担保方',
    `platform_no`                    varchar(50) COMMENT '平台方编号',
    `platform_name`                  varchar(50) COMMENT '平台方',
    `financer_no`                    varchar(50) COMMENT '资金方编号',
    `financer_name`                  varchar(50) COMMENT '资金方',
    `project_no`                     varchar(64) COMMENT '项目编号',
    `system_no`                      bigint COMMENT '系统',
    `oa_platform_no`                 varchar(100) COMMENT '外部系统平台编码',
    `oa_cust_no`                     varchar(100) COMMENT '担保公司编码',
    `oa_partner_no`                  varchar(100) COMMENT '合作方编码',
    `oa_fund_no`                     varchar(100) COMMENT '资金方编码',
    `oa_it_no`                       varchar(100) COMMENT '科技方编码',
    `is_project_finish`              varchar(1) COMMENT '项目是否已结束,Y是N否',
    `project_finish_date`            date COMMENT '项目结束日期',
    `is_project_company`             varchar(1) COMMENT '客户是否为企业,Y是C同时含有个人N否',
    `is_project_td`                  varchar(1) COMMENT '借据是否通道简版数据,Y是N否',
    `is_project_plan`                varchar(1) COMMENT '借据是否有还款计划数据,Y有N否',
    `is_project_plan_update`         varchar(1) COMMENT '借据是否有还款计划实还更新数据,Y有C有但未上线N否',
    `is_project_plan_reset`          varchar(1) COMMENT '借据是否有还款计划缩期情况,Y有C有但未上线N否',
    `is_project_repay1`              varchar(1) COMMENT '借据是否有正常还款数据,Y有C有但未上线N否',
    `is_project_repay4`              varchar(1) COMMENT '借据是否有提前还款数据,Y有C有但未上线N否',
    `is_project_repay5`              varchar(1) COMMENT '借据是否有提前结清数据,Y有C有但未上线N否',
    `is_project_repay7`              varchar(1) COMMENT '借据是否有代偿还款数据,Y有C有但未上线N否',
    `is_project_total_repay7`        varchar(1) COMMENT '借据是否有累计代偿还款数据,Y有C有但未上线N否',
    `is_project_total_repay`         varchar(1) COMMENT '借据是否有用户累计还款数据,Y有C有但未上线N否',
    `is_project_repay8`              varchar(1) COMMENT '借据是否有追偿还款数据,Y有C有但未上线N否',
    `is_project_repay7_finish`       varchar(1) COMMENT '借据是否代偿时结清,Y是N否',
    `is_project_repay8_normal`       varchar(1) COMMENT '借据追偿还款是否按正常还款提供,Y是N否',
    `is_result_fpd10`                varchar(1) COMMENT 'FPD10是否可用，Y是N否',
    `is_result_vintage`              varchar(1) COMMENT 'vintage是否可用，Y是N否',
    `is_result_balance_distribution` varchar(1) COMMENT '余额分布是否可用，Y是N否',
    `remark`                         varchar(255) COMMENT '备注',
    `status`                         varchar(1) COMMENT '状态（0正常 1停用）',
    `check_status`                   varchar(1) COMMENT '审核状态 0新增审核状态，1修改项目审核中，2删除项目审核中，3（-）',
    `description`                    varchar(500) COMMENT '说明',
    `create_time`                    timestamp,
    `update_time`                    timestamp,
    `create_by`                      varchar(64) COMMENT '创建者',
    `update_by`                      varchar(64) COMMENT '更新人',
    `biz_type`                       varchar(2) COMMENT '业务类型 01-个人业务 02-对公业务',
    `product_type`                   varchar(2) COMMENT '产品类型 11-个人住房商业贷款 12-个人商用房（含商住两用）贷款 13-个人住房公积金贷款 21-个人汽车消费贷款 41-个人经营性贷款 42-个人创业担保贷款 51-农户贷款 52-经营性农户贷款 53-消费性农户贷款 91-其他个人消费贷款 99-其他贷款',
    `min_credit_limit`               bigint         DEFAULT '0' COMMENT '最低授信额度',
    `max_credit_limit`               bigint         DEFAULT '0' COMMENT '最高授信额度',
    `min_inte_rate`                  decimal(11, 8) DEFAULT '0.00000000' COMMENT '最低利率 年化小数',
    `max_inte_rate`                  decimal(11, 8) DEFAULT '0.00000000' COMMENT '最高利率  年化小数',
    `min_credit_spread`              int            DEFAULT '0' COMMENT '最短授信期限',
    `max_credit_spread`              int            DEFAULT '0' COMMENT '最长授信期限',
    `grt_method`                     varchar(2) COMMENT '担保方式 01-信用 02-抵押 03-质押',
    `repayment_method`               varchar(2) COMMENT '还款方式 11-分期等额本息 12-分期等额本金 13-到期还本分期结息 21-到期一次还本付息 90-不区分还款方式',
    `prepayment`                     varchar(1) COMMENT '是否允许提前还款 0-可以提前还款 1-不可以提前还款',
    `repayment_data`                 tinyint COMMENT '还款数据优先级 1-优先还款信息 2-优先还款计划',
    `act_year_rate`                  decimal(11, 8) DEFAULT '0.00000000' COMMENT '利率（实际年化）',
    `service_rate`                   decimal(11, 8) DEFAULT '0.00000000' COMMENT '前期服务费费率',
    `guarantee_rate`                 decimal(11, 8) DEFAULT '0.00000000' COMMENT '前期担保费费率',
    `margin_rate`                    decimal(11, 8) DEFAULT '0.00000000' COMMENT '前期保证金费率',
    `compensate_rate`                decimal(11, 8) DEFAULT '0.00000000' COMMENT '前期代偿金费率',
    `period_service_rate`            decimal(11, 8) DEFAULT '0.00000000' COMMENT '年化服务费费率',
    `period_guarantee_rate`          decimal(11, 8) DEFAULT '0.00000000' COMMENT '年化担保费费率',
    `period_margin_rate`             decimal(11, 8) DEFAULT '0.00000000' COMMENT '年化保证金费率',
    `period_compensate_rate`         decimal(11, 8) DEFAULT '0.00000000' COMMENT '年化代偿金费率',
    `oint_rate`                      decimal(11, 8) DEFAULT '0.00000000' COMMENT '罚息日利率',
    `define_rate`                    decimal(11, 8) DEFAULT '0.00000000' COMMENT '逾期违约金/滞纳金费率',
    `adv_define_rate`                decimal(11, 8) DEFAULT '0.00000000' COMMENT '提前还款违约金费率',
    `compensate_days`                smallint COMMENT '代偿天数',
    `cpst_rule_no`                   varchar(5) COMMENT '代偿规则编号',
    `grace_day`                      smallint COMMENT '宽限期',
    `interest_free_period`           smallint COMMENT '免息期',
    `is_cpst`                        varchar(1) COMMENT '是否提供代偿 0-是 1-否',
    `is_recovery`                    varchar(1) COMMENT '是否提供追偿 0-是 1-否',
    `fee1_rate`                      decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率1',
    `fee2_rate`                      decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率2',
    `fee3_rate`                      decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率3',
    `fee4_rate`                      decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率4',
    `fee5_rate`                      decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率5',
    `fee6_rate`                      decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率6',
    `fee7_rate`                      decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率7',
    `fee8_rate`                      decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率8',
    `dw_create_time`                 timestamp      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `dw_update_time`                 timestamp      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_end`                         varchar(1) COMMENT '是否结束 Y是  N否',
    PRIMARY KEY (`id`)
) COMMENT ='产品信息表'
;
alter table dc_invoicing_business
    add reason_fail varchar(200) null comment '失败原因' after reason_delete_file;

create table dc_debt_user
(
    id          bigint auto_increment comment '主键'
        primary key,
    name        varchar(64)                        null comment '姓名',
    phone_num   varchar(20)                        null comment '手机号',
    id_card     varchar(64)                        null comment '身份证',
    cust_id     bigint                             null comment '注册公司(担保公司)',
    data_source varchar(3)                         null comment '来源 1.小程序',
    create_by   varchar(64)                        null comment '创建者',
    create_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by   varchar(64)                        null comment '更新人',
    update_time datetime                           null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '债转用户表' row_format = DYNAMIC;
alter table dc_debt_user
    add last_login_time datetime null comment '最后登录时间' after data_source;

ALTER TABLE dim_bill_no_info ADD INDEX idx_id_card_mobile_name (id_card, mobile,name);


INSERT INTO dc_debt_user (id, name, phone_num, id_card, cust_id, data_source, create_by, create_time, update_by, update_time, last_login_time) VALUES (1, '张赫', '15835962866', '142733199602191211', 1, '1', null, '2025-05-15 10:13:13.*********', null, '2025-05-20 16:23:27.*********', '2025-05-20 16:23:27.*********');
INSERT INTO dim_base_prod_info (id, product_no, product_name, guarantor_no, guarantor_name, platform_no, platform_name, financer_no, financer_name, project_no, system_no, oa_platform_no, oa_cust_no, oa_partner_no, oa_fund_no, oa_it_no, is_project_finish, project_finish_date, is_project_company, is_project_td, is_project_plan, is_project_plan_update, is_project_plan_reset, is_project_repay1, is_project_repay4, is_project_repay5, is_project_repay7, is_project_total_repay7, is_project_total_repay, is_project_repay8, is_project_repay7_finish, is_project_repay8_normal, is_result_fpd10, is_result_vintage, is_result_balance_distribution, remark, status, check_status, description, create_time, update_time, create_by, update_by, biz_type, product_type, min_credit_limit, max_credit_limit, min_inte_rate, max_inte_rate, min_credit_spread, max_credit_spread, grt_method, repayment_method, prepayment, repayment_data, act_year_rate, service_rate, guarantee_rate, margin_rate, compensate_rate, period_service_rate, period_guarantee_rate, period_margin_rate, period_compensate_rate, oint_rate, define_rate, adv_define_rate, compensate_days, cpst_rule_no, grace_day, interest_free_period, is_cpst, is_recovery, fee1_rate, fee2_rate, fee3_rate, fee4_rate, fee5_rate, fee6_rate, fee7_rate, fee8_rate, dw_create_time, dw_update_time, is_end) VALUES (101, 'MAILIANGCHE-1', '樽昊-麦靓车-浙商银行', '2', '湖南樽昊', '104', '麦靓车', '92', '浙商银行', '15', 900, 'CD', 'HNZH', 'MLC', 'ZSBK', '', 'N', null, 'N', 'N', 'Y', 'Y', 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'Y', null, '0', null, null, '2023-06-12 08:49:43.*********', '2024-09-07 11:31:33.*********', null, 'sunyc', null, null, 0, 0, 0.00000000, 0.00000000, 0, 0, null, null, null, null, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, null, null, null, null, null, null, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, '2025-03-03 10:12:16.*********', '2025-03-03 10:12:16.*********', null);
INSERT INTO dim_bill_no_info (bill_app_no, loan_month, product_no, loan_date, due_date, loan_amt, loan_period, loan_term, guarantor_no, id_card_type, id_card, mobile, name, farmer_flag, data_source, province_id, city_id, dw_create_time, dw_update_time) VALUES ('J_MAILIANGCHE_ZHESHANG_0034126', '2023-02', 'MAILIANGCHE-1', '2023-02-02', '2026-02-20', 19890000.00, 36, 36, '2', '01', '142733199602191211', '15835962866', '张赫', '1', '02', '62', '6201', '2024-12-18 14:14:34.*********', '2025-05-15 09:43:55.*********');

