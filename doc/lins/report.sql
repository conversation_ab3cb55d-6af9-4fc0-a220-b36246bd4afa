
create table report_database
(
    id          bigint auto_increment comment 'ID'
        primary key,
    name        varchar(200) charset utf8mb4 not null comment '数据源名称',
    jdbc_url    varchar(200) charset utf8mb4 not null comment '数据源连接字符串(JDBC)',
    user        varchar(100) charset utf8mb4 not null comment '数据源登录用户名',
    password    varchar(100) charset utf8mb4 null comment '数据源登录密码',
    data_type   varchar(64)                  null comment '数据库类型',
    pool_type   varchar(64)                  null comment '连接池类型',
    options     longtext                     not null comment '数据源配置选项(JSON格式）',
    comment     varchar(400) charset utf8mb4 null comment '说明备注',
    create_by   varchar(64) default ''       null comment '创建者',
    create_time datetime                     null comment '创建时间',
    update_by   varchar(64) default ''       null comment '更新者',
    update_time datetime                     null comment '更新时间'
)
    comment '数据库配置信息' row_format = DYNAMIC;
INSERT INTO report_database (id, name, jdbc_url, user, password, data_type, pool_type, options, comment, create_by, create_time, update_by, update_time) VALUES (1, '演示', '****************************************************************************************************************************************************', 'root', '123456789', 'MySQL', 'druid', '[{"configKey":"initialSize","configValue":3,"remark":"1231"},{"configKey":"maxActive","configValue":20,"remark":""},{"configKey":"minIdle","configValue":1,"remark":""},{"configKey":"maxWait","configValue":60000,"remark":""},{"configKey":"timeBetweenEvictionRunsMillis","configValue":60000,"remark":""},{"configKey":"minEvictableIdleTimeMillis","configValue":300000,"remark":""},{"configKey":"testWhileIdle","configValue":true,"remark":""},{"configKey":"testOnBorrow","configValue":true,"remark":""},{"configKey":"testOnReturn","configValue":false,"remark":""},{"configKey":"maxOpenPreparedStatements","configValue":20,"remark":""},{"configKey":"removeAbandoned","configValue":true,"remark":""},{"configKey":"removeAbandonedTimeout","configValue":1800,"remark":""},{"configKey":"logAbandoned","configValue":true,"remark":""}]', '', 'admin', '2021-11-18 00:00:00', 'admin', '2024-05-17 09:30:14');
INSERT INTO report_database (id, name, jdbc_url, user, password, data_type, pool_type, options, comment, create_by, create_time, update_by, update_time) VALUES (2, 'dev', '******************************************************************************************************************************************************************************************************', 'root', '123456789', 'MySQL', 'druid', '[{"configKey":"initialSize","configValue":3,"remark":""},{"configKey":"maxActive","configValue":20,"remark":""},{"configKey":"minIdle","configValue":1,"remark":""},{"configKey":"maxWait","configValue":60000,"remark":""},{"configKey":"timeBetweenEvictionRunsMillis","configValue":60000,"remark":""},{"configKey":"minEvictableIdleTimeMillis","configValue":300000,"remark":""},{"configKey":"testWhileIdle","configValue":true,"remark":""},{"configKey":"testOnBorrow","configValue":false,"remark":""},{"configKey":"testOnReturn","configValue":false,"remark":""},{"configKey":"maxOpenPreparedStatements","configValue":20,"remark":""},{"configKey":"removeAbandoned","configValue":true,"remark":""},{"configKey":"removeAbandonedTimeout","configValue":1800,"remark":""},{"configKey":"logAbandoned","configValue":true,"remark":""}]', null, 'admin', '2024-05-16 14:18:21', '', null);

-- auto-generated definition
create table report_sql
(
    id          bigint auto_increment comment '编号'
        primary key,
    sql_name    varchar(200) default '' null comment '名称',
    database_id bigint                  not null comment '所属数据源',
    query_sql   text                    null comment '查询sql',
    layout      json                    null comment '栅格布局',
    create_by   varchar(64)  default '' null comment '创建者',
    create_time datetime                null comment '创建时间',
    update_by   varchar(64)  default '' null comment '更新者',
    update_time datetime                null comment '更新时间'
)
    comment '报表查询sql' row_format = DYNAMIC;
INSERT INTO report_sql (sql_name, database_id, query_sql, layout, create_by, create_time, update_by, update_time) VALUES ('用户报表', 1, 'select user_name, nick_name, phonenumber, sex, create_time from sys_user', '[{"h": 1, "i": "user_name", "w": 5, "x": 0, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 3}, {"h": 1, "i": "nick_name", "w": 5, "x": 5, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 3}, {"h": 1, "i": "sex", "w": 5, "x": 10, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 3}, {"h": 1, "i": "create_time", "w": 5, "x": 15, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 3}, {"h": 1, "i": "#search", "w": 5, "x": 19, "y": 1, "maxH": 0, "maxW": 0, "minH": 0, "minW": 4}, {"h": 9, "i": "#table", "w": 24, "x": 15, "y": 2, "maxH": 0, "maxW": 0, "minH": 9, "minW": 12}]', 'admin', '2021-12-01 00:00:00', 'admin', '2022-07-14 15:23:35');

INSERT INTO report_sql (sql_name, database_id, query_sql, layout, create_by, create_time, update_by, update_time) VALUES ('11', 2, 'select
  *
from
  sys_user', '[{"h": 1, "i": "user_name", "w": 5, "x": 0, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 3}, {"h": 1, "i": "nick_name", "w": 5, "x": 5, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 3}, {"h": 1, "i": "user_type", "w": 5, "x": 10, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 3}, {"h": 1, "i": "sex", "w": 5, "x": 15, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 3}, {"h": 1, "i": "#search", "w": 5, "x": 19, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 4}, {"h": 9, "i": "#table", "w": 24, "x": 15, "y": 2, "maxH": 0, "maxW": 0, "minH": 9, "minW": 12}]', 'admin', '2024-05-16 14:18:47', '', null);

INSERT INTO report_sql (sql_name, database_id, query_sql, layout, create_by, create_time, update_by, update_time) VALUES ('222', 2, 'select
  sys_company.company_name,company_type_mapping.company_type_code
from
  sys_company
  left join company_type_mapping on sys_company.id = company_type_mapping.company_id
group by
  sys_company.id', '[{"h": 1, "i": "company_name", "w": 5, "x": 0, "y": 0, "maxH": 0, "maxW": 0, "minH": 0, "minW": 3}, {"h": 1, "i": "#search", "w": 5, "x": 19, "y": 1, "maxH": 0, "maxW": 0, "minH": 0, "minW": 4}, {"h": 9, "i": "#table", "w": 24, "x": 0, "y": 3, "maxH": 0, "maxW": 0, "minH": 9, "minW": 12}]', 'admin', '2024-05-17 00:00:00', 'admin', '2024-05-17 10:44:23');


create table report_sql_column
(
    id          bigint auto_increment comment '编号'
        primary key,
    sql_id      bigint                  not null comment '绑定的sql',
    sql_field   varchar(255)            null comment 'sql查询字段',
    column_name varchar(200)            null comment '列名称（用来回显）',
    is_list     char                    null comment '是否列表字段（1是）',
    is_export   char                    null comment '是否导出字段（1是）',
    is_query    char                    null comment '是否查询字段（1是）',
    query_type  varchar(200)            null comment '查询方式（等于、不等于、大于、大于等于、小于、小于等于、范围、模糊）',
    html_type   varchar(200)            null comment '显示类型（文本框、下拉框、日期）',
    dict_type   varchar(200) default '' null comment '字典类型',
    create_by   varchar(64)  default '' null comment '创建者',
    create_time datetime                null comment '创建时间',
    update_by   varchar(64)  default '' null comment '更新者',
    update_time datetime                null comment '更新时间'
)
    comment '报表查询sql字段' row_format = DYNAMIC;

INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (1, 'user_name', '登录名', '1', '1', '1', 'LIKE', null, '', 'admin', '2022-07-14 14:03:33', 'admin', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (1, 'nick_name', '用户昵称', '1', '1', '1', 'LIKE', null, '', 'admin', '2022-07-14 14:03:33', 'admin', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (1, 'phonenumber', '手机号', '1', '1', '0', null, null, '', 'admin', '2022-07-14 14:03:33', 'admin', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (1, 'sex', '性别', '1', '1', '1', 'EQ', 'select', 'sys_user_sex', 'admin', '2022-07-14 14:03:33', 'admin', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (1, 'create_time', '创建时间', '1', '1', '1', 'BETWEEN', 'datetime', '', 'admin', '2022-07-14 14:03:33', 'admin', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'user_id', 'user_id', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'dept_id', 'dept_id', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'user_name', 'user_name', '1', '1', '1', 'LIKE', null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'nick_name', 'nick_name', '1', '1', '1', 'LIKE', null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'user_type', 'user_type', '1', '1', '1', 'EQ', 'select', '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'email', 'email', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'phonenumber', 'phonenumber', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'sex', 'sex', '1', '1', '1', 'EQ', 'select', '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'avatar', 'avatar', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'password', 'password', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'status', 'status', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'del_flag', 'del_flag', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'login_ip', 'login_ip', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'login_date', 'login_date', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'create_by', 'create_by', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'create_time', 'create_time', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'update_by', 'update_by', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'update_time', 'update_time', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'remark', 'remark', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (3, 'cust_no', 'cust_no', '1', '1', '0', null, null, '', 'admin', '2024-05-16 14:18:47', '', null);
INSERT INTO report_sql_column (sql_id, sql_field, column_name, is_list, is_export, is_query, query_type, html_type, dict_type, create_by, create_time, update_by, update_time) VALUES (4, 'company_name', 'company_name', '1', '1', '1', 'LIKE', null, '', 'admin', '2024-05-17 10:44:23', '', null);



INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('报表管理', 0, 0, 'reports', null, null, 1, 0, 'M', '0', '0', null, 'excel', 'admin', '2021-12-01 11:04:36', '', null, '');
SELECT @baobiaoParendId := LAST_INSERT_ID();
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('演示-用户报表', @baobiaoParendId, 0, 'user', 'report/gen/generalReport', '{"reportId": 1}', 1, 1, 'C', '0', '0', null, 'build', 'admin', '2021-12-01 11:09:08', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('用户库', @baobiaoParendId, 2, '/User', 'report/gen/generalReport', '{"reportId": 3}', 1, 1, 'C', '0', '0', null, 'checkbox', 'admin', '2024-05-16 18:01:13', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('ssssss', @baobiaoParendId, 2, 'usersss', 'report/gen/generalReport', '{"reportId": 3}', 1, 1, 'C', '0', '0', null, 'druid', 'admin', '2024-05-16 18:02:35', '', null, '');
SELECT @baobiaoParendId := LAST_INSERT_ID();
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('数据源管理', 0, 0, 'base', null, null, 1, 0, 'M', '0', '0', '', 'build', 'admin', '2021-11-17 14:24:28', 'admin', '2021-11-17 14:25:36', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('数据库配置', 2000, 0, 'database', 'report/database', null, 1, 1, 'C', '0', '0', null, 'druid', 'admin', '2021-11-17 14:25:30', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('SQL管理', 2000, 1, 'report_sql', 'report/sql', null, 1, 1, 'C', '0', '0', null, 'dict', 'admin', '2021-11-24 09:49:30', '', null, '');
