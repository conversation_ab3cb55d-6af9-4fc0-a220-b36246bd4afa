-- auto-generated definition
create table zl_supervise_information
(
    id                      bigint auto_increment comment '主键'
        primary key,
    information_name        varchar(255)     null comment '监管资料名称',
    catalogue_id            int              not null comment '目录id',
    information_system_code varchar(255)     null comment '系统监管资料编号',
    information_year        varchar(255)     null comment '资料年度',
    information_code        varchar(255)     null comment '监管资料编号',
    save_flag               char             not null comment '保存状态(0 非永久  1永久)',
    save_start_time         datetime         null comment '保管开始时间',
    save_end_time           datetime         null comment '保管结束时间',
    audit_state             char             null comment '审核状态( 1审核通过，2未审核 3审核不通过 4审核中)',
    submit_state            char             not null comment '提交状态 ( 0 未提交，1 已提交)',
    file_url                varchar(255)     not null comment '文件路径',
    file_name               varchar(255)     not null comment '文件名称',
    cooperation_company     bigint           null comment '合作公司',
    cooperation_project     bigint           null comment '合作项目',
    remark                  varchar(255)     null comment '备注',
    create_by               varchar(255)     not null comment '创建人',
    create_time             datetime         not null comment '创建时间',
    update_by               varchar(255)     null comment '修改人',
    update_time             datetime         null comment '修改时间',
    is_delete               char default '1' null comment '是否删除 0.是 1.否 ',
    is_abandoned            char default '1' null comment '是否废弃 0.是 1.否 '
)
    comment '监管资料' collate = utf8mb4_general_ci
                       row_format = DYNAMIC;

-- auto-generated definition
create table zl_supervise_information_catalogue
(
    id                    bigint auto_increment comment '主键'
        primary key,
    catalogue_name        varchar(255)     null comment '目录名称',
    parent_id             bigint           null comment '上级目录id',
    catalogue_system_code varchar(255)     not null comment '系统目录编号',
    catalogue_code        varchar(255)     null comment '目录编号',
    cooperation_company   bigint           null comment '合作公司',
    cooperation_project   bigint           null comment '合作项目',
    catalogue_type        varchar(5)       null comment '目录类型',
    is_public             varchar(2)       null comment '是否为公共资料库 1.是 0.否',
    org_id                bigint default 0 not null comment '所属公司',
    dept_id               bigint default 0 not null comment '所属部门',
    order_num             int    default 0 not null comment '排序号',
    remake                varchar(600)     null comment '备注',
    create_by             varchar(255)     not null comment '创建人',
    create_time           datetime         not null comment '创建时间',
    update_by             varchar(255)     null comment '修改人',
    update_time           datetime         null comment '修改时间'
)
    comment '监管资料目录' collate = utf8mb4_general_ci
                           row_format = DYNAMIC;

-- auto-generated definition
create table zl_supervise_information_process
(
    id             bigint auto_increment comment '主键'
        primary key,
    is_used        char         not null comment '是否用印 0.是 1.否 ',
    process_type   char         not null comment '流程类型 1.监管资料审核 2.监管资料下载 3.监管资料用印',
    process_id     varchar(40)  not null comment '流程关联id',
    information_id bigint       not null comment '关联的资料id',
    create_by      varchar(255) not null comment '创建人',
    create_time    datetime     not null comment '创建时间',
    update_by      varchar(255) null comment '修改人',
    update_time    datetime     null comment '修改时间'
)
    comment '监管流程表' collate = utf8mb4_general_ci
                             row_format = DYNAMIC;

create table zl_supervise_information_used
(
    id                    bigint auto_increment comment '主键'
        primary key,
    information_user_name varchar(255) null,
    audit_time            datetime     not null comment '资料审核时间',
    sponsor               varchar(255) not null comment '发起人',
    process_id            varchar(40)  not null comment '流程关联id',
    cooperation_company   varchar(500) null comment '合作公司',
    cooperation_project   varchar(500) null comment '合作项目',
    create_by             varchar(255) not null comment '创建人',
    create_time           datetime     not null comment '创建时间',
    update_by             varchar(255) null comment '修改人',
    update_time           datetime     null comment '修改时间',
    sponsor_name          varchar(255) null comment '发起人name',
    project_id            bigint       null comment '所属项目',
    used_company_id       bigint       null comment '用印公司'
)
    comment '监管资料用印' collate = utf8mb4_general_ci
                           row_format = DYNAMIC;

INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES ('监管报送资料目录类型', 'regulatoryCatalogType', '0', 'admin', '2024-10-11 13:47:54', '', null, null);

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '资料目录', '1', 'regulatoryCatalogType', null, 'default', 'N', '0', 'admin', '2024-10-11 13:59:46', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '文件目录', '2', 'regulatoryCatalogType', null, 'default', 'N', '0', 'admin', '2024-10-11 13:59:57', '', null, null);

UPDATE sys_menu
SET menu_name = '授信及贷后资料管理'
WHERE menu_name = '资料管理';

UPDATE sys_menu
SET query = '{"title":"资料用印审批(授信及贷后)"}'
WHERE menu_name = '资料用印审批';

UPDATE sys_menu
SET query = '{"title":"资料下载审批(授信及贷后)"}'
WHERE menu_name = '资料下载审批';

UPDATE sys_menu
SET query = '{"title":"资料目录(授信及贷后)"}'
WHERE menu_name = '资料目录';


UPDATE sys_menu
SET query = '{"title":"录入资料(授信及贷后)"}'
WHERE menu_name = '录入资料';


UPDATE sys_menu
SET query = '{"title":"历史信息记录(授信及贷后)"}"'
WHERE menu_name = '历史信息记录';

UPDATE sys_menu
SET query = '{"title":"资料发放统计(授信及贷后)"}'
WHERE menu_name = '资料发放统计';


INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('监管报送资料管理', 0, 13, 'dataManagementSupervise', null, null, 1, 0, 'M', '0', '0', '', 'date-range', 'admin', '2024-10-09 15:13:26', 'admin', '2024-10-09 15:14:39', '');
SELECT @menu := LAST_INSERT_ID();

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('资料下载审批/用印审批', @menu, 2, 'dataUpLoadApprovalSupervise', 'dataManagementSupervise/dataUpLoadApproval', '{"title":"资料下载审批/用印审批(监管报送)"}', 1, 1, 'C', '0', '0', '', '#', 'admin', '2024-10-09 15:17:35', 'admin', '2024-10-15 14:27:14', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('资料目录', @menu, 3, 'directoryMationSupervise', 'dataManagementSupervise/directoryMation/index', '{"title":"资料目录(监管报送)"}', 1, 0, 'C', '0', '0', '', '#', 'admin', '2024-10-09 15:18:48', 'admin', '2024-10-09 15:19:10', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('录入资料', @menu, 4, 'inputDataSupervise', 'dataManagementSupervise/inputData/index', '{"title":"录入资料(监管报送)"}', 1, 0, 'C', '0', '0', '', '#', 'admin', '2024-10-09 15:20:04', 'admin', '2024-10-09 15:20:34', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('历史信息记录', @menu, 5, 'historyMationSupervise', 'dataManagementSupervise/historyMation/index', '{"title":"历史信息记录(监管报送)"}', 1, 0, 'C', '0', '0', '', '#', 'admin', '2024-10-09 15:21:23', 'admin', '2024-10-09 15:22:43', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('资料发放统计', @menu, 6, 'dataStatisticsSupervise', 'dataManagementSupervise/dataStatistics/index', '{"title":"资料发放统计(监管报送)"}', 1, 0, 'C', '0', '0', '', '#', 'admin', '2024-10-09 15:22:09', 'admin', '2024-10-09 15:22:36', '');

alter table zl_supervise_information
    add information_month int null comment '资料所属月份' after information_year;

alter table zl_information
    add information_month int null comment '资料所属月份' after information_year;

alter table zl_information_catalogue
    alter column org_id set default 0;

alter table zl_information_catalogue
    alter column dept_id set default 0;

alter table zl_information_catalogue
    add is_public varchar(10) null comment '是否公共资料库(1.是 0.否)' after order_num;

update zl_information_catalogue
set is_public = '0' where is_public is null;

# INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '监管资料录入', 'JG1', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-10-14 18:14:29', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '监管资料下载', 'JG2', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-10-14 18:14:44', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '监管资料用印', 'JG3', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-10-14 18:14:56', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '直接用印流程', 'ZL4', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-10-16 13:32:48', 'admin', '2024-10-16 15:04:57', null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (23, '投诉类资料录入流程', 'JGTS1', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-11-12 11:21:24', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (24, '风险经理上传资料流程', 'JGFX1', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-11-12 11:21:53', 'admin', '2024-11-12 11:22:04', null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (25, '会计资料流程', 'JGKJ1', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-11-12 11:22:36', '', null, null);

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (4, 'B类合同档案库', 'BHT', 'archivist_pertain', null, 'default', 'N', '0', 'admin', '2024-10-14 14:38:35', 'admin', '2024-10-14 14:40:24', null);


SELECT @aid1 := (select user_id from sys_user where user_name = 'liurl');
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @aid1, 'SXINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());

INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @aid1, 'JGINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @aid1, 'JGINFORMATION','COMMON','PROJ0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());



SELECT @bid1 := (select user_id from sys_user where user_name = 'zhanglj');
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @bid1, 'SXINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());

INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @bid1, 'JGINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @bid1, 'JGINFORMATION','COMMON','PROJ0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());



SELECT @cid1 := (select user_id from sys_user where user_name = 'zhaixc');
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @cid1, 'SXINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());

INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @cid1, 'JGINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @cid1, 'JGINFORMATION','COMMON','PROJ0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());



SELECT @did1 := (select user_id from sys_user where user_name = 'liugj');
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @did1, 'SXINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());

INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @did1, 'JGINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @did1, 'JGINFORMATION','COMMON','PROJ0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());


SELECT @eid1 := (select user_id from sys_user where user_name = 'mashuo');
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @eid1, 'SXINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());

INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @eid1, 'JGINFORMATION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @eid1, 'JGINFORMATION','COMMON','PROJ0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());

INSERT INTO da_archivist_catalogue (org_id, catalogue_system_code, catalogue_name, create_by, create_time,pertain_archivist )
    (SELECT id,  company_code, company_short_name, create_by, NOW(), 'BHT'
     FROM sys_company where is_inside = 1);
