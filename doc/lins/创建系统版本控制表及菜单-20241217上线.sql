CREATE TABLE `sys_version_maintain` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `version` varchar(11) DEFAULT NULL COMMENT '版本',
  `msg` varchar(500) DEFAULT NULL COMMENT '版本内容',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本维护表';


#初始化第一版
INSERT INTO `sys_version_maintain`(`version`, `msg`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1.0.0', NULL, 'admin',now(), NULL, NULL);

#创建菜单
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('系统版本控制', 0, 23, 'version', 'version/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', now(), '', NULL, '');

