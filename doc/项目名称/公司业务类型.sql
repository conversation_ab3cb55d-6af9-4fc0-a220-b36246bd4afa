update project_company_relevance
set unit_type = 'cust'
where unit_type = '0';

update project_company_relevance
set unit_type = 'partner'
where unit_type = '1';

update project_company_relevance
set unit_type = 'fund'
where unit_type = '2';

update project_company_relevance
set unit_type = 'other'
where unit_type = '3';

SELECT @房抵 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '房抵');
SELECT @房产抵押担保 := (select id  from oa_data_manage where data_name = '房产抵押担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @房产抵押担保
where type_id = @房抵;

SELECT @房抵阶段担保 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '房抵阶段担保');
SELECT @房抵阶段性担保 := (select id  from oa_data_manage where data_name = '房抵阶段性担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @房抵阶段性担保
where type_id = @房抵阶段担保;

SELECT @车贷 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '车贷');
SELECT @车辆抵押担保 := (select id  from oa_data_manage where data_name = '车辆抵押担保'  and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @车辆抵押担保
where type_id = @车贷;

SELECT @车贷再保理 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '车贷再保理');
SELECT @车贷再保理担保 := (select id from oa_data_manage where data_name = '车贷再保理担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @车贷再保理担保
where type_id = @车贷再保理;

SELECT @供应链 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '供应链');
SELECT @TMC差旅担保 := (select id from oa_data_manage where data_name = 'TMC差旅担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @TMC差旅担保
where type_id = @供应链;

SELECT @消金 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '消金');
SELECT @现金贷担保 := (select id from oa_data_manage where data_name = '现金贷担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @现金贷担保
where type_id = @消金;

SELECT @教育分期 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '教育分期');
SELECT @教育分期担保 := (select id from oa_data_manage where data_name = '教育分期担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @教育分期担保
where type_id = @教育分期;

SELECT @保函 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '保函');
SELECT @投标担保 := (select id from oa_data_manage where data_name = '投标担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @投标担保
where type_id = @保函;

SELECT @房产交易担保支付 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '房产交易担保支付');
SELECT @房产交易支付担保 := (select id from oa_data_manage where data_name = '房产交易支付担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @房产交易支付担保
where type_id = @房产交易担保支付;


SELECT @通道业务dict := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '通道业务');
SELECT @通道业务data := (select id from oa_data_manage where data_name = '通道业务' and first_data_code = 'project_type');
UPDATE project_type_relevance
set type_id = @通道业务data
where type_id = @通道业务dict;

SELECT @分润业务 := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '分润业务');
SELECT @实担业务 := (select id from oa_data_manage where data_name = '实担业务' and first_data_code = 'project_type');
UPDATE project_type_relevance
set type_id = @实担业务
where type_id = @分润业务;

SELECT @保函业务 := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '保函业务');
SELECT @非融资性担保业务 := (select id from oa_data_manage where data_name = '非融资性担保业务' and first_data_code = 'project_type');
UPDATE project_type_relevance
set type_id = @非融资性担保业务
where type_id = @保函业务;

SELECT @法催业务 := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '法催业务');
SELECT @不良资产处置业务 := (select id from oa_data_manage where data_name = '不良资产处置业务' and first_data_code = 'project_type');
UPDATE project_type_relevance
set type_id = @不良资产处置业务
where type_id = @法催业务;
# 新增五个
SELECT @车分期 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '车分期');
SELECT @购车分期担保 := (select id  from oa_data_manage where data_name = '购车分期担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @购车分期担保
where type_id = @车分期;

SELECT @农机 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '农机');
SELECT @农机车担保 := (select id  from oa_data_manage where data_name = '农机车担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @农机车担保
where type_id = @农机;

SELECT @小微企业贷 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '小微企业贷');
SELECT @信用贷担保 := (select id  from oa_data_manage where data_name = '信用贷担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @信用贷担保
where type_id = @小微企业贷;

SELECT @电子保函 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '电子保函');
SELECT @投标担保 := (select id  from oa_data_manage where data_name = '投标担保' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @投标担保
where type_id = @电子保函;

SELECT @不良出表 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '不良出表');
SELECT @金融机构出表 := (select id  from oa_data_manage where data_name = '金融机构出表' and first_data_code = 'business_type');
UPDATE  project_type_relevance
set type_id = @金融机构出表
where type_id = @不良出表;


# --公司业务类型
SELECT @公司房抵 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '房抵');
SELECT @公司房类 := (select id from oa_data_manage where data_name = '房类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司房类
where company_business_type_code = @公司房抵;

SELECT @公司房抵阶段担保 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '房抵阶段担保');
SELECT @公司房类 := (select id from oa_data_manage where data_name = '房类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司房类
where company_business_type_code = @公司房抵阶段担保;

SELECT @公司车贷 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '车贷');
SELECT @公司车类 := (select id from oa_data_manage where data_name = '车类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司车类
where company_business_type_code = @公司车贷;

SELECT @公司车贷再保理 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '车贷再保理');
SELECT @公司车类 := (select id from oa_data_manage where data_name = '车类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司车类
where company_business_type_code = @公司车贷再保理;

SELECT @公司供应链 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '供应链');
SELECT @公司小微类 := (select id from oa_data_manage where data_name = '小微类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司小微类
where company_business_type_code = @公司供应链;

SELECT @公司消金 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '消金');
SELECT @公司消金类 := (select id from oa_data_manage where data_name = '消金类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司消金类
where company_business_type_code = @公司消金;

SELECT @公司教育分期 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '教育分期');
SELECT @公司消金类 := (select id from oa_data_manage where data_name = '消金类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司消金类
where company_business_type_code = @公司教育分期;

SELECT @公司教育分期 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '教育分期');
SELECT @公司消金类 := (select id from oa_data_manage where data_name = '消金类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司消金类
where company_business_type_code = @公司教育分期;

SELECT @公司保函 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '保函');
SELECT @公司保函类 := (select id from oa_data_manage where data_name = '保函类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司保函类
where company_business_type_code = @公司保函;

SELECT @公司房产交易担保支付 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '房产交易担保支付');
SELECT @公司保函类 := (select id from oa_data_manage where data_name = '保函类' and first_data_code = 'business_type');
UPDATE company_business_type_mapping
set company_business_type_code = @公司保函类
where company_business_type_code = @公司房产交易担保支付;

# 新增五个
SELECT @车分期 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '车分期');
SELECT @车类 := (select id  from oa_data_manage where data_name = '车类' and first_data_code = 'business_type');
UPDATE  company_business_type_mapping
set company_business_type_code = @车类
where company_business_type_code = @车分期;

SELECT @农机 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '农机');
SELECT @车类 := (select id  from oa_data_manage where data_name = '车类' and first_data_code = 'business_type');
UPDATE  company_business_type_mapping
set company_business_type_code = @车类
where company_business_type_code = @农机 ;

SELECT @小微企业贷 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '小微企业贷');
SELECT @小微类 := (select id  from oa_data_manage where data_name = '小微类' and first_data_code = 'business_type');
UPDATE  company_business_type_mapping
set company_business_type_code = @小微类
where company_business_type_code = @小微企业贷;

SELECT @电子保函 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '电子保函');
SELECT @保函类 := (select id  from oa_data_manage where data_name = '保函类' and first_data_code = 'business_type');
UPDATE  company_business_type_mapping
set company_business_type_code = @保函类
where company_business_type_code = @电子保函;

SELECT @不良出表 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '不良出表');
SELECT @不良出表类 := (select id  from oa_data_manage where data_name = '不良出表类' and first_data_code = 'business_type');
UPDATE  company_business_type_mapping
set company_business_type_code = @不良出表类
where company_business_type_code = @不良出表;


##项目立项修改
alter table xmgl_project_company_relevance
    modify unit_type varchar(100) null comment '公司类型';
update xmgl_project_company_relevance
set unit_type = 'cust'
where unit_type = '0';

update xmgl_project_company_relevance
set unit_type = 'partner'
where unit_type = '1';

update xmgl_project_company_relevance
set unit_type = 'fund'
where unit_type = '2';

update xmgl_project_company_relevance
set unit_type = 'other'
where unit_type = '3';


SELECT @房抵 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '房抵');
SELECT @房产抵押担保 := (select id  from oa_data_manage where data_name = '房产抵押担保' and first_data_code = 'business_type');
UPDATE  xmgl_project_type_relevance
set type_id = @房产抵押担保
where type_id = @房抵;

SELECT @房抵阶段担保 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '房抵阶段担保');
SELECT @房抵阶段性担保 := (select id  from oa_data_manage where data_name = '房抵阶段性担保' and first_data_code = 'business_type');
UPDATE  xmgl_project_type_relevance
set type_id = @房抵阶段性担保
where type_id = @房抵阶段担保;

SELECT @车贷 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '车贷');
SELECT @车辆抵押担保 := (select id  from oa_data_manage where data_name = '车辆抵押担保'  and first_data_code = 'business_type');
UPDATE  xmgl_project_type_relevance
set type_id = @车辆抵押担保
where type_id = @车贷;

SELECT @车贷再保理 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '车贷再保理');
SELECT @车贷再保理担保 := (select id from oa_data_manage where data_name = '车贷再保理担保' and first_data_code = 'business_type');
UPDATE  xmgl_project_type_relevance
set type_id = @车贷再保理担保
where type_id = @车贷再保理;

SELECT @供应链 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '供应链');
SELECT @TMC差旅担保 := (select id from oa_data_manage where data_name = 'TMC差旅担保' and first_data_code = 'business_type');
UPDATE  xmgl_project_type_relevance
set type_id = @TMC差旅担保
where type_id = @供应链;

SELECT @消金 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '消金');
SELECT @现金贷担保 := (select id from oa_data_manage where data_name = '现金贷担保' and first_data_code = 'business_type');
UPDATE  xmgl_project_type_relevance
set type_id = @现金贷担保
where type_id = @消金;

SELECT @教育分期 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '教育分期');
SELECT @教育分期担保 := (select id from oa_data_manage where data_name = '教育分期担保' and first_data_code = 'business_type');
UPDATE  xmgl_project_type_relevance
set type_id = @教育分期担保
where type_id = @教育分期;

SELECT @保函 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '保函');
SELECT @投标担保 := (select id from oa_data_manage where data_name = '投标担保' and first_data_code = 'business_type');
UPDATE  xmgl_project_type_relevance
set type_id = @投标担保
where type_id = @保函;

SELECT @房产交易担保支付 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '房产交易担保支付');
SELECT @房产交易支付担保 := (select id from oa_data_manage where data_name = '房产交易支付担保' and first_data_code = 'business_type');
UPDATE  xmgl_project_type_relevance
set type_id = @房产交易支付担保
where type_id = @房产交易担保支付;

# 新增五个
SELECT @车分期 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '车分期');
SELECT @购车分期担保 := (select id  from oa_data_manage where data_name = '购车分期担保' and first_data_code = 'business_type');
UPDATE xmgl_project_type_relevance
set type_id = @购车分期担保
where type_id = @车分期;

SELECT @农机 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '农机');
SELECT @农机车担保 := (select id  from oa_data_manage where data_name = '农机车担保' and first_data_code = 'business_type');
UPDATE xmgl_project_type_relevance
set type_id = @农机车担保
where type_id = @农机;

SELECT @小微企业贷 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '小微企业贷');
SELECT @信用贷担保 := (select id  from oa_data_manage where data_name = '信用贷担保' and first_data_code = 'business_type');
UPDATE xmgl_project_type_relevance
set type_id = @信用贷担保
where type_id = @小微企业贷;

SELECT @电子保函 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '电子保函');
SELECT @投标担保 := (select id  from oa_data_manage where data_name = '投标担保' and first_data_code = 'business_type');
UPDATE xmgl_project_type_relevance
set type_id = @投标担保
where type_id = @电子保函;

SELECT @不良出表 := (select dict_code from  sys_dict_data where dict_type = 'company_business_type' and dict_label = '不良出表');
SELECT @金融机构出表 := (select id  from oa_data_manage where data_name = '金融机构出表' and first_data_code = 'business_type');
UPDATE xmgl_project_type_relevance
set type_id = @金融机构出表
where type_id = @不良出表;

# 业务类型
SELECT @通道业务dict := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '通道业务');
SELECT @通道业务data := (select id from oa_data_manage where data_name = '通道业务' and first_data_code = 'project_type');
UPDATE xmgl_project_type_relevance
set type_id = @通道业务data
where type_id = @通道业务dict;

SELECT @分润业务 := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '分润业务');
SELECT @实担业务 := (select id from oa_data_manage where data_name = '实担业务' and first_data_code = 'project_type');
UPDATE xmgl_project_type_relevance
set type_id = @实担业务
where type_id = @分润业务;

SELECT @保函业务 := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '保函业务');
SELECT @非融资性担保业务 := (select id from oa_data_manage where data_name = '非融资性担保业务' and first_data_code = 'project_type');
UPDATE xmgl_project_type_relevance
set type_id = @非融资性担保业务
where type_id = @保函业务;

SELECT @法催业务 := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '法催业务');
SELECT @不良资产处置业务 := (select id from oa_data_manage where data_name = '不良资产处置业务' and first_data_code = 'project_type');
UPDATE xmgl_project_type_relevance
set type_id = @不良资产处置业务
where type_id = @法催业务;


###财务项目
SELECT @通道业务dict := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '通道业务');
SELECT @通道业务data := (select id from oa_data_manage where data_name = '通道业务' and first_data_code = 'project_type');
UPDATE cw_project
set project_type_relevance_type_id = @通道业务data
where project_type_relevance_type_id = @通道业务dict;

SELECT @分润业务 := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '分润业务');
SELECT @实担业务 := (select id from oa_data_manage where data_name = '实担业务' and first_data_code = 'project_type');
UPDATE cw_project
set project_type_relevance_type_id = @实担业务
where project_type_relevance_type_id = @分润业务;

SELECT @保函业务 := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '保函业务');
SELECT @非融资性担保业务 := (select id from oa_data_manage where data_name = '非融资性担保业务' and first_data_code = 'project_type');
UPDATE cw_project
set project_type_relevance_type_id = @非融资性担保业务
where project_type_relevance_type_id = @保函业务;

SELECT @法催业务 := (select dict_code from  sys_dict_data where dict_type = 'project_type' and dict_label = '法催业务');
SELECT @不良资产处置业务 := (select id from oa_data_manage where data_name = '不良资产处置业务' and first_data_code = 'project_type');
UPDATE cw_project
set project_type_relevance_type_id = @不良资产处置业务
where project_type_relevance_type_id = @法催业务;
