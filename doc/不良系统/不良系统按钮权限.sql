SELECT @不良系统 := (select menu_id from sys_menu where menu_name = '不良系统');

SELECT @金融产品配置 := (select menu_id from sys_menu where menu_name = '金融产品配置' and parent_id = @不良系统);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('查看', @金融产品配置, 2, '', null, null, 1, 0, 'F', '0', '0', 'badSystem:financialProductConfiguration:view', '#', 'admin', '2025-02-17 11:08:44', 'admin', '2025-02-17 11:08:55', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('修改', @金融产品配置, 3, '', null, null, 1, 0, 'F', '0', '0', 'badSystem:financialProductConfiguration:update', '#', 'admin', '2025-02-17 11:09:11', 'admin', '2025-02-17 11:21:51', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('删除', @金融产品配置, 4, '', null, null, 1, 0, 'F', '0', '0', 'badSystem:financialProductConfiguration:delete', '#', 'admin', '2025-02-17 11:09:32', 'admin', '2025-02-17 11:09:40', '');

SELECT @借据导入规则 := (select menu_id from sys_menu where menu_name = '借据导入规则' and parent_id = @不良系统);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('查看', @借据导入规则, 2, '', null, null, 1, 0, 'F', '0', '0', 'badSystem:importPromissoryNotes:view', '#', 'admin', '2025-02-17 11:12:10', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('删除', @借据导入规则, 3, '', null, null, 1, 0, 'F', '0', '0', 'badSystem:importPromissoryNotes:delete', '#', 'admin', '2025-02-17 11:12:22', '', null, '');

SELECT @资产管理 := (select menu_id from sys_menu where menu_name = '资产管理' and parent_id = @不良系统);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('查看', @资产管理, 2, '', null, null, 1, 0, 'F', '0', '0', 'bad:assetManagement:view', '#', 'admin', '2025-02-17 11:12:58', '', null, '');

SELECT @委后还款明细 := (select menu_id from sys_menu where menu_name = '委后还款明细' and parent_id = @不良系统);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('删除', @委后还款明细, 2, '', null, null, 1, 0, 'F', '0', '0', 'bad:repaymentDetailsAfter:delete', '#', 'admin', '2025-02-17 11:13:53', '', null, '');

SELECT @委外分案 := (select menu_id from sys_menu where menu_name = '委外分案' and parent_id = @不良系统);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('查看', @委外分案, 2, '', null, null, 1, 0, 'F', '0', '0', 'bad:outsourecd:view', '#', 'admin', '2025-02-17 11:15:42', '', null, '');

SELECT @渠道业务对账单 := (select menu_id from sys_menu where menu_name = '渠道业务对账单' and parent_id = @不良系统);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('查看', @渠道业务对账单, 2, '', null, null, 1, 0, 'F', '0', '0', 'bad:channelBusiness:view', '#', 'admin', '2025-02-17 11:16:26', '', null, '');

SELECT @财务结算单 := (select menu_id from sys_menu where menu_name = '财务结算单' and parent_id = @不良系统);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('查看', @财务结算单, 2, '', null, null, 1, 0, 'F', '0', '0', 'bad:financialSettlement:view', '#', 'admin', '2025-02-17 11:17:03', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('修改', @财务结算单, 3, '', null, null, 1, 0, 'F', '0', '0', 'bad:financialSettlement:update', '#', 'admin', '2025-02-17 11:17:14', 'admin', '2025-02-17 11:17:21', '');

SELECT @机构管理 := (select menu_id from sys_menu where menu_name = '机构管理' and parent_id = @不良系统);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('查看', @机构管理, 2, '', null, null, 1, 0, 'F', '0', '0', 'bad:organizationalManagement:view', '#', 'admin', '2025-02-17 11:18:25', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('修改', @机构管理, 3, '', null, null, 1, 0, 'F', '0', '0', 'bad:organizationalManagement:update', '#', 'admin', '2025-02-17 11:18:50', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('删除', @机构管理, 4, '', null, null, 1, 0, 'F', '0', '0', 'bad:organizationalManagement:delete', '#', 'admin', '2025-02-17 11:19:05', '', null, '');






