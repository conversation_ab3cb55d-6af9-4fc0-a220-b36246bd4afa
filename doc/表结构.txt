-- auto-generated definition
create table bl_asset_management
(
    id                          bigint auto_increment comment '主键'
        primary key,
    promissory_note_number      varchar(64)                           null comment '借据号',
    borrower_name               varchar(64)                           null comment '借款人姓名',
    id_card                     varchar(20)                           null comment '身份证',
    sex                         char                                  null comment '用户性别（0男 1女 2未知）',
    hometown                    varchar(64)                           null comment '户籍所在地',
    phone_number                varchar(13)                           null comment '联系人电话',
    current_address             varchar(100)                          null comment '借款人现住址',
    company_product_id          bigint                                null comment '产品',
    loan_amount                 decimal(18, 2)                        null comment '借款金额',
    interest_rate_year          decimal(10, 4)                        null comment '利率(年化)',
    loan_disbursement_date      datetime                              null comment '放款日',
    due_date                    datetime                              null comment '到期日',
    remaining_principal         decimal(18, 2)                        null comment '金额(剩余本金)',
    cumulative_repayment_amount decimal(18, 2)                        null comment '客户累计实还本金',
    loan_interest               decimal(18, 2)                        null comment '借款利息',
    loan_penalty_interest       decimal(18, 2)                        null comment '借款罚息',
    remaining_due               decimal(18, 2)                        null comment '剩余应还本息',
    overdue_interest            decimal(18, 2)                        null comment '逾期利息',
    compensatory_interest       decimal(18, 2)                        null comment '代偿利息',
    guarantee_interest          decimal(18, 2)                        null comment '担保利息',
    periods                     decimal(5)                            null comment '期数',
    overdue_days                decimal(6)                            null comment '逾期天数',
    accumulated_compensation    decimal(18, 2)                        null comment '累计代偿本金',
    overdue_repayment           varchar(3)                            null comment '用户逾期还款',
    compensatory_repayment      decimal(18, 2)                        null comment '代偿还款',
    loan_contract               varchar(200)                          null comment '借款合同',
    is_overdue                  varchar(10)                           null comment '是否逾期',
    customer_repayment_way      varchar(10)                           null comment '客户还款方式',
    first_day_overdue           datetime                              null comment '用户逾期首日',
    usage_loan                  varchar(10)                           null comment '借款用途',
    loan_platform               varchar(100)                          null comment '借款平台',
    creditor_institutions_id    bigint                                null comment '债权机构',
    import_interest_calculation datetime                              null comment '开始计息日(导入后)',
    outsourced_project_id       bigint                                null comment '关联委外方案',
    case_status                 varchar(10) default '1'               null comment '匹配案件状态(1.未匹配 2.已匹配)',
    create_by                   varchar(64)                           null comment '创建者',
    create_time                 datetime    default CURRENT_TIMESTAMP null comment '创建时间',
    update_by                   varchar(64)                           null comment '更新者',
    update_time                 datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '不良系统-资产管理' row_format = DYNAMIC;


create table bl_outsourced_repayment_detail
(
    id                        bigint auto_increment comment '主键'
        primary key,
    loan_bill                 varchar(64)                        null comment '借款单',
    outsourced_project_number varchar(64)                        null comment '分案批次',
    borrower_name             varchar(64)                        null comment '借款人姓名',
    phone_number              varchar(13)                        null comment '联系人电话',
    repayment_amount          decimal(18, 2)                     null comment '还款金额',
    repayment_time            datetime default CURRENT_TIMESTAMP null comment '还款时间',
    serial_number             varchar(64)                        null comment '流水号',
    trustee_institution_id    bigint                             null comment '受托机构',
    check_status              varchar(10)                        null comment '核对状态',
    service_amount            decimal(18, 2)                     null comment '服务费用金额',
    remark                    varchar(500)                       null comment '流水号',
    create_by                 varchar(64)                        null comment ' 创建者',
    create_time               datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by                 varchar(64)                        null comment '更新者',
    update_time               datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '不良系统-委外还款明细' row_format = DYNAMIC;
