<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cwproject.mapper.CwProjectIncomeMapper">

    <resultMap type="org.ruoyi.core.cwproject.domain.CwProjectIncome" id="CwProjectIncomeResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="term"    column="term"    />
        <result property="termMonth"    column="term_month"    />
        <result property="termBegin"    column="term_begin"    />
        <result property="termEnd"    column="term_end"    />
        <result property="incomeAmt"    column="income_amt"    />
        <result property="grossProfitAmt"    column="gross_profit_amt"    />
        <result property="grossProfitAmt2"    column="gross_profit_amt2"    />
        <result property="feeAmt"    column="fee_amt"    />
        <result property="unfeeAmt"    column="unfee_amt"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="incomeFlag"    column="income_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="phaseStatus"    column="phase_status"    />
        <result property="phaseFlag"    column="phase_flag"    />
        <result property="phaseId"    column="phase_id"    />
        <result property="rejectionFlag"    column="rejection_flag"    />
        <result property="incomeRejectionReason"    column="income_rejection_reason"    />
        <result property="feeRejectionReason"    column="fee_rejection_reason"    />
        <result property="collectionTime"    column="collection_time"    />
        <result property="trueComeAmt"    column="true_come_amt"    />
        <result property="serviceFee"    column="service_fee"    />
    </resultMap>

    <sql id="selectCwProjectIncomeVo">
        select id, project_id, term, term_month, term_begin, term_end, income_amt, gross_profit_amt, gross_profit_amt2, fee_amt, unfee_amt, remark, status, income_flag, create_by, create_time, update_by, update_time, phase_status,rejection_flag,income_rejection_reason,fee_rejection_reason,collection_time,phase_id,true_come_amt,service_fee from cw_project_income
    </sql>

    <select id="selectCwProjectIncomeList" parameterType="org.ruoyi.core.cwproject.domain.CwProjectIncome" resultMap="CwProjectIncomeResult">
        <include refid="selectCwProjectIncomeVo"/>
        <where>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="term != null  and term != ''"> and term = #{term}</if>
            <if test="termMonth != null  and termMonth != ''"> and term_month = #{termMonth}</if>
            <if test="termBegin != null "> and term_begin = #{termBegin}</if>
            <if test="termEnd != null "> and term_end = #{termEnd}</if>
            <if test="incomeAmt != null "> and income_amt = #{incomeAmt}</if>
            <if test="grossProfitAmt != null "> and gross_profit_amt = #{grossProfitAmt}</if>
            <if test="grossProfitAmt2 != null "> and gross_profit_amt2 = #{grossProfitAmt2}</if>
            <if test="feeAmt != null "> and fee_amt = #{feeAmt}</if>
            <if test="unfeeAmt != null "> and unfee_amt = #{unfeeAmt}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="incomeFlag != null  and incomeFlag != ''"> and income_flag = #{incomeFlag}</if>
            <if test="phaseStatus != null  and phaseStatus != ''"> and phase_status = #{phaseStatus}</if>
        </where>
    </select>

    <select id="selectCwProjectIncomeById" parameterType="Long" resultMap="CwProjectIncomeResult">
        <include refid="selectCwProjectIncomeVo"/>
        where id = #{id}
    </select>

    <insert id="insertCwProjectIncome" parameterType="org.ruoyi.core.cwproject.domain.CwProjectIncome" useGeneratedKeys="true" keyProperty="id">
        insert into cw_project_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="term != null and term != ''">term,</if>
            <if test="termMonth != null">term_month,</if>
            <if test="termBegin != null">term_begin,</if>
            <if test="termEnd != null">term_end,</if>
            <if test="incomeAmt != null">income_amt,</if>
            <if test="grossProfitAmt != null">gross_profit_amt,</if>
            <if test="grossProfitAmt2 != null">gross_profit_amt2,</if>
            <if test="feeAmt != null">fee_amt,</if>
            <if test="unfeeAmt != null">unfee_amt,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="incomeFlag != null">income_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="phaseStatus != null">phase_status,</if>
            <if test="phaseFlag != null and phaseFlag != ''">phase_flag</if>
            <if test="collectionTime != null">collection_time</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="term != null and term != ''">#{term},</if>
            <if test="termMonth != null">#{termMonth},</if>
            <if test="termBegin != null">#{termBegin},</if>
            <if test="termEnd != null">#{termEnd},</if>
            <if test="incomeAmt != null">#{incomeAmt},</if>
            <if test="grossProfitAmt != null">#{grossProfitAmt},</if>
            <if test="grossProfitAmt2 != null">#{grossProfitAmt2},</if>
            <if test="feeAmt != null">#{feeAmt},</if>
            <if test="unfeeAmt != null">#{unfeeAmt},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="incomeFlag != null">#{incomeFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="phaseStatus != null">#{phaseStatus},</if>
            <if test="phaseFlag != null and phaseFlag != ''">#{phaseFlag,jdbcType=VARCHAR},</if>
            <if test="collectionTime != null">#{collectionTime,jdbcType=TIMESTAMP},</if>
         </trim>
    </insert>

    <update id="updateCwProjectIncome" parameterType="org.ruoyi.core.cwproject.domain.CwProjectIncome">
        update cw_project_income
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="term != null and term != ''">term = #{term},</if>
            <if test="termMonth != null">term_month = #{termMonth},</if>
            <if test="termBegin != null">term_begin = #{termBegin},</if>
            <if test="termEnd != null">term_end = #{termEnd},</if>
            <if test="incomeAmt != null">income_amt = #{incomeAmt},</if>
            <if test="grossProfitAmt != null">gross_profit_amt = #{grossProfitAmt},</if>
            <if test="grossProfitAmt2 != null">gross_profit_amt2 = #{grossProfitAmt2},</if>
            <if test="feeAmt != null">fee_amt = #{feeAmt},</if>
            <if test="unfeeAmt != null">unfee_amt = #{unfeeAmt},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="incomeFlag != null">income_flag = #{incomeFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="phaseStatus != null">phase_status = #{phaseStatus},</if>
            <if test="rejectionFlag != null">rejection_flag = #{rejectionFlag,jdbcType=VARCHAR},</if>
<!--            <if test="incomeRejectionTime != null">income_rejection_time = #{incomeRejectionTime,jdbcType=TIMESTAMP},</if>-->
<!--            <if test="incomeRejectionUser != null and incomeRejectionUser != ''">income_rejection_user = #{incomeRejectionUser,jdbcType=VARCHAR},</if>-->
            <if test="incomeRejectionReason != null and incomeRejectionReason != ''">income_rejection_reason = #{incomeRejectionReason,jdbcType=VARCHAR},</if>
<!--            <if test="feeRejectionTime != null">fee_rejection_time = #{feeRejectionTime,jdbcType=TIMESTAMP},</if>-->
<!--            <if test="feeRejectionUser != null and feeRejectionUser != ''">fee_rejection_user = #{feeRejectionUser,jdbcType=VARCHAR},</if>-->
            <if test="feeRejectionReason != null and feeRejectionReason != ''">fee_rejection_reason = #{feeRejectionReason,jdbcType=VARCHAR},</if>
            <if test="collectionTime != null">collection_time = #{collectionTime,jdbcType=TIMESTAMP},</if>
            <if test="collectionTime == null">
                <if test="rejectionIncomeAndChangeIncome != null and rejectionIncomeAndChangeIncome != ''">
                    collection_time = #{collectionTime,jdbcType=TIMESTAMP},
                </if>
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCwProjectIncomeById" parameterType="Long">
        delete from cw_project_income where id = #{id}
    </delete>

    <delete id="deleteCwProjectIncomeByIds" parameterType="String">
        delete from cw_project_income where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCwProjectIncomeListFlagZero" resultType="map">
<!--        SELECT GROUP_CONCAT(i.id) id,i.project_id,p.project_name,p.cust_name,p.income_cust_name-->
<!--        FROM cw_project p,cw_project_income i WHERE p.id=i.project_id AND i.project_id=#{projectId,jdbcType=BIGINT} AND i.phase_status='0' AND i.status='0' AND p.project_flag='0' GROUP BY i.project_id-->
        SELECT opd.project_type,case when i.rejection_flag='1' then '1' else '' end as rejection_flag,i.phase_status as phase_status,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
         WHERE p.status='0' AND i.project_id=#{projectId,jdbcType=BIGINT} AND (i.phase_status='0' OR (i.phase_status='2' AND i.rejection_flag='1')) AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC
<!--    TODO 原来-->
<!--        SELECT p.project_type,case when i.rejection_flag='1' then '1' else '' end as rejection_flag,i.phase_status as phase_status,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,-->
<!--        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare-->
<!--        FROM cw_project p,cw_project_income i-->
<!--         WHERE p.id=i.project_id AND i.project_id=#{projectId,jdbcType=BIGINT} AND (i.phase_status='0' OR (i.phase_status='2' AND i.rejection_flag='1')) AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC-->
    </select>

    <select id="selectCwProjectIncomeListFlagOne" resultType="map">
<!--        SELECT GROUP_CONCAT(i.id) id,i.project_id,p.project_name,p.cust_name,p.income_cust_name-->
<!--        FROM cw_project p,cw_project_income i WHERE p.id=i.project_id AND i.project_id=#{projectId,jdbcType=BIGINT} AND i.phase_status='1' AND i.status='0' AND p.project_flag='0' GROUP BY i.project_id-->
        SELECT opd.project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
         WHERE i.project_id=#{projectId,jdbcType=BIGINT} AND i.phase_status='1' AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC

<!--         SELECT p.project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,-->
<!--        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare-->
<!--        FROM cw_project p,cw_project_income i-->
<!--         WHERE p.id=i.project_id AND i.project_id=#{projectId,jdbcType=BIGINT} AND i.phase_status='1' AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC-->
    </select>

    <select id="selectCwProjectIncomeListFlagTwo" resultMap="CwProjectIncomeResult">
        SELECT SUM(income_amt) income_amt,SUM(gross_profit_amt) gross_profit_amt,SUM(gross_profit_amt2) gross_profit_amt2,SUM(fee_amt) fee_amt,SUM(unfee_amt) unfee_amt,
        (SELECT COUNT(*) FROM cw_project_income WHERE phase_status!='2' AND status='0' AND project_id=#{projectId,jdbcType=BIGINT}
        <if test="sumFlag != null">
            <if test="startDate != null and startDate != ''">
                AND term_month &gt;= #{startDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                AND term_month &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </if>) phase_status
        FROM cw_project_income WHERE status='0' AND project_id=#{projectId,jdbcType=BIGINT}
        <if test="sumFlag != null">
            <if test="startDate != null and startDate != ''">
                AND term_month &gt;= #{startDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                AND term_month &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </if>
    </select>

    <select id="selectCwProjectFeeDetailsByProjectId" resultMap="CwProjectIncomeResult">
        SELECT SUM(i.fee_amt) fee_amt,SUM(i.unfee_amt) unfee_amt FROM cw_project p,cw_project_income i WHERE p.id=i.project_id AND i.project_id=#{projectId,jdbcType=BIGINT}
    </select>

    <select id="selectCwProjectIncomeListById" resultMap="CwProjectIncomeResult">
        SELECT fee_amt,unfee_amt FROM cw_project_income WHERE id=#{id,jdbcType=BIGINT}
    </select>

    <select id="selectcwprojectincomeListByProjectId" resultMap="CwProjectIncomeResult">
        SELECT i.id, i.project_id, i.term, i.term_month, i.term_begin, i.term_end, i.income_amt, i.gross_profit_amt, i.gross_profit_amt2,
        i.fee_amt, i.unfee_amt, i.phase_status, i.remark, i.income_flag FROM cw_project_income i,cw_project p WHERE i.status='0' AND p.status='0'
        AND i.project_id=p.id
        <if test="sumFlag == null">
            AND p.project_flag = '1'
        </if>
        <if test="sumFlag != null ">
            AND p.project_flag='0'
        </if>
        AND i.project_id=#{projectId,jdbcType=BIGINT}
    </select>

    <select id="selectCwProjectIncomeListFlagOneByAdmin" resultType="map">
<!--        SELECT GROUP_CONCAT(i.id) id,i.project_id,p.project_name,p.cust_name,p.income_cust_name-->
<!--        FROM cw_project p,cw_project_income i WHERE p.id=i.project_id AND i.phase_status='1' AND i.status='0' AND p.project_flag='0' GROUP BY i.project_id-->
        SELECT opd.project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
        WHERE i.phase_status='1' AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC
        <!--TODO 原来
        SELECT p.project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p,cw_project_income i
        WHERE p.id=i.project_id AND i.phase_status='1' AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC-->
    </select>

    <select id="selectCwProjectIncomeListFlagZeroByAdmin" resultType="map">
<!--        SELECT GROUP_CONCAT(i.id) id,i.project_id,p.project_name,p.cust_name,p.income_cust_name-->
<!--        FROM cw_project p,cw_project_income i WHERE p.id=i.project_id AND i.phase_status='0' AND i.status='0' AND p.project_flag='0' GROUP BY i.project_id-->
        SELECT opd.project_type,case when i.rejection_flag='1' then '1' else '' end as rejection_flag,i.phase_status as phase_status,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
        WHERE (i.phase_status='0' OR (i.phase_status='2' AND i.rejection_flag='1')) AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC

        <!--TODO 原来
        SELECT p.project_type,case when i.rejection_flag='1' then '1' else '' end as rejection_flag,i.phase_status as phase_status,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p,cw_project_income i
        WHERE p.id=i.project_id AND (i.phase_status='0' OR (i.phase_status='2' AND i.rejection_flag='1')) AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC-->
    </select>

    <select id="selectcwprojectincomeListAllByProjectId" resultMap="CwProjectIncomeResult">
        SELECT id, project_id, term,
        case when term='0' then CONCAT(substring(term_month,1,4),'年',substring(term_month,6,2),'月') when term='1' then CONCAT(date_format(term_begin,'%Y.%m.%d'),'-', date_format(term_end,'%Y.%m.%d'))
          else '' end as term_month, term_begin, term_end, income_amt, gross_profit_amt, gross_profit_amt2,
          case when phase_status='0' then '待录入'
            when phase_status='1' then '待确认'
            when phase_status='2'  and rejection_flag='1' then '业务驳回'
            when phase_status='2' and (rejection_flag='' or rejection_flag=null) then '已完成'
             else '' end as phase_status,
        fee_amt, unfee_amt, remark FROM cw_project_income WHERE status='0'
        AND project_id=#{projectId,jdbcType=BIGINT} AND phase_status!='0'
    </select>
    <select id="selectCwProjectIncomeListAll" resultMap="CwProjectIncomeResult">
        select * from cw_project_income
    </select>


    <resultMap id="pro" type="org.ruoyi.core.cwproject.domain.CwProjectAck">
        <result property="projectId"    column="project_id" />
        <result property="id"    column="ack_id" />
    </resultMap>

    <select id="selectCwProjectIncomeByProjectId" resultMap="pro">
        SELECT
            cpar.ack_id,
            cpa.project_id,
            SUM(cpi.gross_profit_amt2) AS aggregateAnmtmt
        FROM
            cw_project_ack cpa
                LEFT JOIN cw_project_ack_ref cpar ON cpa.id = cpar.ack_id
                LEFT JOIN cw_project_income cpi ON cpar.project_income_id = cpi.id
        WHERE
            cpa.ack_flag = '0'
          AND cpa.project_id = #{projectId}
          AND ((cpi.phase_status = '2' AND (cpi.rejection_flag='' OR cpi.rejection_flag IS NULL)) OR cpi.phase_status = '4')
<!--          AND cpi.phase_status = '6'-->
        GROUP BY cpa.project_id
        HAVING aggregateAnmtmt  &gt;= 50000
    </select>

    <select id="selectCwProjectIncomeTermByProjectId" resultMap="CwProjectIncomeResult">
<!--        SELECT id,DATE_FORMAT(term_begin,'%Y-%m') term_begin,DATE_FORMAT(term_end,'%Y-%m') term_end FROM cw_project_income WHERE project_id=#{projectId,jdbcType=BIGINT} AND term='1'-->
        SELECT id,term,term_begin,term_end FROM cw_project_income WHERE project_id=#{projectId,jdbcType=BIGINT} AND term='1'
    </select>

    <select id="selectCwProjectIncomeAllTermByProjectId" resultMap="CwProjectIncomeResult">
        SELECT id,term,term_begin,term_end FROM cw_project_income WHERE project_id=#{projectId,jdbcType=BIGINT}
    </select>

    <select id="selectCwProjectIncomeByIdAndTime" resultMap="CwProjectIncomeResult">
        <include refid="selectCwProjectIncomeVo"/>
        WHERE status='0' AND id=#{id,jdbcType=BIGINT}
        <if test="sumFlag != null">
            <if test="startDate != null and startDate != ''">
                AND term_begin &gt;= #{startDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                AND term_end &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </if>
    </select>

    <select id="selectCwProjectIncomeAllInfoByProjectId" resultMap="CwProjectIncomeResult">
        <include refid="selectCwProjectIncomeVo"/>
        where project_id = #{projectId,jdbcType=BIGINT} AND status='0'
    </select>

    <select id="selectCwProjectIncomeTerm2ById" resultMap="CwProjectIncomeResult">
        <include refid="selectCwProjectIncomeVo"/>
        WHERE id=#{id,jdbcType=BIGINT}
        <if test="startDate != null and startDate != ''">
            AND term_month &gt;= #{startDate,jdbcType=VARCHAR}
        </if>
        <if test="endDate != null and endDate != ''">
            AND term_month &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectCwProjectIncomeTerm3ById" resultMap="CwProjectIncomeResult">
        <include refid="selectCwProjectIncomeVo"/>
        WHERE id=#{id,jdbcType=BIGINT}
        <if test="startDate != null and startDate != ''">
            AND term_begin &gt;= #{startDate,jdbcType=VARCHAR}
        </if>
        <if test="endDate != null and endDate != ''">
            AND term_end &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
        AND
        PERIOD_DIFF(DATE_FORMAT(term_end,'%Y%m'),DATE_FORMAT(term_begin,'%Y%m'))=0
    </select>

    <select id="selectCwProjectIncomeDiffMonthById" resultType="java.util.Map">
        SELECT id,project_id projectId,term_end termEnd,PERIOD_DIFF(DATE_FORMAT(term_end,'%Y%m'),DATE_FORMAT(term_begin,'%Y%m')) monthDiff FROM cw_project_income WHERE id=#{id,jdbcType=BIGINT}
        AND PERIOD_DIFF(DATE_FORMAT(term_end,'%Y%m'),DATE_FORMAT(term_begin,'%Y%m'))!=0
    </select>

    <select id="selectCwProjectIncomeDiffMonthListByTermEndAndMonthDiff" resultType="java.util.Map">
        SELECT DATE_FORMAT(DATE_SUB(#{termEnd,jdbcType=VARCHAR}, INTERVAL d MONTH), '%Y-%m') as month
        FROM (
             select d from (
                   SELECT @xi:=@xi+1 as d from
                           (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) xc1,
                           (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) xc2,
                           (SELECT @xi:=-1) xc0
                   ) as m where d &lt;= #{monthDiff}
        ) dtc
    </select>

    <select id="selectCwProjectIncomePhaseNoFinishCountById" resultType="int">
        SELECT COUNT(*) FROM cw_project_income WHERE phase_status!='2' AND id=#{id,jdbcType=BIGINT}
    </select>
    <select id="selectCwProject" resultMap="CwProjectIncomeResult">
        <include refid="selectCwProjectIncomeVo"/>
        where term = #{term} and project_id = #{projectId} and term_month = #{termMonth} and status = '0'
    </select>

<!--    <select id="selectPhaseLawByProjectId" resultMap="CwProjectIncomeResult">-->
<!--        <include refid="selectCwProjectIncomeVo"/> WHERE project_id=#{projectId,jdbcType=BIGINT} AND phase_flag='0' AND status='0'-->
<!--    </select>-->

    <insert id="insertLawIncome" parameterType="org.ruoyi.core.cwproject.domain.dto.CwProjectIncomeForLawDto" useGeneratedKeys="true" keyProperty="projectIncomeId">
        insert into cw_project_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="term != null and term != ''">term,</if>
            <if test="serviceProviderFlag != null and serviceProviderFlag != ''">service_provider_flag,</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">service_provider,</if>
            <if test="serviceProviderIncome != null">income_amt,</if>
            <if test="serviceProviderSecondName != null and serviceProviderSecondName != ''">service_provider_second,</if>
            <if test="serviceProviderSecondIncome != null">service_provider_second_income,</if>
            <if test="trueComeAmt != null">true_come_amt,</if>
            <if test="serviceFee != null">service_fee,</if>
            <if test="principal != null">principal,</if>
            <if test="phaseFlag != null and phaseFlag != ''">phase_flag,</if>
            <if test="hebingId != null">service_group_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="phaseId != null">phase_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId,jdbcType=BIGINT},</if>
            <if test="term != null and term != ''">#{term,jdbcType=VARCHAR},</if>
            <if test="serviceProviderFlag != null and serviceProviderFlag != ''">#{serviceProviderFlag,jdbcType=VARCHAR},</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">#{serviceProviderName,jdbcType=VARCHAR},</if>
            <if test="serviceProviderIncome != null">#{serviceProviderIncome,jdbcType=DECIMAL},</if>
            <if test="serviceProviderSecondName != null and serviceProviderSecondName != ''">#{serviceProviderSecondName,jdbcType=VARCHAR},</if>
            <if test="serviceProviderSecondIncome != null">#{serviceProviderSecondIncome,jdbcType=DECIMAL},</if>
            <if test="trueComeAmt != null">#{trueComeAmt,jdbcType=DECIMAL},</if>
            <if test="serviceFee != null">#{serviceFee,jdbcType=DECIMAL},</if>
            <if test="principal != null">#{principal,jdbcType=DECIMAL},</if>
            <if test="phaseFlag != null and phaseFlag != ''">#{phaseFlag,jdbcType=VARCHAR},</if>
            <if test="hebingId != null">#{hebingId},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="phaseId != null">#{phaseId,jdbcType=BIGINT},</if>
         </trim>
    </insert>

    <select id="selectLawProjectPhaseInfo" resultType="org.ruoyi.core.cwproject.domain.dto.CwProjectPhaseDto">
    SELECT id,project_id AS projectId,
        case when term='0' then term_month when term='1' then date_format(term_begin,'%Y-%m') else '' end as termMonthCompare,
        case when term='0' then CONCAT(substring(term_month,1,4),'年',substring(term_month,6,2),'月') when term='1' then CONCAT(date_format(term_begin,'%Y.%m.%d'),'-', date_format(term_end,'%Y.%m.%d')) else '' end as termMonth,
        income_amt AS incomeAmt,gross_profit_amt AS grossProfitAmt,gross_profit_amt2 AS grossProfitAmt2,fee_amt AS feeAmt,unfee_amt AS unFeeAmt,remark,
        case when phase_status='7' then '待录入'
             when phase_status='8' then '待确认'
             when phase_status='9' then '待打款'
             when phase_status='10' then '已完成'
		else '' end as phaseStatus FROM cw_project_income WHERE project_id=#{projectId,jdbcType=BIGINT} AND phase_flag='0' AND status='0'
    </select>

    <select id="selectLawCwProjectOverListDetileByProjectIdV2" resultType="org.ruoyi.core.cwproject.domain.view.CwProjectLawDetailView">
    select cpi.service_group_flag AS serviceGroupFlag,cpf.jtfr_amt AS jtfrAmt, cpf.law_profit AS lawProfit,cpi.collection_time AS collectionTime,cpf.fee_flag,cpf.current_fee AS currentFee,cpf.suspend_clear_id AS suspendClearId,cpf.suspend_flag AS suspendFlag,cpi.phase_id AS phaseId,cpi.service_provider_flag AS serviceProviderFlag,cpi.service_provider AS serviceProvider,cpi.service_provider_second AS serviceProviderSecond,cpi.service_provider_second_income AS serviceProviderSecondIncome,
        cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
<!--        ,cpp.id as projectPayId-->
        ,cpc.id projectCustId,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as termMonth,

          cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt, cpi.gross_profit_amt2 as grossProfitAmt2,
<!--          cpi.fee_amt as feeAmtSum, cpi.unfee_amt as unfeeAmtSum,-->
           cpi.remark,
          case when phase_status='7' then '待录入'
             when phase_status='8' then '待确认'
             when phase_status='9' then '已完成'
<!--             when phase_status='10' then '已完成'  -->
             else '' end as phaseStatus
          , cpf.cust_name as custName, cpc.cust_name as feeCustName, cpf.fee_amt as feeAmt, cpf.fee_amt2 as feeAmt2,
					cpi.true_come_amt AS trueComeAmt,cpi.service_fee AS serviceFee,cpi.principal AS principal,cpf.fee_round AS feeRound,
					cpf.reveal_fee_company_id AS revealFeeCompanyId
<!--          , DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,-->
<!--           case when cpp.pay_flag='0' then '未打款'-->
<!--          when cpp.pay_flag='1' then '已打款'-->
<!--          when cpp.pay_flag='2' then '已确认'-->
<!--          when cpf.fee_flag='3' then '不需打款'-->
<!--          when cpf.suspend_flag='1' then '不需打款'-->
<!--          when cpf.suspend_flag='2' then '不需打款'-->
<!--          else '' end as payFlag-->
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
<!--          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')-->
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
         where cpi.project_id = #{id,jdbcType=BIGINT} and cpi.status='0'  order by cpi.project_id,cpi.id,cpf.id
<!--         ,cpp.id-->
    </select>

    <select id="selectLawCwProjectOverListDetileByPhaseIdList" resultType="org.ruoyi.core.cwproject.domain.view.CwProjectLawDetailView">
        SELECT * FROM
        (
        SELECT a.phaseId, a.projectCustId, case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
                  case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
                  else '' end as termMonth, cpi.income_amt, a.feeCustName, a.custName, a.revealFeeCompanyId FROM cw_project_income cpi INNER JOIN (
                select
                cpi.phase_id AS phaseId,
                cpc.id as projectCustId,
                cpf.cust_name as custName,
                cpc.cust_name as feeCustName,
             cpf.reveal_fee_company_id AS revealFeeCompanyId
            from
                cw_project_income cpi
            LEFT JOIN
                cw_project_fee cpf
                    ON (
                        cpf.project_income_id=cpi.id
                        AND  cpf.status='0'
                    )
            LEFT JOIN
                cw_project_cust cpc
                    ON cpf.cust_id=cpc.id
                 where
                 cpi.phase_id in
                  <foreach collection="phaseIdList" item="phaseId" open="(" separator="," close=")">
                 #{phaseId,jdbcType=BIGINT}
                </foreach>
                and cpi.status='0'
                        GROUP BY phaseId
            order by
                cpi.id
        ) a ON cpi.id=a.phaseId
        ) b ORDER BY termMonthCompare DESC
    </select>

    <select id="selectPhaseLawByIncomeId" resultMap="CwProjectIncomeResult">
        <include refid="selectCwProjectIncomeVo"/> WHERE id = (SELECT phase_id FROM cw_project_income WHERE id = #{id,jdbcType=BIGINT})
    </select>

    <select id="selectcwprojectincomeLawListAllByProjectId" resultMap="CwProjectIncomeResult">
        SELECT id, project_id, term,
        case when term='0' then CONCAT(substring(term_month,1,4),'年',substring(term_month,6,2),'月') when term='1' then CONCAT(date_format(term_begin,'%Y.%m.%d'),'-', date_format(term_end,'%Y.%m.%d'))
          else '' end as term_month, term_begin, term_end, income_amt, gross_profit_amt, gross_profit_amt2,
          case when phase_status='7' then '待录入'
             when phase_status='8' then '待确认'
             when phase_status='9' then '已完成' else '' end as phase_status,
        fee_amt, unfee_amt, remark,phase_flag,phase_id FROM cw_project_income WHERE status='0'
        AND project_id=#{projectId,jdbcType=BIGINT} AND phase_status!='7'
    </select>

    <select id="selectCwProjectLawIncomePhaseNoFinishCountById" resultType="int">
        SELECT COUNT(*) FROM cw_project_income WHERE phase_status!='9' AND id=#{id,jdbcType=BIGINT}
    </select>

    <select id="findRecentlyCustFeeCompanyAndFeeCompany1" resultType="java.util.Map">
<!--        SELECT f.cust_name AS custName,c.cust_name AS feeName FROM cw_project_income i,cw_project_fee f,cw_project_cust c WHERE i.id=f.project_income_id AND f.cust_id=c.id AND  i.term_month!='' AND i.term_month&lt;'2022-01' AND i.id=1022-->
        SELECT c.id AS custId,i.id,f.cust_name AS custName,c.cust_name AS feeCustName FROM cw_project_income i,cw_project_fee f,cw_project_cust c WHERE i.id=f.project_income_id AND f.cust_id=c.id AND i.phase_id=#{phaseId,jdbcType=BIGINT} LIMIT 1
    </select>

    <select id="findRecentlyCustFeeCompanyAndFeeCompany2" resultType="java.util.Map">

    </select>

    <select id="findLawRecentlyPhaseIdByProjectIdAndTremMonth" resultType="java.util.Map">
        SELECT i.id,i.term_month AS termMonth FROM cw_project_income i WHERE i.term_month&lt;#{date,jdbcType=VARCHAR} AND i.term_month!='' AND i.status='0' AND i.project_id=#{projectId,jdbcType=BIGINT} ORDER BY i.term_month DESC LIMIT 1
    </select>

    <select id="findLawRecentlyPhaseIdByProjectIdAndTremMonthTermOne" resultType="java.util.Map">
        SELECT i.id,DATE_FORMAT(i.term_begin,'%Y-%m') AS termMonth FROM cw_project_income i WHERE DATE_FORMAT(i.term_begin,'%Y-%m')&lt;#{date,jdbcType=VARCHAR} AND i.status='0' AND i.project_id=#{projectId,jdbcType=BIGINT} ORDER BY i.term_month DESC LIMIT 1
    </select>

    <select id="selectLawSuspendFlagIsOneSumByServiceProviderName" resultType="java.util.Map">
<!--       下面的是细分到二级服务商的-->
        SELECT GROUP_CONCAT(f.id) AS id,i.service_provider_flag AS serviceProviderFlag,i.service_provider AS serviceProvider,i.service_provider_second AS serviceProviderSecond,SUM(f.current_fee) AS feeAmtSuspendFlagIsOne
        FROM cw_project_fee f LEFT JOIN cw_project_income i ON i.id=f.project_income_id WHERE f.project_income_id IN (
            SELECT id FROM cw_project_income WHERE phase_id IN(SELECT phase_id FROM cw_project_income WHERE service_provider IN
                <foreach collection="list" separator="," item="item" open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            )
        ) AND i.status='0' AND f.suspend_flag='1' GROUP BY i.service_provider,i.service_provider_second
<!--        下面的是只有一级服务商的-->
<!--        SELECT i.service_provider AS serviceProvider,SUM(f.fee_amt) AS feeAmtSuspendFlagIsOne FROM cw_project_fee f LEFT JOIN cw_project_income i ON i.id=f.project_income_id-->
<!--        WHERE f.project_income_id IN (-->
<!--        SELECT id FROM cw_project_income WHERE service_provider IN-->
<!--        <foreach collection="list" separator="," item="item" open="(" close=")">-->
<!--            #{item,jdbcType=VARCHAR}-->
<!--        </foreach>-->
<!--        ) AND i.status='0' AND f.suspend_flag='1' GROUP BY i.service_provider-->
    </select>
    <select id="selectLawCwProjectIncomeListFlagSevenByAdmin" resultType="java.util.Map">
        SELECT opd.project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
        WHERE i.phase_status='7' AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC
    </select>

    <select id="selectLawCwProjectIncomeListFlagSeven" resultType="java.util.Map">
        SELECT opd.project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
        WHERE i.project_id=#{projectId,jdbcType=BIGINT} AND i.phase_status='7' AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC
    </select>

    <select id="selectLawCwProjectIncomeListFlagEightByAdmin" resultType="java.util.Map">
        SELECT opd.project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
        WHERE i.phase_status='8' AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC
    </select>

    <select id="selectLawCwProjectIncomeListFlagEight" resultType="java.util.Map">
        SELECT opd.project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
         WHERE i.project_id=#{projectId,jdbcType=BIGINT} AND i.phase_status='8' AND i.status='0' AND p.project_flag='0' ORDER BY termMonthCompare DESC
    </select>

    <select id="selectLawIncomeByPhaseId" resultMap="CwProjectIncomeResult">
        <include refid="selectCwProjectIncomeVo"/> WHERE phase_id=#{phaseId,jdbcType=BIGINT}
    </select>

    <update id="updateLawCwProjectIncome" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update cw_project_income
                <set>
                    <if test="item.status != null">status = #{item.status,jdbcType=VARCHAR},</if>
    <!--                <if test="item.updateBy != null">update_by = #{item.updateBy},</if>-->
                    <if test="item.updateTime != null">update_time = #{item.updateTime,jdbcType=TIMESTAMP},</if>
                </set>
            where id = #{item.id}
        </foreach>
    </update>

    <select id="selectSuspendAmtByFeeId" resultType="java.math.BigDecimal">
        SELECT SUM(current_fee) FROM cw_project_fee WHERE suspend_clear_id=#{projectFeeId,jdbcType=BIGINT}
    </select>

    <select id="selectCwprojectFeeIdListByPhase" resultType="java.lang.Long">
        SELECT f.id AS feeId FROM cw_project_income i LEFT JOIN cw_project_fee f ON i.id=f.project_income_id WHERE phase_id=#{phaseId,jdbcType=BIGINT}
    </select>

    <delete id="deleteCwProjectIncomeByPhaseId" parameterType="java.lang.Long">
        DELETE FROM cw_project_income WHERE phase_id=#{phaseId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteCwProjectFeeByFeeIds" parameterType="java.util.List">
        DELETE FROM cw_project_fee WHERE id IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>

    <select id="selectFeeNoAlreadyQueryDetailByQueryTime" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo">
        select case when cpf.actually_pay_fee_amt IS NULL then 0 when cpf.actually_pay_fee_amt IS NOT NULL then cpf.actually_pay_fee_amt else '' end as actuallyPayFeeAmt,cpi.income_rejection_reason AS incomeRejectionReason,cpi.fee_rejection_reason AS feeRejectionReason,opd.project_type AS projectType,cpi.id as phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId,cpp.id as projectPayId,cpc.id projectCustId,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as feeNoAlreadyTerm,cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,cpf.fee_amt as feeAmt,cpi.fee_amt as feeAmtSum
					, cpi.unfee_amt as unfeeAmtSum, cpi.remark  ,cpf.cust_name as custName, cpc.cust_name as feeCustName, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate
					, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,
           case when cpp.pay_flag='0' then '未打款'
          when cpp.pay_flag='1' then '已打款'
          when cpp.pay_flag='2' then '已确认'
          else '' end as payFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
						LEFT JOIN(SELECT project_id,income_id,DATE_FORMAT(MAX(dynamic_time),'%Y-%m-%d') AS dynamic_time FROM cw_project_dynamic WHERE status='0' AND phase_status='1' GROUP BY income_id) cpd ON cpi.id=cpd.income_id
						WHERE
						cp.project_flag='0'
						<if test="projectType != null and projectType != ''">
						    AND opd.project_type=#{projectType,jdbcType=VARCHAR}
						</if>
						<if test="projectType == ''">
						    AND (opd.project_type='0' OR opd.project_type='2' OR opd.project_type='3')
                        </if>
						AND cp.status='0' AND cpi.status='0' AND cpi.phase_status='2' AND cpd.dynamic_time&lt;=#{queryTime,jdbcType=VARCHAR})) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.unfee_amt>0 order by cpi.project_id,cpi.id,cpf.id,cpp.id
    </select>

    <select id="selectLawFeeNoAlreadyQueryDetailByQueryTime" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo">
        select cpf.fee_round AS feeRound, cpf.law_profit AS lawProfit, opd.project_type AS projectType,cpi.phase_id AS phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId,cpp.id as projectPayId,cpc.id projectCustId
				,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as feeNoAlreadyTerm,
          cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,
          cpi.fee_amt as feeAmtSum, cpi.unfee_amt as unfeeAmtSum, cpi.remark, cpf.cust_name as custName, cpc.cust_name as feeCustName, cpf.fee_amt as feeAmt, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,
           case when cpp.pay_flag='0' then '未打款'
          when cpp.pay_flag='1' then '已打款'
          when cpp.pay_flag='2' then '已确认'
          when cpf.fee_flag='3' then '不需打款'
          when cpf.suspend_flag='1' then '不需打款'
          when cpf.suspend_flag='2' then '不需打款'
          else '' end as payFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN(SELECT project_id,income_id,DATE_FORMAT(MAX(dynamic_time),'%Y-%m-%d') AS dynamic_time FROM cw_project_dynamic WHERE status='0' AND phase_status='7' GROUP BY income_id) cpd ON cpi.id=cpd.income_id
						WHERE cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND cpi.phase_status='9' AND cpd.dynamic_time&lt;=#{queryTime,jdbcType=VARCHAR})) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.income_amt>0 order by cpi.project_id,cpi.id,cpf.id,cpp.id
    </select>

    <select id="selectFeeNoAlreadyQueryDetailByQueryTimeAndUserId" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo">
        select case when cpf.actually_pay_fee_amt IS NULL then 0 when cpf.actually_pay_fee_amt IS NOT NULL then cpf.actually_pay_fee_amt else '' end as actuallyPayFeeAmt,cpi.income_rejection_reason AS incomeRejectionReason,cpi.fee_rejection_reason AS feeRejectionReason,opd.project_type AS projectType,cpi.id as phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId,cpp.id as projectPayId,cpc.id projectCustId,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as feeNoAlreadyTerm,cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,cpf.fee_amt as feeAmt,cpi.fee_amt as feeAmtSum
					, cpi.unfee_amt as unfeeAmtSum, cpi.remark  ,cpf.cust_name as custName, cpc.cust_name as feeCustName, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate
					, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,
           case when cpp.pay_flag='0' then '未打款'
          when cpp.pay_flag='1' then '已打款'
          when cpp.pay_flag='2' then '已确认'
          else '' end as payFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN(SELECT project_id,income_id,DATE_FORMAT(MAX(dynamic_time),'%Y-%m-%d') AS dynamic_time FROM cw_project_dynamic WHERE status='0' AND phase_status='1' GROUP BY income_id) cpd ON cpi.id=cpd.income_id
						LEFT JOIN cw_project_user cpu ON cp.id=cpu.project_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
						WHERE cp.project_flag='0'
						<if test="projectType != null and projectType != ''">
						    AND opd.project_type=#{projectType,jdbcType=VARCHAR}
						</if>
						<if test="projectType == ''">
						    AND (opd.project_type='0' OR opd.project_type='2' OR opd.project_type='3')
                        </if>
						AND cp.status='0' AND cpi.status='0' AND cpi.phase_status='2' AND cpd.dynamic_time&lt;=#{queryTime,jdbcType=VARCHAR} AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.unfee_amt>0 order by cpi.project_id,cpi.id,cpf.id,cpp.id
    </select>

    <select id="selectLawFeeNoAlreadyQueryDetailByQueryTimeAndUserId" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo">
        select opd.project_type AS projectType,cpi.phase_id AS phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId,cpp.id as projectPayId,cpc.id projectCustId
				,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as feeNoAlreadyTerm,
          cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,
          cpi.fee_amt as feeAmtSum, cpi.unfee_amt as unfeeAmtSum, cpi.remark, cpf.cust_name as custName, cpc.cust_name as feeCustName, cpf.fee_amt as feeAmt, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,
           case when cpp.pay_flag='0' then '未打款'
          when cpp.pay_flag='1' then '已打款'
          when cpp.pay_flag='2' then '已确认'
          when cpf.fee_flag='3' then '不需打款'
          when cpf.suspend_flag='1' then '不需打款'
          when cpf.suspend_flag='2' then '不需打款'
          else '' end as payFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN(SELECT project_id,income_id,DATE_FORMAT(MAX(dynamic_time),'%Y-%m-%d') AS dynamic_time FROM cw_project_dynamic WHERE status='0' AND phase_status='7' GROUP BY income_id) cpd ON cpi.id=cpd.income_id
						LEFT JOIN cw_project_user cpu ON cp.id=cpu.project_id
						WHERE cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND cpi.phase_status='9' AND cpd.dynamic_time&lt;=#{queryTime,jdbcType=VARCHAR} AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.income_amt>0 order by cpi.project_id,cpi.id,cpf.id,cpp.id
    </select>

    <select id="selectPayDateQueryDetailByQueryTime" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo">
        select case when cpf.actually_pay_fee_amt IS NULL then 0 when cpf.actually_pay_fee_amt IS NOT NULL then cpf.actually_pay_fee_amt else '' end as actuallyPayFeeAmt,cpi.income_rejection_reason AS incomeRejectionReason,cpi.fee_rejection_reason AS feeRejectionReason,opd.project_type AS projectType,DATE_FORMAT(cpi.collection_time,'%Y年%m月%d日') AS collectionTime,cpi.id as phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId,cpp.id as projectPayId,cpc.id projectCustId,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as term,cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,cpf.fee_amt as feeAmt,cpi.fee_amt as feeAmtSum
					, cpi.unfee_amt as unfeeAmtSum, cpi.remark  ,cpf.cust_name as custName, cpc.cust_name as feeCustName, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate
					, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,
           case when cpp.pay_flag='0' then '未打款'
          when cpp.pay_flag='1' then '已打款'
          when cpp.pay_flag='2' then '已确认'
          else '' end as payFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cpi.id AS projectIncomeId FROM cw_project_income cpi
						LEFT JOIN cw_project cp ON cp.id=cpi.project_id
						LEFT JOIN cw_project_pay cpp ON cpi.id=cpp.project_income_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
<!--						WHERE cp.project_type='0' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND #{queryStartTime,jdbcType=VARCHAR}&lt;=cpp.pay_date&lt;=#{queryEndTime,jdbcType=VARCHAR})) AS a-->
						WHERE cp.project_flag='0'
						<if test="projectType != null and projectType != ''">
						    AND opd.project_type=#{projectType,jdbcType=VARCHAR}
						</if>
						<if test="projectType == ''">
						    AND (opd.project_type='0' OR opd.project_type='2' OR opd.project_type='3')
                        </if>
						 AND cp.status='0' AND cpi.status='0' AND cpp.pay_date BETWEEN #{queryStartTime,jdbcType=VARCHAR} AND #{queryEndTime,jdbcType=VARCHAR})) AS a
						ON cpi.id=a.projectIncomeId order by cpi.project_id,cpi.id,cpf.id,cpp.id
    </select>

    <select id="selectLawPayDateQueryDetailByQueryTime" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo">
        select cpf.fee_round AS feeRound, cpf.law_profit AS lawProfit, opd.project_type AS projectType,DATE_FORMAT(cpi.collection_time,'%Y年%m月') AS collectionTime,cpi.phase_id AS phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId,cpp.id as projectPayId,cpc.id projectCustId
				,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as term,
          cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,
          cpi.fee_amt as feeAmtSum, cpi.unfee_amt as unfeeAmtSum, cpi.remark, cpf.cust_name as custName, cpc.cust_name as feeCustName, cpf.fee_amt as feeAmt, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,
           case when cpp.pay_flag='0' then '未打款'
          when cpp.pay_flag='1' then '已打款'
          when cpp.pay_flag='2' then '已确认'
          when cpf.fee_flag='3' then '不需打款'
          when cpf.suspend_flag='1' then '不需打款'
          when cpf.suspend_flag='2' then '不需打款'
          else '' end as payFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN cw_project_pay cpp ON cpi.id=cpp.project_income_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
<!--						WHERE cp.project_type='1' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND #{queryStartTime,jdbcType=VARCHAR}&lt;=cpp.pay_date&lt;=#{queryEndTime,jdbcType=VARCHAR})) AS a-->
						WHERE opd.project_type='1' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND cpp.pay_date BETWEEN #{queryStartTime,jdbcType=VARCHAR} AND #{queryEndTime,jdbcType=VARCHAR})) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.income_amt>0 order by cpi.project_id,cpi.id,cpf.id,cpp.id
    </select>

    <select id="selectPayDateQueryDetailByQueryTimeAndUserId" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo">
        select case when cpf.actually_pay_fee_amt IS NULL then 0 when cpf.actually_pay_fee_amt IS NOT NULL then cpf.actually_pay_fee_amt else '' end as actuallyPayFeeAmt,cpi.income_rejection_reason AS incomeRejectionReason,cpi.fee_rejection_reason AS feeRejectionReason,opd.project_type AS projectType,DATE_FORMAT(cpi.collection_time,'%Y年%m月%d日') AS collectionTime,cpi.id as phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId,cpp.id as projectPayId,cpc.id projectCustId,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as term,cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,cpf.fee_amt as feeAmt,cpi.fee_amt as feeAmtSum
					, cpi.unfee_amt as unfeeAmtSum, cpi.remark  ,cpf.cust_name as custName, cpc.cust_name as feeCustName, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate
					, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,
           case when cpp.pay_flag='0' then '未打款'
          when cpp.pay_flag='1' then '已打款'
          when cpp.pay_flag='2' then '已确认'
          else '' end as payFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cpi.id AS projectIncomeId FROM cw_project_income cpi
						LEFT JOIN cw_project cp ON cp.id=cpi.project_id
						LEFT JOIN cw_project_pay cpp ON cpi.id=cpp.project_income_id
						LEFT JOIN cw_project_user cpu ON cp.id=cpu.project_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
<!--						WHERE cp.project_type='0' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND #{queryStartTime,jdbcType=VARCHAR}&lt;=cpp.pay_date&lt;=#{queryEndTime,jdbcType=VARCHAR} AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a-->
						WHERE cp.project_flag='0'
						<if test="projectType != null and projectType != ''">
						    AND opd.project_type=#{projectType,jdbcType=VARCHAR}
						</if>
						<if test="projectType == ''">
						    AND (opd.project_type='0' OR opd.project_type='2' OR opd.project_type='3')
                        </if>
						AND cp.status='0' AND cpi.status='0' AND cpp.pay_date BETWEEN #{queryStartTime,jdbcType=VARCHAR} AND #{queryEndTime,jdbcType=VARCHAR} AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a
						ON cpi.id=a.projectIncomeId order by cpi.project_id,cpi.id,cpf.id,cpp.id
    </select>

    <select id="selectLawPayDateQueryDetailByQueryTimeAndUserId" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo">
        select cpf.fee_round AS feeRound, cpf.law_profit AS lawProfit, opd.project_type AS projectType,DATE_FORMAT(cpi.collection_time,'%Y年%m月') AS collectionTime,cpi.id as phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId,cpp.id as projectPayId,cpc.id projectCustId,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as term,cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,cpf.fee_amt as feeAmt,cpi.fee_amt as feeAmtSum
					, cpi.unfee_amt as unfeeAmtSum, cpi.remark  ,cpf.cust_name as custName, cpc.cust_name as feeCustName, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate
					, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,
           case when cpp.pay_flag='0' then '未打款'
          when cpp.pay_flag='1' then '已打款'
          when cpp.pay_flag='2' then '已确认'
          else '' end as payFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN cw_project_pay cpp ON cpi.id=cpp.project_income_id
						LEFT JOIN cw_project_user cpu ON cp.id=cpu.project_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
<!--						WHERE cp.project_type='1' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND #{queryStartTime,jdbcType=VARCHAR}&lt;=cpp.pay_date&lt;=#{queryEndTime,jdbcType=VARCHAR} AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a-->
						WHERE opd.project_type='1' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND cpp.pay_date BETWEEN #{queryStartTime,jdbcType=VARCHAR} AND #{queryEndTime,jdbcType=VARCHAR} AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.income_amt>0 order by cpi.project_id,cpi.id,cpf.id,cpp.id
    </select>

    <select id="selectCollectionTimeQueryDetailByQueryTime" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo">
        select case when cpf.actually_pay_fee_amt IS NULL then 0 when cpf.actually_pay_fee_amt IS NOT NULL then cpf.actually_pay_fee_amt else '' end as actuallyPayFeeAmt,cpi.income_rejection_reason AS incomeRejectionReason,cpi.fee_rejection_reason AS feeRejectionReason,odm.data_code AS projectType,DATE_FORMAT(cpi.collection_time,'%Y年%m月%d日') AS collectionTime,cpi.id as phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
        ,cpc.id projectCustId,opd.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as term,cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,cpf.fee_amt as feeAmt
					, cpi.remark  ,cpf.cust_name as custName, cpc.cust_name as feeCustName, cpc.replace_flag AS replaceFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
                LEFT JOIN cw_project cp ON cpi.project_id=cp.id
                LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
                LEFT JOIN oa_data_manage odm ON (cp.project_type_relevance_type_id = odm.id )
					INNER JOIN (
					(SELECT DISTINCT cpi.id AS projectIncomeId FROM cw_project_income cpi
						LEFT JOIN cw_project cp ON cp.id=cpi.project_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
                        LEFT JOIN oa_data_manage odm ON (cp.project_type_relevance_type_id = odm.id )
						WHERE cp.project_flag='0'
						<if test="projectType != null and projectType != ''">
						    AND odm.data_code=#{projectType,jdbcType=VARCHAR}
						</if>
						<if test="projectType == ''">
						    AND (odm.data_code='0' OR odm.data_code='2' OR odm.data_code='3')
                        </if>
                        <if test="projectIds != null and projectIds.size() != 0">
                            AND opd.id IN
                            <foreach collection="projectIds" item="oaProjectDeployId" open="(" separator="," close=")">
                            #{oaProjectDeployId,jdbcType=BIGINT}
                            </foreach>
                        </if>
						AND cp.status='0' AND cpi.status='0' AND cpi.collection_time BETWEEN #{queryStartTime,jdbcType=VARCHAR} AND #{queryEndTime,jdbcType=VARCHAR})) AS a
						ON cpi.id=a.projectIncomeId order by cpi.project_id,cpi.id,cpf.id
    </select>

    <select id="selectLawCollectionTimeQueryDetailByQueryTime" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo">
        select cpf.fee_round AS feeRound, cpf.law_profit AS lawProfit, sdd.dict_value AS projectType,DATE_FORMAT(cpi.collection_time,'%Y年%m月') AS collectionTime,cpi.phase_id AS phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
        ,cpc.id projectCustId
				,opd.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as term,
          cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,
          cpi.remark, cpf.cust_name as custName, cpc.cust_name as feeCustName, cpf.fee_amt as feeAmt, cpc.replace_flag AS replaceFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					LEFT JOIN sys_dict_data sdd ON (cp.project_type_relevance_type_id = sdd.dict_code AND sdd.dict_type = 'project_type')
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
						LEFT JOIN sys_dict_data sdd ON (cp.project_type_relevance_type_id = sdd.dict_code AND sdd.dict_type = 'project_type')
						WHERE sdd.dict_value='1' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND DATE_FORMAT(cpi.collection_time,'%Y-%m') BETWEEN DATE_FORMAT(#{queryStartTime,jdbcType=VARCHAR},'%Y-%m') AND DATE_FORMAT(#{queryEndTime,jdbcType=VARCHAR},'%Y-%m'))) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.income_amt>0 order by cpi.project_id,cpi.id,cpf.id
    </select>

    <select id="selectCollectionTimeQueryDetailByQueryTimeAndUserId" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo">
        select case when cpf.actually_pay_fee_amt IS NULL then 0 when cpf.actually_pay_fee_amt IS NOT NULL then cpf.actually_pay_fee_amt else '' end as actuallyPayFeeAmt,cpi.income_rejection_reason AS incomeRejectionReason,cpi.fee_rejection_reason AS feeRejectionReason,opd.project_type AS projectType,DATE_FORMAT(cpi.collection_time,'%Y年%m月%d日') AS collectionTime,cpi.id as phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
<!--        ,cpp.id as projectPayId-->
        ,cpc.id projectCustId,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as term,cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,cpf.fee_amt as feeAmt
<!--          ,cpi.fee_amt as feeAmtSum-->
<!--					, cpi.unfee_amt as unfeeAmtSum-->
					, cpi.remark  ,cpf.cust_name as custName, cpc.cust_name as feeCustName, cpc.replace_flag AS replaceFlag
<!--					, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate-->
<!--					, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,-->
<!--           case when cpp.pay_flag='0' then '未打款'-->
<!--          when cpp.pay_flag='1' then '已打款'-->
<!--          when cpp.pay_flag='2' then '已确认'-->
<!--          else '' end as payFlag-->
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
<!--          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')-->
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cpi.id AS projectIncomeId FROM cw_project_income cpi
						LEFT JOIN cw_project cp ON cp.id=cpi.project_id
						LEFT JOIN cw_project_user cpu ON cp.id=cpu.project_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
<!--						WHERE cp.project_type='0' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND #{queryStartTime,jdbcType=VARCHAR}&lt;=cpi.collection_time&lt;=#{queryEndTime,jdbcType=VARCHAR})) AS a-->
						WHERE cp.project_flag='0'
						<if test="projectType != null and projectType != ''">
						    AND opd.project_type=#{projectType,jdbcType=VARCHAR}
						</if>
						<if test="projectType == ''">
						    AND (opd.project_type='0' OR opd.project_type='2' OR opd.project_type='3')
                        </if>
						AND cp.status='0' AND cpi.status='0' AND cpi.collection_time BETWEEN #{queryStartTime,jdbcType=VARCHAR} AND #{queryEndTime,jdbcType=VARCHAR} AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a
						ON cpi.id=a.projectIncomeId order by cpi.project_id,cpi.id,cpf.id
<!--						,cpp.id-->
    </select>

    <select id="selectLawCollectionTimeQueryDetailByQueryTimeAndUserId" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo">
        select cpf.fee_round AS feeRound, cpf.law_profit AS lawProfit, opd.project_type AS projectType,DATE_FORMAT(cpi.collection_time,'%Y年%m月') AS collectionTime,cpi.phase_id AS phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
<!--        ,cpp.id as projectPayId-->
        ,cpc.id projectCustId
				,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as term,
          cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,
<!--          cpi.fee_amt as feeAmtSum, cpi.unfee_amt as unfeeAmtSum, -->
          cpi.remark, cpf.cust_name as custName, cpc.cust_name as feeCustName, cpf.fee_amt as feeAmt, cpc.replace_flag AS replaceFlag
<!--          , DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,-->
<!--           case when cpp.pay_flag='0' then '未打款'-->
<!--          when cpp.pay_flag='1' then '已打款'-->
<!--          when cpp.pay_flag='2' then '已确认'-->
<!--          when cpf.fee_flag='3' then '不需打款'-->
<!--          when cpf.suspend_flag='1' then '不需打款'-->
<!--          when cpf.suspend_flag='2' then '不需打款'-->
<!--          else '' end as payFlag-->
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
<!--          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')-->
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN cw_project_user cpu ON cp.id=cpu.project_id
						LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
<!--						WHERE cp.project_type='1' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND #{queryStartTime,jdbcType=VARCHAR}&lt;=cpi.collection_time&lt;=#{queryEndTime,jdbcType=VARCHAR})) AS a-->
						WHERE opd.project_type='1' AND cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND DATE_FORMAT(cpi.collection_time,'%Y-%m') BETWEEN DATE_FORMAT(#{queryStartTime,jdbcType=VARCHAR},'%Y-%m') AND DATE_FORMAT(#{queryEndTime,jdbcType=VARCHAR},'%Y-%m') AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.income_amt>0 order by cpi.project_id,cpi.id,cpf.id
<!--						,cpp.id-->
    </select>

    <select id="selectRemarkQueryDetailByQueryRemark" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo">
        select case when cpf.actually_pay_fee_amt IS NULL then 0 when cpf.actually_pay_fee_amt IS NOT NULL then cpf.actually_pay_fee_amt else '' end as actuallyPayFeeAmt,cpi.income_rejection_reason AS incomeRejectionReason,cpi.fee_rejection_reason AS feeRejectionReason,sdd.dict_value AS projectType,cpi.id as phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
        ,cpc.id projectCustId,opd.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as feeNoAlreadyTerm,cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,cpf.fee_amt as feeAmt
					, cpi.remark  ,cpf.cust_name as custName, cpc.cust_name as feeCustName, cpi.collection_time AS collectionTime, cpc.replace_flag AS replaceFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					LEFT JOIN sys_dict_data sdd ON (cp.project_type_relevance_type_id = sdd.dict_code AND sdd.dict_type = 'project_type')
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN sys_dict_data sdd ON (cp.project_type_relevance_type_id = sdd.dict_code AND sdd.dict_type = 'project_type')
						WHERE cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND cpi.phase_status='1'
						<if test="projectIds != null and projectIds.size() != 0">
                            AND cp.oa_project_deploy_id IN
                            <foreach collection="projectIds" item="oaProjectDeployId" open="(" separator="," close=")">
                            #{oaProjectDeployId,jdbcType=BIGINT}
                            </foreach>
                        </if>
						)) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.remark LIKE CONCAT('%',#{remark,jdbcType=VARCHAR},'%') order by cpi.project_id,cpi.id,cpf.id
    </select>

    <select id="selectLawRemarkQueryDetailByQueryRemark" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo">
        select cpf.fee_round AS feeRound, cpf.law_profit AS lawProfit, sdd.dict_value AS projectType,cpi.phase_id AS phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
        ,cpc.id projectCustId
				,opd.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as feeNoAlreadyTerm,
          cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,
          cpi.remark, cpf.cust_name as custName, cpc.cust_name as feeCustName, cpf.fee_amt as feeAmt, cpi.collection_time AS collectionTime, cpc.replace_flag AS replaceFlag
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					LEFT JOIN sys_dict_data sdd ON (cp.project_type_relevance_type_id = sdd.dict_code AND sdd.dict_type = 'project_type')
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN sys_dict_data sdd ON (cp.project_type_relevance_type_id = sdd.dict_code AND sdd.dict_type = 'project_type')
						WHERE cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND cpi.phase_status='8' AND cpi.remark LIKE CONCAT('%',#{remark,jdbcType=VARCHAR},'%')
						<if test="projectIds != null and projectIds.size() != 0">
                            AND cp.oa_project_deploy_id IN
                            <foreach collection="projectIds" item="oaProjectDeployId" open="(" separator="," close=")">
                            #{oaProjectDeployId,jdbcType=BIGINT}
                            </foreach>
                        </if>
						)) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.income_amt>0  order by cpi.project_id,cpi.id,cpf.id
    </select>

    <select id="selectRemarkQueryDetailByQueryRemarkAndUserId" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo">
        select case when cpf.actually_pay_fee_amt IS NULL then 0 when cpf.actually_pay_fee_amt IS NOT NULL then cpf.actually_pay_fee_amt else '' end as actuallyPayFeeAmt,cpi.income_rejection_reason AS incomeRejectionReason,cpi.fee_rejection_reason AS feeRejectionReason,opd.project_type AS projectType,cpi.id as phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
<!--        ,cpp.id as projectPayId-->
        ,cpc.id projectCustId,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as feeNoAlreadyTerm,cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,cpf.fee_amt as feeAmt
<!--          ,cpi.fee_amt as feeAmtSum-->
<!--					, cpi.unfee_amt as unfeeAmtSum-->
					, cpi.remark  ,cpf.cust_name as custName, cpc.cust_name as feeCustName, cpi.collection_time AS collectionTime, cpc.replace_flag AS replaceFlag
<!--					, DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate-->
<!--					, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,-->
<!--           case when cpp.pay_flag='0' then '未打款'-->
<!--          when cpp.pay_flag='1' then '已打款'-->
<!--          when cpp.pay_flag='2' then '已确认'-->
<!--          else '' end as payFlag-->
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
<!--          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')-->
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN cw_project_user cpu ON cp.id=cpu.project_id
						WHERE cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND cpi.phase_status='1'AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.remark LIKE CONCAT('%',#{remark,jdbcType=VARCHAR},'%') order by cpi.project_id,cpi.id,cpf.id
<!--						,cpp.id-->
    </select>

    <select id="selectLawRemarkQueryDetailByQueryRemarkAndUserId" resultType="org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo">
        select cpf.fee_round AS feeRound, cpf.law_profit AS lawProfit, opd.project_type AS projectType,cpi.phase_id AS phaseId,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
<!--        ,cpp.id as projectPayId-->
        ,cpc.id projectCustId
				,cp.project_name AS projectName,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d'))
          else '' end as feeNoAlreadyTerm,
          cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt,
<!--          cpi.fee_amt as feeAmtSum, cpi.unfee_amt as unfeeAmtSum, -->
          cpi.remark, cpf.cust_name as custName, cpc.cust_name as feeCustName, cpf.fee_amt as feeAmt, cpi.collection_time AS collectionTime, cpc.replace_flag AS replaceFlag
<!--          , DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,-->
<!--           case when cpp.pay_flag='0' then '未打款'-->
<!--          when cpp.pay_flag='1' then '已打款'-->
<!--          when cpp.pay_flag='2' then '已确认'-->
<!--          when cpf.fee_flag='3' then '不需打款'-->
<!--          when cpf.suspend_flag='1' then '不需打款'-->
<!--          when cpf.suspend_flag='2' then '不需打款'-->
<!--          else '' end as payFlag-->
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
<!--          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')-->
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
					LEFT JOIN cw_project cp ON cpi.project_id=cp.id
					LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
					INNER JOIN (
					(SELECT DISTINCT cp.id AS projectId FROM cw_project cp
						LEFT JOIN cw_project_income cpi ON cp.id=cpi.project_id
						LEFT JOIN cw_project_user cpu ON cp.id=cpu.project_id
						WHERE cp.project_flag='0' AND cp.status='0' AND cpi.status='0' AND cpi.phase_status='8' AND cpi.remark LIKE CONCAT('%',#{remark,jdbcType=VARCHAR},'%') AND cpu.user_id=#{userId,jdbcType=BIGINT})) AS a
						ON cpi.project_id=a.projectId WHERE cpi.status='0' AND cpi.income_amt>0 order by cpi.project_id,cpi.id,cpf.id
<!--						,cpp.id-->
    </select>

    <select id="selectCwProjectIncomeAllPhaseCountById" resultType="int">
        SELECT COUNT(*) FROM cw_project_income WHERE id=#{id,jdbcType=BIGINT}
    </select>

    <select id="selectCwProjectLawIncomeAllPhaseCountById" resultType="int">
        SELECT COUNT(*) FROM cw_project_income WHERE id=#{id,jdbcType=BIGINT}
    </select>

    <select id="selectAllPhaseByProjectId" resultType="int">
        SELECT COUNT(*) FROM cw_project_income WHERE status='0' AND project_id=#{projectId,jdbcType=BIGINT}
    </select>

    <select id="selectCwprojectIncomePhaseByIncomeId" resultType="java.lang.String">
        SELECT CASE WHEN term='0' THEN CONCAT(substring(term_month,1,4),'年',substring(term_month,6,2),'月') WHEN term='1' THEN CONCAT(date_format(term_begin,'%Y.%m.%d'),'-', date_format(term_end,'%Y.%m.%d')) ELSE '' END FROM cw_project_income WHERE id=#{incomeId,jdbcType=BIGINT}
    </select>

    <select id="selectCwProjectLawIncomeListFlagTwo" resultMap="CwProjectIncomeResult">
        SELECT SUM(income_amt) income_amt,SUM(gross_profit_amt) gross_profit_amt,SUM(gross_profit_amt2) gross_profit_amt2,SUM(fee_amt) fee_amt,SUM(unfee_amt) unfee_amt,
        (SELECT COUNT(*) FROM cw_project_income WHERE phase_status>'6' AND phase_status!='9' AND status='0' AND project_id=#{projectId,jdbcType=BIGINT} AND term!='9'
        <if test="sumFlag != null">
            <if test="startDate != null and startDate != ''">
                AND term_month &gt;= #{startDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                AND term_month &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </if>) phase_status
        FROM cw_project_income WHERE status='0' AND project_id=#{projectId,jdbcType=BIGINT} AND term!='9'
        <if test="sumFlag != null">
            <if test="startDate != null and startDate != ''">
                AND term_month &gt;= #{startDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                AND term_month &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </if>
    </select>

    <select id="selectAllLawPhaseByProjectId" resultType="int">
        SELECT COUNT(*) FROM cw_project_income WHERE status='0' AND project_id=#{projectId,jdbcType=BIGINT} AND term!='9'
    </select>

    <select id="selectAllSuspendAmtInfo" resultType="Map">
<!--        SELECT id, current_fee AS currentFee FROM cw_project_fee WHERE suspend_flag!='0'-->
        SELECT i.phase_id AS phaseId, f.id, i.service_provider_flag AS serviceProviderFlag, i.service_provider AS serviceProvider, i.service_provider_second AS serviceProviderSecond, f.current_fee AS currentFee, f.suspend_flag AS suspendFlag, f.suspend_clear_id AS suspendClearId
         FROM cw_project_fee f
        LEFT JOIN cw_project_income i ON f.project_income_id=i.id WHERE f.suspend_flag!='0'
    </select>

    <update id="updateLawCwProjectFeeSuspendSumByFeeIds">
        UPDATE cw_project_fee SET suspend_flag='1', suspend_clear_id = NULL WHERE suspend_clear_id IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="selectCwprojectLawPayInfoByPhaseId" resultType="Map">
        SELECT cpp.id, #{phaseId,jdbcType=BIGINT} AS phaseId, cpp.project_id AS projectId, cpp.project_income_id AS projectIncomeId,
        cpp.project_fee_id AS projectFeeId, cpp.pay_date AS payDate, cpp.pay_amt AS payAmt, cpp.difference_amt AS differenceAmt, cpf.cust_id AS custId, cpf.cust_name AS custName, cpc.cust_name AS feeCustName
        FROM cw_project_pay cpp
        INNER JOIN (
        SELECT f.id,f.cust_id, f.cust_name FROM cw_project_fee f
        INNER JOIN (
        SELECT id FROM cw_project_income WHERE phase_id=#{phaseId,jdbcType=BIGINT}
        ) a ON f.project_income_id=a.id
        ) cpf ON cpp.project_fee_id=cpf.id
        LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
    </select>

    <select id="selectLawCwProjectIncomeByProjectId" resultMap="pro">
        SELECT a.id AS ack_id,a.project_id AS project_id,a.phase_id,cpi.id AS income_id,SUM(cpf.law_profit) AS aggregateAnmtmt FROM cw_project_income cpi
        INNER JOIN
        (
        SELECT cpa.id,cpa.project_id,cpi.id AS phase_id FROM cw_project_ack cpa
        LEFT JOIN cw_project_ack_ref cpar ON cpa.id = cpar.ack_id
        LEFT JOIN cw_project_income cpi ON cpar.project_income_id = cpi.id
        WHERE cpa.ack_flag = '0' AND cpa.project_id = #{projectId,jdbcType=BIGINT}
        AND (cpi.phase_status='9' OR cpi.phase_status = '10')
        ) a
        ON cpi.phase_id=a.phase_id
        LEFT JOIN cw_project_fee cpf
        ON cpi.id=cpf.project_income_id
        WHERE cpi.service_provider_flag='1'
        GROUP BY a.project_id
        HAVING aggregateAnmtmt >= 50000
    </select>

    <select id="selectCwprojectIncomeLawPhaseListByProjectIdAndCustIds" resultType="java.lang.Long">
        SELECT DISTINCT cpi.phase_id FROM cw_project_income cpi LEFT JOIN cw_project_fee cpf ON cpi.id=cpf.project_income_id WHERE cpi.status='0'
        AND cpi.project_id=#{projectId,jdbcType=BIGINT} AND phase_id IS NOT NULL
        <if test="custIdList != null and custIdList.size() != 0">
            AND cpf.cust_id IN
            <foreach item="custId" collection="custIdList" open="(" separator="," close=")">
                #{custId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="selectCwprojectIncomeListByProjectIdAndCustIds" resultType="java.lang.Long">
        SELECT DISTINCT cpi.* FROM cw_project_income cpi LEFT JOIN cw_project_fee cpf ON cpi.id=cpf.project_income_id WHERE cpi.status='0'
        AND cpi.project_id=#{projectId,jdbcType=BIGINT}
        <if test="custIdList != null and custIdList.size() != 0">
            AND cpf.cust_id IN
            <foreach item="custId" collection="custIdList" open="(" separator="," close=")">
                #{custId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="selectCwProjectIncomeListNotEnterByOaProjectDeployId" resultType="java.util.Map">
        <if test="cwProjectIncome != null and cwProjectIncome.rejectionIncomeAndChangeIncome == '1'.toString() ">
        SELECT opd.id AS oaProjectDeployId, sdd.dict_value AS project_type,case when i.rejection_flag='1' then '1' else '' end as rejection_flag,i.phase_status as phase_status,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        </if>
        <if test="cwProjectIncome != null and cwProjectIncome.rejectionIncomeAndChangeIncome != '1'.toString() ">
        SELECT opd.id AS oaProjectDeployId, sdd.dict_value AS project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        </if>
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
        LEFT JOIN sys_dict_data sdd ON (p.project_type_relevance_type_id = sdd.dict_code AND sdd.dict_type = 'project_type')
        <where>
            <if test="projectIds != null and projectIds.size() != 0">
            AND opd.id IN
                <foreach collection="projectIds" item="oaProjectDeployId" open="(" separator="," close=")">
                #{oaProjectDeployId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="cwProjectIncome != null and cwProjectIncome.rejectionIncomeAndChangeIncome == '1'.toString() ">
            AND (i.phase_status='0' OR (i.phase_status='2' AND i.rejection_flag='1'))
            </if>
            <if test="cwProjectIncome != null and cwProjectIncome.rejectionIncomeAndChangeIncome != '1'.toString() ">
            AND i.phase_status='7'
            </if>
        AND i.status='0' AND opd.is_enable='Y' AND p.project_flag='0'
        </where>
        ORDER BY termMonthCompare DESC
    </select>

    <select id="selectCwProjectIncomeListNotVerifyByOaProjectDeployId" resultType="java.util.Map">
        SELECT opd.id AS oaProjectDeployId, sdd.dict_value AS project_type,i.id id,i.project_id,i.term,i.term_month,i.term_begin,i.term_end,p.project_name,p.cust_name,p.income_cust_name,
        case when i.term='0' then i.term_month when i.term='1' then date_format(i.term_begin,'%Y-%m') else '' end as termMonthCompare
        FROM cw_project p
        LEFT JOIN cw_project_income i ON p.id=i.project_id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
        LEFT JOIN sys_dict_data sdd ON (p.project_type_relevance_type_id = sdd.dict_code AND sdd.dict_type = 'project_type')
        <where>
            <if test="projectIds != null and projectIds.size() != 0">
            AND opd.id IN
                <foreach collection="projectIds" item="oaProjectDeployId" open="(" separator="," close=")">
                #{oaProjectDeployId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="cwProjectIncome != null and cwProjectIncome.rejectionIncomeAndChangeIncome == '1'.toString() ">
            AND i.phase_status='1'
            </if>
            <if test="cwProjectIncome != null and cwProjectIncome.rejectionIncomeAndChangeIncome != '1'.toString() ">
            AND i.phase_status='8'
            </if>
        AND i.status='0' AND opd.is_enable='Y' AND p.project_flag='0'
        </where>
        ORDER BY termMonthCompare DESC
    </select>
</mapper>
