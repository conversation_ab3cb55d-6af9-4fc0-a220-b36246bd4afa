<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cwproject.mapper.CwProjectMapper">

    <resultMap type="org.ruoyi.core.cwproject.domain.CwProject" id="CwProjectResult">
        <result property="id"    column="id"    />
        <result property="projectType"    column="project_type"    />
        <result property="projectName"    column="project_name"    />
        <result property="custName"    column="cust_name"    />
        <result property="incomeCustName"    column="income_cust_name"    />
        <result property="projectFlag"    column="project_flag"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="prestoreIncomeFlag"    column="prestore_income_flag"    />
        <result property="projectTypeChinese"    column="project_type_chinese"    />
        <result property="oaProjectDeployId"    column="oa_project_deploy_id"    />
        <result property="generateCertificateFlag"    column="generate_certificate_flag"    />
        <result property="accountSetsId"    column="account_sets_id"    />
        <result property="guaranteeIncomeType"    column="guarantee_income_type"    />
        <result property="guarantyPayee"    column="guaranty_payee"    />
        <result property="payeeAbbreviation"    column="payee_abbreviation"    />
        <result property="projectTypeRelevanceTypeId"    column="project_type_relevance_type_id"    />
        <result property="projectTypeId"    column="project_type_id"  />
    </resultMap>

    <sql id="selectCwProjectVo">
        select id, project_type, project_name, cust_name, income_cust_name, project_flag, status, create_by, create_time, update_by, update_time, prestore_income_flag, oa_project_deploy_id, generate_certificate_flag, account_sets_id, guarantee_income_type, guaranty_payee, project_type_relevance_type_id   from cw_project
    </sql>

    <select id="selectCwProjectList" parameterType="org.ruoyi.core.cwproject.domain.CwProject" resultMap="CwProjectResult">
        <include refid="selectCwProjectVo"/>
        <where>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="custName != null  and custName != ''"> and cust_name like concat('%', #{custName}, '%')</if>
            <if test="incomeCustName != null  and incomeCustName != ''"> and income_cust_name like concat('%', #{incomeCustName}, '%')</if>
            <if test="projectFlag != null  and projectFlag != ''"> and project_flag = #{projectFlag}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="oaProjectDeployId != null"> and oa_project_deploy_id = #{oaProjectDeployId}</if>
        </where>
    </select>

    <select id="selectCwProjectById" parameterType="Long" resultMap="CwProjectResult">
        select p.id, odm.data_code AS project_type, opd.project_name, p.cust_name, p.income_cust_name, p.project_flag, p.status, p.create_by, p.create_time, p.update_by, p.update_time, p.prestore_income_flag, p.oa_project_deploy_id, p.generate_certificate_flag, p.account_sets_id, p.guarantee_income_type, p.guaranty_payee, p.project_type_relevance_type_id from cw_project p
        LEFT JOIN oa_project_deploy opd ON p.oa_project_deploy_id=opd.id
        LEFT JOIN oa_data_manage odm ON p.project_type_relevance_type_id = odm.id
        where p.id = #{id} AND p.status='0'
    </select>

    <insert id="insertCwProject" parameterType="org.ruoyi.core.cwproject.domain.CwProject" useGeneratedKeys="true" keyProperty="id">
        insert into cw_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="projectType != null and projectType != ''">project_type,</if>
            <if test="custName != null and custName != ''">cust_name,</if>
            <if test="incomeCustName != null and incomeCustName != ''">income_cust_name,</if>
            <if test="projectFlag != null and projectFlag != ''">project_flag,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="prestoreIncomeFlag != null and prestoreIncomeFlag != ''">prestore_income_flag,</if>
            <if test="oaProjectDeployId != null">oa_project_deploy_id,</if>
            <if test="generateCertificateFlag != null and generateCertificateFlag != ''">generate_certificate_flag,</if>
            <if test="accountSetsId != null">account_sets_id,</if>
            <if test="guaranteeIncomeType != null and guaranteeIncomeType != ''">guarantee_income_type,</if>
            <if test="guarantyPayee != null">guaranty_payee,</if>
            <if test="projectTypeRelevanceTypeId != null">project_type_relevance_type_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="projectType != null and projectType != ''">#{projectType,jdbcType=VARCHAR},</if>
            <if test="custName != null and custName != ''">#{custName},</if>
            <if test="incomeCustName != null and incomeCustName != ''">#{incomeCustName},</if>
            <if test="projectFlag != null and projectFlag != ''">#{projectFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="prestoreIncomeFlag != null and prestoreIncomeFlag != ''">#{prestoreIncomeFlag,jdbcType=VARCHAR},</if>
            <if test="oaProjectDeployId != null">#{oaProjectDeployId,jdbcType=BIGINT},</if>
            <if test="generateCertificateFlag != null and generateCertificateFlag != ''">#{generateCertificateFlag,jdbcType=VARCHAR},</if>
            <if test="accountSetsId != null">#{accountSetsId,jdbcType=BIGINT},</if>
            <if test="guaranteeIncomeType != null and guaranteeIncomeType != ''">#{guaranteeIncomeType,jdbcType=VARCHAR},</if>
            <if test="guarantyPayee != null">#{guarantyPayee,jdbcType=BIGINT},</if>
            <if test="projectTypeRelevanceTypeId != null">#{projectTypeRelevanceTypeId,jdbcType=BIGINT},</if>
         </trim>
    </insert>

    <update id="updateCwProject" parameterType="org.ruoyi.core.cwproject.domain.CwProject">
        update cw_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="custName != null and custName != ''">cust_name = #{custName},</if>
            <if test="incomeCustName != null and incomeCustName != ''">income_cust_name = #{incomeCustName},</if>
            <if test="projectFlag != null and projectFlag != ''">project_flag = #{projectFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="prestoreIncomeFlag != null and prestoreIncomeFlag != ''">prestore_income_flag = #{prestoreIncomeFlag,jdbcType=VARCHAR},</if>
            <if test="oaProjectDeployId != null">oa_project_deploy_id = #{oaProjectDeployId,jdbcType=BIGINT},</if>
            <if test="generateCertificateFlag != null and generateCertificateFlag != ''">generate_certificate_flag = #{generateCertificateFlag,jdbcType=VARCHAR},</if>
            <if test="accountSetsId != null">account_sets_id = #{accountSetsId,jdbcType=BIGINT},</if>
            <if test="guaranteeIncomeType != null and guaranteeIncomeType != ''">guarantee_income_type = #{guaranteeIncomeType,jdbcType=VARCHAR},</if>
            <if test="guarantyPayee != null">guaranty_payee = #{guarantyPayee,jdbcType=BIGINT},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateCwProject1" parameterType="org.ruoyi.core.cwproject.domain.CwProject">
        update cw_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="custName != null and custName != ''">cust_name = #{custName},</if>
            <if test="incomeCustName != null and incomeCustName != ''">income_cust_name = #{incomeCustName},</if>
            <if test="projectFlag != null and projectFlag != ''">project_flag = #{projectFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="prestoreIncomeFlag != null and prestoreIncomeFlag != ''">prestore_income_flag = #{prestoreIncomeFlag,jdbcType=VARCHAR},</if>
            <if test="oaProjectDeployId != null">oa_project_deploy_id = #{oaProjectDeployId,jdbcType=BIGINT},</if>
            <if test="generateCertificateFlag != null and generateCertificateFlag != ''">generate_certificate_flag = #{generateCertificateFlag,jdbcType=VARCHAR},</if>
            account_sets_id = #{accountSetsId,jdbcType=BIGINT},
            guarantee_income_type = #{guaranteeIncomeType,jdbcType=VARCHAR},
            guaranty_payee = #{guarantyPayee,jdbcType=BIGINT},
            <if test="projectTypeRelevanceTypeId != null">project_type_relevance_type_id = #{projectTypeRelevanceTypeId,jdbcType=BIGINT}</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCwProjectById" parameterType="Long">
        delete from cw_project where id = #{id}
    </delete>

    <delete id="deleteCwProjectByIds" parameterType="String">
        delete from cw_project where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCwProjectListByCustNameOrIncomeCustName" resultMap="CwProjectResult">
        SELECT DISTINCT p.id id,p.project_name project_name,p.cust_name cust_name,p.income_cust_name income_cust_name FROM cw_project p LEFT JOIN cw_project_income i ON p.id=i.project_id
        <where>
            AND p.status='0'
            <if test="sumFlag == null ">
                AND p.project_flag='1'
            </if>
            <if test="sumFlag != null ">
                AND p.project_flag='0'
            </if>
            <if test="cwProject.projectName != null and cwProject.projectName != ''">
                AND p.project_name like CONCAT('%',#{cwProject.projectName,jdbcType=VARCHAR},'%')
            </if>
            <if test="cwProject.custName != null and cwProject.custName != ''">
                AND p.cust_name like #{cwProject.custName,jdbcType=VARCHAR}
            </if>
            <if test="cwProject.incomeCustName != null and cwProject.incomeCustName != ''">
                AND p.income_cust_name like #{cwProject.incomeCustName,jdbcType=VARCHAR}
            </if>
            <if test="startDate != null and startDate != ''">
                AND i.term_month &gt;= #{startDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                AND i.term_month &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectCwProjectListFirst" resultMap="CwProjectResult">
        <if test="userId == null">
            SELECT cust_name,income_cust_name FROM cw_project WHERE project_flag='1' AND status='0'
        </if>
        <if test="userId != null">
            SELECT p.cust_name,p.income_cust_name FROM cw_project p,cw_project_user u WHERE p.id=u.project_id AND p.project_flag='1' AND p.status='0' AND u.user_id=#{userId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectCwProjectListAll" resultMap="CwProjectResult">
        <include refid="selectCwProjectVo"/> WHERE project_flag='0' AND status='0'
    </select>

    <select id="selectCwprojectDetailThree" resultType="java.util.Map">
        SELECT cp.id id,opd.project_name projectName,cp.cust_name custName,cp.income_cust_name incomeCustName,
<!--        case when prestore_income_flag='0' then '0'-->
<!--        when prestore_income_flag='1' then '1'-->
<!--        else '' end AS prestoreIncomeFlag,-->
        odm.data_code AS projectType,optc.project_portfolio_code as projectPortfolioCode,odm.id as projectTypeId
        FROM cw_project cp
        LEFT JOIN oa_project_deploy opd ON cp.oa_project_deploy_id=opd.id
        LEFT JOIN oa_data_manage odm ON cp.project_type_relevance_type_id = odm.id
        LEFT JOIN oa_project_type_correlation optc ON FIND_IN_SET(odm.id, optc.project_type_id) > 0
        WHERE odm.first_data_code = 'project_type'
              AND (optc.project_portfolio_code LIKE '%lawUrging%' or optc.project_portfolio_code is null)
              AND  cp.id=#{projectId,jdbcType=BIGINT}
    </select>
    <update id="closeProjectById">
        update cw_project set project_flag = '1' where id = #{id}
    </update>

    <select id="selectByIncomeId" resultMap="CwProjectResult">
        <include refid="selectCwProjectVo"/>
        where id = (select project_id from cw_project_income where id = #{id})
    </select>

    <select id="selectCwProjectByIdAndUserId" resultType="java.lang.Long">
        SELECT project_id projectId FROM cw_project_user WHERE user_id=#{userId,jdbcType=BIGINT} AND project_id=#{projectId,jdbcType=BIGINT}
    </select>

    <select id="selectCwProjectListByCustNameOrIncomeCustNameTermOne" resultMap="CwProjectResult">
        SELECT DISTINCT p.id id,p.project_name project_name,p.cust_name cust_name,p.income_cust_name income_cust_name FROM cw_project p LEFT JOIN cw_project_income i ON p.id=i.project_id
        <where>
            <if test="sumFlag == null ">
                AND p.project_flag='1'
            </if>
            <if test="sumFlag != null ">
                AND p.project_flag='0'
            </if>
            <if test="cwProject.projectName != null and cwProject.projectName != ''">
                AND p.project_name like CONCAT('%',#{cwProject.projectName,jdbcType=VARCHAR},'%')
            </if>
            <if test="cwProject.custName != null and cwProject.custName != ''">
                AND p.cust_name like #{cwProject.custName,jdbcType=VARCHAR}
            </if>
            <if test="cwProject.incomeCustName != null and cwProject.incomeCustName != ''">
                AND p.income_cust_name like #{cwProject.incomeCustName,jdbcType=VARCHAR}
            </if>
            <if test="startDate != null and startDate != ''">
                AND i.term_begin &gt;= #{startDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                AND i.term_end &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectCwProjectListByCustNameOrIncomeCustNameFirst" resultMap="CwProjectResult">
        SELECT p.id id,p.project_name project_name,p.cust_name cust_name,p.income_cust_name income_cust_name FROM cw_project p
        <where>
            <if test="sumFlag == null ">
                AND p.project_flag='1'
            </if>
            <if test="sumFlag != null ">
                AND p.project_flag='0'
            </if>
            <if test="cwProject.projectName != null and cwProject.projectName != ''">
                AND p.project_name like CONCAT('%',#{cwProject.projectName,jdbcType=VARCHAR},'%')
            </if>
            <if test="cwProject.custName != null and cwProject.custName != ''">
                AND p.cust_name like #{cwProject.custName,jdbcType=VARCHAR}
            </if>
            <if test="cwProject.incomeCustName != null and cwProject.incomeCustName != ''">
                AND p.income_cust_name like #{cwProject.incomeCustName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectCwProjectByIds" resultMap="CwProjectResult">
        SELECT p.id id,
<!--        CASE                                               -->
<!--        WHEN sdd.dict_value='0' THEN '通道业务'             -->
<!--        WHEN sdd.dict_value='1' THEN '法催业务'             -->
<!--        WHEN sdd.dict_value='2' THEN '分润业务'             -->
<!--        WHEN sdd.dict_value='3' THEN '保函业务' ELSE '' END             -->
<!--        AS project_type_chinese,                -->
<!--        p.project_type project_type,p.project_name project_name,p.cust_name cust_name,p.income_cust_name income_cust_name,-->
<!--        sdd.dict_value project_type,-->
        odm.data_code as project_type,odm.id as project_type_id,optc.project_portfolio_code as projectPortfolioCode,
        opd.project_name project_name,p.cust_name cust_name,p.income_cust_name income_cust_name,
<!--        CASE-->
<!--        WHEN p.prestore_income_flag='0' THEN '无'-->
<!--        WHEN p.prestore_income_flag IS NULL THEN '无'-->
<!--        WHEN p.prestore_income_flag='1' THEN '有' ELSE '' END-->
<!--        AS prestore_income_flag,-->
        p.oa_project_deploy_id,
        p.generate_certificate_flag,
        p.account_sets_id,
        p.guarantee_income_type,
        p.guaranty_payee,
        ot.abbreviation AS payee_abbreviation,
        p.project_type_relevance_type_id
        FROM cw_project p
        LEFT JOIN oa_project_deploy opd ON p.oa_project_deploy_id=opd.id
        LEFT JOIN oa_trader ot ON p.guaranty_payee=ot.id
        LEFT JOIN oa_data_manage odm ON p.project_type_relevance_type_id = odm.id
        LEFT JOIN oa_project_type_correlation optc ON FIND_IN_SET(odm.id, optc.project_type_id) > 0
<!--         LEFT JOIN sys_dict_data sdd ON (p.project_type_relevance_type_id = sdd.dict_code AND sdd.dict_type = 'project_type')-->
        <if test="list != null">
            WHERE opd.id IN
            <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
                #{id,jdbcType=BIGINT}
            </foreach>
            AND p.project_flag=#{projectFlag,jdbcType=VARCHAR}
        </if>
        <if test="list == null ">
            WHERE p.project_flag=#{projectFlag,jdbcType=VARCHAR}
        </if>
        <if test="cwProject.projectName != null and cwProject.projectName != ''">
                AND opd.project_name like CONCAT('%',#{cwProject.projectName,jdbcType=VARCHAR},'%')
            </if>
            <if test="cwProject.custName != null and cwProject.custName != ''">
                AND p.cust_name like #{cwProject.custName,jdbcType=VARCHAR}
            </if>
            <if test="cwProject.incomeCustName != null and cwProject.incomeCustName != ''">
                AND p.income_cust_name like #{cwProject.incomeCustName,jdbcType=VARCHAR}
            </if>
<!--            <if test="cwProject.projectType != null and cwProject.projectType != ''">-->
<!--                AND sdd.dict_value = #{cwProject.projectType,jdbcType=VARCHAR}-->
<!--            </if>-->
            <if test="cwProject.projectTypes != null and cwProject.projectTypes.size() > 0">
                and odm.data_code in
                <foreach item="type" collection="cwProject.projectTypes" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="cwProject.prestoreIncome != null and cwProject.prestoreIncome != ''">
                <if test="cwProject.prestoreIncome == '1'.toString()">
                    AND p.prestore_income_flag = '1'
                </if>
                <if test="cwProject.prestoreIncome == '0'.toString()">
                    AND (p.prestore_income_flag = '0' OR p.prestore_income_flag IS NULL)
                </if>
            </if>
            AND p.status='0'
            AND odm.first_data_code = 'project_type'
            AND (optc.project_portfolio_code LIKE '%lawUrging%' or optc.project_portfolio_code is null)
    </select>


    <select id="getCustByProjectId"  resultType="map">
        select cust_name as label, cust_name as value from cw_project
        <where>
            project_flag = 0 and status = 0

            <if test="projectId != null  ">
                and id in
                <foreach collection="projectId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        group by cust_name
    </select>

    <select id="getincomeCustByProjectId"  resultType="map">
        select income_cust_name as label, income_cust_name as value from cw_project
        <where>
            project_flag = 0 and status = 0
            <if test="projectId != null  ">
                and id in
                <foreach collection="projectId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        group by income_cust_name
    </select>


    <select id="getProjectId" resultType="map">
        select cp.id, cp.project_type as projectType  from cw_project cp LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id where cp.project_flag = 0 and cp.status = 0
    </select>

    <update id="updateCwprojectPrestoreIncomeByProjectId">
        UPDATE cw_project_prestore_income SET status='1' WHERE project_id=#{id,jdbcType=BIGINT}
    </update>

    <update id="updateCwProjectByOaProjectDeployId" parameterType="org.ruoyi.core.cwproject.domain.CwProject">
        update cw_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
<!--            <if test="custName != null and custName != ''">cust_name = #{custName},</if>-->
<!--            <if test="incomeCustName != null and incomeCustName != ''">income_cust_name = #{incomeCustName},</if>-->
<!--            <if test="projectFlag != null and projectFlag != ''">project_flag = #{projectFlag},</if>-->
            <if test="status != null">status = #{status},</if>
<!--            <if test="createBy != null">create_by = #{createBy},</if>-->
<!--            <if test="createTime != null">create_time = #{createTime},</if>-->
<!--            <if test="updateBy != null">update_by = #{updateBy},</if>-->
            <if test="updateTime != null">update_time = #{updateTime},</if>
<!--            <if test="prestoreIncomeFlag != null and prestoreIncomeFlag != ''">prestore_income_flag = #{prestoreIncomeFlag,jdbcType=VARCHAR},</if>-->
<!--            <if test="oaProjectDeployId != null">oa_project_deploy_id = #{oaProjectDeployId,jdbcType=BIGINT},</if>-->
<!--            <if test="generateCertificateFlag != null and generateCertificateFlag != ''">generate_certificate_flag = #{generateCertificateFlag,jdbcType=VARCHAR},</if>-->
<!--            <if test="accountSetsId != null">account_sets_id = #{accountSetsId,jdbcType=BIGINT},</if>-->
<!--            <if test="guaranteeIncomeType != null and guaranteeIncomeType != ''">guarantee_income_type = #{guaranteeIncomeType,jdbcType=VARCHAR},</if>-->
<!--            <if test="guarantyPayee != null">guaranty_payee = #{guarantyPayee,jdbcType=BIGINT},</if>-->
        </trim>
        where oa_project_deploy_id = #{oaProjectDeployId,jdbcType=BIGINT}
    </update>
    <select id="getNumByprojectId" resultType="int">
        select count(*) from  cw_project where oa_project_deploy_id = #{projectId}
    </select>

    <select id="selectByNameAndProId" resultType="int">
        SELECT count(*) FROM cw_project_cust a LEFT JOIN cw_project b on a.project_id = b.id
                                               LEFT JOIN oa_trader c ON a.oa_trader_id = c.id
        WHERE c.trader_type = '1'
          AND b.oa_project_deploy_id = #{projectId} AND c.account_number = #{number}
    </select>

    <select id="selectCwProjectIdsByOaTraderIdList" resultType="java.lang.Long">
        SELECT DISTINCT cp.id FROM cw_project cp
        INNER JOIN (SELECT cpc.project_id FROM cw_project_cust cpc LEFT JOIN cw_project_fee cpf ON cpf.cust_id=cpc.id
        WHERE cpc.oa_trader_id
        IN
        <if test="list != null and list.size() != 0">
            <foreach collection="list" item="oaTraderId" open="(" separator="," close=")">
                #{oaTraderId,jdbcType=BIGINT}
            </foreach>
        </if>
        AND cpf.cust_id IS NOT NULL AND cpf.cust_id>0
        ) a ON a.project_id=cp.id
    </select>

    <select id="selectCwProjectListByOaProjectDeployIdList" resultMap="CwProjectResult">
        <include refid="selectCwProjectVo"/>
        WHERE project_flag = '0'
        and status = '0'
        <if test="list != null and list.size() != 0">
        and oa_project_deploy_id in
        <foreach collection="list" item="oaProjectDeployId" open="(" separator="," close=")">
            #{oaProjectDeployId,jdbcType=BIGINT}
        </foreach>
        </if>
    </select>

    <select id="selectCwProjectByOaProjectDeployId" resultMap="CwProjectResult">
        <include refid="selectCwProjectVo"/>
        WHERE oa_project_deploy_id=#{oaProjectDeployId,jdbcType=BIGINT} AND project_flag='0' AND status='0'
    </select>

    <select id="selectCwProjectIdAndProjectTypeByOaProjectDeployIdListAndCwProjectFlag" resultType="java.util.Map">
        SELECT
            cp.id,
            odm.data_code AS projectType
        FROM
            cw_project cp
            LEFT JOIN oa_project_deploy opd ON opd.id = cp.oa_project_deploy_id
            LEFT JOIN oa_data_manage odm ON cp.project_type_relevance_type_id = odm.id
        WHERE
            cp.project_flag = #{cwProjectFlag,jdbcType=VARCHAR}
            AND cp.STATUS = '0'
            AND opd.id IN
            <foreach collection="oaProjectDeployIdList" item="oaProjectDeployId" open="(" separator="," close=")">
            #{oaProjectDeployId,jdbcType=BIGINT}
            </foreach>
    </select>
</mapper>
