<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.badsystem.mapper.MechanismFinanceAccountMapper">

    <resultMap type="MechanismFinanceAccount" id="MechanismFinanceAccountResult">
        <result property="id"    column="id"    />
        <result property="mechanismId"    column="mechanism_id"    />
        <result property="accountName"    column="account_name"    />
        <result property="dutyParagraph"    column="duty_paragraph"    />
        <result property="bank"    column="bank"    />
        <result property="bankAccount"    column="bank_account"    />
    </resultMap>

    <sql id="selectMechanismFinanceAccountVo">
        select id, mechanism_id, account_name, duty_paragraph, bank, bank_account from bl_mechanism_finance_account
    </sql>

    <select id="selectMechanismFinanceAccountList" parameterType="MechanismFinanceAccount" resultMap="MechanismFinanceAccountResult">
        <include refid="selectMechanismFinanceAccountVo"/>
        <where>
            <if test="mechanismId != null "> and mechanism_id = #{mechanismId}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="dutyParagraph != null  and dutyParagraph != ''"> and duty_paragraph = #{dutyParagraph}</if>
            <if test="bank != null  and bank != ''"> and bank = #{bank}</if>
            <if test="bankAccount != null  and bankAccount != ''"> and bank_account = #{bankAccount}</if>
        </where>
    </select>

    <select id="selectMechanismFinanceAccountById" parameterType="Long" resultMap="MechanismFinanceAccountResult">
        <include refid="selectMechanismFinanceAccountVo"/>
        where id = #{id}
    </select>

    <insert id="insertMechanismFinanceAccount" parameterType="MechanismFinanceAccount" useGeneratedKeys="true" keyProperty="id">
        insert into bl_mechanism_finance_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mechanismId != null">mechanism_id,</if>
            <if test="accountName != null">account_name,</if>
            <if test="dutyParagraph != null">duty_paragraph,</if>
            <if test="bank != null">bank,</if>
            <if test="bankAccount != null">bank_account,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mechanismId != null">#{mechanismId},</if>
            <if test="accountName != null">#{accountName},</if>
            <if test="dutyParagraph != null">#{dutyParagraph},</if>
            <if test="bank != null">#{bank},</if>
            <if test="bankAccount != null">#{bankAccount},</if>
         </trim>
    </insert>

    <update id="updateMechanismFinanceAccount" parameterType="MechanismFinanceAccount">
        update bl_mechanism_finance_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="mechanismId != null">mechanism_id = #{mechanismId},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="dutyParagraph != null">duty_paragraph = #{dutyParagraph},</if>
            <if test="bank != null">bank = #{bank},</if>
            <if test="bankAccount != null">bank_account = #{bankAccount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMechanismFinanceAccountById" parameterType="Long">
        delete from bl_mechanism_finance_account where id = #{id}
    </delete>

    <delete id="deleteMechanismFinanceAccountByIds" parameterType="String">
        delete from bl_mechanism_finance_account where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchMechanismFinanceAccount" parameterType="java.util.List">
        INSERT INTO bl_mechanism_finance_account
        (mechanism_id, account_name, duty_paragraph, bank, bank_account)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (
            #{item.mechanismId},
            #{item.accountName},
            #{item.dutyParagraph},
            #{item.bank},
            #{item.bankAccount}
            )
        </foreach>
    </insert>

    <delete id="deleteByMechanismId" parameterType="Long">
        delete from bl_mechanism_finance_account where mechanism_id = #{mechanismId}
    </delete>
</mapper>
