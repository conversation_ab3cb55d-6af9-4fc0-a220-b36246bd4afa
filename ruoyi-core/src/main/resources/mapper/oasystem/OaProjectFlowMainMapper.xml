<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaProjectFlowMainMapper">

    <resultMap type="OaProjectFlowMain" id="OaProjectFlowMainResult">
        <result property="id"    column="id"    />
        <result property="companyNo"    column="company_no"    />
        <result property="modelId"    column="model_id"    />
        <result property="modelName"    column="model_name"    />
        <result property="isLinkageCwxmgl"    column="is_linkage_cwxmgl"    />
        <result property="accountingField"    column="accounting_field"    />
        <result property="accountingFieldName"    column="accounting_field_name"    />
        <result property="projectTypeField" column="project_type_field"/>
        <result property="projectTypeFieldName" column="project_type_field_name"/>
        <result property="feeCompanyField" column="fee_company_field"/>
        <result property="feeCompanyFieldName" column="fee_company_field_name"/>
        <result property="projectField"    column="project_field"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBr"    column="create_br"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBr"    column="update_br"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOaProjectFlowMainVo">
        select id, company_no, model_id, model_name, is_linkage_cwxmgl, accounting_field, accounting_field_name,project_type_field,project_type_field_name,fee_company_field,fee_company_field_name, project_field, remark, status, create_br, create_time, update_br, update_time from oa_project_flow_main
    </sql>

    <select id="selectOaProjectFlowMainList" parameterType="OaProjectFlowMain" resultMap="OaProjectFlowMainResult">
        <include refid="selectOaProjectFlowMainVo"/>
        <where>  
            <if test="companyNo != null  and companyNo != ''"> and company_no = #{companyNo}</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="isLinkageCwxmgl != null  and isLinkageCwxmgl != ''"> and is_linkage_cwxmgl = #{isLinkageCwxmgl}</if>
            <if test="accountingField != null  and accountingField != ''"> and accounting_field = #{accountingField}</if>
            <if test="accountingFieldName != null  and accountingFieldName != ''"> and accounting_field_name like concat('%', #{accountingFieldName}, '%')</if>
            <if test="projectField != null  and projectField != ''"> and project_field = #{projectField}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
        </where>
    </select>
    
    <select id="selectOaProjectFlowMainById" parameterType="Long" resultMap="OaProjectFlowMainResult">
        <include refid="selectOaProjectFlowMainVo"/>
        where id = #{id}
    </select>

    <insert id="insertOaProjectFlowMain" parameterType="OaProjectFlowMain" useGeneratedKeys="true" keyProperty="id">
        insert into oa_project_flow_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyNo != null">company_no,</if>
            <if test="modelId != null">model_id,</if>
            <if test="modelName != null">model_name,</if>
            <if test="isLinkageCwxmgl != null">is_linkage_cwxmgl,</if>
            <if test="accountingField != null">accounting_field,</if>
            <if test="accountingFieldName != null">accounting_field_name,</if>
            <if test="projectTypeField != null and projectTypeField != ''">project_type_field,</if>
            <if test="projectTypeFieldName != null and projectTypeFieldName != ''">project_type_field_name,</if>
            <if test="feeCompanyField != null and feeCompanyField != ''">fee_company_field,</if>
            <if test="feeCompanyFieldName != null and feeCompanyFieldName != ''">fee_company_field_name,</if>
            <if test="projectField != null">project_field,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBr != null">create_br,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBr != null">update_br,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="companyNo != null">#{companyNo},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="modelName != null">#{modelName},</if>
            <if test="isLinkageCwxmgl != null">#{isLinkageCwxmgl},</if>
            <if test="accountingField != null">#{accountingField},</if>
            <if test="accountingFieldName != null">#{accountingFieldName},</if>
            <if test="projectTypeField != null and projectTypeField != ''">#{projectTypeField},</if>
            <if test="projectTypeFieldName != null and projectTypeFieldName != ''">#{projectTypeFieldName},</if>
            <if test="feeCompanyField != null and feeCompanyField != ''">#{feeCompanyField},</if>
            <if test="feeCompanyFieldName != null and feeCompanyFieldName != ''">#{feeCompanyFieldName},</if>
            <if test="projectField != null">#{projectField},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBr != null">#{createBr},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBr != null">#{updateBr},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateOaProjectFlowMain" parameterType="OaProjectFlowMain">
        update oa_project_flow_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyNo != null">company_no = #{companyNo},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="modelName != null">model_name = #{modelName},</if>
            <if test="isLinkageCwxmgl != null">is_linkage_cwxmgl = #{isLinkageCwxmgl},</if>
            <if test="accountingField != null">accounting_field = #{accountingField},</if>
            <if test="accountingFieldName != null">accounting_field_name = #{accountingFieldName},</if>
            <if test="projectTypeField != null and projectTypeField != ''">project_type_field = #{projectTypeField},</if>
            <if test="projectTypeFieldName != null and projectTypeFieldName != ''">project_type_field_name = #{projectTypeFieldName},</if>
            <if test="feeCompanyField != null and feeCompanyField != ''">fee_company_field = #{feeCompanyField},</if>
            <if test="feeCompanyFieldName != null and feeCompanyFieldName != ''">fee_company_field_name = #{feeCompanyFieldName},</if>
            <if test="projectField != null">project_field = #{projectField},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBr != null">create_br = #{createBr},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBr != null">update_br = #{updateBr},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOaProjectFlowMainById" parameterType="Long">
        delete from oa_project_flow_main where id = #{id}
    </delete>

    <delete id="deleteOaProjectFlowMainByIds" parameterType="String">
        delete from oa_project_flow_main where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectOaProjectFlowList" resultType="map">
        SELECT a.id,a.company_no AS companyNo,a.is_linkage_cwxmgl as isLinkageCwxmgl,a.accounting_field as accountingField,a.accounting_field_name as accountingFieldName,d.template_name as modelName,a.remark as remark,b.project_name as projectName,c.coll_name as collName,c.coll_abbreviation as collAbbreviation,c.coll_bank_of_deposit as collBankOfDeposit,c.coll_account_number as collAccountNumber,c.pay_bank_of_deposit as payBankOfDeposit,c.pay_account_number as payAccountNumber,c.pay_name as payName,c.pay_abbreviation as payAbbreviation
        FROM oa_project_flow_main  a LEFT JOIN oa_project_flow_association b ON a.id = b.project_flow_main_id LEFT JOIN oa_project_payer_association c ON b.id = c.pfa_id
        LEFT JOIN oa_process_template d on a.model_id = d.parent_id
        <where>
            <if test="id != null "> and a.id = #{id}</if>
            <if test="companyNo != null  and companyNo != ''"> and a.company_no = #{companyNo}</if>
            <if test="modelId != null "> and a.model_id = #{modelId}</if>
            <if test="modelName != null  and modelName != ''"> and a.model_name like concat('%', #{modelName}, '%')</if>
            <if test="remark != null  and remark != ''"> and b.project_name like concat('%', #{remark}, '%')</if>
        </where>
        ORDER BY a.create_time DESC
    </select>
    <select id="getAllData" resultMap="OaProjectFlowMainResult">

        select id, company_no, model_id, model_name, remark, status, create_br, create_time, update_br, update_time from oa_project_flow_main
         GROUP BY id
    </select>
    <select id="getTotal" resultType="java.lang.Long">


        SELECT count(*) FROM oa_project_flow_main  a LEFT JOIN oa_project_flow_association b ON a.id = b.project_flow_main_id LEFT JOIN oa_project_payer_association c ON b.id = c.pfa_id
        <where>
            <if test="id != null "> and a.id = #{id}</if>
            <if test="companyNo != null  and companyNo != ''"> and a.company_no = #{companyNo}</if>
            <if test="modelId != null "> and a.model_id = #{modelId}</if>
            <if test="modelName != null  and modelName != ''"> and a.model_name like concat('%', #{modelName}, '%')</if>
            <if test="remark != null  and remark != ''"> and b.project_name like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <select id="selectOaProjectFlowListByFilterOaApplyIdAndCompanyIdList" resultType="map">
        SELECT a.id,a.company_no AS companyNo,d.template_name as modelName,a.remark as remark,b.project_name as projectName,c.coll_name as collName,c.coll_abbreviation as collAbbreviation,c.coll_bank_of_deposit as collBankOfDeposit,c.coll_account_number as collAccountNumber,c.pay_bank_of_deposit as payBankOfDeposit,c.pay_account_number as payAccountNumber,c.pay_name as payName,c.pay_abbreviation as payAbbreviation
        FROM oa_project_flow_main  a LEFT JOIN oa_project_flow_association b ON a.id = b.project_flow_main_id LEFT JOIN oa_project_payer_association c ON b.id = c.pfa_id
        LEFT JOIN oa_process_template d on a.model_id = d.parent_id
        <where>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND a.company_no IN
                <foreach collection="companyIdList" item="companyNo" open="(" separator="," close=")">
                    #{companyNo,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="filterOaApplyId != null and filterOaApplyId.size() != 0">
                AND a.id IN
                <foreach collection="filterOaApplyId" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="oaProjectFlowMain.id != null "> and a.id = #{oaProjectFlowMain.id}</if>
            <if test="oaProjectFlowMain.companyNo != null  and oaProjectFlowMain.companyNo != ''"> and a.company_no = #{oaProjectFlowMain.companyNo}</if>
            <if test="oaProjectFlowMain.modelId != null "> and a.model_id = #{oaProjectFlowMain.modelId}</if>
            <if test="oaProjectFlowMain.modelName != null  and oaProjectFlowMain.modelName != ''"> and a.model_name like concat('%', #{oaProjectFlowMain.modelName}, '%')</if>
            <if test="oaProjectFlowMain.remark != null  and oaProjectFlowMain.remark != ''"> and b.project_name like concat('%', #{oaProjectFlowMain.remark}, '%')</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="getTotalByCompanyIdList" resultType="java.lang.Long">
        SELECT count(*) FROM oa_project_flow_main  a LEFT JOIN oa_project_flow_association b ON a.id = b.project_flow_main_id LEFT JOIN oa_project_payer_association c ON b.id = c.pfa_id
        <where>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND a.company_no IN
                <foreach collection="companyIdList" item="companyNo" open="(" separator="," close=")">
                    #{companyNo,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="oaProjectFlowMain.id != null "> and a.id = #{oaProjectFlowMain.id}</if>
            <if test="oaProjectFlowMain.companyNo != null  and oaProjectFlowMain.companyNo != ''"> and a.company_no = #{oaProjectFlowMain.companyNo}</if>
            <if test="oaProjectFlowMain.modelId != null "> and a.model_id = #{oaProjectFlowMain.modelId}</if>
            <if test="oaProjectFlowMain.modelName != null  and oaProjectFlowMain.modelName != ''"> and a.model_name like concat('%', #{oaProjectFlowMain.modelName}, '%')</if>
            <if test="oaProjectFlowMain.remark != null  and oaProjectFlowMain.remark != ''"> and b.project_name like concat('%', #{oaProjectFlowMain.remark}, '%')</if>
        </where>
    </select>

    <select id="getTotalByCompanyIdListAndFilterOaApplyId" resultType="java.lang.Long">
        SELECT count(*) FROM (SELECT DISTINCT a.id FROM oa_project_flow_main  a LEFT JOIN oa_project_flow_association b ON a.id = b.project_flow_main_id LEFT JOIN oa_project_payer_association c ON b.id = c.pfa_id
        <where>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND a.company_no IN
                <foreach collection="companyIdList" item="companyNo" open="(" separator="," close=")">
                    #{companyNo,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="filterOaApplyId != null and filterOaApplyId.size() != 0">
                AND a.id IN
                <foreach collection="filterOaApplyId" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="oaProjectFlowMain.id != null "> and a.id = #{oaProjectFlowMain.id}</if>
            <if test="oaProjectFlowMain.companyNo != null  and oaProjectFlowMain.companyNo != ''"> and a.company_no = #{oaProjectFlowMain.companyNo}</if>
            <if test="oaProjectFlowMain.modelId != null "> and a.model_id = #{oaProjectFlowMain.modelId}</if>
            <if test="oaProjectFlowMain.modelName != null  and oaProjectFlowMain.modelName != ''"> and a.model_name like concat('%', #{oaProjectFlowMain.modelName}, '%')</if>
            <if test="oaProjectFlowMain.remark != null  and oaProjectFlowMain.remark != ''"> and b.project_name like concat('%', #{oaProjectFlowMain.remark}, '%')</if>
        </where>) opfm
    </select>
</mapper>