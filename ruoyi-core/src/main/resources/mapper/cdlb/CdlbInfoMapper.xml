<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cdlb.mapper.CdlbInfoMapper">

    <resultMap type="CdlbInfo" id="CdlbInfoResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="projectInId"    column="project_in_id"    />
        <result property="projectOutId"    column="project_out_id"    />
        <result property="contractCode"    column="contract_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="clientCardId"    column="client_card_id"    />
        <result property="loanBinding"    column="loan_binding"    />
        <result property="loanNo"    column="loan_no"    />
        <result property="mailDate"    column="mail_date"    />
        <result property="lbFlag"    column="lb_flag"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_Time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_Time"    />
        <result property="custNo"    column="cust_no"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="cdlbId"    column="cdlbId"    />
        <result property="cdlbRecord"    column="cdlb_record"    />
        <result property="loanId"    column="loan_id"    />

        <result property="reconciliationTime"    column="reconciliation_time"    />
        <result property="registerTime"    column="register_time"    />



        <result property="counts"    column="counts"    />


        <result property="projectName"    column="projectName"    />
        <result property="custId"    column="custId"    />
        <result property="custName"    column="custName"    />
        <result property="custNameA"    column="custNameA"    />
        <result property="childFlag"    column="child_flag"    />
        <result property="temporaryFlag"    column="temporary_flag"    />
        <result property="lbNumber"    column="lb_number"    />
        <result property="balanceAmt"    column="balance_amt"    />

    </resultMap>
    <sql id="countsrk">
        select count(DISTINCT a.id) from cdlb_info a  left join  cdlb_loan_info b  on  a.client_card_id=b.client_card_id
                                         left join  cdlb_project c on a.project_id=c.id
                                                      left join  cdlb_project_user d on a.project_id=d.project_id
    </sql>
    <sql id="countsck">
        select count(DISTINCT a.id) from cdlb_info a   left join  cdlb_loan_info b  on  a.loan_no=b.id
                                                       left join  cdlb_project c on a.project_id=c.id
                                                       left join  cdlb_project_user d on a.project_id=d.project_id
    </sql>
    <sql id="selectCdlbInfoVo">
        select id, project_id,lb_number, project_in_id, child_flag, temporary_flag, project_out_id, contract_code, client_name, client_card_id, loan_binding, loan_no, mail_date, lb_flag, status, create_by, create_Time, update_by, update_Time ,register_time from cdlb_info
    </sql>
    <sql id="selectCdlbInfoleftJoinlianVo">
        select     b.balance_amt, c.project_name  as   projectName , c.cust_id as custId, c.cust_name as custNameA,  a.lb_number,    a.child_flag ,  a.temporary_flag,        a.id as id , b.id as loan_id , b.cdlb_id as cdlbId,     a.project_id, a.project_in_id, a.project_out_id, a.contract_code, a.client_name, a.client_card_id, a.loan_binding, b.loan_no, a.mail_date, a.lb_flag, a.status,  a.create_Time,    b.apply_time,b.loan_amt,b.cust_no,b.cdlb_record ,
                    b.reconciliation_time,a.register_time
                    from cdlb_info a
        left join  cdlb_loan_info b  on  a.client_card_id=b.client_card_id
        left join  cdlb_project c on a.project_id=c.id

</sql>
    <sql id="selectCdlbInfoleftJoinlianchuVo">
        select    b.balance_amt,  c.project_name  as   projectName , c.cust_id as custId, c.cust_name as custNameA,   a.lb_number,       a.child_flag ,    a.temporary_flag,     a.id as id , b.id as loan_id , b.cdlb_id as cdlbId,     a.project_id, a.project_in_id, a.project_out_id, a.contract_code, a.client_name, a.client_card_id, a.loan_binding, b.loan_no, a.mail_date, a.lb_flag, a.status,  a.create_Time,    b.apply_time,b.loan_amt,b.cust_no,b.cdlb_record,
            b.reconciliation_time,a.register_time
        from cdlb_info a
            left join  cdlb_loan_info b  on  a.loan_no=b.id
            left join  cdlb_project c on a.project_id=c.id

</sql>

    <select id="selectCdlbInfoList" parameterType="CdlbInfo" resultMap="CdlbInfoResult">
        <include refid="selectCdlbInfoVo"/>
        <where>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="projectInId != null "> and project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and project_out_id = #{projectOutId}</if>
            <if test="contractCode != null  and contractCode != ''"> and contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and client_card_id = #{clientCardId}</if>
            <if test="loanBinding != null  and loanBinding != ''"> and loan_binding = #{loanBinding}</if>
            <if test="loanNo != null  and loanNo != ''"> and loan_no = #{loanNo}</if>
            <if test="mailDate != null "> and mail_date = #{mailDate}</if>
            <if test="lbFlag != null  and lbFlag != ''"> and lb_flag = #{lbFlag}</if>

            <if test="notlbFlag != null  and notlbFlag != ''"> and lb_flag != #{notlbFlag}</if>



            <if test="idList != null"> and  id in
            <foreach collection="idList" item="ida" open="(" close=")" separator=",">
                #{ida}
            </foreach>
            </if>

            <if test="lbFlagList != null"> and lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>

            <if test="status != null  and status != ''"> and status = #{status}</if>

            <if test="childFlag != null  and childFlag != ''"> and child_flag = #{childFlag}</if>
            <if test="temporaryFlag != null  and temporaryFlag != ''"> and temporary_flag = #{temporaryFlag}</if>
            <if test="lbNumber != null  and lbNumber != ''"> and lb_number = #{lbNumber}</if>

            <if test="createTime != null "> and create_Time = #{createTime}</if>
            <if test="updateTime != null "> and update_Time = #{updateTime}</if>
        </where>
    </select>
    <select id="selectCdlbInfoListxiangqing" parameterType="CdlbInfo" resultMap="CdlbInfoResult">
        <include refid="selectCdlbInfoVo"/>
        <where>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="projectInId != null "> and project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and project_out_id = #{projectOutId}</if>
            <if test="contractCode != null  and contractCode != ''"> and contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and client_card_id = #{clientCardId}</if>
            <if test="loanBinding != null  and loanBinding != ''"> and loan_binding = #{loanBinding}</if>
            <if test="loanNo != null  and loanNo != ''"> and loan_no = #{loanNo}</if>
            <if test="mailDate != null "> and mail_date = #{mailDate}</if>
            <if test="lbFlag != null  and lbFlag != ''"> and lb_flag = #{lbFlag}</if>

            <if test="notlbFlag != null  and notlbFlag != ''"> and lb_flag != #{notlbFlag}</if>



            <if test="idList != null"> and  id in
                <foreach collection="idList" item="ida" open="(" close=")" separator=",">
                    #{ida}
                </foreach>
            </if>

            <if test="lbFlagList != null"> and lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>

            <if test="status != null  and status != ''"> and status = #{status}</if>

            <if test="childFlag != null  and childFlag != ''"> and child_flag = #{childFlag}</if>
            <if test="temporaryFlag != null  and temporaryFlag != ''"> and temporary_flag = #{temporaryFlag}</if>
            <if test="lbNumber != null  and lbNumber != ''"> and lb_number = #{lbNumber}</if>

            <if test="createTime != null "> and create_Time = #{createTime}</if>
            <if test="updateTime != null "> and update_Time = #{updateTime}</if>
        </where>
        order by   if(isnull(lb_number)||lb_number = '',1,0),lb_number asc,id
    </select>
<!--    &#45;&#45;             //合同编号-->
<!--    &#45;&#45;             this.queryParams.contractCode-->
<!--    &#45;&#45;             // 客户姓名-->
<!--    &#45;&#45;             this.queryParams.clientName-->
<!--    &#45;&#45;             //身份证号码-->
<!--    &#45;&#45;             this.queryParams.clientCardId-->
<!--    &#45;&#45;             //  车贷借据编号-->
<!--    &#45;&#45;             this.queryParams.loanNo-->


    <select id="selectCdlbInfoListJoinlian" parameterType="CdlbInfo" resultMap="CdlbInfoResult">
        <include refid="selectCdlbInfoleftJoinlianVo"/>
        <where>

            <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>
            <if test="custId != null"> and c.cust_id = #{custId}</if>

            <if test="projectId != null "> and a.project_id = #{projectId}</if>

            <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
            <if test="loanId != null "> and b.id = #{loanId}</if>

            <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


            <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
            <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>





            <if test="lbFlagList != null"> and a.lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="notlbFlagList != null"> and a.lb_flag   not in
                <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="ids != null   and ids != ''">
                and a.id in
                <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

             <if test="loanIds != null ">
                and b.id in
                <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNos != null ">
                and b.loan_no in
                <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



            <if test="id != null"> and a.id != #{id}</if>

              <if test="isId != null"> and a.id = #{isId}</if>

              <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

            <if test="status != null  and status != ''"> and a.status = #{status}</if>
             <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
            <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
            <if test="as != null  and bs != null ">
                and a.id in( select pp.id	from (select a.id  FROM
                 cdlb_info a
                left join  cdlb_loan_info b  on  a.client_card_id=b.client_card_id
                left join  cdlb_project c on a.project_id=c.id
                left join  cdlb_project_user d on a.project_id=d.project_id
                <where>
                    <if test="userId != null"> and d.user_id = #{userId}</if>
                    <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>
                    <if test="userId != null"> and d.user_id = #{userId}</if>

                    <if test="custId != null"> and c.cust_id = #{custId}</if>

                    <if test="projectId != null "> and a.project_id = #{projectId}</if>

                    <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
                    <if test="loanId != null "> and b.id = #{loanId}</if>

                    <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
                    <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


                    <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
                    <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
                    <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
                    <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>


                    <if test="lbFlagList != null"> and a.lb_flag in
                        <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="notlbFlagList != null"> and a.lb_flag   not in
                        <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="ids != null   and ids != ''">
                        and a.id in
                        <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanIds != null ">
                        and b.id in
                        <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNos != null ">
                        and b.loan_no in
                        <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



                    <if test="id != null"> and a.id != #{id}</if>

                    <if test="isId != null"> and a.id = #{isId}</if>

                    <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

                    <if test="status != null  and status != ''"> and a.status = #{status}</if>
                    <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
                    <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
                </where>
                GROUP BY a.id
                LIMIT  #{as} , #{bs} ) as pp )


            </if>
            order by a.id ,b.id
        </where>

    </select>
    <select id="selectCdlbInfoListJoinlianxiangqing" parameterType="CdlbInfo" resultMap="CdlbInfoResult">
        <include refid="selectCdlbInfoleftJoinlianVo"/>
        <where>

            <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>
            <if test="custId != null"> and c.cust_id = #{custId}</if>

            <if test="projectId != null "> and a.project_id = #{projectId}</if>

            <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
            <if test="loanId != null "> and b.id = #{loanId}</if>

            <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


            <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
            <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>





            <if test="lbFlagList != null"> and a.lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="notlbFlagList != null"> and a.lb_flag   not in
                <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="ids != null   and ids != ''">
                and a.id in
                <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanIds != null ">
                and b.id in
                <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNos != null ">
                and b.loan_no in
                <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



            <if test="id != null"> and a.id != #{id}</if>

            <if test="isId != null"> and a.id = #{isId}</if>

            <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

            <if test="status != null  and status != ''"> and a.status = #{status}</if>
            <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
            <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
            <if test="as != null  and bs != null ">
                and a.id in( select pp.id	from (select a.id  FROM
                cdlb_info a
                left join  cdlb_loan_info b  on  a.client_card_id=b.client_card_id
                left join  cdlb_project c on a.project_id=c.id
                left join  cdlb_project_user d on a.project_id=d.project_id
                <where>
                    <if test="userId != null"> and d.user_id = #{userId}</if>
                    <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>
                    <if test="userId != null"> and d.user_id = #{userId}</if>

                    <if test="custId != null"> and c.cust_id = #{custId}</if>

                    <if test="projectId != null "> and a.project_id = #{projectId}</if>

                    <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
                    <if test="loanId != null "> and b.id = #{loanId}</if>

                    <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
                    <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


                    <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
                    <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
                    <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
                    <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>


                    <if test="lbFlagList != null"> and a.lb_flag in
                        <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="notlbFlagList != null"> and a.lb_flag   not in
                        <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="ids != null   and ids != ''">
                        and a.id in
                        <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanIds != null ">
                        and b.id in
                        <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNos != null ">
                        and b.loan_no in
                        <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



                    <if test="id != null"> and a.id != #{id}</if>

                    <if test="isId != null"> and a.id = #{isId}</if>

                    <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

                    <if test="status != null  and status != ''"> and a.status = #{status}</if>
                    <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
                    <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
                </where>
                GROUP BY a.id
                order by  IF(isnull(a.lb_number)||a.lb_number = '',1,0),a.lb_number asc, a.id
                LIMIT  #{as} , #{bs} ) as pp )


            </if>
            order by   if(isnull(a.lb_number)||a.lb_number = '',1,0),a.lb_number asc,a.id
        </where>

    </select>
 <select id="selectCdlbInfoListJoinlianchu" parameterType="CdlbInfo" resultMap="CdlbInfoResult">
        <include refid="selectCdlbInfoleftJoinlianchuVo"/>
        <where>
            <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>

            <if test="custId != null"> and c.cust_id = #{custId}</if>

            <if test="projectId != null "> and a.project_id = #{projectId}</if>

            <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
            <if test="loanId != null "> and b.id = #{loanId}</if>

            <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


            <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
            <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>





            <if test="lbFlagList != null"> and a.lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="notlbFlagList != null"> and a.lb_flag   not in
                <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="ids != null   and ids != ''">
                and a.id in
                <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

             <if test="loanIds != null ">
                and b.id in
                <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNos != null ">
                and b.loan_no in
                <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



            <if test="id != null"> and a.id != #{id}</if>

              <if test="isId != null"> and a.id = #{isId}</if>

              <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

            <if test="status != null  and status != ''"> and a.status = #{status}</if>
             <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
            <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
            <if test="as != null  and bs != null ">
                and a.id in( select pp.id	from (select a.id  FROM
                cdlb_info a
                left join  cdlb_loan_info b  on  a.client_card_id=b.client_card_id
                left join  cdlb_project c on a.project_id=c.id
                left join  cdlb_project_user d on a.project_id=d.project_id
                <where>
                    <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>

                    <if test="userId != null"> and d.user_id = #{userId}</if>

                    <if test="custId != null"> and c.cust_id = #{custId}</if>

                    <if test="projectId != null "> and a.project_id = #{projectId}</if>

                    <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
                    <if test="loanId != null "> and b.id = #{loanId}</if>

                    <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
                    <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


                    <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
                    <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
                    <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
                    <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>


                    <if test="lbFlagList != null"> and a.lb_flag in
                        <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="notlbFlagList != null"> and a.lb_flag   not in
                        <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="ids != null   and ids != ''">
                        and a.id in
                        <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanIds != null ">
                        and b.id in
                        <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNos != null ">
                        and b.loan_no in
                        <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



                    <if test="id != null"> and a.id != #{id}</if>

                    <if test="isId != null"> and a.id = #{isId}</if>

                    <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

                    <if test="status != null  and status != ''"> and a.status = #{status}</if>
                    <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
                    <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
                </where>
                GROUP BY a.id
                LIMIT  #{as} , #{bs} ) as pp )


            </if>
            order by a.id  desc
        </where>

    </select> <select id="selectCdlbInfoListJoinlianchuxiangqing" parameterType="CdlbInfo" resultMap="CdlbInfoResult">
        <include refid="selectCdlbInfoleftJoinlianchuVo"/>
        <where>
            <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>

            <if test="custId != null"> and c.cust_id = #{custId}</if>

            <if test="projectId != null "> and a.project_id = #{projectId}</if>

            <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
            <if test="loanId != null "> and b.id = #{loanId}</if>

            <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


            <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
            <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>





            <if test="lbFlagList != null"> and a.lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="notlbFlagList != null"> and a.lb_flag   not in
                <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="ids != null   and ids != ''">
                and a.id in
                <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

             <if test="loanIds != null ">
                and b.id in
                <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNos != null ">
                and b.loan_no in
                <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



            <if test="id != null"> and a.id != #{id}</if>

              <if test="isId != null"> and a.id = #{isId}</if>

              <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

            <if test="status != null  and status != ''"> and a.status = #{status}</if>
             <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
            <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
            <if test="as != null  and bs != null ">
                and a.id in( select pp.id	from (select a.id  FROM
                cdlb_info a
                left join  cdlb_loan_info b  on  a.client_card_id=b.client_card_id
                left join  cdlb_project c on a.project_id=c.id
                left join  cdlb_project_user d on a.project_id=d.project_id
                <where>
                    <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>

                    <if test="userId != null"> and d.user_id = #{userId}</if>

                    <if test="custId != null"> and c.cust_id = #{custId}</if>

                    <if test="projectId != null "> and a.project_id = #{projectId}</if>

                    <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
                    <if test="loanId != null "> and b.id = #{loanId}</if>

                    <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
                    <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


                    <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
                    <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
                    <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
                    <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>


                    <if test="lbFlagList != null"> and a.lb_flag in
                        <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="notlbFlagList != null"> and a.lb_flag   not in
                        <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="ids != null   and ids != ''">
                        and a.id in
                        <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanIds != null ">
                        and b.id in
                        <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNos != null ">
                        and b.loan_no in
                        <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



                    <if test="id != null"> and a.id != #{id}</if>

                    <if test="isId != null"> and a.id = #{isId}</if>

                    <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

                    <if test="status != null  and status != ''"> and a.status = #{status}</if>
                    <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
                    <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
                </where>
                GROUP BY a.id
                order by  IF(isnull(a.lb_number)||a.lb_number = '',1,0),a.lb_number asc, a.id
                LIMIT  #{as} , #{bs} ) as pp )


            </if>
            order by   if(isnull(a.lb_number)||a.lb_number = '',1,0),a.lb_number asc,a.id
        </where>

    </select>

    <select id="selecInfoListstate" parameterType="string" resultMap="CdlbInfoResult">
        <include refid="selectCdlbInfoleftJoinlianVo"/>
        where b.cdlb_binding= 'Y' and a.`status`=0 and b.`status` =0
            and  a.lb_flag=#{id}

    </select>

    <select id="selectCdlbInfoById" parameterType="Long" resultMap="CdlbInfoResult">
        <include refid="selectCdlbInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectcounts"  parameterType="Long" resultType="java.lang.Integer">
        select count(id) from cdlb_info
        where project_id = #{id}
               and lb_flag = '12'
                and status = '0'
    </select>
    <select id="selectcounts4"  parameterType="CdlbInfo"  resultType="java.lang.Integer">
        select count(id) from cdlb_info
        where project_id = #{projectId}
        <if test="lbFlagList != null"> and lb_flag in
            <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                #{lbFlag}
            </foreach>
        </if>
          and status = '0'
    </select>
    <select id="selectcountsruku"  parameterType="Long" resultType="java.lang.Integer">
        select count(id) from cdlb_info
        where project_in_id = #{id}
          and status = '0'
    </select>
    <select id="selectlbFlagCountsList"  parameterType="Long" resultMap="CdlbInfoResult">

            SELECT lb_flag, COUNT(lb_flag) as counts FROM cdlb_info
            WHERE lb_flag in (11,12,20,21)  and project_id =#{id}
            and status != '1'
            GROUP BY lb_flag;
    </select>
    <select id="selectClientCardIdCounts" resultMap="CdlbInfoResult">
        SELECT client_card_id, COUNT(client_card_id) as counts FROM cdlb_info
        WHERE   `status`=0
        and lb_flag =#{applyFlag}
        <if test="clientCardIds != null ">
            and client_card_id in
            <foreach item="item" collection="clientCardIds.split(',')" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        GROUP BY client_card_id;
    </select>
    <select id="selectcontractCodeCounts"   resultMap="CdlbInfoResult">
        SELECT contract_code, COUNT(contract_code) as counts FROM cdlb_info
        WHERE   `status`=0
          and lb_flag =#{applyFlag}
        <if test="contractCodes != null ">
            and contract_code in
            <foreach item="item" collection="contractCodes.split(',')" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>

        GROUP BY contract_code;
    </select>
    <select id="selectcountsinfork"  parameterType="CdlbInfo" resultType="java.lang.Integer">
        <include refid="countsrk"/>
        <where>
            <if test="userId != null"> and d.user_id = #{userId}</if>

            <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>

            <if test="custId != null"> and c.cust_id = #{custId}</if>

            <if test="projectId != null "> and a.project_id = #{projectId}</if>

            <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
            <if test="loanId != null "> and b.id = #{loanId}</if>

            <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>

            <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
            <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>

            <if test="lbFlagList != null"> and a.lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="notlbFlagList != null"> and a.lb_flag   not in
                <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="ids != null   and ids != ''">
                and a.id in
                <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanIds != null ">
                and b.id in
                <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNos != null ">
                and b.loan_no in
                <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



            <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

            <if test="status != null  and status != ''"> and a.status = #{status}</if>

            <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>


        </where>

    </select>
    <select id="selectcountsinfock"  parameterType="CdlbInfo" resultType="java.lang.Integer">
        <include refid="countsck"/>
        <where>
            <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>

            <if test="userId != null"> and d.user_id = #{userId}</if>

            <if test="custId != null"> and c.cust_id = #{custId}</if>

            <if test="projectId != null "> and a.project_id = #{projectId}</if>

            <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
            <if test="loanId != null "> and b.id = #{loanId}</if>

            <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


            <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
            <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>


            <if test="lbFlagList != null"> and a.lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="notlbFlagList != null"> and a.lb_flag   not in
                <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="ids != null   and ids != ''">
                and a.id in
                <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanIds != null ">
                and b.id in
                <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNos != null ">
                and b.loan_no in
                <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>

            <if test="id != null"> and a.id != #{id}</if>

            <if test="isId != null"> and a.id = #{isId}</if>

            <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

            <if test="status != null  and status != ''"> and a.status = #{status}</if>
            <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
            <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>

        </where>


    </select>
    <select id="getSonState" parameterType="CdlbInfo"    resultMap="CdlbInfoResult">
        select   COUNT(temporary_flag)as counts ,temporary_flag from cdlb_info
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="projectInId != null "> and project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and project_out_id = #{projectOutId}</if>
        </where>
        GROUP BY temporary_flag
    </select>
    <select id="getSonStatesSave" parameterType="CdlbInfo"    resultMap="CdlbInfoResult">
        select   COUNT(child_flag)as counts ,child_flag from cdlb_info
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="projectInId != null "> and project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and project_out_id = #{projectOutId}</if>
        </where>
        GROUP BY child_flag
    </select>
    <select id="selectMaxlbNumber" resultType="java.lang.String">
        SELECT MAX(lb_number) FROM cdlb_info  where `status` = '0';
    </select>
    <select id="selectCdlbInfoListJoinlianForCount" resultType="java.lang.Long">
        select     count(1)
        from cdlb_info a
        left join  cdlb_loan_info b  on  a.client_card_id=b.client_card_id
        left join  cdlb_project c on a.project_id=c.id
        <where>

            <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>
            <if test="custId != null"> and c.cust_id = #{custId}</if>

            <if test="projectId != null "> and a.project_id = #{projectId}</if>

            <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
            <if test="loanId != null "> and b.id = #{loanId}</if>

            <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


            <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
            <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>





            <if test="lbFlagList != null"> and a.lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="notlbFlagList != null"> and a.lb_flag   not in
                <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="ids != null   and ids != ''">
                and a.id in
                <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanIds != null ">
                and b.id in
                <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNos != null ">
                and b.loan_no in
                <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



            <if test="id != null"> and a.id != #{id}</if>

            <if test="isId != null"> and a.id = #{isId}</if>

            <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

            <if test="status != null  and status != ''"> and a.status = #{status}</if>
            <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
            <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
            <if test="as != null  and bs != null ">
                and a.id in( select pp.id	from (select a.id  FROM
                cdlb_info a
                left join  cdlb_loan_info b  on  a.client_card_id=b.client_card_id
                left join  cdlb_project c on a.project_id=c.id
                left join  cdlb_project_user d on a.project_id=d.project_id
                <where>
                    <if test="userId != null"> and d.user_id = #{userId}</if>
                    <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>
                    <if test="userId != null"> and d.user_id = #{userId}</if>

                    <if test="custId != null"> and c.cust_id = #{custId}</if>

                    <if test="projectId != null "> and a.project_id = #{projectId}</if>

                    <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
                    <if test="loanId != null "> and b.id = #{loanId}</if>

                    <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
                    <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


                    <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
                    <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
                    <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
                    <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>


                    <if test="lbFlagList != null"> and a.lb_flag in
                        <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="notlbFlagList != null"> and a.lb_flag   not in
                        <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="ids != null   and ids != ''">
                        and a.id in
                        <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanIds != null ">
                        and b.id in
                        <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNos != null ">
                        and b.loan_no in
                        <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



                    <if test="id != null"> and a.id != #{id}</if>

                    <if test="isId != null"> and a.id = #{isId}</if>

                    <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

                    <if test="status != null  and status != ''"> and a.status = #{status}</if>
                    <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
                    <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
                </where>
                GROUP BY a.id
                 ) as pp )


            </if>
            order by a.id ,b.id
        </where>

    </select>
    <select id="selectCdlbInfoListJoinlianchuForCount" resultType="java.lang.Long">
        select    count(1)
        from cdlb_info a
        left join  cdlb_loan_info b  on  a.loan_no=b.id
        left join  cdlb_project c on a.project_id=c.id
        <where>
            <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>

            <if test="custId != null"> and c.cust_id = #{custId}</if>

            <if test="projectId != null "> and a.project_id = #{projectId}</if>

            <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
            <if test="loanId != null "> and b.id = #{loanId}</if>

            <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


            <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
            <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
            <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>





            <if test="lbFlagList != null"> and a.lb_flag in
                <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="notlbFlagList != null"> and a.lb_flag   not in
                <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                    #{lbFlag}
                </foreach>
            </if>
            <if test="ids != null   and ids != ''">
                and a.id in
                <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanIds != null ">
                and b.id in
                <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNos != null ">
                and b.loan_no in
                <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>

            <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



            <if test="id != null"> and a.id != #{id}</if>

            <if test="isId != null"> and a.id = #{isId}</if>

            <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

            <if test="status != null  and status != ''"> and a.status = #{status}</if>
            <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
            <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
            <if test="as != null  and bs != null ">
                and a.id in( select pp.id	from (select a.id  FROM
                cdlb_info a
                left join  cdlb_loan_info b  on  a.client_card_id=b.client_card_id
                left join  cdlb_project c on a.project_id=c.id
                left join  cdlb_project_user d on a.project_id=d.project_id
                <where>
                    <if test="temporaryFlag != null"> and a.temporary_flag = #{temporaryFlag}</if>

                    <if test="userId != null"> and d.user_id = #{userId}</if>

                    <if test="custId != null"> and c.cust_id = #{custId}</if>

                    <if test="projectId != null "> and a.project_id = #{projectId}</if>

                    <if test="cdlbId != null "> and a.id = #{cdlbId}</if>
                    <if test="loanId != null "> and b.id = #{loanId}</if>

                    <if test="projectInId != null "> and a.project_in_id = #{projectInId}</if>
                    <if test="projectOutId != null "> and a.project_out_id = #{projectOutId}</if>


                    <if test="contractCode != null  and contractCode != ''"> and a.contract_code = #{contractCode}</if>
                    <if test="clientName != null  and clientName != ''"> and a.client_name like concat('%', #{clientName}, '%')</if>
                    <if test="clientCardId != null  and clientCardId != ''"> and  a.client_card_id = #{clientCardId}</if>
                    <if test="loanNo != null  and loanNo != ''"> and b.loan_no = #{loanNo}</if>


                    <if test="lbFlagList != null"> and a.lb_flag in
                        <foreach item="lbFlag" collection="lbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="notlbFlagList != null"> and a.lb_flag   not in
                        <foreach item="lbFlag" collection="notlbFlagList" open="(" separator="," close=")">
                            #{lbFlag}
                        </foreach>
                    </if>
                    <if test="ids != null   and ids != ''">
                        and a.id in
                        <foreach item="item" collection="ids.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanIds != null ">
                        and b.id in
                        <foreach item="item" collection="loanIds.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNos != null ">
                        and b.loan_no in
                        <foreach item="item" collection="loanNos.split(',')" separator="," open="(" close=")" index="index">
                            #{item}
                        </foreach>
                    </if>

                    <if test="loanNonull != null"> and b.loan_no is ${loanNonull}</if>



                    <if test="id != null"> and a.id != #{id}</if>

                    <if test="isId != null"> and a.id = #{isId}</if>

                    <if test="cdlbRecord != null"> and b.cdlb_record = #{cdlbRecord}</if>

                    <if test="status != null  and status != ''"> and a.status = #{status}</if>
                    <if test="statusB != null  and statusB != ''"> and b.status = #{statusB}</if>
                    <if test="cdlbBinding != null "> and b.cdlb_binding = #{cdlbBinding}</if>
                </where>
                GROUP BY a.id
                ) as pp )


            </if>
            order by a.id  desc
        </where>
    </select>
    <!--    13入库完成-->

    <insert id="insertCdlbInfo" parameterType="CdlbInfo" useGeneratedKeys="true" keyProperty="id">
        insert into cdlb_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="projectInId != null">project_in_id,</if>
            <if test="projectOutId != null">project_out_id,</if>
            <if test="contractCode != null">contract_code,</if>
            <if test="clientName != null and clientName != ''">client_name,</if>
            <if test="clientCardId != null and clientCardId != ''">client_card_id,</if>
            <if test="loanBinding != null and loanBinding != ''">loan_binding,</if>
            <if test="loanNo != null and loanNo != ''">loan_no,</if>
            <if test="mailDate != null">mail_date,</if>
            <if test="lbFlag != null and lbFlag != ''">lb_flag,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_Time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_Time,</if>

            <if test="childFlag != null">child_flag,</if>
            <if test="temporaryFlag != null">temporary_flag,</if>
            <if test="lbNumber != null">lb_number,</if>
            <if test="registerTime != null">register_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="projectInId != null">#{projectInId},</if>
            <if test="projectOutId != null">#{projectOutId},</if>
            <if test="contractCode != null">#{contractCode},</if>
            <if test="clientName != null and clientName != ''">#{clientName},</if>
            <if test="clientCardId != null and clientCardId != ''">#{clientCardId},</if>
            <if test="loanBinding != null and loanBinding != ''">#{loanBinding},</if>
            <if test="loanNo != null and loanNo != ''">#{loanNo},</if>
            <if test="mailDate != null">#{mailDate},</if>
            <if test="lbFlag != null and lbFlag != ''">#{lbFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>

            <if test="childFlag != null">#{childFlag},</if>
            <if test="temporaryFlag != null">#{temporaryFlag},</if>
            <if test="lbNumber != null">#{lbNumber},</if>
            <if test="registerTime != null">#{registerTime},</if>
        </trim>
    </insert>


    <update id="updateCdlbInfo" parameterType="CdlbInfo">
        update cdlb_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="registerTime != null ">register_time = #{registerTime},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="projectInId != null">project_in_id = #{projectInId},</if>
            <if test="projectOutId != null">project_out_id = #{projectOutId},</if>
            <if test="contractCode != null">contract_code = #{contractCode},</if>
            <if test="clientName != null and clientName != ''">client_name = #{clientName},</if>
            <if test="clientCardId != null and clientCardId != ''">client_card_id = #{clientCardId},</if>
            <if test="loanBinding != null and loanBinding != ''">loan_binding = #{loanBinding},</if>
            <if test="loanNo != null and loanNo != ''">loan_no = #{loanNo},</if>
            <if test="mailDate != null">mail_date = #{mailDate},</if>
            <if test="lbFlag != null and lbFlag != ''">lb_flag = #{lbFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_Time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>

            <if test="childFlag != null">child_flag = #{childFlag},</if>
            <if test="temporaryFlag != null">temporary_flag = #{temporaryFlag},</if>
            <if test="lbNumber != null">lb_number = #{lbNumber},</if>
        </trim>
        where id = #{id}
    </update>
 <update id="updateCdlbInfoByprojectInId" parameterType="CdlbInfo">
        update cdlb_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>

            <if test="projectOutId != null">project_out_id = #{projectOutId},</if>
            <if test="contractCode != null">contract_code = #{contractCode},</if>
            <if test="clientName != null and clientName != ''">client_name = #{clientName},</if>
            <if test="clientCardId != null and clientCardId != ''">client_card_id = #{clientCardId},</if>
            <if test="loanBinding != null and loanBinding != ''">loan_binding = #{loanBinding},</if>
            <if test="loanNo != null and loanNo != ''">loan_no = #{loanNo},</if>
            <if test="mailDate != null">mail_date = #{mailDate},</if>
            <if test="lbFlag != null and lbFlag != ''">lb_flag = #{lbFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_Time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>

            <if test="childFlag != null">child_flag = #{childFlag},</if>
            <if test="temporaryFlag != null">temporary_flag = #{temporaryFlag},</if>
            <if test="lbNumber != null">lb_number = #{lbNumber},</if>
        </trim>
        where project_in_id = #{projectInId}
    </update>

    <update id="updateCdlbInfoByprojectInIddto" parameterType="CdlbInfo">
        update cdlb_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="projectInIdDto != null">project_in_id = #{projectInIdDto},</if>
            <if test="projectOutIdDto != null">project_out_id = #{projectOutIdDto},</if>
            <if test="contractCode != null">contract_code = #{contractCode},</if>
            <if test="clientName != null and clientName != ''">client_name = #{clientName},</if>
            <if test="clientCardId != null and clientCardId != ''">client_card_id = #{clientCardId},</if>
            <if test="loanBinding != null and loanBinding != ''">loan_binding = #{loanBinding},</if>
            <if test="loanNo != null and loanNo != ''">loan_no = #{loanNo},</if>
            <if test="mailDate != null">mail_date = #{mailDate},</if>
            <if test="lbFlag != null and lbFlag != ''">lb_flag = #{lbFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_Time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>

            <if test="temporaryFlag != null">temporary_flag = #{temporaryFlag},</if>
            <if test="lbNumber != null">lb_number = #{lbNumber},</if>
        </trim>
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="projectInId != null "> and project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and project_out_id = #{projectOutId}</if>
            <if test="childFlag != null "> and child_flag = #{childFlag}</if>
        </where>

    </update>

    <update id="updateCdlbInfoByprojectoutId" parameterType="CdlbInfo">
        update cdlb_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>


            <if test="contractCode != null">contract_code = #{contractCode},</if>
            <if test="clientName != null and clientName != ''">client_name = #{clientName},</if>
            <if test="clientCardId != null and clientCardId != ''">client_card_id = #{clientCardId},</if>
            <if test="loanBinding != null and loanBinding != ''">loan_binding = #{loanBinding},</if>
            <if test="loanNo != null and loanNo != ''">loan_no = #{loanNo},</if>
            <if test="mailDate != null">mail_date = #{mailDate},</if>
            <if test="lbFlag != null and lbFlag != ''">lb_flag = #{lbFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_Time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>

            <if test="childFlag != null">child_flag = #{childFlag},</if>
            <if test="temporaryFlag != null">temporary_flag = #{temporaryFlag},</if>
            <if test="lbNumber != null">lb_number = #{lbNumber},</if>
        </trim>
        where project_out_id = #{projectOutId}
    </update>
    <update id="updateCdlbInfotemporaryFlag">
        update cdlb_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="childFlag != null">child_flag = #{childFlag},</if>
            <if test="temporaryFlag != null">temporary_flag = #{temporaryFlag},</if>
            <if test="lbNumber != null">lb_number = #{lbNumber},</if>


        </trim>
        where  status = '0'    and    id in
        <foreach collection="idList" item="ida" open="(" close=")" separator=",">
            #{ida}
        </foreach>

    </update>
    <update id="updatezFlags">
        UPDATE cdlb_info  set temporary_flag = child_flag

        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="projectInId != null "> and project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and project_out_id = #{projectOutId}</if>
        </where>
    </update>
    <update id="saveupdatezFlags">
        UPDATE cdlb_info  set   child_flag =  temporary_flag
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="projectInId != null "> and project_in_id = #{projectInId}</if>
            <if test="projectOutId != null "> and project_out_id = #{projectOutId}</if>
        </where>
    </update>
    <update id="updatelbnumber">
        update  cdlb_info  SET  lb_number = NULL
        where
        project_in_id  = #{projectInId}
          and   `status` = '0'
          and 	 child_flag   != '02'
    </update>
    <delete id="deleteCdlbInfoById" parameterType="Long">
        delete from cdlb_info where id = #{id}
    </delete>

    <delete id="deleteCdlbInfoByIds" parameterType="String">
        delete from cdlb_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>



</mapper>