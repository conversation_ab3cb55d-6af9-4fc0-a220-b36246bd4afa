<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.xmglproject.mapper.XmglDynamicMapper">
    
    <resultMap type="org.ruoyi.core.xmglproject.domain.XmglDynamic" id="XmglDynamicResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="projectName"    column="projectName"    />
        <result property="dynamicType"    column="dynamic_type"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="dynamicMsg"    column="dynamic_msg"    />
        <result property="dynamicTime"    column="dynamic_time"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBr"    column="create_br"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBr"    column="update_br"    />
    </resultMap>

    <sql id="selectXmglDynamicVo">
        select id, project_id, dynamic_type, user_id,user_name, dynamic_msg, dynamic_time, status, create_time, create_br, update_time, update_br from xmgl_dynamic
    </sql>

    <select id="selectXmglDynamicList" parameterType="XmglDynamic" resultMap="XmglDynamicResult">
        select xd.id, xd.project_id, xp.project_name as 'projectName', xd.dynamic_type, xd.user_id, xd.user_name, xd.dynamic_msg,
                xd.dynamic_time, xd.status, xd.create_time, xd.create_br, xd.update_time, xd.update_br
        from xmgl_dynamic xd
        left join xmgl_project xp on xd.project_id = xp.id
        <where>
            <if test="projectIdList != null and projectIdList.size() > 0">
                and xp.id in
                <foreach collection="projectIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="projectName != null  and projectName != ''">
                and xp.project_name like concat('%', #{projectName}, '%')
            </if>
            <if test="userId != null">
                and xd.user_id = #{userId}
            </if>
            <if test="userName != null  and userName != ''">
                and xd.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                and date_format(dynamic_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                and date_format(dynamic_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>

        </where>
        /*keep orderby*/
        ORDER BY create_time DESC
    </select>

    <!--<select id="selectXmglDynamicListByProjectId" parameterType="XmglDynamic" resultMap="XmglDynamicResult">
    <include refid="selectXmglDynamicVo"/>
    <where>
    <if test="projectId != null "> and project_id = #{projectId}</if>
    <if test="dynamicType != null  and dynamicType != ''"> and dynamic_type = #{dynamicType}</if>
    <if test="userId != null  and dynamicType != ''"> and user_id = #{userId}</if>
    <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
    <if test="dynamicMsg != null  and dynamicMsg != ''"> and dynamic_msg = #{dynamicMsg}</if>
    <if test="dynamicTime != null "> and dynamic_time = #{dynamicTime}</if>
    <if test="status != null  and status != ''"> and status = #{status}</if>
    <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
    <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
    </where>
    </select>-->

    <select id="selectXmglDynamicById" parameterType="Long" resultMap="XmglDynamicResult">
        <include refid="selectXmglDynamicVo"/>
        where id = #{id}
    </select>

    <insert id="insertXmglDynamic" parameterType="XmglDynamic" useGeneratedKeys="true" keyProperty="id">
        insert into xmgl_dynamic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="dynamicType != null and dynamicType != ''">dynamic_type,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="dynamicMsg != null">dynamic_msg,</if>
            <if test="dynamicTime != null">dynamic_time,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBr != null">create_br,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBr != null">update_br,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="dynamicType != null and dynamicType != ''">#{dynamicType},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="dynamicMsg != null">#{dynamicMsg},</if>
            <if test="dynamicTime != null">#{dynamicTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBr != null">#{createBr},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBr != null">#{updateBr},</if>
         </trim>
    </insert>

    <update id="updateXmglDynamic" parameterType="XmglDynamic">
        update xmgl_dynamic
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="dynamicType != null and dynamicType != ''">dynamic_type = #{dynamicType},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="dynamicMsg != null">dynamic_msg = #{dynamicMsg},</if>
            <if test="dynamicTime != null">dynamic_time = #{dynamicTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBr != null">create_br = #{createBr},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBr != null">update_br = #{updateBr},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteXmglDynamicById" parameterType="Long">
        delete from xmgl_dynamic where id = #{id}
    </delete>

    <delete id="deleteXmglDynamicByIds" parameterType="String">
        delete from xmgl_dynamic where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectXmglDynamicListByProjectId" resultType="org.ruoyi.core.xmglproject.domain.XmglDynamic">
        select * from xmgl_dynamic where project_id = #{projectId} and status = '0' order by create_time desc
    </select>

    <select id="selectProjectManagerListByUserId" resultType="java.lang.Long">
        select ad.third_table_id from auth_main am
            left join auth_detail ad on am.id = ad.auth_main_id
            where am.module_type = 'PROJSETUP' and am.role_type = #{roleType} and am.status = '0' and ad.status = '0'
    </select>

    <select id="selectProjectInfoByUserId" resultType="java.lang.Long">
        select distinct xpd.project_id from xmgl_project_deploy xpd
            left join xmgl_project_user xpu on xpd.project_id = xpu.project_id
            left join xmgl_project_channel xpc on xpc.project_id = xpd.project_id
            where xpu.user_id = #{userId}
              or xpc.channel_id = #{userId}
              and xpd.deploy_id in
            <foreach collection="readList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and xpu.user_flag = '1' and xpc.status = '1'
    </select>

    <select id="selectAnyAuthByUserId" resultType="org.ruoyi.core.xmglproject.domain.AuthDetailVo">
        select am.id as 'authMainId', am.third_id as 'thirdId', ad.id as 'authDtailId', am.role_type as 'roleType',
        ad.third_table_id as 'thirdTableId'
        from auth_main am
        left join auth_detail ad on am.id = ad.auth_main_id
        where am.module_type = 'FINANCEPROJ'
        and am.third_id = #{userId} and am.status = '0'
        <if test="status != null and status != '' and status.equals('0')">
            and ad.status = '0'
        </if>
        <if test="status != null and status != '' and status.equals('1')">
            and ad.status = '1'
        </if>

    </select>

</mapper>