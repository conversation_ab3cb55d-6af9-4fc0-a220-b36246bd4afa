<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.debtConversion.mapper.DebtConversionFileMapper">

    <resultMap type="DebtConversionFileVo" id="DebtConversionFileResult">
        <result property="id"    column="id"    />
        <result property="custId"    column="cust_id"    />
        <result property="debtConversionTheme"    column="debt_conversion_theme"    />
        <result property="noticeLaunchTime"    column="notice_launch_time"    />
        <result property="noticeCompleteTime"    column="notice_complete_time"    />
        <result property="pushStatus"    column="push_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createByName"    column="create_by_name"    />

        <result property="companyShortName"    column="company_short_name"    />
        <result property="conversionCount"    column="conversion_count"    />
    </resultMap>

    <sql id="selectDebtConversionFileVo">
        select id, cust_id, debt_conversion_theme, notice_launch_time, notice_complete_time, push_status, create_by, create_time, update_by, update_time from dc_debt_conversion_file
    </sql>

    <select id="selectDebtConversionFileList" parameterType="DebtConversionFileVo" resultMap="DebtConversionFileResult">
        select
            sdcf.id, sdcf.cust_id, sdcf.debt_conversion_theme, sdcf.notice_launch_time, sdcf.notice_complete_time, sdcf.push_status
             , sdcf.create_by, sdcf.create_time, sdcf.update_by, sdcf.update_time
             , (select count(1) from dc_debt_conversion ddc where ddc.file_id = sdcf.id) as conversion_count
        from dc_debt_conversion_file sdcf
        <where>
            <if test="custId != null "> and cust_id = #{custId}</if>
            <if test="debtConversionTheme != null  and debtConversionTheme != ''"> and debt_conversion_theme like concat('%', #{debtConversionTheme}, '%')</if>
            <if test="noticeLaunchTime != null "> and notice_launch_time = #{noticeLaunchTime}</if>
            <if test="noticeCompleteTime != null "> and notice_complete_time = #{noticeCompleteTime}</if>
            <if test="pushStatus != null  and pushStatus != ''"> and push_status = #{pushStatus}</if>
            <if test="startCreateTime != null"> and date_format(sdcf.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> date_format(#{startCreateTime},'%Y-%m-%d')</if>
            <if test="endCreateTime != null"> and date_format(sdcf.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> date_format(#{endCreateTime},'%Y-%m-%d')</if>
        </where>
    </select>

    <select id="selectDebtConversionFileById" parameterType="Long" resultMap="DebtConversionFileResult">
        <include refid="selectDebtConversionFileVo"/>
        where id = #{id}
    </select>

    <insert id="insertDebtConversionFile" parameterType="DebtConversionFile" useGeneratedKeys="true" keyProperty="id">
        insert into dc_debt_conversion_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="custId != null">cust_id,</if>
            <if test="debtConversionTheme != null">debt_conversion_theme,</if>
            <if test="noticeLaunchTime != null">notice_launch_time,</if>
            <if test="noticeCompleteTime != null">notice_complete_time,</if>
            <if test="pushStatus != null">push_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="custId != null">#{custId},</if>
            <if test="debtConversionTheme != null">#{debtConversionTheme},</if>
            <if test="noticeLaunchTime != null">#{noticeLaunchTime},</if>
            <if test="noticeCompleteTime != null">#{noticeCompleteTime},</if>
            <if test="pushStatus != null">#{pushStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDebtConversionFile" parameterType="DebtConversionFile">
        update dc_debt_conversion_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="custId != null">cust_id = #{custId},</if>
            <if test="debtConversionTheme != null">debt_conversion_theme = #{debtConversionTheme},</if>
            <if test="noticeLaunchTime != null">notice_launch_time = #{noticeLaunchTime},</if>
            <if test="noticeCompleteTime != null">notice_complete_time = #{noticeCompleteTime},</if>
            <if test="pushStatus != null">push_status = #{pushStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDebtConversionFileById" parameterType="Long">
        delete from dc_debt_conversion_file where id = #{id}
    </delete>

    <delete id="deleteDebtConversionFileByIds" parameterType="String">
        delete from dc_debt_conversion_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
