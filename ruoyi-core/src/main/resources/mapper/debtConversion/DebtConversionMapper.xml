<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.debtConversion.mapper.DebtConversionMapper">

    <resultMap type="DebtConversionVo" id="DebtConversionResult">
        <result property="id"    column="id"    />
        <result property="fileId"    column="file_id"    />
        <result property="loanCode"    column="loan_code"    />
        <result property="borrower"    column="borrower"    />
        <result property="idCard"    column="id_card"    />
        <result property="phoneNum"    column="phone_num"    />
        <result property="realIdCard"    column="real_id_card"    />
        <result property="realPhoneNum"    column="real_phone_num"    />
        <result property="loanTime"    column="loan_time"    />
        <result property="guaranteeTime"    column="guarantee_time"    />
        <result property="custId"    column="cust_id"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="fundId"    column="fund_id"    />
        <result property="loanAmount"    column="loan_amount"    />
        <result property="debtRecipientId"    column="debt_recipient_id"    />
        <result property="debtConversionCode"    column="debt_conversion_code"    />
        <result property="invoiceStatus"    column="invoice_status"    />
        <result property="pushChannel"    column="push_channel"    />
        <result property="isRead"    column="is_read"    />
        <result property="readTime"    column="read_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="noticeCompleteTime"    column="notice_complete_time"    />
        <result property="noticeLaunchTime"    column="notice_launch_time"    />
    </resultMap>

    <sql id="selectDebtConversionVo">
        select id, file_id, loan_code, borrower, id_card, phone_num, loan_time, guarantee_time, cust_id, partner_id, fund_id, loan_amount, debt_recipient_id, debt_conversion_code, invoice_status, push_channel, is_read, read_time, create_by, create_time, update_by, update_time from dc_debt_conversion
    </sql>

    <select id="selectDebtConversionList" parameterType="DebtConversionVo" resultMap="DebtConversionResult">
        select ddc.id, ddc.file_id, ddc.loan_code, ddc.borrower
            , CONCAT(LEFT(ddc.id_card, 4),'*******',RIGHT(ddc.id_card, 4)) AS id_card
            , CONCAT(LEFT(ddc.phone_num, 3),'****',RIGHT(ddc.phone_num, 4)) AS phone_num
            , ddc.id_card AS real_id_card
            , ddc.phone_num AS real_phone_num
            , ddc.loan_time, guarantee_time, ddc.cust_id, partner_id, ddc.fund_id, ddc.loan_amount
            , ddc.debt_recipient_id, ddc.debt_conversion_code, ddc.invoice_status, ddc.push_channel, ddc.is_read, ddc.read_time
            , ddc.create_by, ddc.create_time, ddc.update_by, ddc.update_time
            , ddcf.notice_complete_time
            , ddcf.notice_launch_time
        from dc_debt_conversion ddc
        left join dc_debt_conversion_file ddcf on ddc.file_id = ddcf.id
        left join dc_invoicing_middle dim on ddc.id = dim.correlation_id and dim.correlation_type = '2'
        <where>
            <if test="fileId != null "> and ddc.file_id = #{fileId}</if>
            <if test="loanCode != null  and loanCode != ''"> and ddc.loan_code = #{loanCode}</if>
            <if test="invoicingBusinessId != null "> and invoicing_business_id = #{invoicingBusinessId}</if>
            <if test="borrower != null  and borrower != ''"> and ddc.borrower like concat('%', #{borrower}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and ddc.id_card like concat('%', #{idCard}, '%')</if>
            <if test="phoneNum != null  and phoneNum != ''"> and ddc.phone_num like concat('%', #{phoneNum}, '%')</if>
            <if test="pushStatus != null  and pushStatus != ''"> and ddcf.push_status = #{pushStatus}</if>
            <if test="loanTime != null "> and ddc.loan_time = #{loanTime}</if>
            <if test="guaranteeTime != null "> and ddc.guarantee_time = #{guaranteeTime}</if>
            <if test="custId != null "> and ddc.cust_id = #{custId}</if>
            <if test="partnerId != null "> and ddc.partner_id = #{partnerId}</if>
            <if test="fundId != null "> and ddc.fund_id = #{fundId}</if>
            <if test="loanAmount != null "> and ddc.loan_amount = #{loanAmount}</if>
            <if test="debtRecipientId != null "> and ddc.debt_recipient_id = #{debtRecipientId}</if>
            <if test="debtConversionCode != null  and debtConversionCode != ''"> and ddc.debt_conversion_code = #{debtConversionCode}</if>
            <if test="invoiceStatus != null  and invoiceStatus != ''"> and ddc.invoice_status = #{invoiceStatus}</if>
            <if test="pushChannel != null  and pushChannel != ''"> and ddc.push_channel = #{pushChannel}</if>
            <if test="isRead != null  and isRead != ''"> and ddc.is_read = #{isRead}</if>
            <if test="readTime != null "> and ddc.read_time = #{readTime}</if>
            <if test="ids != null and ids.size() > 0">
                and ddc.id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY ddc.id DESC;
    </select>

    <select id="selectDebtConversionImportList" parameterType="DebtConversionImport" resultType="DebtConversionImport">
        select id, file_id, loan_code, borrower, id_card, phone_num, loan_time, guarantee_time, cust_id, partner_id
             , fund_id, loan_amount, debt_recipient_id, debt_conversion_code, invoice_status, push_channel, is_read, read_time
             , create_by, create_time, update_by, update_time
        from dc_debt_conversion
        <where>
            <if test="fileId != null "> and file_id = #{fileId}</if>
            <if test="loanCodeList != null and loanCodeList.size() > 0">
                and loan_code in
                <foreach collection="loanCodeList" item="loanCode" separator="," open="(" close=")">
                    #{loanCode}
                </foreach>
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectDebtConversionById" parameterType="Long" resultMap="DebtConversionResult">
        select ddc.id, ddc.file_id, ddc.loan_code, ddc.borrower
             , CONCAT(LEFT(ddc.id_card, 4),'*******',RIGHT(ddc.id_card, 4)) AS id_card
             , CONCAT(LEFT(ddc.phone_num, 3),'****',RIGHT(ddc.phone_num, 4)) AS phone_num
             , ddc.loan_time, guarantee_time, ddc.cust_id, partner_id, ddc.fund_id, ddc.loan_amount
             , ddc.debt_recipient_id, ddc.debt_conversion_code, ddc.invoice_status, ddc.push_channel, ddc.is_read, ddc.read_time
             , ddc.create_by, ddc.create_time, ddc.update_by, ddc.update_time
             , ddcf.notice_complete_time
             , ddcf.notice_launch_time
        from dc_debt_conversion ddc
        left join dc_debt_conversion_file ddcf on ddc.file_id = ddcf.id
        where ddc.id = #{id}
    </select>

    <insert id="insertDebtConversion" parameterType="DebtConversion" useGeneratedKeys="true" keyProperty="id">
        insert into dc_debt_conversion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">file_id,</if>
            <if test="loanCode != null">loan_code,</if>
            <if test="borrower != null">borrower,</if>
            <if test="idCard != null">id_card,</if>
            <if test="phoneNum != null">phone_num,</if>
            <if test="loanTime != null">loan_time,</if>
            <if test="guaranteeTime != null">guarantee_time,</if>
            <if test="custId != null">cust_id,</if>
            <if test="partnerId != null">partner_id,</if>
            <if test="fundId != null">fund_id,</if>
            <if test="loanAmount != null">loan_amount,</if>
            <if test="debtRecipientId != null">debt_recipient_id,</if>
            <if test="debtConversionCode != null">debt_conversion_code,</if>
            <if test="invoiceStatus != null">invoice_status,</if>
            <if test="pushChannel != null">push_channel,</if>
            <if test="isRead != null">is_read,</if>
            <if test="readTime != null">read_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">#{fileId},</if>
            <if test="loanCode != null">#{loanCode},</if>
            <if test="borrower != null">#{borrower},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="phoneNum != null">#{phoneNum},</if>
            <if test="loanTime != null">#{loanTime},</if>
            <if test="guaranteeTime != null">#{guaranteeTime},</if>
            <if test="custId != null">#{custId},</if>
            <if test="partnerId != null">#{partnerId},</if>
            <if test="fundId != null">#{fundId},</if>
            <if test="loanAmount != null">#{loanAmount},</if>
            <if test="debtRecipientId != null">#{debtRecipientId},</if>
            <if test="debtConversionCode != null">#{debtConversionCode},</if>
            <if test="invoiceStatus != null">#{invoiceStatus},</if>
            <if test="pushChannel != null">#{pushChannel},</if>
            <if test="isRead != null">#{isRead},</if>
            <if test="readTime != null">#{readTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateDebtConversion" parameterType="DebtConversion">
        update dc_debt_conversion
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="loanCode != null">loan_code = #{loanCode},</if>
            <if test="borrower != null">borrower = #{borrower},</if>
<!--            <if test="idCard != null">id_card = #{idCard},</if>-->
<!--            <if test="phoneNum != null">phone_num = #{phoneNum},</if>-->
            <if test="loanTime != null">loan_time = #{loanTime},</if>
            <if test="guaranteeTime != null">guarantee_time = #{guaranteeTime},</if>
            <if test="custId != null">cust_id = #{custId},</if>
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="fundId != null">fund_id = #{fundId},</if>
            <if test="loanAmount != null">loan_amount = #{loanAmount},</if>
            <if test="debtRecipientId != null">debt_recipient_id = #{debtRecipientId},</if>
            <if test="debtConversionCode != null">debt_conversion_code = #{debtConversionCode},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="pushChannel != null">push_channel = #{pushChannel},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
            <if test="readTime != null">read_time = #{readTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDebtConversionById" parameterType="Long">
        delete from dc_debt_conversion where id = #{id}
    </delete>

    <delete id="deleteDebtConversionByFildId" parameterType="Long">
        delete from dc_debt_conversion where file_id = #{id}
    </delete>

    <delete id="deleteDebtConversionByIds" parameterType="String">
        delete from dc_debt_conversion where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertDebtConversionBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into dc_debt_conversion
        (
            file_id,
            loan_code,
            borrower,
            id_card,
            phone_num,
            loan_time,
            guarantee_time,
            cust_id,
            partner_id,
            fund_id,
            loan_amount,
            debt_recipient_id,
            push_channel,
            create_by,
            create_time
            )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.fileId},
            #{item.loanCode},
            #{item.borrower},
            #{item.idCard},
            #{item.phoneNum},
            #{item.loanTime},
            #{item.guaranteeTime},
            #{item.custId},
            #{item.partnerId},
            #{item.fundId},
            #{item.loanAmount},
            #{item.debtRecipientId},
            #{item.pushChannel},
            #{item.createBy},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <update id="batchUpdateDebtConversionCode" parameterType="java.util.List">
        UPDATE dc_debt_conversion
        SET debt_conversion_code = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN #{item.debtConversionCode}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateInvoicingApplicationCode" parameterType="DebtConversionVo">
        update dc_debt_conversion
        <trim prefix="SET" suffixOverrides=",">
            invoice_status = #{invoiceStatus},
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id in
        <if test="ids != null and ids.size() > 0">
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </update>
</mapper>
