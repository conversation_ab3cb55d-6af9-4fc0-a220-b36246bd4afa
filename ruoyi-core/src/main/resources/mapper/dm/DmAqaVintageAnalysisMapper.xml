<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.dm.mapper.DmAqaVintageAnalysisMapper">

    <resultMap type="DmAqaVintageAnalysisVo" id="DmAqaVintageAnalysisResult">
        <result property="id"    column="id"    />
        <result property="overdueCaliber"    column="overdue_caliber"    />
        <result property="productNo"    column="product_no"    />
        <result property="productName"    column="product_name"    />
        <result property="loanBalDate"    column="loan_bal_date"    />
        <result property="cusLoanBalAmt"    column="cus_loan_bal_amt"    />
        <result property="cfundLoanBalAmt"    column="cfund_loan_bal_amt"    />
        <result property="cusLoanBalAmtPay"    column="cus_loan_bal_amt_pay"    />
        <result property="cfundLoanBalAmtPay"    column="cfund_loan_bal_amt_pay"    />
        <result property="loanMonth"    column="loan_month"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="odueMob1"    column="odue_mob1"    />
        <result property="odueMob2"    column="odue_mob2"    />
        <result property="odueMob3"    column="odue_mob3"    />
        <result property="odueMob4"    column="odue_mob4"    />
        <result property="odueMob5"    column="odue_mob5"    />
        <result property="odueMob6"    column="odue_mob6"    />
        <result property="odueMob7"    column="odue_mob7"    />
        <result property="odueMob8"    column="odue_mob8"    />
        <result property="odueMob9"    column="odue_mob9"    />
        <result property="odueMob10"    column="odue_mob10"    />
        <result property="odueMob11"    column="odue_mob11"    />
        <result property="odueMob12"    column="odue_mob12"    />
        <result property="odueMob13"    column="odue_mob13"    />
        <result property="odueMob14"    column="odue_mob14"    />
        <result property="odueMob15"    column="odue_mob15"    />
        <result property="odueMob16"    column="odue_mob16"    />
        <result property="odueMob17"    column="odue_mob17"    />
        <result property="odueMob18"    column="odue_mob18"    />
        <result property="odueMob19"    column="odue_mob19"    />
        <result property="odueMob20"    column="odue_mob20"    />
        <result property="odueMob21"    column="odue_mob21"    />
        <result property="odueMob22"    column="odue_mob22"    />
        <result property="odueMob23"    column="odue_mob23"    />
        <result property="odueMob24"    column="odue_mob24"    />
        <result property="mob1"    column="mob1"    />
        <result property="mob2"    column="mob2"    />
        <result property="mob3"    column="mob3"    />
        <result property="mob4"    column="mob4"    />
        <result property="mob5"    column="mob5"    />
        <result property="mob6"    column="mob6"    />
        <result property="mob7"    column="mob7"    />
        <result property="mob8"    column="mob8"    />
        <result property="mob9"    column="mob9"    />
        <result property="mob10"    column="mob10"    />
        <result property="mob11"    column="mob11"    />
        <result property="mob12"    column="mob12"    />
        <result property="mob13"    column="mob13"    />
        <result property="mob14"    column="mob14"    />
        <result property="mob15"    column="mob15"    />
        <result property="mob16"    column="mob16"    />
        <result property="mob17"    column="mob17"    />
        <result property="mob18"    column="mob18"    />
        <result property="mob19"    column="mob19"    />
        <result property="mob20"    column="mob20"    />
        <result property="mob21"    column="mob21"    />
        <result property="mob22"    column="mob22"    />
        <result property="mob23"    column="mob23"    />
        <result property="mob24"    column="mob24"    />
    </resultMap>

    <sql id="selectDmAqaVintageAnalysisVo">
        select id, overdue_caliber, product_no, loan_bal_date, cus_loan_bal_amt, cfund_loan_bal_amt, cus_loan_bal_amt_pay, cfund_loan_bal_amt_pay, loan_month, loan_amt, odue_mob1, odue_mob2, odue_mob3, odue_mob4, odue_mob5, odue_mob6, odue_mob7, odue_mob8, odue_mob9, odue_mob10, odue_mob11, odue_mob12, odue_mob13, odue_mob14, odue_mob15, odue_mob16, odue_mob17, odue_mob18, odue_mob19, odue_mob20, odue_mob21, odue_mob22, odue_mob23, odue_mob24 from dm_aqa_vintage_analysis
    </sql>

    <select id="selectDmAqaVintageAnalysisList" parameterType="DmAqaVintageAnalysisVo" resultMap="DmAqaVintageAnalysisResult">
        select
            dava.id, dpp.product_name, overdue_caliber, dava.product_no, loan_bal_date,
            cus_loan_bal_amt, cfund_loan_bal_amt, cus_loan_bal_amt_pay, cfund_loan_bal_amt_pay,
            loan_month, loan_amt,
            ROUND((odue_mob1 / loan_amt) * 100, 2) AS mob1,
            ROUND((odue_mob2 / loan_amt) * 100, 2) AS mob2,
            ROUND((odue_mob3 / loan_amt) * 100, 2) AS mob3,
            ROUND((odue_mob4 / loan_amt) * 100, 2) AS mob4,
            ROUND((odue_mob5 / loan_amt) * 100, 2) AS mob5,
            ROUND((odue_mob6 / loan_amt) * 100, 2) AS mob6,
            ROUND((odue_mob7 / loan_amt) * 100, 2) AS mob7,
            ROUND((odue_mob8 / loan_amt) * 100, 2) AS mob8,
            ROUND((odue_mob9 / loan_amt) * 100, 2) AS mob9,
            ROUND((odue_mob10 / loan_amt) * 100, 2) AS mob10,
            ROUND((odue_mob11 / loan_amt) * 100, 2) AS mob11,
            ROUND((odue_mob12 / loan_amt) * 100, 2) AS mob12,
            ROUND((odue_mob13 / loan_amt) * 100, 2) AS mob13,
            ROUND((odue_mob14 / loan_amt) * 100, 2) AS mob14,
            ROUND((odue_mob15 / loan_amt) * 100, 2) AS mob15,
            ROUND((odue_mob16 / loan_amt) * 100, 2) AS mob16,
            ROUND((odue_mob17 / loan_amt) * 100, 2) AS mob17,
            ROUND((odue_mob18 / loan_amt) * 100, 2) AS mob18,
            ROUND((odue_mob19 / loan_amt) * 100, 2) AS mob19,
            ROUND((odue_mob20 / loan_amt) * 100, 2) AS mob20,
            ROUND((odue_mob21 / loan_amt) * 100, 2) AS mob21,
            ROUND((odue_mob22 / loan_amt) * 100, 2) AS mob22,
            ROUND((odue_mob23 / loan_amt) * 100, 2) AS mob23,
            ROUND((odue_mob24 / loan_amt) * 100, 2) AS mob24,
            odue_mob1, odue_mob2, odue_mob3, odue_mob4, odue_mob5, odue_mob6, odue_mob7, odue_mob8, odue_mob9, odue_mob10, odue_mob11, odue_mob12,
            odue_mob13, odue_mob14, odue_mob15, odue_mob16, odue_mob17, odue_mob18, odue_mob19, odue_mob20, odue_mob21, odue_mob22, odue_mob23, odue_mob24
        from dm_aqa_vintage_analysis dava
        left join d_project_parameter dpp on dava.product_no = dpp.product_no
        <where>
            <if test="overdueCaliber != null  and overdueCaliber != ''"> and overdue_caliber = #{overdueCaliber}</if>
            <if test="productNo != null  and productNo != ''"> and dava.product_no = #{productNo}</if>
            <if test="loanBalDate != null "> and loan_bal_date = #{loanBalDate}</if>
            <if test="cusLoanBalAmt != null "> and cus_loan_bal_amt = #{cusLoanBalAmt}</if>
            <if test="cfundLoanBalAmt != null "> and cfund_loan_bal_amt = #{cfundLoanBalAmt}</if>
            <if test="cusLoanBalAmtPay != null "> and cus_loan_bal_amt_pay = #{cusLoanBalAmtPay}</if>
            <if test="cfundLoanBalAmtPay != null "> and cfund_loan_bal_amt_pay = #{cfundLoanBalAmtPay}</if>
            <if test="loanMonth != null  and loanMonth != ''"> and loan_month = #{loanMonth}</if>
            <if test="loanAmt != null "> and loan_amt = #{loanAmt}</if>
            <if test="productNos != null and productNos.size() > 0">
                and dava.product_no in
                <foreach item="productNo" collection="productNos" open="(" close=")" separator=",">
                    #{productNo}
                </foreach>
            </if>
        </where>
         ORDER BY
         dava.product_no desc
        <if test="sortMap != null and sortMap.size() > 0">
            <foreach collection="sortMap.entrySet()" item="value" index="key" >
                ,${key}  ${value}
            </foreach>
        </if>
    </select>

    <select id="selectDmAqaVintageAnalysisById" parameterType="Long" resultMap="DmAqaVintageAnalysisResult">
        <include refid="selectDmAqaVintageAnalysisVo"/>
        where id = #{id}
    </select>

    <insert id="insertDmAqaVintageAnalysis" parameterType="DmAqaVintageAnalysis" useGeneratedKeys="true" keyProperty="id">
        insert into dm_aqa_vintage_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="overdueCaliber != null">overdue_caliber,</if>
            <if test="productNo != null">product_no,</if>
            <if test="loanBalDate != null">loan_bal_date,</if>
            <if test="cusLoanBalAmt != null">cus_loan_bal_amt,</if>
            <if test="cfundLoanBalAmt != null">cfund_loan_bal_amt,</if>
            <if test="cusLoanBalAmtPay != null">cus_loan_bal_amt_pay,</if>
            <if test="cfundLoanBalAmtPay != null">cfund_loan_bal_amt_pay,</if>
            <if test="loanMonth != null">loan_month,</if>
            <if test="loanAmt != null">loan_amt,</if>
            <if test="odueMob1 != null">odue_mob1,</if>
            <if test="odueMob2 != null">odue_mob2,</if>
            <if test="odueMob3 != null">odue_mob3,</if>
            <if test="odueMob4 != null">odue_mob4,</if>
            <if test="odueMob5 != null">odue_mob5,</if>
            <if test="odueMob6 != null">odue_mob6,</if>
            <if test="odueMob7 != null">odue_mob7,</if>
            <if test="odueMob8 != null">odue_mob8,</if>
            <if test="odueMob9 != null">odue_mob9,</if>
            <if test="odueMob10 != null">odue_mob10,</if>
            <if test="odueMob11 != null">odue_mob11,</if>
            <if test="odueMob12 != null">odue_mob12,</if>
            <if test="odueMob13 != null">odue_mob13,</if>
            <if test="odueMob14 != null">odue_mob14,</if>
            <if test="odueMob15 != null">odue_mob15,</if>
            <if test="odueMob16 != null">odue_mob16,</if>
            <if test="odueMob17 != null">odue_mob17,</if>
            <if test="odueMob18 != null">odue_mob18,</if>
            <if test="odueMob19 != null">odue_mob19,</if>
            <if test="odueMob20 != null">odue_mob20,</if>
            <if test="odueMob21 != null">odue_mob21,</if>
            <if test="odueMob22 != null">odue_mob22,</if>
            <if test="odueMob23 != null">odue_mob23,</if>
            <if test="odueMob24 != null">odue_mob24,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="overdueCaliber != null">#{overdueCaliber},</if>
            <if test="productNo != null">#{productNo},</if>
            <if test="loanBalDate != null">#{loanBalDate},</if>
            <if test="cusLoanBalAmt != null">#{cusLoanBalAmt},</if>
            <if test="cfundLoanBalAmt != null">#{cfundLoanBalAmt},</if>
            <if test="cusLoanBalAmtPay != null">#{cusLoanBalAmtPay},</if>
            <if test="cfundLoanBalAmtPay != null">#{cfundLoanBalAmtPay},</if>
            <if test="loanMonth != null">#{loanMonth},</if>
            <if test="loanAmt != null">#{loanAmt},</if>
            <if test="odueMob1 != null">#{odueMob1},</if>
            <if test="odueMob2 != null">#{odueMob2},</if>
            <if test="odueMob3 != null">#{odueMob3},</if>
            <if test="odueMob4 != null">#{odueMob4},</if>
            <if test="odueMob5 != null">#{odueMob5},</if>
            <if test="odueMob6 != null">#{odueMob6},</if>
            <if test="odueMob7 != null">#{odueMob7},</if>
            <if test="odueMob8 != null">#{odueMob8},</if>
            <if test="odueMob9 != null">#{odueMob9},</if>
            <if test="odueMob10 != null">#{odueMob10},</if>
            <if test="odueMob11 != null">#{odueMob11},</if>
            <if test="odueMob12 != null">#{odueMob12},</if>
            <if test="odueMob13 != null">#{odueMob13},</if>
            <if test="odueMob14 != null">#{odueMob14},</if>
            <if test="odueMob15 != null">#{odueMob15},</if>
            <if test="odueMob16 != null">#{odueMob16},</if>
            <if test="odueMob17 != null">#{odueMob17},</if>
            <if test="odueMob18 != null">#{odueMob18},</if>
            <if test="odueMob19 != null">#{odueMob19},</if>
            <if test="odueMob20 != null">#{odueMob20},</if>
            <if test="odueMob21 != null">#{odueMob21},</if>
            <if test="odueMob22 != null">#{odueMob22},</if>
            <if test="odueMob23 != null">#{odueMob23},</if>
            <if test="odueMob24 != null">#{odueMob24},</if>
         </trim>
    </insert>

    <update id="updateDmAqaVintageAnalysis" parameterType="DmAqaVintageAnalysis">
        update dm_aqa_vintage_analysis
        <trim prefix="SET" suffixOverrides=",">
            <if test="overdueCaliber != null">overdue_caliber = #{overdueCaliber},</if>
            <if test="productNo != null">product_no = #{productNo},</if>
            <if test="loanBalDate != null">loan_bal_date = #{loanBalDate},</if>
            <if test="cusLoanBalAmt != null">cus_loan_bal_amt = #{cusLoanBalAmt},</if>
            <if test="cfundLoanBalAmt != null">cfund_loan_bal_amt = #{cfundLoanBalAmt},</if>
            <if test="cusLoanBalAmtPay != null">cus_loan_bal_amt_pay = #{cusLoanBalAmtPay},</if>
            <if test="cfundLoanBalAmtPay != null">cfund_loan_bal_amt_pay = #{cfundLoanBalAmtPay},</if>
            <if test="loanMonth != null">loan_month = #{loanMonth},</if>
            <if test="loanAmt != null">loan_amt = #{loanAmt},</if>
            <if test="odueMob1 != null">odue_mob1 = #{odueMob1},</if>
            <if test="odueMob2 != null">odue_mob2 = #{odueMob2},</if>
            <if test="odueMob3 != null">odue_mob3 = #{odueMob3},</if>
            <if test="odueMob4 != null">odue_mob4 = #{odueMob4},</if>
            <if test="odueMob5 != null">odue_mob5 = #{odueMob5},</if>
            <if test="odueMob6 != null">odue_mob6 = #{odueMob6},</if>
            <if test="odueMob7 != null">odue_mob7 = #{odueMob7},</if>
            <if test="odueMob8 != null">odue_mob8 = #{odueMob8},</if>
            <if test="odueMob9 != null">odue_mob9 = #{odueMob9},</if>
            <if test="odueMob10 != null">odue_mob10 = #{odueMob10},</if>
            <if test="odueMob11 != null">odue_mob11 = #{odueMob11},</if>
            <if test="odueMob12 != null">odue_mob12 = #{odueMob12},</if>
            <if test="odueMob13 != null">odue_mob13 = #{odueMob13},</if>
            <if test="odueMob14 != null">odue_mob14 = #{odueMob14},</if>
            <if test="odueMob15 != null">odue_mob15 = #{odueMob15},</if>
            <if test="odueMob16 != null">odue_mob16 = #{odueMob16},</if>
            <if test="odueMob17 != null">odue_mob17 = #{odueMob17},</if>
            <if test="odueMob18 != null">odue_mob18 = #{odueMob18},</if>
            <if test="odueMob19 != null">odue_mob19 = #{odueMob19},</if>
            <if test="odueMob20 != null">odue_mob20 = #{odueMob20},</if>
            <if test="odueMob21 != null">odue_mob21 = #{odueMob21},</if>
            <if test="odueMob22 != null">odue_mob22 = #{odueMob22},</if>
            <if test="odueMob23 != null">odue_mob23 = #{odueMob23},</if>
            <if test="odueMob24 != null">odue_mob24 = #{odueMob24},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDmAqaVintageAnalysisById" parameterType="Long">
        delete from dm_aqa_vintage_analysis where id = #{id}
    </delete>

    <delete id="deleteDmAqaVintageAnalysisByIds" parameterType="String">
        delete from dm_aqa_vintage_analysis where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDProjectParameter" parameterType="DmAqaVintageAnalysisVo" resultType="DProjectParameter">
        select
        dpp.id,custNo.unit_id,custNo.unit_type,dpp.product_no
        ,partnerNo.unit_id,partnerNo.unit_type
        ,fundNo.unit_id,fundNo.unit_type
        from oa_project_deploy opd
        LEFT JOIN project_company_relevance custNo ON opd.id = custNo.project_id and custNo.unit_type = '0'
        LEFT JOIN project_company_relevance partnerNo ON opd.id = partnerNo.project_id and partnerNo.unit_type = '1'
        LEFT JOIN project_company_relevance fundNo ON opd.id = fundNo.project_id and fundNo.unit_type = '2'
        left join d_project_parameter dpp on opd.id = dpp.project_id
        <where>
            dpp.product_no is not null
            <if test="auProjectIds != null and auProjectIds.size() > 0">
                and opd.id in
                <foreach item="auProjectId" collection="auProjectIds" open="(" close=")" separator=",">
                    #{auProjectId}
                </foreach>
            </if>
            <if test="systemNos != null and systemNos.size() > 0">
                and dpp.system_no in
                <foreach item="systemNo" collection="systemNos" open="(" close=")" separator=",">
                    #{systemNo}
                </foreach>
            </if>
            <if test="custNos != null and custNos.size() > 0">
                and custNo.unit_id in
                <foreach item="custNo" collection="custNos" open="(" close=")" separator=",">
                    #{custNo}
                </foreach>
            </if>
            <if test="partnerNos != null and partnerNos.size() > 0">
                and partnerNo.unit_id in
                <foreach item="partnerNo" collection="partnerNos" open="(" close=")" separator=",">
                    #{partnerNo}
                </foreach>
            </if>
            <if test="fundNos != null and fundNos.size() > 0">
                and fundNo.unit_id in
                <foreach item="fundNo" collection="fundNos" open="(" close=")" separator=",">
                    #{fundNo}
                </foreach>
            </if>
        </where>
        group by dpp.product_no
    </select>
</mapper>
