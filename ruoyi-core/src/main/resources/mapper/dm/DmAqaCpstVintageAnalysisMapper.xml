<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.dm.mapper.DmAqaCpstVintageAnalysisMapper">

    <resultMap type="DmAqaCpstVintageAnalysisVo" id="DmAqaCpstVintageAnalysisResult">
        <result property="id"    column="id"    />
        <result property="productNo"    column="product_no"    />
        <result property="productName"    column="product_name"    />
        <result property="loanMonth"    column="loan_month"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="cusLoanBalAmt"    column="cus_loan_bal_amt"    />
        <result property="cfundLoanBalAmt"    column="cfund_loan_bal_amt"    />
        <result property="cusLoanBalAmtPay"    column="cus_loan_bal_amt_pay"    />
        <result property="cfundLoanBalAmtPay"    column="cfund_loan_bal_amt_pay"    />
        <result property="cpstBalAmtMob1"    column="cpst_bal_amt_mob1"    />
        <result property="cpstBalAmtMob2"    column="cpst_bal_amt_mob2"    />
        <result property="cpstBalAmtMob3"    column="cpst_bal_amt_mob3"    />
        <result property="cpstBalAmtMob4"    column="cpst_bal_amt_mob4"    />
        <result property="cpstBalAmtMob5"    column="cpst_bal_amt_mob5"    />
        <result property="cpstBalAmtMob6"    column="cpst_bal_amt_mob6"    />
        <result property="cpstBalAmtMob7"    column="cpst_bal_amt_mob7"    />
        <result property="cpstBalAmtMob8"    column="cpst_bal_amt_mob8"    />
        <result property="cpstBalAmtMob9"    column="cpst_bal_amt_mob9"    />
        <result property="cpstBalAmtMob10"    column="cpst_bal_amt_mob10"    />
        <result property="cpstBalAmtMob11"    column="cpst_bal_amt_mob11"    />
        <result property="cpstBalAmtMob12"    column="cpst_bal_amt_mob12"    />
        <result property="cpstAmtMob1"    column="cpst_amt_mob1"    />
        <result property="cpstAmtMob2"    column="cpst_amt_mob2"    />
        <result property="cpstAmtMob3"    column="cpst_amt_mob3"    />
        <result property="cpstAmtMob4"    column="cpst_amt_mob4"    />
        <result property="cpstAmtMob5"    column="cpst_amt_mob5"    />
        <result property="cpstAmtMob6"    column="cpst_amt_mob6"    />
        <result property="cpstAmtMob7"    column="cpst_amt_mob7"    />
        <result property="cpstAmtMob8"    column="cpst_amt_mob8"    />
        <result property="cpstAmtMob9"    column="cpst_amt_mob9"    />
        <result property="cpstAmtMob10"    column="cpst_amt_mob10"    />
        <result property="cpstAmtMob11"    column="cpst_amt_mob11"    />
        <result property="cpstAmtMob12"    column="cpst_amt_mob12"    />

        <result property="cpstBalRateMob1"     column="cpst_bal_rate_mob1"    />
        <result property="cpstBalRateMob2"     column="cpst_bal_rate_mob2"    />
        <result property="cpstBalRateMob3"     column="cpst_bal_rate_mob3"    />
        <result property="cpstBalRateMob4"     column="cpst_bal_rate_mob4"    />
        <result property="cpstBalRateMob5"     column="cpst_bal_rate_mob5"    />
        <result property="cpstBalRateMob6"     column="cpst_bal_rate_mob6"    />
        <result property="cpstBalRateMob7"     column="cpst_bal_rate_mob7"    />
        <result property="cpstBalRateMob8"     column="cpst_bal_rate_mob8"    />
        <result property="cpstBalRateMob9"     column="cpst_bal_rate_mob9"    />
        <result property="cpstBalRateMob10"    column="cpst_bal_rate_mob10"    />
        <result property="cpstBalRateMob11"    column="cpst_bal_rate_mob11"    />
        <result property="cpstBalRateMob12"    column="cpst_bal_rate_mob12"    />

        <result property="cpstRateMob1"     column="cpst_rate_mob1"    />
        <result property="cpstRateMob2"     column="cpst_rate_mob2"    />
        <result property="cpstRateMob3"     column="cpst_rate_mob3"    />
        <result property="cpstRateMob4"     column="cpst_rate_mob4"    />
        <result property="cpstRateMob5"     column="cpst_rate_mob5"    />
        <result property="cpstRateMob6"     column="cpst_rate_mob6"    />
        <result property="cpstRateMob7"     column="cpst_rate_mob7"    />
        <result property="cpstRateMob8"     column="cpst_rate_mob8"    />
        <result property="cpstRateMob9"     column="cpst_rate_mob9"    />
        <result property="cpstRateMob10"    column="cpst_rate_mob10"    />
        <result property="cpstRateMob11"    column="cpst_rate_mob11"    />
        <result property="cpstRateMob12"    column="cpst_rate_mob12"    />
    </resultMap>

    <sql id="selectDmAqaCpstVintageAnalysisVo">
        select id, product_no, loan_month, loan_amt, cus_loan_bal_amt, cfund_loan_bal_amt, cus_loan_bal_amt_pay, cfund_loan_bal_amt_pay, cpst_bal_amt_mob1, cpst_bal_amt_mob2, cpst_bal_amt_mob3, cpst_bal_amt_mob4, cpst_bal_amt_mob5, cpst_bal_amt_mob6, cpst_bal_amt_mob7, cpst_bal_amt_mob8, cpst_bal_amt_mob9, cpst_bal_amt_mob10, cpst_bal_amt_mob11, cpst_bal_amt_mob12, cpst_amt_mob1, cpst_amt_mob2, cpst_amt_mob3, cpst_amt_mob4, cpst_amt_mob5, cpst_amt_mob6, cpst_amt_mob7, cpst_amt_mob8, cpst_amt_mob9, cpst_amt_mob10, cpst_amt_mob11, cpst_amt_mob12 from dm_aqa_cpst_vintage_analysis
    </sql>

    <select id="selectDmAqaCpstVintageAnalysisList" parameterType="DmAqaCpstVintageAnalysisVo" resultMap="DmAqaCpstVintageAnalysisResult">
        select dacva.id, dacva.product_no,dpp.product_name ,loan_month, loan_amt, cus_loan_bal_amt, cfund_loan_bal_amt, cus_loan_bal_amt_pay, cfund_loan_bal_amt_pay,

               cpst_bal_amt_mob1,cpst_bal_amt_mob2, cpst_bal_amt_mob3, cpst_bal_amt_mob4, cpst_bal_amt_mob5, cpst_bal_amt_mob6,
               cpst_bal_amt_mob7, cpst_bal_amt_mob8, cpst_bal_amt_mob9,cpst_bal_amt_mob10, cpst_bal_amt_mob11, cpst_bal_amt_mob12,

               cpst_amt_mob1, cpst_amt_mob2, cpst_amt_mob3, cpst_amt_mob4, cpst_amt_mob5, cpst_amt_mob6,
               cpst_amt_mob7, cpst_amt_mob8, cpst_amt_mob9, cpst_amt_mob10, cpst_amt_mob11, cpst_amt_mob12,

               ROUND((cpst_bal_amt_mob1 / loan_amt) * 100, 2) AS  cpst_bal_rate_mob1,
               ROUND((cpst_bal_amt_mob2 / loan_amt) * 100, 2) AS  cpst_bal_rate_mob2,
               ROUND((cpst_bal_amt_mob3 / loan_amt) * 100, 2) AS  cpst_bal_rate_mob3,
               ROUND((cpst_bal_amt_mob4 / loan_amt) * 100, 2) AS  cpst_bal_rate_mob4,
               ROUND((cpst_bal_amt_mob5 / loan_amt) * 100, 2) AS  cpst_bal_rate_mob5,
               ROUND((cpst_bal_amt_mob6 / loan_amt) * 100, 2) AS  cpst_bal_rate_mob6,
               ROUND((cpst_bal_amt_mob7 / loan_amt) * 100, 2) AS  cpst_bal_rate_mob7,
               ROUND((cpst_bal_amt_mob8 / loan_amt) * 100, 2) AS  cpst_bal_rate_mob8,
               ROUND((cpst_bal_amt_mob9 / loan_amt) * 100, 2) AS  cpst_bal_rate_mob9,
               ROUND((cpst_bal_amt_mob10 / loan_amt) * 100, 2) AS cpst_bal_rate_mob10,
               ROUND((cpst_bal_amt_mob11 / loan_amt) * 100, 2) AS cpst_bal_rate_mob11,
               ROUND((cpst_bal_amt_mob12 / loan_amt) * 100, 2) AS cpst_bal_rate_mob12,

               ROUND((cpst_amt_mob1 / loan_amt) * 100, 2) AS  cpst_rate_mob1,
               ROUND((cpst_amt_mob2 / loan_amt) * 100, 2) AS  cpst_rate_mob2,
               ROUND((cpst_amt_mob3 / loan_amt) * 100, 2) AS  cpst_rate_mob3,
               ROUND((cpst_amt_mob4 / loan_amt) * 100, 2) AS  cpst_rate_mob4,
               ROUND((cpst_amt_mob5 / loan_amt) * 100, 2) AS  cpst_rate_mob5,
               ROUND((cpst_amt_mob6 / loan_amt) * 100, 2) AS  cpst_rate_mob6,
               ROUND((cpst_amt_mob7 / loan_amt) * 100, 2) AS  cpst_rate_mob7,
               ROUND((cpst_amt_mob8 / loan_amt) * 100, 2) AS  cpst_rate_mob8,
               ROUND((cpst_amt_mob9 / loan_amt) * 100, 2) AS  cpst_rate_mob9,
               ROUND((cpst_amt_mob10 / loan_amt) * 100, 2) AS cpst_rate_mob10,
               ROUND((cpst_amt_mob11 / loan_amt) * 100, 2) AS cpst_rate_mob11,
               ROUND((cpst_amt_mob12 / loan_amt) * 100, 2) AS cpst_rate_mob12
        from dm_aqa_cpst_vintage_analysis dacva
        left join d_project_parameter dpp on dacva.product_no = dpp.product_no
        <where>
            <if test="cusLoanBalAmt != null "> and cus_loan_bal_amt = #{cusLoanBalAmt}</if>
            <if test="cfundLoanBalAmt != null "> and cfund_loan_bal_amt = #{cfundLoanBalAmt}</if>
            <if test="cusLoanBalAmtPay != null "> and cus_loan_bal_amt_pay = #{cusLoanBalAmtPay}</if>
            <if test="cfundLoanBalAmtPay != null "> and cfund_loan_bal_amt_pay = #{cfundLoanBalAmtPay}</if>
            <if test="loanMonth != null  and loanMonth != ''"> and loan_month = #{loanMonth}</if>
            <if test="loanAmt != null "> and loan_amt = #{loanAmt}</if>
            <if test="productNos != null and productNos.size() > 0">
                and dacva.product_no in
                <foreach item="productNo" collection="productNos" open="(" close=")" separator=",">
                    #{productNo}
                </foreach>
            </if>
        </where>
        ORDER BY
        dacva.product_no desc
        <if test="sortMap != null and sortMap.size() > 0">
            <foreach collection="sortMap.entrySet()" item="value" index="key" >
                ,${key}  ${value}
            </foreach>
        </if>
    </select>

    <select id="selectDmAqaCpstVintageAnalysisById" parameterType="Long" resultMap="DmAqaCpstVintageAnalysisResult">
        <include refid="selectDmAqaCpstVintageAnalysisVo"/>
        where id = #{id}
    </select>

    <insert id="insertDmAqaCpstVintageAnalysis" parameterType="DmAqaCpstVintageAnalysis" useGeneratedKeys="true" keyProperty="id">
        insert into dm_aqa_cpst_vintage_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productNo != null">product_no,</if>
            <if test="loanMonth != null">loan_month,</if>
            <if test="loanAmt != null">loan_amt,</if>
            <if test="cusLoanBalAmt != null">cus_loan_bal_amt,</if>
            <if test="cfundLoanBalAmt != null">cfund_loan_bal_amt,</if>
            <if test="cusLoanBalAmtPay != null">cus_loan_bal_amt_pay,</if>
            <if test="cfundLoanBalAmtPay != null">cfund_loan_bal_amt_pay,</if>
            <if test="cpstBalAmtMob1 != null">cpst_bal_amt_mob1,</if>
            <if test="cpstBalAmtMob2 != null">cpst_bal_amt_mob2,</if>
            <if test="cpstBalAmtMob3 != null">cpst_bal_amt_mob3,</if>
            <if test="cpstBalAmtMob4 != null">cpst_bal_amt_mob4,</if>
            <if test="cpstBalAmtMob5 != null">cpst_bal_amt_mob5,</if>
            <if test="cpstBalAmtMob6 != null">cpst_bal_amt_mob6,</if>
            <if test="cpstBalAmtMob7 != null">cpst_bal_amt_mob7,</if>
            <if test="cpstBalAmtMob8 != null">cpst_bal_amt_mob8,</if>
            <if test="cpstBalAmtMob9 != null">cpst_bal_amt_mob9,</if>
            <if test="cpstBalAmtMob10 != null">cpst_bal_amt_mob10,</if>
            <if test="cpstBalAmtMob11 != null">cpst_bal_amt_mob11,</if>
            <if test="cpstBalAmtMob12 != null">cpst_bal_amt_mob12,</if>
            <if test="cpstAmtMob1 != null">cpst_amt_mob1,</if>
            <if test="cpstAmtMob2 != null">cpst_amt_mob2,</if>
            <if test="cpstAmtMob3 != null">cpst_amt_mob3,</if>
            <if test="cpstAmtMob4 != null">cpst_amt_mob4,</if>
            <if test="cpstAmtMob5 != null">cpst_amt_mob5,</if>
            <if test="cpstAmtMob6 != null">cpst_amt_mob6,</if>
            <if test="cpstAmtMob7 != null">cpst_amt_mob7,</if>
            <if test="cpstAmtMob8 != null">cpst_amt_mob8,</if>
            <if test="cpstAmtMob9 != null">cpst_amt_mob9,</if>
            <if test="cpstAmtMob10 != null">cpst_amt_mob10,</if>
            <if test="cpstAmtMob11 != null">cpst_amt_mob11,</if>
            <if test="cpstAmtMob12 != null">cpst_amt_mob12,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productNo != null">#{productNo},</if>
            <if test="loanMonth != null">#{loanMonth},</if>
            <if test="loanAmt != null">#{loanAmt},</if>
            <if test="cusLoanBalAmt != null">#{cusLoanBalAmt},</if>
            <if test="cfundLoanBalAmt != null">#{cfundLoanBalAmt},</if>
            <if test="cusLoanBalAmtPay != null">#{cusLoanBalAmtPay},</if>
            <if test="cfundLoanBalAmtPay != null">#{cfundLoanBalAmtPay},</if>
            <if test="cpstBalAmtMob1 != null">#{cpstBalAmtMob1},</if>
            <if test="cpstBalAmtMob2 != null">#{cpstBalAmtMob2},</if>
            <if test="cpstBalAmtMob3 != null">#{cpstBalAmtMob3},</if>
            <if test="cpstBalAmtMob4 != null">#{cpstBalAmtMob4},</if>
            <if test="cpstBalAmtMob5 != null">#{cpstBalAmtMob5},</if>
            <if test="cpstBalAmtMob6 != null">#{cpstBalAmtMob6},</if>
            <if test="cpstBalAmtMob7 != null">#{cpstBalAmtMob7},</if>
            <if test="cpstBalAmtMob8 != null">#{cpstBalAmtMob8},</if>
            <if test="cpstBalAmtMob9 != null">#{cpstBalAmtMob9},</if>
            <if test="cpstBalAmtMob10 != null">#{cpstBalAmtMob10},</if>
            <if test="cpstBalAmtMob11 != null">#{cpstBalAmtMob11},</if>
            <if test="cpstBalAmtMob12 != null">#{cpstBalAmtMob12},</if>
            <if test="cpstAmtMob1 != null">#{cpstAmtMob1},</if>
            <if test="cpstAmtMob2 != null">#{cpstAmtMob2},</if>
            <if test="cpstAmtMob3 != null">#{cpstAmtMob3},</if>
            <if test="cpstAmtMob4 != null">#{cpstAmtMob4},</if>
            <if test="cpstAmtMob5 != null">#{cpstAmtMob5},</if>
            <if test="cpstAmtMob6 != null">#{cpstAmtMob6},</if>
            <if test="cpstAmtMob7 != null">#{cpstAmtMob7},</if>
            <if test="cpstAmtMob8 != null">#{cpstAmtMob8},</if>
            <if test="cpstAmtMob9 != null">#{cpstAmtMob9},</if>
            <if test="cpstAmtMob10 != null">#{cpstAmtMob10},</if>
            <if test="cpstAmtMob11 != null">#{cpstAmtMob11},</if>
            <if test="cpstAmtMob12 != null">#{cpstAmtMob12},</if>
        </trim>
    </insert>

    <update id="updateDmAqaCpstVintageAnalysis" parameterType="DmAqaCpstVintageAnalysis">
        update dm_aqa_cpst_vintage_analysis
        <trim prefix="SET" suffixOverrides=",">
            <if test="productNo != null">product_no = #{productNo},</if>
            <if test="loanMonth != null">loan_month = #{loanMonth},</if>
            <if test="loanAmt != null">loan_amt = #{loanAmt},</if>
            <if test="cusLoanBalAmt != null">cus_loan_bal_amt = #{cusLoanBalAmt},</if>
            <if test="cfundLoanBalAmt != null">cfund_loan_bal_amt = #{cfundLoanBalAmt},</if>
            <if test="cusLoanBalAmtPay != null">cus_loan_bal_amt_pay = #{cusLoanBalAmtPay},</if>
            <if test="cfundLoanBalAmtPay != null">cfund_loan_bal_amt_pay = #{cfundLoanBalAmtPay},</if>
            <if test="cpstBalAmtMob1 != null">cpst_bal_amt_mob1 = #{cpstBalAmtMob1},</if>
            <if test="cpstBalAmtMob2 != null">cpst_bal_amt_mob2 = #{cpstBalAmtMob2},</if>
            <if test="cpstBalAmtMob3 != null">cpst_bal_amt_mob3 = #{cpstBalAmtMob3},</if>
            <if test="cpstBalAmtMob4 != null">cpst_bal_amt_mob4 = #{cpstBalAmtMob4},</if>
            <if test="cpstBalAmtMob5 != null">cpst_bal_amt_mob5 = #{cpstBalAmtMob5},</if>
            <if test="cpstBalAmtMob6 != null">cpst_bal_amt_mob6 = #{cpstBalAmtMob6},</if>
            <if test="cpstBalAmtMob7 != null">cpst_bal_amt_mob7 = #{cpstBalAmtMob7},</if>
            <if test="cpstBalAmtMob8 != null">cpst_bal_amt_mob8 = #{cpstBalAmtMob8},</if>
            <if test="cpstBalAmtMob9 != null">cpst_bal_amt_mob9 = #{cpstBalAmtMob9},</if>
            <if test="cpstBalAmtMob10 != null">cpst_bal_amt_mob10 = #{cpstBalAmtMob10},</if>
            <if test="cpstBalAmtMob11 != null">cpst_bal_amt_mob11 = #{cpstBalAmtMob11},</if>
            <if test="cpstBalAmtMob12 != null">cpst_bal_amt_mob12 = #{cpstBalAmtMob12},</if>
            <if test="cpstAmtMob1 != null">cpst_amt_mob1 = #{cpstAmtMob1},</if>
            <if test="cpstAmtMob2 != null">cpst_amt_mob2 = #{cpstAmtMob2},</if>
            <if test="cpstAmtMob3 != null">cpst_amt_mob3 = #{cpstAmtMob3},</if>
            <if test="cpstAmtMob4 != null">cpst_amt_mob4 = #{cpstAmtMob4},</if>
            <if test="cpstAmtMob5 != null">cpst_amt_mob5 = #{cpstAmtMob5},</if>
            <if test="cpstAmtMob6 != null">cpst_amt_mob6 = #{cpstAmtMob6},</if>
            <if test="cpstAmtMob7 != null">cpst_amt_mob7 = #{cpstAmtMob7},</if>
            <if test="cpstAmtMob8 != null">cpst_amt_mob8 = #{cpstAmtMob8},</if>
            <if test="cpstAmtMob9 != null">cpst_amt_mob9 = #{cpstAmtMob9},</if>
            <if test="cpstAmtMob10 != null">cpst_amt_mob10 = #{cpstAmtMob10},</if>
            <if test="cpstAmtMob11 != null">cpst_amt_mob11 = #{cpstAmtMob11},</if>
            <if test="cpstAmtMob12 != null">cpst_amt_mob12 = #{cpstAmtMob12},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDmAqaCpstVintageAnalysisById" parameterType="Long">
        delete from dm_aqa_cpst_vintage_analysis where id = #{id}
    </delete>

    <delete id="deleteDmAqaCpstVintageAnalysisByIds" parameterType="String">
        delete from dm_aqa_cpst_vintage_analysis where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDProjectParameter" parameterType="DmAqaCpstVintageAnalysisVo" resultType="DProjectParameter">
        select
        dpp.id,custNo.unit_id,custNo.unit_type,dpp.product_no
        ,partnerNo.unit_id,partnerNo.unit_type
        ,fundNo.unit_id,fundNo.unit_type
        from oa_project_deploy opd
        LEFT JOIN project_company_relevance custNo ON opd.id = custNo.project_id and custNo.unit_type = '0'
        LEFT JOIN project_company_relevance partnerNo ON opd.id = partnerNo.project_id and partnerNo.unit_type = '1'
        LEFT JOIN project_company_relevance fundNo ON opd.id = fundNo.project_id and fundNo.unit_type = '2'
        left join d_project_parameter dpp on opd.id = dpp.project_id
        <where>
            dpp.product_no is not null
            <if test="auProjectIds != null and auProjectIds.size() > 0">
                and opd.id in
                <foreach item="auProjectId" collection="auProjectIds" open="(" close=")" separator=",">
                    #{auProjectId}
                </foreach>
            </if>
            <if test="systemNos != null and systemNos.size() > 0">
                and dpp.system_no in
                <foreach item="systemNo" collection="systemNos" open="(" close=")" separator=",">
                    #{systemNo}
                </foreach>
            </if>
            <if test="custNos != null and custNos.size() > 0">
                and custNo.unit_id in
                <foreach item="custNo" collection="custNos" open="(" close=")" separator=",">
                    #{custNo}
                </foreach>
            </if>
            <if test="partnerNos != null and partnerNos.size() > 0">
                and partnerNo.unit_id in
                <foreach item="partnerNo" collection="partnerNos" open="(" close=")" separator=",">
                    #{partnerNo}
                </foreach>
            </if>
            <if test="fundNos != null and fundNos.size() > 0">
                and fundNo.unit_id in
                <foreach item="fundNo" collection="fundNos" open="(" close=")" separator=",">
                    #{fundNo}
                </foreach>
            </if>
        </where>
        group by dpp.product_no
    </select>
</mapper>
