<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.offSupply.mapper.OffSupplyMainMapper">
    
    <resultMap type="OffSupplyMain" id="OffSupplyMainResult">
        <result property="id"    column="id"    />
        <result property="itemName"    column="item_name"    />
        <result property="sysCode"    column="sys_code"    />
        <result property="itemType"    column="item_type"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="companyShortName"    column="company_short_name"    />
        <result property="specification"    column="specification"    />
        <result property="amount"    column="amount"    />
        <result property="measureUnit"    column="measure_unit"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="isUseNotify"    column="is_use_notify"    />
        <result property="notifyType"    column="notify_type"    />
        <result property="beforeNum"    column="before_num"    />
        <result property="negativeInventory"    column="negative_inventory"    />
        <result property="status"    column="status"    />
        <result property="itemWarning"    column="item_warning"    />
        <result property="version"    column="version"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--<resultMap type="org.ruoyi.core.offSupply.domain.OffSupplyHistory" id="OffSupplyHistoryResult">
        <result property="id"    column="id"    />
        <result property="oldSupplyName"    column="old_supply_name"    />
        <result property="newSupplyName"    column="new_supply_name"    />
        <result property="oldAmount"    column="old_amount"    />
        <result property="newAmount"    column="new_amount"    />
        <result property="oldJson"    column="old_json"    />
        <result property="newJson"    column="new_json"    />
        <result property="updateRemark"    column="update_remark"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>-->

    <sql id="selectOffSupplyMainVo">
        select id, item_name, sys_code, item_type, category_id, measure_unit, specification, amount, expiration_date, is_use_notify, notify_type, before_num, negative_inventory, status,
               item_warning, version, del_flag, remark, create_by, create_time, update_by, update_time from off_supply_main
    </sql>

    <select id="selectOffSupplyMainList" parameterType="OffSupplyMain" resultMap="OffSupplyMainResult">
        SELECT
        osm.id,
        osm.item_name,
        osm.sys_code,
        osm.item_type,
        osm.category_id,
        ocm.category_name,
        sc.company_short_name,
        osm.specification,
        osm.amount,
        osm.measure_unit,
        osm.expiration_date,
        osm.is_use_notify,
        osm.notify_type,
        osm.before_num,
        osm.negative_inventory,
        osm.status,
        osm.item_warning,
        osm.version,
        osm.del_flag,
        osm.remark,
        osm.create_by,
        osm.create_time,
        osm.update_by,
        osm.update_time
        FROM
        off_supply_main osm
        INNER JOIN (
        SELECT sys_code, MAX(version) AS max_version
        FROM off_supply_main
        GROUP BY sys_code
        ) max_version_osm ON osm.sys_code = max_version_osm.sys_code AND osm.version = max_version_osm.max_version
        LEFT JOIN
        off_category_main ocm ON osm.category_id = ocm.id
        LEFT JOIN
        sys_company sc ON ocm.company_id = sc.id
        <where>
            <if test="itemName != null  and itemName != ''"> and osm.item_name like concat('%', #{itemName}, '%')</if>
            <if test="companyId != null"> and ocm.company_id = #{companyId}</if>
            <if test="categoryId != null "> and osm.category_id = #{categoryId}</if>
            <if test="status != null  and status != ''"> and osm.status = #{status}</if>
            <if test="itemType != null and itemType != ''"> and osm.item_type = #{itemType}</if>
            <if test="authCompanyIds != null and authCompanyIds.size > 0">
            and ocm.company_id in
                <foreach collection="authCompanyIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryFlag != null and queryFlag != '' and queryFlag.equals('1'.toString())">
                and osm.status = '0'
            </if>
            and osm.del_flag = '0' and ocm.del_flag = '0' and sc.is_delete = '0'
        </where>
        /*keep orderby*/
        order by create_time desc
    </select>
    
    <select id="selectOffSupplyMainById" parameterType="Long" resultMap="OffSupplyMainResult">
        select osm.id, osm.item_name, osm.sys_code, osm.item_type, osm.category_id, ocm.category_name, sc.company_short_name, osm.specification, osm.amount, osm.measure_unit, osm.expiration_date, osm.is_use_notify, osm.notify_type, osm.before_num,
               osm.negative_inventory, osm.status, osm.item_warning, osm.del_flag, osm.remark, osm.create_by, osm.create_time, osm.update_by, osm.update_time, osm.version
        from off_supply_main osm
                 left join off_category_main ocm on osm.category_id = ocm.id
                 left join sys_company sc on ocm.company_id = sc.id
        where osm.id = #{id}
    </select>
        
    <insert id="insertOffSupplyMain" parameterType="OffSupplyMain" useGeneratedKeys="true" keyProperty="id">
        insert into off_supply_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="sysCode != null and sysCode != ''">sys_code,</if>
            <if test="itemType != null and itemType != ''">item_type,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="specification != null and specification != ''">specification,</if>
            <if test="amount != null">amount,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="measureUnit != null">measure_unit,</if>
            <if test="isUseNotify != null and isUseNotify != ''">is_use_notify,</if>
            <if test="notifyType != null and notifyType != ''">notify_type,</if>
            <if test="beforeNum != null">before_num,</if>
            <if test="negativeInventory != null and negativeInventory != ''">negative_inventory,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="itemWarning != null">item_warning,</if>
            <if test="version != null and version != ''">version,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="sysCode != null and sysCode != ''">#{sysCode},</if>
            <if test="itemType != null and itemType != ''">#{itemType},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="specification != null and specification != ''">#{specification},</if>
            <if test="amount != null">#{amount},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="measureUnit != null and measureUnit != ''">#{measureUnit},</if>
            <if test="isUseNotify != null and isUseNotify != ''">#{isUseNotify},</if>
            <if test="notifyType != null and notifyType != ''">#{notifyType},</if>
            <if test="beforeNum != null">#{beforeNum},</if>
            <if test="negativeInventory != null and negativeInventory != ''">#{negativeInventory},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="itemWarning != null">#{itemWarning},</if>
            <if test="version != null and version != ''">#{version},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOffSupplyMain" parameterType="OffSupplyMain">
        update off_supply_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="sysCode != null and sysCode != ''">sys_code = #{sysCode},</if>
            <if test="itemType != null and itemType != ''">item_type = #{itemType},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="specification != null and specification != ''">specification = #{specification},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="measureUnit != null and measureUnit != ''">measure_unit = #{measureUnit},</if>
            <if test="isUseNotify != null and isUseNotify != ''">is_use_notify = #{isUseNotify},</if>
            <if test="notifyType != null and notifyType != ''">notify_type = #{notifyType},</if>
            <if test="beforeNum != null">before_num = #{beforeNum},</if>
            <if test="negativeInventory != null and negativeInventory != ''">negative_inventory = #{negativeInventory},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="itemWarning != null">item_warning = #{itemWarning},</if>
            <if test="version != null and version != ''">version = #{version},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOffSupplyMainBySysCode" parameterType="String">
        update off_supply_main set del_flag = '1' where sys_code = #{sysCode}
    </delete>

    <delete id="deleteOffSupplyMainByIds" parameterType="String">
        delete from off_supply_main where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--修改记录-->
    <resultMap type="OffSupplyHistory" id="OffSupplyHistoryResult">
        <result property="id"    column="id"    />
        <result property="supplyId"    column="supply_id"    />
        <result property="oldSupplyName"    column="old_supply_name"    />
        <result property="newSupplyName"    column="new_supply_name"    />
        <result property="oldAmount"    column="old_amount"    />
        <result property="newAmount"    column="new_amount"    />
        <result property="oldJson"    column="old_json"    />
        <result property="newJson"    column="new_json"    />
        <result property="updateRemark"    column="update_remark"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateByName"    column="nick_name"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOffSupplyHistoryVo">
        select id, supply_id, supply_sys_code, old_supply_name, new_supply_name, old_amount, new_amount, old_json, new_json, update_remark, update_by, update_time from off_supply_history
    </sql>

    <select id="selectOffSupplyHistoryList" parameterType="OffSupplyHistory" resultType="org.ruoyi.core.offSupply.domain.OffSupplyHistory">
        <include refid="selectOffSupplyHistoryVo"/>
        <where>
            <if test="supplyId != null "> and supply_id = #{supplyId}</if>
            <if test="oldSupplyName != null  and oldSupplyName != ''"> and old_supply_name like concat('%', #{oldSupplyName}, '%')</if>
            <if test="newSupplyName != null  and newSupplyName != ''"> and new_supply_name like concat('%', #{newSupplyName}, '%')</if>
            <if test="oldAmount != null "> and old_amount = #{oldAmount}</if>
            <if test="newAmount != null "> and new_amount = #{newAmount}</if>
            <if test="oldJson != null  and oldJson != ''"> and old_json = #{oldJson}</if>
            <if test="newJson != null  and newJson != ''"> and new_json = #{newJson}</if>
            <if test="updateRemark != null  and updateRemark != ''"> and update_remark = #{updateRemark}</if>
        </where>
    </select>

    <select id="selectOffSupplyHistoryById" parameterType="Long" resultType="org.ruoyi.core.offSupply.domain.OffSupplyHistory">
        <include refid="selectOffSupplyHistoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertOffSupplyHistory" parameterType="OffSupplyHistory" useGeneratedKeys="true" keyProperty="id">
        insert into off_supply_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplyId != null">supply_id,</if>
            <if test="supplySysCode != null">supply_sys_code,</if>
            <if test="oldSupplyName != null and oldSupplyName != ''">old_supply_name,</if>
            <if test="newSupplyName != null and newSupplyName != ''">new_supply_name,</if>
            <if test="oldAmount != null">old_amount,</if>
            <if test="newAmount != null">new_amount,</if>
            <if test="oldJson != null">old_json,</if>
            <if test="newJson != null">new_json,</if>
            <if test="updateRemark != null and updateRemark != ''">update_remark,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplyId != null">#{supplyId},</if>
            <if test="supplySysCode != null">#{supplySysCode},</if>
            <if test="oldSupplyName != null and oldSupplyName != ''">#{oldSupplyName},</if>
            <if test="newSupplyName != null and newSupplyName != ''">#{newSupplyName},</if>
            <if test="oldAmount != null">#{oldAmount},</if>
            <if test="newAmount != null">#{newAmount},</if>
            <if test="oldJson != null">#{oldJson},</if>
            <if test="newJson != null">#{newJson},</if>
            <if test="updateRemark != null and updateRemark != ''">#{updateRemark},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateOffSupplyHistory" parameterType="OffSupplyHistory">
        update off_supply_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplyId != null">supply_id = #{supplyId},</if>
            <if test="supplySysCode != null and supplySysCode != ''">supply_sys_code = #{supplySysCode},</if>
            <if test="oldSupplyName != null and oldSupplyName != ''">old_supply_name = #{oldSupplyName},</if>
            <if test="newSupplyName != null and newSupplyName != ''">new_supply_name = #{newSupplyName},</if>
            <if test="oldAmount != null">old_amount = #{oldAmount},</if>
            <if test="newAmount != null">new_amount = #{newAmount},</if>
            <if test="oldJson != null">old_json = #{oldJson},</if>
            <if test="newJson != null">new_json = #{newJson},</if>
            <if test="updateRemark != null">update_remark = #{updateRemark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectOffSupplyMainByCategoryId" resultMap="OffSupplyMainResult">
        <include refid="selectOffSupplyMainVo"/>
        where category_id = #{id}
    </select>

    <select id="selectOffSupplyMainByCategoryIds" resultType="org.ruoyi.core.offSupply.domain.OffSupplyMain">
        <include refid="selectOffSupplyMainVo"/>
        where category_id in
        <foreach item="categoryId" collection="categoryIds" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </select>

    <update id="updateOffSupplyMainCategoryIdByCategoryIds">
        update off_supply_main set category_id = #{categoryId} where id in
        <foreach item="supplyId" collection="supplyIds" open="(" separator="," close=")">
            #{supplyId}
        </foreach>
    </update>

    <select id="selectCountNumber" resultType="java.lang.Integer">
        select count(*) from off_supply_main
    </select>

    <update id="updateSupplySettle">
        update off_supply_main set category_id = #{categoryId} where id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectOffSupplyByExpireDateAndUseNotifyList" resultType="org.ruoyi.core.offSupply.domain.OffSupplyMain">
        select *
        FROM off_supply_main osm
                 INNER JOIN (SELECT sys_code, MAX(version) AS max_version
                             FROM off_supply_main
                             GROUP BY sys_code) max_version_osm
                            ON osm.sys_code = max_version_osm.sys_code AND osm.version = max_version_osm.max_version
        where osm.expiration_date is not null and osm.is_use_notify = '0' and osm.del_flag = '0' and osm.status = '0'
    </select>

    <select id="selectOffSupplyRecord" resultMap="OffSupplyHistoryResult">
        select osh.*, su.nick_name from off_supply_history osh
                 left join sys_user su on osh.update_by = su.user_name
                 where osh.supply_sys_code = #{supplySysCode}
    </select>

    <select id="selectOffSupplyListByType" resultType="org.ruoyi.core.offSupply.domain.OffSupplyMain">
        SELECT *
        FROM off_supply_main osm
        INNER JOIN off_category_main ocm ON osm.category_id = ocm.id
        WHERE (osm.sys_code, osm.version) IN (
        SELECT sys_code, MAX(version)
        FROM off_supply_main
        WHERE del_flag = '0' AND status = '0'
        GROUP BY sys_code
        )
        AND osm.del_flag = '0'
        AND osm.status = '0'
        AND ocm.status = '0'
        AND ocm.del_flag = '0'
        <if test="authCompanyIds != null and authCompanyIds.size() > 0">
            AND ocm.company_id IN
            <foreach collection="authCompanyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="itemType != null and itemType != ''">
            AND osm.item_type = #{itemType}
        </if>
        <if test="categoryId != null and categoryId != ''">
            AND osm.category_id = #{categoryId}
        </if>
    </select>

    <select id="selectOffReceiveDetailByFlowId" resultType="org.ruoyi.core.offSupply.domain.OffReceiveDetail">
        select * from off_receive_detail where process_id = #{flowId}
    </select>

    <select id="selectOffSupplyMainBySysCode" resultMap="OffSupplyMainResult">
        <include refid="selectOffSupplyMainVo"/>
        where sys_code = #{sysCode}
        ORDER BY version DESC
        LIMIT 1
    </select>

    <select id="selectSupplyHistoryInfoById" resultMap="OffSupplyHistoryResult">
        select * from off_supply_history where id = #{historyId}
    </select>

    <select id="selectOffSupplyMainByIds" resultType="org.ruoyi.core.offSupply.domain.OffSupplyMain">
        select * from off_supply_main where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>