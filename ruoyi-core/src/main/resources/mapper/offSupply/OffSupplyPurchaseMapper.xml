<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.offSupply.mapper.OffSupplyPurchaseMapper">
    
    <resultMap type="OffSupplyPurchase" id="OffSupplyPurchaseResult">
        <result property="id"    column="id"    />
        <result property="supplyId"    column="supply_id"    />
        <result property="itemName"    column="item_name"    />
        <result property="companyId"    column="companyId"    />
        <result property="companyShortName"    column="company_short_name"    />
        <result property="amount"    column="amount"    />
        <result property="ordersCode"    column="orders_code"    />
        <result property="price"    column="price"    />
        <result property="totalPrice"    column="total_price"    />
        <result property="discountRate"    column="discount_rate"    />
        <result property="totalPayable"    column="total_payable"    />
        <result property="purchaseDate"    column="purchase_date"    />
        <result property="purchaseDateStr"    column="purchaseDateStr"    />
        <result property="status"    column="status"    />
        <result property="addUuid"    column="add_uuid"    />
        <result property="processId"    column="process_id"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createByName"    column="nick_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOffSupplyPurchaseVo">
        select id, supply_id, amount, orders_code, price, total_price, discount_rate, total_payable, purchase_date, status, company_id, add_uuid, process_id, remark, del_flag, create_by, create_time, update_by, update_time from off_supply_purchase
    </sql>

    <select id="selectOffSupplyPurchaseList" parameterType="OffSupplyPurchase" resultMap="OffSupplyPurchaseResult">
        select ss.* from (
        select osp.id, osp.supply_id, osp.amount, osp.orders_code, osp.price, osp.total_price, osp.discount_rate,
        osp.del_flag, osp.total_payable, osp.purchase_date, osp.status,
        DATE_FORMAT(osp.purchase_date, '%Y年%m月%d日') as purchaseDateStr,
        case
        when osp.status = '0' then '未提交'
        when osp.status = '1' then '审核中'
        when osp.status = '2' then '审核不通过'
        when osp.status = '3' then '审核通过'
        end as statusLabel,
        osp.add_uuid, osp.process_id, osp.remark, osp.create_by,
        osp.create_time, osp.update_by, osp.update_time, osm.item_name, sc.id AS companyId, sc.company_short_name, su.nick_name
        from off_supply_purchase osp
        left join off_supply_main osm on osp.supply_id = osm.id
        left join off_category_main ocm on osm.category_id = ocm.id
        left join sys_user su on osp.create_by = su.user_name
        left join sys_company sc on ocm.company_id = sc.id
        where 1=1
        <!-- 查询当前登录用户的数据 -->
        <if test="loginUserName != null and loginUserName != ''">
            AND (osp.status in ('0','2') and osp.create_by = #{loginUserName}) or osp.create_by = #{loginUserName}
        </if>
        ) ss
        <where>
            <if test="companyId != null ">and ss.companyId = #{companyId}</if>
            <if test="createBeginTime != null ">and date_format(ss.create_time,'%Y-%m-%d') &gt;=
                DATE_FORMAT(#{createBeginTime},'%Y-%m-%d')
            </if>
            <if test="createEndTime != null ">and date_format(ss.create_time,'%Y-%m-%d') &lt;=
                DATE_FORMAT(#{createEndTime},'%Y-%m-%d')
            </if>
            <if test="status != null  and status != ''">and ss.status = #{status}</if>
            <if test="itemName != null  and itemName != ''">and ss.item_name like concat('%', #{itemName}, '%')</if>
            and ss.del_flag = '0'
        </where>
        order by ss.create_time desc, ss.status asc
    </select>
    
    <select id="selectOffSupplyPurchaseById" parameterType="Long" resultMap="OffSupplyPurchaseResult">
        select osp.id, osp.supply_id, osm.item_name, osp.amount, osp.orders_code, osp.price, osp.total_price, osp.discount_rate, osp.total_payable, osp.purchase_date, osp.status, osp.add_uuid, osp.process_id, osp.remark, osp.del_flag,
               osp.create_by, osp.create_time, osp.update_by, osp.update_time, sc.company_short_name, su.nick_name, osp.company_id,
            case
        when osp.status = '0' then '未提交'
        when osp.status = '1' then '审核中'
        when osp.status = '2' then '审核不通过'
        when osp.status = '3' then '审核通过'
        end as statusLabel
        from off_supply_purchase osp
        left join off_supply_main osm on osp.supply_id = osm.id
        left join sys_company sc on osp.company_id = sc.id
        left join sys_user su on osp.create_by = su.user_name
        where osp.id = #{id}
    </select>


    <insert id="insertOffSupplyPurchase" parameterType="OffSupplyPurchase" useGeneratedKeys="true" keyProperty="id">
        insert into off_supply_purchase
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplyId != null">supply_id,</if>
            <if test="amount != null">amount,</if>
            <if test="ordersCode != null and ordersCode != ''">orders_code,</if>
            <if test="price != null">price,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="discountRate != null and discountRate != ''">discount_rate,</if>
            <if test="totalPayable != null">total_payable,</if>
            <if test="purchaseDate != null">purchase_date,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="companyId != null">company_id,</if>
            <if test="addUuid != null and addUuid != ''">add_uuid,</if>
            <if test="processId != null and processId != ''">process_id,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplyId != null">#{supplyId},</if>
            <if test="amount != null">#{amount},</if>
            <if test="ordersCode != null and ordersCode != ''">#{ordersCode},</if>
            <if test="price != null">#{price},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="discountRate != null and discountRate != ''">#{discountRate},</if>
            <if test="totalPayable != null">#{totalPayable},</if>
            <if test="purchaseDate != null">#{purchaseDate},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="addUuid != null and addUuid != ''">#{addUuid},</if>
            <if test="processId != null and processId != ''">#{processId},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOffSupplyPurchase" parameterType="OffSupplyPurchase">
        update off_supply_purchase
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplyId != null">supply_id = #{supplyId},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="ordersCode != null and ordersCode !=''">orders_code = #{ordersCode},</if>
            <if test="price != null">price = #{price},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="discountRate != null and discountRate !=''">discount_rate = #{discountRate},</if>
            <if test="totalPayable != null">total_payable = #{totalPayable},</if>
            <if test="purchaseDate != null">purchase_date = #{purchaseDate},</if>
            <if test="status != null and status !=''">status = #{status},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="addUuid != null and addUuid !=''">add_uuid = #{addUuid},</if>
            <if test="processId != null and processId !=''">process_id = #{processId},</if>
            <if test="remark != null and remark !=''">remark = #{remark},</if>
            <if test="createBy != null and createBy !=''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy !=''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateOffSupplyPurchaseByIds">
        update off_supply_purchase
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null and status !=''">status = #{status},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="processId != null and processId !=''">process_id = #{processId},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateOffSupplyPurchaseStatus">
        update off_supply_purchase set status = #{status} where id = #{id}
    </update>

    <delete id="deleteOffSupplyPurchaseById" parameterType="Long">
        update off_supply_purchase set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteOffSupplyPurchaseByIds" parameterType="String">
        delete from off_supply_purchase where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectOffSupplyPurchaseByProcessId" resultMap="OffSupplyPurchaseResult">
        <include refid="selectOffSupplyPurchaseVo"/>
        where process_id = #{processId}
    </select>
</mapper>