<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.OASettingUp.mapper.NoaWorkflowRemindMapper">

    <resultMap type="NoaWorkflowRemind" id="NoaWorkflowRemindResult">
        <result property="flowId"    column="flow_id"    />
        <result property="nodeId"    column="node_id"    />
        <result property="nodeId"    column="node_id"    />
        <result property="state"    column="state"    />
        <result property="remindTitle"    column="remind_title"    />
        <result property="remindText"    column="remind_text"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectNoaWorkflowRemindVo">
        select flow_id, node_id, state, remind_title, remind_text, create_time, create_by from noa_workflow_remind
    </sql>

    <select id="selectNoaWorkflowRemindList" parameterType="org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind" resultType="org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind">
        SELECT
            pfd.name flowName,
            remind.flow_id flowId,
            remind.node_id nodeId,
            remind.id id,
            remind.flow_full_id flowFullId,
            remind.state,
            us.nick_name createByName,
            remind.create_time createTime
        FROM
            noa_workflow_remind remind
                LEFT JOIN oa_process_template tem ON tem.flow_id = remind.flow_id
                LEFT JOIN proc_form_def pfd ON tem.form_id = pfd.id
                LEFT JOIN sys_user us ON remind.create_by = us.user_name
        <where>
            <if test="flowId != null and flowId != ''"> and remind.flow_id = #{flowId} </if>
            <if test="startTime != null"> and date_format(remind.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> date_format(#{startTime},'%Y-%m-%d') </if>
            <if test="endTime != null"> and date_format(remind.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> date_format(#{endTime},'%Y-%m-%d') </if>
        </where>
        order by remind.create_time desc
    </select>

    <select id="selectNoaWorkflowRemindByFlowId" parameterType="String" resultMap="NoaWorkflowRemindResult">
        <include refid="selectNoaWorkflowRemindVo"/>
        where flow_id = #{flowId}
    </select>
    <select id="selectNoaWorkflowRemindByFlowIdAndNodeId"
            resultType="org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind">
        SELECT
            pfd.name flowName,
            remind.flow_id flowId,
            remind.node_id nodeId,
            remind.id id,
            remind.flow_full_id flowFullId,
            remind.state,
            remind.remind_title remindTitle,
            remind.remind_text remindText
        FROM
            noa_workflow_remind remind
                LEFT JOIN proc_form_def pfd ON remind.flow_id = pfd.id
                LEFT JOIN sys_user us ON remind.create_by = us.user_name
        where remind.id = #{id}
    </select>

    <select id="selectWorkFlowByBusinessIdAndNodeId"
            resultType="org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind">
        SELECT
            tem.template_name flowName,
            tem.flow_full_id flowFullId,
            rem.remind_title remindTitle,
            rem.remind_text remindText,
            rem.id id,
            rem.flow_id flowId,
            rem.node_id nodeId
        FROM
            oa_process_template tem
                left join proc_form_data fd on tem.form_id = fd.form_id
                left join noa_workflow_remind rem on rem.flow_id = tem.flow_id
        WHERE
           rem.flow_full_id = #{keyId} and rem.node_id = #{nodeId}
        group by rem.id
    </select>

    <select id="selectAuthority" resultType="org.ruoyi.core.OASettingUp.domain.NoaRemindPerson">
        SELECT
            us.user_name remindPerson,
            #{id} remindId,
            '0' state
        FROM
            noa_authority noa
                LEFT JOIN sys_user us ON us.user_id = noa.authority_id
        WHERE
            noa.flow_id = #{flowId}
          AND noa.node_id = #{nodeId}
          AND noa.authority_type = 2
          AND us.del_flag = '0'
        group by us.user_id
        UNION
        SELECT
            us1.user_name remindPerson,
            #{id} remindId,
            '0' state
        FROM
            noa_authority noa1
                LEFT JOIN sys_user us1 ON us1.dept_id = noa1.authority_id
        WHERE
            noa1.flow_id = #{flowId}
          AND noa1.node_id = #{nodeId}
          AND noa1.authority_type = 0
          AND us1.del_flag = '0'
        group by us1.user_id
        UNION
        SELECT
            us2.user_name remindPerson,
            #{id} remindId,
            '0' state
        FROM
            noa_authority noa2
                LEFT JOIN sys_user_post po ON po.post_id = noa2.authority_id
                LEFT JOIN sys_user us2 ON us2.user_id = po.user_id
        WHERE
            noa2.flow_id = #{flowId}
          AND noa2.node_id = #{nodeId}
          AND noa2.authority_type = 1
          AND us2.del_flag = '0'
        group by us2.user_id
    </select>

    <select id="selectRemindByUser" resultType="org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind">
        SELECT
            flow.remind_title remindTitle,
            flow.remind_text remindText,
            rem.create_time createTime,
            rem.id remindId
        FROM
            noa_remind_person per
                LEFT JOIN noa_need_remind rem ON per.remind_id = rem.id
                LEFT JOIN noa_workflow_remind flow ON flow.flow_id = rem.flow_id
                AND flow.node_id = rem.node_id
        WHERE
            per.remind_person = #{username}
          AND flow.state = '0' AND per.state = '0'
    </select>

    <select id="selectFormulaByNotify" resultType="org.ruoyi.core.OASettingUp.domain.NoaFlow">
        select
            tem.template_name flowName,
            da.create_name createUser,
            date_format(da.create_time,'%Y-%m-%d %T') createTime,
            form.create_name auditUser,
            form.task_node_name nodeName,
            date_format(form.create_time,'%Y-%m-%d %T') auditTime
        from
            noa_need_remind rem
                LEFT JOIN oa_process_template tem ON rem.business_id = tem.flow_full_id
                LEFT JOIN proc_form_data da ON da.form_id = tem.form_id
                left join noa_remind_person per on per.remind_id = rem.id and per.remind_person = #{userName} and per.state = #{state}
                LEFT JOIN proc_workflow_formdata form ON form.business_key = per.bus_id
                AND form.step_id = rem.node_id
        where rem.id = #{topNotify.remindId}
        group by form.create_time
        order by form.create_time desc limit 1
    </select>

    <select id="selectRemindStateById" resultType="java.lang.Integer">
        select count(remind_id) from noa_remind_person where remind_id = #{remindId} AND state = '0'
    </select>

    <insert id="insertNoaWorkflowRemind" parameterType="NoaWorkflowRemind">
        insert into noa_workflow_remind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="flowId != null and flowId != ''">flow_id,</if>
            <if test="nodeId != null and nodeId != ''">node_id,</if>
            <if test="flowFullId != null and flowFullId != ''">flow_full_id,</if>
            <if test="state != null and state != ''">state,</if>
            <if test="remindTitle != null and remindTitle != ''">remind_title,</if>
            <if test="remindText != null and remindText != ''">remind_text,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="flowId != null and flowId != ''">#{flowId},</if>
            <if test="nodeId != null and nodeId != ''">#{nodeId},</if>
            <if test="flowFullId != null and flowFullId != ''">#{flowFullId},</if>
            <if test="state != null and state != ''">#{state},</if>
            <if test="remindTitle != null and remindTitle != ''">#{remindTitle},</if>
            <if test="remindText != null and remindText != ''">#{remindText},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
         </trim>
    </insert>

    <insert id="insertNeedRemind">
        insert into noa_need_remind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="remindId != null">remind_id,</if>
            <if test="flowId != null and flowId != ''">flow_id,</if>
            <if test="nodeId != null and nodeId != ''">node_id,</if>
            <if test="businessId != null and businessId != ''">business_id,</if>
            <if test="state != null and state != ''">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="endTime != null">end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="remindId != null">#{remindId},</if>
            <if test="flowId != null and flowId != ''">#{flowId},</if>
            <if test="nodeId != null and nodeId != ''">#{nodeId},</if>
            <if test="businessId != null and businessId != ''">#{businessId},</if>
            <if test="state != null and state != ''">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="endTime != null">#{endTime},</if>
        </trim>
    </insert>

    <insert id="insertRemindPerson">
        insert into noa_remind_person (remind_id,remind_person,state,bus_id)
        values
        <foreach collection="personDate" item="personDate" separator=",">
            (#{personDate.remindId},#{personDate.remindPerson},#{personDate.state},#{busId})
        </foreach>
    </insert>

    <update id="updateNoaWorkflowRemind" parameterType="NoaWorkflowRemind">
        update noa_workflow_remind
        <trim prefix="SET" suffixOverrides=",">
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="remindTitle != null and remindTitle != ''">remind_title = #{remindTitle},</if>
            <if test="remindText != null and remindText != ''">remind_text = #{remindText},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="setRemindState">
        UPDATE noa_remind_person set state = '1',notarize_time = now() where remind_id = #{remindId} AND remind_person = #{userName}
    </update>

    <update id="updateRemind">
        UPDATE noa_need_remind set state = '1' where id = #{remindId}
    </update>



    <delete id="deleteNoaWorkflowRemindByFlowIds" parameterType="String">
        delete from noa_workflow_remind where flow_id in
        <foreach item="flowId" collection="array" open="(" separator="," close=")">
            #{flowId}
        </foreach>
    </delete>

    <delete id="deleteRemindStateById">
        delete from noa_remind_person where remind_id = #{remindId}
    </delete>

    <delete id="deleteNoaWorkflowRemindByFlowId">
        delete from noa_workflow_remind where id in
        <foreach item="ids" collection="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </delete>

    <delete id="deleteAuthority">
        delete from noa_authority where remind_id in
        <foreach item="ids" collection="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </delete>
</mapper>
