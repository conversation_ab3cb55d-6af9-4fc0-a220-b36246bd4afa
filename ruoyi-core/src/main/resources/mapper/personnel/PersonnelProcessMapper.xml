<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.personnel.mapper.PersonnelProcessMapper">

    <resultMap type="PersonnelProcess" id="PersonnelProcessResult">
        <result property="id"    column="id"    />
        <result property="correlationId"    column="correlation_id"    />
        <result property="processType"    column="process_type"    />
        <result property="processName"    column="process_name"    />
        <result property="processId"    column="process_id"    />
        <result property="personnelName"    column="personnel_name"    />
        <result property="processState"    column="process_state"    />
        <result property="sponsor"    column="sponsor"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPersonnelProcessVo">
        select id,correlation_id ,process_type, process_name, process_id, personnel_name, process_state, sponsor,complete_time, create_by, create_time, update_by, update_time from rs_personnel_process
    </sql>

    <select id="selectPersonnelProcessList" parameterType="PersonnelProcess" resultMap="PersonnelProcessResult">
        <include refid="selectPersonnelProcessVo"/>
        <where>
            <if test="correlationId != null  and correlationId != ''"> and correlation_id = #{correlationId}</if>
            <if test="processType != null  and processType != ''"> and process_type = #{processType}</if>
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="processId != null  and processId != ''"> and process_id = #{processId}</if>
            <if test="personnelName != null  and personnelName != ''"> and personnel_name = #{personnelName}</if>
            <if test="processState != null  and processState != ''"> and process_state = #{processState}</if>
            <if test="sponsor != null  and sponsor != ''"> and sponsor = #{sponsor}</if>
        </where>
    </select>

    <select id="selectPersonnelProcessById" parameterType="Long" resultMap="PersonnelProcessResult">
        <include refid="selectPersonnelProcessVo"/>
        where id = #{id}
    </select>

    <select id="getByProcessId" parameterType="String" resultMap="PersonnelProcessResult">
        <include refid="selectPersonnelProcessVo"/>
        where process_id = #{id}
    </select>

    <insert id="insertPersonnelProcess" parameterType="PersonnelProcess" useGeneratedKeys="true" keyProperty="id">
        insert into rs_personnel_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="correlationId != null and correlationId != ''">correlation_id,</if>
            <if test="processType != null and processType != ''">process_type,</if>
            <if test="processName != null and processName != ''">process_name,</if>
            <if test="processId != null and processId != ''">process_id,</if>
            <if test="personnelName != null and personnelName != ''">personnel_name,</if>
            <if test="processState != null and processState != ''">process_state,</if>
            <if test="sponsor != null and sponsor != ''">sponsor,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="correlationId != null and correlationId != ''">#{correlationId},</if>
            <if test="processType != null and processType != ''">#{processType},</if>
            <if test="processName != null and processName != ''">#{processName},</if>
            <if test="processId != null and processId != ''">#{processId},</if>
            <if test="personnelName != null and personnelName != ''">#{personnelName},</if>
            <if test="processState != null and processState != ''">#{processState},</if>
            <if test="sponsor != null and sponsor != ''">#{sponsor},</if>
            <if test="completeTime != null">completeTime,</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePersonnelProcess" parameterType="PersonnelProcess">
        update rs_personnel_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="correlationId != null and correlationId != ''">correlation_id = #{correlationId},</if>
            <if test="processType != null and processType != ''">process_type = #{processType},</if>
            <if test="processName != null and processName != ''">process_name = #{processName},</if>
            <if test="processId != null and processId != ''">process_id = #{processId},</if>
            <if test="personnelName != null and personnelName != ''">personnel_name = #{personnelName},</if>
            <if test="processState != null and processState != ''">process_state = #{processState},</if>
            <if test="sponsor != null and sponsor != ''">sponsor = #{sponsor},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePersonnelProcessById" parameterType="Long">
        delete from rs_personnel_process where id = #{id}
    </delete>

    <delete id="deletePersonnelProcessByIds" parameterType="String">
        delete from rs_personnel_process where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getPersonnelProcessList" parameterType="PersonnelProcess" resultType="personnelProcessVo">
        select DISTINCT id,correlation_id ,process_type, process_name, process_id, personnel_name, process_state,sponsor, rpp.create_by,
               rpp.create_time,rpp.update_by, rpp.update_time
        from rs_personnel_process rpp
        left join sys_user su on su.user_name = rpp.create_by
        left join sys_user_post sup on sup.user_id = su.user_id
        left join sys_post post on sup.post_id = post.post_id and sup.home_post = 0
        left join sys_dept dept on dept.dept_id = post.dept_id
        <where>
            <if test="correlationId != null  and correlationId != ''"> and correlation_id = #{correlationId}</if>
            <if test="processType != null  and processType != ''"> and process_type = #{processType}</if>
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="processId != null  and processId != ''"> and process_id = #{processId}</if>
            <if test="personnelName != null  and personnelName != ''"> and personnel_name like concat('%', #{personnelName}, '%')</if>
            <if test="processState != null  and processState != ''"> and process_state = #{processState}</if>
            <if test="sponsor != null  and sponsor != ''"> and sponsor = #{sponsor}</if>
            <trim prefix="and (" suffix=")" prefixOverrides="and|or">
                <if test="createBy != null and createBy != ''">
                    or rpp.create_by = #{createBy}
                </if>
                <if test="unitIds != null and unitIds.size() > 0">
                    or dept.unit_id in
                    <foreach item="unitId" collection="unitIds" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                </if>
                <if test="deptIds != null and deptIds.size() > 0">
                    or dept.dept_id in
                    <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
            </trim>
        </where>
        order by create_time desc
    </select>

    <select id="getProcessList" parameterType="PersonnelProcess" resultType="personnelProcessVo">
        SELECT
            act_hi_procinst.PROC_INST_ID_,
            act_hi_procinst.BUSINESS_KEY_ as process_id,
            act_hi_actinst.START_TIME_,
            act_hi_actinst.ACT_NAME_ AS process_node
        FROM act_hi_procinst
        LEFT JOIN act_hi_actinst ON act_hi_procinst.PROC_INST_ID_ = act_hi_actinst.PROC_INST_ID_
        WHERE act_hi_actinst.START_TIME_ = (
            SELECT MAX(act_hi_actinst.START_TIME_)
            FROM act_hi_actinst
            WHERE act_hi_actinst.PROC_INST_ID_ = act_hi_procinst.PROC_INST_ID_
        )
        AND act_hi_procinst.PROC_INST_ID_ IN
        <foreach item="processId" collection="processIds" open="(" close=")" separator=",">
            #{processId}
        </foreach>
    </select>

    <update id="completeProcessByProcessId" parameterType="PersonnelProcess">
        update rs_personnel_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="processState != null and processState != ''">process_state = #{processState},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where process_id = #{processId}
    </update>

    <update id="abandonedProcess" parameterType="PersonnelProcess">
        update rs_personnel_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="processState != null and processState != ''">process_state = #{processState},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where correlation_id = #{correlationId}
          and process_type = #{processType}
          and process_state != 2
    </update>

    <update id="draftSubmit" parameterType="PersonnelProcess">
        update rs_personnel_process
        set process_state = 1
        where process_id = #{processId}
    </update>
</mapper>
