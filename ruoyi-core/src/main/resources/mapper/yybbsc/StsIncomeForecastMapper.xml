<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.yybbsc.mapper.StsIncomeForecastMapper">

    <resultMap type="StsIncomeForecastLoanInfo" id="StsIncomeForecastLoanInfoResult">
        <result property="id"    column="id"    />
        <result property="productNo"    column="product_no"    />
        <result property="loanMonth"    column="loan_month"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="productType"    column="product_type"    />
        <result property="phase"    column="phase"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="StsIncomeForecastRepaymentInfo" id="StsIncomeForecastRepaymentInfoResult">
        <result property="id"    column="id"    />
        <result property="productNo"    column="product_no"    />
        <result property="loanMonth"    column="loan_month"    />
        <result property="productType"    column="product_type"    />
        <result property="phase"    column="phase"    />
        <result property="repaymentMonth"    column="repayment_month"    />
        <result property="repaymentPrintAmount"    column="repayment_print_amount"    />
        <result property="repaymentIntAmount"    column="repayment_int_amount"    />
        <result property="repaymentOintAmt"    column="repayment_oint_amt"    />
        <result property="repaymentFlAmt"    column="repayment_fl_amt"    />
        <result property="advDefineAmt"    column="adv_define_amt"    />
        <result property="deductAmt"    column="deduct_amt"    />
        <result property="reduceAmt"    column="reduce_amt"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="StsIncomeForecastInfo" id="StsIncomeForecastInfoResult">
        <result property="id"    column="id"    />
        <result property="productNo"    column="product_no"    />
        <result property="loanMonth"    column="loan_month"    />
        <result property="repaymentMonth"    column="repayment_month"    />
        <result property="jtFrAmt"    column="jt_fr_amt"    />
        <result property="costOfCapital"    column="cost_of_capital"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="StsIncomeForecastInfoCompensatory" id="StsIncomeForecastInfoCompensatoryResult">
        <result property="id"    column="id"    />
        <result property="productNo"    column="product_no"    />
        <result property="loanMonth"    column="loan_month"    />
        <result property="compensatoryMonth"    column="compensatory_month"    />
        <result property="compensateTotalAmt"    column="compensate_total_amt"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStsIncomeForecastLoanInfoVo">
        select id, product_no, loan_month, loan_amt, product_type, phase, create_time, update_time from sts_income_forecast_loan_info
    </sql>

    <sql id="selectStsIncomeForecastRepaymentInfoVo">
        select id, product_no, loan_month, product_type, phase, repayment_month, repayment_print_amount, repayment_int_amount, repayment_oint_amt, repayment_fl_amt, adv_define_amt, deduct_amt, reduce_amt, create_time, update_time from sts_income_forecast_repayment_info
    </sql>

    <sql id="selectStsIncomeForecastInfoVo">
        select id, product_no, loan_month, repayment_month, jt_fr_amt, cost_of_capital, create_time, update_time from sts_income_forecast_info
    </sql>

    <sql id="selectStsIncomeForecastInfoCompensatoryVo">
        select id, product_no, loan_month, compensatory_month, compensate_total_amt, create_time, update_time from sts_income_forecast_info_compensatory
    </sql>

    <insert id="insertStsIncomeForecastLoanInfo" parameterType="StsIncomeForecastLoanInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sts_income_forecast_loan_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productNo != null and productNo !=''">product_no,</if>
            <if test="loanMonth != null and loanMonth != ''">loan_month,</if>
            <if test="loanAmt != null">loan_amt,</if>
            <if test="productType != null and productType != ''">product_type,</if>
            <if test="phase != null">phase,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productNo != null and productNo !=''">#{productNo},</if>
            <if test="loanMonth != null and loanMonth != ''">#{loanMonth},</if>
            <if test="loanAmt != null">#{loanAmt},</if>
            <if test="productType != null and productType != ''">#{productType},</if>
            <if test="phase != null">#{phase},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

      <insert id="insertStsIncomeForecastRepaymentInfo" parameterType="StsIncomeForecastRepaymentInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sts_income_forecast_repayment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productNo != null and productNo !=''">product_no,</if>
            <if test="loanMonth != null and loanMonth != ''">loan_month,</if>
            <if test="productType != null and productType != ''">product_type,</if>
            <if test="phase != null">phase,</if>
            <if test="repaymentMonth != null and repaymentMonth != ''">repayment_month,</if>
            <if test="importMonth != null and importMonth != ''">import_month,</if>
            <if test="repaymentPrintAmount != null">repayment_print_amount,</if>
            <if test="repaymentIntAmount != null">repayment_int_amount,</if>
            <if test="repaymentOintAmt != null">repayment_oint_amt,</if>
            <if test="repaymentFlAmt != null">repayment_fl_amt,</if>
            <if test="advDefineAmt != null">adv_define_amt,</if>
            <if test="deductAmt != null">deduct_amt,</if>
            <if test="reduceAmt != null">reduce_amt,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productNo != null and productNo !=''">#{productNo},</if>
            <if test="loanMonth != null and loanMonth != ''">#{loanMonth},</if>
            <if test="productType != null and productType != ''">#{productType},</if>
            <if test="phase != null">#{phase},</if>
            <if test="repaymentMonth != null and repaymentMonth != ''">#{repaymentMonth},</if>
            <if test="importMonth != null and importMonth != ''">#{importMonth},</if>
            <if test="repaymentPrintAmount != null">#{repaymentPrintAmount},</if>
            <if test="repaymentIntAmount != null">#{repaymentIntAmount},</if>
            <if test="repaymentOintAmt != null">#{repaymentOintAmt},</if>
            <if test="repaymentFlAmt != null">#{repaymentFlAmt},</if>
            <if test="advDefineAmt != null">#{advDefineAmt},</if>
            <if test="deductAmt != null">#{deductAmt},</if>
            <if test="reduceAmt != null">#{reduceAmt},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertStsIncomeForecastInfo" parameterType="StsIncomeForecastInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sts_income_forecast_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productNo != null and productNo !=''">product_no,</if>
            <if test="loanMonth != null and loanMonth != ''">loan_month,</if>
            <if test="repaymentMonth != null and repaymentMonth != ''">repayment_month,</if>
            <if test="jtFrAmt != null">jt_fr_amt,</if>
            <if test="costOfCapital != null">cost_of_capital,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productNo != null and productNo !=''">#{productNo},</if>
            <if test="loanMonth != null and loanMonth != ''">#{loanMonth},</if>
            <if test="repaymentMonth != null and repaymentMonth != ''">#{repaymentMonth},</if>
            <if test="jtFrAmt != null">#{jtFrAmt},</if>
            <if test="costOfCapital != null">#{costOfCapital},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertStsIncomeForecastInfoCompensatory" parameterType="StsIncomeForecastInfoCompensatory" useGeneratedKeys="true" keyProperty="id">
        insert into sts_income_forecast_info_compensatory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productNo != null and productNo !=''">product_no,</if>
            <if test="loanMonth != null and loanMonth != ''">loan_month,</if>
            <if test="compensatoryMonth != null and compensatoryMonth != ''">compensatory_month,</if>
            <if test="compensateTotalAmt != null">compensate_total_amt,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productNo != null and productNo !=''">#{productNo},</if>
            <if test="loanMonth != null and loanMonth != ''">#{loanMonth},</if>
            <if test="compensatoryMonth != null and compensatoryMonth != ''">#{compensatoryMonth},</if>
            <if test="compensateTotalAmt != null">#{compensateTotalAmt},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <select id="queryAllInfo" resultMap="StsIncomeForecastInfoResult">
        <include refid="selectStsIncomeForecastInfoVo"/>
    </select>

    <resultMap type="org.ruoyi.core.yybbsc.domain.vo.StsIncomeForecastLoanVo" id="StsIncomeForecastLoanVoResult">
        <result property="loanMonth"    column="loan_month"    />
        <result property="loanId"    column="loan_id"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="productType"    column="product_type"    />
        <result property="phase"    column="phase"    />
        <result property="repaymentMonth"    column="repayment_month"    />
        <result property="repaymentPrintAmount"    column="repayment_print_amount"    />
        <result property="repaymentIntAmount"    column="repayment_int_amount"    />
        <result property="repaymentOintAmt"    column="repayment_oint_amt"    />
        <result property="repaymentFlAmt"    column="repayment_fl_amt"    />
        <result property="advDefineAmt"    column="adv_define_amt"    />
        <result property="deductAmt"    column="deduct_amt"    />
        <result property="reduceAmt"    column="reduce_amt"    />
        <result property="monthEndBalanceAmt"    column="month_end_balance_amt"    />
        <result property="technicalServiceFee"    column="technical_service_fee"    />
    </resultMap>

    <select id="queryLoanInfoAndRepayInfoByLoanMonth" resultMap="StsIncomeForecastLoanVoResult">
        SELECT sifri.loan_month,sifli.loan_amt,
            CASE WHEN sifri.product_type='0' THEN '无'
	            WHEN sifri.product_type='1' THEN '随借随还'
                WHEN sifri.product_type='2' THEN '等本等息'
	            WHEN sifri.product_type='3' THEN '先息后本'  ELSE '' END AS product_type,
        sifri.phase,
        sifli.id AS loan_id,
        sifri.repayment_month,
        sifri.repayment_print_amount,
        sifri.repayment_int_amount,
        sifri.repayment_oint_amt,
        sifri.repayment_fl_amt,
        sifri.adv_define_amt,
        sifri.deduct_amt,
        sifri.reduce_amt
        FROM sts_income_forecast_repayment_info sifri
        LEFT JOIN sts_income_forecast_loan_info sifli ON (sifri.loan_month=sifli.loan_month AND sifri.product_type=sifli.product_type AND sifri.phase=sifli.phase)
        WHERE sifri.loan_month=#{loanMonth,jdbcType=VARCHAR} ORDER BY sifli.id,sifri.repayment_month
    </select>

    <select id="queryAllLoanInfoAndRepayInfo" resultMap="StsIncomeForecastLoanVoResult">
        SELECT sifri.loan_month,sifli.loan_amt,
            CASE WHEN sifri.product_type='0' THEN '无'
	            WHEN sifri.product_type='1' THEN '随借随还'
                WHEN sifri.product_type='2' THEN '等本等息'
	            WHEN sifri.product_type='3' THEN '先息后本'  ELSE '' END AS product_type,
        sifri.phase,
        sifli.id AS loan_id,
        sifri.repayment_month,
        sifri.repayment_print_amount,
        sifri.repayment_int_amount,
        sifri.repayment_oint_amt,
        sifri.repayment_fl_amt,
        sifri.adv_define_amt,
        sifri.deduct_amt,
        sifri.reduce_amt
        FROM sts_income_forecast_repayment_info sifri
        LEFT JOIN sts_income_forecast_loan_info sifli ON (sifri.loan_month=sifli.loan_month AND sifri.product_type=sifli.product_type AND sifri.phase=sifli.phase)
        WHERE sifli.id!='' ORDER BY sifli.id,sifri.repayment_month
    </select>



    <resultMap type="org.ruoyi.core.yybbsc.domain.vo.StsIncomeForecastRepayMonthVo" id="StsIncomeForecastRepayMonthVoResult">
        <result property="loanMonth"    column="loan_month"    />
        <result property="productType"    column="product_type"    />
        <result property="phase"    column="phase"    />
        <result property="repaymentMonth"    column="repayment_month"    />
        <result property="repaymentPrintAmount"    column="repayment_print_amount"    />
        <result property="repaymentIntAmount"    column="repayment_int_amount"    />
        <result property="repaymentOintAmt"    column="repayment_oint_amt"    />
        <result property="repaymentFlAmt"    column="repayment_fl_amt"    />
        <result property="advDefineAmt"    column="adv_define_amt"    />
        <result property="deductAmt"    column="deduct_amt"    />
        <result property="reduceAmt"    column="reduce_amt"    />
        <result property="monthEndBalanceAmt"    column="month_end_balance_amt"    />
        <result property="technicalServiceFee"    column="technical_service_fee"    />
        <result property="paidInterestFee"    column="paid_interest_fee"    />
        <result property="jtFrAmt"    column="jt_fr_amt"    />
        <result property="zbFrAmt"    column="zb_fr_amt"    />
        <result property="costOfCapital"    column="cost_of_capital"    />
        <result property="zbIncome"    column="zb_income"    />
        <result property="netIncome"    column="net_income"    />
        <result property="compensateTotalAmt"    column="compensate_total_amt"    />
        <result property="netIncomeCompensateTotalAmt"    column="net_income_compensate_total_amt"    />
        <result property="avgBalance"    column="avg_balance"    />
        <result property="fa"    column="fa"    />
        <result property="marginCost"    column="margin_cost"    />
        <result property="incomeNet"    column="income_net"    />
    </resultMap>

    <select id="queryInfoAndInfoCompensatoryByLoanMonth" resultMap="StsIncomeForecastRepayMonthVoResult">
<!--        SELECT sifi.loan_month,-->
<!--        '全部' AS product_type,-->
<!--        '全部' AS phase,-->
<!--        sifi.repayment_month,-->
<!--        SUM(sifi.jt_fr_amt) AS jt_fr_amt,-->
<!--        SUM(sifi.cost_of_capital) AS cost_of_capital-->
<!--        FROM sts_income_forecast_info sifi-->
<!--        WHERE sifi.loan_month=#{loanMonth,jdbcType=VARCHAR} GROUP BY repayment_month-->
        SELECT sifi.loan_month,
        '全部' AS product_type,
        '全部' AS phase,
        sifi.repayment_month,
        SUM(sifi.jt_fr_amt) AS jt_fr_amt,
        SUM(sifi.cost_of_capital) AS cost_of_capital,
		SUM(sific.compensate_total_amt) AS compensate_total_amt
        FROM sts_income_forecast_info sifi LEFT JOIN sts_income_forecast_info_compensatory sific ON (sifi.loan_month=sific.loan_month AND sifi.repayment_month=sific.compensatory_month) WHERE sifi.loan_month=#{loanMonth,jdbcType=VARCHAR} GROUP BY sifi.repayment_month ORDER BY sifi.repayment_month
    </select>

    <select id="queryAllRepayInfoAndInfo" resultMap="StsIncomeForecastRepayMonthVoResult">
        SELECT sifi.loan_month,
        '全部' AS product_type,
        '全部' AS phase,
        sifi.repayment_month,
        SUM(sifi.jt_fr_amt) AS jt_fr_amt,
        SUM(sifi.cost_of_capital) AS cost_of_capital,
				SUM(sific.compensate_total_amt) AS compensate_total_amt
        FROM sts_income_forecast_info sifi LEFT JOIN sts_income_forecast_info_compensatory sific ON (sifi.loan_month=sific.loan_month AND sifi.repayment_month=sific.compensatory_month) GROUP BY sifi.loan_month,sifi.repayment_month
    </select>


    <resultMap type="org.ruoyi.core.yybbsc.domain.vo.StsIncomeForecastTotalVo" id="StsIncomeForecastTotalVoResult">
        <result property="loanMonth"    column="loan_month"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="productType"    column="product_type"    />
        <result property="phase"    column="phase"    />
        <result property="repaymentMonth"    column="repayment_month"    />
        <result property="technicalServiceFee"    column="technical_service_fee"    />
        <result property="paidInterestFee"    column="paid_interest_fee"    />
        <result property="jtFrAmt"    column="jt_fr_amt"    />
        <result property="zbFrAmt"    column="zb_fr_amt"    />
        <result property="costOfCapital"    column="cost_of_capital"    />
        <result property="zbIncome"    column="zb_income"    />
        <result property="netIncome"    column="net_income"    />
        <result property="netIncomeCompensateTotalAmt"    column="net_income_compensate_total_amt"    />
        <result property="fa"    column="fa"    />
        <result property="marginCost"    column="margin_cost"    />
        <result property="incomeNet"    column="income_net"    />
    </resultMap>

    <resultMap type="org.ruoyi.core.yybbsc.domain.dto.StsIncomeForecastTotalDtoPart1" id="StsIncomeForecastTotalDtoPart1Result">
        <result property="loanMonth"    column="loan_month"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="productType"    column="product_type"    />
        <result property="phase"    column="phase"    />
        <result property="repaymentMonth"    column="repayment_month"    />
        <result property="technicalServiceFee"    column="technical_service_fee"    />
    </resultMap>

    <resultMap type="org.ruoyi.core.yybbsc.domain.dto.StsIncomeForecastTotalDtoPart2" id="StsIncomeForecastTotalDtoPart2Result">
        <result property="loanMonth"    column="loan_month"    />
        <result property="paidInterestFee"    column="paid_interest_fee"    />
        <result property="jtFrAmt"    column="jt_fr_amt"    />
        <result property="zbFrAmt"    column="zb_fr_amt"    />
        <result property="costOfCapital"    column="cost_of_capital"    />
        <result property="zbIncome"    column="zb_income"    />
        <result property="netIncome"    column="net_income"    />
        <result property="netIncomeCompensateTotalAmt"    column="net_income_compensate_total_amt"    />
        <result property="fa"    column="fa"    />
        <result property="marginCost"    column="margin_cost"    />
        <result property="incomeNet"    column="income_net"    />
    </resultMap>

    <resultMap type="org.ruoyi.core.yybbsc.domain.dto.TechnicalServiceFee" id="TechnicalServiceFeeResult">
        <result property="loanMonth"    column="loan_month"    />
        <result property="technicalServiceFee"    column="technical_service_fee"    />
    </resultMap>

    <select id="queryAllInfoTechnicalServiceFee" resultMap="TechnicalServiceFeeResult">
        SELECT sifli.loan_month,SUM(sifri.technical_service_fee) AS technical_service_fee FROM sts_income_forecast_repayment_info sifri,sts_income_forecast_loan_info sifli WHERE sifli.id=sifri.loan_id GROUP BY sifli.loan_month
    </select>

    <select id="queryAllTotalInfoPart1" resultMap="StsIncomeForecastTotalDtoPart1Result">
        SELECT loan_month,SUM(loan_amt) AS loan_amt,'全部' AS product_type,'全部' AS phase,'全部' AS repayment_month
        FROM sts_income_forecast_loan_info sifli GROUP BY sifli.loan_month
    </select>

    <select id="queryAllTotalInfoPart2" resultMap="StsIncomeForecastTotalDtoPart2Result">
        SELECT sifi.loan_month,SUM(sifi.paid_interest_fee) AS paid_interest_fee,SUM(sifi.jt_fr_amt) AS jt_fr_amt,SUM(sifi.zb_fr_amt) AS zb_fr_amt,SUM(sifi.cost_of_capital) AS cost_of_capital,
        SUM(sifi.zb_income) AS zb_income,SUM(sifi.net_income) AS net_income,SUM(sifi.compensate_total_amt) AS compensate_total_amt,SUM(sifi.net_income_compensate_total_amt) AS net_income_compensate_total_amt,
        SUM(sifi.avg_balance) AS avg_balance,SUM(sifi.fa) AS fa,SUM(sifi.margin_cost) AS margin_cost,SUM(sifi.income_net) AS income_net
        FROM sts_income_forecast_info sifi GROUP BY sifi.loan_month
    </select>

    <select id="queryLoanMonthDateFirst" resultType="java.lang.String">
        SELECT DISTINCT CONCAT(substring(loan_month,1,4),'年',substring(loan_month,6,2),'月') AS loan_month FROM sts_income_forecast_loan_info ORDER BY loan_month DESC
    </select>

    <select id="queryLoanMonthDateFirst1" resultType="java.lang.String">
        SELECT DISTINCT loan_month FROM sts_income_forecast_loan_info ORDER BY loan_month DESC
    </select>

    <delete id="deleteLoanInfoByLoanMonth">
        DELETE FROM sts_income_forecast_loan_info WHERE loan_month=#{loanMonth,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteRepayInfoByRepaymentMonth">
        DELETE FROM sts_income_forecast_repayment_info WHERE repayment_month=#{repaymentMonth,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteRepayInfoByImportMonth">
        DELETE FROM sts_income_forecast_repayment_info WHERE import_month=#{importMonth,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteInfoByLoanMonth">
        DELETE FROM sts_income_forecast_info WHERE repayment_month=#{repaymentMonth,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteInfoCompensatoryByCompensatoryMonth">
        DELETE FROM sts_income_forecast_info_compensatory WHERE compensatory_month=#{compensatoryMonth,jdbcType=VARCHAR}
    </delete>

    <select id="queryAllLoanInfo" resultMap="StsIncomeForecastLoanInfoResult">
        <include refid="selectStsIncomeForecastLoanInfoVo"/>
    </select>

    <select id="queryAllRepayInfo" resultMap="StsIncomeForecastRepaymentInfoResult">
        <include refid="selectStsIncomeForecastRepaymentInfoVo"/>
    </select>

    <select id="queryRepayinfoByRepaymentMonth" resultMap="StsIncomeForecastRepaymentInfoResult">
        <include refid="selectStsIncomeForecastRepaymentInfoVo"/> WHERE repayment_month=#{repaymentMonth,jdbcType=VARCHAR}
    </select>

    <select id="queryInfoByRepaymentMonth" resultMap="StsIncomeForecastInfoResult">
        <include refid="selectStsIncomeForecastInfoVo"/> WHERE repayment_month=#{repaymentMonth,jdbcType=VARCHAR}
    </select>

    <select id="queryInfoAndInfoCompensatoryByRepaymentMonth" resultMap="StsIncomeForecastRepayMonthVoResult">
        SELECT sifi.loan_month,
        '全部' AS product_type,
        '全部' AS phase,
        sifi.repayment_month,
        SUM(sifi.jt_fr_amt) AS jt_fr_amt,
        SUM(sifi.cost_of_capital) AS cost_of_capital,
		SUM(sific.compensate_total_amt) AS compensate_total_amt
        FROM sts_income_forecast_info sifi LEFT JOIN sts_income_forecast_info_compensatory sific ON (sifi.loan_month=sific.loan_month AND sifi.repayment_month=sific.compensatory_month) WHERE sifi.repayment_month=#{repaymentMonth,jdbcType=VARCHAR} GROUP BY sifi.repayment_month
    </select>

    <select id="querySumLoanInfoAndRepayInfoByLoanMonth" resultMap="StsIncomeForecastLoanVoResult">
        SELECT sifri.loan_month,
        '全部' AS product_type,
        sifri.repayment_month AS repayment_month,
        SUM(sifri.repayment_print_amount) AS repayment_print_amount,
        SUM(sifri.repayment_int_amount) AS repayment_int_amount,
        SUM(sifri.repayment_oint_amt) AS repayment_oint_amt,
        SUM(sifri.repayment_fl_amt) AS repayment_fl_amt,
        SUM(sifri.adv_define_amt) AS adv_define_amt,
        SUM(sifri.deduct_amt) AS deduct_amt,
        SUM(sifri.reduce_amt) AS reduce_amt
        FROM sts_income_forecast_repayment_info sifri
        LEFT JOIN sts_income_forecast_loan_info sifli ON (sifri.loan_month=sifli.loan_month AND sifri.product_type=sifli.product_type AND sifri.phase=sifli.phase)
        WHERE sifri.loan_month=#{loanMonth,jdbcType=VARCHAR} GROUP BY sifri.repayment_month ORDER BY sifri.repayment_month
    </select>

    <select id="querySumLoanInfoAndRepayInfoAll" resultMap="StsIncomeForecastLoanVoResult">
        SELECT sifri.loan_month,
        '全部' AS product_type,
        sifri.repayment_month AS repayment_month,
        SUM(sifri.repayment_print_amount) AS repayment_print_amount,
        SUM(sifri.repayment_int_amount) AS repayment_int_amount,
        SUM(sifri.repayment_oint_amt) AS repayment_oint_amt,
        SUM(sifri.repayment_fl_amt) AS repayment_fl_amt,
        SUM(sifri.adv_define_amt) AS adv_define_amt,
        SUM(sifri.deduct_amt) AS deduct_amt,
        SUM(sifri.reduce_amt) AS reduce_amt
        FROM sts_income_forecast_repayment_info sifri
        LEFT JOIN sts_income_forecast_loan_info sifli ON (sifri.loan_month=sifli.loan_month AND sifri.product_type=sifli.product_type AND sifri.phase=sifli.phase)
        GROUP BY sifri.loan_month,sifri.repayment_month ORDER BY sifri.repayment_month
    </select>

    <select id="queryLoanMonthByImportMonth" resultType="java.lang.String">
        SELECT DISTINCT CONCAT(substring(loan_month,1,4),'-',substring(loan_month,6,2)) AS loan_month FROM sts_income_forecast_loan_info WHERE loan_month=#{importMonth,jdbcType=VARCHAR} ORDER BY loan_month DESC
    </select>

    <select id="queryLoanMonthDateFirstStr" resultType="java.lang.String">
        SELECT DISTINCT loan_month FROM sts_income_forecast_loan_info ORDER BY loan_month DESC
    </select>
</mapper>