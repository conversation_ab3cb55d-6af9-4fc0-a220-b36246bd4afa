<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.kaoqin.mapper.WorkOvertimeSlaveMapper">

    <resultMap type="WorkOvertimeSlave" id="WorkOvertimeSlaveResult">
        <result property="id"    column="id"    />
        <result property="overtimeId"    column="overtime_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="startTimePeriod"    column="start_time_period"    />
        <result property="endTime"    column="end_time"    />
        <result property="endTimePeriod"    column="end_time_period"    />
        <result property="times"    column="times"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="processId"    column="process_id"    />
    </resultMap>

    <sql id="selectWorkOvertimeSlaveVo">
        select id, overtime_id, start_time, start_time_period, end_time, end_time_period, times, create_by, create_time, update_by, update_time from kq_work_overtime_slave
    </sql>

    <select id="selectWorkOvertimeSlaveList" parameterType="WorkOvertimeSlave" resultMap="WorkOvertimeSlaveResult">
        select kwos.id, overtime_id, start_time, start_time_period, end_time,
        end_time_period, times ,kwo.process_id
        from kq_work_overtime_slave kwos
        left join kq_work_overtime kwo on kwos.overtime_id= kwo.id
        <where>
            kwo.is_delete = 1
            <if test="overtimeId != null "> and overtime_id = #{overtimeId}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="effective != null "> and kwo.effective = #{effective}</if>
            <if test="startTimePeriod != null "> and start_time_period = #{startTimePeriod}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="endTimePeriod != null "> and end_time_period = #{endTimePeriod}</if>
            <if test="times != null "> and times = #{times}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="month != null  and month != ''">and (kwos.start_time like concat('%', #{month}, '%')  or kwos.end_time like concat('%', #{month}, '%'))</if>
            <if test="createBy != null "> and kwos.create_by = #{createBy}</if>
        </where>
    </select>

    <select id="selectWorkOvertimeSlaveById" parameterType="Long" resultMap="WorkOvertimeSlaveResult">
        <include refid="selectWorkOvertimeSlaveVo"/>
        where id = #{id}
    </select>

    <insert id="insertWorkOvertimeSlave" parameterType="WorkOvertimeSlave" useGeneratedKeys="true" keyProperty="id">
        insert into kq_work_overtime_slave
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="overtimeId != null">overtime_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="startTimePeriod != null">start_time_period,</if>
            <if test="endTime != null">end_time,</if>
            <if test="endTimePeriod != null">end_time_period,</if>
            <if test="times != null">times,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="overtimeId != null">#{overtimeId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="startTimePeriod != null">#{startTimePeriod},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="endTimePeriod != null">#{endTimePeriod},</if>
            <if test="times != null">#{times},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="insertWorkOvertimeSlaveBatch" parameterType="java.util.List">
        insert into kq_work_overtime_slave
        (overtime_id, start_time, start_time_period, end_time, end_time_period, times, create_by, create_time )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.overtimeId},
                #{item.startTime},
                #{item.startTimePeriod},
                #{item.endTime},
                #{item.endTimePeriod},
                #{item.times},
                #{item.createBy},
                #{item.createTime}
            )
        </foreach>
    </insert>

    <update id="updateWorkOvertimeSlave" parameterType="WorkOvertimeSlave">
        update kq_work_overtime_slave
        <trim prefix="SET" suffixOverrides=",">
            <if test="overtimeId != null">overtime_id = #{overtimeId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="startTimePeriod != null">start_time_period = #{startTimePeriod},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="endTimePeriod != null">end_time_period = #{endTimePeriod},</if>
            <if test="times != null">times = #{times},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkOvertimeSlaveById" parameterType="Long">
        delete from kq_work_overtime_slave where id = #{id}
    </delete>

    <delete id="deleteWorkOvertimeSlaveByIds" parameterType="String">
        delete from kq_work_overtime_slave where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteWorkOvertimeSlaveByOverTimeId" parameterType="Long">
        delete from kq_work_overtime_slave where overtime_id = #{overTimeId}
    </delete>
</mapper>
