<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.kaoqin.mapper.RewardPunishmentMapper">

    <resultMap type="rewardPunishmentVo" id="RewardPunishmentResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="rewardPunishmentCode"    column="reward_punishment_code"    />
        <result property="type"    column="type"    />
        <result property="measure"    column="measure"    />
        <result property="amount"    column="amount"    />
        <result property="reason"    column="reason"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="effective"    column="effective"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemNum"    column="item_num"    />
        <result property="auditCompletionTime"    column="audit_completion_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="processId"    column="process_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />

        <result property="deptName"    column="dept_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="nickName"    column="nick_name"  />
        <result property="createNickName"  column="create_nick_name" />
        <result property="voidReason"  column="void_reason" />
        <result property="refuseReason"  column="refuse_reason" />
        <result property="handleState"  column="handle_state" />
        <result property="createByUserId"  column="create_by_user_id" />
        <result property="createNickName"  column="create_nick_name"/>
        <result property="companyName"  column="company_name" />
        <collection property="files"    column="id = id"  select = "selectKqFileList"/>
    </resultMap>

    <resultMap type="KqFile" id="KqFileResult">
        <result property="id"    column="id"    />
        <result property="correlationId"    column="correlation_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileState"    column="file_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRewardPunishmentVo">
        select id, user_id, reward_punishment_code, type, measure, amount, reason, remark, status, effective, item_name, item_num, audit_completion_time, is_delete, process_id, create_by, create_time, update_by, update_time from kq_reward_punishment
    </sql>

    <select id="selectRewardPunishmentList" parameterType="RewardPunishmentVo" resultMap="RewardPunishmentResult">
        select krp.id, krp.user_id, krp.reward_punishment_code, krp.type, krp.measure, krp.amount, krp.reason, krp.remark
             , krp.status, krp.effective, krp.item_name, krp.item_num, krp.audit_completion_time, krp.is_delete
             , krp.process_id, krp.create_by, krp.create_time, krp.update_by, krp.update_time
             , dept.dept_name ,dept.dept_id
             , cuser.user_id as create_user_id, cuser.nick_name as create_nick_name
             , user.nick_name
        from kq_reward_punishment krp
        left join sys_user_post sup on sup.user_id = krp.user_id and sup.home_post = 0
        left join sys_post post on sup.post_id = post.post_id
        left join sys_dept dept on dept.dept_id = post.dept_id
        left join sys_user cuser on cuser.user_name = krp.create_by
        left join sys_user user on user.user_id = krp.user_id

        left join sys_user_post csup on csup.user_id = cuser.user_id and csup.home_post = 0
        left join sys_post cpost on csup.post_id = cpost.post_id
        left join sys_dept cdept on cdept.dept_id = cpost.dept_id
        <where>
            is_delete = '0'
            <if test="itemName != null  and itemName != ''"> and krp.item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemNum != null "> and krp.item_num = #{itemNum}</if>
            <if test="processId != null  and processId != ''"> and krp.process_id = #{processId}</if>
            <if test="reason != null  and reason != ''"> and krp.reason like concat('%', #{reason}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and user.nick_name = #{nickName}</if>
            <if test="deptId != null  and deptId != ''"> and dept.dept_id = #{deptId}</if>
            <if test="type != null  and type != ''"> and krp.type = #{type}</if>
            <if test="processId != null  and processId != ''"> and krp.process_id = #{processId}</if>
            <if test="amountMin != null  and amountMin != ''"> and krp.amount >= #{amountMin}</if>
            <if test="amountMax != null  and amountMax != ''"> and #{amountMax} >= krp.amount </if>
            <if test="createNickName != null  and createNickName != ''"> and cuser.nick_name like concat('%', #{createNickName}, '%') </if>
            <if test="createTimeStart != null  ">
                AND date_format(krp.create_time,'%Y-%m-%d') <![CDATA[ >= ]]>  date_format(#{createTimeStart},'%Y-%m-%d')
            </if>
            <if test="createTimeEnd != null  ">
                AND date_format(krp.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> date_format(#{createTimeEnd},'%Y-%m-%d')
            </if>
            <if test="auditCompletionTimeStart != null  ">
                AND date_format(krp.audit_completion_time,'%Y-%m-%d') <![CDATA[ >= ]]>  date_format(#{auditCompletionTimeStart},'%Y-%m-%d')
            </if>
            <if test="auditCompletionTimeEnd != null  ">
                AND date_format(krp.audit_completion_time,'%Y-%m-%d') <![CDATA[ <= ]]> date_format(#{auditCompletionTimeEnd},'%Y-%m-%d')
            </if>
            <if test="status!= null  and status!= ''"> and krp.status = #{status}</if>
            <if test="effective!= null  and effective!= ''"> and krp.effective = #{effective}</if>
            <if test="ids != null and ids.size() > 0">
                and krp.id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="createBy != null and createBy != ''"> and (krp.create_by = #{createBy} or (krp.status = 2 OR krp.status = 3)) </if>
            <trim prefix="and (" suffix=")" prefixOverrides="and|or">
                <if test="createBy != null and createBy != ''">
                    or krp.create_by = #{createBy}
                </if>
                <if test="unitIds != null and unitIds.size() > 0">
                    or cdept.unit_id in
                    <foreach item="unitId" collection="unitIds" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                </if>
                <if test="deptIds != null and deptIds.size() > 0">
                    or cdept.dept_id in
                    <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
                <if test="createByList != null and createByList.size() > 0">
                    or krp.create_by in
                    <foreach item="createBy" collection="createByList" open="(" close=")" separator=",">
                        #{createBy}
                    </foreach>
                </if>
            </trim>
        </where>
        group by krp.id
        order by  FIELD(krp.status, '1', '2', '4', '3'),
                  krp.create_time desc
    </select>

    <select id="selectRewardPunishmentById" parameterType="Long" resultMap="RewardPunishmentResult">
        select krp.id, krp.user_id, krp.reward_punishment_code, krp.type, krp.measure, krp.amount, krp.reason, krp.remark
             , krp.status, krp.effective, krp.item_name, krp.item_num, krp.audit_completion_time, krp.is_delete
             , krp.process_id, krp.create_by, krp.create_time, krp.update_by, krp.update_time
             , dept.dept_name ,dept.dept_id
             , cuser.user_id as create_user_id, cuser.nick_name as create_nick_name
             , user.nick_name ,sc.company_name
        from kq_reward_punishment krp
        left join sys_user_post sup on sup.user_id = krp.user_id and sup.home_post = 0
        left join sys_post post on sup.post_id = post.post_id
        left join sys_dept dept on dept.dept_id = post.dept_id
        left join sys_user cuser on cuser.user_name = krp.create_by
        left join sys_user user on user.user_id = krp.user_id
        left join rs_personnel_archives rpa on rpa.sys_name = user.user_name
        left join sys_company sc on sc.id = rpa.archives_company
        where krp.id = #{id}
    </select>

    <insert id="insertRewardPunishment" parameterType="RewardPunishment" useGeneratedKeys="true" keyProperty="id">
        insert into kq_reward_punishment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="rewardPunishmentCode != null">reward_punishment_code,</if>
            <if test="type != null">type,</if>
            <if test="measure != null">measure,</if>
            <if test="amount != null">amount,</if>
            <if test="reason != null">reason,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="effective != null">effective,</if>
            <if test="itemName != null">item_name,</if>
            <if test="itemNum != null">item_num,</if>
            <if test="auditCompletionTime != null">audit_completion_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="processId != null">process_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="rewardPunishmentCode != null">#{rewardPunishmentCode},</if>
            <if test="type != null">#{type},</if>
            <if test="measure != null">#{measure},</if>
            <if test="amount != null">#{amount},</if>
            <if test="reason != null">#{reason},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="effective != null">#{effective},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="itemNum != null">#{itemNum},</if>
            <if test="auditCompletionTime != null">#{auditCompletionTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="processId != null">#{processId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateRewardPunishment" parameterType="RewardPunishment">
        update kq_reward_punishment
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="rewardPunishmentCode != null">reward_punishment_code = #{rewardPunishmentCode},</if>
            <if test="type != null">type = #{type},</if>
            <if test="measure != null">measure = #{measure},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="effective != null">effective = #{effective},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="itemNum != null">item_num = #{itemNum},</if>
            <if test="auditCompletionTime != null">audit_completion_time = #{auditCompletionTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRewardPunishmentById" parameterType="Long">
        update kq_reward_punishment
        set is_delete = 1
        where id = #{id}
    </delete>

    <delete id="deleteRewardPunishmentByIds" parameterType="String">
        delete from kq_reward_punishment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCountByCreateTimeOfReward" resultType="int" parameterType="String">
        SELECT COUNT(id) FROM kq_reward_punishment
        <where>
            type = '1'
            <if test="createTime != null  and createTime != ''"> and create_time like concat('%', #{createTime}, '%')</if>
        </where>
    </select>

    <select id="getCountByCreateTimeOfPunishment" resultType="int" parameterType="String">
        SELECT COUNT(id) FROM kq_reward_punishment
        <where>
            type = '2'
            <if test="createTime != null  and createTime != ''"> and create_time like concat('%', #{createTime}, '%')</if>
        </where>
    </select>

    <select id="selectKqFileList" parameterType="KqFile" resultMap="KqFileResult">
        select id, correlation_id, file_type, file_name, file_url, file_state, create_by, create_time, update_by, update_time from kq_file
             where correlation_id = #{id}
            and file_type = '1' and file_state = '1'
    </select>

    <update id="voidRewardPunishment" parameterType="RewardPunishment">
        update kq_reward_punishment
        set    effective = 2,
               void_reason = #{voidReason},
               void_time = #{voidTime},
               update_time = #{updateTime}
        where  id = #{id}
    </update>

    <update id="processRewardPunishment" parameterType="RewardPunishmentVo">
        update kq_reward_punishment
        set    status = #{status},
               process_id = #{processId}
        where  id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="passRewardPunishment" parameterType="RewardPunishmentVo">
        update kq_reward_punishment
        set    status = '3',
               audit_completion_time = #{auditCompletionTime}
        where  id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="unpassRewardPunishment" parameterType="RewardPunishmentVo">
        update kq_reward_punishment
        set    status = '4',
               audit_completion_time = #{auditCompletionTime}
        where  id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectRewardPunishmentByHandleId" parameterType="Long" resultMap="RewardPunishmentResult">
        select
            krp.id, krp.user_id, krp.reward_punishment_code, krp.type, krp.measure, krp.amount, krp.reason, krp.remark
            , krp.status, krp.effective, krp.item_name, krp.item_num, krp.audit_completion_time, krp.is_delete
            , krp.process_id, krp.remark, krp.create_by, krp.create_time, krp.update_by, krp.update_time ,cuser.nick_name as create_nick_name,
            kvh.refuse_reason ,kvh.handle_state,cuser.user_id as create_by_user_id,user.nick_name as nick_name
        from kq_reward_punishment krp
        left join sys_user cuser on krp.create_by = cuser.user_name
        left join kq_void_handle kvh  on kvh.correlation_id = krp.id
        left join sys_user user on user.user_id = krp.user_id
        where kvh.type = 3 and  kvh.id = #{id}
        group by krp.id
    </select>
</mapper>
