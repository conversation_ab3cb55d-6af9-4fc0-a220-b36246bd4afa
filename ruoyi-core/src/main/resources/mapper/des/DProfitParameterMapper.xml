<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.mapper.DProfitParameterMapper">

    <resultMap type="DProfitParameter" id="DProfitParameterResult">
        <result property="id"    column="id"    />
        <result property="platformNo"    column="platform_no"    />
        <result property="partnerNo"    column="partner_no"    />
        <result property="fundNo"    column="fund_no"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="expiryDate"    column="expiry_date"    />


        <result property="monthAisleRate"    column="month_aisle_rate"    />
        <result property="flowCost"    column="flow_cost"    />
        <result property="flowCostRate"    column="flow_cost_rate"    />
        <result property="badDebtWay"    column="bad_debt_way"    />

        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBr"    column="create_br"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBr"    column="update_br"    />
        <result property="flowCalculationMode"    column="flow_calculation_mode"    />
        <result property="monthOperationCost"    column="month_operation_cost"    />
    </resultMap>

    <sql id="selectDProfitParameterVo">
        select id, platform_no, partner_no, fund_no, effective_date, expiry_date, month_aisle_rate, flow_cost, flow_cost_rate, bad_debt_way, status, create_time, create_br, update_time, update_br, flow_calculation_mode, month_operation_cost from d_profit_parameter
    </sql>

    <select id="selectDProfitParameterList" parameterType="DProfitParameter" resultMap="DProfitParameterResult">
        <include refid="selectDProfitParameterVo"/>
        <where>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="partnerNo != null  and partnerNo != ''"> and partner_no = #{partnerNo}</if>
            <if test="fundNo != null  and fundNo != ''"> and fund_no = #{fundNo}</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="expiryDate != null "> and expiry_date = #{expiryDate}</if>
            <if test="monthAisleRate != null "> and month_aisle_rate = #{monthAisleRate}</if>
            <if test="flowCost != null "> and flow_cost = #{flowCost}</if>
            <if test="badDebtWay != null "> and bad_debt_way = #{badDebtWay}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
        </where>
    </select>

    <select id="selectDProfitParameterById" parameterType="Long" resultMap="DProfitParameterResult">
        <include refid="selectDProfitParameterVo"/>
        where id = #{id}
    </select>

    <insert id="insertDProfitParameter" parameterType="DProfitParameter" useGeneratedKeys="true" keyProperty="id">
        insert into d_profit_parameter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platformNo != null">platform_no,</if>
            <if test="partnerNo != null">partner_no,</if>
            <if test="fundNo != null">fund_no,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="monthAisleRate != null">month_aisle_rate,</if>
            <if test="flowCalculationMode != null and flowCalculationMode != ''">flow_calculation_mode,</if>
            <if test="flowCost != null">flow_cost,</if>
            <if test="flowCostRate != null">flow_cost_rate,</if>
            <if test="badDebtWay != null">bad_debt_way,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBr != null">create_br,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBr != null">update_br,</if>
            <if test="monthOperationCost != null">month_operation_cost,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platformNo != null">#{platformNo},</if>
            <if test="partnerNo != null">#{partnerNo},</if>
            <if test="fundNo != null">#{fundNo},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="monthAisleRate != null">#{monthAisleRate},</if>
            <if test="flowCalculationMode != null and flowCalculationMode != ''">#{flowCalculationMode,jdbcType=VARCHAR},</if>
            <if test="flowCost != null">#{flowCost},</if>
            <if test="flowCostRate != null">#{flowCostRate,jdbcType=DECIMAL},</if>
            <if test="badDebtWay != null">#{badDebtWay},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBr != null">#{createBr},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBr != null">#{updateBr},</if>
            <if test="monthOperationCost != null">#{monthOperationCost,jdbcType=DECIMAL},</if>
         </trim>
    </insert>

    <update id="updateDProfitParameter" parameterType="DProfitParameter">
        update d_profit_parameter
        <trim prefix="SET" suffixOverrides=",">
            <if test="platformNo != null">platform_no = #{platformNo},</if>
            <if test="partnerNo != null">partner_no = #{partnerNo},</if>
            <if test="fundNo != null">fund_no = #{fundNo},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="expiryDate != null">expiry_date = #{expiryDate},</if>
            <if test="monthAisleRate != null">month_aisle_rate = #{monthAisleRate},</if>
            <if test="flowCalculationMode != null and flowCalculationMode != ''">flow_calculation_mode = #{flowCalculationMode,jdbcType=VARCHAR},</if>
            flow_cost = #{flowCost},
            flow_cost_rate = #{flowCostRate},
            <if test="badDebtWay != null">bad_debt_way = #{badDebtWay},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBr != null">create_br = #{createBr},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBr != null">update_br = #{updateBr},</if>
            <if test="monthOperationCost != null">month_operation_cost = #{monthOperationCost,jdbcType=DECIMAL},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDProfitParameterById" parameterType="Long">
        delete from d_profit_parameter where id = #{id}
    </delete>

    <delete id="deleteDProfitParameterByIds" parameterType="String">
        delete from d_profit_parameter where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getVerifyData" resultMap="DProfitParameterResult">
        select * from  d_profit_parameter where status = '0'
        and platform_no = #{platformNo}
        and partner_no = #{partnerNo}
        and fund_no = #{fundNo}
        <if test="id != null"> and id != #{id}</if>
    </select>

    <select id="getProfitParamByLastMonth" resultMap="DProfitParameterResult">
        select * from  d_profit_parameter where status = '0' and #{lastMonth} >= effective_date and #{lastMonth} &lt;= expiry_date
    </select>
    <select id="getNearVerifyData" resultMap="DProfitParameterResult">
        select * from  d_profit_parameter where status = '0'
        and platform_no = #{platformNo}
        and partner_no = #{partnerNo}
        and fund_no = #{fundNo}
        ORDER BY update_time desc
    </select>

    <select id="selectDProfitParameter" resultMap="DProfitParameterResult">
        select id, platform_no, partner_no, fund_no, CONCAT(substring(effective_date,1,4),'年',substring(effective_date,6,2),'月') AS effective_date, CONCAT(substring(expiry_date,1,4),'年',substring(expiry_date,6,2),'月') AS expiry_date
        , month_aisle_rate, flow_cost, flow_cost_rate, bad_debt_way, status
        , create_time, create_br, update_time, update_br, flow_calculation_mode, month_operation_cost from d_profit_parameter
        <where>
            status = '0'
            <if test="platformNos != null  ">
                and platform_no in
                <foreach collection="platformNos" item="platformNo" open="(" close=")" separator=",">
                    #{platformNo}
                </foreach>
            </if>
            <if test="partnerNos != null">
                and partner_no in
                <foreach collection="partnerNos" item="partnerNo" open="(" close=")" separator=",">
                    #{partnerNo}
                </foreach>
            </if>
            <if test="fundNos != null">
                and fund_no in
                <foreach collection="fundNos" item="fundNo" open="(" close=")" separator=",">
                    #{fundNo}
                </foreach>
            </if>
            <if test="dProfitParameter.moreSearchMap != null and !dProfitParameter.moreSearchMap.isEmpty()">
                <foreach collection="dProfitParameter.moreSearchMap.entrySet()" index="key" item="values">
                    AND ${key}_no IN
                    <foreach collection="values" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                </foreach>
            </if>
            <!-- 数据范围过滤 -->
            ${dProfitParameter.params.dataScope}
        </where>
    </select>

    <select id="selectDProfitParameterByPlatformNoAndPartnerNoAndFundNo" resultMap="DProfitParameterResult">
        <include refid="selectDProfitParameterVo"/>
        <where>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="partnerNo != null  and partnerNo != ''"> and partner_no = #{partnerNo}</if>
            <if test="fundNo != null  and fundNo != ''"> and fund_no = #{fundNo}</if>
            AND status='0'
        </where>
    </select>
</mapper>
