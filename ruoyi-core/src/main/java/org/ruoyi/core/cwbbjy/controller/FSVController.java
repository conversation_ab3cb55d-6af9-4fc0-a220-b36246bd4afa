package org.ruoyi.core.cwbbjy.controller;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictTypeData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysDictTypeService;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.cwbbjy.domain.FinancialStatementVerify;
import org.ruoyi.core.cwbbjy.domain.FinancialStatementVerifyRecord;
import org.ruoyi.core.cwbbjy.domain.LsMap;
import org.ruoyi.core.cwbbjy.service.IFinancialStatementVerifyService;
import org.ruoyi.core.cwbbjy.utils.ExcelCheck;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 财务报校验Controller
 *
 * <AUTHOR>
 * @date 2023-04-27
 */
@RestController
@RequestMapping("/core/cwbbjy")
public class FSVController extends BaseController {
    private final String guaranteeNameV = "担保公司";
    @Value("${urule.url}")
    private  String uruleXMURl;
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private IFinancialStatementVerifyService financialStatementVerifyService;

    /**
     * 查询财务报校验列表
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinancialStatementVerify financialStatementVerify) {
        startPage();
        List<FinancialStatementVerify> list = financialStatementVerifyService.selectFinancialStatementVerifyList(financialStatementVerify,getUserId());
        return getDataTable(list);
    }

    /**
     * 导出财务报校验列表
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:export')")
    @Log(title = "财务报校验", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancialStatementVerify financialStatementVerify) {
        List<FinancialStatementVerify> list = financialStatementVerifyService.selectFinancialStatementVerifyList(financialStatementVerify,getUserId());
        ExcelUtil<FinancialStatementVerify> util = new ExcelUtil<FinancialStatementVerify>(FinancialStatementVerify.class);
        util.exportExcel(response, list, "财务报校验数据");
    }

    /**
     * 获取财务报校验详细信息
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(financialStatementVerifyService.selectFinancialStatementVerifyById(id));
    }

    /**
     * 新增财务报校验
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:add')")
    @Log(title = "财务报校验", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinancialStatementVerify financialStatementVerify) {
        List<FinancialStatementVerify> financialStatementVerifyList = financialStatementVerifyService.checkFinancialStatementVerifyUnique(financialStatementVerify);
        if (!CollectionUtils.isEmpty(financialStatementVerifyList)) {
            return AjaxResult.error("新增失败，当前担保公司，报表类型，报表名称已存在");
        }
        //查询外部系统编码
        if (StringUtils.isNotEmpty(financialStatementVerify.getGuaranteeCompanyId())) {
            SysDictTypeData label2 = getLabel(guaranteeNameV, financialStatementVerify.getGuaranteeCompanyId());
            if (label2 != null && StringUtils.isNotEmpty(label2.getDictName())) {
                financialStatementVerify.setGuaranteeCompanyName(label2.getDictLabel());
            }
        }
        financialStatementVerify.setCreateBy(getUsername());
        financialStatementVerify.setCreateTime(new Date());
        financialStatementVerify.setUpdateTime(new Date());
        return toAjax(financialStatementVerifyService.insertFinancialStatementVerify(financialStatementVerify));
    }

    /**
     * 修改财务报校验
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:edit')")
    @Log(title = "财务报校验", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinancialStatementVerify financialStatementVerify) {
        List<FinancialStatementVerify> financialStatementVerifyList = financialStatementVerifyService.checkFinancialStatementVerifyUnique(financialStatementVerify);
        if (!CollectionUtils.isEmpty(financialStatementVerifyList)) {
            return AjaxResult.error("修改失败，当前担保公司，报表类型，报表名称已存在");
        }
        //查询外部系统编码
        if (StringUtils.isNotEmpty(financialStatementVerify.getGuaranteeCompanyId())) {
            SysDictTypeData label2 = getLabel(guaranteeNameV, financialStatementVerify.getGuaranteeCompanyId());
            if (label2 != null && StringUtils.isNotEmpty(label2.getDictName())) {
                financialStatementVerify.setGuaranteeCompanyName(label2.getDictLabel());
            }
        }
        financialStatementVerify.setUpdateBy(getUsername());
        financialStatementVerify.setUpdateTime(new Date());
        return toAjax(financialStatementVerifyService.updateFinancialStatementVerify(financialStatementVerify));
    }

    /**
     * 删除财务报校验
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:remove')")
    @Log(title = "财务报校验", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(financialStatementVerifyService.deleteFinancialStatementVerifyByIds(ids));
    }

    @PostMapping("/importData")
    @Log(title = "财务报校验", businessType = BusinessType.IMPORT)
    public AjaxResult verifyData(MultipartFile file, Long id) throws Exception {
        if (id==null) {
            return AjaxResult.error("请选择报表名称");
        }
        FinancialStatementVerify f = financialStatementVerifyService.selectFinancialStatementVerifyById(id);
        if (f!=null &&  !CollectionUtils.isEmpty(f.getRuleList()) && f.getRuleList().size()>0){
        }else {
            return AjaxResult.error("未找到报表或报表未配置规则");
        }
        FinancialStatementVerifyRecord info =new FinancialStatementVerifyRecord();
        String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.BBJY_SYSTEM, file);
        info.setFileName(file.getOriginalFilename());
        info.setUrl(url);
        Map<String, String> resultMap= ExcelCheck.doExcelVerify(file.getInputStream(), f.getRuleList());
        List<LsMap> objectsmap = new ArrayList<>();
        for (Map.Entry<String, String> entry : resultMap.entrySet()) {
            LsMap lsMap = new LsMap();
            lsMap.setKey(entry.getKey());
            lsMap.setValue(entry.getValue().toString());
            objectsmap.add(lsMap);
        }
        info.setCustNo(f.getGuaranteeCompanyId());
        info.setReportType(f.getReportType());
        info.setReportFormsName(f.getReportFormsName());
        info.setResult(JSON.toJSONString(objectsmap));
        info.setCreateBy(getUsername());
        info.setCreateTime(new Date());
        info.setUpdateBy(getUsername());
        info.setUpdateTime(new Date());
        financialStatementVerifyService.insertFinancialStatementVerifyRecord(info);
        return    AjaxResult.success(objectsmap);
    }


    /**
     * 查询标签
     *
     * @param assetNameV
     * @param assetName
     * @return
     */

    public SysDictTypeData getLabel(String assetNameV, String assetName) {
        SysDictTypeData dictType = new SysDictTypeData();
        dictType.setDictName(assetNameV);
        /*   dictType.setDictLabel(assetName);*/
        dictType.setDictValue(assetName);
        dictType.setStatus("0");
        dictType.setStatusd("0");
        SysDictTypeData assetNameLabel = dictTypeService.selectDictDictValuecd(dictType);
        if (assetNameLabel == null) {
            return null;
        } else {
            return assetNameLabel;
        }
    }
    
    @GetMapping("/custList")
    public AjaxResult custList() {
        List<FinancialStatementVerify> list = financialStatementVerifyService.selectCustList(getUserId());
        return AjaxResult.success(list);
    }
    @GetMapping("/reportTypeList")
    public AjaxResult reportTypeList(FinancialStatementVerify financialStatementVerify) {
    	financialStatementVerify.setStatus("0");
        List<FinancialStatementVerify> list = financialStatementVerifyService.selectReportTypeList(financialStatementVerify,getUserId());
        return AjaxResult.success(list);
    }
    @GetMapping("/reportNameList")
    public AjaxResult reportNameList(FinancialStatementVerify financialStatementVerify) {
    	financialStatementVerify.setStatus("0");
        List<FinancialStatementVerify> list = financialStatementVerifyService.selectReportNameList(financialStatementVerify,getUserId());
        return AjaxResult.success(list);
    }
    
    
    /**
     * 查询校验记录列表
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:record:list')")
    @GetMapping("/record/list")
    public TableDataInfo getRecordList(FinancialStatementVerifyRecord info) {
        startPage();
        List<FinancialStatementVerifyRecord> list = financialStatementVerifyService.selectFinancialStatementVerifyRecordList(info,getUserId());
        return getDataTable(list);
    }

    /**
     * 查询校验记录
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:record:view')")
    @GetMapping(value = "/record/{id}")
    public AjaxResult selectFinancialStatementVerifyRecordById(@PathVariable("id") Long id) {
        return AjaxResult.success(financialStatementVerifyService.selectFinancialStatementVerifyRecordById(id));
    }
    
    /**
     * 获取权限列表
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:permission:list')")
    @GetMapping(value = "/permission/{custNo}")
    public AjaxResult getCustPermission(@PathVariable("custNo") String custNo) {
        return AjaxResult.success(financialStatementVerifyService.selectFinancialStatementVerifyPermissionById(custNo));
    }
    
    /**
     * 更新权限
     */
    @PreAuthorize("@ss.hasPermi('core:cwbbjy:permission:edit')")
    @Log(title = "财务报校验权限", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/permission")
    public AjaxResult editCustPermission(@RequestBody FinancialStatementVerify info) {
        return AjaxResult.success(financialStatementVerifyService.updateFinancialStatementVerifyPermission(info));
    }

}


