package org.ruoyi.core.dm.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.dm.domain.DmAqaFpdAnalysis;
import org.ruoyi.core.dm.domain.vo.DmAqaFpdAnalysisVo;
import org.ruoyi.core.dm.service.IDmAqaFpdAnalysisService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产品首逾率Controller
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@RestController
@RequestMapping("/fpd/analysis")
public class DmAqaFpdAnalysisController extends BaseController
{
    @Autowired
    private IDmAqaFpdAnalysisService dmAqaFpdAnalysisService;

    /**
     * 查询产品首逾率列表
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody DmAqaFpdAnalysisVo dmAqaFpdAnalysis)
    {
        List<DmAqaFpdAnalysisVo> list = dmAqaFpdAnalysisService.selectDmAqaFpdAnalysisList(dmAqaFpdAnalysis);
        return getDataTable(list);
    }

    /**
     * 导出产品首逾率列表
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:export')")
    @Log(title = "产品首逾率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DmAqaFpdAnalysisVo dmAqaFpdAnalysis)
    {
        Gson gson = new Gson();
        Map<String,String> resultMap = gson.fromJson(dmAqaFpdAnalysis.getSortMapString(), Map.class);
        dmAqaFpdAnalysis.setSortMap(resultMap);
        List<DmAqaFpdAnalysisVo> list = dmAqaFpdAnalysisService.selectDmAqaFpdAnalysisList(dmAqaFpdAnalysis);
        ExcelUtil<DmAqaFpdAnalysisVo> util = new ExcelUtil<DmAqaFpdAnalysisVo>(DmAqaFpdAnalysisVo.class);
        util.exportExcel(response, list, "产品首逾率数据");
    }

    /**
     * 获取产品首逾率详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(dmAqaFpdAnalysisService.selectDmAqaFpdAnalysisById(id));
    }

    /**
     * 新增产品首逾率
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:add')")
    @Log(title = "产品首逾率", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DmAqaFpdAnalysis dmAqaFpdAnalysis)
    {
        return toAjax(dmAqaFpdAnalysisService.insertDmAqaFpdAnalysis(dmAqaFpdAnalysis));
    }

    /**
     * 修改产品首逾率
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:edit')")
    @Log(title = "产品首逾率", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DmAqaFpdAnalysis dmAqaFpdAnalysis)
    {
        return toAjax(dmAqaFpdAnalysisService.updateDmAqaFpdAnalysis(dmAqaFpdAnalysis));
    }

    /**
     * 删除产品首逾率
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:remove')")
    @Log(title = "产品首逾率", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dmAqaFpdAnalysisService.deleteDmAqaFpdAnalysisByIds(ids));
    }
}
