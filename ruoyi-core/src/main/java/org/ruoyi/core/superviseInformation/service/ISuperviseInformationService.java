package org.ruoyi.core.superviseInformation.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUnit;
import org.ruoyi.core.superviseInformation.domain.SuperviseInformation;
import org.ruoyi.core.superviseInformation.domain.SuperviseInformationReleaseStatistics;
import org.ruoyi.core.superviseInformation.domain.vo.BArchivist;
import org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationVO;

import java.text.ParseException;
import java.util.List;


public interface ISuperviseInformationService
{
    /**
     * 查询资料
     *
     * @param id 资料主键
     * @return 资料
     *
     */
    SuperviseInformation selectInformationById(Long id);

    /**
     * 查询资料列表
     *
     * @param information 资料
     * @return 资料集合
     */
    List<SuperviseInformationVO> selectInformationList(SuperviseInformationVO information);

    /**
     * 新增资料
     *
     * @param information 资料
     * @return 结果
     */
    public int insertInformation(SuperviseInformation information);

    AjaxResult insertCommitInformation(SuperviseInformationVO information);

    /**
     * 新增临时资料
     *
     * @param information 资料
     * @return 结果
     */
    AjaxResult insertTemporaryInformation(SuperviseInformation information);

    /**
     * 修改资料
     *
     * @param information 资料
     * @return 结果
     */
    public int updateInformation(SuperviseInformation information);

    /**
     * 修改资料
     *
     * @param information 资料
     * @return 结果
     */
    public int arrangeInformation(SuperviseInformationVO information);

    /**
     * 批量删除资料
     *
     * @param ids 需要删除的资料主键集合
     * @return 结果
     */
    public int deleteInformationByIds(Long[] ids);

    /**
     * 删除资料信息
     *
     * @param id 资料主键
     * @return 结果
     */
    public int deleteInformationById(Long id);

    public int getCountByCatalogueId(Long[] catalogueIds);

    /**
     * 查询数量
     */
    int getCountByCreateTime(String createTime);

    int getTemporaryCountByCreateTime(String createTime);

    public List<SuperviseInformationVO> exportInformationList(SuperviseInformationVO information);

    public List<SuperviseInformationVO> exportAllInformationList(SuperviseInformationVO information);

    public int commitInformationByIds(Long[] ids);

    /**
     * 根据ids获取已提交的列表
     * @param ids
     * @return
     */
    List<SuperviseInformation> getSubmitList(Long[] ids);

    int passInformationByIds(Long[] ids);

    int unPassInformationByIds(Long[] ids);

    public int abandonedInformationByIds(Long[] ids);

    public int unabandonedInformationByIds(Long[] ids);

    /**
     *
     */
    List<SuperviseInformation> getAuditPassList(Long[] ids);

    //List<SuperviseInformation> getDownloadList();

    public int empowerInformationByIds(Long[] ids);

    public List<SuperviseInformation> getUnPassList(Long[] ids);

    List<SuperviseInformationVO> selectAllInformationList(SuperviseInformationVO information);

    public List<SuperviseInformationVO> selectInformationAbandonedList(SuperviseInformationVO information);

    List<SuperviseInformation> getExpireList(SuperviseInformation information);

    List<SuperviseInformation> getListByIds(Long[] ids);

    public int isAuthority(Long catalogueId);

    List<SuperviseInformationReleaseStatistics> getReleaseStatistics(SuperviseInformationReleaseStatistics information);

    List<SuperviseInformationReleaseStatistics> exportReleaseStatistics(SuperviseInformationReleaseStatistics information);

    List<SysUnit> getUnit();

    public AjaxResult commitCheck(Long[] ids);

    public List<SuperviseInformationVO> getinformationStatisticsList(SuperviseInformationVO information);

    public int addBContract(BArchivist bArchivist) throws ParseException;
}
