package org.ruoyi.core.superviseInformation.domain.vo;

import lombok.Data;
import org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue;

import java.util.List;

/**
 * 资料目录对象 zl_information_catalogue
 *
 * <AUTHOR>
 * @date 2023-11-10
 *
 */
@Data
public class SuperviseInformationCatalogueVO extends SuperviseInformationCatalogue
{

    private String orgName;

    private String deptName;
    /** 授权部门 */
    private Long auDeptId;

    /** 授权人 */
    private Long auUserId;

    /** 岗位 */
    private List<Long> auPostIds;

    /** 上级目录名称*/
    private String parentName;

    private Long parentOrgId;

    /** 子集 */
    List<SuperviseInformationCatalogueVO> informationCatalogueVOList;

    /**
     * 合作公司名称
     */
    private String cooperationCompanyName;

    /**
     * 合作项目名称
     */
    private String cooperationProjectName;
    /** 授权公司*/
    private List<Long> auCompanyIds;

    /** 合作项目*/
    private List<Long> auProjectIds;
    /** 合作项目*/
    private List<Long> auIds;
}
