package org.ruoyi.core.spycx.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.spycx.domain.YspProcessTemplate;
import org.ruoyi.core.spycx.domain.YspQueryParams;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IYspProcessTemplateService.java
 * @Description TODO
 * @createTime 2024年10月15日 10:12:00
 */
public interface IYspProcessTemplateService {


    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public YspProcessTemplate selectYspProcessTemplateById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param yspProcessTemplate 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<YspProcessTemplate> selectYspProcessTemplateList(YspProcessTemplate yspProcessTemplate);

    /**
     * 新增【请填写功能名称】
     *
     * @param yspProcessTemplate 【请填写功能名称】
     * @return 结果
     */
    public int insertYspProcessTemplate(YspProcessTemplate yspProcessTemplate);

    /**
     * 修改【请填写功能名称】
     *
     * @param yspProcessTemplate 【请填写功能名称】
     * @return 结果
     */
    public int updateYspProcessTemplate(YspProcessTemplate yspProcessTemplate);
    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteYspProcessTemplateByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteYspProcessTemplateById(Long id);


    Map<String,Object>  queryYSPProcessList(YspQueryParams yspQueryParams, LoginUser loginUser);

}
