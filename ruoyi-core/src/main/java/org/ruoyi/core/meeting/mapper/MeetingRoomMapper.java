package org.ruoyi.core.meeting.mapper;

import org.ruoyi.core.meeting.domain.MeetingRoom;
import org.ruoyi.core.meeting.domain.vo.MeetingRoomVo;

import java.util.List;

/**
 * 会议室管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-24
 */
public interface MeetingRoomMapper
{
    /**
     * 查询会议室管理
     *
     * @param id 会议室管理主键
     * @return 会议室管理
     */
    public MeetingRoomVo selectMeetingRoomById(Long id);

    /**
     * 查询会议室管理列表
     *
     * @param meetingRoom 会议室管理
     * @return 会议室管理集合
     */
    public List<MeetingRoomVo> selectMeetingRoomList(MeetingRoomVo meetingRoom);

    /**
     * 新增会议室管理
     *
     * @param meetingRoom 会议室管理
     * @return 结果
     */
    public int insertMeetingRoom(MeetingRoom meetingRoom);

    /**
     * 修改会议室管理
     *
     * @param meetingRoom 会议室管理
     * @return 结果
     */
    public int updateMeetingRoom(MeetingRoom meetingRoom);

    /**
     * 删除会议室管理
     *
     * @param id 会议室管理主键
     * @return 结果
     */
    public int deleteMeetingRoomById(Long id);

    /**
     * 批量删除会议室管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMeetingRoomByIds(Long[] ids);

    int getCount();
}
