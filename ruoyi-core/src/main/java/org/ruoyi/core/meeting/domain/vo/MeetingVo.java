package org.ruoyi.core.meeting.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import lombok.Data;
import org.apache.catalina.User;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.ruoyi.core.meeting.domain.Meeting;
import org.ruoyi.core.meeting.domain.MeetingFile;

import java.util.Date;
import java.util.List;

/**
 * 会议管理对象 hy_meeting
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
@Data
public class MeetingVo extends Meeting
{
    /**
     * 会议室名称
     */
    @Excel(name = "会议室")
    private String meetingRoomName;

    /**
     * 会议室名称
     */
    private String meetingRoomId;

    @Excel(name = "会议组织人")
    private String organizationalUserName;

    private List<Long> organizationalDeptIds;

    @Excel(name = "组织部门")
    private String organizationalDeptString;

    private List<SysDept> depts;

    private List<Long> attendUserIds;
    /**
     * 表示时:分;
     */
    private String hourMin;
    /**
     * 日历(月)表条件
     */
    private String monthCondition;
    /**
     * 参与状态 1.我组织的  2.我参加的  3.我组织的 + 我参加的
     */
    private String attendState;

    /** 会议开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date meetingStartDay;
    /**
     * 开始时间-条件
     */
    private Date startTime;
    /**
     * 结束时间-条件
     */
    private Date endTime;
    /**
     * 会议提醒方式ids
     */
    private List<Long> meetingReminderWayIds;
    /**
     * 通知详情
     */
    private String notificationStatus;
    /**
     * 会议提醒 待处理人
     */
    private Long disposeUser;

    private List<SysUser> attendUserList;
    /**
     * 时间段合集  用于会议室时间重叠
     */
    private List<String> dates;

    @JSONField(serialize = false, deserialize = false)
    private List<Long> fileIds;

    private List<MeetingFile> files;

    private Long meetingId;
    /**
     * 会议室地点
     */
    private String meetingRoomLocation;

    /**
     * 会议室地点
     */
    private String notifyType;

    /**
     * 创建人名称
     */
    private String createByName;
}
