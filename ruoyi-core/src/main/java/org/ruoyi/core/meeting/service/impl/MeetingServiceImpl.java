package org.ruoyi.core.meeting.service.impl;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.meeting.domain.Meeting;
import org.ruoyi.core.meeting.domain.MeetingFile;
import org.ruoyi.core.meeting.domain.vo.MeetingFileVo;
import org.ruoyi.core.meeting.domain.vo.MeetingVo;
import org.ruoyi.core.meeting.mapper.MeetingMapper;
import org.ruoyi.core.meeting.service.IMeetingFileService;
import org.ruoyi.core.meeting.service.IMeetingNotifyService;
import org.ruoyi.core.meeting.service.IMeetingService;
import org.ruoyi.core.personnel.domain.vo.PersonnelFileVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 会议管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
@Service
public class MeetingServiceImpl implements IMeetingService
{
    @Autowired
    private MeetingMapper meetingMapper;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private IMeetingNotifyService meetingNotifyService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IMeetingFileService meetingFileService;
    /**
     * 查询会议管理
     *
     * @param id 会议管理主键
     * @return 会议管理
     */
    @Override
    public MeetingVo selectMeetingById(Long id)
    {
        MeetingVo meetingVo = meetingMapper.selectMeetingById(id);
        if (meetingVo.getOrganizationalDeptIds() != null && !meetingVo.getOrganizationalDeptIds().isEmpty()) {
            List<SysDept> sysDepts = sysDeptService.selectDeptByArrayIds(meetingVo.getOrganizationalDeptIds().stream().mapToLong(Long::longValue).toArray());
            meetingVo.setDepts(sysDepts);
        }
        if(meetingVo.getAttendUserIds() != null && !meetingVo.getAttendUserIds().isEmpty()){
            List<SysUser> sysUsers = sysUserService.selectUserByUserIds(meetingVo.getAttendUserIds());
            meetingVo.setAttendUserList(sysUsers);
        }

        MeetingFile forFile = new MeetingFile();
        forFile.setCorrelationId(meetingVo.getId());
        forFile.setFileState("1");
        forFile.setFileType("2");
        List<MeetingFile> forFiles = meetingFileService.selectMeetingFileList(forFile);
        meetingVo.setFiles(forFiles);

        return meetingVo;
    }

    @Override
    public MeetingVo selectMeetingByProcessId(String processId)
    {
        MeetingVo meetingVo = meetingMapper.selectMeetingByProcessId(processId);
        if (meetingVo.getOrganizationalDeptIds() != null && !meetingVo.getOrganizationalDeptIds().isEmpty()) {
            List<SysDept> sysDepts = sysDeptService.selectDeptByArrayIds(meetingVo.getOrganizationalDeptIds().stream().mapToLong(Long::longValue).toArray());
            meetingVo.setDepts(sysDepts);
        }
        if(meetingVo.getAttendUserIds() != null && !meetingVo.getAttendUserIds().isEmpty()){
            List<SysUser> sysUsers = sysUserService.selectUserByUserIds(meetingVo.getAttendUserIds());
            meetingVo.setAttendUserList(sysUsers);
        }

        MeetingFile forFile = new MeetingFile();
        forFile.setCorrelationId(meetingVo.getId());
        forFile.setFileState("1");
        forFile.setFileType("2");
        List<MeetingFile> forFiles = meetingFileService.selectMeetingFileList(forFile);
        meetingVo.setFiles(forFiles);

        return meetingVo;
    }

    /**
     * 查询会议管理列表
     *
     * @param meeting 会议管理
     * @return 会议管理
     */
    @Override
    public List<MeetingVo> selectMeetingList(MeetingVo meeting)
    {
        List<MeetingVo> meetingVos = meetingMapper.selectMeetingList(meeting);
        if (meetingVos.isEmpty()) {
            return meetingVos;
        }
        // 合并所有 MeetingVo 中的 deptIds
        List<Long> allDeptIds = meetingVos.stream()
                .flatMap(vo -> vo.getOrganizationalDeptIds().stream())
                .collect(Collectors.toList());
        long[] deptIdsArray = allDeptIds.stream().mapToLong(Long::longValue).toArray();
        if (deptIdsArray.length > 0){
            List<SysDept> sysDepts = sysDeptService.selectDeptByArrayIds(deptIdsArray);
            Map<Long, SysDept> sysDeptMap = sysDepts.stream()
                    .collect(Collectors.toMap(SysDept::getDeptId, sysDept -> sysDept));
            meetingVos.forEach(vo -> {
                if (vo.getOrganizationalDeptIds() != null && !vo.getOrganizationalDeptIds().isEmpty()) {
                List<SysDept> sysDepts1 = vo.getOrganizationalDeptIds().stream()
                        .filter(sysDeptMap::containsKey)
                        .map(sysDeptMap::get)
                        .collect(Collectors.toList());
                vo.setDepts(sysDepts1);
                }
            });
        }
        meetingVos.forEach(vo -> {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
            String timeString = sdf.format(vo.getMeetingStartTime());
            vo.setHourMin(timeString);

            // 获取当前时间
            Date currentDate = new Date();  // 当前系统时间
            if (!"3".equals(vo.getMeetingState())){
                if (currentDate.before(vo.getMeetingStartTime())) {
                    vo.setMeetingState("2");
                } else if (currentDate.after(vo.getMeetingEndTime())) {
                    vo.setMeetingState("5");
                } else {
                    vo.setMeetingState("4");
                }
            }

        });

        return meetingVos;
    }

    /**
     * 查询会议管理列表
     *
     * @param meeting 会议管理
     * @return 会议管理
     */
    @Override
    public List<MeetingVo> selectMeetingListNotification(MeetingVo meeting)
    {
        meeting.setDisposeUser(getLoginUser().getUser().getUserId());
        meeting.setNotifyType("1");
        List<MeetingVo> meetingVos = meetingMapper.selectMeetingListNotification(meeting);
        if (meetingVos.isEmpty()) {
            return meetingVos;
        }
        // 合并所有 MeetingVo 中的 deptIds
        List<Long> allDeptIds = meetingVos.stream()
                .flatMap(vo -> vo.getOrganizationalDeptIds().stream())
                .collect(Collectors.toList());
        long[] deptIdsArray = allDeptIds.stream().mapToLong(Long::longValue).toArray();
        if (deptIdsArray.length > 0){
            List<SysDept> sysDepts = sysDeptService.selectDeptByArrayIds(deptIdsArray);
            Map<Long, SysDept> sysDeptMap = sysDepts.stream()
                    .collect(Collectors.toMap(SysDept::getDeptId, sysDept -> sysDept));
            meetingVos.forEach(vo -> {
                if (vo.getOrganizationalDeptIds() != null && !vo.getOrganizationalDeptIds().isEmpty()) {
                    List<SysDept> sysDepts1 = vo.getOrganizationalDeptIds().stream()
                            .filter(sysDeptMap::containsKey)
                            .map(sysDeptMap::get)
                            .collect(Collectors.toList());
                    vo.setDepts(sysDepts1);
                }
            });
        }
        meetingVos.forEach(vo -> {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
            String timeString = sdf.format(vo.getMeetingStartTime());
            vo.setHourMin(timeString);

            // 获取当前时间
            Date currentDate = new Date();  // 当前系统时间
            if (!"3".equals(vo.getMeetingState())){
                if (currentDate.before(vo.getMeetingStartTime())) {
                    vo.setMeetingState("2");
                } else if (currentDate.after(vo.getMeetingEndTime())) {
                    vo.setMeetingState("5");
                } else {
                    vo.setMeetingState("4");
                }
            }
        });

        return meetingVos;
    }

    @Override
    public Map<String,Object> selectMeetingMonthList(MeetingVo meeting)
    {
        LoginUser loginUser = getLoginUser();
        Long userId = loginUser.getUserId();
        Map<String, Object> map = new HashMap<>();

        List<Long> userList = Collections.singletonList(userId);
        meeting.setAttendUserIds(userList);
        meeting.setOrganizationalUser(userId);
        List<MeetingVo>  monthList =  meetingMapper.selectMeetingMonthList(meeting);
        // 参加
        int organizationalNum = 0;
        int attend = 0;

        for (MeetingVo vo : monthList) {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
            String timeString = sdf.format(vo.getMeetingStartTime());
            vo.setMeetingTheme(timeString + " " + vo.getMeetingTheme());

            //前段非要汉字表示状态 ^_^
            if (vo.getAttendUserIds().contains(userId)){
                attend++;
                vo.setAttendState("我参加的");
            }
            if (userId.equals(vo.getOrganizationalUser())){
                organizationalNum++;
                vo.setAttendState("我组织的");
            }
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfMonth = now.withDayOfMonth(1).toLocalDate().atStartOfDay();
        LocalDateTime endOfMonth = now.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        LocalDateTime startOfYear = LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear());

        //当前时间
        Date currentDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        //本月开始时间
        Date startOfMonthDate = Date.from(startOfMonth.atZone(ZoneId.systemDefault()).toInstant());
        // 获取本月最后一天的日期
        Date endOfMonthDate = Date.from(endOfMonth.atZone(ZoneId.systemDefault()).toInstant());
        // 获取本年第一天
        Date startOfYearDate = Date.from(startOfYear.atZone(ZoneId.systemDefault()).toInstant());

        long alreadyMonthAttendNum = monthList.stream()
                .filter(vo -> {
                    Date meetingStartTime = vo.getMeetingStartTime();
                    return !meetingStartTime.after(currentDate) && !meetingStartTime.before(startOfMonthDate);
                }).count();

        long soonMonthAttendNum = monthList.stream()
                .filter(vo -> {
                    Date meetingStartTime = vo.getMeetingStartTime();
                    // 判断会议开始时间是否在当前时间和本月最后一天之间
                    return !meetingStartTime.after(endOfMonthDate) && !meetingStartTime.before(currentDate);
                }).count();
        long alreadyYearAttendNum = monthList.stream()
                .filter(vo -> {
                    Date meetingStartTime = vo.getMeetingStartTime();
                    //  在本年第一天和当前时间之间的会议
                    return !meetingStartTime.before(startOfYearDate) && !meetingStartTime.after(currentDate);
                }).count();

        map.put("monthList",monthList);
        //我组织的会议数量
        map.put("organizationalNum",organizationalNum);
        //我参加的会议数量
        map.put("attendNum",attend);
        //本月即将参加的数量
        map.put("soonMonthAttendNum",soonMonthAttendNum);
        //本月已经参加的数量
        map.put("alreadyMonthAttendNum",alreadyMonthAttendNum);
        //本年参加的数量
        map.put("alreadyYearAttendNum",alreadyYearAttendNum);
        return map;
    }

    /**
     * 新增会议管理
     *
     * @param meeting 会议管理
     * @return 结果
     */
    @Override
    public int insertMeeting(MeetingVo meeting)
    {
        //inspectionTime(meeting);
        meeting.setCreateTime(DateUtils.getNowDate());
        LoginUser loginUser = getLoginUser();
        meeting.setCreateBy(loginUser.getUser().getUserName());

        //系统编号逻辑
        int count = getCountByCreateTime(DateUtils.getDate()) + 1;
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        meeting.setMeetingCode("HY" + createTimeNum + String.format("%02d", count));

        meetingMapper.insertMeeting(meeting);

        if (meeting.getFileIds() != null){
            MeetingFileVo meetingFile = new MeetingFileVo();
            meetingFile.setIds(meeting.getFileIds());
            meetingFile.setCorrelationId(meeting.getId());
            meetingFileService.correlationFile(meetingFile);
        }
        return notifyMeeting(meeting.getId());
    }

    /**
     * 修改会议管理
     *
     * @param meeting 会议管理
     * @return 结果
     */
    @Override
    public int updateMeeting(Meeting meeting)
    {
        meeting.setUpdateTime(DateUtils.getNowDate());
        return meetingMapper.updateMeeting(meeting);
    }

    /**
     * 批量删除会议管理
     *
     * @param ids 需要删除的会议管理主键
     * @return 结果
     */
    @Override
    public int deleteMeetingByIds(Long[] ids)
    {
        return meetingMapper.deleteMeetingByIds(ids);
    }

    /**
     * 删除会议管理信息
     *
     * @param id 会议管理主键
     * @return 结果
     */
    @Override
    public int deleteMeetingById(Long id)
    {
        return meetingMapper.deleteMeetingById(id);
    }

    @Override
    public int passMeeting(Long meetingId){
        MeetingVo meeting = selectMeetingById(meetingId);
        meeting.setMeetingState("2");
        return updateMeeting(meeting);
    }

    @Override
    public int unpassMeeting(Long meetingId){
        MeetingVo meeting = selectMeetingById(meetingId);
        meetingNotifyService.deleteMeetingNotifyByCorrelationId(meeting.getId());
        meetingNotifyService.batchAddMeetingNotificationsCancel(meeting);
        meetingNotifyService.batchAddMeetingManagerNotificationsCancel(meeting);
        meeting.setMeetingState("3");
        return updateMeeting(meeting);
    }

    /**
     * 会议通知参会人员
     * @param meetingId
     * @return
     */
    public int notifyMeeting(Long meetingId){
        MeetingVo meeting = selectMeetingById(meetingId);
        meetingNotifyService.batchAddMeetingNotifications(meeting);
        return meetingNotifyService.batchAddMeetingManagerNotifications(meeting);
    }

    public int inspectionTime(MeetingVo meeting){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        List<String> dates = new ArrayList<>();
        dates.add(dateFormat.format(meeting.getMeetingStartTime()));
        dates.add(dateFormat.format(meeting.getMeetingEndTime()));
        meeting.setDates(dates);
        int i = meetingMapper.inspectionTime(meeting);
        if (i > 0){
            throw new ServiceException("所选时间段与已申请会议室申请时间重叠");
        }
        return 1;
    }

    @Override
    public AjaxResult uploadFile(MultipartFile file){
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.MEETING_SYSTEM, file);
            MeetingFile meetingFile = new MeetingFile();
            meetingFile.setFileUrl(url);
            meetingFile.setFileName(name);
            meetingFile.setFileState("0");
            meetingFile.setFileType("2");
            meetingFile.setCreateTime(DateUtils.getNowDate());
            meetingFile.setCreateBy(getUsername());
            meetingFileService.insertMeetingFile(meetingFile);
            return AjaxResult.success(meetingFile);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    public int getCountByCreateTime(String createTime){
        return meetingMapper.getCountByCreateTime(createTime);
    }
}
