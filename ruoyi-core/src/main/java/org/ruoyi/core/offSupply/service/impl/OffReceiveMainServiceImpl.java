package org.ruoyi.core.offSupply.service.impl;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.AuthRoleEnum;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.AuthMain;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.ruoyi.core.offSupply.domain.*;
import org.ruoyi.core.offSupply.domain.vo.OffReceiveReport;
import org.ruoyi.core.offSupply.domain.vo.ReceiveReportVo;
import org.ruoyi.core.offSupply.domain.vo.SysComVo;
import org.ruoyi.core.offSupply.exportUtil.Export;
import org.ruoyi.core.offSupply.mapper.OffCategoryMainMapper;
import org.ruoyi.core.offSupply.mapper.OffReceiveMainMapper;
import org.ruoyi.core.offSupply.mapper.OffSupplyFilesMapper;
import org.ruoyi.core.offSupply.mapper.OffSupplyMainMapper;
import org.ruoyi.core.offSupply.service.IOffReceiveMainService;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.service.impl.PersonnelArchivesServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 办公用品领用主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-21
 */
@Service
public class OffReceiveMainServiceImpl implements IOffReceiveMainService 
{
    @Autowired
    private OffReceiveMainMapper offReceiveMainMapper;

    @Autowired
    private OffSupplyFilesMapper offSupplyFilesMapper;

    @Autowired
    private OffSupplyMainMapper offSupplyMainMapper;

    @Autowired
    private OffCategoryMainMapper offCategoryMainMapper;

    @Autowired
    private OffSupplyMainServiceImpl offSupplyMainServiceImpl;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityServiceImpl;

    @Autowired
    private PersonnelArchivesServiceImpl personnelArchivesServiceImpl;

    @Autowired
    private ISysOperLogService sysOperLogService;

    /**
     * 查询办公用品领用
     * 
     * @param id 办公用品领用主主键
     * @return 办公用品领用主
     */
    @Override
    public OffReceiveMain selectOffReceiveMainById(Long id)
    {
        //根据id查询领用主表信息
        OffReceiveMain offReceiveMain = offReceiveMainMapper.selectOffReceiveMainById(id);
        //根据id查询领用明细表信息
        List<OffReceiveDetail> offReceiveDetails = offReceiveMainMapper.selectOffReceiveDetailByReceiveId(id);
        if (CollectionUtils.isNotEmpty(offReceiveDetails)){
            offReceiveMain.setOffReceiveDetailList(offReceiveDetails);
        }
        //办公用品附件
        List<OffSupplyFiles> supplyAttachs = offSupplyFilesMapper.selectOffSupplyFilesByTypeAndRelevancyId("2", id);
        if (CollectionUtils.isNotEmpty(supplyAttachs)){
            offReceiveMain.setOffSupplyFileList(supplyAttachs);
        }
        return offReceiveMain;
    }

    /**
     * 查询办公用品领用列表
     * 
     * @param offReceiveMain 办公用品领用主
     * @return 办公用品领用主
     */
    @Override
    public List<OffReceiveMain> selectOffReceiveMainList(OffReceiveMain offReceiveMain)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();

        //新权限->查询当前登录用户有哪些公司的物品管理员权限
        List<Long> companyIds = queryLoginUserWPGLYAuth(loginUser.getUser().getUserId());
        //查询当前用户有哪些下级
        List<Long> userSubordinateList = queryLooginUserSubordinate(loginUser.getUsername());
        offReceiveMain.setAuthCompanyIds(companyIds);
        offReceiveMain.setUserSubordinateList(userSubordinateList);
        offReceiveMain.setLoginUserId(SecurityUtils.getUserId());
        offReceiveMain.setLoginUserName(SecurityUtils.getUsername());
        if (offReceiveMain.getPageSize() != null && offReceiveMain.getPageNum() != null){
            PageHelper.startPage(offReceiveMain.getPageNum(),offReceiveMain.getPageSize());
        }
        return offReceiveMainMapper.selectOffReceiveMainList(offReceiveMain);
    }

    /**
     * 根据当前登录用户账号查询下级 下下级 下下下级...
     * @param username
     * @return
     */
    private List<Long> queryLooginUserSubordinate(String username) {
        List<Long> userSubordinateList = new ArrayList<>();
        //当前登陆人和登陆人的下级
        PersonnelArchivesVo vos = personnelArchivesServiceImpl.getPersonnelOrganization(username);
        //转换为普通的list集合
        List<PersonnelArchivesVo> perList = personnelArchivesServiceImpl.getPerOrganizationList(vos);
        if (CollectionUtils.isNotEmpty(perList)){
            userSubordinateList = perList.stream().map(PersonnelArchivesVo::getUserId).collect(Collectors.toList());
        }
        return userSubordinateList;
    }

    /**
     * 根据用户id，查询当前用户有哪些公司的物品管理员角色
     * @param userId
     * @return
     */
    private List<Long> queryLoginUserWPGLYAuth(Long userId) {
        List<Long> authCompanyIds = new ArrayList<>();
        List<AuthMain> newAuthForModuleAndRole = offReceiveMainMapper.getNewAuthForModuleAndRole(userId, AuthModuleEnum.OFFSUPPLY.getCode(), AuthRoleEnum.WPGLY1.getCode());
        if (CollectionUtils.isEmpty(newAuthForModuleAndRole)){
            return new ArrayList<>();
        }
        //根据权限主表循环查询权限副表
        for (AuthMain authMain : newAuthForModuleAndRole) {
            List<AuthDetail> detailList = offReceiveMainMapper.selectAuthDetailByMainId(authMain.getId());
            //如果主表有数据，副表为空，则证明是初始化权限，返回所有内部担保公司
            if (CollectionUtils.isEmpty(detailList)){
                return offReceiveMainMapper.selectSysCompanyList(new ReceiveReportVo()).stream().map(SysComVo::getId).collect(Collectors.toList());
            }
        }
        /** 如果主表有数据，副表也有对应的数据，则证明后期人工授权，查询对应的公司id */
        //根据权限主表id集合批量查询对应的副表信息
        List<AuthDetail> detailList = offReceiveMainMapper.selectAuthDetailByMainIdList(newAuthForModuleAndRole);
        if (!CollectionUtils.isEmpty(detailList)){
            authCompanyIds = (List<Long>) detailList.stream().map(AuthDetail::getThirdTableId).collect(Collectors.toSet());
        }
        return authCompanyIds;
    }

    /**
     * 新增办公用品领用
     * 
     * @param offReceiveMain 办公用品领用主
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult insertOffReceiveMain(OffReceiveMain offReceiveMain) {
        AjaxResult ajaxResult = new AjaxResult();
        int rows = 0;
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String errorMessage = "";
        String successMessage = "";
        String itemNames = "";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分");
        try {
            offReceiveMain.setCreateTime(DateUtils.getNowDate());
            offReceiveMain.setCreateBy(SecurityUtils.getUsername());
            offReceiveMain.setDelFlag("0");
            if (StringUtils.equals(offReceiveMain.getApprovalStatus(), "1")){
                //提交时间
                offReceiveMain.setSubmitTime(DateUtils.getNowDate());
            }
            //生成申请单编号
            String nowDate = DateUtils.dateTime();
            //查询当天日期的领用数量
            int todayCount = offReceiveMainMapper.selectCount();
            // 生成2位编号，不足2位前面补0
            String applicationCode = "WPLY" + nowDate + String.format("%02d", todayCount + 1);
            offReceiveMain.setApplicationCode(applicationCode);

            //插入领用申请主表
            rows = offReceiveMainMapper.insertOffReceiveMain(offReceiveMain);
            //插入附件
            List<OffSupplyFiles> supplyFiles = offReceiveMain.getOffSupplyFileList();
            if (!CollectionUtils.isEmpty(supplyFiles)) {
                for (OffSupplyFiles supplyFile : supplyFiles) {
                    OffSupplyFiles file = new OffSupplyFiles();
                    file.setId(supplyFile.getId());
                    file.setRelevancyId(offReceiveMain.getId());
                    file.setStatus("0");
                    offSupplyFilesMapper.updateOffSupplyFiles(file);
                }
            }
            //插入领用申请明细表
            itemNames = insertOffReceiveDetail(offReceiveMain);

            //保存流程发起时的表单json
            if (!Objects.isNull(offReceiveMain.getOffReceivePurchaseDetail())){
                //先根据流程id查询是否存在此流程，存在则删除旧数据
                offReceiveMainMapper.deleteOffReceivePurchaseInfo(offReceiveMain.getProcessId());
                OffReceivePurchaseDetail offReceivePurchaseDetail = offReceiveMain.getOffReceivePurchaseDetail();
                offReceivePurchaseDetail.setCreateBy(loginUser.getUsername());
                offReceivePurchaseDetail.setCreateTime(DateUtils.getNowDate());
                offReceivePurchaseDetail.setProcessId(offReceiveMain.getProcessId());
                offReceiveMainMapper.insertOffReceivePurchaseInfo(offReceivePurchaseDetail);
            }
            //如果是提交流程(状态为1审核中)，则更新物品扣减数量
            if (StringUtils.equals(offReceiveMain.getApprovalStatus(), "1")){
                //更新办公用品库存数量
                List<OffReceiveDetail> offReceiveDetails = offReceiveMain.getOffReceiveDetailList();
                for (OffReceiveDetail detail : offReceiveDetails) {
                    //先获取物品的数量
                    OffSupplyMain offSupplyMain = offSupplyMainMapper.selectOffSupplyMainById(detail.getSupplyId());
                    Integer amount = offSupplyMain.getAmount();
                    //减去本次申请的数量
                    Integer stock = detail.getApplyNum();
                    Integer newAmount =  amount - stock;
                    OffSupplyMain supplyMain = new OffSupplyMain();
                    supplyMain.setId(detail.getSupplyId());
                    supplyMain.setAmount(newAmount);
                    offSupplyMainMapper.updateOffSupplyMain(supplyMain);
                    //更新物品数量后，判断是否到达预警值，如果到达预警值，则给用户发送预警信息
                    //先判断这个物品是否到达库存预警值
                    if (newAmount <= offSupplyMain.getItemWarning()){
                        //再判断是否开启通知形式，未开启则不通知
                        if (!offSupplyMain.getNotifyType().equals("") || offSupplyMain.getNotifyType() != null){
                            // 获取当前物品的类别的预警人员id
                            List<OffNotifyUser> offNotifyUserList = offCategoryMainMapper.selectOffNotifyUserByCategoryId(offSupplyMain.getCategoryId());
                            String sysMsg ="";
                            String VXmsg ="";
                            sysMsg= "办公用品管理中维护的物品【"+ offSupplyMain.getItemName() +"】，于"+ dateFormat.format(new Date()) +"已到达库存预警值，请注意补充库存。";
                            VXmsg = "<div class=\"gray\">" + "办公用品管理中维护的物品【"+ offSupplyMain.getItemName() +"】，于"+ dateFormat.format(new Date()) +"已到达库存预警值，请注意补充库存。" + "</div>";
                            //判断当前物品通知形式（0系统代办）
                            if(offSupplyMain.getNotifyType().equals("0")){
                                for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                                    offSupplyMainServiceImpl.sendNotify(offSupplyMain, sysMsg, offNotifyUser.getUserId(),"1");
                                }
                            }
                            //判断当前物品通知形式（1企业微信）
                            if (offSupplyMain.getNotifyType().equals("1")){
                                for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                                    SysUser sysUser = sysUserMapper.selectUserById(offNotifyUser.getUserId());
                                    offSupplyMainServiceImpl.sendQYWXNotify(offSupplyMain, VXmsg, sysUser.getUserName()); // 企业微信
                                }
                            }
                            //判断当前物品通知形式（3代办和企业微信）
                            if (offSupplyMain.getNotifyType().equals("3")){
                                // 发送系统代办和企业微信通知
                                for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                                    offSupplyMainServiceImpl.sendNotify(offSupplyMain, sysMsg, offNotifyUser.getUserId(), "1");
                                    SysUser sysUser = sysUserMapper.selectUserById(offNotifyUser.getUserId());
                                    offSupplyMainServiceImpl.sendQYWXNotify(offSupplyMain, VXmsg, sysUser.getUserName()); // 企业微信
                                }
                            }
                        }
                    }
                }
                //查询当前登录用户姓名
                SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
                successMessage = sysUser.getNickName() + "对办公用品【" + itemNames + "】发起了领用申请";
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFRECEIVEMAIN.getCode(), successMessage, 1, errorMessage, "");
            }
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            successMessage = sysUser.getNickName() + "对办公用品【" + itemNames + "】发起了领用申请";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFRECEIVEMAIN.getCode(), successMessage, 1, errorMessage, "");
        }
        if (rows > 0){
            ajaxResult.put("mgs","操作成功");
            ajaxResult.put("code","200");
            ajaxResult.put("id",offReceiveMain.getId());
        }else {
            ajaxResult.put("mgs","操作失败");
            ajaxResult.put("code","500");
        }
        return ajaxResult;
    }

    /**
     * 修改办公用品领用
     * 
     * @param offReceiveMain 办公用品领用主
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult updateOffReceiveMain(OffReceiveMain offReceiveMain)
    {
        AjaxResult ajaxResult = new AjaxResult();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分");
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String errorMessage = "";
        String successMessage = "";
        String itemNames = "";
        try {
            //删除旧的领用申请明细表数据
            offReceiveMainMapper.deleteOffReceiveDetailByReceiveId(offReceiveMain.getId());
            //插入新的领用申请明细表数据
            itemNames = insertOffReceiveDetail(offReceiveMain);
            //附件
            //先将旧的附件标记为历史数据
            OffSupplyFiles offSupplyFiles = new OffSupplyFiles();
            offSupplyFiles.setRelevancyId(offReceiveMain.getId());
            offSupplyFiles.setStatus("1");
            offSupplyFiles.setFileType("2");
            offSupplyFilesMapper.updateOffSupplyFilesStatusByRelevancyId(offSupplyFiles);
            //插入新的附件信息
            if (offReceiveMain.getOffSupplyFileList() != null && offReceiveMain.getOffSupplyFileList().size() > 0){
            offReceiveMain.getOffSupplyFileList().forEach(supplyFiles -> {
                OffSupplyFiles picture = new OffSupplyFiles();
                picture.setId(supplyFiles.getId());
                picture.setRelevancyId(offReceiveMain.getId());
                picture.setStatus("0");
                offSupplyFilesMapper.updateOffSupplyFiles(picture);
            });
            }
            //保存流程发起时的表单json
            if (!Objects.isNull(offReceiveMain.getOffReceivePurchaseDetail())){
                //先根据流程id查询是否存在此流程，存在则删除旧数据
                offReceiveMainMapper.deleteOffReceivePurchaseInfo(offReceiveMain.getProcessId());
                OffReceivePurchaseDetail offReceivePurchaseDetail = offReceiveMain.getOffReceivePurchaseDetail();
                offReceivePurchaseDetail.setCreateBy(loginUser.getUsername());
                offReceivePurchaseDetail.setCreateTime(DateUtils.getNowDate());
                offReceivePurchaseDetail.setProcessId(offReceiveMain.getProcessId());
                offReceiveMainMapper.insertOffReceivePurchaseInfo(offReceivePurchaseDetail);
            }

            //如果是提交流程(状态为1审核中)，则更新物品扣减数量
            if (StringUtils.equals(offReceiveMain.getApprovalStatus(), "1")){
                //提交时间
                offReceiveMain.setSubmitTime(DateUtils.getNowDate());
                //更新办公用品库存数量
                List<OffReceiveDetail> offReceiveDetails = offReceiveMain.getOffReceiveDetailList();
                for (OffReceiveDetail detail : offReceiveDetails) {
                    //先获取物品的数量
                    OffSupplyMain offSupplyMain = offSupplyMainMapper.selectOffSupplyMainById(detail.getSupplyId());
                    Integer amount = offSupplyMain.getAmount();
                    //减去本次申请的数量
                    Integer stock = detail.getApplyNum();
                    Integer newAmount =  amount - stock;
                    OffSupplyMain supplyMain = new OffSupplyMain();
                    supplyMain.setId(detail.getSupplyId());
                    supplyMain.setAmount(newAmount);
                    offSupplyMainMapper.updateOffSupplyMain(supplyMain);
                    //更新物品数量后，判断是否到达预警值，如果到达预警值，则给用户发送预警信息
                    //先判断这个物品是否到达库存预警值
                    if (newAmount <= offSupplyMain.getItemWarning()){
                        //再判断是否开启通知形式，未开启则不通知
                        if (offSupplyMain.getNotifyType() != null && !offSupplyMain.getNotifyType().equals("")){
                            // 获取当前物品的类别的预警人员id
                            List<OffNotifyUser> offNotifyUserList = offCategoryMainMapper.selectOffNotifyUserByCategoryId(offSupplyMain.getCategoryId());
                            String sysMsg ="";
                            String VXmsg ="";
                            sysMsg= "办公用品管理中维护的物品【"+ offSupplyMain.getItemName() +"】，于"+ dateFormat.format(new Date()) +"已到达库存预警值，请注意补充库存。";
                            VXmsg = "<div class=\"gray\">" + "办公用品管理中维护的物品【"+ offSupplyMain.getItemName() +"】，于"+ dateFormat.format(new Date()) +"已到达库存预警值，请注意补充库存。" + "</div>";
                            //判断当前物品通知形式（0系统代办）
                            if(offSupplyMain.getNotifyType().equals("0")){
                                for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                                    offSupplyMainServiceImpl.sendNotify(offSupplyMain, sysMsg, offNotifyUser.getUserId(),"1");
                                }
                            }
                            //判断当前物品通知形式（1企业微信）
                            if (offSupplyMain.getNotifyType().equals("1")){
                                for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                                    SysUser sysUser = sysUserMapper.selectUserById(offNotifyUser.getUserId());
                                    offSupplyMainServiceImpl.sendQYWXNotify(offSupplyMain, VXmsg, sysUser.getUserName()); // 企业微信
                                }
                            }
                            //判断当前物品通知形式（3代办和企业微信）
                            if (offSupplyMain.getNotifyType().equals("3")){
                                // 发送系统代办和企业微信通知
                                for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                                    offSupplyMainServiceImpl.sendNotify(offSupplyMain, sysMsg, offNotifyUser.getUserId(), "1");//系统代办
                                    SysUser sysUser = sysUserMapper.selectUserById(offNotifyUser.getUserId());
                                    offSupplyMainServiceImpl.sendQYWXNotify(offSupplyMain, VXmsg, sysUser.getUserName()); // 企业微信
                                }
                            }
                        }
                    }
                }
                //查询当前登录用户姓名
                SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
                successMessage = sysUser.getNickName() + "对办公用品【" + itemNames + "】发起了领用申请，申请单编号：" + offReceiveMain.getApplicationCode();
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFRECEIVEMAIN.getCode(), successMessage, 1, errorMessage, "");
            }
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            successMessage = sysUser.getNickName() + "对办公用品【" + itemNames + "】进行了领用，申请单编号";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFRECEIVEMAIN.getCode(), successMessage, 1, errorMessage, "");
        }
        //更新办公用品领用表信息
        int i = offReceiveMainMapper.updateOffReceiveMain(offReceiveMain);
        ajaxResult.put("id", offReceiveMain.getId());
        if (i > 0) {
            ajaxResult.put("code", 200);
            ajaxResult.put("msg", "修改办公用品领用成功");
        }else {
            ajaxResult.put("code", 500);
            ajaxResult.put("msg", "修改办公用品领用失败");
        }
        return ajaxResult;
    }

    /**
     * 批量删除办公用品领用
     * 
     * @param ids 需要删除的办公用品领用主主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteOffReceiveMainByIds(Long[] ids)
    {
        offReceiveMainMapper.deleteOffReceiveDetailByReceiveIds(ids);
        return offReceiveMainMapper.deleteOffReceiveMainByIds(ids);
    }

    /**
     * 删除办公用品领用信息
     * 
     * @param id 办公用品领用主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteOffReceiveMainById(Long id)
    {
        int i = 0;
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String errorMessage = "";
        String successMessage = "";
        try {
            //逻辑删除办公用品领用表信息
            i = offReceiveMainMapper.deleteOffReceiveMainById(id);
            //逻辑删除附件表信息
            offSupplyFilesMapper.deleteOffSupplyFilesBySupplyIdAndType(id, "2");
            //删除领用明细表
            //i = offReceiveMainMapper.deleteOffReceiveDetailByReceiveId(id);
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        } finally {
            //查询当前登录用户姓名
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            //查询领用申请详情
            OffReceiveMain offSupplyMain = selectOffReceiveMainById(id);
            successMessage = sysUser.getNickName() + "删除了编号为【" + offSupplyMain.getApplicationCode() + "】的办公用品领用申请单";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFRECEIVEMAIN.getCode(), successMessage, 1, errorMessage, "");
        }
        return i;
    }

    /**
     * 修改流程状态
     * flowStatus 流程状态(驳回传1(审核中)，废弃和审核不通过传2，审核通过传3)
     * @param offReceiveMain
     * @return
     */
    @Override
    public int updateProcessStatus(OffReceiveMain offReceiveMain) {
        //审核通过
        if (offReceiveMain.getApprovalStatus().equals("3")){
            offReceiveMain.setApprovalTime(DateUtils.getNowDate());
        }
        //废弃或审核不通过
        if (offReceiveMain.getApprovalStatus().equals("2")){
            //根据流程id获取本次申请的物品id
            // List<OffReceiveDetail> offReceiveDetails = offReceiveMain.getOffReceiveDetailList();
            List<OffReceiveDetail> offReceiveDetails = offSupplyMainMapper.selectOffReceiveDetailByFlowId(offReceiveMain.getProcessId());
            for (OffReceiveDetail detail : offReceiveDetails) {
                //先获取物品的数量
                OffSupplyMain offSupplyMain = offSupplyMainMapper.selectOffSupplyMainById(detail.getSupplyId());
                Integer amount = offSupplyMain.getAmount();
                //加上本次申请的数量
                Integer stock = detail.getApplyNum();
                Integer newAmount =  amount+stock;
                OffSupplyMain supplyMain = new OffSupplyMain();
                supplyMain.setId(detail.getSupplyId());
                supplyMain.setAmount(newAmount);
                offSupplyMainMapper.updateOffSupplyMain(supplyMain);
            }
        }
        return offReceiveMainMapper.updateReceiveStatusByFlowId(offReceiveMain);
    }

    /**
     * 新增办公用品申请详情信息
     * 
     * @param offReceiveMain 办公用品领用对象
     */
    public String insertOffReceiveDetail(OffReceiveMain offReceiveMain)
    {
        String itemName = "";
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<OffReceiveDetail> offReceiveDetailList = offReceiveMain.getOffReceiveDetailList();
        Long id = offReceiveMain.getId();
        if (CollectionUtils.isNotEmpty(offReceiveDetailList))
        {
            List<OffReceiveDetail> list = new ArrayList<OffReceiveDetail>();
            for (OffReceiveDetail offReceiveDetail : offReceiveDetailList)
            {
                offReceiveDetail.setReceiveId(id);
                if (StringUtils.isNotEmpty(offReceiveMain.getProcessId())) {
                    offReceiveDetail.setProcessId(offReceiveMain.getProcessId());
                }
                offReceiveDetail.setCreateBy(loginUser.getUsername());
                offReceiveDetail.setCreateTime(DateUtils.getNowDate());
                list.add(offReceiveDetail);
                //查询物品信息，组装物品名称参数
                OffSupplyMain offSupplyMain = offSupplyMainMapper.selectOffSupplyMainById(offReceiveDetail.getSupplyId());
                itemName += "【"+offSupplyMain.getItemName()+"】";
            }
            if (list.size() > 0)
            {
                offReceiveMainMapper.batchOffReceiveDetail(list);
            }
        }
        return itemName;
    }

    /**
     * 查询物品领用报表
     * @param receiveReportVo
     * @return
     */
    @Override
    public List<OffReceiveReport> selectSupplyReceiveReportList(ReceiveReportVo receiveReportVo) {
        //查询通用授权-公司权限
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //新权限->查询当前登录用户有哪些公司的物品管理员权限
        List<Long> companyIds = queryLoginUserWPGLYAuth(loginUser.getUser().getUserId());
        //查询当前用户有哪些下级
        List<Long> userSubordinateList = queryLooginUserSubordinate(loginUser.getUsername());
        receiveReportVo.setAuthCompanyIds(companyIds);
        receiveReportVo.setUserSubordinateList(userSubordinateList);
        receiveReportVo.setLoginUserName(loginUser.getUsername());
        //获取所有的物品领用信息
        List<OffReceiveReport> offReceiveMains = offReceiveMainMapper.selectSupplyReceiveReportList(receiveReportVo);
        // 使用流操作进行分组和求和
        Map<String, Integer> sumMap = offReceiveMains.stream()
                .filter(report -> report.getApprovalStatus().equals("1")) // 过滤 approvalStatus 为 1 的记录
                .collect(Collectors.groupingBy(
                        OffReceiveReport::getItemSysCode, // 根据 itemSysCode 分组
                        Collectors.summingInt(OffReceiveReport::getApplyNum) // 求和 applyNum
                ));

        // 更新 offReceiveMains 中每个 OffReceiveReport 对象的 amount 字段
        offReceiveMains.forEach(report -> {
            String itemSysCode = report.getItemSysCode();
            if (sumMap.containsKey(itemSysCode)) {
                report.setAmount(sumMap.get(itemSysCode) + report.getAmount());
            }
        });
        return offReceiveMains;
    }

    /**
     * 导出物品领用报表
     * @param receiveReportVo
     */
    @Override
    public void exportReceiveReport(HttpServletResponse response, ReceiveReportVo receiveReportVo) throws IOException {
        //查询通用授权-公司权限
        LoginUser loginUser = SecurityUtils.getLoginUser();

        //新权限->查询当前登录用户有哪些公司的物品管理员权限
        List<Long> companyIds = queryLoginUserWPGLYAuth(loginUser.getUser().getUserId());
        //查询当前用户有哪些下级
        List<Long> userSubordinateList = queryLooginUserSubordinate(loginUser.getUsername());
        receiveReportVo.setAuthCompanyIds(companyIds);
        receiveReportVo.setUserSubordinateList(userSubordinateList);
        receiveReportVo.setLoginUserName(loginUser.getUsername());
        //获取所有的物品领用信息
        List<OffReceiveReport> offReceiveMains = offReceiveMainMapper.selectSupplyReceiveReportList(receiveReportVo);

        if (CollectionUtils.isEmpty(offReceiveMains)){
            return;
        }
        //获取所有的物品领用信息
        Set<Long> collect = offReceiveMains.stream().map(OffReceiveReport::getDetailId).collect(Collectors.toSet());
        //获取状态为审核中的各个物品的总数
        List<OffReceiveReport> offReceiveDetailList = offReceiveMainMapper.selectAllReceiveList(collect);
        for (OffReceiveReport offReceiveDetail : offReceiveDetailList) {
            for (OffReceiveReport offReceiveMain : offReceiveMains) {
                if (offReceiveDetail.getItemSysCode().equals(offReceiveMain.getItemSysCode())){
                    Integer amount = offReceiveMain.getAmount();//当前库存数量
                    Integer approvalStatusCount = new Integer(offReceiveDetail.getApprovalStatusCount());//审核中的数量
                    //计算当前库存+审核中的数量的总和
                    int newResidueNum = amount + approvalStatusCount;
                    offReceiveMain.setAuditAmount(approvalStatusCount.toString());
                    offReceiveMain.setAmount(newResidueNum);
                }
            }
        }
        //调用导出接口
        Export.exportToExcel(response, offReceiveMains, "物品领用报表.xlsx");
    }

    /**
     * 检查用户是否有某公司流程发起权限
     * @return
     */
    @Override
    public Boolean checkUserReceiveAuth(Long companyId) {
        Boolean flag = false;
        Set<Long> authCompanyIds = new HashSet<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<AuthMain> authMainList = offReceiveMainMapper.selectOaLaunchAuth(loginUser.getUserId(), AuthModuleEnum.OALAUNCH.getCode(), AuthRoleEnum.COMMON.getCode());
        if (CollectionUtils.isEmpty(authMainList)){
            flag = false;
        }else {
            for (AuthMain authMain : authMainList) {
                //根据权限主表id查询权限附表信息
                List<AuthDetail> detailList = offReceiveMainMapper.selectAuthDetailByMainId(authMain.getId());
                //如果主表有数据，副表为空，则证明是初始化权限，返回所有内部担保公司
                if (CollectionUtils.isEmpty(detailList)) {
                    flag = true;
                    return flag;
                }
            }
            /** 如果主表有数据，副表也有对应的数据，则证明后期人工授权，查询对应的公司id */
            //根据权限主表id集合批量查询对应的副表信息
            List<AuthDetail> detailList = offReceiveMainMapper.selectAuthDetailByMainIdList(authMainList);
            if (!CollectionUtils.isEmpty(detailList)) {
                authCompanyIds = detailList.stream().map(AuthDetail::getThirdTableId).collect(Collectors.toSet());
            }
            if (authCompanyIds.contains(companyId)) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 根据流程id查询办公用品领用、物品领用、采购申请表单信息
     * @param processId
     * @return
     */
    @Override
    public OffReceivePurchaseDetail selectOffReceiveMainByProcessId(String processId) {
        return offReceiveMainMapper.selectOffReceiveMainByProcessId(processId);
    }

    /**
     * 保存表单数据
     * @param offReceivePurchaseDetail
     * @return
     */
    @Override
    public int saveFormData(OffReceivePurchaseDetail offReceivePurchaseDetail) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        int i = 0;
        //保存流程发起时的表单json
        if (!Objects.isNull(offReceivePurchaseDetail)){
            //先根据流程id查询是否存在此流程，存在则删除旧数据
            offReceiveMainMapper.deleteOffReceivePurchaseInfo(offReceivePurchaseDetail.getProcessId());
            offReceivePurchaseDetail.setCreateBy(loginUser.getUsername());
            offReceivePurchaseDetail.setCreateTime(DateUtils.getNowDate());
            i = offReceiveMainMapper.insertOffReceivePurchaseInfo(offReceivePurchaseDetail);
        }
        return i;
    }
}
