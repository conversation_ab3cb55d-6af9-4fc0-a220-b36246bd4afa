package org.ruoyi.core.offSupply.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.offSupply.domain.OffSupplyFiles;

import java.util.List;

public interface OffSupplyFilesMapper {

    /**
     * 查询详情
     *
     * @param id 主键
     * @return
     */
    public OffSupplyFiles selectOffSupplyFilesById(Long id);

    /**
     * 查询列表
     *
     * @param offSupplyFiles
     * @return
     */
    public List<OffSupplyFiles> selectOffSupplyFilesList(OffSupplyFiles offSupplyFiles);

    /**
     * 新增附件
     *
     * @param offSupplyFiles
     * @return 结果
     */
    public int insertOffSupplyFiles(OffSupplyFiles offSupplyFiles);

    /**
     * 修改【请填写功能名称】
     *
     * @param offSupplyFiles
     * @return 结果
     */
    public int updateOffSupplyFiles(OffSupplyFiles offSupplyFiles);

    /**
     * 根据附件id删除附件
     *
     * @param id 主键
     * @return 结果
     */
    public int deleteOffSupplyFilesById(Long id);

    /**
     * 批量删除
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOffSupplyFilesByIds(Long[] ids);

    /**
     * 根据物品id和物品附件类型删除附件
     * @param supplyId
     * @param type
     */
    void deleteOffSupplyFilesBySupplyIdAndType(@Param("supplyId") Long supplyId, @Param("type")String type);

    /**
     * 根据文件类型和关联id查询附件信息
     * 集合
     */
    public List<OffSupplyFiles> selectOffSupplyFilesByTypeAndRelevancyId(@Param("fileType") String fileType, @Param("relevancyId") Long relevancyId);

    /**
     * 根据关联id和附件类型查询物品图片
     * 单条
     */
    public OffSupplyFiles selectSupplyFile(@Param("fileType") String fileType, @Param("relevancyId") Long relevancyId);

    /**
     * 查询当天采购单数量
     */
    int selectCountNumber();

    /**
     * 根据关联id和附件类型更新状态
     * @param offSupplyFiles
     */
    void updateOffSupplyFilesStatusByRelevancyId(OffSupplyFiles offSupplyFiles);
}
