package org.ruoyi.core.offSupply.domain;

import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 办公用品领用主对象 off_receive_main
 * 
 * <AUTHOR>
 * @date 2025-03-21
 */
public class OffReceiveMain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 申请单编号 */
    @Excel(name = "申请单编号")
    private String applicationCode;

    /** 申请人id */
    @Excel(name = "申请人id")
    private Long userId;

    /** 申请人姓名 */
    private String userNickName;

    /** 流程id */
    @Excel(name = "流程id")
    private String processId;

    /** 状态(0未提交 1审核中 2审核通过 3审核不通过) */
    @Excel(name = "状态(0未提交 1审核中 2审核不通过 3审核通过)")
    private String approvalStatus;

    /** 状态字典值 */
    private String approvalStatusLabel;

    /** 删除标识(0未删除 1已删除) */
    @Excel(name = "删除标识(0未删除 1已删除)")
    private String delFlag;

    /** 申请事由 */
    @Excel(name = "申请事由")
    private String cause;

    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /** 审核通过时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核通过时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /** 领用申请所属公司ID */
    private Long companyId;

    /** 领用申请所属公司简称 */
    private String companyShortName;

    /** 办公用品申请详情信息 */
    private List<OffReceiveDetail> offReceiveDetailList;

    /** 办公用品名称 */
    private String itemName;

    /** 当前登录用户id */
    private Long loginUserId;

    /** 当前登录用户名称 */
    private String loginUserName;

    /** 领用申请附件 */
    private List<OffSupplyFiles> offSupplyFileList;

    /** 创建开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date createBeginTime;

    /** 创建结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date createEndTime;

    /** 通用授权-公司id集合 */
    private List<Long> authCompanyIds;

    /** 用户下级，孙级用户id集合 */
    private List<Long> userSubordinateList;

    /** 分页参数 */
    private Integer pageSize;

    /** 分页参数 */
    private Integer pageNum;

    /** 流程发起表单json */
    private OffReceivePurchaseDetail offReceivePurchaseDetail;

    public String getCompanyShortName() {
        return companyShortName;
    }

    public void setCompanyShortName(String companyShortName) {
        this.companyShortName = companyShortName;
    }

    public String getLoginUserName() {
        return loginUserName;
    }

    public void setLoginUserName(String loginUserName) {
        this.loginUserName = loginUserName;
    }

    public OffReceivePurchaseDetail getOffReceivePurchaseDetail() {
        return offReceivePurchaseDetail;
    }

    public void setOffReceivePurchaseDetail(OffReceivePurchaseDetail offReceivePurchaseDetail) {
        this.offReceivePurchaseDetail = offReceivePurchaseDetail;
    }

    public String getUserNickName() {
        return userNickName;
    }

    public void setUserNickName(String userNickName) {
        this.userNickName = userNickName;
    }

    public List<Long> getUserSubordinateList() {
        return userSubordinateList;
    }

    public void setUserSubordinateList(List<Long> userSubordinateList) {
        this.userSubordinateList = userSubordinateList;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public List<Long> getAuthCompanyIds() {
        return authCompanyIds;
    }

    public void setAuthCompanyIds(List<Long> authCompanyIds) {
        this.authCompanyIds = authCompanyIds;
    }

    public String getApprovalStatusLabel() {
        return approvalStatusLabel;
    }

    public void setApprovalStatusLabel(String approvalStatusLabel) {
        this.approvalStatusLabel = approvalStatusLabel;
    }

    public Long getLoginUserId() {
        return loginUserId;
    }

    public void setLoginUserId(Long loginUserId) {
        this.loginUserId = loginUserId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setApplicationCode(String applicationCode) 
    {
        this.applicationCode = applicationCode;
    }

    public String getApplicationCode() 
    {
        return applicationCode;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public void setApprovalStatus(String approvalStatus)
    {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() 
    {
        return approvalStatus;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }
    public void setCause(String cause) 
    {
        this.cause = cause;
    }

    public String getCause() 
    {
        return cause;
    }
    public void setSubmitTime(Date submitTime) 
    {
        this.submitTime = submitTime;
    }

    public Date getSubmitTime() 
    {
        return submitTime;
    }
    public void setApprovalTime(Date approvalTime) 
    {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() 
    {
        return approvalTime;
    }

    public List<OffReceiveDetail> getOffReceiveDetailList()
    {
        return offReceiveDetailList;
    }

    public void setOffReceiveDetailList(List<OffReceiveDetail> offReceiveDetailList)
    {
        this.offReceiveDetailList = offReceiveDetailList;
    }

    public List<OffSupplyFiles> getOffSupplyFileList() {
        return offSupplyFileList;
    }

    public void setOffSupplyFileList(List<OffSupplyFiles> offSupplyFileList) {
        this.offSupplyFileList = offSupplyFileList;
    }

    public Date getCreateBeginTime() {
        return createBeginTime;
    }

    public void setCreateBeginTime(Date createBeginTime) {
        this.createBeginTime = createBeginTime;
    }

    public Date getCreateEndTime() {
        return createEndTime;
    }

    public void setCreateEndTime(Date createEndTime) {
        this.createEndTime = createEndTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applicationCode", getApplicationCode())
            .append("userId", getUserId())
            .append("processId", getProcessId())
            .append("approvalStatus", getApprovalStatus())
            .append("delFlag", getDelFlag())
            .append("cause", getCause())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("submitTime", getSubmitTime())
            .append("approvalTime", getApprovalTime())
            .append("offReceiveDetailList", getOffReceiveDetailList())
            .toString();
    }
}
