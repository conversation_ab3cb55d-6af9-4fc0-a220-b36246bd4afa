package org.ruoyi.core.offSupply.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

@Data
public class OffSupplyFiles extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 文件类型(1办公用品图片 2办公用品领用 3办公用品采购发票 4其他附件 5办公用品附件) */
    private String fileType;

    /** 关联id */
    private Long relevancyId;

    /** 文件路径 */
    private String filePath;

    /** 文件名 */
    private String fileName;

    /** 状态(0最新 1历史) */
    private String status;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("fileType", getFileType())
                .append("relevancyId", getRelevancyId())
                .append("filePath", getFilePath())
                .append("fileName", getFileName())
                .append("version", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .toString();
    }
}
