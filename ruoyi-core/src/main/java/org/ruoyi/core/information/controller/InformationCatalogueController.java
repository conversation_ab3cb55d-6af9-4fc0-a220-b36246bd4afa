package org.ruoyi.core.information.controller;

import java.text.ParseException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.information.domain.InformationCatalogue;
import org.ruoyi.core.information.domain.vo.InformationCatalogueVO;
import org.ruoyi.core.information.service.IInformationCatalogueService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资料目录Controller
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@RestController
@RequestMapping("/information/informationCatalogue")
public class InformationCatalogueController extends BaseController
{
    @Autowired
    private IInformationCatalogueService informationCatalogueService;

    /**
     * 查询资料目录列表
     */
    //@PreAuthorize("@ss.hasPermi('core:information:list')")
    @GetMapping("/list")
    public TableDataInfo list(InformationCatalogueVO informationCatalogue)
    {
        //startPage();
        List<InformationCatalogueVO> list = informationCatalogueService.selectInformationCatalogueList(informationCatalogue);
        return getDataTable(list);
    }

    /**
     * 导出资料目录列表
     */
    //@PreAuthorize("@ss.hasPermi('core:information:export')")
    @Log(title = "导出资料目录列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InformationCatalogue informationCatalogue)
    {
        List<InformationCatalogueVO> list = informationCatalogueService.selectInformationCatalogueList(informationCatalogue);
        ExcelUtil<InformationCatalogueVO> util = new ExcelUtil<InformationCatalogueVO>(InformationCatalogueVO.class);
        util.exportExcel(response, list, "资料目录数据");
    }

    /**
     * 获取资料目录详细信息
     */
    //@PreAuthorize("@ss.hasPermi('core:information:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(informationCatalogueService.selectInformationCatalogueVOById(id));
    }

    /**
     * 新增资料目录
     */
    //@PreAuthorize("@ss.hasPermi('core:information:add')")
    @Log(title = "新增资料目录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InformationCatalogue informationCatalogue) throws ParseException {
        return toAjax(informationCatalogueService.insertInformationCatalogue(informationCatalogue));
    }

    /**
     * 修改资料目录
     */
    //@PreAuthorize("@ss.hasPermi('core:information:edit')")
    @Log(title = "修改资料目录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InformationCatalogue informationCatalogue) throws ParseException {
        return toAjax(informationCatalogueService.updateInformationCatalogue(informationCatalogue));
    }

    /**
     * 删除资料目录
     */
    //@PreAuthorize("@ss.hasPermi('core:information:remove')")
    @Log(title = "资料目录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(informationCatalogueService.deleteInformationCatalogueByIds(ids));
    }

    /**
     * 获取子目录
     *
     */
    //@PreAuthorize("@ss.hasPermi('core:information:query')")
    @GetMapping(value = "/getListByParentId/{parentId}")
    public TableDataInfo getListByParentId(@PathVariable("parentId") Long parentId)
    {
        startPage();
        List<InformationCatalogue> list = informationCatalogueService.selectInformationCatalogueByParentId(parentId);
        return getDataTable(list);
    }




    /**
     * 上级目录树状列表
     */
    //@PreAuthorize("@ss.hasPermi('archivist:catalogue:query')")
    @GetMapping(value = "/getTreeList")
    public AjaxResult getTreeList(InformationCatalogueVO informationCatalogue)
    {
        return AjaxResult.success(informationCatalogueService.getTreeListOfAuthority(informationCatalogue));
    }

    /**
     * 获取资料有权限的一级目录
     */
    //@PreAuthorize("@ss.hasPermi('archivist:catalogue:query')")
    @GetMapping(value = "/selectCatalogueListOfAuthority")
    public AjaxResult selectCatalogueListOfAuthority(InformationCatalogue informationCatalogue)
    {
        return AjaxResult.success(informationCatalogueService.selectCatalogueListOfAuthority(informationCatalogue));
    }

}
