package org.ruoyi.core.information.service.impl;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.service.INewAuthorityService;
import com.ruoyi.system.service.ISysPostService;
import org.ruoyi.core.information.domain.Authority;
import org.ruoyi.core.information.domain.InformationCatalogue;
import org.ruoyi.core.information.domain.vo.InformationCatalogueVO;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.information.mapper.InformationCatalogueMapper;
import org.ruoyi.core.information.mapper.InformationMapper;
import org.ruoyi.core.information.service.IAuthorityService;
import org.ruoyi.core.information.service.IInformationCatalogueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 资料目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-10
 *
 */
@Service
public class InformationCatalogueServiceImpl implements IInformationCatalogueService
{
    @Autowired
    private InformationCatalogueMapper informationCatalogueMapper;
    @Autowired
    private InformationMapper iformationMapper;
    @Autowired
    private ISysPostService iSysPostService;
    @Autowired
    private IAuthorityService authorityService;
    @Autowired
    private INewAuthorityService newAuthorityService;
    /**
     * 查询资料目录
     *
     * @param id 资料目录主键
     * @return 资料目录
     */
    @Override
    public InformationCatalogue selectInformationCatalogueById(Long id)
    {
        return informationCatalogueMapper.selectInformationCatalogueById(id);
    }

    /**
     * 查询资料目录
     *
     * @param id 资料目录主键
     * @return 资料目录
     */
    @Override
    public InformationCatalogueVO selectInformationCatalogueVOById(Long id)
    {
        return informationCatalogueMapper.selectInformationCatalogueVOById(id);
    }


    /**
     * 查询资料目录列表
     *
     * @param informationCatalogue 资料目录
     * @return 资料目录
     */
    @Override
    public List<InformationCatalogueVO> selectInformationCatalogueList(InformationCatalogue informationCatalogue)
    {
        List<InformationCatalogueVO> treeListOfAuthority = getTreeListOfAuthority(informationCatalogue);
        List<Long> allIds = new ArrayList<>();
        if (informationCatalogue.getParentId() == 0){
            return treeListOfAuthority;
        } else {
            allIds = getChildIds(treeListOfAuthority,informationCatalogue.getParentId());
        }
        allIds.remove(informationCatalogue.getParentId());
        informationCatalogue.setIds(allIds);
        if (allIds.isEmpty()){
            return new ArrayList<>();
        }
        //解决若依框架一个接口查询多个list分页失效问题
        PageUtil.startPage();
        return informationCatalogueMapper.selectInformationCatalogueList(informationCatalogue);
    }

    @Override
    public List<InformationCatalogueVO> selectInformationCatalogueListNoLimit(InformationCatalogue informationCatalogue)
    {
        List<InformationCatalogueVO> treeListOfAuthority = getTreeListOfAuthority(informationCatalogue);
        List<Long> allIds = new ArrayList<>();
        if (informationCatalogue.getParentId() == null){
            allIds=  getAllIds(treeListOfAuthority);
        } else {
            allIds = getChildIds(treeListOfAuthority,informationCatalogue.getParentId());
        }
        allIds.remove(informationCatalogue.getParentId());
        informationCatalogue.setIds(allIds);
        if (allIds.isEmpty()){
            return new ArrayList<>();
        }
        return informationCatalogueMapper.selectInformationCatalogueList(informationCatalogue);
    }

    public List<Long> getAllIds(List<InformationCatalogueVO> list) {
        List<Long> allIds = new ArrayList<>();
        for (InformationCatalogueVO vo : list) {
            allIds.add(vo.getId());  // 提取当前层级对象的id
            if (vo.getInformationCatalogueVOList() != null) {
                allIds.addAll(getAllIds(vo.getInformationCatalogueVOList()));  // 递归提取子集的id
            }
        }
        return allIds;
    }

    public List<Long> getChildIds(List<InformationCatalogueVO> list, Long parentId) {
        List<Long> childIds = new ArrayList<>();
        findChildIds(list, parentId, childIds);
        return childIds;
    }

    private void findChildIds(List<InformationCatalogueVO> list, Long parentId, List<Long> childIds) {
        for (InformationCatalogueVO vo : list) {
            if (vo.getId().equals(parentId)) {
                addChildIds(vo, childIds);  // 添加当前对象及其子集的id
                return;  // 找到指定id的对象后直接返回，不再继续遍历
            } else {
                if (vo.getInformationCatalogueVOList() != null) {
                    findChildIds(vo.getInformationCatalogueVOList(), parentId, childIds);  // 递归查找子集
                }
            }
        }
    }

    private void addChildIds(InformationCatalogueVO vo, List<Long> childIds) {
        childIds.add(vo.getId());  // 添加当前对象的id
        if (vo.getInformationCatalogueVOList() != null) {
            for (InformationCatalogueVO child : vo.getInformationCatalogueVOList()) {
                addChildIds(child, childIds);  // 递归添加子集的id
            }
        }
    }

    /**
     * 新增资料目录
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    @Override
    public int insertInformationCatalogue(InformationCatalogue informationCatalogue) throws ParseException {
        //补充目录信息
        informationCatalogue.setCreateTime(DateUtils.getNowDate());
        informationCatalogue.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        //系统编号逻辑
        int count = getCountByCreateTime(DateUtils.getDate()) + 1;
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        informationCatalogue.setCatalogueSystemCode("ML" + createTimeNum + String.format("%03d", count));
        int row = informationCatalogueMapper.insertInformationCatalogue(informationCatalogue);

        List<Authority> authorityList = new ArrayList<>();
        if (!informationCatalogue.getAuDeptIds().isEmpty()){
            //informationCatalogue.getAuDeptIds().forEach(deptId -> {
            Authority authority = new Authority();
            authority.setBillId(informationCatalogue.getId() + "");
            authority.setBillType("0");
            authority.setAuthorityType("0");
            authority.setAuthorityIds(informationCatalogue.getAuDeptIds().toArray(new Long[0]));
            authority.setImpower(getUsername());
            authority.setImpowerTime(DateUtils.getNowDate());
            authorityList.add(authority);
            //});
        }

        if (!informationCatalogue.getAuPostIds().isEmpty()){
            //informationCatalogue.getAuPostIds().forEach(postId -> {
            Authority authority = new Authority();
            authority.setBillId(informationCatalogue.getId() + "");
            authority.setBillType("0");
            authority.setAuthorityType("1");
            authority.setAuthorityIds(informationCatalogue.getAuPostIds().toArray(new Long[0]));
            authority.setImpower(getUsername());
            authority.setImpowerTime(DateUtils.getNowDate());
            authorityList.add(authority);
            //});
        }

        if (!informationCatalogue.getAuUserIds().isEmpty()){
            //informationCatalogue.getAuUserIds().forEach(userId ->{
            Authority authority = new Authority();
            authority.setBillId(informationCatalogue.getId() + "");
            authority.setBillType("0");
            authority.setAuthorityType("2");
            authority.setAuthorityIds(informationCatalogue.getAuUserIds().toArray(new Long[0]));
            authority.setImpower(getUsername());
            authority.setImpowerTime(DateUtils.getNowDate());
            authorityList.add(authority);
            //});
        }
        if (!authorityList.isEmpty()){
            authorityService.insertAuthority(authorityList);
        }
        return row;
    }

    /**
     * 修改资料目录
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    @Override
    public int updateInformationCatalogue(InformationCatalogue informationCatalogue) throws ParseException {
        if(Objects.equals(informationCatalogue.getId(), informationCatalogue.getParentId())){
            throw new ServiceException("上级目录不能选择与修改目录相同");
        }

        informationCatalogue.setUpdateTime(DateUtils.getNowDate());
        informationCatalogue.setUpdateBy(SecurityUtils.getLoginUser().getUsername());

        return informationCatalogueMapper.updateInformationCatalogue(informationCatalogue);
    }

    /**
     * 批量删除资料目录
     *
     * @param ids 需要删除的资料目录主键
     * @return 结果
     */
    @Override
    public int deleteInformationCatalogueByIds(Long[] ids)
    {
        int catalogueCount = informationCatalogueMapper.getCountByCatalogueParentId(ids);
        if (catalogueCount > 0) {
            throw new ServiceException("该目录下还有子目录,不能删除");
        }
        int count = iformationMapper.getCountByCatalogueId(ids);
        if (count > 0){
            throw new ServiceException("删除的目录存在资料,无法删除");
        }
        //释放目录权限

        return informationCatalogueMapper.deleteInformationCatalogueByIds(ids);
    }

    /**
     * 删除资料目录信息
     *
     * @param id 资料目录主键
     * @return 结果
     */
    @Override
    public int deleteInformationCatalogueById(Long id)
    {
        return informationCatalogueMapper.deleteInformationCatalogueById(id);
    }

    @Override
    public int getCountByCreateTime(String createTime)
    {
        return informationCatalogueMapper.getCountByCreateTime(createTime);
    }

    @Override
    public List<InformationCatalogue> selectInformationCatalogueByParentId(Long parentId)
    {
        InformationCatalogue informationCatalogue = new InformationCatalogue();
        informationCatalogue.setParentId(parentId);
//        SysUser user = SecurityUtils.getLoginUser().getUser();
//        informationCatalogue.setAuDeptId(user.getDeptId());
//        informationCatalogue.setAuUserId(user.getUserId());
//        List<Long> postIds = iSysPostService.selectPostListByUserId(user.getUserId());
//        informationCatalogue.setAuPostIds(postIds);
        return informationCatalogueMapper.selectInformationCatalogueByParentId(informationCatalogue);

    }


    public List<InformationCatalogueVO> getTreeListOfAuthority(InformationCatalogue informationCatalogue) {
        SysUser user = getLoginUser().getUser();
        //informationCatalogue.setAuDeptId(user.getDeptId());
//        informationCatalogue.setAuUserId(user.getUserId());
//        informationCatalogue.setCreateBy(user.getUserName());
//        //List<Long> postIds = iSysPostService.selectPostListByUserId(user.getUserId());
//        List<Long> postIds = iSysPostService.selectPostsByUserName(user.getUserName()).stream().map(SysPost::getPostId).collect(Collectors.toList());
//        List<Long> deptIds = iSysPostService.selectPostsByUserName(user.getUserName()).stream().map(SysPost::getDeptId).collect(Collectors.toList());
//        informationCatalogue.setAuPostIds(postIds);
//        informationCatalogue.setAuDeptIds(deptIds);
//
//        //查询有权限的父目录
//        List<InformationCatalogueVO> informationCatalogueVOList = informationCatalogueMapper.selectCatalogueListOfAuthority(informationCatalogue);
//        //得到有权限的父目录id,可能存在多个父目录同属相同公司(可能出现的特殊场景),但不一定有权限 最后去过滤
//        List<Long> catalogueIds = informationCatalogueVOList.stream().map(InformationCatalogueVO::getId).collect(Collectors.toList());
//
//        //得到有权限父目录的OrgId,查询所在公司的所有目录
//        List<Long> OrgIds = informationCatalogueVOList.stream().map(InformationCatalogueVO::getOrgId).collect(Collectors.toList());
//        informationCatalogue.setOrgIds(OrgIds);

        List<Long> comapnyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.SXINFORMATION.getCode());
        if (comapnyIds.isEmpty()) {
            return new ArrayList<>();
        }
        informationCatalogue.setAuCompanyIds(comapnyIds);
        List<InformationCatalogueVO> informationCatalogueVOList = informationCatalogueMapper.selectInformationCatalogueList(informationCatalogue);


        //当查询子目录名称时形成目录树
        if (!(informationCatalogue.getCatalogueName() == null || informationCatalogue.getCatalogueName().isEmpty())){
            //过滤复合模糊查询子目录id
            Set<Long> parentId = informationCatalogueVOList.stream()
                    .filter(catalogue -> catalogue.getCatalogueName().contains(informationCatalogue.getCatalogueName()))
                    .map(InformationCatalogueVO::getId)
                    .collect(Collectors.toSet());

            Set<Long> parentIds = new HashSet<>(parentId);
            //循环查询出子目录当上级目录id，每次循环查询出上一级目录，查询到最上一级时跳出循环
            while (!(parentId.contains(0L) || parentId.isEmpty())) {
                Set<Long> finalParentIds = parentId;

                List<InformationCatalogueVO> filteredCatalogues = informationCatalogueVOList.stream()
                        .filter(catalogue -> finalParentIds.contains(catalogue.getId()))
                        .collect(Collectors.toList());

                parentId = filteredCatalogues.stream()
                        .map(InformationCatalogueVO::getParentId)
                        .collect(Collectors.toSet());
                parentIds.addAll(parentId);
            }
            //查询出相关目录id，过滤掉多余的目录
            informationCatalogueVOList = informationCatalogueVOList.stream()
                    .filter(catalogue -> parentIds.contains(catalogue.getId()))
                    .collect(Collectors.toList());
        }

        // 父级，用于存放最终结果
        List<InformationCatalogueVO> fp = new ArrayList<>();
        // 筛选出的子集
        List<InformationCatalogueVO> fpson = new ArrayList<>();
        //筛选出所有目录id
        Set<Long> Ids = informationCatalogueVOList.stream().map(InformationCatalogueVO::getId).collect(Collectors.toSet());
        // 先提取出顶级目录和子集
        for (int i = 0; i < informationCatalogueVOList.size(); i++) {
            if (!Ids.contains(informationCatalogueVOList.get(i).getParentId()) ||  informationCatalogueVOList.get(i).getParentId() == null || 0L == informationCatalogueVOList.get(i).getParentId()) {
                fp.add(informationCatalogueVOList.get(i));
            } else {
                fpson.add(informationCatalogueVOList.get(i));
            }
        }
        // 从顶级目录开始，递归穿插数据
        for (int i = 0; i < fp.size(); i++) {
            getChildData(fp.get(i), fpson);
        }
        //用有权限父目录id做最后过滤
        return fp;
    }

    /**
     * @param fp    父集
     * @param fpson 子集
     */
    private void getChildData(InformationCatalogueVO fp, List<InformationCatalogueVO> fpson) {
        List<InformationCatalogueVO> stessoLive = new ArrayList<>();
        for (int j = 0; j < fpson.size(); j++) {
            // 如果是其子类，则存储在子类的list中，循环完统一set
            if (fpson.get(j).getParentId().equals(fp.getId())) {
                stessoLive.add(fpson.get(j));
                getChildData(fpson.get(j), fpson);
            }
        }
        // 设置子数据
        fp.setInformationCatalogueVOList(stessoLive);
    }

    /**
     * 根据ids查询目录
     * @param catalogueIds
     * @return
     */
    public List<InformationCatalogue> selectInformationCatalogueByIds(Set<Long> catalogueIds){
        return informationCatalogueMapper.selectInformationCatalogueByIds(catalogueIds);
    }

    public List<InformationCatalogueVO> selectCatalogueListOfAuthority(InformationCatalogue informationCatalogue){
        SysUser user = getLoginUser().getUser();
        informationCatalogue.setAuDeptId(user.getDeptId());
        informationCatalogue.setAuUserId(user.getUserId());
        informationCatalogue.setCreateBy(user.getUserName());
        List<Long> postIds = iSysPostService.selectPostListByUserId(user.getUserId());
        informationCatalogue.setAuPostIds(postIds);
        return informationCatalogueMapper.selectCatalogueListOfAuthority(informationCatalogue);
    }

}
