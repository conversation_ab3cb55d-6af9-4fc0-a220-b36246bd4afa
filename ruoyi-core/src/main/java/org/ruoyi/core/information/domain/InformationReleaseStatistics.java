package org.ruoyi.core.information.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 资料发放统计
 *
 */
@Data
public class InformationReleaseStatistics {

    /**
     * 所属组织(全量公司名称)
     */
    @Excel(name = "所属组织")
    private String company;

    /**
     * 所属组织ID
     */
    private Integer companyId;

    /**
     * 目录名称
     */
    @Excel(name = "目录")
    private String catalogueName;

    /**
     * 目录Id
     */
    private Long catalogueId;

    /**
     * 父级目录ID
     */
    private Long parentId;

    /**
     * 资料年度
     */
    @Excel(name = "年度")
    private String informationYear;

    /**
     * 资料类型
     */
    @Excel(name = "资料类型")
    private String informationType;

    /**
     * 资料ID
     */
    private String informationId;

    /**
     * 资料名称
     */
    @Excel(name = "资料名称")
    private String informationName;

    /**
     * 资料系统编号
     */
    @Excel(name = "资料编号")
    private String informationSystemCode;

    /**
     * 资料编号
     */
    private String informationCode;

    /**
     * 资方名称
     */
    @Excel(name = "资料获取方")
    private String theme;

    /**
     * 流程发起人
     */
    @Excel(name = "流程发起人")
    private String startPerson;

    /**
     * 流程发起人部门
     */
    private String startDept;

    /**
     * 附件名称
     */
    @Excel(name = "附件")
    private String fileName;

    /**
     * 附件地址
     */
    private String fileUrl;

    /**
     * 资料发放日期(下载审核通过日期)
     */
    @JsonFormat(pattern = "yyyy年MM月dd日 HH时mm分ss秒")
    @Excel(name = "资料发放日期", width = 30, dateFormat = "yyyy年MM月dd日 HH时mm分ss秒")
    private Date provideDate;

    /**
     * 资料发放开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 资料发放结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 是否已用印
     */
    @Excel(name = "是否已用印")
    private String sealFlag;

    /** 保存状态(0 非永久  1永久) */
    //@Excel(name = "保存状态")
    @NotNull(message = "保存状态不能为空")
    private String saveFlag;

    /** 保管开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "保管开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date saveStartTime;

    /** 保管结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "保管结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date saveEndTime;

    /**
     * 备注(来自资料)
     */
    private String remark;

    /**
     * 树形结构定位锚点
     */
    private String ancher;

    private List<InformationReleaseStatistics> sonData;


}
