package org.ruoyi.core.payrollFile.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 薪资保存对象 xzda_payrollfile_record
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
public class PayrollFileRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private String id;

    /** 薪资主表id */
    @Excel(name = "薪资主表id")
    private String payrollfileId;

    /** 调整前薪资 */
    @Excel(name = "调整前薪资")
    private BigDecimal oldRecordMoney;

    /** 调整后薪资 */
    @Excel(name = "调整后薪资")
    private BigDecimal newRecordMoney;

    /** 调薪种类 */
    @Excel(name = "调薪种类")
    private String kind;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 调整金额 */
    @Excel(name = "调整金额")
    private BigDecimal adjustmentMoney;

    /** 调整原因 */
    @Excel(name = "调整原因")
    private String adjustmentCause;

    /** 审核通过时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核通过时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /**
     * 发起人姓名
     */
    private String createByName;

    /**
     * 传递子表id
     */
    private String newRecordId;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 状态：0 正常，-1 草稿 1 提交 2 审核通过
     */
    private String state;
}
