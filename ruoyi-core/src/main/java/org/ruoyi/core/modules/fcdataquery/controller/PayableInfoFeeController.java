package org.ruoyi.core.modules.fcdataquery.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageUtils;
import org.ruoyi.core.modules.fcdataquery.po.PayableInfoFeeDetailPo;
import org.ruoyi.core.modules.fcdataquery.po.PayableInfoFeePo;
import org.ruoyi.core.modules.fcdataquery.service.IFcPayableInfoFeeService;
import org.ruoyi.core.modules.fcdataquery.vo.PayableInfoFeeDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.PayableInfoFeeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 应付信息费Controller
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/fc/payableInfoFee")
public class PayableInfoFeeController extends BaseController
{
    @Autowired
    private IFcPayableInfoFeeService fcPayableInfoFeeService;

    /**
     * 查询应付信息费列表
     */
    @PreAuthorize("@ss.hasPermi('fc:payableInfoFee:list')")
    @GetMapping("/list")
    public TableDataInfo list(PayableInfoFeeVo payableInfoFeeVo, int pageNum, int pageSize)
    {
        //查询应付信息费
        List<PayableInfoFeePo> payableInfoFeePoList = fcPayableInfoFeeService.selectPayableInfoFeeList(payableInfoFeeVo);
        PageInfo<PayableInfoFeePo> pageInfo = PageUtils.getPageInfo(pageNum, pageSize, payableInfoFeePoList);
        TableDataInfo tableDataInfo = getDataTable(pageInfo.getList());
        tableDataInfo.setTotal(pageInfo.getTotal());
        Map<Object, Object> map = new HashMap<>();
        //增加金额合计
        map.put("sumAddAmt", payableInfoFeePoList.stream().map(PayableInfoFeePo::getAddAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        //减少金额合计
        map.put("sumSubtractAmt", payableInfoFeePoList.stream().map(PayableInfoFeePo::getSubtractAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        //余额合计
        map.put("sumBalanceAmt", payableInfoFeePoList.stream().map(PayableInfoFeePo::getBalanceAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        tableDataInfo.setMap(map);
        return tableDataInfo;
    }

    /**
     * <AUTHOR>
     * @Description 获取应付信息费详情
     * @Date 2024/11/20 16:13
     * @Param [payableInfoFeeDetailVo, pageNum, pageSize]
     * @return com.ruoyi.common.core.page.TableDataInfo
     **/
    @PostMapping("/detail")
    public TableDataInfo payableInfoFeeDetail(@RequestBody PayableInfoFeeDetailVo payableInfoFeeDetailVo){
        List<PayableInfoFeeDetailPo> payableInfoFeeDetailPos = fcPayableInfoFeeService.getPayableInfoFeeDetail(payableInfoFeeDetailVo);
        PageInfo<PayableInfoFeeDetailPo> pageInfo = PageUtils.getPageInfo(payableInfoFeeDetailVo.getPageNum(), payableInfoFeeDetailVo.getPageSize(), payableInfoFeeDetailPos);
        TableDataInfo tableDataInfo = getDataTable(pageInfo.getList());
        tableDataInfo.setTotal(pageInfo.getTotal());
        return tableDataInfo;
    }

    /**
     * 导出应付信息费
     */
    @PreAuthorize("@ss.hasPermi('fc:payableInfoFee:export')")
    @Log(title = "经营分析-运营部-应付信息费导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public List<PayableInfoFeePo> export(@RequestBody PayableInfoFeeVo payableInfoFeeVo)
    {
        return fcPayableInfoFeeService.selectPayableInfoFeeList(payableInfoFeeVo);
    }
}
