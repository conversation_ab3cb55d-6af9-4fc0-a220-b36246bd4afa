package org.ruoyi.core.modules.fcdataquery.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.modules.fcdataquery.domain.FcManaulInput;
import org.ruoyi.core.modules.fcdataquery.po.FcManaulInputPo;
import org.ruoyi.core.modules.fcdataquery.po.ManaulInputPo;
import org.ruoyi.core.modules.fcdataquery.vo.FcManaulInputVo;

import java.util.List;

/**
 * 手工录入明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-27
 */
public interface FcManaulInputMapper 
{
    /**
     * 查询手工录入明细列表
     * 
     * @param fcManaulInputVo 手工录入明细
     * @return 手工录入明细集合
     */
    public List<FcManaulInputPo> selectFcManaulInputList(FcManaulInputVo fcManaulInputVo);

    /**
     * 新增手工录入明细
     * 
     * @param list 手工录入明细
     * @return 结果
     */
    public int insertFcManaulInput(List<FcManaulInput> list);

    /**
     * <AUTHOR>
     * @Description 根据项目编号删除数据
     * @Date 2024/11/27 11:17
     * @Param []
     * @return int
     **/
    int deleteByProjectId(Integer projectId);

    /**
     * <AUTHOR>
     * @Description 获取累计代偿数据
     * @Date 2024/11/27 15:37
     * @Param []
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.ManaulInputPo>
     **/
    List<ManaulInputPo> selectAccumCompensate(@Param("stsIdentify") String stsIdentify, @Param("endTime") String endTime);
}
