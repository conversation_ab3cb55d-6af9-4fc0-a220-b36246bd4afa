package org.ruoyi.core.modules.lawcoll.po;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 *@Authoer: huoruidong
 *@Description: 法律诉讼业务响应数据-安徽
 *@Date: 2023/7/20 10:30
 **/
@Data
public class LpAhPo {

    private Long id;

    /** 导入标识 */
    @JSONField(name = "importIdentify")
    private String importIdentify;

    /** 客户姓名 */
    @JSONField(name = "name")
    private String name;

    /** 借款申请编号 */
    @JSONField(name = "cApplyNo")
    private String cApplyNo;

    /** 回款途径 */
    @JSONField(name = "repaySrc")
    private String repaySrc;

    /** 系统回款日期 */
    @JSONField(name = "sRepayDate")
    private String sRepayDate;

    /** 对账单回款日期 */
    @JSONField(name = "cRepayDate")
    private String cRepayDate;

    /** 回款日期比对结果 */
    @JSONField(name = "repayDateResult")
    private Boolean repayDateResult = true;

    /** 对账单回款金额 */
    @JSONField(name = "cActRepayTotalAmt")
    private BigDecimal cActRepayTotalAmt;

    /** 系统回款金额 */
    @JSONField(name = "sActRepayTotalAmt")
    private String sActRepayTotalAmt;

    /** 回款金额比对结果 */
    @JSONField(name = "actRepayTotalAmtResult")
    private Boolean actRepayTotalAmtResult = true;

    /** 对账单批次号 */
    @JSONField(name = "cBatchNo")
    private String cBatchNo;

    /** 批次号 */
    @JSONField(name = "sBatchNo")
    private String sBatchNo;

    /** 批次号比对结果 */
    @JSONField(name = "batchNoResult")
    private Boolean batchNoResult = true;

    /** 对账单账龄 */
    @JSONField(name = "cAging")
    private String cAging;

    /** 回款情况账龄 */
    @JSONField(name = "hAging")
    private String hAging;

    /** 系统账龄 */
    @JSONField(name = "sAging")
    private String sAging;

    /** 回款情况账龄比对结果 */
    @JSONField(name = "hAgingResult")
    private Boolean hAgingResult = true;

    /** 系统账龄比对结果 */
    @JSONField(name = "sAgingResult")
    private Boolean sAgingResult = true;

    /** 对账单服务费费率 */
    @JSONField(name = "cServiceRate")
    private String cServiceRate;

    /** 系统服务费费率 */
    @JSONField(name = "sServiceRate")
    private String sServiceRate;

    /** 服务费费率比对结果 */
    @JSONField(name = "serviceRateResult")
    private Boolean serviceRateResult = true;

    /** 对账单服务费 */
    @JSONField(name = "cServiceAmt")
    private BigDecimal cServiceAmt;

    /** 系统服务费 */
    @JSONField(name = "sServiceAmt")
    private BigDecimal sServiceAmt;

    /** 服务费比对结果 */
    @JSONField(name = "serviceAmtResult")
    private Boolean serviceAmtResult = true;

    /** 收款方式 */
    @JSONField(name = "collectionType")
    private String collectionType;

    /** 账协议约定佣金比例 */
    @JSONField(name = "commissionRate")
    private String commissionRate;

    /** 甲方佣金 */
    @JSONField(name = "commission")
    private BigDecimal commission;
}
