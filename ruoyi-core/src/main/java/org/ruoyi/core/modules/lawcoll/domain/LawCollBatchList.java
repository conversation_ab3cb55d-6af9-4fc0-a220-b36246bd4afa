package org.ruoyi.core.modules.lawcoll.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 法催批次明细对象 law_coll_batch_list
 * 
 * <AUTHOR>
 * @date 2021-03-26
 */
public class LawCollBatchList extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** null */
    private Long pk;

    /** 委托日期 */
    @Excel(name = "委托日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date wtrq;

    /** null */
    private Long bpk;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 借款合同号 */
    @Excel(name = "借据号")
    private String loanNo;

    /** 资方 */
    @Excel(name = "资方", readConverterExp = "FBBK=富邦银行,BBWBK=北部湾银行", type = Excel.Type.EXPORT)
    private String fundCode;

    /** 执行金额 */
    @Excel(name = "主张金额")
    private BigDecimal execAmt;

    /** 状态 */
    private String status;

    /** 放款成功日期 */
    @Excel(name = "放款成功日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date encashTime;

    /** 姓名 */
    @Excel(name = "姓名", type = Excel.Type.EXPORT)
    private String name;

    /** 性别 */
    @Excel(name = "性别", type = Excel.Type.EXPORT)
    private String sex;

    /** 身份证号 */
    @Excel(name = "身份证号", type = Excel.Type.EXPORT)
    private String certNo;

    /** 身份证地址 */
    @Excel(name = "身份证地址", type = Excel.Type.EXPORT)
    private String mailFullAddr;

    /** 手机号 */
    @Excel(name = "手机号", type = Excel.Type.EXPORT)
    private String mobile;

    /** 放款金额 */
    @Excel(name = "放款金额", type = Excel.Type.EXPORT)
    private BigDecimal loanAmt;

    /** 总期数 */
    @Excel(name = "总期数", type = Excel.Type.EXPORT)
    private Long totalTerms;

    /** 借款用途 */
    @Excel(name = "借款用途", type = Excel.Type.EXPORT)
    private String loanUse;

    /** 日利率 */
    @Excel(name = "日利率", type = Excel.Type.EXPORT)
    private BigDecimal intRate;

    /** 代偿日期 */
    @Excel(name = "代偿日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date compensatoryDate;

    /** 代偿总金额 */
    @Excel(name = "代偿总金额", type = Excel.Type.EXPORT)
    private BigDecimal compensatoryTotalAmt;

    /** 代偿本金 */
    @Excel(name = "代偿本金" , type = Excel.Type.EXPORT)
    private BigDecimal compensatoryPrinAmt;

    /** 代偿利息 */
    @Excel(name = "代偿利息", type = Excel.Type.EXPORT)
    private BigDecimal compensatoryIntAmt;

    /** 代偿罚息 */
    @Excel(name = "代偿罚息", type = Excel.Type.EXPORT)
    private BigDecimal compensatoryOintAmt;

    private BigDecimal repayAmt;

    public void setEncashTime(Date encashTime)
    {
        this.encashTime = encashTime;
    }

    public BigDecimal getRepayAmt() {
        return repayAmt;
    }

    public void setRepayAmt(BigDecimal repayAmt) {
        this.repayAmt = repayAmt;
    }

    public Date getEncashTime()
    {
        return encashTime;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getSex()
    {
        return sex;
    }
    public void setCertNo(String certNo)
    {
        this.certNo = certNo;
    }

    public String getCertNo()
    {
        return certNo;
    }
    public void setMailFullAddr(String mailFullAddr)
    {
        this.mailFullAddr = mailFullAddr;
    }

    public String getMailFullAddr()
    {
        return mailFullAddr;
    }
    public void setMobile(String mobile)
    {
        this.mobile = mobile;
    }

    public String getMobile()
    {
        return mobile;
    }
    public void setLoanAmt(BigDecimal loanAmt)
    {
        this.loanAmt = loanAmt;
    }

    public BigDecimal getLoanAmt()
    {
        return loanAmt;
    }
    public void setTotalTerms(Long totalTerms)
    {
        this.totalTerms = totalTerms;
    }

    public Long getTotalTerms()
    {
        return totalTerms;
    }
    public void setLoanUse(String loanUse)
    {
        this.loanUse = loanUse;
    }

    public String getLoanUse()
    {
        return loanUse;
    }
    public void setIntRate(BigDecimal intRate)
    {
        this.intRate = intRate;
    }

    public BigDecimal getIntRate()
    {
        return intRate;
    }
    public void setCompensatoryDate(Date compensatoryDate)
    {
        this.compensatoryDate = compensatoryDate;
    }

    public Date getCompensatoryDate()
    {
        return compensatoryDate;
    }
    public void setCompensatoryTotalAmt(BigDecimal compensatoryTotalAmt)
    {
        this.compensatoryTotalAmt = compensatoryTotalAmt;
    }

    public BigDecimal getCompensatoryTotalAmt()
    {
        return compensatoryTotalAmt;
    }
    public void setCompensatoryPrinAmt(BigDecimal compensatoryPrinAmt)
    {
        this.compensatoryPrinAmt = compensatoryPrinAmt;
    }

    public BigDecimal getCompensatoryPrinAmt()
    {
        return compensatoryPrinAmt;
    }
    public void setCompensatoryIntAmt(BigDecimal compensatoryIntAmt)
    {
        this.compensatoryIntAmt = compensatoryIntAmt;
    }

    public BigDecimal getCompensatoryIntAmt()
    {
        return compensatoryIntAmt;
    }
    public void setCompensatoryOintAmt(BigDecimal compensatoryOintAmt)
    {
        this.compensatoryOintAmt = compensatoryOintAmt;
    }

    public BigDecimal getCompensatoryOintAmt()
    {
        return compensatoryOintAmt;
    }
    public void setPk(Long pk)
    {
        this.pk = pk;
    }

    public Long getPk() 
    {
        return pk;
    }
    public void setBatchNo(String batchNo) 
    {
        this.batchNo = batchNo;
    }

    public String getBatchNo() 
    {
        return batchNo;
    }
    public void setLoanNo(String loanNo) 
    {
        this.loanNo = loanNo;
    }

    public String getLoanNo() 
    {
        return loanNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public void setExecAmt(BigDecimal execAmt)
    {
        this.execAmt = execAmt;
    }

    public BigDecimal getExecAmt() 
    {
        return execAmt;
    }

    public Long getBpk() {
        return bpk;
    }

    public void setBpk(Long bpk) {
        this.bpk = bpk;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getWtrq() {
        return wtrq;
    }

    public void setWtrq(Date wtrq) {
        this.wtrq = wtrq;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("pk", getPk())
            .append("batchNo", getBatchNo())
            .append("loanNo", getLoanNo())
            .append("fundCode", getFundCode())
            .append("execAmt", getExecAmt())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
