package org.ruoyi.core.modules.fcdataquery.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.modules.fcdataquery.domain.FcProjectSts;
import org.ruoyi.core.modules.fcdataquery.mapper.FcProjectStsMapper;
import org.ruoyi.core.modules.fcdataquery.service.IFcProjectStsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 财务项目日统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-26
 */
@Service
public class FcProjectStsServiceImpl implements IFcProjectStsService
{
    @Autowired
    private FcProjectStsMapper fcProjectStsMapper;

    /**
     * 新增财务项目日统计
     * 
     * @param fcProjectSts 财务项目日统计
     * @return 结果
     */
    @Override
    public int insertFcProjectSts(FcProjectSts fcProjectSts)
    {
        fcProjectSts.setCreateTime(DateUtils.getNowDate());
        return fcProjectStsMapper.insertFcProjectSts(fcProjectSts);
    }

    /**
     * 修改财务项目日统计
     * 
     * @param fcProjectSts 财务项目日统计
     * @return 结果
     */
    @Override
    public int updateFcProjectSts(FcProjectSts fcProjectSts)
    {
        fcProjectSts.setUpdateTime(DateUtils.getNowDate());
        return fcProjectStsMapper.updateFcProjectSts(fcProjectSts);
    }
}
