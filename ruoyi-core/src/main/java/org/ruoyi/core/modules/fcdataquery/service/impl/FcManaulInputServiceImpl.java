package org.ruoyi.core.modules.fcdataquery.service.impl;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.modules.fcdataquery.domain.FcManaulInput;
import org.ruoyi.core.modules.fcdataquery.mapper.FcManaulInputMapper;
import org.ruoyi.core.modules.fcdataquery.po.FcManaulInputPo;
import org.ruoyi.core.modules.fcdataquery.service.IFcManaulInputService;
import org.ruoyi.core.modules.fcdataquery.vo.FcManaulInputVo;
import org.ruoyi.core.modules.fcdataquery.vo.ManaulInputDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.ManaulInputVo;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.mapper.OaProjectDeployMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 手工录入明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-27
 */
@Service
public class FcManaulInputServiceImpl implements IFcManaulInputService
{
    @Autowired
    private FcManaulInputMapper fcManaulInputMapper;
    @Autowired
    private OaProjectDeployMapper oaProjectDeployMapper;


    /**
     * 查询手工录入明细列表
     * 
     * @param fcManaulInputVo 手工录入明细
     * @return 手工录入明细
     */
    @Override
    public List<FcManaulInputPo> selectFcManaulInputList(FcManaulInputVo fcManaulInputVo)
    {
        return fcManaulInputMapper.selectFcManaulInputList(fcManaulInputVo);
    }

    /**
     * 新增手工录入明细
     * 
     * @param manaulInputVo 手工录入明细
     * @return 结果
     */
    @Override
    public int insertFcManaulInput(ManaulInputVo manaulInputVo)
    {
        //如果之前已经录入过该项目的明细则先删除再重新录入
        FcManaulInputVo fmInput = new FcManaulInputVo();
        fmInput.setProjectId(manaulInputVo.getProjectId());
        List<FcManaulInputPo> fcManaulInputsList = selectFcManaulInputList(fmInput);
        if(!CollectionUtils.isEmpty(fcManaulInputsList)){
            fcManaulInputMapper.deleteByProjectId(manaulInputVo.getProjectId());
        }
        //查询项目信息
        OaProjectDeploy oaProjectDeploy = oaProjectDeployMapper.selectOaProjectDeployById(Long.valueOf(manaulInputVo.getProjectId()));
        List<FcManaulInput> fcManaulInputs = new ArrayList<>();
        for (ManaulInputDetailVo detailVo: manaulInputVo.getList()) {
            FcManaulInput fcManaulInput = new FcManaulInput();
            BeanUtils.copyProperties(detailVo, fcManaulInput);
            fcManaulInput.setProjectId(manaulInputVo.getProjectId());//项目id
            fcManaulInput.setProjectType(oaProjectDeploy.getProjectType());//项目类型
            fcManaulInput.setProjectName(oaProjectDeploy.getProjectName());//项目名称
            String[] split = detailVo.getDataDay().split("-");
            fcManaulInput.setDataYear(split[0]);//数据年
            fcManaulInput.setDataQuarter(DateUtils.getQuarter(detailVo.getDataDay()));//数据季度
            fcManaulInput.setDataMonth(DateUtils.getMonthForDate(detailVo.getDataDay()));//数据月
            fcManaulInput.setDebitAmount(detailVo.getActCompensateAmt());//实际代偿款
            fcManaulInputs.add(fcManaulInput);
        }
        //添加手动录入明细
        return fcManaulInputMapper.insertFcManaulInput(fcManaulInputs);
    }
}
