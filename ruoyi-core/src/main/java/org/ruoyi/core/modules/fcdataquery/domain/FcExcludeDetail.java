package org.ruoyi.core.modules.fcdataquery.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 财务剔除明细对象 fc_exclude_detail
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public class FcExcludeDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long voucherId;

    /** 凭证日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "凭证日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date voucherDate;

    /** 凭证字 */
    @Excel(name = "凭证字")
    private String word;

    /** 摘要 */
    @Excel(name = "摘要")
    private String summary;

    /** 借方金额 */
    @Excel(name = "借方金额")
    private BigDecimal debitAmount;

    /** 贷方金额 */
    @Excel(name = "贷方金额")
    private BigDecimal creditAmount;

    /** 制单人 */
    @Excel(name = "制单人")
    private Long createMember;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setVoucherId(Long voucherId) 
    {
        this.voucherId = voucherId;
    }

    public Long getVoucherId() 
    {
        return voucherId;
    }
    public void setVoucherDate(Date voucherDate) 
    {
        this.voucherDate = voucherDate;
    }

    public Date getVoucherDate() 
    {
        return voucherDate;
    }
    public void setWord(String word) 
    {
        this.word = word;
    }

    public String getWord() 
    {
        return word;
    }
    public void setSummary(String summary) 
    {
        this.summary = summary;
    }

    public String getSummary() 
    {
        return summary;
    }
    public void setDebitAmount(BigDecimal debitAmount) 
    {
        this.debitAmount = debitAmount;
    }

    public BigDecimal getDebitAmount() 
    {
        return debitAmount;
    }
    public void setCreditAmount(BigDecimal creditAmount) 
    {
        this.creditAmount = creditAmount;
    }

    public BigDecimal getCreditAmount() 
    {
        return creditAmount;
    }
    public void setCreateMember(Long createMember) 
    {
        this.createMember = createMember;
    }

    public Long getCreateMember() 
    {
        return createMember;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("voucherId", getVoucherId())
            .append("voucherDate", getVoucherDate())
            .append("word", getWord())
            .append("summary", getSummary())
            .append("debitAmount", getDebitAmount())
            .append("creditAmount", getCreditAmount())
            .append("createMember", getCreateMember())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
