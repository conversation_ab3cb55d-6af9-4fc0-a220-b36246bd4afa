package org.ruoyi.core.modules.lawcoll.excel.importE;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 法催对账历史对象 law_coll_check_history
 * 
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
public class ImportLpRepay
{
    @ExcelIgnore
    private String sheetName;

    @ExcelProperty("借据编号")
    private String applyNo;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("真实回款金额（元）")
    private BigDecimal actRepayTotalAmt;

    @ExcelProperty("回款诉讼费（元）")
    private BigDecimal costsAmt;

    @ExcelProperty("回款日期")
    private String repayDate;

    @ExcelProperty("回款途径")
    private String repaySrc;

    @ExcelProperty("委托逾期天数")
    private Integer ovdDays;

    @ExcelProperty("批次")
    private String batchNo;
}
