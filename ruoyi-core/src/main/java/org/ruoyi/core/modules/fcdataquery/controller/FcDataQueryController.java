package org.ruoyi.core.modules.fcdataquery.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.modules.fcdataquery.po.AccountSetsInfoPo;
import org.ruoyi.core.modules.fcdataquery.po.BeneficiaryInfoPo;
import org.ruoyi.core.modules.fcdataquery.po.ChannelInfoPo;
import org.ruoyi.core.modules.fcdataquery.po.CompanyTypePo;
import org.ruoyi.core.modules.fcdataquery.service.IFcDataQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 经营分析Controller
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/fc/dataquery")
public class FcDataQueryController extends BaseController
{
    @Autowired
    private IFcDataQueryService fcDataQueryService;

    /**
     * <AUTHOR>
     * @Description 获取公司类型
     * @Date 2024/12/13 16:13
     * @Param []
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.CompanyTypePo>
     **/
    @GetMapping("/getCompanyType")
    public List<CompanyTypePo> selectCompanyType()
    {
        List<CompanyTypePo> companyTypePoList = fcDataQueryService.selectCompanyType();
        return companyTypePoList;
    }

    /**
     * <AUTHOR>
     * @Description 获取渠道信息
     * @Date 2024/12/26 9:41
     * @Param []
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.ChannelInfoPo>
     **/
    @GetMapping("/getChannel")
    public List<ChannelInfoPo> selectChannelInfo()
    {
        List<ChannelInfoPo> channelInfoPoList = fcDataQueryService.selectChannelInfo();
        return channelInfoPoList;
    }

    /**
     * <AUTHOR>
     * @Description 获取收款单位信息
     * @Date 2024/12/26 14:12
     * @Param [type]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.BeneficiaryInfoPo>
     **/
    @GetMapping("/getBeneficiary")
    public List<BeneficiaryInfoPo> selectBeneficiaryInfo(Integer type)
    {
        List<BeneficiaryInfoPo> beneficiaryInfoList = fcDataQueryService.selectBeneficiaryInfo(type);
        return beneficiaryInfoList;
    }

    /**
     * <AUTHOR>
     * @Description 获取账套信息
     * @Date 2024/12/26 14:13
     * @Param []
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.AccountSetsInfoPo>
     **/
    @GetMapping("/getAccountSets")
    public AjaxResult selectAccountSets()
    {
        List<AccountSetsInfoPo> accountSetsInfoPos = fcDataQueryService.selectAccountSets();
        return AjaxResult.success(accountSetsInfoPos);
    }
}
