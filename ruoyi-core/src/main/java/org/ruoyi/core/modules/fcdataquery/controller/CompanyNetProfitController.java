package org.ruoyi.core.modules.fcdataquery.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageUtils;
import org.ruoyi.core.modules.fcdataquery.po.CompanyNetProfitPo;
import org.ruoyi.core.modules.fcdataquery.service.IFcCompanyNetProfitService;
import org.ruoyi.core.modules.fcdataquery.vo.CompanyNetProfitVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司净利润Controller
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/fc/companyNetProfit")
public class CompanyNetProfitController extends BaseController
{
    @Autowired
    private IFcCompanyNetProfitService fcCompanyNetProfitService;

    /**
     * 查询公司净利润
     */
    @PreAuthorize("@ss.hasPermi('fc:companyNetProfit:list')")
    @GetMapping("/list")
    public TableDataInfo list(CompanyNetProfitVo companyNetProfitVo, int pageNum, int pageSize)
    {
        List<CompanyNetProfitPo> companyNetProfitPoList = fcCompanyNetProfitService.selectCompanyNetProfitList(companyNetProfitVo);
        PageInfo<CompanyNetProfitPo> pageInfo = PageUtils.getPageInfo(pageNum, pageSize, companyNetProfitPoList);
        TableDataInfo tableDataInfo = getDataTable(pageInfo.getList());
        Map<Object, Object> map = new HashMap<>();
        map.put("sumTotalAmt", companyNetProfitPoList.stream().map(CompanyNetProfitPo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        tableDataInfo.setMap(map);
        return tableDataInfo;
    }

    /**
     * 导出公司净利润列表
     */
    @PreAuthorize("@ss.hasPermi('fc:companyNetProfit:export')")
    @Log(title = "经营分析-项目收入-公司净利润导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public List<CompanyNetProfitPo> export(@RequestBody CompanyNetProfitVo companyNetProfitVo)
    {
        return fcCompanyNetProfitService.selectCompanyNetProfitList(companyNetProfitVo);
    }
}
