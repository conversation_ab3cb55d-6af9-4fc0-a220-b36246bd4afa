package org.ruoyi.core.modules.lawcoll.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.ruoyi.core.modules.lawcoll.domain.LawCollBatchList;
import org.ruoyi.core.modules.lawcoll.domain.LawCollCheck;
import org.ruoyi.core.modules.lawcoll.domain.LawCollCheckDetail;
import org.ruoyi.core.modules.lawcoll.domain.LawCollRepay;
import org.ruoyi.core.modules.lawcoll.enums.FundCodeEnum;
import org.ruoyi.core.modules.lawcoll.enums.RepayStatusEnum;
import org.ruoyi.core.modules.lawcoll.excel.exportE.ExportEm;
import org.ruoyi.core.modules.lawcoll.excel.importE.ImportEm;
import org.ruoyi.core.modules.lawcoll.excel.listener.ImportEmListener;
import org.ruoyi.core.modules.lawcoll.mapper.LawCollBatchListMapper;
import org.ruoyi.core.modules.lawcoll.mapper.LawCollCheckDetailMapper;
import org.ruoyi.core.modules.lawcoll.mapper.LawCollCheckMapper;
import org.ruoyi.core.modules.lawcoll.mapper.LawCollRepayMapper;
import org.ruoyi.core.modules.lawcoll.po.EmPo;
import org.ruoyi.core.modules.lawcoll.po.EntrustedMediationPo;
import org.ruoyi.core.modules.lawcoll.service.ILawCollEmService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 法催对账历史Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-19
 */
@Slf4j
@Service
public class LawCollEmServiceImpl implements ILawCollEmService
{
    @Autowired
    private LawCollRepayMapper lawCollRepayMapper;

    @Autowired
    private LawCollBatchListMapper lawCollBatchListMapper;

    @Autowired
    private LawCollCheckMapper lawCollCheckMapper;

    @Autowired
    private LawCollCheckDetailMapper lawCollCheckDetailMapper;


    /**
     * 查询委托调解业务数据
     *
     * @param importIdentify 导入唯一标识
     * @return 法催对账历史
     */
    @Override
    public List<EntrustedMediationPo> selectEmByImportIdentify(String importIdentify)
    {
        //查询导入记录
        List<LawCollCheckDetail> lawCollCheckList = lawCollCheckDetailMapper.selectByImportIdentify(importIdentify);
        List<EntrustedMediationPo> emResList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(lawCollCheckList)){
            //按照sheet页分组
            Map<String, List<LawCollCheckDetail>> dataGroupMap = lawCollCheckList.stream().collect(Collectors.groupingBy(LawCollCheckDetail::getSheetName));
            int i = 0;
            for (String sheetName: dataGroupMap.keySet()) {
                i++;
                //子列表
                List<LawCollCheckDetail> childCheckList = dataGroupMap.get(sheetName);

                EntrustedMediationPo entrustedMediationPo = new EntrustedMediationPo();
                //缺失借据
                List<String> missApplyNos = new ArrayList<>();
                //响应数据
                List<EmPo> dataList = new ArrayList<>();
                for (LawCollCheckDetail lawCollCheckDetail : childCheckList) {
                    if(StringUtils.isNotBlank(lawCollCheckDetail.getIsMiss())
                            && lawCollCheckDetail.getIsMiss().equals("0")){
                        missApplyNos.add(lawCollCheckDetail.getcApplyNo());
                        continue;
                    }
                    EmPo emPo = new EmPo();
                    BeanUtils.copyProperties(lawCollCheckDetail, emPo);
                    //合计单独处理
                    if(lawCollCheckDetail.getcApplyNo().equals("合计")){
                        emPo.setSRepayTotalAmt(lawCollCheckDetail.getsRepayTotalAmt().toString());
                        emPo.setFee3Amt(lawCollCheckDetail.getFee3Amt().toString());
                        //合计结果比对
                        sumCompare(lawCollCheckDetail, emPo);
                        dataList.add(emPo);
                        continue;
                    }
                    if(StringUtils.isBlank(lawCollCheckDetail.getsApplyNo())){
                        emPo.setSRepayTotalAmt("无该借据信息");
                        emPo.setFee3Amt("无该借据信息");
                        emPo.setFee3AmtResult(false);
                        emPo.setRepayDateResult(false);
                        emPo.setRepayTotalAmtResult(false);
                        emPo.setRepayPrinAmtResult(false);
                        emPo.setOvdDaysResult(false);
                        emPo.setServiceRateResult(false);
                        emPo.setServiceAmtResult(false);
                        emPo.setFundCodeResult(false);
                        dataList.add(emPo);
                        continue;
                    }
                    if(null == lawCollCheckDetail.getsRepayDate()){
                        emPo.setSRepayDate("无该日期数据");
                        emPo.setFee3Amt("0.00");
                        emPo.setRepayDateResult(false);
                        emPo.setRepayTotalAmtResult(false);
                        emPo.setRepayPrinAmtResult(false);
                        emPo.setOvdDaysResult(false);
                        emPo.setServiceRateResult(false);
                        emPo.setServiceAmtResult(false);
                        emPo.setFundCodeResult(false);
                        dataList.add(emPo);
                        continue;
                    }
                    //分润金额
                    emPo.setFee3Amt(lawCollCheckDetail.getFee3Amt().toString());

                    //回款金额=相同日期的还款总金额相加
                    emPo.setSRepayTotalAmt(lawCollCheckDetail.getsRepayTotalAmt().toString());
                    if(null == lawCollCheckDetail.getsRepayTotalAmt() || lawCollCheckDetail.getsRepayTotalAmt().compareTo(lawCollCheckDetail.getcRepayTotalAmt())!=0){
                        emPo.setRepayTotalAmtResult(false);
                    }
                    //回款本金=相同日期的还款本金相加
                    if(null == lawCollCheckDetail.getsRepayPrinAmt() || lawCollCheckDetail.getsRepayPrinAmt().compareTo(lawCollCheckDetail.getcRepayPrinAmt())!=0){
                        emPo.setRepayPrinAmtResult(false);
                    }
                    //逾期天数=委托日期-代偿日期+代偿天数（资金方如果是富邦银行则为60，北部湾银行则为50）
                    if(null == lawCollCheckDetail.getsOvdDays() || !lawCollCheckDetail.getsOvdDays().equals(lawCollCheckDetail.getcOvdDays())){
                        emPo.setOvdDaysResult(false);
                    }
                    //服务费费率
                    if(null == lawCollCheckDetail.getsServiceRate() || !lawCollCheckDetail.getsServiceRate().equals(lawCollCheckDetail.getcServiceRate())){
                        emPo.setServiceRateResult(false);
                    }
                    //服务费=回款金额*服务费费率
                    if(null == lawCollCheckDetail.getsServiceAmt() || lawCollCheckDetail.getsServiceAmt().compareTo(lawCollCheckDetail.getcServiceAmt())!=0){
                        emPo.setServiceAmtResult(false);
                    }
                    //资金方
                    if(null == lawCollCheckDetail.getsFundCode() || !lawCollCheckDetail.getcFundCode().equals(lawCollCheckDetail.getsFundCode())){
                        emPo.setFundCodeResult(false);
                    }
                    //代偿日期
                    if(!lawCollCheckDetail.getcCompensatoryDate().equals(lawCollCheckDetail.getsCompensatoryDate())){
                        emPo.setCompensatoryDateResult(false);
                    }
                    dataList.add(emPo);
                }
                entrustedMediationPo.setImportIdentify(childCheckList.get(0).getImportIdentify());
                entrustedMediationPo.setSheetIndex("sheet"+i);
                entrustedMediationPo.setSheetName(sheetName);
                entrustedMediationPo.setDataList(dataList);
                entrustedMediationPo.setDifferentTotal(calDifferentEm(dataList));
                entrustedMediationPo.setMissApplyNos(String.join(", ", missApplyNos));
                emResList.add(entrustedMediationPo);
            }
        }
        return emResList;
    }


    /**
     * <AUTHOR>
     * @Description 导入委托调解对账单
     * @Date 2023/7/19 15:45
     * @Param [multipartFile]
     * @return void
     **/
    @Override
    public List<EntrustedMediationPo> importEm(MultipartFile file) {
        LawCollCheck lawCollCheck = new LawCollCheck();
        lawCollCheck.setCheckDate(new Date());//核对日期
        lawCollCheck.setImportType("0");//委托调解
        lawCollCheck.setImportIdentify(IdUtils.simpleUUID());//导入标识
        lawCollCheck.setIsSave("1");//是否保存 0是 1否
        lawCollCheck.setCheckFile(file.getOriginalFilename());//对账文件
        lawCollCheck.setCreateBy(SecurityUtils.getUsername());//创建人
        lawCollCheck.setRemark("");
        try {
            //插入导入记录
            lawCollCheckMapper.insertLawCollCheck(lawCollCheck);

            ImportEmListener importEmListener = new ImportEmListener(lawCollCheck, lawCollCheckDetailMapper);
            EasyExcel.read(file.getInputStream(), ImportEm.class, importEmListener)
                    .ignoreEmptyRow(true)//过滤空行
                    .doReadAll();//读取所有sheet页
            //合并临时数据后插入明细表
            lawCollCheckDetailMapper.insertLawCollCheckDetailEm();
            //清除临时表数据
            lawCollCheckDetailMapper.clearTempData();
            //处理导入数据
            return handleReadDataEm(lawCollCheck);
        } catch (Exception e) {
            log.error("读取委托调解对账单异常");
            e.printStackTrace();
            //清空临时表数据
            lawCollCheckDetailMapper.clearTempData();
            //清空导入数据
            lawCollCheckDetailMapper.clearData(lawCollCheck.getImportIdentify());
            lawCollCheckMapper.deleteByImportIdentify(lawCollCheck.getImportIdentify());
            throw new ServiceException(e.getMessage(), 200);
        }
    }

    /**
     * <AUTHOR>
     * @Description 处理委托调解对账数据
     * @Date 2023/7/27 16:24
     * @Param [lawCollCheck]
     * @return org.ruoyi.core.modules.lawcoll.po.EntrustedMediationPo
     **/
    private List<EntrustedMediationPo> handleReadDataEm(LawCollCheck lawCollCheck){
        List<LawCollCheckDetail> lawCollCheckList = lawCollCheckDetailMapper.selectByImportIdentify(lawCollCheck.getImportIdentify());
        List<EntrustedMediationPo> emResList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(lawCollCheckList)){
            //按照sheet页分组
            Map<String, List<LawCollCheckDetail>> dataGroupMap = lawCollCheckList.stream().collect(Collectors.groupingBy(LawCollCheckDetail::getSheetName));
            int i = 0;
            for (String sheetName: dataGroupMap.keySet()) {
                i++;
                //子列表
                List<LawCollCheckDetail> childCheckList = dataGroupMap.get(sheetName);
                EntrustedMediationPo entrustedMediationPo = new EntrustedMediationPo();
                //缺失借据
                List<String> missApplyNos = new ArrayList<>();
                //响应数据
                List<EmPo> dataList = new ArrayList<>();
                for (LawCollCheckDetail lawCollCheckDetail : childCheckList) {
                    EmPo emPo = new EmPo();
                    String applyNo = lawCollCheckDetail.getcApplyNo();

                    //是否存在该借据信息
                    List<LawCollRepay> lawCollRepays = lawCollRepayMapper.selectLcRepayByLoanNo(applyNo);
                    if(CollectionUtils.isEmpty(lawCollRepays)|| null == lawCollRepays.get(0)){
                        BeanUtils.copyProperties(lawCollCheckDetail, emPo);
                        emPo.setSRepayTotalAmt("无该借据信息");
                        emPo.setFee3Amt("无该借据信息");
                        emPo.setFee3AmtResult(false);
                        emPo.setRepayDateResult(false);
                        emPo.setRepayTotalAmtResult(false);
                        emPo.setRepayPrinAmtResult(false);
                        emPo.setOvdDaysResult(false);
                        emPo.setServiceRateResult(false);
                        emPo.setServiceAmtResult(false);
                        emPo.setFundCodeResult(false);
                        dataList.add(emPo);
                        continue;
                    }
                    //系统借据号
                    lawCollCheckDetail.setsApplyNo(applyNo);
                    //是否存在该日期数据
                    LawCollRepay lawCollRepay = lawCollRepayMapper.selectLcRepayByLoanNoAndRepayTime(applyNo, lawCollCheckDetail.getcRepayDate());
                    if(null == lawCollRepay){
                        BeanUtils.copyProperties(lawCollCheckDetail, emPo);
                        emPo.setFee3Amt("0.00");
                        emPo.setSRepayDate("无该日期数据");
                        emPo.setRepayDateResult(false);
                        emPo.setRepayTotalAmtResult(false);
                        emPo.setRepayPrinAmtResult(false);
                        emPo.setOvdDaysResult(false);
                        emPo.setServiceRateResult(false);
                        emPo.setServiceAmtResult(false);
                        emPo.setFundCodeResult(false);
                        dataList.add(emPo);

                        //更新对账明细数据
                        lawCollCheckDetailMapper.updateLawCollCheckDetail(null, lawCollCheckDetail);
                        continue;
                    }
                    //分润金额
                    lawCollCheckDetail.setFee3Amt(lawCollRepay.getFee3Amt());
                    emPo.setFee3Amt(lawCollCheckDetail.getFee3Amt().toString());

                    //回款日期比较
                    Date cRepayDate = DateUtils.parseDate(lawCollCheckDetail.getcRepayDate());//对账单回款日期
                    if(cRepayDate.compareTo(lawCollRepay.getRepayTime())==0){//回款日期相同
                        lawCollCheckDetail.setsRepayDate(DateUtils.parseDateToStr("yyyy-MM-dd", lawCollRepay.getRepayTime()));
                    }else{//excel中找不到消金系统存在的日期
                        lawCollCheckDetail.setIsMiss("0");//缺失记录
                        lawCollCheckDetailMapper.updateLawCollCheckDetail(null, lawCollCheckDetail);//更新对账明细
                        missApplyNos.add(applyNo);
                        continue;
                    }

                    //回款金额=相同日期的还款总金额相加
                    lawCollCheckDetail.setsRepayTotalAmt(lawCollRepay.getTotalAmt());//系统回款金额
                    emPo.setSRepayTotalAmt(lawCollCheckDetail.getsRepayTotalAmt().toString());
                    if(lawCollCheckDetail.getsRepayTotalAmt().compareTo(lawCollCheckDetail.getcRepayTotalAmt())!=0){
                        emPo.setRepayTotalAmtResult(false);
                    }
                    //回款本金=相同日期的还款本金相加
                    lawCollCheckDetail.setsRepayPrinAmt(lawCollRepay.getPrinAmt());//系统回款本金
                    if(lawCollCheckDetail.getsRepayPrinAmt().compareTo(lawCollCheckDetail.getcRepayPrinAmt())!=0){
                        emPo.setRepayPrinAmtResult(false);
                    }

                    //代偿日期
                    LawCollBatchList lawCollBatchList = lawCollBatchListMapper.selectLawCollBatchListByLoanNo(applyNo);
                    Date encrustDate = DateUtils.parseDate(lawCollCheckDetail.getcBatchNo().substring(0, 8));//委托日期=对账单批次前8个字符
                    Date compensatoryDate = lawCollBatchList.getCompensatoryDate();//案件管理-代偿日期
                    lawCollCheckDetail.setsCompensatoryDate(DateUtils.parseDateToStr("yyyy-MM-dd", compensatoryDate));
                    if(!lawCollCheckDetail.getcCompensatoryDate().equals(lawCollCheckDetail.getsCompensatoryDate())){
                        emPo.setCompensatoryDateResult(false);
                    }

                    //逾期天数=委托日期-代偿日期+代偿天数（资金方如果是富邦银行则为60，北部湾银行则为50）
                    int intervalDays = DateUtils.getIntervalDays(encrustDate, compensatoryDate);
                    int compensateDays = lawCollBatchList.getFundCode().equals("FBBK")?60:50;
                    int sOvdDays = intervalDays + compensateDays;
                    lawCollCheckDetail.setsOvdDays(sOvdDays);
                    if(!lawCollCheckDetail.getsOvdDays().equals(lawCollCheckDetail.getcOvdDays())){
                        emPo.setOvdDaysResult(false);
                    }
                    //服务费费率
                    if(sOvdDays>=1 && sOvdDays<=360){
                        lawCollCheckDetail.setsServiceRate("30%");
                    }else if(sOvdDays>360 && sOvdDays<=720){
                        lawCollCheckDetail.setsServiceRate("35%");
                    }else if(sOvdDays>720){
                        lawCollCheckDetail.setsServiceRate("40%");
                    }
                    if(!lawCollCheckDetail.getsServiceRate().equals(lawCollCheckDetail.getcServiceRate())){
                        emPo.setServiceRateResult(false);
                    }
                    //服务费=回款金额*服务费费率
                    BigDecimal serviceRate = new BigDecimal(lawCollCheckDetail.getsServiceRate().replace("%", "")).divide(new BigDecimal(100));
                    lawCollCheckDetail.setsServiceAmt(lawCollCheckDetail.getsRepayTotalAmt().multiply(serviceRate).setScale(2, RoundingMode.HALF_UP));
                    if(lawCollCheckDetail.getsServiceAmt().compareTo(lawCollCheckDetail.getcServiceAmt())!=0){
                        emPo.setServiceAmtResult(false);
                    }
                    //资金方
                    lawCollCheckDetail.setsFundCode(lawCollBatchList.getFundCode());
                    if(!lawCollCheckDetail.getcFundCode().equals(lawCollCheckDetail.getsFundCode())){
                        emPo.setFundCodeResult(false);
                    }

                    //更新对账明细数据
                    lawCollCheckDetailMapper.updateLawCollCheckDetail(null, lawCollCheckDetail);

                    BeanUtils.copyProperties(lawCollCheckDetail, emPo);
                    dataList.add(emPo);
                }
                //添加合计
                EmPo emPo = addSumEm(lawCollCheck, sheetName, childCheckList);
                dataList.add(emPo);

                entrustedMediationPo.setImportIdentify(childCheckList.get(0).getImportIdentify());
                entrustedMediationPo.setSheetIndex("sheet"+i);
                entrustedMediationPo.setSheetName(sheetName);
                entrustedMediationPo.setDataList(dataList);
                entrustedMediationPo.setDifferentTotal(calDifferentEm(dataList));
                entrustedMediationPo.setMissApplyNos(String.join(", ", missApplyNos));
                emResList.add(entrustedMediationPo);
            }
        }
        return emResList;
    }
    /**
     * <AUTHOR>
     * @Description 计算有多少笔不借据结果不一致
     * @Date 2024/4/2 10:11
     * @Param [dataList]
     * @return java.lang.Integer
     **/
    private Integer calDifferentEm(List<EmPo> dataList){
        int count = 0;
        for(EmPo emPo: dataList){
            if(!emPo.getRepayTotalAmtResult()){
                count++;
                continue;
            }else if(!emPo.getRepayPrinAmtResult()){
                count++;
                continue;
            }else if(!emPo.getRepayDateResult()){
                count++;
                continue;
            }else if(!emPo.getOvdDaysResult()){
                count++;
                continue;
            }else if(!emPo.getServiceRateResult()){
                count++;
                continue;
            }else if(!emPo.getServiceAmtResult()){
                count++;
                continue;
            }else if(!emPo.getFundCodeResult()){
                count++;
                continue;
            }else if(!emPo.getCompensatoryDateResult()){
                count++;
                continue;
            }
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @Description 添加合计
     * @Date 2023/8/3 9:52
     * @Param [lawCollCheck, sheetName, childCheckList]
     * @return void
     **/
    private EmPo addSumEm(LawCollCheck lawCollCheck, String sheetName, List<LawCollCheckDetail> childCheckList){
        LawCollCheckDetail lawCollCheckDetail = new LawCollCheckDetail();
        lawCollCheckDetail.setImportIdentify(lawCollCheck.getImportIdentify());
        lawCollCheckDetail.setSheetName(sheetName);
        lawCollCheckDetail.setcApplyNo("合计");

        //回款金额和
        BigDecimal sumCRepayTotalAmt = childCheckList.stream().filter(e->null != e.getcRepayTotalAmt()).map(LawCollCheckDetail::getcRepayTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);//对账单回款金额和
        lawCollCheckDetail.setcRepayTotalAmt(sumCRepayTotalAmt);
        BigDecimal sumSRepayTotalAmt = childCheckList.stream().filter(e->null != e.getsRepayTotalAmt()).map(LawCollCheckDetail::getsRepayTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);//系统回款金额和
        lawCollCheckDetail.setsRepayTotalAmt(sumSRepayTotalAmt);

        //回款本金和
        BigDecimal sumCRepayPrintAmt = childCheckList.stream().filter(e->null != e.getcRepayPrinAmt()).map(LawCollCheckDetail::getcRepayPrinAmt).reduce(BigDecimal.ZERO, BigDecimal::add);//对账单回款本金和
        lawCollCheckDetail.setcRepayPrinAmt(sumCRepayPrintAmt);
        BigDecimal sumSRepayPrintAmt = childCheckList.stream().filter(e->null != e.getsRepayPrinAmt()).map(LawCollCheckDetail::getsRepayPrinAmt).reduce(BigDecimal.ZERO, BigDecimal::add);//系统回款本金和
        lawCollCheckDetail.setsRepayPrinAmt(sumSRepayPrintAmt);

        //服务费和
        BigDecimal sumCServiceAmt = childCheckList.stream().filter(e->null != e.getcServiceAmt()).map(LawCollCheckDetail::getcServiceAmt).reduce(BigDecimal.ZERO, BigDecimal::add);//对账单服务费和
        lawCollCheckDetail.setcServiceAmt(sumCServiceAmt);
        BigDecimal sumSServiceAmt = childCheckList.stream().filter(e->null != e.getsServiceAmt()).map(LawCollCheckDetail::getsServiceAmt).reduce(BigDecimal.ZERO, BigDecimal::add);//系统服务费和
        lawCollCheckDetail.setsServiceAmt(sumSServiceAmt);

        //分润金额和
        BigDecimal sumFee3Amt = childCheckList.stream().filter(e->null != e.getFee3Amt()).map(LawCollCheckDetail::getFee3Amt).reduce(BigDecimal.ZERO, BigDecimal::add);//系统分润金额和
        lawCollCheckDetail.setFee3Amt(sumFee3Amt);

        lawCollCheckDetailMapper.insertLawCollCheckDetailSumEm(lawCollCheckDetail);

        EmPo emPo = new EmPo();
        BeanUtils.copyProperties(lawCollCheckDetail, emPo);
        emPo.setSRepayTotalAmt(lawCollCheckDetail.getsRepayTotalAmt().toString());
        emPo.setFee3Amt(lawCollCheckDetail.getFee3Amt().toString());
        //合计结果比对
        sumCompare(lawCollCheckDetail, emPo);
        return emPo;
    }
    /**
     * <AUTHOR>
     * @Description 导出结果数据
     * @Date 2023/7/24 17:32
     * @Param [response, importIdentify]
     * @return void
     **/
    @Override
    public void exportEm(HttpServletResponse response, String importIdentify) throws Exception {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode(IdUtils.fastUUID(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 导出数据处理
            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), ExportEm.class).build()) {
                List<LawCollCheckDetail> lawCollCheckList = lawCollCheckDetailMapper.selectByImportIdentify(importIdentify);
                Map<String, List<LawCollCheckDetail>> dataGroupMap = lawCollCheckList.stream().collect(Collectors.groupingBy(LawCollCheckDetail::getSheetName));
                int i=0;
                for (String sheetName: dataGroupMap.keySet()) {
                    //子列表
                    List<LawCollCheckDetail> childCheckList = dataGroupMap.get(sheetName);
                    // 每次都要创建writeSheet 这里注意必须指定sheetNo 而且sheetName必须不一样
                    WriteSheet writeSheet = EasyExcel.writerSheet(i, sheetName).build();
                    excelWriter.write(exportDataEm(childCheckList), writeSheet);
                    i++;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = MapUtils.newHashMap();
            map.put("status", "failure");
            map.put("message", "下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }
    }

    /**
     * <AUTHOR>
     * @Description 导出明细处理
     * @Date 2024/4/2 10:12
     * @Param [childCheckList]
     * @return java.util.List<org.ruoyi.core.modules.lawcoll.excel.exportE.ExportEm>
     **/
    private List<ExportEm> exportDataEm(List<LawCollCheckDetail> childCheckList){
        List<ExportEm> dataList = new ArrayList<>();
        for (LawCollCheckDetail lawCollCheckDetail : childCheckList) {
            if(StringUtils.isNotBlank(lawCollCheckDetail.getIsMiss())
                    && lawCollCheckDetail.getIsMiss().equals("0")){
                continue;
            }
            ExportEm exportEm = new ExportEm();
            BeanUtils.copyProperties(lawCollCheckDetail, exportEm);
            if(null != lawCollCheckDetail.getsRepayTotalAmt()){
                exportEm.setSRepayTotalAmt(lawCollCheckDetail.getsRepayTotalAmt().toString());
            }
            if(null != lawCollCheckDetail.getFee3Amt()){
                exportEm.setFee3Amt(lawCollCheckDetail.getFee3Amt().toString());
            }
            if(!lawCollCheckDetail.getcApplyNo().equals("合计")){
                exportEm.setStatus(RepayStatusEnum.valueOf(lawCollCheckDetail.getStatus()).getStatus());
                exportEm.setCFundCode(FundCodeEnum.valueOf(lawCollCheckDetail.getcFundCode()).getFundName());
                if(StringUtils.isNotBlank(lawCollCheckDetail.getsFundCode())){
                    exportEm.setSFundCode(FundCodeEnum.valueOf(lawCollCheckDetail.getsFundCode()).getFundName());
                }
                if(StringUtils.isBlank(lawCollCheckDetail.getsApplyNo())){
                    exportEm.setSRepayTotalAmt("无该借据信息");
                    exportEm.setFee3Amt("无该借据信息");
                }
                if(StringUtils.isBlank(lawCollCheckDetail.getsRepayDate())){
                    exportEm.setSRepayDate("无该日期数据");
                }
            }
            dataList.add(exportEm);
        }
        return dataList;
    }

    /**
     * <AUTHOR>
     * @Description 合计比对
     * @Date 2023/10/8 16:36
     * @Param [lawCollCheckDetail, emPo]
     * @return void
     **/
    private void sumCompare(LawCollCheckDetail lawCollCheckDetail, EmPo emPo){
        //回款金额
        if(null == lawCollCheckDetail.getsRepayTotalAmt() || lawCollCheckDetail.getsRepayTotalAmt().compareTo(lawCollCheckDetail.getcRepayTotalAmt())!=0){
            emPo.setRepayTotalAmtResult(false);
        }
        //回款本金=相同日期的还款本金相加
        if(null == lawCollCheckDetail.getsRepayPrinAmt() || lawCollCheckDetail.getsRepayPrinAmt().compareTo(lawCollCheckDetail.getcRepayPrinAmt())!=0){
            emPo.setRepayPrinAmtResult(false);
        }
        //服务费=回款金额*服务费费率
        if(null == lawCollCheckDetail.getsServiceAmt() || lawCollCheckDetail.getsServiceAmt().compareTo(lawCollCheckDetail.getcServiceAmt())!=0){
            emPo.setServiceAmtResult(false);
        }
    }
}
