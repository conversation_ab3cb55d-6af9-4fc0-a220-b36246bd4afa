package org.ruoyi.core.modules.fcdataquery.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 平台保证金明细VO类
 * @Date 2024/9/27 10:14
 * @Param
 * @return
 **/
@Data
public class PayableInfoFeeDetailVo {

    /** 返费公司名称 */
    private String companyName;

    /** 项目ID */
    private List<Long> projectIds;

    /** 担保公司 */
    private List<Integer> guaranteeIds;

    /** 资产方 */
    private List<Integer> assetIds;

    /** 资金方 */
    private List<Integer> fundIds;

    /** 其他公司 */
    private List<Integer> others;

    /** 渠道方名称 */
    private List<String> channels;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 科目id集合 */
    private List<Integer> subjectIds;

    private Integer pageNum;
    private Integer pageSize;
}
