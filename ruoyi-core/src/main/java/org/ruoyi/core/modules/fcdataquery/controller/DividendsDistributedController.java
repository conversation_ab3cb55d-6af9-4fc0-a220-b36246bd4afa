package org.ruoyi.core.modules.fcdataquery.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageUtils;
import org.ruoyi.core.modules.fcdataquery.po.DividendsDistributedPo;
import org.ruoyi.core.modules.fcdataquery.service.IFcDividendsDistributedService;
import org.ruoyi.core.modules.fcdataquery.vo.DividendsDistributedVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 待分配红利Controller
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/fc/dividendsDistributed")
public class DividendsDistributedController extends BaseController
{
    @Autowired
    private IFcDividendsDistributedService fcDividendsDistributedService;

    /**
     * 查询待分配红利
     */
    @PreAuthorize("@ss.hasPermi('fc:dividendsDistributed:list')")
    @GetMapping("/list")
    public TableDataInfo list(DividendsDistributedVo dividendsDistributedVo, int pageNum, int pageSize)
    {
        List<DividendsDistributedPo> dividendsDistributedPoList = fcDividendsDistributedService.selectDividendsDistributedList(dividendsDistributedVo);
        PageInfo<DividendsDistributedPo> pageInfo = PageUtils.getPageInfo(pageNum, pageSize, dividendsDistributedPoList);
        TableDataInfo tableDataInfo = getDataTable(pageInfo.getList());
        Map<Object, Object> map = new HashMap<>();
        map.put("sumTotalAmt", dividendsDistributedPoList.stream().map(DividendsDistributedPo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        tableDataInfo.setMap(map);
        return tableDataInfo;
    }

    /**
     * 导出待分配红利列表
     */
    @PreAuthorize("@ss.hasPermi('fc:dividendsDistributed:export')")
    @Log(title = "经营分析-项目收入-待分配红利导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public List<DividendsDistributedPo> export(@RequestBody DividendsDistributedVo dividendsDistributedVo)
    {
        return fcDividendsDistributedService.selectDividendsDistributedList(dividendsDistributedVo);
    }
}
