package org.ruoyi.core.archivist.file;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.OSSObject;
import com.ruoyi.common.properties.OSSProperties;
import lombok.extern.slf4j.Slf4j;
import org.ruoyi.core.archivist.constant.NumStrConstant;
import org.ruoyi.core.archivist.domain.DaArchivistFiles;
import org.ruoyi.core.archivist.domain.SysAttMain;
import org.ruoyi.core.archivist.service.IDaArchivistMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.zip.Adler32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.ruoyi.common.utils.oss.OSSUtil.getDAOssClient;


@Slf4j
@Component
public class fileUtils {

    private static String endpoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String uploadPath;

    private static OSS ossClient;

    @Autowired
    public void init(OSSProperties ossProperties) {
        fileUtils.endpoint = ossProperties.getEndpoint();
        fileUtils.accessKeyId = ossProperties.getAccessKeyId();
        fileUtils.accessKeySecret = ossProperties.getAccessKeySecret();
        fileUtils.bucketName = ossProperties.getBucketName();
        fileUtils.uploadPath = ossProperties.getUploadPath();
    }

    @Autowired
    IDaArchivistMainService daArchivistMainService;

    private static IDaArchivistMainService iDaArchivistMainService;

    @PostConstruct
    public void init() {
        iDaArchivistMainService = daArchivistMainService;
    }


    /**
     * 批量下载附件打包zip文件
     * @param request
     * @param response
     * @param daArchivistFiles
     * @return
     * @throws IOException
     */
    public static void getOssFile(HttpServletRequest request, HttpServletResponse response, DaArchivistFiles daArchivistFiles){
        try {
            // 初始化
            OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);

            //zip文件名称
            String fileName = NumStrConstant.DA_ZIP_FILE_NAME + ".zip";
            // 创建临时文件
            File zipFile = File.createTempFile(NumStrConstant.DA_ZIP_FILE_NAME, ".zip");
            FileOutputStream f = new FileOutputStream(zipFile);

            CheckedOutputStream csum = new CheckedOutputStream(f, new Adler32());
            // 用于将数据压缩成Zip文件格式
            ZipOutputStream zos = new ZipOutputStream(csum);
            for (DaArchivistFiles ossfile : daArchivistFiles.getFileArray()) {
                // 获取Object，返回结果为OSSObject对象
                int oss = ossfile.getUrl().indexOf("oss" );
                String substring = ossfile.getUrl().substring(oss+4);
                OSSObject ossObject = ossClient.getObject(bucketName, substring);
                // 读取Object内容  返回
                InputStream inputStream = ossObject.getObjectContent();
                // 对于每一个要被存放到压缩包的文件，都必须调用ZipOutputStream对象的putNextEntry()方法，确保压缩包里面文件不同名
                zos.putNextEntry(new ZipEntry(ossfile.getFileName()));
                int bytesRead = 0;
                // 向压缩文件中输出数据
                while ((bytesRead = inputStream.read()) != -1) {
                    zos.write(bytesRead);
                }
                inputStream.close();
                zos.closeEntry(); // 当前文件写完，定位为写入下一条项目
            }
            zos.close();
            String header = request.getHeader("User-Agent").toUpperCase();
            if (header.contains("MSIE") || header.contains("TRIDENT") || header.contains("EDGE")) {
                fileName = URLEncoder.encode(fileName, "utf-8");
                fileName = fileName.replace("+", "%20");
            } else {
                fileName = new String(fileName.getBytes(),  "ISO8859-1");
            }
            response.reset();
            response.setContentType("text/plain");
            response.setContentType("application/octet-stream; charset=utf-8");
            response.setHeader("Location", fileName);
            response.setHeader("Cache-Control", "max-age=0");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            FileInputStream fis = new FileInputStream(zipFile);
            BufferedInputStream buff = new BufferedInputStream(fis);
            BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());
            byte[] car = new byte[1024];
            int l = 0;
            while (l < zipFile.length()) {
                int j = buff.read(car, 0, 1024);
                l += j;
                out.write(car, 0, j);
            }
            // 关闭流
            fis.close();
            buff.close();
            out.close();
            ossClient.shutdown();
            // 删除临时文件
            zipFile.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 档案历史数据附件在线预览
     * @param filePath
     * @param fileName
     * @param response
     */
    public static void downloadDAFile(String filePath, String fileName, HttpServletResponse response) {
        OSS ossClient = getDAOssClient();
        try {
            response.reset();
            OSSObject ossObject = ossClient.getObject(bucketName, filePath+fileName);
            //取历史数据档案附件contentType类型
            String key = ossObject.getKey();
            //根据路径查找历史数据附件表，
            SysAttMain sysAttMain = iDaArchivistMainService.selectFileMsgByFilePath(key);
            //不为空则获取附件的contentType，并赋值给response，供前端解析并预览
            if (sysAttMain != null){
                String fdContentType = sysAttMain.getFdContentType();
                response.setContentType(fdContentType);
            }else {
                response.setContentType(ossObject.getObjectMetadata().getContentType());
            }
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition","inline");
            try(BufferedInputStream reader = new BufferedInputStream(ossObject.getObjectContent());
                OutputStream os = response.getOutputStream();){
                byte[] buffer = new byte[1024];
                int length = 0;
                while ((length = reader.read(buffer)) != -1) {
                    os.write(buffer, 0, length);
                }
            }
        } catch (Exception e) {
            log.error("ex:{}" , e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
    }

}