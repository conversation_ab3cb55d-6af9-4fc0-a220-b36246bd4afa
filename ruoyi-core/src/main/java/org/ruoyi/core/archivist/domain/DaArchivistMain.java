package org.ruoyi.core.archivist.domain;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.ruoyi.core.archivist.domain.vo.DaArchivistCatalogueVo;
import org.ruoyi.core.archivist.domain.vo.OaProjectDeployVo;
import org.ruoyi.core.archivist.domain.vo.SysAttFileVo;

/**
 * 档案详情对象 da_archivist_main
 *
 * <AUTHOR>
 * @date 2023-11-28
 */
@Data
public class DaArchivistMain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 档案编号 */
    @Excel(name = "档案编号")
    private String archivistCode;

    /** 档案名称 */
    @Excel(name = "档案名称")
    private String archivstName;

    /** 归档流程发起人 */
    @Excel(name = "归档流程发起人")
    private String flowInitiator;

    /** 归档流程发起时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "归档流程发起时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initiateTime;

    /** 所属公司id */
    // @Excel(name = "所属公司")
    private Long pertainCompanyId;

    /** 所属公司名称 */
    @Excel(name = "所属公司名称")
    private String pertainCompanyName;

    /** 所属部门id */
    // @Excel(name = "所属部门")
    private Long pertainDeptId;

    /** 所属部门名称 */
    @Excel(name = "所属部门")
    private String pertainDeptName;

    /** 归档人 */
    @Excel(name = "归档人")
    private String archivistBy;

    /** 归档时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "归档时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date archivistTime;

    /** 所属卷库 */
    @Excel(name = "所属卷库")
    private String pertainLib;

    /** 所属流程id */
    // @Excel(name = "所属流程id")
    private String pertainFlowId;

    /** 所属流程名称 */
    @Excel(name = "所属流程")
    private String pertainFlowName;

    /** 档案来源(LC:流程归档;DR:历史数据导入;WB:外部对接) */
    @Excel(name = "档案来源")
    private String archivistSource;

    /** 借阅状态(待定，默认WJY未借阅) */
    @Excel(name = "借阅状态")
    private String borrowType;

    /** 档案状态(SX生效;ZF作废) */
    @Excel(name = "档案状态")
    private String archivistType;

    /** 归档开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date archivistBeginTime;

    /** 归档结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date archivistEndTime;

    /** 目录id */
    @JsonProperty(value = "catalogueId")
    private Long catalogueId;

    /** 当前档案的所属目录集合 */
    private List<DaArchivistCatalogueVo> archivistCatalogueVoList;

    /** 项目集合 */
    private List<OaProjectDeployVo> cwProjectList;

    /** 创建人 */
    @Excel(name = "创建人")

    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 所属目录 */
    @Excel(name = "所属目录")
    private String catalogueName;

    /** 所属档案库 */
    @Excel(name = "所属档案库")
    private String pertainArchivist;

    /** 终稿扫描件 */
    private List<DaArchivistFiles> daArchivistFileList;

    private Integer pageSize;

    private Integer pageNum;

    /** 导出数据集合 */
    private List<DaArchivistMain> exportJsList;

    /** json档案id */
    private String exportList;

    /** 当前登陆人所属公司集合 */
    private Set<Long> unitIdList;

    /** 档案来源名称(LC:流程归档;DR:历史数据导入;WB:外部对接) */
    private String archivistSourceType;

    /** 历史数据-附件集合 */
    private List<SysAttFileVo> sysAttFileVoList;

    /** 项目关联的档案id集合 */
    private List<String> projectArchivistIdList;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("archivistCode", getArchivistCode())
                .append("archivstName", getArchivstName())
                .append("flowInitiator", getFlowInitiator())
                .append("initiateTime", getInitiateTime())
                .append("pertainCompanyId", getPertainCompanyId())
                .append("pertainDeptId", getPertainDeptId())
                .append("archivistBy", getArchivistBy())
                .append("archivistTime", getArchivistTime())
                .append("pertainLib", getPertainLib())
                .append("pertainFlowId", getPertainFlowId())
                .append("archivistSource", getArchivistSource())
                .append("borrowType", getBorrowType())
                .append("archivistType", getArchivistType())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("archivistEndTime", getArchivistEndTime())
                .append("archivistBeginTime", getArchivistBeginTime())
                .append("catalogueId", getCatalogueId())
                .toString();
    }
}
