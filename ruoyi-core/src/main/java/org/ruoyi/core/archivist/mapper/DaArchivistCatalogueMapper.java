package org.ruoyi.core.archivist.mapper;

import java.util.List;
import java.util.Set;

import com.ruoyi.common.core.domain.entity.*;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.SysPost;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.archivist.domain.DaArchivistCatalogue;
import org.ruoyi.core.archivist.domain.vo.DaArchivistCatalogueVo;
import org.ruoyi.core.archivist.domain.vo.DaProcFormDataVo;
import org.ruoyi.core.archivist.domain.vo.DaProjectVo;
import org.ruoyi.core.archivist.domain.vo.SysDeptVo;
import org.ruoyi.core.cwproject.domain.CwProject;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;

/**
 * 归档目录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-28
 */
public interface DaArchivistCatalogueMapper
{
    /**
     * 查询归档目录
     *
     * @param id 归档目录主键
     * @return 归档目录
     */
    public DaArchivistCatalogueVo selectDaArchivistCatalogueById(Long id);

    /**
     * 查询归档目录列表
     *
     * @param daArchivistCatalogue 归档目录
     * @return 归档目录集合
     */
    public List<DaArchivistCatalogue> selectDaArchivistCatalogueList(DaArchivistCatalogue daArchivistCatalogue);

    /**
     * 新增归档目录
     *
     * @param daArchivistCatalogue 归档目录
     * @return 结果
     */
    public int insertDaArchivistCatalogue(DaArchivistCatalogue daArchivistCatalogue);

    /**
     * 修改归档目录
     *
     * @param daArchivistCatalogue 归档目录
     * @return 结果
     */
    public int updateDaArchivistCatalogue(DaArchivistCatalogue daArchivistCatalogue);

    /**
     * 删除归档目录
     *
     * @param id 归档目录主键
     * @return 结果
     */
    public int deleteDaArchivistCatalogueById(Long id);

    /**
     * 批量删除归档目录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDaArchivistCatalogueByIds(Long[] ids);

    /**
     * 根据所属公司id查询目录
     *
     * @param orgId 所属公司id(父id)
     * @return 结果
     */
    List<DaArchivistCatalogueVo> selectOrgByOrgId(Long orgId);

    /**
     * 根据上级目录查询子目录
     *
     * @param parentId 上级目录id
     * @return 结果
     */
    List<DaArchivistCatalogueVo> selectTowGradeByParentId(@Param("parentId") Long parentId);

    /**
     * 根据所属档案库类型查询树状列表
     * @param pertainArchivist
     * @return
     */
    List<DaArchivistCatalogueVo> selectDaArchivistCatalogueVoList(@Param("pertainArchivist") String pertainArchivist, @Param("allCompanyIds")List<Long> allCompanyIds);

    /**
     * 获取目录总数量，生成目录系统编号
     * @param createTime
     * @return
     */
    public int getCountByCreateTime(String createTime);

    /**
     * 根据用户填写的所属目录查找目录
     * @param name
     * @param bizType
     * @return
     */
    DaArchivistCatalogueVo queryCatalogueByName(@Param("name") String name, @Param("bizType") String bizType);

    /**
     * 根据部门id查询公司信息
     * @param deptId
     * @return
     */
    SysDept selectCompanyInfoByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据流程id获取流程发起人
     * @param flowId
     * @return
     */
    DaProcFormDataVo selectFormDataByBusinessId(@Param("flowId") String flowId);

    /**
     * 根据项目id查询项目
     * @param id
     * @return
     */
    DaProjectVo selectPorjectMsgByProjectId(String id);

    /**
     * 查询全量公司信息
     * @return
     */
    List<SysUnit> selectAllCompany();

    /**
     * 根据公司id和档案类型查询全量公司信息
     * @param orgId
     * @param bizType
     * @return
     */
    DaArchivistCatalogueVo selectCompanyInfoById(@Param("orgId") Long orgId, @Param("bizType") String bizType);

    /**
     * 根据公司id查询部门树
     * @param companyId
     * @return
     */
    List<SysDeptVo> selectSysDeptList(@Param("companyId")Long companyId);

    /**
     * 根据父级id和档案类型和目录名称判断是否存在该目录
     * @param name
     * @param bizType
     * @param id
     * @return
     */
    DaArchivistCatalogueVo queryCatalogueByNameParentId(@Param("name")String name, @Param("bizType")String bizType, @Param("id")Long id);
    /**
     * 根据用户id查询岗位
     * @param userId
     * @return
     */
    List<SysPostAO> selectAllCompanyMessage(@Param("userId")Long userId);

    /**
     * 根据部门id查询全量公司信息列表
     * @param deptId
     * @return
     */
    SysUnit selectDeptById(@Param("deptId") Long deptId);

    /**
     * 根据所属公司id查询目录
     * @param daArchivistCatalogueVo
     * @return
     */
    List<DaArchivistCatalogueVo> selectDaArchivistCatalogueListByUnitId(DaArchivistCatalogueVo daArchivistCatalogueVo);

    /**
     * 根据名称、档案类型、所属公司id查询目录信息
     * @param name
     * @param bizType
     * @param orgId
     * @return
     */
    DaArchivistCatalogueVo queryCatalogueByNameAndCode(@Param("name") String name, @Param("bizType") String bizType, @Param("orgId")Long orgId);

    /**
     * 根据用户id查询其岗位的所属公司
     * @param userId
     * @return
     */
    List<SysUnit> selectUserAreaCompany(@Param("userId") Long userId);

    /**
     * 获取字典数据表中archivist_pertain所属档案库类型
     * @param archivistPertain
     * @return
     */
    List<SysDictData> selectDictDataList(String archivistPertain);

    /**
     * 更新全量公司时更新档案菜单一级目录信息
     * @param daArchivistCatalogue
     * @return
     */
    int updateCompanyCatalogue(DaArchivistCatalogue daArchivistCatalogue);

    /**
     * 查询归档流程发起人(当前用户)的归档流程的所属目录
     * @param userName 用户登录名称
     * @return
     */
    List<DaArchivistCatalogueVo> selectCatalogueListByUserId(@Param("userName") String userName, @Param("pertainArchivist") String pertainArchivist);

    /**
     * 根据父id查询上级目录
     * @param parentId
     * @return
     */
    DaArchivistCatalogueVo selectCatalogueInfoByParentId(Long parentId);

    /**
     * 根据当前用户的项目名称权限获取档案目录集合
     * @param loginUserHaveProjectDeployIds
     * @return
     */
    List<DaArchivistCatalogueVo> selectDaArchivistCatalogueListByDeployProjectIds(@Param("loginUserHaveProjectDeployIds") List<Long> loginUserHaveProjectDeployIds, @Param("pertainArchivist") String pertainArchivist);

    /**
     * 根据目录id集合查询目录信息集合
     * @param catalogueIds
     * @return
     */
    List<DaArchivistCatalogueVo> selectCatalogueListByIds(@Param("catalogueIds") Set<Long> catalogueIds);

    /**
     * 根据档案id集合查询所属目录信息集合
     * @param archivistId
     * @return
     */
    List<DaArchivistCatalogueVo> queryCatalogueListByArchivistIds(@Param("archivistId") List<String> archivistId);

    /**
     * 根据公司id查询各公司下的所有部门
     * @param companyList 公司id集合
     * @return
     */
    List<SysDept> selectDeptListByCompanyIds(@Param("companyList") Set<Long> companyList);

    /**
     * 查询当前用户有哪些公司的档案管理员权限
     * @param userId
     * @param moduleType
     * @param roleType
     * @return
     */
    List<AuthDetailVo> selectLoginUserCompanyDAGLYAuth(@Param("userId") Long userId, @Param("moduleType")String moduleType, @Param("roleType")String roleType);
}
