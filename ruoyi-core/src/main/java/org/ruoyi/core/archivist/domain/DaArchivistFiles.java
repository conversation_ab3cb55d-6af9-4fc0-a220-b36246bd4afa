package org.ruoyi.core.archivist.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 【请填写功能名称】对象 da_archivist_files
 * 
 * <AUTHOR>
 * @date 2023-12-08
 */
public class DaArchivistFiles extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 流程id */
    @Excel(name = "流程id")
    private String businessId;

    /** 文件名 */
    @Excel(name = "文件名")
    private String fileName;

    /** 扩展名 */
    @Excel(name = "扩展名")
    private String extension;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String url;

    /** 是否是最终扫描件(0是;1否) */
    @Excel(name = "是否是最终扫描件(0是;1否)")
    private String isUltimately;

    /** 借阅状态(待定，默认WJY未借阅) */
    @Excel(name = "借阅状态(待定，默认WJY未借阅)")
    private String borrowType;

    /** 删除标志(0未删除;1已删除) */
    private Long delFlag;

    private List<DaArchivistFiles> fileArray;

    public List<DaArchivistFiles> getFileArray() {
        return fileArray;
    }

    public void setFileArray(List<DaArchivistFiles> fileArray) {
        this.fileArray = fileArray;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setBusinessId(String businessId) 
    {
        this.businessId = businessId;
    }

    public String getBusinessId() 
    {
        return businessId;
    }
    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }
    public void setExtension(String extension) 
    {
        this.extension = extension;
    }

    public String getExtension() 
    {
        return extension;
    }
    public void setUrl(String url) 
    {
        this.url = url;
    }

    public String getUrl() 
    {
        return url;
    }
    public void setIsUltimately(String isUltimately) 
    {
        this.isUltimately = isUltimately;
    }

    public String getIsUltimately() 
    {
        return isUltimately;
    }
    public void setBorrowType(String borrowType) 
    {
        this.borrowType = borrowType;
    }

    public String getBorrowType() 
    {
        return borrowType;
    }
    public void setDelFlag(Long delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Long getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("businessId", getBusinessId())
            .append("fileName", getFileName())
            .append("extension", getExtension())
            .append("url", getUrl())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("isUltimately", getIsUltimately())
            .append("borrowType", getBorrowType())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
