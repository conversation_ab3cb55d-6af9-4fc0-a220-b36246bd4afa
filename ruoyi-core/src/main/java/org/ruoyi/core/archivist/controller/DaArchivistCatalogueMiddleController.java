package org.ruoyi.core.archivist.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.archivist.domain.vo.DaArchivistCatalogueMiddleVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.archivist.domain.DaArchivistCatalogueMiddle;
import org.ruoyi.core.archivist.service.IDaArchivistCatalogueMiddleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 档案-目录中间Controller
 *
 * <AUTHOR>
 * @date 2023-11-28
 */
@RestController
@RequestMapping("/archivistCatalogueMiddle/arcCataMiddle")
public class DaArchivistCatalogueMiddleController extends BaseController
{
    @Autowired
    private IDaArchivistCatalogueMiddleService daArchivistCatalogueMiddleService;

    /**
     * 查询列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DaArchivistCatalogueMiddle daArchivistCatalogueMiddle)
    {
        startPage();
        List<DaArchivistCatalogueMiddle> list = daArchivistCatalogueMiddleService.selectDaArchivistCatalogueMiddleList(daArchivistCatalogueMiddle);
        return getDataTable(list);
    }

    /**
     * 导出
     */
    @Log(title = "档案-目录中间", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DaArchivistCatalogueMiddle daArchivistCatalogueMiddle)
    {
        List<DaArchivistCatalogueMiddle> list = daArchivistCatalogueMiddleService.selectDaArchivistCatalogueMiddleList(daArchivistCatalogueMiddle);
        ExcelUtil<DaArchivistCatalogueMiddle> util = new ExcelUtil<DaArchivistCatalogueMiddle>(DaArchivistCatalogueMiddle.class);
        util.exportExcel(response, list, "档案-目录中间数据");
    }

    /**
     * 查看详情
     */
    @GetMapping(value = "/{archivistId}")
    public AjaxResult getInfo(@PathVariable("archivistId") String archivistId)
    {
        return AjaxResult.success(daArchivistCatalogueMiddleService.selectDaArchivistCatalogueMiddleByArchivistId(archivistId));
    }

    /**
     * 新增
     */
    @Log(title = "档案-目录中间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DaArchivistCatalogueMiddle daArchivistCatalogueMiddle)
    {
        return toAjax(daArchivistCatalogueMiddleService.insertDaArchivistCatalogueMiddle(daArchivistCatalogueMiddle));
    }

    /**
     * 修改
     */
    @Log(title = "档案-目录中间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DaArchivistCatalogueMiddle daArchivistCatalogueMiddle)
    {
        return toAjax(daArchivistCatalogueMiddleService.updateDaArchivistCatalogueMiddle(daArchivistCatalogueMiddle));
    }

    /**
     * 删除
     */
    @Log(title = "档案-目录中间", businessType = BusinessType.DELETE)
    @DeleteMapping("/{archivistIds}")
    public AjaxResult remove(@PathVariable String[] archivistIds)
    {
        return toAjax(daArchivistCatalogueMiddleService.deleteDaArchivistCatalogueMiddleByArchivistIds(archivistIds));
    }

    /**
     * 档案移动/复制
     */
    @PreAuthorize("@ss.hasPermi('fileManagement:contractFile:arrangement')")
    @Log(title = "档案-目录中间", businessType = BusinessType.UPDATE)
    @PutMapping("/moveOrCopy")
    public AjaxResult moveOrCopy(@RequestBody DaArchivistCatalogueMiddleVo daArchivistCatalogueMiddleVo){
        return toAjax(daArchivistCatalogueMiddleService.archivistMoveOrCopy(daArchivistCatalogueMiddleVo));
    }
}
