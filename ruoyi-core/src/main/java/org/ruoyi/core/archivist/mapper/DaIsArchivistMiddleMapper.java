package org.ruoyi.core.archivist.mapper;

import java.util.List;
import java.util.Set;

import com.ruoyi.common.core.domain.entity.SysPostAO;
import com.ruoyi.common.core.domain.entity.SysUnit;
import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.archivist.domain.DaIsArchivistMiddle;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;

/**
 * 流程是否归档中间Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface DaIsArchivistMiddleMapper
{
    /**
     * 查询流程是否归档中间
     *
     * @param flowId 流程是否归档中间主键
     * @return 流程是否归档中间
     */
    public DaIsArchivistMiddle selectDaIsArchivistMiddleByFlowId(String flowId);

    /**
     * 查询流程是否归档中间列表
     *
     * @param daIsArchivistMiddle 流程是否归档中间
     * @return 流程是否归档中间集合
     */
    public List<DaIsArchivistMiddle> selectDaIsArchivistMiddleList(DaIsArchivistMiddle daIsArchivistMiddle);

    /**
     * 新增流程是否归档中间
     *
     * @param daIsArchivistMiddle 流程是否归档中间
     * @return 结果
     */
    public int insertDaIsArchivistMiddle(DaIsArchivistMiddle daIsArchivistMiddle);

    /**
     * 修改流程是否归档中间
     *
     * @param daIsArchivistMiddle 流程是否归档中间
     * @return 结果
     */
    public int updateDaIsArchivistMiddle(DaIsArchivistMiddle daIsArchivistMiddle);

    /**
     * 删除流程是否归档中间
     *
     * @param flowId 流程是否归档中间主键
     * @return 结果
     */
    public int deleteDaIsArchivistMiddleByFlowId(String flowId);

    /**
     * 批量删除流程是否归档中间
     *
     * @param flowIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDaIsArchivistMiddleByFlowIds(String[] flowIds);

    /**
     * 根据流程id查询是否为归档模板
     * @param flowId
     * @return
     */
    OaProcessTemplate selectIsFilingByFlowId(String flowId);

    /**
     * 根据创建人名称查询所属公司信息
     * @param archivistBy
     * @return
     */
    List<SysPostAO> selectAllCompanyMessageByUserName(String archivistBy);

    /**
     * 根据用户id/模块标识/角色标识/公司id查询用户是否是档案管理员
     * @param userId
     * @param moduleType
     * @param roleType
     * @param companyId
     */
    List<AuthDetailVo> selectDAGLY1AuthorityByUserIdAndModuleType(@Param("userId") Long userId, @Param("moduleType") String moduleType, @Param("roleType") String roleType, @Param("companyId") Long companyId);

    /**
     * 根据主表id查询附表是否存在数据
     * @param authMainId
     * @return
     */
    List<AuthDetail> selectAuthFlowInfoByAuthMainId(Long authMainId);

    /**
     * 根据公司id查询公司信息
     * @param companyIdList
     * @return
     */
    List<SysCompanyVo> selectCompanyInfoListByIds(@Param("companyIdList") Set<Long> companyIdList);
}
