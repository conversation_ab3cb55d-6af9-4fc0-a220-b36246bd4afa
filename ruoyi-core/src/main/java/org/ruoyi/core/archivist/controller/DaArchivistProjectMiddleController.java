package org.ruoyi.core.archivist.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.archivist.domain.DaArchivistProjectMiddle;
import org.ruoyi.core.archivist.service.IDaArchivistProjectMiddleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 档案-项目中间Controller
 * 
 * <AUTHOR>
 * @date 2023-11-28
 */
@RestController
@RequestMapping("/archivistProjectMiddle/arcProMiddle")
public class DaArchivistProjectMiddleController extends BaseController
{
    @Autowired
    private IDaArchivistProjectMiddleService daArchivistProjectMiddleService;

    /**
     * 查询档案-项目中间列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DaArchivistProjectMiddle daArchivistProjectMiddle)
    {
        startPage();
        List<DaArchivistProjectMiddle> list = daArchivistProjectMiddleService.selectDaArchivistProjectMiddleList(daArchivistProjectMiddle);
        return getDataTable(list);
    }

    /**
     * 导出档案-项目中间列表
     */
    @Log(title = "档案-项目中间表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DaArchivistProjectMiddle daArchivistProjectMiddle)
    {
        List<DaArchivistProjectMiddle> list = daArchivistProjectMiddleService.selectDaArchivistProjectMiddleList(daArchivistProjectMiddle);
        ExcelUtil<DaArchivistProjectMiddle> util = new ExcelUtil<DaArchivistProjectMiddle>(DaArchivistProjectMiddle.class);
        util.exportExcel(response, list, "档案-项目中间数据");
    }

    /**
     * 获取档案-项目中间详细信息
     */
    @GetMapping(value = "/{projectId}")
    public AjaxResult getInfo(@PathVariable("projectId") Long projectId)
    {
        return AjaxResult.success(daArchivistProjectMiddleService.selectDaArchivistProjectMiddleByProjectId(projectId));
    }

    /**
     * 新增档案-项目中间
     */
    @Log(title = "档案-项目中间表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DaArchivistProjectMiddle daArchivistProjectMiddle)
    {
        return toAjax(daArchivistProjectMiddleService.insertDaArchivistProjectMiddle(daArchivistProjectMiddle));
    }

    /**
     * 修改档案-项目中间
     */
    @Log(title = "档案-项目中间表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DaArchivistProjectMiddle daArchivistProjectMiddle)
    {
        return toAjax(daArchivistProjectMiddleService.updateDaArchivistProjectMiddle(daArchivistProjectMiddle));
    }

    /**
     * 删除档案-项目中间
     */
    @Log(title = "档案-项目中间表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{projectIds}")
    public AjaxResult remove(@PathVariable Long[] projectIds)
    {
        return toAjax(daArchivistProjectMiddleService.deleteDaArchivistProjectMiddleByProjectIds(projectIds));
    }
}
