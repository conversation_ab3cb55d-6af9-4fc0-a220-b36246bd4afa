package org.ruoyi.core.archivist.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

@Data
public class OaProjectDeployVo {
    /** 主键 */
    private Long id;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 0通道业务1分润业务2法催业务 */
    @Excel(name = "0通道业务1分润业务2法催业务")
    private String projectType;

    /** 是否启用Y启用N禁用 */
    @Excel(name = "是否启用Y启用N禁用")
    private String isEnable;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createBr;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updateBr;

    /** 档案id */
    private String archivistId;

}
