package org.ruoyi.core.kaohe.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.ruoyi.common.core.domain.entity.SysDept;
import lombok.Data;
import org.ruoyi.core.kaohe.domain.AnnualPlan;

import java.util.List;
import java.util.Set;

@Data
public class AnnualPlanVo extends  AnnualPlan{

    private SysDept dept;

    private String companyName;

    private String companyShortName;

    private String deptName;

    private String userName;

    private String nickName;
    /**
    * 公司/部门/个人
    * */
    private String name;

    private String shortName;

    public List<AnnualPlanVo> annualPlanVoList;

    @JSONField(serialize = false, deserialize = false)
    public Set<String> years;

    @JSONField(serialize = false, deserialize = false)
    public Set<Long> companyIds;

    @JSONField(serialize = false, deserialize = false)
    public Set<Long> userIds;

    @JSONField(serialize = false, deserialize = false)
    public Set<Long> deptIds;
    /**
    * 数据父类类型(此条数据上级的数据类型为1.公司 2.部门)
    */
    public String parentType;
    /**
    * 部门的父级id(前端在上一级数据是部门时需要)
    */
    public Long parentDeptId;

    /**
    * 公司数据维度需要的主部门名称    ^ ^
    */
    public String mainDeptName;
    /**
    * 公司数据维度需要的主部门id    ^ ^
    */
    public String mainDeptId;
    /**
    * 流程 id
    */
    public String processId;

    @JSONField(serialize = false, deserialize = false)
    public Set<Long> ids;

    /** 季度 */
    private Integer quarter;

    //有权限公司(条件字段)
    List<Long> authorityCompanyIds;
}
