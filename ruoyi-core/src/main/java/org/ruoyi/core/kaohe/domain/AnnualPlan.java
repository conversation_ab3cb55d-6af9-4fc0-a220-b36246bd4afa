package org.ruoyi.core.kaohe.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 年度计划对象 kh_annual_plan
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
public class AnnualPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 年度 */
    @Excel(name = "年度")
    private String year;

    /** 所属类型  1.公司 2.部门 3.个人 */
    @Excel(name = "所属类型  1.公司 2.部门 3.个人")
    private String type;

    /** 所属公司 */
    @Excel(name = "所属公司")
    private Long companyId;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private Long deptId;

    /** 所属个人 */
    @Excel(name = "所属个人")
    private Long userId;

    /** 所属上级id */
    @Excel(name = "所属上级id")
    private Long parentId;

    /** 一季度总指标 */
    @Excel(name = "一季度总指标")
    private BigDecimal q1TotalIndex;

    /** 一季度分配项目指标 */
    @Excel(name = "一季度分配项目指标")
    private BigDecimal q1DistributionIndex;

    /** 一季度自拓项目指标 */
    @Excel(name = "一季度自拓项目指标")
    private BigDecimal q1ExtensionIndex;

    /** 一季度自拓银行 */
    @Excel(name = "一季度自拓银行")
    private BigDecimal q1ExtensionBank;

    /** 二季度总指标 */
    @Excel(name = "二季度总指标")
    private BigDecimal q2TotalIndex;

    /** 二季度分配项目指标 */
    @Excel(name = "二季度分配项目指标")
    private BigDecimal q2DistributionIndex;

    /** 二季度自拓项目指标 */
    @Excel(name = "二季度自拓项目指标")
    private BigDecimal q2ExtensionIndex;

    /** 二季度自拓银行 */
    @Excel(name = "二季度自拓银行")
    private BigDecimal q2ExtensionBank;

    /** 三季度总指标 */
    @Excel(name = "三季度总指标")
    private BigDecimal q3TotalIndex;

    /** 三季度分配项目指标 */
    @Excel(name = "三季度分配项目指标")
    private BigDecimal q3DistributionIndex;

    /** 三季度自拓项目指标 */
    @Excel(name = "三季度自拓项目指标")
    private BigDecimal q3ExtensionIndex;

    /** 三季度自拓银行 */
    @Excel(name = "三季度自拓银行")
    private BigDecimal q3ExtensionBank;

    /** 四季度总指标 */
    @Excel(name = "四季度总指标")
    private BigDecimal q4TotalIndex;

    /** 四季度分配项目指标 */
    @Excel(name = "四季度分配项目指标")
    private BigDecimal q4DistributionIndex;

    /** 四季度自拓项目指标 */
    @Excel(name = "四季度自拓项目指标")
    private BigDecimal q4ExtensionIndex;

    /** 四季度自拓银行 */
    @Excel(name = "四季度自拓银行")
    private BigDecimal q4ExtensionBank;

    private String state;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("year", getYear())
            .append("type", getType())
            .append("companyId", getCompanyId())
            .append("deptId", getDeptId())
            .append("userId", getUserId())
            .append("parentId", getParentId())
            .append("q1TotalIndex", getQ1TotalIndex())
            .append("q1DistributionIndex", getQ1DistributionIndex())
            .append("q1ExtensionIndex", getQ1ExtensionIndex())
            .append("q1ExtensionBank", getQ1ExtensionBank())
            .append("q2TotalIndex", getQ2TotalIndex())
            .append("q2DistributionIndex", getQ2DistributionIndex())
            .append("q2ExtensionIndex", getQ2ExtensionIndex())
            .append("q2ExtensionBank", getQ2ExtensionBank())
            .append("q3TotalIndex", getQ3TotalIndex())
            .append("q3DistributionIndex", getQ3DistributionIndex())
            .append("q3ExtensionIndex", getQ3ExtensionIndex())
            .append("q3ExtensionBank", getQ3ExtensionBank())
            .append("q4TotalIndex", getQ4TotalIndex())
            .append("q4DistributionIndex", getQ4DistributionIndex())
            .append("q4ExtensionIndex", getQ4ExtensionIndex())
            .append("q4ExtensionBank", getQ4ExtensionBank())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
