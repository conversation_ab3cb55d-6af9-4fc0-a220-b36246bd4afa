package org.ruoyi.core.kaohe.service;

import org.ruoyi.core.kaohe.domain.CheckConfigSlave;

import java.util.List;

/**
 * 考核配置从Service接口
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface ICheckConfigSlaveService
{
    /**
     * 查询考核配置从
     *
     * @param id 考核配置从主键
     * @return 考核配置从
     */
    public CheckConfigSlave selectCheckConfigSlaveById(Long id);

    /**
     * 查询考核配置从列表
     *
     * @param checkConfigSlave 考核配置从
     * @return 考核配置从集合
     */
    public List<CheckConfigSlave> selectCheckConfigSlaveList(CheckConfigSlave checkConfigSlave);

    /**
     * 新增考核配置从
     *
     * @param checkConfigSlave 考核配置从
     * @return 结果
     */
    public int insertCheckConfigSlave(CheckConfigSlave checkConfigSlave);


    public int batchCheckConfigSlave(List<CheckConfigSlave> checkConfigSlaves);

    public int replaceCheckConfigSlave(List<CheckConfigSlave> checkConfigSlaves);

    /**
     * 修改考核配置从
     *
     * @param checkConfigSlave 考核配置从
     * @return 结果
     */
    public int updateCheckConfigSlave(CheckConfigSlave checkConfigSlave);

    /**
     * 批量删除考核配置从
     *
     * @param ids 需要删除的考核配置从主键集合
     * @return 结果
     */
    public int deleteCheckConfigSlaveByIds(Long[] ids);

    /**
     * 删除考核配置从信息
     *
     * @param id 考核配置从主键
     * @return 结果
     */
    public int deleteCheckConfigSlaveById(Long id);
}
