package org.ruoyi.core.kaohe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.kaohe.domain.CheckConfigSlave;
import org.ruoyi.core.kaohe.service.ICheckConfigSlaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 考核配置从Controller
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/check/config/slave")
public class CheckConfigSlaveController extends BaseController
{
    @Autowired
    private ICheckConfigSlaveService checkConfigSlaveService;

    /**
     * 查询考核配置从列表
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckConfigSlave checkConfigSlave)
    {
        startPage();
        List<CheckConfigSlave> list = checkConfigSlaveService.selectCheckConfigSlaveList(checkConfigSlave);
        return getDataTable(list);
    }

    /**
     * 导出考核配置从列表
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:export')")
    @Log(title = "考核配置从", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckConfigSlave checkConfigSlave)
    {
        List<CheckConfigSlave> list = checkConfigSlaveService.selectCheckConfigSlaveList(checkConfigSlave);
        ExcelUtil<CheckConfigSlave> util = new ExcelUtil<CheckConfigSlave>(CheckConfigSlave.class);
        util.exportExcel(response, list, "考核配置从数据");
    }

    /**
     * 获取考核配置从详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(checkConfigSlaveService.selectCheckConfigSlaveById(id));
    }

    /**
     * 新增考核配置从
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:add')")
    @Log(title = "考核配置从", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckConfigSlave checkConfigSlave)
    {
        return toAjax(checkConfigSlaveService.insertCheckConfigSlave(checkConfigSlave));
    }

    /**
     * 修改考核配置从
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:edit')")
    @Log(title = "考核配置从", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckConfigSlave checkConfigSlave)
    {
        return toAjax(checkConfigSlaveService.updateCheckConfigSlave(checkConfigSlave));
    }

    /**
     * 删除考核配置从
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:remove')")
    @Log(title = "考核配置从", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(checkConfigSlaveService.deleteCheckConfigSlaveByIds(ids));
    }
}
