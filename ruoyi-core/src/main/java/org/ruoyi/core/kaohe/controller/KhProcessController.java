package org.ruoyi.core.kaohe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.kaohe.domain.KhProcess;
import org.ruoyi.core.kaohe.service.IKhProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 绩效考核流程Controller
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
@RestController
@RequestMapping("/check/process")
public class KhProcessController extends BaseController
{
    @Autowired
    private IKhProcessService khProcessService;

    /**
     * 查询绩效考核流程列表
     */
    //@PreAuthorize("@ss.hasPermi('system:process:list')")
    @GetMapping("/list")
    public TableDataInfo list(KhProcess khProcess)
    {
        startPage();
        List<KhProcess> list = khProcessService.selectKhProcessList(khProcess);
        return getDataTable(list);
    }

    /**
     * 导出绩效考核流程列表
     */
    //@PreAuthorize("@ss.hasPermi('system:process:export')")
    @Log(title = "绩效考核流程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KhProcess khProcess)
    {
        List<KhProcess> list = khProcessService.selectKhProcessList(khProcess);
        ExcelUtil<KhProcess> util = new ExcelUtil<KhProcess>(KhProcess.class);
        util.exportExcel(response, list, "绩效考核流程数据");
    }

    /**
     * 获取绩效考核流程详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:process:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(khProcessService.selectKhProcessById(id));
    }

    /**
     * 新增绩效考核流程
     */
    //@PreAuthorize("@ss.hasPermi('system:process:add')")
    @Log(title = "绩效考核流程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KhProcess khProcess)
    {
        return toAjax(khProcessService.insertKhProcess(khProcess));
    }

    /**
     * 修改绩效考核流程
     */
    //@PreAuthorize("@ss.hasPermi('system:process:edit')")
    @Log(title = "绩效考核流程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KhProcess khProcess)
    {
        return toAjax(khProcessService.updateKhProcess(khProcess));
    }

    /**
     * 删除绩效考核流程
     */
    //@PreAuthorize("@ss.hasPermi('system:process:remove')")
    @Log(title = "绩效考核流程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(khProcessService.deleteKhProcessByIds(ids));
    }
}
