package org.ruoyi.core.lyx.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.lyx.domain.LyxUploadFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ILyxUploadFileService.java
 * @Description TODO
 * @createTime 2024年03月11日 09:28:00
 */
public interface ILyxUploadFileService {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public LyxUploadFile selectLyxUploadFileById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param lyxUploadFile 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<LyxUploadFile> selectLyxUploadFileList(LyxUploadFile lyxUploadFile);

    /**
     * 新增【请填写功能名称】
     *
     * @param lyxUploadFile 【请填写功能名称】
     * @return 结果
     */
    public int insertLyxUploadFile(LyxUploadFile lyxUploadFile);

    /**
     * 修改【请填写功能名称】
     *
     * @param lyxUploadFile 【请填写功能名称】
     * @return 结果
     */
    public int updateLyxUploadFile(LyxUploadFile lyxUploadFile);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxUploadFileByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxUploadFileById(Long id);

    Map<String, Object> uploadFile(MultipartFile file, LoginUser loginUser);

    AjaxResult deleteFileByUrl(Map<String, String> obj);
}
