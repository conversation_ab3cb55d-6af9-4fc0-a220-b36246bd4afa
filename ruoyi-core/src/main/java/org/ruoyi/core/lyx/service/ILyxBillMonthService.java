package org.ruoyi.core.lyx.service;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.lyx.domain.LyxBillMonth;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ILyxBillMonthService.java
 * @Description TODO
 * @createTime 2024年03月11日 09:14:00
 */
public interface ILyxBillMonthService {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public LyxBillMonth selectLyxBillMonthById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param lyxBillMonth 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<LyxBillMonth> selectLyxBillMonthList(LyxBillMonth lyxBillMonth);

    /**
     * 新增【请填写功能名称】
     *
     * @param lyxBillMonth 【请填写功能名称】
     * @return 结果
     */
    public Map<String,Object> insertLyxBillMonth(LyxBillMonth lyxBillMonth);

    /**
     * 修改【请填写功能名称】
     *
     * @param lyxBillMonth 【请填写功能名称】
     * @return 结果
     */
    public int updateLyxBillMonth(LyxBillMonth lyxBillMonth);
    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxBillMonthByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxBillMonthById(Long id);
}
