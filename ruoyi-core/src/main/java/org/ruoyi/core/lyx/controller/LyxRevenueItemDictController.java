package org.ruoyi.core.lyx.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.lyx.domain.LyxRevenueItemDict;
import org.ruoyi.core.lyx.service.ILyxRevenueItemDictService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2024-03-11
 */
@RestController
@RequestMapping("/lyxSystem/dict")
public class LyxRevenueItemDictController extends BaseController
{
    @Autowired
    private ILyxRevenueItemDictService lyxRevenueItemDictService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/list")
    public TableDataInfo list(LyxRevenueItemDict lyxRevenueItemDict)
    {
        startPage();
        List<LyxRevenueItemDict> list = lyxRevenueItemDictService.selectLyxRevenueItemDictList(lyxRevenueItemDict);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:dict:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LyxRevenueItemDict lyxRevenueItemDict)
    {
        List<LyxRevenueItemDict> list = lyxRevenueItemDictService.selectLyxRevenueItemDictList(lyxRevenueItemDict);
        ExcelUtil<LyxRevenueItemDict> util = new ExcelUtil<LyxRevenueItemDict>(LyxRevenueItemDict.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lyxRevenueItemDictService.selectLyxRevenueItemDictById(id));
    }

//    /**
//     * 新增【请填写功能名称】
//     */
//    @PreAuthorize("@ss.hasPermi('system:dict:add')")
//    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody LyxRevenueItemDict lyxRevenueItemDict)
//    {
//        return toAjax(lyxRevenueItemDictService.insertLyxRevenueItemDict(lyxRevenueItemDict));
//    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LyxRevenueItemDict lyxRevenueItemDict)
    {
        return toAjax(lyxRevenueItemDictService.updateLyxRevenueItemDict(lyxRevenueItemDict));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lyxRevenueItemDictService.deleteLyxRevenueItemDictByIds(ids));
    }
}
