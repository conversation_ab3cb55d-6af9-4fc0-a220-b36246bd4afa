package org.ruoyi.core.lyx.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 【请填写功能名称】对象 lyx_day_check
 * 
 * <AUTHOR>
 * @date 2024-03-11
 */
public class LyxDayCheckVO
{

    private LyxDayCheck lyxDayCheck;

    /**
     *
     * 收入明细集合
     */
    private List<LyxDynamicIncome> lyxDynamicIncomeList;


    /**
     *上传文件
     */
    private List<LyxUploadFile> lyxUploadFileList;


    private Long lyxDayId;

    private String dayTime;

    private String operation;

    public Long getLyxDayId() {
        return lyxDayId;
    }

    public void setLyxDayId(Long lyxDayId) {
        this.lyxDayId = lyxDayId;
    }

    public String getDayTime() {
        return dayTime;
    }

    public void setDayTime(String dayTime) {
        this.dayTime = dayTime;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public LyxDayCheck getLyxDayCheck() {
        return lyxDayCheck;
    }

    public void setLyxDayCheck(LyxDayCheck lyxDayCheck) {
        this.lyxDayCheck = lyxDayCheck;
    }

    public List<LyxDynamicIncome> getLyxDynamicIncomeList() {
        return lyxDynamicIncomeList;
    }

    public void setLyxDynamicIncomeList(List<LyxDynamicIncome> lyxDynamicIncomeList) {
        this.lyxDynamicIncomeList = lyxDynamicIncomeList;
    }

    public List<LyxUploadFile> getLyxUploadFileList() {
        return lyxUploadFileList;
    }

    public void setLyxUploadFileList(List<LyxUploadFile> lyxUploadFileList) {
        this.lyxUploadFileList = lyxUploadFileList;
    }

    public LyxDayCheckVO(LyxDayCheck lyxDayCheck, List<LyxDynamicIncome> lyxDynamicIncomeList, List<LyxUploadFile> lyxUploadFileList, Long lyxDayId, String dayTime, String operation) {
        this.lyxDayCheck = lyxDayCheck;
        this.lyxDynamicIncomeList = lyxDynamicIncomeList;
        this.lyxUploadFileList = lyxUploadFileList;
        this.lyxDayId = lyxDayId;
        this.dayTime = dayTime;
        this.operation = operation;
    }


    public LyxDayCheckVO() {
    }
}
