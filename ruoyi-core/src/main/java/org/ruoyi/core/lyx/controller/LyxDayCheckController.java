package org.ruoyi.core.lyx.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.oss.OSSUtil;
import org.apache.commons.io.FilenameUtils;
import org.ruoyi.core.lyx.domain.*;
import org.ruoyi.core.lyx.service.ILyxBillMonthService;
import org.ruoyi.core.lyx.service.ILyxDayCheckService;
import org.ruoyi.core.lyx.service.ILyxRevenueItemDictService;
import org.ruoyi.core.lyx.service.ILyxUploadFileService;
import org.ruoyi.core.lyx.service.impl.LyxUploadFileServiceImpl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 *
 * 每日台账
 * <AUTHOR>
 * @date 2024-03-11
 */
@RestController
@RequestMapping("/lyxSystem/dayCheck")
public class LyxDayCheckController extends BaseController
{
    @Autowired
    private ILyxDayCheckService lyxDayCheckService;

    @Autowired
    private ILyxBillMonthService iLyxBillMonthService;

    @Autowired
    private ILyxRevenueItemDictService lyxRevenueItemDictService;

    @Autowired
    private ILyxUploadFileService lyxUploadFileService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:check:list')")
    @GetMapping("/list")
    public TableDataInfo list(LyxDayCheck lyxDayCheck)
    {
        startPage();
        List<LyxDayCheck> list = lyxDayCheckService.selectLyxDayCheckList(lyxDayCheck);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
//    @PreAuthorize("@ss.hasPermi('system:check:export')")
//    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LyxBillMonth lyxBillMonth) throws IOException {
//        List<LyxDayCheck> list = lyxDayCheckService.selectLyxDayCheckList(lyxDayCheck);
//        ExcelUtil<LyxDayCheck> util = new ExcelUtil<LyxDayCheck>(LyxDayCheck.class);
//        util.exportExcel(response, list, "【请填写功能名称】数据");
        lyxDayCheckService.exportDataByMonth(response,lyxBillMonth);
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:check:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lyxDayCheckService.selectLyxDayCheckById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:check:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LyxDayCheck lyxDayCheck)
    {
        return toAjax(lyxDayCheckService.insertLyxDayCheck(lyxDayCheck));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:check:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LyxDayCheck lyxDayCheck)
    {
        return toAjax(lyxDayCheckService.updateLyxDayCheck(lyxDayCheck));
    }

    /**
     * 删除【请填写功能名称】
     */
//    @PreAuthorize("@ss.hasPermi('system:check:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lyxDayCheckService.deleteLyxDayCheckByIds(ids));
    }


    /**
     * 校验台账月份唯一性并添加
     * @param lyxBillMonth
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @PutMapping("/addMonthCheck")
    public Map<String, Object> addMonthCheck(@RequestBody LyxBillMonth lyxBillMonth)
    {
        return  iLyxBillMonthService.insertLyxBillMonth(lyxBillMonth);

    }

    /**
     * 查找台账月份下拉框数据
     * @param lyxBillMonth
     * @return {@link List}<{@link LyxBillMonth}>
     */
    @GetMapping("/queryMonthCheck")
    public List<LyxBillMonth> getMonthSelect (LyxBillMonth lyxBillMonth)
    {
        return iLyxBillMonthService.selectLyxBillMonthList(lyxBillMonth);
    }

    /**
     * 删除台账月份
     * @param ids
     * @return {@link AjaxResult}
     */
    @DeleteMapping("/deleteMonthCheck/{ids}")
    public AjaxResult removeMonthCheck(@PathVariable Long[] ids)
    {
        return toAjax(iLyxBillMonthService.deleteLyxBillMonthByIds(ids));
    }

    /**
     * 获取动态收入项下拉框字典数据
     * @return {@link List}<{@link LyxRevenueItemDict}>
     */
    @GetMapping("/queryLyxDictData")
    public List<LyxRevenueItemDict> getItemDict ()
    {
        LyxRevenueItemDict lyxRevenueItemDict = new LyxRevenueItemDict();
        return lyxRevenueItemDictService.selectLyxRevenueItemDictList(lyxRevenueItemDict);
    }


    /**
     *
     * 添加动态收入项下拉框字典数据
     * @param lyxRevenueItemDict
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @PutMapping("/addItemDictData")
    public Map<String, Object> addItemDIctData(@RequestBody LyxRevenueItemDict lyxRevenueItemDict)
    {
        return  lyxRevenueItemDictService.insertLyxRevenueItemDict(lyxRevenueItemDict);

    }

    /**
     * 删除动态收入字典数据
     * @param ids
     * @return {@link AjaxResult}
     */
    @DeleteMapping("/removeItemDict/{ids}")
    public AjaxResult removeItemDict(@PathVariable Long[] ids)
    {
        return toAjax(lyxRevenueItemDictService.deleteLyxRevenueItemDictByIds(ids));
    }

    /**
     * 只上传文件
     */
    @PostMapping("/lyxUploadFile")
    public Map<String, Object> uploadFile(MultipartFile file) {
        LoginUser loginUser = getLoginUser();
        return lyxUploadFileService.uploadFile(file, loginUser);
    }

    /**
     * 只删除文件
     */
    @PostMapping("/lyxDeleteFileByUrl")
    public AjaxResult deleteFileByUrl(@RequestBody Map<String, String> obj) {
        return lyxUploadFileService.deleteFileByUrl(obj);
    }

    /**
     * 下载文件
     */
    @GetMapping("/lyxFile/download")
    public void download(HttpServletRequest request, HttpServletResponse response, String url) {
        String filePath = FilenameUtils.getPath(url);
        filePath = "/" + filePath;
        String fileName = FilenameUtils.getName(url);
        //修复下载后的文件是空文件的问题
        String replace = filePath.replace(Constants.OSS_VIEW_PREFIX, "");
        OSSUtil.downloadFile(replace, fileName, response);
    }

    /**
     * 修改或新增每日台账
     *
     * @param lyxDayCheck
     * @return {@link AjaxResult}
     */
    @PostMapping("/insertOrUpdate")
    public AjaxResult addOrUpdate(@RequestBody LyxDayCheckVO lyxDayCheck)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(lyxDayCheckService.inserOrUpdate(lyxDayCheck,loginUser));
    }



    @GetMapping("/checkDayDate")
    public Map<String,Object> checklyxDayDate(LyxDayCheckVO lyxDayCheck){
        return lyxDayCheckService.checkDayDate(lyxDayCheck);
    }


    /**
     * 获取每日台账详情信息
     * @param id
     * @return {@link AjaxResult}
     */
    @GetMapping(value = "/getDetails/{id}")
    public AjaxResult getDetailsById(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lyxDayCheckService.getDetailsById(id));
    }
    @GetMapping("/dayCheckList")
    public Map<String,Object> dayCheckList(LyxDayCheck lyxDayCheck)
    {
     return lyxDayCheckService.queryDayCheckData(lyxDayCheck);
    }


    /**
     * 获取此月份最后一次录入的台账的动态收入数据
     * @param lyxBillMonth
     * @return {@link List}<{@link LyxDynamicIncome}>
     */
    @GetMapping("/lastDayCheckDynamic")
    public List<LyxDynamicIncome> dayCheckList(LyxBillMonth lyxBillMonth)
    {
        return lyxDayCheckService.getLastDayCheckDynamic(lyxBillMonth);
    }


    /**
     * 查看此凭证有没有结账
     * @param lyxDayVouchar
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/checkVoucharStatus")
    public Map<String,Object> checkVouchar(LyxDayVouchar lyxDayVouchar)
    {
        return lyxDayCheckService.checkVoucharStatus(lyxDayVouchar);
    }


    @GetMapping("/removeVouchar")
    public Map<String,Object> removeVouchar(LyxDayVouchar lyxDayVouchar)
    {
        return lyxDayCheckService.removeVouchar(lyxDayVouchar);
    }


}
