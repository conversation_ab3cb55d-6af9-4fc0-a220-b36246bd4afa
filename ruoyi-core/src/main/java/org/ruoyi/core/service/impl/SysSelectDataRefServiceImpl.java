package org.ruoyi.core.service.impl;

import com.google.gson.Gson;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.apache.logging.log4j.util.Strings;
import org.ruoyi.core.domain.DData;
import org.ruoyi.core.domain.DProjectParameter;
import org.ruoyi.core.domain.SysSelectDataRef;
import org.ruoyi.core.mapper.DProjectParameterMapper;
import org.ruoyi.core.mapper.SysSelectDataRefMapper;
import org.ruoyi.core.oasystem.domain.ProjectCompanyRelevance;
import org.ruoyi.core.oasystem.mapper.OaProjectDeployMapper;
import org.ruoyi.core.oasystem.mapper.ProjectCompanyRelevanceMapper;
import org.ruoyi.core.service.ISysSelectDataRefService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Service
public class SysSelectDataRefServiceImpl implements ISysSelectDataRefService
{
    @Autowired
    private SysSelectDataRefMapper sysSelectDataRefMapper;

    @Autowired
    private DProjectParameterMapper dProjectParameterMapper;

    @Autowired
    private ProjectCompanyRelevanceMapper projectCompanyRelevanceMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityService;

    @Autowired
    private OaProjectDeployMapper oaProjectDeployMapper;
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SysSelectDataRef selectSysSelectDataRefById(Long id)
    {
        return sysSelectDataRefMapper.selectSysSelectDataRefById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param sysSelectDataRef 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SysSelectDataRef> selectSysSelectDataRefList(SysSelectDataRef sysSelectDataRef)
    {
        return sysSelectDataRefMapper.selectSysSelectDataRefList(sysSelectDataRef);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param sysSelectDataRef 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSysSelectDataRef(SysSelectDataRef sysSelectDataRef)
    {
        sysSelectDataRef.setCreateTime(DateUtils.getNowDate());
        return sysSelectDataRefMapper.insertSysSelectDataRef(sysSelectDataRef);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param sysSelectDataRef 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSysSelectDataRef(SysSelectDataRef sysSelectDataRef)
    {
        sysSelectDataRef.setUpdateTime(DateUtils.getNowDate());
        return sysSelectDataRefMapper.updateSysSelectDataRef(sysSelectDataRef);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysSelectDataRefByIds(Long[] ids)
    {
        return sysSelectDataRefMapper.deleteSysSelectDataRefByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysSelectDataRefById(Long id)
    {
        return sysSelectDataRefMapper.deleteSysSelectDataRefById(id);
    }

    /**
     * 根据所传数据条件筛选出当前登录人有权限的担保公司或者合作方产品等
     * @param projectCompanyRelevance
     * @param loginUser
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public List<Map<String, Object>> queryCompanyByProjectId(ProjectCompanyRelevance projectCompanyRelevance, LoginUser loginUser) {
        List<Map<String, Object>> returnList = new ArrayList<>();
        String unitType = projectCompanyRelevance.getUnitType();
        List<Long> projectIds = new ArrayList<>();
        if(loginUser.getUsername().equals("admin")){
            projectIds = null;
        }else {
            projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), projectCompanyRelevance.getModuleTypeOfNewAuth());
        }
        if((!loginUser.getUsername().equals("admin")) && projectIds.size()==0){
            return returnList;
        }else {
            //有权限的  0担保公司  1资产方  2资金方 3产品 4系统 5项目 6其他公司
            if (unitType.equals("0") || unitType.equals("1") || unitType.equals("2")) {
                //获取有权限的项目id
                returnList = sysSelectDataRefMapper.selectUserRoleCompany(unitType, projectIds);
            } else if (unitType.equals("3")) {
                returnList = dProjectParameterMapper.queryproductListByProjectId(projectIds);
            } else if (unitType.equals("4")) {
                returnList = dProjectParameterMapper.querySysDataListByProjectId(projectIds);
            } else if (unitType.equals("5")) {
                returnList = oaProjectDeployMapper.getProjectDataById(projectIds);
            } else if (unitType.equals("6")) {
                returnList = sysSelectDataRefMapper.selectUserRoleCompany("3", projectIds);
            }
        }
        return returnList;

    }


    /**
     *  初始化历史数据
     */
    public void InitializationData(){
        //初始化数据前先清空表
        sysSelectDataRefMapper.deleteAllData();
        //查询所有有项目id的产品
        List<DProjectParameter> dProjectParameterList = dProjectParameterMapper.selectProjectList();
        for (DProjectParameter dProjectParameter : dProjectParameterList) {
            this.insertSelectData(dProjectParameter,0);
        }
    }

    /**
     *产品新增或者修改调用
     *
     *
     * 根据产品信息新增关系表
     * @param dProjectParameter 产品信息实体
     * @param operation 操作（0新增，1修改）
     * @return int
     */
    public int insertSelectData(DProjectParameter dProjectParameter,int operation){
        int i = 0;

        if(null == dProjectParameter.getProjectId() || dProjectParameter.getProjectId().equals("")){
            i=0;
        }else {
            SysSelectDataRef sysSelectDataRef = new SysSelectDataRef();
            if(null == dProjectParameter.getSystemNo() || dProjectParameter.getSystemNo().equals("")){
                return i;
            }else {
                sysSelectDataRef.setPlatformId(Long.valueOf(dProjectParameter.getSystemNo()));
            }
//        sysSelectDataRef.setPlatformId(Long.valueOf(dProjectParameter.getSystemNo()));
            sysSelectDataRef.setProjectId(dProjectParameter.getProjectId());
            sysSelectDataRef.setProductId(Long.valueOf(dProjectParameter.getId()));
            sysSelectDataRef.setProductNo(dProjectParameter.getProductNo());
            sysSelectDataRef.setCreateBy("SYSTEM");
            sysSelectDataRef.setCreateTime(new Date());
            sysSelectDataRef.setUpdateBy("SYSTEM");
            sysSelectDataRef.setUpdateTime(new Date());
            //修改的话为了避免出现修改问题，所以在删除后新增
            if (operation == 0) {
                //根据项目id获取项目的担保公司 合作方 资金方id
                Map<String, Object> companyIdByProject = this.getCompanyIdByProjectId(dProjectParameter.getProjectId());
                sysSelectDataRef.setCustId(companyIdByProject.get("cust").toString());
                sysSelectDataRef.setPartnerId(companyIdByProject.get("partner").toString());
                sysSelectDataRef.setFundId(companyIdByProject.get("fund").toString());

            } else if (operation == 1) {
                //因为是按照产品为最小维度，所以删除是根据产品id去删，产品在这个表里唯一
                sysSelectDataRefMapper.deleteSysSelectDataRefByProductId(dProjectParameter.getId());
                Map<String, Object> companyIdByProject = this.getCompanyIdByProjectId(dProjectParameter.getProjectId());
                sysSelectDataRef.setCustId(companyIdByProject.get("cust").toString());
                sysSelectDataRef.setPartnerId(companyIdByProject.get("partner").toString());
                sysSelectDataRef.setFundId(companyIdByProject.get("fund").toString());
            }

            i = sysSelectDataRefMapper.insertSysSelectDataRef(sysSelectDataRef);
        }
        return i;
    }


    /**
     * 根据项目id获取项目的担保公司资产方资金方等
     * @param projectId
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> getCompanyIdByProjectId(Long projectId){
        HashMap<String, Object> returnMap = new HashMap<>();
        String cust = "";
        String partner = "";
        String fund = "";
        ProjectCompanyRelevance projectCompanyRelevance = new ProjectCompanyRelevance();
        projectCompanyRelevance.setProjectId(projectId);
        List<ProjectCompanyRelevance> projectCompanyRelevances = projectCompanyRelevanceMapper.selectProjectCompanyRelevanceList(projectCompanyRelevance);
        //由于担保公司资金方可能会有多个，为了查询不出问题，所以拼接为‘ #id# ’，避免出现模糊匹配出现问题的情况
        for (ProjectCompanyRelevance companyRelevance : projectCompanyRelevances) {
            if(companyRelevance.getUnitType().equals("0")){
                cust = cust+"#"+companyRelevance.getUnitId()+"#";
            }else if(companyRelevance.getUnitType().equals("1")){
                partner = partner+"#"+companyRelevance.getUnitId()+"#";
            }else if(companyRelevance.getUnitType().equals("2")){
                fund = fund+"#"+companyRelevance.getUnitId()+"#";
            }
        }

        returnMap.put("cust",cust);
        returnMap.put("partner",partner);
        returnMap.put("fund",fund);
        return returnMap;
    }


    /**
     * 项目更新后调用这个方法
     * @param projectId
     */
    public void projectUpdateRef(Long projectId){
        //获取项目更新后的数据
        Map<String, Object> companyIdByProject = this.getCompanyIdByProjectId(projectId);
        //获取关系表中跟此项目有关的数据
        SysSelectDataRef sysSelectDataRef = new SysSelectDataRef();
        sysSelectDataRef.setProjectId(projectId);
        List<SysSelectDataRef> sysSelectDataRefs = sysSelectDataRefMapper.selectSysSelectDataRefList(sysSelectDataRef);
        if(sysSelectDataRefs.size()>0){
            for (SysSelectDataRef selectDataRef : sysSelectDataRefs) {
                selectDataRef.setCustId(companyIdByProject.get("cust").toString());
                selectDataRef.setPartnerId(companyIdByProject.get("partner").toString());
                selectDataRef.setFundId(companyIdByProject.get("fund").toString());
            }
            int i = sysSelectDataRefMapper.updateBatch(sysSelectDataRefs);
        }

    }


    /**
     * 删除产品后 同时删除关系表中相关信息
     * @param productId
     * @return int
     */
    public int deleteProduct(Long productId){
      return sysSelectDataRefMapper.deleteSysSelectDataRefByProductId(Integer.parseInt(productId.toString()));
    }


    /**
     * 根据传参获取产品编码
     *
     * @param dData
     * @return 返回的是字符串（集合构建成的字符串）
     */
    public String getProductCode(DData dData){
        List<String> platforms = null;
        if (Strings.isNotEmpty(dData.getPlatformNo())) {
            platforms = Arrays.asList(dData.getPlatformNo().split(","));
        }
        List<String> custNos = null;
        if (Strings.isNotEmpty(dData.getCustNo())) {
            custNos = Arrays.asList(dData.getCustNo().split(","));
        }
        List<String> partnerNos = null;
        if (Strings.isNotEmpty(dData.getPartnerNo())) {

            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
        }
        List<String> fundNos = null;
        if (Strings.isNotEmpty(dData.getFundNo())) {

            fundNos = Arrays.asList(dData.getFundNo().split(","));
        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }
        if (dData.getMoreSearch() != null && !dData.getMoreSearch().isEmpty()){
            Gson gson = new Gson();
            // 将字符串转换为 Map<String, List<Long>>
            Map<String, List<String>> moreSearch = gson.fromJson(dData.getMoreSearch(), Map.class);

            for (Map.Entry<String, List<String>> entry : moreSearch.entrySet()) {
                if (entry.getValue() != null) {
                    List<?> valueList = (List<?>) entry.getValue();
                    List<String> stringList = new ArrayList<>();
                    for (Object item : valueList) {
                        if (item instanceof Double) {
                            stringList.add(String.valueOf(((Double) item).longValue())); // 将 Double 转换为 String
                        } else if (item instanceof String) {
                            stringList.add((String) item);
                        }
                    }
                    if ("cust".equals(entry.getKey())){
                        if (custNos == null || custNos.isEmpty()){
                            custNos = new ArrayList<>();
                        }
                        custNos.addAll(stringList);
                    }
                    if ("partner".equals(entry.getKey())){
                        if (partnerNos == null || partnerNos.isEmpty()){
                            partnerNos = new ArrayList<>();
                        }
                        partnerNos.addAll(stringList);
                    }
                    if ("fund".equals(entry.getKey())){
                        if (fundNos == null || fundNos.isEmpty()){
                            fundNos = new ArrayList<>();
                        }
                        fundNos.addAll(stringList);
                    }
                }
            }
        }

        if(null == platforms && custNos ==null && partnerNos == null && fundNos == null && products== null){
            return null;
        }else {
            List<String> productCodeList = sysSelectDataRefMapper.selectProductCodeList(platforms, custNos, partnerNos, fundNos);
            if (productCodeList.size() == 0) {
                //返回处理
                List<String> list = new ArrayList<>();
                list.add("99zz***99**!!@@99z9z9z9881z");
                //处理结束
                productCodeList = list;
            }
            //并集
            if (null != products) {
                return products.stream().collect(Collectors.joining(","));
            } else if (null == products) {
                return productCodeList.stream().collect(Collectors.joining(","));
            }
        }
//        if(null != productCodeList){
//            returnList.addAll(productCodeList);
//        }
//        List<String> listAllDistinct = new ArrayList<>();
//        // 去重并集
//        if(null != productCodeList){
//          listAllDistinct = returnList.stream().distinct().collect(Collectors.toList());
//        }
        return null;
    }

    /**
     * 根据传参获取产品编码
     *
     * @param dData
     * @return 返回的是集合
     */
    public List<String> getProductCode1(DData dData){
        List<String> platforms = null;
        if (Strings.isNotEmpty(dData.getPlatformNo())) {
            platforms = Arrays.asList(dData.getPlatformNo().split(","));
        }
        List<String> custNos = null;
        if (Strings.isNotEmpty(dData.getCustNo())) {
            custNos = Arrays.asList(dData.getCustNo().split(","));
        }
        List<String> partnerNos = null;
        if (Strings.isNotEmpty(dData.getPartnerNo())) {

            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
        }
        List<String> fundNos = null;
        if (Strings.isNotEmpty(dData.getFundNo())) {

            fundNos = Arrays.asList(dData.getFundNo().split(","));
        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }

        if(null == platforms && custNos ==null && partnerNos == null && fundNos == null && products== null){
            return null;
        }else {
            List<String> productCodeList = sysSelectDataRefMapper.selectProductCodeList(platforms, custNos, partnerNos, fundNos);
            if (productCodeList.size() == 0) {
                //返回处理
                List<String> list = new ArrayList<>();
                list.add("99zz***99**!!@@99z9z9z9881z");
                //处理结束
                productCodeList = list;
            }
            //并集
            if (null != products) {
                return products;
            } else if (null == products) {
                return productCodeList;
            }
        }
//        if(null != productCodeList){
//            returnList.addAll(productCodeList);
//        }
//        List<String> listAllDistinct = new ArrayList<>();
//        // 去重并集
//        if(null != productCodeList){
//          listAllDistinct = returnList.stream().distinct().collect(Collectors.toList());
//        }
        return null;
    }


}
