package org.ruoyi.core.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.domain.DData;
import org.ruoyi.core.domain.Data.DDataT;
import org.ruoyi.core.domain.Data.DDataTByMonthOrYear;
import org.ruoyi.core.domain.Data.MonCompeData;
import org.ruoyi.core.domain.Data.Overdue;
import org.ruoyi.core.yybbsc.domain.dto.ImportData;

import java.util.List;
import java.util.Map;

/**
 * 外部系统平台运营情况数据表(DData)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-12 16:42:49
 */
public interface DDataService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DData queryById(Long id);

    /**
     * 分页查询
     *
     * @param dData 筛选条件
     * @return 查询结果
     */
    List<DData> queryByPage(DData dData);

    /**
     * 导出数据
     *
     * @param dData 维数据
     * @return {@link List}<{@link DData}>
     */
    public List<DData> exportData(DData dData);

    /**
     * 导出数据
     *
     * @param dData 维数据
     * @return {@link List}<{@link DData}>
     */
    public List<Map<String, Object>> exportData1(DData dData);

    /**
     * 新增数据
     *
     * @param dData 实例对象
     * @return 新增条数
     */
    int insert(DData dData);
    int insertbf(DData dData);
    int insertDData(ImportData dData);

    /**
     * 修改数据
     *
     * @param dData 实例对象
     * @return 更新条数
     */
    int update(DData dData);
    int updateDData(ImportData dData);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 通过主键删除数据
     *
     * @param ids 主键
     * @return 删除条数
     */
    public int deleteDDataByIds(Long[] ids);

    /**
     * 重新映射
     */
    void anewMappingData();

    /**
     * 年获得数据
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> getDataYear();



    /**
     * 获取担保数据统计数据
     *
     * @param dData
     * @param platformNos
     * @return
     */
    Map<String, Object> queryData(DData dData, List<String> platformNos, List<String> custNos, List<String> partnerNos, List<String> fundNos,List<String> products);


    List<DDataT> queryByPaget(DDataT dDataT);

    /**
     * 导出数据2
     *
     * @param dDataT 维数据
     * @return {@link List}<{@link DData}>
     */
    public List<DDataT> exportData2(DDataT dDataT);


    List<MonCompeData> queryMonCompeByData(DData dData, List<String> platformNos, List<String> partnerNos, List<String> fundNos, List<String> custNos,List<String> products) ;

    List<Overdue> queryByOverdue(Overdue overdue, List<String> platformNos, List<String> partnerNos,List<String> custNos,List<String> products);

    /**
     * 运营情况月报表列表查询
     */
    List<DDataTByMonthOrYear> queryMonthByPaget(DDataTByMonthOrYear obj);

    /**
     * 运营情况年报表列表查询
     */
    List<DDataTByMonthOrYear> queryYearByPaget(DDataTByMonthOrYear obj);

    /**
     * 查询有小于当前日期的数据
     * @param dData
     * @return
     */
    List<DData> selectDdatas(DData dData);

    /**
     * 查询是否有本条数据
     * @param dData
     * @return
     */
    List<DData> selectDdata(DData dData);



    List<DData> selectDdatabf(DData dData1);

    DData selectDdatabf1(DData dData);

    DData selectDdataslj(DData dDataas);

    int selectDdatasinsertorUpdate(List<DData> list);
    List<DData> queryAll(DData dData);
    List<DData> queryAllbf(DData dData);

    //打压缩包
    String batchExcelZip(DData dData,LoginUser loginUser);
}
