package org.ruoyi.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysDictDataRefMapper;
import com.ruoyi.system.util.UserRoles;
import org.apache.logging.log4j.util.Strings;
import org.ruoyi.core.domain.DData;
import org.ruoyi.core.domain.DProfitParameter;
import org.ruoyi.core.mapper.DProfitParameterMapper;
import org.ruoyi.core.service.IDProfitParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 【利润测算参数设置】Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-25
 */
@Service
public class DProfitParameterServiceImpl implements IDProfitParameterService
{
    @Autowired
    private DProfitParameterMapper dProfitParameterMapper;

    @Autowired
    private SysDictDataRefMapper sysDictDataRefMapper;

    @Autowired
    private SysSelectDataRefServiceImpl sysSelectDataRefService;

    /**
     * 查询【利润测算参数设置】
     *
     * @param id 【利润测算参数设置】主键
     * @return 【利润测算参数设置】
     */
    @Override
    public DProfitParameter selectDProfitParameterById(Long id)
    {
        DProfitParameter dProfitParameter = dProfitParameterMapper.selectDProfitParameterById(id);
        if (StringUtils.EMPTY.equals(dProfitParameter.getFundNo())) {
            dProfitParameter.setFundNo("ZIJINFANGNOTDEFINITION");
        }
        if (dProfitParameter.getFlowCost() != null) {
            dProfitParameter.setFlowCostControl(dProfitParameter.getFlowCost());
        } else {
            dProfitParameter.setFlowCostControl(dProfitParameter.getFlowCostRate());
        }
        return dProfitParameter;
    }

    /**
     * 查询【利润测算参数设置】列表
     *
     * @param dProfitParameter 【利润测算参数实体】
     * @return 【利润测算参数设置列表】
     */
    @Override
    public List<DProfitParameter> selectDProfitParameterList(DProfitParameter dProfitParameter,LoginUser loginUser)
    {

        List<String> platforms = null;
        if (Strings.isNotEmpty(dProfitParameter.getPlatformNo())) {
            platforms = Arrays.asList(dProfitParameter.getPlatformNo().split(","));
        }
        List<String> partnerNos = null;
        if (Strings.isNotEmpty(dProfitParameter.getPartnerNo())) {

            partnerNos = Arrays.asList(dProfitParameter.getPartnerNo().split(","));
        }
        List<String> fundNos = null;
        if (Strings.isNotEmpty(dProfitParameter.getFundNo())) {
            fundNos = Arrays.asList(dProfitParameter.getFundNo().split(","));
        }
        if(!UserRoles.isAdmin(loginUser)){
            String s = this.roleSql(loginUser);
            HashMap<String, Object> roleString = new HashMap<>();
            roleString.put("dataScope",s);
            dProfitParameter.setParams(roleString);
        }

        if (dProfitParameter.getMoreSearch() != null && dProfitParameter.getMoreSearch().length() > 2){
            Gson gson = new Gson();
            // 将字符串转换为 Map<String, List<Long>>
            Map<String, List<String>> moreSearch = gson.fromJson(dProfitParameter.getMoreSearch(), Map.class);
            moreSearch.remove("cust");
            if(!moreSearch.isEmpty()){
                dProfitParameter.setMoreSearchMap(moreSearch);
            }
        }
        PageUtils.startPage();
        List<DProfitParameter> dProfitParameters = dProfitParameterMapper.selectDProfitParameter(platforms, partnerNos, fundNos, dProfitParameter);
        dProfitParameters.forEach(t -> {
            if (t.getFlowCost() != null) {
                t.setFlowCostControl(t.getFlowCost());
            } else {
                t.setFlowCostControl(t.getFlowCostRate());
            }
        });
        return dProfitParameters;
    }
    //
    public String roleSql(LoginUser loginUser){
        StringBuilder stringBuilder = new StringBuilder();
        //获取角色权限的全编码  然后去关系表查询所有符合的资金方编码
        List<String> allCode = UserRoles.getAllCode(loginUser);
        Map<String, String> customRole = UserRoles.getCustomRole(loginUser);
        List<String> custNo = Arrays.asList(customRole.get("custNo").split(","));
        PageHelper.clearPage();
        List<Map<String,Object>> funds = sysDictDataRefMapper.queryFundByAllCode(allCode);

        ArrayList<String> allNoCustNo = new ArrayList<>();
        for (String s : custNo) {
            for (Map<String, Object> fund : funds) {
                String allCode1 = fund.get("allCode").toString();
                String replace = allCode1.replace("_" + s, "");
                allNoCustNo.add(replace);
            }

        }
        stringBuilder.append(" and ( ");

        for (int i =0;i<allNoCustNo.size();i++) {
            List<String> strings = Arrays.asList(allNoCustNo.get(i).split("_"));
            if(allNoCustNo.size()-1==i){
                  stringBuilder.append("(platform_no ='"+strings.get(0)+"' and partner_no = '"+strings.get(1)+"' and fund_no = '" +strings.get(2) + "')");
              }else {
                  stringBuilder.append("(platform_no ='"+strings.get(0)+"' and partner_no = '"+strings.get(1)+"' and fund_no = '" +strings.get(2) + "') or ");
              }
          }


        stringBuilder.append(" ) ");
        return  stringBuilder.toString();
    }
    @Override
    public List<DProfitParameter>exportDProfitParameterList(DProfitParameter dProfitParameter,LoginUser loginUser)
    {


        List<String> platforms = null;
        if (Strings.isNotEmpty(dProfitParameter.getPlatformNo())) {
            platforms = Arrays.asList(dProfitParameter.getPlatformNo().split(","));
        }
        List<String> partnerNos = null;
        if (Strings.isNotEmpty(dProfitParameter.getPartnerNo())) {

            partnerNos = Arrays.asList(dProfitParameter.getPartnerNo().split(","));
        }
        List<String> fundNos = null;
        if (Strings.isNotEmpty(dProfitParameter.getFundNo())) {
            fundNos = Arrays.asList(dProfitParameter.getFundNo().split(","));
        }
        String s = this.roleSql(loginUser);
        HashMap<String, Object> roleString = new HashMap<>();
        roleString.put("dataScope",s);
        dProfitParameter.setParams(roleString);
        if (dProfitParameter.getMoreSearch() != null && dProfitParameter.getMoreSearch().length() > 2){
            Gson gson = new Gson();
            // 将字符串转换为 Map<String, List<Long>>
            Map<String, List<String>> moreSearch = gson.fromJson(dProfitParameter.getMoreSearch(), Map.class);
            moreSearch.remove("cust");
            if(!moreSearch.isEmpty()){
                dProfitParameter.setMoreSearchMap(moreSearch);
            }
        }
        return dProfitParameterMapper.selectDProfitParameter(platforms,partnerNos,fundNos,dProfitParameter);
    }

    /**
     * 新增【利润测算参数设置】
     *
     * @param dProfitParameter 【利润测算参数实体】
     * @return 结果
     */
    @Override
    public int insertDProfitParameter(DProfitParameter dProfitParameter)
    {
        int i = 0;
        if ("ZIJINFANGNOTDEFINITION".equals(dProfitParameter.getFundNo())) {
            dProfitParameter.setFundNo(StringUtils.EMPTY);
        }
        dProfitParameter.setCreateTime(DateUtils.getNowDate());
        String platformNo = dProfitParameter.getPlatformNo();
        String partnerNo = dProfitParameter.getPartnerNo();
        String fundNo = dProfitParameter.getFundNo();
        List<DProfitParameter> dProfitParameters =  dProfitParameterMapper.getVerifyData(platformNo,partnerNo,fundNo,"");
        boolean add = this.isAdd(dProfitParameter, dProfitParameters);
        if(add){
            int count = dProfitParameterMapper.insertDProfitParameter(dProfitParameter);
            i = i+count;
        }
        return i;
    }

    /**
     * 修改【利润测算参数设置】
     *
     * @param dProfitParameter 【利润测算参数实体】
     * @return 结果
     */
    @Override
    public int updateDProfitParameter(DProfitParameter dProfitParameter)
    {
        int i = 0;
        if ("ZIJINFANGNOTDEFINITION".equals(dProfitParameter.getFundNo())) {
            dProfitParameter.setFundNo(StringUtils.EMPTY);
        }
        dProfitParameter.setUpdateTime(DateUtils.getNowDate());
        String platformNo = dProfitParameter.getPlatformNo();
        String partnerNo = dProfitParameter.getPartnerNo();
        String fundNo = dProfitParameter.getFundNo();
        //因为要做入库日期校验，如果只更改其他项不更改生效时间则还是判断生效时间存在就不会存入进去所以要先查除过原数据之外的数据
        List<DProfitParameter> dProfitParameters =  dProfitParameterMapper.getVerifyData(platformNo,partnerNo,fundNo,dProfitParameter.getId().toString());
        boolean add = this.isAdd(dProfitParameter, dProfitParameters);
        if(add){
            int count = dProfitParameterMapper.updateDProfitParameter(dProfitParameter);
            i = i+count;
        }
        return i;
    }

    /**
     * 批量删除【利润测算参数设置】
     *
     * @param ids 需要删除的【利润测算参数设置】主键
     * @return 结果
     */
    @Override
    public int deleteDProfitParameterByIds(Long[] ids)
    {
        return dProfitParameterMapper.deleteDProfitParameterByIds(ids);
    }

    /**
     * 删除【利润测算参数设置】信息
     *
     * @param id 【利润测算参数设置】主键
     * @return 结果
     */
    @Override
    public int deleteDProfitParameterById(Long id)
    {
        return dProfitParameterMapper.deleteDProfitParameterById(id);
    }

    @Override
    public String importProfitParameter(List<DProfitParameter> mappingsList, String operName) {
        int count = 0;
        String error = "";
        DateFormat sf =new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
        int num = 1;
        for (DProfitParameter dProfitParameter : mappingsList) {
            try {
                if (dProfitParameter.getEffectiveDate().indexOf("-")<0){
                    dProfitParameter.setEffectiveDate(sf.format(simpleDateFormat.parse(dProfitParameter.getEffectiveDate())));

                }
                if(dProfitParameter.getExpiryDate().indexOf("-")<0){
                    dProfitParameter.setExpiryDate(sf.format(simpleDateFormat.parse(dProfitParameter.getExpiryDate())));
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            dProfitParameter.setCreateBr(operName);
            int i = this.insertDProfitParameter(dProfitParameter);
            if(i==0){
                error=error+"第"+num+"条、";
            }

            count = count + i;
            num++;
        }
        return "此次导入共计"+mappingsList.size()+"条数据,导入成功"+count+"条数据，以下数据为时间范围重叠不合格数据："+error;
    }

    @Override
    public Boolean checkParamsDProfitParameter(DProfitParameter dProfitParameter) {
        DProfitParameter obj = dProfitParameterMapper.selectDProfitParameterByPlatformNoAndPartnerNoAndFundNo(dProfitParameter);
        if (obj == null) {
            return true;
        } else {
            return false;
        }
    }


    public boolean isAdd(DProfitParameter addData,List<DProfitParameter> verifyDatas){
        boolean b = true;
        DateFormat sf =new SimpleDateFormat("yyyy-MM");
        try {
            //插入的生效时间以及失效时间
            long addSTime = sf.parse(addData.getEffectiveDate()).getTime();
            long addETime = sf.parse(addData.getExpiryDate()).getTime();
            for (DProfitParameter verifyData : verifyDatas) {
                //校验的时间
                long verifySTime = sf.parse(verifyData.getEffectiveDate()).getTime();
                long verifyETime = sf.parse(verifyData.getExpiryDate()).getTime();
                if((addSTime < verifySTime && addETime <verifySTime) || (addSTime > verifyETime && addETime > verifyETime)){
                    b = true;
                }else {
                    b = false;
                    break;
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return b;
    }
}
