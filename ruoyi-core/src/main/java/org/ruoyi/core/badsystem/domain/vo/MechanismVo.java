package org.ruoyi.core.badsystem.domain.vo;

import com.ruoyi.common.core.domain.entity.SysUser;
import lombok.Data;
import org.ruoyi.core.badsystem.domain.Mechanism;
import org.ruoyi.core.badsystem.domain.MechanismContacts;
import org.ruoyi.core.badsystem.domain.MechanismFinanceAccount;
import org.ruoyi.core.badsystem.domain.MechanismSettlementFormula;

import java.math.BigDecimal;
import java.util.List;

/**
 * 机构对象 bl_mechanism
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
public class MechanismVo extends Mechanism
{
    private static final long serialVersionUID = 1L;
    /**
     * 机构-结算公式明细对象列表
     */
    private List<MechanismSettlementFormula> mechanismSettlementFormulaList;
    /**
     * 机构-结算账号明细对象列表
     */
    private List<MechanismFinanceAccount> mechanismFinanceAccountList;
    /**
     * 机构-联系人对象列表
     */
    private List<MechanismContacts> mechanismContactsList;
    /**
     * 机构-联系人对象列表
     */
    private List<SysUser> businessManagerList;

    private List<String> mechanismNameList;

    /**
     * 累计委案本金
     */
    private BigDecimal totalOutsourcedPrincipal;

    /**
     * 委后协助还款总额
     */
    private BigDecimal totalRepaymentAmount;

    private int count;
}
