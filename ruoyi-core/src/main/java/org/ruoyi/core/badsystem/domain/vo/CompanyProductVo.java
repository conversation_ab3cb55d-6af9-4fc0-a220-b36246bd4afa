package org.ruoyi.core.badsystem.domain.vo;

import lombok.Data;
import org.ruoyi.core.badsystem.domain.CompanyProduct;
import org.ruoyi.core.badsystem.domain.CompanyProductInterest;

import java.util.List;

/**
 * 不良系统-公司产品对象 bl_company_product
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
public class CompanyProductVo extends CompanyProduct
{
    /**
     * 不良系统-公司产品-计息类型对象 列表
     */
    private List<CompanyProductInterest> companyProductInterestList;

    private List<String> productNameList;

    private String assetCompanyName;
}
