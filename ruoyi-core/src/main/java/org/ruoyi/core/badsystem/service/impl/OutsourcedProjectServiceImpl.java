package org.ruoyi.core.badsystem.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.badsystem.domain.OutsourcedProject;
import org.ruoyi.core.badsystem.domain.OutsourcedProjectStrategy;
import org.ruoyi.core.badsystem.domain.OutsourcedRepaymentDetail;
import org.ruoyi.core.badsystem.domain.vo.AssetManagementVo;
import org.ruoyi.core.badsystem.domain.vo.OutsourcedProjectVo;
import org.ruoyi.core.badsystem.mapper.OutsourcedProjectMapper;
import org.ruoyi.core.badsystem.service.IAssetManagementService;
import org.ruoyi.core.badsystem.service.IOutsourcedProjectService;
import org.ruoyi.core.badsystem.service.IOutsourcedProjectStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 不良系统-委外方案Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Service
public class OutsourcedProjectServiceImpl implements IOutsourcedProjectService
{
    @Autowired
    private OutsourcedProjectMapper outsourcedProjectMapper;
    @Autowired
    private IOutsourcedProjectStrategyService outsourcedProjectStrategyService;
    @Autowired
    private IAssetManagementService assetManagementService;
    /**
     * 查询不良系统-委外方案
     *
     * @param id 不良系统-委外方案主键
     * @return 不良系统-委外方案
     */
    @Override
    public OutsourcedProjectVo selectOutsourcedProjectById(Long id)
    {
        OutsourcedProjectVo vo = outsourcedProjectMapper.selectOutsourcedProjectById(id);
        if (vo.getOutsourcedProjectStrategyList() != null && !vo.getOutsourcedProjectStrategyList().isEmpty()) {
            // 统计 quantity 的总和
            long totalQuantity = vo.getOutsourcedProjectStrategyList().stream()
                    .mapToLong(strategy -> strategy.getQuantity() != null ? strategy.getQuantity() : 0L)
                    .sum();
            // 统计 amountMoney 的总和
            BigDecimal totalAmountMoney = vo.getOutsourcedProjectStrategyList().stream()
                    .map(OutsourcedProjectStrategy::getAmountMoney)
                    .filter(Objects::nonNull) // 防止 null 值影响计算
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalQuantity(totalQuantity);
            vo.setTotalAmountMoney(totalAmountMoney);
        } else {
            vo.setTotalQuantity(0L);
            vo.setTotalAmountMoney(BigDecimal.ZERO);
        }

        if (vo.getAssetManagementList() != null && !vo.getAssetManagementList().isEmpty()) {
            long realTotalQuantity = vo.getAssetManagementList().size();
            BigDecimal realTotalAmountMoney = vo.getAssetManagementList().stream().map(AssetManagementVo::getRemainingDue)
                    .filter(Objects::nonNull) // 防止 null 值影响计算
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setRealTotalQuantity(realTotalQuantity);
            vo.setRealTotalAmountMoney(realTotalAmountMoney);
        } else {
            vo.setRealTotalQuantity(0L);
            vo.setRealTotalAmountMoney(BigDecimal.ZERO);
        }
        if (vo.getOutsourcedRepaymentDetailList() != null && !vo.getOutsourcedRepaymentDetailList().isEmpty()) {
            // 统计 委外还款笔数 的总和
            long detailQuantity = vo.getOutsourcedRepaymentDetailList().size();
            // 统计 amountMoney 的总和
            BigDecimal detailRepaymentAmount = vo.getOutsourcedRepaymentDetailList().stream()
                    .map(OutsourcedRepaymentDetail::getRepaymentAmount)
                    .filter(Objects::nonNull) // 防止 null 值影响计算
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setDetailQuantity(detailQuantity);
            vo.setDetailRepaymentAmount(detailRepaymentAmount);
        } else {
            vo.setDetailQuantity(0L);
            vo.setDetailRepaymentAmount(BigDecimal.ZERO);
        }


        return vo;
    }

    /**
     * 查询不良系统-委外方案列表
     *
     * @param outsourcedProject 不良系统-委外方案
     * @return 不良系统-委外方案
     */
    @Override
    public List<OutsourcedProjectVo> selectOutsourcedProjectList(OutsourcedProject outsourcedProject)
    {
        List<OutsourcedProjectVo> outsourcedProjectVos = outsourcedProjectMapper.selectOutsourcedProjectList(outsourcedProject);
        for (OutsourcedProjectVo vo : outsourcedProjectVos) {
            if (vo.getOutsourcedProjectStrategyList() != null && !vo.getOutsourcedProjectStrategyList().isEmpty()) {
                // 统计 quantity 的总和
                long totalQuantity = vo.getOutsourcedProjectStrategyList().stream()
                                    .mapToLong(strategy -> strategy.getQuantity() != null ? strategy.getQuantity() : 0L)
                                    .sum();
                // 统计 amountMoney 的总和
                BigDecimal totalAmountMoney = vo.getOutsourcedProjectStrategyList().stream()
                        .map(OutsourcedProjectStrategy::getAmountMoney)
                        .filter(Objects::nonNull) // 防止 null 值影响计算
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setTotalQuantity(totalQuantity);
                vo.setTotalAmountMoney(totalAmountMoney);
            } else {
                vo.setTotalQuantity(0L);
                vo.setTotalAmountMoney(BigDecimal.ZERO);
            }

            if (vo.getAssetManagementList() != null && !vo.getAssetManagementList().isEmpty()) {
                long realTotalQuantity = vo.getAssetManagementList().size();
                BigDecimal realTotalAmountMoney = vo.getAssetManagementList().stream().map(AssetManagementVo::getRemainingDue)
                        .filter(Objects::nonNull) // 防止 null 值影响计算
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setRealTotalQuantity(realTotalQuantity);
                vo.setRealTotalAmountMoney(realTotalAmountMoney);
            } else {
                vo.setRealTotalQuantity(0L);
                vo.setRealTotalAmountMoney(BigDecimal.ZERO);
            }

            if (vo.getOutsourcedRepaymentDetailList() != null && !vo.getOutsourcedRepaymentDetailList().isEmpty()) {
                // 统计 委外还款笔数 的总和
                long detailQuantity = vo.getOutsourcedRepaymentDetailList().size();
                // 统计 amountMoney 的总和
                BigDecimal detailRepaymentAmount = vo.getOutsourcedRepaymentDetailList().stream()
                        .map(OutsourcedRepaymentDetail::getRepaymentAmount)
                        .filter(Objects::nonNull) // 防止 null 值影响计算
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setDetailQuantity(detailQuantity);
                vo.setDetailRepaymentAmount(detailRepaymentAmount);
            } else {
                vo.setDetailQuantity(0L);
                vo.setDetailRepaymentAmount(BigDecimal.ZERO);
            }
        }
        return outsourcedProjectVos;
    }

    /**
     * 新增不良系统-委外方案
     *
     * @param outsourcedProject 不良系统-委外方案
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOutsourcedProject(OutsourcedProjectVo outsourcedProject)
    {
        outsourcedProject.setCreateTime(DateUtils.getNowDate());
        outsourcedProject.setCreateBy(getUsername());
        int i = outsourcedProject.getCount();
        int j = 0;
        try {
            if (outsourcedProject.getOutsourcedProjectNumber() == null || outsourcedProject.getOutsourcedProjectNumber().isEmpty()) {
                i++;
                int count = getCountByCreateTime(DateUtils.getDate()) + i;
                String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
                outsourcedProject.setOutsourcedProjectNumber("PC-" + createTimeNum + String.format("%03d", count));
            } else {
                //重复提醒
                int projectNumber = outsourcedProjectMapper.getCountByOutsourcedProjectNumber(outsourcedProject.getOutsourcedProjectNumber());
                if (projectNumber > 0) {
                    throw new ServiceException("输入分案批次重复!");
                }
            }
            //系统编号逻辑

            j = outsourcedProjectMapper.insertOutsourcedProject(outsourcedProject);
            if (outsourcedProject.getOutsourcedProjectStrategyList() != null && !outsourcedProject.getOutsourcedProjectStrategyList().isEmpty()) {
                for (OutsourcedProjectStrategy strategy : outsourcedProject.getOutsourcedProjectStrategyList()) {
                    strategy.setOutsourcedProjectId(outsourcedProject.getId());
                }
                //批量新增分案策略
                outsourcedProjectStrategyService.batchOutsourcedProjectStrategy(outsourcedProject.getOutsourcedProjectStrategyList());
                //最终 符合条件的资产管理-借款人信息
                List<AssetManagementVo> assetManagementVoList = new ArrayList<>();
                //根据子集分案策略查询对应数据
                for (OutsourcedProjectStrategy strategy : outsourcedProject.getOutsourcedProjectStrategyList()) {
                    AssetManagementVo managementVo = new AssetManagementVo();
                    //委外方案-债权机构
                    managementVo.setCreditorInstitutionsId(outsourcedProject.getCreditorInstitutionsId());
                    //分案策略-逾期开始日期
                    managementVo.setFirstDayOverdueStart(strategy.getOverdueStart());
                    //分案策略-逾期截止日期
                    managementVo.setFirstDayOverdueEnd(strategy.getOverdueEnd());
                    //分案测率-客户逾期后是否发生还款
                    managementVo.setOverdueRepayment(strategy.getIsRepayment());
                    //分案测率-分配规则
                    String allocationRules = strategy.getAllocationRules();
                    if ("1".equals(allocationRules)){
                        // 使用 ThreadLocalRandom 生成随机数
                        int randInt = ThreadLocalRandom.current().nextInt(2); // 生成 0 或 1
                        // 根据随机数输出 "2" 或 "3"
                        allocationRules = randInt == 0 ? "2" : "3";
                    }
                    managementVo.setAllocationRules(allocationRules);
                    List<AssetManagementVo> assetManagementVos = assetManagementService.selectAssetManagementList(managementVo);
                    //根据案件范围 过滤得到符合的数据
                    List<AssetManagementVo> managementVoList = filterAssets(assetManagementVos, strategy.getCaseRange());
                    //本次策略真实金额
                    BigDecimal loanAmount = new BigDecimal(0);
                    //本次策略最大值
                    BigDecimal amountMoney = strategy.getAmountMoney().multiply(new BigDecimal("1.05")) ;
                    long quantity = 0L;
                    for (AssetManagementVo vo : managementVoList) {
                        //分案策略数量不为空时,计算条件带上数量条件
                        if(strategy.getQuantity() != null){
                            if (quantity < strategy.getQuantity() && loanAmount.compareTo(amountMoney) < 0){
                                quantity++;
                                loanAmount = loanAmount.add(vo.getLoanAmount());
                                assetManagementVoList.add(vo);
                            }
                        } else {
                            if (loanAmount.compareTo(amountMoney) < 0){
                                loanAmount = loanAmount.add(vo.getLoanAmount());
                                assetManagementVoList.add(vo);
                            }
                        }
                    }
                }
                List<Long> ids = assetManagementVoList.stream().map(AssetManagementVo::getId).distinct().collect(Collectors.toList());
                AssetManagementVo managementVo = new AssetManagementVo();
                managementVo.setOutsourcedProjectId(outsourcedProject.getId());
                managementVo.setIds(ids);
                managementVo.setUpdateBy(getUsername());
                managementVo.setUpdateTime(DateUtils.getNowDate());
                assetManagementService.correlationOutsourcedProject(managementVo);
            }

        } catch (DataAccessException e) {
            if (e.getCause() instanceof SQLException) {
                SQLException sqlException = (SQLException) e.getCause();
                // 根据数据库的错误代码判断是否是违反唯一约束的错误
                if (sqlException.getErrorCode() == 1062) {
                    outsourcedProject.setOutsourcedProjectNumber(null);
                    outsourcedProject.setCount(i);
                    insertOutsourcedProject(outsourcedProject);
                }
            }
        }
        return j;
    }

    /**
     * 修改不良系统-委外方案
     *
     * @param outsourcedProject 不良系统-委外方案
     * @return 结果
     */
    @Override
    public int updateOutsourcedProject(OutsourcedProject outsourcedProject)
    {
        outsourcedProject.setUpdateTime(DateUtils.getNowDate());
        if("4".equals(outsourcedProject.getCaseStatus()) || "8".equals(outsourcedProject.getCaseStatus())){
            AssetManagementVo managementVo = new AssetManagementVo();
            managementVo.setOutsourcedProjectId(outsourcedProject.getId());
            managementVo.setUpdateBy(getUsername());
            managementVo.setUpdateTime(DateUtils.getNowDate());
            assetManagementService.cancelOutsourcedProject(managementVo);
        }
        return outsourcedProjectMapper.updateOutsourcedProject(outsourcedProject);
    }

    /**
     * 批量删除不良系统-委外方案
     *
     * @param ids 需要删除的不良系统-委外方案主键
     * @return 结果
     */
    @Override
    public int deleteOutsourcedProjectByIds(Long[] ids)
    {
        return outsourcedProjectMapper.deleteOutsourcedProjectByIds(ids);
    }

    /**
     * 删除不良系统-委外方案信息
     *
     * @param id 不良系统-委外方案主键
     * @return 结果
     */
    @Override
    public int deleteOutsourcedProjectById(Long id)
    {
        return outsourcedProjectMapper.deleteOutsourcedProjectById(id);
    }

    public int getCountByCreateTime(String createTime){
        return outsourcedProjectMapper.getCountByCreateTime(createTime);
    }

    /**
     * 创建委外方案匹配对应资产管理信息
     * @param assetManagementList
     * @param conditions
     * @return
     */
    public List<AssetManagementVo> filterAssets(List<AssetManagementVo> assetManagementList, List<String> conditions) {
        // 定义条件和逾期天数范围的映射
        Map<String, int[]> overdueRangeMap = new HashMap<>();
        overdueRangeMap.put("m1", new int[]{1, 30});
        overdueRangeMap.put("m2", new int[]{31, 60});
        overdueRangeMap.put("m3", new int[]{61, 90});
        overdueRangeMap.put("m4", new int[]{91, 120});
        overdueRangeMap.put("m5", new int[]{121, 150});
        overdueRangeMap.put("m6", new int[]{151, 180});
        overdueRangeMap.put("m7", new int[]{181, 210});
        overdueRangeMap.put("m8", new int[]{211, 240});
        overdueRangeMap.put("m9", new int[]{241, 270});
        overdueRangeMap.put("m10", new int[]{271, 300});
        overdueRangeMap.put("m11", new int[]{301, 330});
        overdueRangeMap.put("m12", new int[]{331, 360});
        overdueRangeMap.put("m13-24", new int[]{360, 720});
        overdueRangeMap.put("m25-36", new int[]{720, 100000});
        // 使用 stream 过滤列表
        return assetManagementList.stream()
                .filter(asset -> conditions.stream().anyMatch(condition -> {
                    int[] range = overdueRangeMap.get(condition);
                    if (range != null) {
                        // 如果该条件存在，则检查该资产的 overdueDays 是否在范围内
                        return asset.getOverdueDays() >= range[0] && asset.getOverdueDays() <= range[1];
                    }
                    return false;
                }))
                .collect(Collectors.toList());  // 收集为一个新的列表
    }

    /**
     * 根据流程 id 获取详情
     * @param processId
     * @return
     */
    public OutsourcedProjectVo selectOutsourcedProjectByProcessId(String processId){
        return outsourcedProjectMapper.selectOutsourcedProjectByProcessId(processId);
    }

    /**
     * 修改不良系统-委外方案-改为对账中
     *
     * @param outsourcedProject 不良系统-委外方案
     * @return 结果
     */
    public int updateOutsourcedProjectByReconciliation(OutsourcedProjectVo outsourcedProject){
        return outsourcedProjectMapper.updateOutsourcedProjectByReconciliation(outsourcedProject);
    }
}
