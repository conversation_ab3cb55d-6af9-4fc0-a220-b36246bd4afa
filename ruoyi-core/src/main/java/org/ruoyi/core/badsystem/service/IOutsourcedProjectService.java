package org.ruoyi.core.badsystem.service;

import org.ruoyi.core.badsystem.domain.OutsourcedProject;
import org.ruoyi.core.badsystem.domain.vo.OutsourcedProjectVo;

import java.util.List;

/**
 * 不良系统-委外方案Service接口
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
public interface IOutsourcedProjectService
{
    /**
     * 查询不良系统-委外方案
     *
     * @param id 不良系统-委外方案主键
     * @return 不良系统-委外方案
     */
    public OutsourcedProjectVo selectOutsourcedProjectById(Long id);

    /**
     * 查询不良系统-委外方案列表
     *
     * @param outsourcedProject 不良系统-委外方案
     * @return 不良系统-委外方案集合
     */
    public List<OutsourcedProjectVo> selectOutsourcedProjectList(OutsourcedProject outsourcedProject);

    /**
     * 新增不良系统-委外方案
     *
     * @param outsourcedProject 不良系统-委外方案
     * @return 结果
     */
    public int insertOutsourcedProject(OutsourcedProjectVo outsourcedProject);

    /**
     * 修改不良系统-委外方案
     *
     * @param outsourcedProject 不良系统-委外方案
     * @return 结果
     */
    public int updateOutsourcedProject(OutsourcedProject outsourcedProject);

    /**
     * 批量删除不良系统-委外方案
     *
     * @param ids 需要删除的不良系统-委外方案主键集合
     * @return 结果
     */
    public int deleteOutsourcedProjectByIds(Long[] ids);

    /**
     * 删除不良系统-委外方案信息
     *
     * @param id 不良系统-委外方案主键
     * @return 结果
     */
    public int deleteOutsourcedProjectById(Long id);

    /**
     * 根据流程 id 获取详情
     * @param processId
     * @return
     */
    public OutsourcedProjectVo selectOutsourcedProjectByProcessId(String processId);

    /**
     * 修改不良系统-委外方案-改为对账中
     *
     * @param outsourcedProject 不良系统-委外方案
     * @return 结果
     */
    public int updateOutsourcedProjectByReconciliation(OutsourcedProjectVo outsourcedProject);
}
