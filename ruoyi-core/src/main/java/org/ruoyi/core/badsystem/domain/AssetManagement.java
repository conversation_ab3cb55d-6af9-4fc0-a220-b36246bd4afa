package org.ruoyi.core.badsystem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 不良系统-资产管理对象 bl_asset_management
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
public class AssetManagement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 借据号 */
    @Excel(name = "借据号")
    private String promissoryNoteNumber;

    /** 借款人姓名 */
    @Excel(name = "借款人姓名")
    private String borrowerName;

    /** 身份证 */
    @Excel(name = "身份证")
    private String idCard;

    /** 用户性别（0男 1女 2未知） */
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /** 户籍所在地 */
    @Excel(name = "户籍所在地")
    private String hometown;

    /** 联系人电话 */
    @Excel(name = "联系人电话")
    private String phoneNumber;

    /** 借款人现住址 */
    @Excel(name = "借款人现住址")
    private String currentAddress;

    /** 产品 */
    //@Excel(name = "产品")
    private Long companyProductId;

    /** 借款金额 */
    @Excel(name = "借款金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal loanAmount;

    /** 利率(年化) */
    @Excel(name = "利率(年化)" , cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal interestRateYear;

    /** 放款日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "放款日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loanDisbursementDate;

    /** 到期日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dueDate;

    /** 金额(剩余本金) */
    @Excel(name = "金额(剩余本金)" , cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal remainingPrincipal;

    /** 客户累计实还本金 */
    @Excel(name = "客户累计实还本金" ,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal cumulativeRepaymentAmount;

    /** 借款利息 */
    @Excel(name = "借款利息" , cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal loanInterest;

    /** 借款罚息 */
    @Excel(name = "借款罚息" ,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal loanPenaltyInterest;

    /** 剩余应还本息 */
    @Excel(name = "剩余应还本息" ,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal remainingDue;

    /** 逾期利息 */
    @Excel(name = "逾期利息" ,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal overdueInterest;

    /** 代偿利息 */
    @Excel(name = "代偿利息" ,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal compensatoryInterest;

    /** 担保利息 */
    @Excel(name = "担保利息" ,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal guaranteeInterest;

    /** 期数 */
    @Excel(name = "期数" ,cellType = Excel.ColumnType.NUMERIC)
    private Long periods;

    /** 逾期天数 */
    @Excel(name = "逾期天数" ,cellType = Excel.ColumnType.NUMERIC)
    private Long overdueDays;

    /** 累计代偿本金 */
    @Excel(name = "累计代偿本金" ,cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal accumulatedCompensation;

    /** 用户逾期还款 */
    @Excel(name = "用户逾期还款" ,readConverterExp = "1=是,2=否")
    private String overdueRepayment;

    /** 代偿还款 */
    @Excel(name = "代偿还款" , cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal compensatoryRepayment;

    /** 借款合同 */
    @Excel(name = "借款合同")
    private String loanContract;

    /** 是否逾期 */
    @Excel(name = "是否逾期" ,readConverterExp = "1=是,2=否")
    private String isOverdue;

    /** 客户还款方式 */
    @Excel(name = "客户还款方式")
    private String customerRepaymentWay;

    /** 用户逾期首日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "用户逾期首日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstDayOverdue;

    /** 借款用途 */
    @Excel(name = "借款用途")
    private String usageLoan;

    /** 借款平台 */
    @Excel(name = "借款平台")
    private String loanPlatform;

    /** 债权机构 */
    //@Excel(name = "债权机构")
    private Long creditorInstitutionsId;

    /** 开始计息日(导入后) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始计息日(导入后)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date importInterestCalculation;

    /** 关联委外方案 */
    @Excel(name = "关联委外方案")
    private Long outsourcedProjectId;

    /** 匹配案件状态(1.未匹配 2.已匹配) */
    @Excel(name = "匹配案件状态(1.未匹配 2.已匹配)")
    private String caseStatus;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("promissoryNoteNumber", getPromissoryNoteNumber())
            .append("borrowerName", getBorrowerName())
            .append("idCard", getIdCard())
            .append("sex", getSex())
            .append("hometown", getHometown())
            .append("phoneNumber", getPhoneNumber())
            .append("currentAddress", getCurrentAddress())
            .append("companyProductId", getCompanyProductId())
            .append("loanAmount", getLoanAmount())
            .append("interestRateYear", getInterestRateYear())
            .append("loanDisbursementDate", getLoanDisbursementDate())
            .append("dueDate", getDueDate())
            .append("remainingPrincipal", getRemainingPrincipal())
            .append("cumulativeRepaymentAmount", getCumulativeRepaymentAmount())
            .append("loanInterest", getLoanInterest())
            .append("loanPenaltyInterest", getLoanPenaltyInterest())
            .append("remainingDue", getRemainingDue())
            .append("periods", getPeriods())
            .append("overdueDays", getOverdueDays())
            .append("accumulatedCompensation", getAccumulatedCompensation())
            .append("overdueRepayment", getOverdueRepayment())
            .append("compensatoryRepayment", getCompensatoryRepayment())
            .append("loanContract", getLoanContract())
            .append("isOverdue", getIsOverdue())
            .append("customerRepaymentWay", getCustomerRepaymentWay())
            .append("firstDayOverdue", getFirstDayOverdue())
            .append("usageLoan", getUsageLoan())
            .append("loanPlatformId", getLoanPlatform())
            .append("creditorInstitutionsId", getCreditorInstitutionsId())
            .append("importInterestCalculation", getImportInterestCalculation())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
