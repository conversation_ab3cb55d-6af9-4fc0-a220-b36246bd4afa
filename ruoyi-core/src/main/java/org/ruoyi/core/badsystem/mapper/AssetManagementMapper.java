package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.AssetManagement;
import org.ruoyi.core.badsystem.domain.vo.AssetManagementVo;

import java.util.List;

/**
 * 不良系统-资产管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
public interface AssetManagementMapper
{
    /**
     * 查询不良系统-资产管理
     *
     * @param id 不良系统-资产管理主键
     * @return 不良系统-资产管理
     */
    public AssetManagementVo selectAssetManagementById(Long id);

    /**
     * 查询不良系统-资产管理列表
     *
     * @param assetManagement 不良系统-资产管理
     * @return 不良系统-资产管理集合
     */
    public List<AssetManagementVo> selectAssetManagementList(AssetManagementVo assetManagement);

    /**
     * 新增不良系统-资产管理
     *
     * @param assetManagement 不良系统-资产管理
     * @return 结果
     */
    public int insertAssetManagement(AssetManagement assetManagement);

    /**
     * 修改不良系统-资产管理
     *
     * @param assetManagement 不良系统-资产管理
     * @return 结果
     */
    public int updateAssetManagement(AssetManagement assetManagement);

    /**
     * 删除不良系统-资产管理
     *
     * @param id 不良系统-资产管理主键
     * @return 结果
     */
    public int deleteAssetManagementById(Long id);

    /**
     * 批量删除不良系统-资产管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAssetManagementByIds(Long[] ids);

    /**
     * 批量新增不良系统-资产管理
     *
     * @param assetManagement 不良系统-资产管理
     * @return 结果
     */
    public int batchAssetManagement(List<AssetManagementVo> assetManagement);


    /**
     * 不良系统-资产管理-关联委外方案
     *
     * @param assetManagement 不良系统-资产管理
     * @return 结果
     */
    public int correlationOutsourcedProject(AssetManagementVo assetManagement);

    /**
     * 不良系统-资产管理-取消关联委外方案
     *
     * @param assetManagement 不良系统-资产管理
     * @return 结果
     */
    public int cancelOutsourcedProject(AssetManagementVo assetManagement);
}
