package org.ruoyi.core.badsystem.service.impl;

import org.ruoyi.core.badsystem.domain.MechanismContacts;
import org.ruoyi.core.badsystem.mapper.MechanismContactsMapper;
import org.ruoyi.core.badsystem.service.IMechanismContactsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机构-联系人Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class MechanismContactsServiceImpl implements IMechanismContactsService
{
    @Autowired
    private MechanismContactsMapper mechanismContactsMapper;

    /**
     * 查询机构-联系人
     *
     * @param id 机构-联系人主键
     * @return 机构-联系人
     */
    @Override
    public MechanismContacts selectMechanismContactsById(Long id)
    {
        return mechanismContactsMapper.selectMechanismContactsById(id);
    }

    /**
     * 查询机构-联系人列表
     *
     * @param mechanismContacts 机构-联系人
     * @return 机构-联系人
     */
    @Override
    public List<MechanismContacts> selectMechanismContactsList(MechanismContacts mechanismContacts)
    {
        return mechanismContactsMapper.selectMechanismContactsList(mechanismContacts);
    }

    /**
     * 新增机构-联系人
     *
     * @param mechanismContacts 机构-联系人
     * @return 结果
     */
    @Override
    public int insertMechanismContacts(MechanismContacts mechanismContacts)
    {
        return mechanismContactsMapper.insertMechanismContacts(mechanismContacts);
    }

    /**
     * 修改机构-联系人
     *
     * @param mechanismContacts 机构-联系人
     * @return 结果
     */
    @Override
    public int updateMechanismContacts(MechanismContacts mechanismContacts)
    {
        return mechanismContactsMapper.updateMechanismContacts(mechanismContacts);
    }

    /**
     * 批量删除机构-联系人
     *
     * @param ids 需要删除的机构-联系人主键
     * @return 结果
     */
    @Override
    public int deleteMechanismContactsByIds(Long[] ids)
    {
        return mechanismContactsMapper.deleteMechanismContactsByIds(ids);
    }

    /**
     * 删除机构-联系人信息
     *
     * @param id 机构-联系人主键
     * @return 结果
     */
    @Override
    public int deleteMechanismContactsById(Long id)
    {
        return mechanismContactsMapper.deleteMechanismContactsById(id);
    }

    @Override
    public int batchMechanismContacts(List<MechanismContacts> mechanismContacts)
    {
        return mechanismContactsMapper.batchMechanismContacts(mechanismContacts);
    }

    @Override
    public int deleteByMechanismId(Long mechanismId){
        return mechanismContactsMapper.deleteByMechanismId(mechanismId);
    }
}
