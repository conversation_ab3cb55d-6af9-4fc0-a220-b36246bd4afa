package org.ruoyi.core.badsystem.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 机构-结算公式明细对象 bl_mechanism_settlement_formula
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Data
public class MechanismSettlementFormula extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 机构 id */
    //@Excel(name = "机构 id")
    private Long mechanismId;

    /** 结算周期 */
    @Excel(name = "结算周期" ,
            readConverterExp  = "m1=m1, m2=m2, m3=m3, m4=m4, m5=m5, m6=m6, m7=m7, m8=m8, m9=m9 ,m10=m10,m11=m11, m12=m12, m13-24=m13-24, m25-36=m25-36")
    private String settlementCycleString;

    private List<String> settlementCycle;

    /** 结算比例(%) */
    @Excel(name = "结算比例(%)")
    private Long settlementRatio;

    /** 启用状态 */
    @Excel(name = "开关(选项:开 或 关)" , readConverterExp  = "1=开,2=关")
    private String enableStatus;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("mechanismId", getMechanismId())
            .append("settlementCycle", getSettlementCycle())
            .append("settlementRatio", getSettlementRatio())
            .append("enableStatus", getEnableStatus())
            .toString();
    }
}
