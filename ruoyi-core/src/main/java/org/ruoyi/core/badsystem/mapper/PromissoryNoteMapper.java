package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.PromissoryNote;
import org.ruoyi.core.badsystem.domain.vo.PromissoryNoteVo;

import java.util.List;

/**
 * 不良系统-借据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
public interface PromissoryNoteMapper
{
    /**
     * 查询不良系统-借据
     *
     * @param id 不良系统-借据主键
     * @return 不良系统-借据
     */
    public PromissoryNote selectPromissoryNoteById(Long id);

    /**
     * 查询不良系统-借据列表
     *
     * @param promissoryNote 不良系统-借据
     * @return 不良系统-借据集合
     */
    public List<PromissoryNoteVo> selectPromissoryNoteList(PromissoryNote promissoryNote);

    /**
     * 新增不良系统-借据
     *
     * @param promissoryNote 不良系统-借据
     * @return 结果
     */
    public int insertPromissoryNote(PromissoryNote promissoryNote);

    /**
     * 修改不良系统-借据
     *
     * @param promissoryNote 不良系统-借据
     * @return 结果
     */
    public int updatePromissoryNote(PromissoryNote promissoryNote);

    /**
     * 删除不良系统-借据
     *
     * @param id 不良系统-借据主键
     * @return 结果
     */
    public int deletePromissoryNoteById(Long id);

    /**
     * 批量删除不良系统-借据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePromissoryNoteByIds(Long[] ids);

    /**
     * 获取当天创建次数
     */
    public int getCountByCreateTime(String createTime);
}
