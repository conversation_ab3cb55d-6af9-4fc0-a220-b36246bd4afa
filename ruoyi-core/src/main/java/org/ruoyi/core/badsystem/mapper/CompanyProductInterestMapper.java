package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.CompanyProductInterest;

import java.util.List;

/**
 * 不良系统-公司产品-计息类型Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface CompanyProductInterestMapper
{
    /**
     * 查询不良系统-公司产品-计息类型
     *
     * @param id 不良系统-公司产品-计息类型主键
     * @return 不良系统-公司产品-计息类型
     */
    public CompanyProductInterest selectCompanyProductInterestById(Long id);

    /**
     * 查询不良系统-公司产品-计息类型列表
     *
     * @param companyProductInterest 不良系统-公司产品-计息类型
     * @return 不良系统-公司产品-计息类型集合
     */
    public List<CompanyProductInterest> selectCompanyProductInterestList(CompanyProductInterest companyProductInterest);

    /**
     * 新增不良系统-公司产品-计息类型
     *
     * @param companyProductInterest 不良系统-公司产品-计息类型
     * @return 结果
     */
    public int insertCompanyProductInterest(CompanyProductInterest companyProductInterest);

    /**
     * 修改不良系统-公司产品-计息类型
     *
     * @param companyProductInterest 不良系统-公司产品-计息类型
     * @return 结果
     */
    public int updateCompanyProductInterest(CompanyProductInterest companyProductInterest);

    /**
     * 删除不良系统-公司产品-计息类型
     *
     * @param id 不良系统-公司产品-计息类型主键
     * @return 结果
     */
    public int deleteCompanyProductInterestById(Long id);

    /**
     * 批量删除不良系统-公司产品-计息类型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompanyProductInterestByIds(Long[] ids);

    /**
     * 批量新增不良系统-公司产品-计息类型
     *
     * @param companyProductInterest 不良系统-公司产品-计息类型
     * @return 结果
     */
    public int batchCompanyProductInterest(List<CompanyProductInterest> companyProductInterest);

    /**
     * 删除不良系统-公司产品-计息类型
     *
     * @param companyProductId 关联不良系统公司产品Id
     * @return 结果
     */
    public int deleteCompanyProductInterestByCompanyProductId(Long companyProductId);
}
