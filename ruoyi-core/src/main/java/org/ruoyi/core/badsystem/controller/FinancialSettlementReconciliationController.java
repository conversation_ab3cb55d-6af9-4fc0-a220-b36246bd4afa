package org.ruoyi.core.badsystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.badsystem.domain.FinancialSettlementReconciliation;
import org.ruoyi.core.badsystem.service.IFinancialSettlementReconciliationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 不良系统-财务结算单-业务对账单Controller
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@RestController
@RequestMapping("/financial/settlement/reconciliation")
public class FinancialSettlementReconciliationController extends BaseController
{
    @Autowired
    private IFinancialSettlementReconciliationService financialSettlementReconciliationService;

    /**
     * 查询不良系统-财务结算单-业务对账单列表
     */
    //@PreAuthorize("@ss.hasPermi('system:reconciliation:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinancialSettlementReconciliation financialSettlementReconciliation)
    {
        startPage();
        List<FinancialSettlementReconciliation> list = financialSettlementReconciliationService.selectFinancialSettlementReconciliationList(financialSettlementReconciliation);
        return getDataTable(list);
    }

    /**
     * 导出不良系统-财务结算单-业务对账单列表
     */
    //@PreAuthorize("@ss.hasPermi('system:reconciliation:export')")
    @Log(title = "不良系统-财务结算单-业务对账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancialSettlementReconciliation financialSettlementReconciliation)
    {
        List<FinancialSettlementReconciliation> list = financialSettlementReconciliationService.selectFinancialSettlementReconciliationList(financialSettlementReconciliation);
        ExcelUtil<FinancialSettlementReconciliation> util = new ExcelUtil<FinancialSettlementReconciliation>(FinancialSettlementReconciliation.class);
        util.exportExcel(response, list, "不良系统-财务结算单-业务对账单数据");
    }

    /**
     * 获取不良系统-财务结算单-业务对账单详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:reconciliation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(financialSettlementReconciliationService.selectFinancialSettlementReconciliationById(id));
    }

    /**
     * 新增不良系统-财务结算单-业务对账单
     */
    //@PreAuthorize("@ss.hasPermi('system:reconciliation:add')")
    @Log(title = "不良系统-财务结算单-业务对账单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinancialSettlementReconciliation financialSettlementReconciliation)
    {
        return toAjax(financialSettlementReconciliationService.insertFinancialSettlementReconciliation(financialSettlementReconciliation));
    }

    /**
     * 修改不良系统-财务结算单-业务对账单
     */
    //@PreAuthorize("@ss.hasPermi('system:reconciliation:edit')")
    @Log(title = "不良系统-财务结算单-业务对账单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinancialSettlementReconciliation financialSettlementReconciliation)
    {
        return toAjax(financialSettlementReconciliationService.updateFinancialSettlementReconciliation(financialSettlementReconciliation));
    }

    /**
     * 删除不良系统-财务结算单-业务对账单
     */
    //@PreAuthorize("@ss.hasPermi('system:reconciliation:remove')")
    @Log(title = "不良系统-财务结算单-业务对账单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(financialSettlementReconciliationService.deleteFinancialSettlementReconciliationByIds(ids));
    }
}
