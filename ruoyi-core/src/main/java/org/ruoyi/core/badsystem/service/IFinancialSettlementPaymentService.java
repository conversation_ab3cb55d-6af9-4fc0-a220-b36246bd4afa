package org.ruoyi.core.badsystem.service;

import org.ruoyi.core.badsystem.domain.FinancialSettlementPayment;

import java.util.List;

/**
 * 不良系统-财务结算单-付款明细Service接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface IFinancialSettlementPaymentService
{
    /**
     * 查询不良系统-财务结算单-付款明细
     *
     * @param id 不良系统-财务结算单-付款明细主键
     * @return 不良系统-财务结算单-付款明细
     */
    public FinancialSettlementPayment selectFinancialSettlementPaymentById(Long id);

    /**
     * 查询不良系统-财务结算单-付款明细列表
     *
     * @param financialSettlementPayment 不良系统-财务结算单-付款明细
     * @return 不良系统-财务结算单-付款明细集合
     */
    public List<FinancialSettlementPayment> selectFinancialSettlementPaymentList(FinancialSettlementPayment financialSettlementPayment);

    /**
     * 新增不良系统-财务结算单-付款明细
     *
     * @param financialSettlementPayment 不良系统-财务结算单-付款明细
     * @return 结果
     */
    public int insertFinancialSettlementPayment(FinancialSettlementPayment financialSettlementPayment);

    /**
     * 修改不良系统-财务结算单-付款明细
     *
     * @param financialSettlementPayment 不良系统-财务结算单-付款明细
     * @return 结果
     */
    public int updateFinancialSettlementPayment(FinancialSettlementPayment financialSettlementPayment);

    /**
     * 批量删除不良系统-财务结算单-付款明细
     *
     * @param ids 需要删除的不良系统-财务结算单-付款明细主键集合
     * @return 结果
     */
    public int deleteFinancialSettlementPaymentByIds(Long[] ids);

    /**
     * 删除不良系统-财务结算单-付款明细信息
     *
     * @param id 不良系统-财务结算单-付款明细主键
     * @return 结果
     */
    public int deleteFinancialSettlementPaymentById(Long id);

    /**
     * 批量新增不良系统-财务结算单-付款明细
     *
     * @param financialSettlementPayment 不良系统-财务结算单-付款明细
     * @return 结果
     */
    public int batchFinancialSettlementPayment(List<FinancialSettlementPayment> financialSettlementPayment);

    /**
     * 根据关联财务结算单 id   删除不良系统-财务结算单-付款明细
     *
     * @param financialSettlementId 不良系统-财务结算单-付款明细主键
     * @return 结果
     */
    public int deleteFinancialSettlementPaymentByFinancialSettlementId(Long financialSettlementId);
}
