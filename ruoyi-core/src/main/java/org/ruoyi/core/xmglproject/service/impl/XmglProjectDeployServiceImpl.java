package org.ruoyi.core.xmglproject.service.impl;

import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.AuthMain;
import org.ruoyi.core.oasystem.domain.OaEditApproveGeneralityEditRecords;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.domain.bo.OaProjectDeployBo;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;
import org.ruoyi.core.xmglproject.domain.XmglDeployProject;
import org.ruoyi.core.xmglproject.domain.XmglProject;
import org.ruoyi.core.xmglproject.mapper.XmglProjectDeployMapper;
import org.ruoyi.core.xmglproject.mapper.XmglProjectMapper;
import org.ruoyi.core.xmglproject.service.IXmglProjectDeployService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class XmglProjectDeployServiceImpl implements IXmglProjectDeployService {

    @Autowired
    private XmglProjectDeployMapper xmglProjectDeployMapper;

    @Autowired
    private XmglProjectMapper projectMapper;
    /**
     * 查询立项项目-项目名称关联
     *
     * @param deployId 立项项目-项目名称关联主键
     * @return 立项项目-项目名称关联
     */
    @Override
    public XmglDeployProject selectXmglProjectDeployByDeployId(Long deployId)
    {
        XmglDeployProject xmglDeployProject = new XmglDeployProject();
        xmglDeployProject = xmglProjectDeployMapper.selectXmglProjectDeployByDeployId(deployId);
        return xmglDeployProject;
    }

    /**
     * 查询立项项目-项目名称关联
     * @param projectId 项目立项模块的项目id
     * @return
     */
    @Override
    public XmglDeployProject selectXmglProjectDeployByProjectId(Long projectId) {
        return xmglProjectDeployMapper.selectXmglProjectDeployByProjectId(projectId);
    }

    /**
     * 查询当前用户在‘授权’中是担任了什么角色(项目负责人/业务管理员/查看权限)
     * 未使用
     * @param userId
     */
    @Override
    public List<AuthMain> selectXmglProjectDeployInfoByDeployId(Long userId, Long deployId) {
        List<AuthMain> collect = new ArrayList<>();
        //从权限主表获取该用户的所有权限
        List<AuthMain> authMains = xmglProjectDeployMapper.selectAuthMainInfoByDeployId(userId);
        if (!CollectionUtils.isEmpty(authMains)){
            // collect = authMains.stream().map(AuthMain::getId).collect(Collectors.toList());
            for (AuthMain authMain : authMains) {
                //根据mainId查询附表是否存在数据
                AuthDetail authDetail = xmglProjectDeployMapper.selectAuthDetailByMainId(authMain.getId());
                if (Objects.isNull(authDetail)){
                    //为空则证明是从其他模块授权的
                    collect.add(authMain);
                //不为空，判断授权的是否是本项目
                }else {
                    if (authDetail.getThirdTableId() == deployId && authDetail.getStatus().equals("0")){
                        collect.add(authMain);
                    }
                }
            }
        }
        return collect;
    }

    /**
     * 查询当前用户在'项目名称'模块是否是该项目的业务责任人
     * @param userId 用户id
     * @param deployId 项目名称模块的项目id
     * @return
     */
    @Override
    public OaEditApproveGeneralityEditRecords selectProjectYWPerson(Long userId, Long deployId) {
        return xmglProjectDeployMapper.selectProjectYWPerson(userId,deployId);
    }

    @Override
    public int updateProjectDeployStatus(XmglDeployProject xmglDeployProject) {
        return xmglProjectDeployMapper.updateProjectDeployStatus(xmglDeployProject);
    }


    @Override
    public List<Long> selectProjectInfoByDeployIdList(Set<Long> deployIdSet) {
        return xmglProjectDeployMapper.selectProjectInfoByDeployIdList(deployIdSet);
    }

    /**
     * 根据立项项目id查询正在关联的项目名称模块的项目id
     * @param projectId
     * @return
     */
    @Override
    public XmglDeployProject selectBeingProjectDeployInfoByProjectId(Long projectId) {
        return xmglProjectDeployMapper.selectBeingProjectDeployInfoByProjectId(projectId);
    }

    /**
     * 根据业务信息配置的项目名称id查询立项项目信息列表
     * @param authProjectIds
     * @return
     */
    @Override
    public List<XmglProject> selectProjectInfoListByDeployIds(List<Long> authProjectIds) {
        return xmglProjectDeployMapper.selectProjectInfoListByDeployIds(authProjectIds);
    }

    /**
     * 从项目名称模块对项目进行修改时，更改项目名称对应的立项项目状态
     * @param oaProjectDeployBo
     * @param operType 操作类型 FQXGSH-发起修改申请
     */
    public void updateProjectStatus(OaProjectDeployBo oaProjectDeployBo, String operType) {
        Long deployId = oaProjectDeployBo.getId();
        //根据项目名称id查询对应的立项项目信息
        XmglDeployProject xmglDeployProject = this.selectXmglProjectDeployByDeployId(deployId);
        if (!Objects.isNull(xmglDeployProject)) {
            XmglProject xmglProject = projectMapper.selectXmglProjectById(xmglDeployProject.getProjectId());
            String scheduleStatus = xmglProject.getScheduleStatus();
            if (scheduleStatus.equals("1") || scheduleStatus.equals("2") || scheduleStatus.equals("3") || scheduleStatus.equals("4")) {
                XmglProject project = new XmglProject();
                project.setId(xmglProject.getId());
                /** 判断操作内容 */
                //发起修改申请，只改变立项项目状态
                if (operType.equals("FQXGSH")){
                    project.setProjectStatus("11");
                    // projectMapper.updateXmglProject(project);
                //修改申请审批通过或者被驳回，恢复立项项目状态为发起流程前的状态
                }else if(operType.equals("XGSHTG") || operType.equals("XGSQBH")){
                    project.setProjectStatus(xmglProject.getScheduleStatus());
                }
                projectMapper.updateXmglProject(project);
            }
        }
    }
}
