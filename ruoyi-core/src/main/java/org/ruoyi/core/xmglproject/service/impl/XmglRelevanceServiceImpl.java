package org.ruoyi.core.xmglproject.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.ProjectCompanyRelevance;
import org.ruoyi.core.oasystem.domain.ProjectTypeRelevance;
import org.ruoyi.core.xmglproject.constant.XmglProjectEnum;
import org.ruoyi.core.xmglproject.domain.XmglDeployProject;
import org.ruoyi.core.xmglproject.domain.XmglProject;
import org.ruoyi.core.xmglproject.domain.XmglProjectCompanyRelevance;
import org.ruoyi.core.xmglproject.domain.XmglProjectTypeRelevance;
import org.ruoyi.core.xmglproject.mapper.XmglProjectMapper;
import org.ruoyi.core.xmglproject.mapper.XmglRelevanceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 项目名称-公司关联
 * 项目名称-项目类型-业务类型关联
 */
@Service
public class XmglRelevanceServiceImpl {

    @Autowired
    private XmglRelevanceMapper xmglRelevanceMapper;

    @Autowired
    private XmglProjectDeployServiceImpl xmglProjectDeployServiceImpl;

    @Autowired
    private XmglProjectMapper xmglProjectMapper;
    /**
     * 当项目名称模块新增时，调此方法批量插入‘立项项目-公司信息关联表’
     * @param xmglProjectCompanyRelevanceList
     * @return
     */
    public int insertProjectCompanyRelevanceList(List<XmglProjectCompanyRelevance> xmglProjectCompanyRelevanceList){
        return xmglRelevanceMapper.insertProjectCompanyRelevanceList(xmglProjectCompanyRelevanceList);
    }

    /**
     * 当项目名称模块新增时，调此方法新增‘立项项目-项目类型-业务类型关联表’
     * @param xmglProjectTypeRelevanceList
     * @return
     */
    public int insertProjectTypeRelevanceList(List<XmglProjectTypeRelevance> xmglProjectTypeRelevanceList){
        return xmglRelevanceMapper.insertProjectTypeRelevanceList(xmglProjectTypeRelevanceList);
    }

    /**
     * 当项目名称模块更新时，调此方法更新立项项目-项目类型-业务类型关联表
     * @param projectTypeList
     * @return
     */
    public int updateProjectTypeRelevance(List<ProjectTypeRelevance> projectTypeList){
        List<XmglProjectTypeRelevance> xmglProjectTypeRelevanceList = new ArrayList<>();
        int result = 0;
        if (!CollectionUtils.isEmpty(projectTypeList)){
            //获取项目名称模块的项目id
            Long deployId = projectTypeList.get(0).getProjectId();
            //根据项目名称id查询项目立项信息
            XmglDeployProject xmglDeployProject = xmglProjectDeployServiceImpl.selectXmglProjectDeployByDeployId(deployId);
            if (Objects.isNull(xmglDeployProject)){
                return result;
            }
            //根据关联表的立项项目id查询立项项目信息
            XmglProject project = xmglProjectMapper.selectXmglProjectById(xmglDeployProject.getProjectId());
            //有关联关系且关联状态是1正常，且立项项目状态不是'已终止'或'已上线'时，更新
            if (xmglDeployProject.getStatus().equals("1") && !project.getProjectStatus().equals(XmglProjectEnum.YZZ.getCode())
                                                          && !project.getProjectStatus().equals(XmglProjectEnum.YSX.getCode())){
                //插入新的关联关系
                for (ProjectTypeRelevance projectTypeRelevance : projectTypeList) {
                    XmglProjectTypeRelevance relevance = new XmglProjectTypeRelevance();
                    relevance.setProjectId(project.getId());
                    relevance.setDeployId(projectTypeRelevance.getProjectId());
                    relevance.setDataType(projectTypeRelevance.getDataType());
                    relevance.setTypeId(projectTypeRelevance.getTypeId());
                    relevance.setCreateBy(SecurityUtils.getLoginUser().getUser().getNickName());
                    relevance.setCreateTime(DateUtils.getNowDate());
                    relevance.setStatus("0");
                    xmglProjectTypeRelevanceList.add(relevance);
                }
                result = this.insertProjectTypeRelevanceList(xmglProjectTypeRelevanceList);
            }
        }
        return result;
    }

    /**
     * 当项目名称模块更新时，调此方法更新立项项目-担保公司/资产方/资金方关联表
     * @param projectCompanyRelevanceList
     * @return
     */
    public int updateProjectCompanyRelevance(List<ProjectCompanyRelevance> projectCompanyRelevanceList){
        List<XmglProjectCompanyRelevance> xmglProjectTypeRelevanceList = new ArrayList<>();
        int result = 0;
        if (!CollectionUtils.isEmpty(projectCompanyRelevanceList)){
            //获取项目名称模块的项目id
            Long deployId = projectCompanyRelevanceList.get(0).getProjectId();
            //根据项目名称id查询项目立项信息
            XmglDeployProject xmglDeployProject = xmglProjectDeployServiceImpl.selectXmglProjectDeployByDeployId(deployId);
            if (Objects.isNull(xmglDeployProject)){
                return result;
            }
            //根据关联表的立项项目id查询立项项目信息
            XmglProject project = xmglProjectMapper.selectXmglProjectById(xmglDeployProject.getProjectId());
            //有关联关系且关联状态是1正常，且立项项目状态不是'已终止'或'已上线'时，更新
            if (xmglDeployProject.getStatus().equals("1") && !project.getProjectStatus().equals(XmglProjectEnum.YZZ.getCode())
                    && !project.getProjectStatus().equals(XmglProjectEnum.YSX.getCode())){
                //插入新的关联关系
                for (ProjectCompanyRelevance projectCompanyRelevance : projectCompanyRelevanceList) {
                    XmglProjectCompanyRelevance relevance = new XmglProjectCompanyRelevance();
                    relevance.setProjectId(project.getId());
                    relevance.setDeployId(projectCompanyRelevance.getProjectId());
                    relevance.setUnitId(projectCompanyRelevance.getUnitId());
                    relevance.setUnitType(projectCompanyRelevance.getUnitType());
                    relevance.setUnitTypeId(projectCompanyRelevance.getUnitTypeId());
                    relevance.setProportion(projectCompanyRelevance.getProportion().toString());
                    relevance.setIsNecessity(projectCompanyRelevance.getIsNecessity());
                    relevance.setRemark(project.getRemark());
                    relevance.setStatus("0");
                    relevance.setCreateBy(SecurityUtils.getLoginUser().getUser().getNickName());
                    relevance.setCreateTime(DateUtils.getNowDate());
                    xmglProjectTypeRelevanceList.add(relevance);
                }
                result = this.insertProjectCompanyRelevanceList(xmglProjectTypeRelevanceList);
            }
        }
        return result;
    }

    /**
     * 查询担保公司/资产方/资金方类型
     * @param unitType
     * @param id
     * @return
     */
    public List<XmglProjectCompanyRelevance> queryDataObjectByTypeAndId(String unitType, Long id) {
        return xmglRelevanceMapper.queryDataObjectByTypeAndId(unitType,id);
    }

    /**
     * 查询担保公司/资产方/资金方类型
     * @param id
     * @return
     */
    public List<XmglProjectCompanyRelevance> queryDataObjectByProjectId(Long id) {
        return xmglRelevanceMapper.queryDataObjectByProjectId(id);
    }

    /**
     * 查询业务/项目类型
     * @param id
     * @return
     */
    public List<XmglProjectTypeRelevance> queryProjectObject(String dataType, Long id) {
        return xmglRelevanceMapper.queryProjectObject(dataType,id);
    }

    List<XmglProjectTypeRelevance> getTypeByProjectId(Long projectId){
        return xmglRelevanceMapper.getTypeByProjectId(projectId);
    }

    List<XmglProjectTypeRelevance> getTypeByProjectIds(List<Long> projectIds){
        return xmglRelevanceMapper.getTypeByProjectIds(projectIds);
    }
}
