package org.ruoyi.core.xmglproject.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.xmglproject.domain.XmglContactWay;
import org.ruoyi.core.xmglproject.domain.XmglProjectUser;

import java.util.List;
import java.util.Set;


/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface XmglContactWayMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public XmglContactWay selectXmglContactWayById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param xmglContactWay 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<XmglContactWay> selectXmglContactWayList(XmglContactWay xmglContactWay);

    /**
     * 新增【请填写功能名称】
     * 
     * @param xmglContactWay 【请填写功能名称】
     * @return 结果
     */
    public int insertXmglContactWay(XmglContactWay xmglContactWay);

    /**
     * 修改【请填写功能名称】
     * 
     * @param xmglContactWay 【请填写功能名称】
     * @return 结果
     */
    public int updateXmglContactWay(XmglContactWay xmglContactWay);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteXmglContactWayById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXmglContactWayByIds(Long[] ids);

    /**
     * 选择通过pro id和用户id查询数据
     *
     * @param id     id
     * @param userId 用户id
     * @return {@link XmglContactWay}
     */
    XmglContactWay selectByProIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    List<XmglContactWay> selectuserList(@Param("id") Long id, @Param("userId") Long userId);

    XmglContactWay queryByUserIdAndPid(@Param("userId") Long userId, @Param("projectId") Long projectId);

    /**
     * 修改和认领项目时，需要先将之前录入的渠道方改成旧渠道方
     * @param id
     * @return
     */
    int updateXmglContactWayByProjectId(Long id);

    /**
     * 根据联系人id集合查询联系人信息
     * @param ids
     * @return
     */
    List<XmglContactWay> selectProjectChannelListByIds(Set<Long> ids);

    /**
     * 根据立项项目id查询联系人集合
     * @param type
     * @param projectId
     * @return
     */
    List<XmglContactWay> selectContactWayListByProjectId(@Param("type")String type, @Param("projectId") Long projectId);

    /**
     * 根据用户id和立项项目id查询联系人信息
     * @param userId
     * @param projectId
     */
    List<XmglContactWay> selectContactWayByLoginUserId(@Param("userId") Long userId, @Param("projectId") Long projectId);

    /**
     * 根据立项项目id将修改联系人状态
     * @param projectId
     * @return
     */
    int updateContactWayStatusByProjectId(Long projectId);

    /**
     * 根据立项项目id查询最新的联系人信息
     * @param projectId
     * @return
     */
    XmglContactWay selectNewXmglContactWayByProjectId(Long projectId);
}
