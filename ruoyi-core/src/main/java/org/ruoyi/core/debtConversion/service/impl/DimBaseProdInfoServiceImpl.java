package org.ruoyi.core.debtConversion.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.debtConversion.domain.DimBaseProdInfo;
import org.ruoyi.core.debtConversion.mapper.DimBaseProdInfoMapper;
import org.ruoyi.core.debtConversion.service.IDimBaseProdInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 产品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Service
public class DimBaseProdInfoServiceImpl implements IDimBaseProdInfoService
{
    @Autowired
    private DimBaseProdInfoMapper dimBaseProdInfoMapper;

    /**
     * 查询产品信息
     *
     * @param id 产品信息主键
     * @return 产品信息
     */
    @Override
    public DimBaseProdInfo selectDimBaseProdInfoById(Long id)
    {
        return dimBaseProdInfoMapper.selectDimBaseProdInfoById(id);
    }

    /**
     * 查询产品信息列表
     *
     * @param dimBaseProdInfo 产品信息
     * @return 产品信息
     */
    @Override
    public List<DimBaseProdInfo> selectDimBaseProdInfoList(DimBaseProdInfo dimBaseProdInfo)
    {
        return dimBaseProdInfoMapper.selectDimBaseProdInfoList(dimBaseProdInfo);
    }

    /**
     * 新增产品信息
     *
     * @param dimBaseProdInfo 产品信息
     * @return 结果
     */
    @Override
    public int insertDimBaseProdInfo(DimBaseProdInfo dimBaseProdInfo)
    {
        dimBaseProdInfo.setCreateTime(DateUtils.getNowDate());
        return dimBaseProdInfoMapper.insertDimBaseProdInfo(dimBaseProdInfo);
    }

    /**
     * 修改产品信息
     *
     * @param dimBaseProdInfo 产品信息
     * @return 结果
     */
    @Override
    public int updateDimBaseProdInfo(DimBaseProdInfo dimBaseProdInfo)
    {
        dimBaseProdInfo.setUpdateTime(DateUtils.getNowDate());
        return dimBaseProdInfoMapper.updateDimBaseProdInfo(dimBaseProdInfo);
    }

    /**
     * 批量删除产品信息
     *
     * @param ids 需要删除的产品信息主键
     * @return 结果
     */
    @Override
    public int deleteDimBaseProdInfoByIds(Long[] ids)
    {
        return dimBaseProdInfoMapper.deleteDimBaseProdInfoByIds(ids);
    }

    /**
     * 删除产品信息信息
     *
     * @param id 产品信息主键
     * @return 结果
     */
    @Override
    public int deleteDimBaseProdInfoById(Long id)
    {
        return dimBaseProdInfoMapper.deleteDimBaseProdInfoById(id);
    }
}
