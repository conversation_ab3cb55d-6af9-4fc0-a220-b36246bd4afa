package org.ruoyi.core.debtConversion.service.impl;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.service.ISysCompanyService;
import org.ruoyi.core.debtConversion.domain.DebtConversion;
import org.ruoyi.core.debtConversion.domain.DebtUser;
import org.ruoyi.core.debtConversion.domain.vo.DebtConversionImport;
import org.ruoyi.core.debtConversion.domain.vo.DebtConversionVo;
import org.ruoyi.core.debtConversion.mapper.DebtConversionMapper;
import org.ruoyi.core.debtConversion.service.IDebtConversionService;
import org.ruoyi.core.debtConversion.service.IDebtUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 债转通知明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class DebtConversionServiceImpl implements IDebtConversionService {
    @Autowired
    private DebtConversionMapper debtConversionMapper;
    @Autowired
    private ISysCompanyService sysCompanyService;
    @Autowired
    private IDebtUserService debtUserService;

    /**
     * 查询债转通知明细
     *
     * @param id 债转通知明细主键
     * @return 债转通知明细
     */
    @Override
    public DebtConversionVo selectDebtConversionById(Long id) {
        DebtConversionVo debtConversion = debtConversionMapper.selectDebtConversionById(id);
        List<Long> conpanyIds = new ArrayList<>();
        conpanyIds.add(debtConversion.getCustId());
        conpanyIds.add(debtConversion.getPartnerId());
        conpanyIds.add(debtConversion.getFundId());
        conpanyIds.add(debtConversion.getDebtRecipientId());

        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(conpanyIds);
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectSysCompanyList(sysCompanyVo);
        // 将 List<SysCompanyVo> 转换为 Map<Long, String>
        Map<Long, String> sysCompanyMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyShortName));
        Map<Long, String> sysCompanyFullMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyName));

        debtConversion.setCustName(sysCompanyMap.get(debtConversion.getCustId()));
        debtConversion.setFundName(sysCompanyMap.get(debtConversion.getFundId()));
        debtConversion.setPartnerName(sysCompanyMap.get(debtConversion.getPartnerId()));
        debtConversion.setDebtRecipientName(sysCompanyMap.get(debtConversion.getDebtRecipientId()));

        debtConversion.setCustFullName(sysCompanyFullMap.get(debtConversion.getCustId()));
        debtConversion.setFundFullName(sysCompanyFullMap.get(debtConversion.getFundId()));
        debtConversion.setPartnerFullName(sysCompanyFullMap.get(debtConversion.getPartnerId()));
        debtConversion.setDebtRecipientFullName(sysCompanyFullMap.get(debtConversion.getDebtRecipientId()));
        return debtConversion;
    }

    @Override
    public DebtConversionVo getAppUserInfo(Long id) {
        DebtConversionVo debtConversion = debtConversionMapper.selectDebtConversionById(id);
        List<Long> conpanyIds = new ArrayList<>();
        conpanyIds.add(debtConversion.getCustId());
        conpanyIds.add(debtConversion.getPartnerId());
        conpanyIds.add(debtConversion.getFundId());
        conpanyIds.add(debtConversion.getDebtRecipientId());

        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(conpanyIds);
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectSysCompanyList(sysCompanyVo);
        // 将 List<SysCompanyVo> 转换为 Map<Long, String>
        Map<Long, String> sysCompanyMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyShortName));
        Map<Long, String> sysCompanyFullMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyName));

        debtConversion.setCustName(sysCompanyMap.get(debtConversion.getCustId()));
        debtConversion.setFundName(sysCompanyMap.get(debtConversion.getFundId()));
        debtConversion.setPartnerName(sysCompanyMap.get(debtConversion.getPartnerId()));
        debtConversion.setDebtRecipientName(sysCompanyMap.get(debtConversion.getDebtRecipientId()));

        debtConversion.setCustFullName(sysCompanyFullMap.get(debtConversion.getCustId()));
        debtConversion.setFundFullName(sysCompanyFullMap.get(debtConversion.getFundId()));
        debtConversion.setPartnerFullName(sysCompanyFullMap.get(debtConversion.getPartnerId()));
        debtConversion.setDebtRecipientFullName(sysCompanyFullMap.get(debtConversion.getDebtRecipientId()));

        debtConversion.setReadTime(DateUtils.getNowDate());
        debtConversion.setIsRead("2");
        updateDebtConversion(debtConversion);
        return debtConversion;
    }

    /**
     * 查询债转通知明细列表
     *
     * @param debtConversion 债转通知明细
     * @return 债转通知明细
     */
    @Override
    public List<DebtConversionVo> selectDebtConversionList(DebtConversionVo debtConversion) {
        List<DebtConversionVo> debtConversionList = debtConversionMapper.selectDebtConversionList(debtConversion);
        List<Long> conpanyIds = debtConversionList.stream()
                .flatMap(dc -> Stream.of(
                        dc.getCustId(),
                        dc.getPartnerId(),
                        dc.getFundId(),
                        dc.getDebtRecipientId()
                ))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(conpanyIds);
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectSysCompanyList(sysCompanyVo);
        // 将 List<SysCompanyVo> 转换为 Map<Long, String>
        Map<Long, String> sysCompanyMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyShortName));
        Map<Long, String> sysCompanyFullMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyName));
        debtConversionList.forEach(vo -> {
            vo.setCustName(sysCompanyMap.get(vo.getCustId()));
            vo.setFundName(sysCompanyMap.get(vo.getFundId()));
            vo.setPartnerName(sysCompanyMap.get(vo.getPartnerId()));
            vo.setDebtRecipientName(sysCompanyMap.get(vo.getDebtRecipientId()));

            vo.setCustFullName(sysCompanyFullMap.get(vo.getCustId()));
            vo.setFundFullName(sysCompanyFullMap.get(vo.getFundId()));
            vo.setPartnerFullName(sysCompanyFullMap.get(vo.getPartnerId()));
            vo.setDebtRecipientFullName(sysCompanyFullMap.get(vo.getDebtRecipientId()));
        });
        Map<String, List<DebtConversionVo>> debtMap = debtConversionList.stream()
                .collect(Collectors.groupingBy(
                        vo ->
                                Optional.ofNullable(vo.getRealIdCard()).orElse("") +
                                Optional.ofNullable(vo.getRealPhoneNum()).orElse("") +
                                Optional.ofNullable(vo.getCustId()).orElse(0L)
                ));
        List<String> idCardList = debtConversionList.stream().map(DebtConversionVo::getRealIdCard).distinct().collect(Collectors.toList());
        DebtUser debtUser = new DebtUser();
        debtUser.setIdCardList(idCardList);
        List<DebtUser> debtUsers = debtUserService.selectDebtUserList(debtUser);

        Map<String, DebtUser> debtUserMap = debtUsers.stream()
                .collect(Collectors.toMap(
                        vo -> Optional.ofNullable(vo.getIdCard()).orElse("") +
                                Optional.ofNullable(vo.getPhoneNum()).orElse("") +
                                Optional.ofNullable(vo.getCustId()).orElse(0L),
                        vo -> vo,
                        (oldValue, newValue) -> oldValue
                ));

        debtMap.forEach((key, debts) -> {
            // 检查当前 key 是否存在于 debtUserMap 中
            if (debtUserMap.containsKey(key)) {
                // 遍历 debtConversionList，为每个元素的 registerMiniProgram 赋值
                debts.forEach(vo -> vo.setRegisterMiniProgram("2"));
            } else {
                debts.forEach(vo -> vo.setRegisterMiniProgram("1"));
            }
        });

        return debtConversionList;
    }

    /**
     * 新增债转通知明细
     *
     * @param debtConversion 债转通知明细
     * @return 结果
     */
    @Override
    public int insertDebtConversion(DebtConversion debtConversion) {
        debtConversion.setCreateTime(DateUtils.getNowDate());
        return debtConversionMapper.insertDebtConversion(debtConversion);
    }

    /**
     * 修改债转通知明细
     *
     * @param debtConversion 债转通知明细
     * @return 结果
     */
    @Override
    public int updateDebtConversion(DebtConversion debtConversion) {
        debtConversion.setUpdateTime(DateUtils.getNowDate());
        return debtConversionMapper.updateDebtConversion(debtConversion);
    }

    /**
     * 批量删除债转通知明细
     *
     * @param ids 需要删除的债转通知明细主键
     * @return 结果
     */
    @Override
    public int deleteDebtConversionByIds(Long[] ids) {
        return debtConversionMapper.deleteDebtConversionByIds(ids);
    }

    /**
     * 删除债转通知明细信息
     *
     * @param id 债转通知明细主键
     * @return 结果
     */
    @Override
    public int deleteDebtConversionById(Long id) {
        return debtConversionMapper.deleteDebtConversionById(id);
    }

    @Override
    public int deleteDebtConversionByFildId(Long id) {
        return debtConversionMapper.deleteDebtConversionByFildId(id);
    }

    @Override
    public List<DebtConversionImport> selectDebtConversionImportList(DebtConversionImport debtConversion) {
        List<DebtConversionImport> debtConversionImports = debtConversionMapper.selectDebtConversionImportList(debtConversion);
        List<Long> conpanyIds = debtConversionImports.stream()
                .flatMap(dc -> Stream.of(
                        dc.getCustId(),
                        dc.getPartnerId(),
                        dc.getFundId(),
                        dc.getDebtRecipientId()
                ))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(conpanyIds);
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectSysCompanyList(sysCompanyVo);
        // 将 List<SysCompanyVo> 转换为 Map<Long, String>
        Map<Long, String> sysCompanyMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyShortName));
        debtConversionImports.forEach(vo -> {
            vo.setCustName(sysCompanyMap.get(vo.getCustId()));
            vo.setFundName(sysCompanyMap.get(vo.getFundId()));
            vo.setPartnerName(sysCompanyMap.get(vo.getPartnerId()));
            vo.setDebtRecipientName(sysCompanyMap.get(vo.getDebtRecipientId()));
        });
        return debtConversionImports;
    }

    /**
     * 批量插入债权转换记录
     *
     * @param list 债权转换记录列表
     * @return 插入的记录数
     */
    @Override
    public int insertDebtConversionBatch(List<DebtConversion> list) {
        return debtConversionMapper.insertDebtConversionBatch(list);
    }

    @Override
    public int batchUpdateDebtConversionCode(List<DebtConversionVo> debtConversion) {
        return debtConversionMapper.batchUpdateDebtConversionCode(debtConversion);
    }

    /**
     * 修改开票申请编码
     *
     * @param debtConversion 债转通知明细
     * @return 结果
     */
    @Override
    public int updateInvoicingApplicationCode(DebtConversionVo debtConversion){
        return debtConversionMapper.updateInvoicingApplicationCode(debtConversion);
    }
}
