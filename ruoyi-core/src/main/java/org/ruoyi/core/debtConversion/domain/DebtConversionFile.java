package org.ruoyi.core.debtConversion.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 债转文件对象 dc_debt_conversion_file
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
public class DebtConversionFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 担保公司 */
    @Excel(name = "担保公司")
    private Long custId;

    /** 债转主题 */
    @Excel(name = "债转主题")
    private String debtConversionTheme;

    /** 债转通知发起时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "债转通知发起时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date noticeLaunchTime;

    /** 债转通知完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "债转通知完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date noticeCompleteTime;

    /** 推送状态 1.未推送 2.推送中 3.推送完成 */
    @Excel(name = "推送状态 1.未推送 2.推送中 3.推送完成")
    private String pushStatus;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("custId", getCustId())
            .append("debtConversionTheme", getDebtConversionTheme())
            .append("noticeLaunchTime", getNoticeLaunchTime())
            .append("noticeCompleteTime", getNoticeCompleteTime())
            .append("pushStatus", getPushStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
