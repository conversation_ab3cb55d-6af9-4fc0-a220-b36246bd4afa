package org.ruoyi.core.debtConversion.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.debtConversion.domain.DebtUser;
import org.ruoyi.core.debtConversion.service.IDebtUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 债转用户Controller
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@RestController
@RequestMapping("/debt/user")
public class DebtUserController extends BaseController
{
    @Autowired
    private IDebtUserService debtUserService;

    /**
     * 查询债转用户列表
     */
    //@PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(DebtUser deptUser)
    {
        startPage();
        List<DebtUser> list = debtUserService.selectDebtUserList(deptUser);
        return getDataTable(list);
    }

    /**
     * 导出债转用户列表
     */
    //@PreAuthorize("@ss.hasPermi('system:user:export')")
    @Log(title = "债转用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DebtUser deptUser)
    {
        List<DebtUser> list = debtUserService.selectDebtUserList(deptUser);
        ExcelUtil<DebtUser> util = new ExcelUtil<DebtUser>(DebtUser.class);
        util.exportExcel(response, list, "债转用户数据");
    }

    /**
     * 获取债转用户详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(debtUserService.selectDebtUserById(id));
    }

    /**
     * 新增债转用户
     */
    //@PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "债转用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DebtUser deptUser)
    {
        return toAjax(debtUserService.insertDebtUser(deptUser));
    }

    /**
     * 修改债转用户
     */
    //@PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "债转用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DebtUser deptUser)
    {
        return toAjax(debtUserService.updateDebtUser(deptUser));
    }

    /**
     * 删除债转用户
     */
    //@PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "债转用户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(debtUserService.deleteDebtUserByIds(ids));
    }

    @GetMapping(value = "/getInfo")
    public AjaxResult getInfo(DebtUser deptUser)
    {
        return AjaxResult.success(debtUserService.getInfo(deptUser));
    }
}
