package org.ruoyi.core.debtConversion.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 借据信息对象 dim_bill_no_info
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
public class DimBillNoInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 借据编号 */
    private String billAppNo;

    /** 放款月份 */
    @Excel(name = "放款月份")
    private String loanMonth;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String productNo;

    /** 放款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "放款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loanDate;

    /** 到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dueDate;

    /** 放款金额 */
    @Excel(name = "放款金额")
    private BigDecimal loanAmt;

    /** 借款期限 */
    @Excel(name = "借款期限")
    private Long loanPeriod;

    /** 分期期数 */
    @Excel(name = "分期期数")
    private Long loanTerm;

    /** 担保公司 */
    @Excel(name = "担保公司")
    private String guarantorNo;

    /** 证件类型 */
    @Excel(name = "证件类型")
    private String idCardType;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String idCard;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobile;

    /** 客户姓名 */
    @Excel(name = "客户姓名")
    private String name;

    /** 农户标识 */
    @Excel(name = "农户标识")
    private String farmerFlag;

    /** 数据来源 */
    @Excel(name = "数据来源")
    private String dataSource;

    /** 用户所在省份 */
    @Excel(name = "用户所在省份")
    private String provinceId;

    /** 用户所在市 */
    @Excel(name = "用户所在市")
    private String cityId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dwCreateTime;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dwUpdateTime;

    /** 担保公司名称 */
    private String guarantorName;
}
