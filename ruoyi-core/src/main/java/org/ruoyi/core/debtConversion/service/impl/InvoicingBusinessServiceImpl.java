package org.ruoyi.core.debtConversion.service.impl;

import java.io.BufferedInputStream;
import java.io.OutputStream;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.io.File;
import java.io.IOException;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.OSSObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.properties.OSSProperties;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.ZipUtil;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.service.ISysCompanyService;
import org.apache.commons.io.FilenameUtils;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.debtConversion.domain.InvoicingApplicationFile;
import org.ruoyi.core.debtConversion.domain.InvoicingBusiness;
import org.ruoyi.core.debtConversion.domain.InvoicingMiddle;
import org.ruoyi.core.debtConversion.domain.until.ReadPDFUntil;
import org.ruoyi.core.debtConversion.domain.vo.DebtConversionVo;
import org.ruoyi.core.debtConversion.domain.vo.InvoicingBusinessVo;
import org.ruoyi.core.debtConversion.domain.vo.InvoicingApplicationVo;
import org.ruoyi.core.debtConversion.mapper.InvoicingBusinessMapper;
import org.ruoyi.core.debtConversion.service.*;
import org.ruoyi.core.esign3.service.IMailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 开票申请业务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Service
public class InvoicingBusinessServiceImpl implements IInvoicingBusinessService
{
    @Autowired
    private InvoicingBusinessMapper invoicingBusinessMapper;
    @Autowired
    private IInvoicingApplicationService invoicingApplicationService;
    @Autowired
    private IInvoicingApplicationFileService iInvoicingApplicationFileService;
    @Autowired
    private ISysCompanyService sysCompanyService;
    @Autowired
    private IDebtConversionService debtConversionService;
    @Autowired
    private IInvoicingMiddleService invoicingMiddleService;
    @Autowired
    private IMailService mailService;
    @Autowired
    private RedisCache redisCache;


    private static String endpoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String uploadPath;

    private static OSS ossClient;

    // 获取项目根目录
    private static final String ROOT_PATH = System.getProperty("user.dir");
    private static String PDF_ZIP = ROOT_PATH + File.separator + "PDFZip";

    @Autowired
    public void init(OSSProperties ossProperties) {
        endpoint = ossProperties.getEndpoint();
        accessKeyId = ossProperties.getAccessKeyId();
        accessKeySecret = ossProperties.getAccessKeySecret();
        bucketName = ossProperties.getBucketName();
        uploadPath = ossProperties.getUploadPath();
    }
    /**
     * 查询开票申请业务
     *
     * @param id 开票申请业务主键
     * @return 开票申请业务
     */
    @Override
    public InvoicingBusinessVo selectInvoicingBusinessById(Long id)
    {
        InvoicingBusinessVo invoicingBusinessVo = invoicingBusinessMapper.selectInvoicingBusinessById(id);
        SysCompanyVo sysCompanyVo = sysCompanyService.selectSysCompanyById(invoicingBusinessVo.getMainBodyId());
        if (sysCompanyVo != null) {
            invoicingBusinessVo.setMainBodyName(sysCompanyVo.getCompanyShortName());
        }
        if ("1".equals(invoicingBusinessVo.getChannel())){
            InvoicingApplicationVo invoicingApplicationVo = new InvoicingApplicationVo();
            invoicingApplicationVo.setInvoicingBusinessId(id);
            List<InvoicingApplicationVo> invoicingApplicationVos = invoicingApplicationService.selectInvoicingApplicationList(invoicingApplicationVo);
            invoicingBusinessVo.setInvoicingApplicationVoList(invoicingApplicationVos);
        } else if ("2".equals(invoicingBusinessVo.getChannel())){
            DebtConversionVo debtConversionVo = new DebtConversionVo();
            debtConversionVo.setInvoicingBusinessId(id);
            List<DebtConversionVo> debtConversionVos = debtConversionService.selectDebtConversionList(debtConversionVo);
            invoicingBusinessVo.setDebtConversionVoList(debtConversionVos);
        }
        return invoicingBusinessVo;
    }

    /**
     * 查询开票申请业务列表
     *
     * @param invoicingBusiness 开票申请业务
     * @return 开票申请业务
     */
    @Override
    public List<InvoicingBusinessVo> selectInvoicingBusinessList(InvoicingBusinessVo invoicingBusiness)
    {
        List<InvoicingBusinessVo> invoicingBusinessVos = invoicingBusinessMapper.selectInvoicingBusinessList(invoicingBusiness);
        List<Long> custIds = invoicingBusinessVos.stream().map(InvoicingBusinessVo::getMainBodyId).distinct().collect(Collectors.toList());
        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(custIds);
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectSysCompanyList(sysCompanyVo);
        Map<Long, String> companyMap = sysCompanyVos.stream().collect(Collectors.toMap(
                SysCompanyVo::getId,              // Value 选择器
                SysCompanyVo::getCompanyShortName, // Key 选择器
                (existing, replacement) -> existing // 合并函数（处理重复key）
        ));

        invoicingBusinessVos.forEach(vo -> {
            vo.setMainBodyName(companyMap.get(vo.getMainBodyId()));
        });
        return invoicingBusinessVos;
    }

    /**
     * 新增开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    @Override
    public int insertInvoicingBusiness(InvoicingBusiness invoicingBusiness)
    {
        invoicingBusiness.setCreateTime(DateUtils.getNowDate());
        return invoicingBusinessMapper.insertInvoicingBusiness(invoicingBusiness);
    }

    /**
     * 新增开票申请业务
     *
     * @param invoicingBusinessList 开票申请业务
     * @return 结果
     */
    @Override
    public int insertInvoicingBusinessBatch(List<InvoicingBusiness> invoicingBusinessList) {
        return invoicingBusinessMapper.insertInvoicingBusinessBatch(invoicingBusinessList);
    }

    /**
     * 修改开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    @Override
    public int updateInvoicingBusiness(InvoicingBusiness invoicingBusiness)
    {
        invoicingBusiness.setUpdateTime(DateUtils.getNowDate());
        return invoicingBusinessMapper.updateInvoicingBusiness(invoicingBusiness);
    }

    /**
     * 修改开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    @Override
    public int singleinvoice(InvoicingBusiness invoicingBusiness) {
//        invoicingBusiness = selectInvoicingBusinessById(invoicingBusiness.getId());
        ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        String filePath = FilenameUtils.getPath(invoicingBusiness.getFileUrl());
        filePath = "/" + filePath;
        String fileName = FilenameUtils.getName(invoicingBusiness.getFileUrl());
        //修复下载后的文件是空文件的问题
        String replace = filePath.replace(Constants.OSS_VIEW_PREFIX, "");
        //从oss获取文件
        OSSObject ossObject = ossClient.getObject(bucketName, replace + fileName);
        //检查文件夹是否存在
        String pdfZipDir = ROOT_PATH + File.separator + "PDFZip";
        File folder = new File(pdfZipDir);
        if (!folder.exists()) {
            // 如果文件夹不存在，则创建新文件夹
            if (!folder.mkdirs()) {
                throw new RuntimeException("系统出错请联系管理员！");
            }
        }
        //获取文件名 替换oss随机文件名
        fileName = invoicingBusiness.getFileName();
        //下载文件
        String localFilePath = pdfZipDir + File.separator + fileName;
        File localFile = new File(localFilePath);

        // 下载文件到临时文件夹
        try (BufferedInputStream reader = new BufferedInputStream(ossObject.getObjectContent());
             OutputStream os = Files.newOutputStream(localFile.toPath())) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = reader.read(buffer)) != -1) {
                os.write(buffer, 0, length);
            }
            SysCompanyVo sysCompanyVo = sysCompanyService.selectSysCompanyById(invoicingBusiness.getMainBodyId());
            String mailResult = mailService.sendUserMessage(sysCompanyVo.getCompanyName(),invoicingBusiness.getReceivingEmail(),"您的发票请查收", localFile, localFile.getName());
            if (!"success".equals(mailResult)) {
                // 邮件发送失败，记录日志
                invoicingBusiness.setReasonFail(mailResult);
            }
            FileUtils.deleteFile(localFilePath);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        invoicingBusiness.setUpdateTime(DateUtils.getNowDate());
        return invoicingBusinessMapper.updateInvoicingBusiness(invoicingBusiness);
    }

    /**
     * 修改开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    @Override
    public int uploadInvoice(MultipartFile file, InvoicingBusinessVo invoicingBusiness) {
        if (file == null || file.isEmpty()) {
            return 0;
        }

        try {
            // 获取项目根目录
            String rootPath = System.getProperty("user.dir");

            // 创建PDFZip目录（如果不存在）
            String pdfZipDir = rootPath + File.separator + "PDFZip";
            File pdfZipFolder = new File(pdfZipDir);
            if (!pdfZipFolder.exists()) {
                pdfZipFolder.mkdirs();
            }

            // 创建随机名称的临时目录用于存放解压文件
            String randomDirName = IdUtils.fastUUID();
            String tempDir = pdfZipDir + File.separator + randomDirName;
            File tempFolder = new File(tempDir);
            if (!tempFolder.exists()) {
                tempFolder.mkdirs();
            }

            // 保存上传的ZIP文件到PDFZip目录
            String zipFileName = IdUtils.fastUUID() + ".zip";
            String zipFilePath = pdfZipDir + File.separator + zipFileName;

            // 将上传的文件保存到指定位置
            File zipFile = new File(zipFilePath);
            file.transferTo(zipFile);

            // 解压ZIP文件到临时目录
            ZipUtil.unZipFile(zipFilePath, tempDir, null);

            // 解压后删除上传的ZIP文件
            FileUtils.deleteFile(zipFilePath);

            // 处理解压后的PDF文件，提取内容并关联
            Map<String, File> fileContentMap = new HashMap<>();
            Map<String, String> fileContentPathMap = new HashMap<>();
            File tempDirFile = new File(tempDir);
            if (tempDirFile.exists() && tempDirFile.isDirectory()) {
                File[] files = tempDirFile.listFiles();
                if (files != null) {
                    for (File fi : files) {
                        if (fi.isFile() && fi.getName().toLowerCase().endsWith(".pdf")) {
                            // 使用ReadPDFUntil提取PDF文件内容
                            String content = ReadPDFUntil.getExtractedContent(fi.getAbsolutePath());
                            if (!content.isEmpty()) {
                                fileContentMap.put(content, fi);
                            }
                        }
                    }
                }
            }

            // 处理业务逻辑
            if (invoicingBusiness != null && invoicingBusiness.getIds() != null) {
                List<InvoicingBusinessVo> invoicingBusinessList = selectInvoicingBusinessList(invoicingBusiness);
                for (InvoicingBusinessVo businessVo : invoicingBusinessList) {
                    String businessKey = businessVo.getBorrower() + "|" + businessVo.getInvoicingAmountTotal();
                    if (fileContentMap.containsKey(businessKey)) {
                        File businessFile = fileContentMap.get(businessKey);
                        MultipartFile customMultipartFile = new MockMultipartFile(
                                "file",
                                businessFile.getName(),
                                "text/plain",
                                Files.readAllBytes(businessFile.toPath())
                        );
                        String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.INVOICING_BUSINESS, customMultipartFile);
                        businessVo.setFileUrl(url);
                        businessVo.setFileName(businessKey + ".pdf");
                        SysCompanyVo sysCompanyVo = sysCompanyService.selectSysCompanyById(invoicingBusiness.getMainBodyId());
                        String mailResult = mailService.sendUserMessage(sysCompanyVo.getCompanyName(),businessVo.getReceivingEmail(),"您的发票请查收", businessFile, businessKey + ".pdf");
                        if (!"success".equals(mailResult)) {
                            // 邮件发送失败，记录日志
                            businessVo.setReasonFail(mailResult);
                            businessVo.setInvoicingStatus("3");
                        } else {
                            businessVo.setPushTime(DateUtils.getNowDate());
                            businessVo.setInvoicingStatus("2");
                        }
                        updateInvoicingBusiness(businessVo);
                    }
                }
            }

            // 删除刚刚解压生成的临时文件夹
            deleteDirectory(new File(tempDir));
            return 1;
        } catch (IOException e) {
            e.printStackTrace();
            return 0;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 批量删除开票申请业务
     *
     * @param ids 需要删除的开票申请业务主键
     * @return 结果
     */
    @Override
    public int deleteInvoicingBusinessByIds(Long[] ids)
    {
        return invoicingBusinessMapper.deleteInvoicingBusinessByIds(ids);
    }

    /**
     * 删除开票申请业务信息
     *
     * @param id 开票申请业务主键
     * @return 结果
     */
    @Override
    public int deleteInvoicingBusinessById(Long id)
    {
        return invoicingBusinessMapper.deleteInvoicingBusinessById(id);
    }

    /**
     * 提交开票申请
     * @param invoicingBusinessVo
     * @return
     */
    @Override
    public int invoicingBusinessBatch(InvoicingBusinessVo invoicingBusinessVo){
        List<InvoicingApplicationVo> invoicingApplicationVoList = invoicingBusinessVo.getInvoicingApplicationVoList();
        if(invoicingApplicationVoList == null || invoicingApplicationVoList.isEmpty()){
            return 0;
        }
        invoicingApplicationVoList.forEach(vo -> vo.setInvoicingApplicationTime(invoicingBusinessVo.getInvoicingApplicationTime()));
        //批量修改开票申请对应的 开票时间
        invoicingApplicationService.batchUpdateInvoicingApplicationCode(invoicingApplicationVoList);
        //修改主表开票状态
        InvoicingApplicationFile invoicingApplicationFile = new InvoicingApplicationFile();
        invoicingApplicationFile.setId(invoicingBusinessVo.getFileId());
        invoicingApplicationFile.setInvoiceStatus("2");
        iInvoicingApplicationFileService.updateInvoicingApplicationFile(invoicingApplicationFile);
        // 根据开票编号分组
        Map<String, List<InvoicingApplicationVo>> groupByCode = invoicingApplicationVoList.stream()
                .filter(vo -> vo.getInvoicingApplicationCode() != null && !vo.getInvoicingApplicationCode().isEmpty())
                .collect(Collectors.groupingBy(InvoicingApplicationVo::getInvoicingApplicationCode));

        List<InvoicingMiddle> invoicingMiddleList= new ArrayList<>();

        // 处理每个分组
        for(Map.Entry<String, List<InvoicingApplicationVo>> entry : groupByCode.entrySet()){
            String code = entry.getKey();
            List<InvoicingApplicationVo> voList = entry.getValue();

            if(voList.isEmpty()){
                continue;
            }

            // 获取第一个元素作为基础数据
            InvoicingApplicationVo firstVo = voList.get(0);

            // 创建开票业务对象
            InvoicingBusiness business = new InvoicingBusiness();
            // 设置基本信息
            business.setInvoicingApplicationCode(code);
            business.setInvoicingApplicationTime(DateUtils.getNowDate());
            business.setChannel("1"); //1.比对申请
            business.setBorrower(firstVo.getBorrower());
            business.setPhoneNum(firstVo.getPhoneNum());
            business.setIdCard(firstVo.getIdCard());
            business.setReceivingEmail(firstVo.getReceivingEmail());
            business.setInvoicingAmountTotal(firstVo.getInvoicingAmount());
            business.setMainBodyId(firstVo.getCustId()); //开票主体

            invoicingBusinessMapper.insertInvoicingBusiness(business);

            voList.forEach(vo -> {
                InvoicingMiddle invoicingMiddle = new InvoicingMiddle();
                invoicingMiddle.setCorrelationId(vo.getId());
                invoicingMiddle.setInvoicingBusinessId(business.getId());
                invoicingMiddle.setCorrelationType("1");
                invoicingMiddleList.add(invoicingMiddle);
            });
        }

        // 批量插入数据
        return invoicingMiddleService.batchInsertInvoicingMiddle(invoicingMiddleList);
    }

    /**
     * 开票统计
     * @param invoicingBusinessVo
     * @return
     */
    @Override
    public Map<String, Object> invoiceStatistics(InvoicingBusinessVo invoicingBusinessVo){
        Map<String, Object> returnMap = new HashMap<>();
        List<InvoicingBusinessVo> invoicingApplicationVos = selectInvoicingBusinessList(invoicingBusinessVo);

        // 获取当前年份
        int currentYear = Year.now().getValue();
        // 获取当前季度
        int currentQuarter = (LocalDate.now().getMonthValue() - 1) / 3 + 1;

        // 初始化统计数据
        BigDecimal yearInvoicedAmount = BigDecimal.ZERO;
        BigDecimal yearNotInvoicedAmount = BigDecimal.ZERO;
        BigDecimal quarterInvoicedAmount = BigDecimal.ZERO;
        BigDecimal quarterNotInvoicedAmount = BigDecimal.ZERO;

        // 按月份分组的已开票和未开票数据
        Map<String, BigDecimal> monthlyInvoicedMap = new LinkedHashMap<>();
        Map<String, BigDecimal> monthlyNotInvoicedMap = new LinkedHashMap<>();

        // 初始化月份数据(当前年份的1月到12月)
        for (int i = 1; i <= 12; i++) {
            String monthKey = currentYear + "-" + (i < 10 ? "0" + i : i);
            monthlyInvoicedMap.put(monthKey, BigDecimal.ZERO);
            monthlyNotInvoicedMap.put(monthKey, BigDecimal.ZERO);
        }

        // 按担保公司分组的年度开票金额
        Map<String, BigDecimal> companyInvoicedMap = new HashMap<>();

        for (InvoicingBusinessVo vo : invoicingApplicationVos) {
            BigDecimal amount = vo.getInvoicingAmountTotal() != null ? vo.getInvoicingAmountTotal() : BigDecimal.ZERO;
            Date createTime = vo.getCreateTime();

            if (createTime != null) {
                LocalDate localDate = createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                int year = localDate.getYear();
                int month = localDate.getMonthValue();
                int quarter = (month - 1) / 3 + 1;

                // 构建月份键
                String monthKey = year + "-" + (month < 10 ? "0" + month : month);

                // 判断是否已开票
                boolean isInvoiced = vo.getFileUrl() != null && !vo.getFileUrl().isEmpty();

                // 按年统计
                if (year == currentYear) {
                    if (isInvoiced) {
                        yearInvoicedAmount = yearInvoicedAmount.add(amount);

                        // 按担保公司统计年度开票金额
                        String companyName = vo.getMainBodyName() != null ? vo.getMainBodyName() : "未知公司";
                        companyInvoicedMap.put(companyName,
                                companyInvoicedMap.getOrDefault(companyName, BigDecimal.ZERO).add(amount));

                        // 按月份统计已开票
                        monthlyInvoicedMap.put(monthKey,
                                monthlyInvoicedMap.getOrDefault(monthKey, BigDecimal.ZERO).add(amount));
                    } else {
                        yearNotInvoicedAmount = yearNotInvoicedAmount.add(amount);

                        // 按月份统计未开票
                        monthlyNotInvoicedMap.put(monthKey,
                                monthlyNotInvoicedMap.getOrDefault(monthKey, BigDecimal.ZERO).add(amount));
                    }

                    // 按季度统计
                    if (quarter == currentQuarter) {
                        if (isInvoiced) {
                            quarterInvoicedAmount = quarterInvoicedAmount.add(amount);
                        } else {
                            quarterNotInvoicedAmount = quarterNotInvoicedAmount.add(amount);
                        }
                    }
                }
            }
        }

        // 将年度和季度统计数据放入returnMap
        returnMap.put("yearInvoicedAmount", yearInvoicedAmount);  //本年累计开票金额
        returnMap.put("yearNotInvoicedAmount", yearNotInvoicedAmount); //本年未开票金额
        returnMap.put("quarterInvoicedAmount", quarterInvoicedAmount); //本季度累计开票金额
        returnMap.put("quarterNotInvoicedAmount", quarterNotInvoicedAmount); //本季度未开票金额

        // 获取开票前十排名
        List<Map.Entry<String, BigDecimal>> companyRankList = companyInvoicedMap.entrySet().stream()
                .sorted(Map.Entry.<String, BigDecimal>comparingByValue().reversed())
                .limit(10)
                .collect(Collectors.toList());

        // 转换为前端需要的格式
        List<Map<String, Object>> topTenList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : companyRankList) {
            Map<String, Object> companyMap = new HashMap<>();
            companyMap.put("companyName", entry.getKey());
            companyMap.put("amount", entry.getValue().divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
            topTenList.add(companyMap);
        }
        returnMap.put("topTenList", topTenList); //本年开票前十排名

        // 月度已开票和未开票数据
        List<Map<String, Object>> monthlyData = new ArrayList<>();
        for (String month : monthlyInvoicedMap.keySet()) {
            Map<String, Object> monthData = new HashMap<>();
            monthData.put("month", month);
            monthData.put("monthlyInvoiced", monthlyInvoicedMap.get(month).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP)); //已开票金额
            monthData.put("monthlyNotInvoiced", monthlyNotInvoicedMap.get(month).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP)); //未开票金额
            monthlyData.add(monthData);
        }
        returnMap.put("monthlyData", monthlyData); //月度开票统计

        return returnMap;
    }

    /**
     * 校验开票申请文件信息
     *
     * @param invoicingBusinessVoList
     * @return 结果
     */
    @Override
    public Map<String, List<InvoicingBusinessVo>> importDataCheck(List<InvoicingBusinessVo> invoicingBusinessVoList) {
        // 准备结果集
        Map<String, List<InvoicingBusinessVo>> result = new HashMap<>();
        result.put("duplicateList", new ArrayList<>());
        result.put("successList", new ArrayList<>());

        // 检查输入列表是否为空
        if (invoicingBusinessVoList == null || invoicingBusinessVoList.isEmpty()) {
            return result;
        }

        // 第一步：检查列表内部重复的开票申请编号
        Map<String, List<InvoicingBusinessVo>> codeMap = new HashMap<>();

        // 按开票申请编号分组
        for (InvoicingBusinessVo vo : invoicingBusinessVoList) {
            if (vo == null) {
                result.get("duplicateList").add(vo);
                continue;
            }
            String code = vo.getInvoicingApplicationCode();
            if (code != null && !code.isEmpty()) {
                codeMap.computeIfAbsent(code, k -> new ArrayList<>()).add(vo);
            }
        }

        // 找出重复的和不重复的
        for (Map.Entry<String, List<InvoicingBusinessVo>> entry : codeMap.entrySet()) {
            List<InvoicingBusinessVo> vos = entry.getValue();
            if (vos == null || vos.isEmpty()) {
                continue;
            }
            if (vos.size() > 1) {
                // 开票申请编号在列表中重复
                result.get("duplicateList").addAll(vos);
            } else {
                // 开票申请编号在列表中不重复
                result.get("successList").add(vos.get(0));
            }
        }

        // 第二步：检查不重复的开票申请编号是否在数据库中已存在
        List<String> uniqueCodes = result.get("successList").stream()
                .filter(Objects::nonNull)
                .map(InvoicingBusinessVo::getInvoicingApplicationCode)
                .filter(code -> code != null && !code.isEmpty())
                .collect(Collectors.toList());

        if (!uniqueCodes.isEmpty()) {
            // 创建查询条件对象
            InvoicingBusinessVo queryVo = new InvoicingBusinessVo();
            queryVo.setInvoicingApplicationCodes(uniqueCodes);

            // 查询数据库中已存在的记录
            List<InvoicingBusinessVo> existingList = selectInvoicingBusinessList(queryVo);

            // 防止数据库查询结果为空
            if (existingList == null) {
                existingList = Collections.emptyList();
            }

            // 获取数据库中已存在的开票申请编号
            Set<String> existingCodes = existingList.stream()
                    .filter(Objects::nonNull)
                    .map(InvoicingBusinessVo::getInvoicingApplicationCode)
                    .filter(code -> code != null && !code.isEmpty())
                    .collect(Collectors.toSet());


            //收集
            Set<String> companyShortName = invoicingBusinessVoList.stream()
                    .filter(Objects::nonNull)
                    .flatMap(dc -> Stream.of(
                            dc.getMainBodyName()
                    ))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (companyShortName.isEmpty()){
                throw new RuntimeException("请检查导入模版或数据是否正确");
            }
            List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectCompanyListByCompanyShortNames(companyShortName);
            Map<String, Long> companyMap = sysCompanyVos.stream().collect(Collectors.toMap(
                    SysCompanyVo::getCompanyShortName, // Key 选择器
                    SysCompanyVo::getId,              // Value 选择器
                    (existing, replacement) -> existing // 合并函数（处理重复key）
            ));


            // 重新筛选successList，将数据库中已存在的移到duplicateList
            List<InvoicingBusinessVo> newSuccessList = new ArrayList<>();

            // 使用迭代器安全地移除元素
            Iterator<InvoicingBusinessVo> iterator = result.get("successList").iterator();
            while (iterator.hasNext()) {
                InvoicingBusinessVo vo = iterator.next();
                if (vo == null || vo.getInvoicingApplicationCode() == null) {
                    iterator.remove(); // 安全地从successList中移除
                    result.get("duplicateList").add(vo);
                    continue;
                }
                // 检查公司是否存在
                if (!companyMap.containsKey(vo.getMainBodyName())) {
                    iterator.remove(); // 安全地从successList中移除
                    result.get("duplicateList").add(vo);
                    continue;
                }
                // 设置ID
                vo.setMainBodyId(companyMap.get(vo.getMainBodyName()));

                if (existingCodes.contains(vo.getInvoicingApplicationCode())) {
                    // 开票申请编号在数据库中已存在
                    iterator.remove(); // 安全地从successList中移除
                    result.get("duplicateList").add(vo);
                } else {
                    // 开票申请编号在数据库中不存在
                    newSuccessList.add(vo);
                }
            }

            // 更新successList
            result.put("successList", newSuccessList);
        }

        return result;
    }

    /**
     * 校验开票申请文件信息
     *
     * @param invoicingBusinessVo
     * @return 结果
     */
    @Override
    public int importData(InvoicingBusinessVo invoicingBusinessVo) {
        invoicingBusinessVo.getSuccessList().forEach(vo -> {
            vo.setCreateBy(getUsername());
            vo.setChannel("3");
            vo.setFileUrl("-");
            vo.setFileName("-");
        });
        return insertInvoicingBusinessBatch(invoicingBusinessVo.getSuccessList());
    }

//    /**
//     * 清理指定的临时目录
//     *
//     * @param tempDirPath 临时目录路径
//     * @return 清理结果，成功返回1，失败返回0
//     */
//    public int cleanTempDirectory(String tempDirPath) {
//        if (tempDirPath == null || tempDirPath.isEmpty()) {
//            return 0;
//        }
//
//        try {
//            File tempDir = new File(tempDirPath);
//            if (tempDir.exists() && tempDir.isDirectory()) {
//                File[] files = tempDir.listFiles();
//                if (files != null) {
//                    for (File file : files) {
//                        if (file.isFile()) {
//                            FileUtils.deleteFile(file.getAbsolutePath());
//                        } else if (file.isDirectory()) {
//                            // 递归删除子目录
//                            deleteDirectory(file);
//                        }
//                    }
//                }
//                // 删除空目录
//                return tempDir.delete() ? 1 : 0;
//            }
//            return 0;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return 0;
//        }
//    }

    /**
     * 递归删除目录及其内容
     *
     * @param directory 要删除的目录
     * @return 成功返回true，失败返回false
     */
    private boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        return directory.delete();
    }

    /**
     * 新增开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    @Override
    public int miniProgramInvoicing(InvoicingBusinessVo invoicingBusiness){
        if ("3".equals(invoicingBusiness.getInvoicingStatus())){
            invoicingBusiness.setInvoicingStatus("4");
            updateInvoicingBusiness(invoicingBusiness);
        } else if ("4".equals(invoicingBusiness.getInvoicingStatus())){
            throw new ServiceException("失败订单不允许重复提交");
        }

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        // 获取今天的结束时间（23:59:59）
        LocalDateTime endOfDay = now.toLocalDate().atTime(LocalTime.MAX);
        // 计算剩余秒数
        Duration duration = Duration.between(now, endOfDay);
        long remainingSeconds = duration.getSeconds();
        if (!redisCache.exists("InvoicingApplicationCodeCount")){
            redisCache.setCacheObject("InvoicingApplicationCodeCount",0,remainingSeconds, TimeUnit.SECONDS);
        }
        String format = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int cacheObject = (int) redisCache.getCacheObject("InvoicingApplicationCodeCount") + 1;
        redisCache.setCacheObject("InvoicingApplicationCodeCount",cacheObject,remainingSeconds, TimeUnit.SECONDS);
        String code = format + cacheObject;

        invoicingBusiness.setInvoicingApplicationCode(code);
        invoicingBusiness.setInvoicingApplicationTime(DateUtils.getNowDate());
        invoicingBusiness.setChannel("2");
        invoicingBusiness.setCreateBy(invoicingBusiness.getPhoneNum());

        DebtConversionVo debtConversion = new DebtConversionVo();
        debtConversion.setIds(invoicingBusiness.getIds());
        debtConversion.setInvoiceStatus("2");
        debtConversion.setUpdateBy(invoicingBusiness.getPhoneNum());
        debtConversion.setUpdateTime(DateUtils.getNowDate());
        debtConversionService.updateInvoicingApplicationCode(debtConversion);
        invoicingBusinessMapper.insertInvoicingBusiness(invoicingBusiness);

        List<InvoicingMiddle> invoicingMiddleList= new ArrayList<>();
        invoicingBusiness.getIds().forEach(id -> {
            InvoicingMiddle invoicingMiddle = new InvoicingMiddle();
            invoicingMiddle.setCorrelationId(id);
            invoicingMiddle.setInvoicingBusinessId(invoicingBusiness.getId());
            invoicingMiddle.setCorrelationType("2");
            invoicingMiddleList.add(invoicingMiddle);
        });
        return invoicingMiddleService.batchInsertInvoicingMiddle(invoicingMiddleList);
    }

}
