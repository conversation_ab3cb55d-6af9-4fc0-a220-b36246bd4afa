package org.ruoyi.core.debtConversion.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 债转通知明细对象 dc_debt_conversion
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
public class DebtConversion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联上传文件 */
    @Excel(name = "关联上传文件")
    private Long fileId;

    /** 借款申请编号 */
    @Excel(name = "借款申请编号")
    private String loanCode;

    /** 借款人 */
    @Excel(name = "借款人" , sort = 2)
    private String borrower;

    /** 身份证号 */
    @Excel(name = "身份证号" , sort = 3)
    private String idCard;

    /** 手机号 */
    @Excel(name = "手机号" , sort = 4)
    private String phoneNum;

    /** 借款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "借款时间" ,sort = 6 , width = 30, dateFormat = "yyyy-MM-dd")
    private Date loanTime;

    /** 担保时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "担保时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date guaranteeTime;

    /** 担保公司 */
    @Excel(name = "担保公司")
    private Long custId;

    /** 资产方 */
    @Excel(name = "资产方")
    private Long partnerId;

    /** 资金方 */
    @Excel(name = "资金方")
    private Long fundId;

    /** 借款金额 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "借款金额(元)" ,sort = 5, width = 30, dateFormat = "yyyy-MM-dd")
    private BigDecimal loanAmount;

    /** 债权接收方 */
    @Excel(name = "债权接收方")
    private Long debtRecipientId;

    /** 债转通知编号 */
    @Excel(name = "债转通知编号" ,sort = 1)
    private String debtConversionCode;

    /** 申请开票状态 1.未申请 2.已申请 */
    @Excel(name = "申请开票状态 1.未申请 2.已申请")
    private String invoiceStatus;

    /** 推送渠道 1.小程序 */
    @Excel(name = "推送渠道" ,readConverterExp = "1=小程序",sort = 12)
    private String pushChannel;

    /** 用户是否阅读 1.否 2.是 */
    @Excel(name = "用户是否阅读 1.否 2.是",readConverterExp = "1=否,2=是", sort = 14)
    private String isRead;

    /** 阅读时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" , sort = 15)
    private Date readTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DebtConversion that = (DebtConversion) o;
        return Objects.equals(loanCode, that.loanCode) &&
                Objects.equals(borrower, that.borrower) &&
                Objects.equals(idCard, that.idCard) &&
                Objects.equals(phoneNum, that.phoneNum) &&
                Objects.equals(loanTime, that.loanTime) &&
                Objects.equals(guaranteeTime, that.guaranteeTime) &&
                Objects.equals(custId, that.custId) &&
                Objects.equals(partnerId, that.partnerId) &&
                Objects.equals(fundId, that.fundId) &&
                Objects.equals(loanAmount, that.loanAmount) &&
                Objects.equals(debtRecipientId, that.debtRecipientId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(loanCode, borrower, idCard, phoneNum,
                loanTime, guaranteeTime, custId, partnerId,
                fundId, loanAmount, debtRecipientId);
    }

}
