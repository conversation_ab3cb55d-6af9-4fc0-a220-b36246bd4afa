package org.ruoyi.core.debtConversion.service.impl;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.service.ISysCompanyService;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.debtConversion.domain.InvoicingApplicationFile;
import org.ruoyi.core.debtConversion.domain.vo.DebtConversionFileVo;
import org.ruoyi.core.debtConversion.domain.vo.InvoicingApplicationFileVo;
import org.ruoyi.core.debtConversion.domain.vo.InvoicingApplicationImport;
import org.ruoyi.core.debtConversion.mapper.InvoicingApplicationFileMapper;
import org.ruoyi.core.debtConversion.service.IInvoicingApplicationFileService;
import org.ruoyi.core.debtConversion.service.IInvoicingApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 开票申请文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Service
public class InvoicingApplicationFileServiceImpl implements IInvoicingApplicationFileService
{
    @Autowired
    private InvoicingApplicationFileMapper invoicingApplicationFileMapper;
    @Autowired
    private ISysCompanyService sysCompanyService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IInvoicingApplicationService iInvoicingApplicationService;
    /**
     * 查询开票申请文件
     *
     * @param id 开票申请文件主键
     * @return 开票申请文件
     */
    @Override
    public InvoicingApplicationFile selectInvoicingApplicationFileById(Long id)
    {
        return invoicingApplicationFileMapper.selectInvoicingApplicationFileById(id);
    }

    /**
     * 查询开票申请文件列表
     *
     * @param invoicingApplicationFile 开票申请文件
     * @return 开票申请文件
     */
    @Override
    public List<InvoicingApplicationFileVo> selectInvoicingApplicationFileList(InvoicingApplicationFileVo invoicingApplicationFile)
    {
        List<InvoicingApplicationFileVo> invoicingApplicationFileVos = invoicingApplicationFileMapper.selectInvoicingApplicationFileList(invoicingApplicationFile);
        List<Long> custIds = invoicingApplicationFileVos.stream().map(InvoicingApplicationFileVo::getMainBodyId).distinct().collect(Collectors.toList());
        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(custIds);
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectSysCompanyList(sysCompanyVo);
        Map<Long, String> companyMap = sysCompanyVos.stream().collect(Collectors.toMap(
                SysCompanyVo::getId,              // Value 选择器
                SysCompanyVo::getCompanyShortName, // Key 选择器
                (existing, replacement) -> existing // 合并函数（处理重复key）
        ));

        List<String> CreateBys = invoicingApplicationFileVos.stream().map(InvoicingApplicationFileVo::getCreateBy).distinct().collect(Collectors.toList());
        SysUser sysUser = new SysUser();
        sysUser.setUserNames(CreateBys);

        List<SysUser> sysUsers = sysUserService.selectUserListOfDayLog(sysUser);
        Map<String, String> userMap = sysUsers.stream().collect(Collectors.toMap(
                SysUser::getUserName,              // Value 选择器
                SysUser::getNickName, // Key 选择器
                (existing, replacement) -> existing // 合并函数（处理重复key）
        ));
        invoicingApplicationFileVos.forEach(vo -> {
            vo.setMainBodyName(companyMap.get(vo.getMainBodyId()));
            vo.setCreateByName(userMap.get(vo.getCreateBy()));
        });

        return invoicingApplicationFileVos;
    }



    /**
     * 新增开票申请文件
     *
     * @param invoicingApplicationFile 开票申请文件
     * @return 结果
     */
    @Override
    public int insertInvoicingApplicationFile(InvoicingApplicationFile invoicingApplicationFile)
    {
        invoicingApplicationFile.setCreateTime(DateUtils.getNowDate());
        return invoicingApplicationFileMapper.insertInvoicingApplicationFile(invoicingApplicationFile);
    }

    /**
     * 修改开票申请文件
     *
     * @param invoicingApplicationFile 开票申请文件
     * @return 结果
     */
    @Override
    public int updateInvoicingApplicationFile(InvoicingApplicationFile invoicingApplicationFile)
    {
        invoicingApplicationFile.setUpdateTime(DateUtils.getNowDate());
        return invoicingApplicationFileMapper.updateInvoicingApplicationFile(invoicingApplicationFile);
    }

    /**
     * 批量删除开票申请文件
     *
     * @param ids 需要删除的开票申请文件主键
     * @return 结果
     */
    @Override
    public int deleteInvoicingApplicationFileByIds(Long[] ids)
    {
        return invoicingApplicationFileMapper.deleteInvoicingApplicationFileByIds(ids);
    }

    /**
     * 删除开票申请文件信息
     *
     * @param id 开票申请文件主键
     * @return 结果
     */
    @Override
    public int deleteInvoicingApplicationFileById(Long id)
    {
        return invoicingApplicationFileMapper.deleteInvoicingApplicationFileById(id);
    }

    @Override
    public Map<String,List<InvoicingApplicationImport>> importDataCheck(List<InvoicingApplicationImport> invoicingApplicationImportList){
        // 准备结果集
        Map<String, List<InvoicingApplicationImport>> result = new HashMap<>();
        result.put("duplicateList", new ArrayList<>());
        result.put("successList", new ArrayList<>());

        //收集
        Set<String> companyShortName = Collections.emptySet();
        if (invoicingApplicationImportList != null && !invoicingApplicationImportList.isEmpty()) {
            companyShortName = invoicingApplicationImportList.stream()
                    .filter(Objects::nonNull)
                    .flatMap(dc -> Stream.of(
                            dc.getPartnerName(),
                            dc.getFundName()
                    ))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        if (companyShortName.isEmpty()){
           throw new RuntimeException("请检查导入模版或数据是否正确");
        }
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectCompanyListByCompanyShortNames(companyShortName);
        Map<String, Long> companyMap = sysCompanyVos.stream().collect(Collectors.toMap(
                SysCompanyVo::getCompanyShortName, // Key 选择器
                SysCompanyVo::getId,              // Value 选择器
                (existing, replacement) -> existing // 合并函数（处理重复key）
        ));

        result.put("successList", invoicingApplicationImportList);

        // 使用迭代器安全地移除元素
        Iterator<InvoicingApplicationImport> iterator = result.get("successList").iterator();
        while (iterator.hasNext()) {
            InvoicingApplicationImport vo = iterator.next();

            // 检查公司是否存在
            if (  !companyMap.containsKey(vo.getFundName()) || !companyMap.containsKey(vo.getPartnerName())) {
                iterator.remove(); // 安全地从successList中移除
                result.get("duplicateList").add(vo);
                continue;
            }
            // 设置ID
            vo.setFundId(companyMap.get(vo.getFundName()));
            vo.setPartnerId(companyMap.get(vo.getPartnerName()));
        }
//        for (InvoicingApplicationImport vo : invoicingApplicationImportList) {
//            //资金方 资产方 不存在公司时
//            if (!companyMap.containsKey(vo.getFundName()) || !companyMap.containsKey(vo.getPartnerName())) {
//                result.get("duplicateList").add(vo);
//                continue;
//            } else {
//                result.get("successList").add(vo);
//            }
//            vo.setFundId(companyMap.get(vo.getFundName()));
//            vo.setPartnerId(companyMap.get(vo.getPartnerName()));
//        }
        return result;
    }

    /**
     * 数据校验成功 批量新增
     * @param invoicingApplicationFile
     * @return
     */
    @Override
    public int importData(InvoicingApplicationFileVo invoicingApplicationFile){
        invoicingApplicationFile.setCreateBy(getUsername());
        insertInvoicingApplicationFile(invoicingApplicationFile);
        invoicingApplicationFile.getSuccessList().forEach(vo -> {
            vo.setFileId(invoicingApplicationFile.getId());
            vo.setCreateBy(getUsername());
        });
        return iInvoicingApplicationService.insertInvoicingApplicationBatch(invoicingApplicationFile.getSuccessList());
    }
}
