package org.ruoyi.core.personnel.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.ruoyi.core.payrollFile.domain.PayrollFileRecord;

import java.util.Date;
import java.util.List;

/**
 * 人员流程对象 rs_personnel_process
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
public class PersonnelProcess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 流程类型 1入职申请 2转正申请 3调职调岗 4薪资调整 5离职申请 */
    //@Excel(name = "流程类型 1入职申请 2转正申请 3调职调岗 4薪资调整 5离职申请")
    private String processType;

    /**关联id */
    private Long correlationId;

    /** 标题 */
    @Excel(name = "标题")
    private String processName;

    /** 流程关联id */
    @Excel(name = "流程关联id")
    private String processId;

    /** 关联的id */
    @Excel(name = "员工姓名")
    private String personnelName;

    /** 状态 1.未完成 2.完成 3.草稿 4.废弃  */
    //@Excel(name = " 状态 1.未完成 2.完成 3.草稿 4.废弃  ")
    private String processState;

    private String recordId;

    @Excel(name = "发起人")
    private String sponsor;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    private List<Long> deptIds;

    private List<Long> unitIds;

    private List<String> processIds;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("processType", getProcessType())
            .append("processName", getProcessName())
            .append("processId", getProcessId())
            .append("personnelName", getPersonnelName())
            .append("processState", getProcessState())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
