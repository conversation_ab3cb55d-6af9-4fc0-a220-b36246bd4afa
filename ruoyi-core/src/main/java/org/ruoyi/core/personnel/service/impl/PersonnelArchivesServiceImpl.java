package org.ruoyi.core.personnel.service.impl;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.*;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.domain.vo.SysPostVo;
import com.ruoyi.system.domain.vo.SysUserPostVo;
import com.ruoyi.system.service.*;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.personnel.domain.PersonnelArchives;
import org.ruoyi.core.personnel.domain.PersonnelFile;
import org.ruoyi.core.personnel.domain.enums.PoliticalLandscapeEnum;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesExcel;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelFileVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelOrganizationVo;
import org.ruoyi.core.personnel.mapper.PersonnelArchivesMapper;
import org.ruoyi.core.personnel.service.*;
import org.ruoyi.core.personnel.util.IdCodeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;


/**
 * 人员档案Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-02
 */
@Service
public class PersonnelArchivesServiceImpl implements IPersonnelArchivesService
{
    @Autowired
    private PersonnelArchivesMapper personnelArchivesMapper;
    @Autowired
    private IPersonnelFormalService personnelFormalService;
    @Autowired
    private IPersonnelFileService personnelFileService;
    @Autowired
    private ISysPostService sysPostService;
    @Autowired
    private IPersonnelOnboardingService personnelOnboardingService;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysUnitService sysUnitService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private NewAuthorityServiceImpl newAuthorityService;
    @Autowired
    private ISysCompanyService companyService;
    /**
     * 查询人员档案
     *
     * @param id 人员档案主键
     * @return 人员档案
     */
    @Override
    public PersonnelArchives selectPersonnelArchivesById(Long id)
    {
        PersonnelArchives personnelArchives = personnelArchivesMapper.selectPersonnelArchivesById(id);
        //根据人员id获取人员主岗位
        SysPostVo homePost = personnelArchivesMapper.selectPerHomePostById(id);
        if (!Objects.isNull(homePost)){
            //根据岗位id获取岗位详细信息
            SysPost sysPost = sysPostService.selectPostById(homePost.getPostId());
            //拼接部门
            String deptBreadcrumb = getDeptBreadcrumb(sysPost.getDept());
            //拼接岗位
            System.out.println(deptBreadcrumb);
            deptBreadcrumb = deptBreadcrumb + ">" + homePost.getPostName();
            personnelArchives.setDeptPostName(deptBreadcrumb);
        }
        return personnelArchives;
    }

    /**
     * 拼接部门层级
     * @param sysDept
     * @return
     */
    public String getDeptBreadcrumb(SysDept sysDept) {
        if(sysDept!=null && sysDept.getDept()!=null ) {
            return getDeptBreadcrumb(sysDept.getDept())+">"+sysDept.getDeptName();
        }else if(sysDept!=null && sysDept.getDeptName()!=null){
            return sysDept.getDeptName();
        }else {
            return "";
        }
    }
    /**
     * 查询人员档案列表
     *
     * @param personnelArchivesVo 人员档案
     * @return 人员档案
     */
    @Override
    public List<PersonnelArchivesVo> selectPersonnelArchivesList(PersonnelArchivesVo personnelArchivesVo)
    {
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = getDataRange(loginUser);
        personnelArchivesVo.setDeptIds(dataRange.get("deptIds"));
        personnelArchivesVo.setUnitIds(dataRange.get("unitIds"));
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<SysRole> oneself = roles.stream().filter(role ->
                ("6".equals(role.getDataScope()) || "1".equals(role.getDataScope())) && "3".equals(role.getRoleType()) && "0".equals(role.getDelFlag()) && "0".equals(role.getStatus())
        ).collect(Collectors.toList());
        if (!oneself.isEmpty()){
            personnelArchivesVo.setCreateBy(loginUser.getUser().getUserName());
        }
        //没有任何权限直接返回空
        if(personnelArchivesVo.getDeptIds().isEmpty() && personnelArchivesVo.getUnitIds().isEmpty() && oneself.isEmpty()){
            return new ArrayList<>();
        }
        PageUtil.startPage();
        List<PersonnelArchivesVo> personnelArchives = personnelArchivesMapper.selectPersonnelArchivesList(personnelArchivesVo);

        if(personnelArchives.isEmpty()){
             return personnelArchives;
        }
        String[] userName= personnelArchives.stream().map(PersonnelArchivesVo::getSysName).toArray(String[]::new);
        if(userName.length > 0){
            List<SysUserPostVo> postListByUserName = sysPostService.getPostListByUserName(userName);
            Map<String, List<SysUserPostVo>> userPostMap = postListByUserName.stream().collect(Collectors.groupingBy(SysUserPostVo::getUserName));
            personnelArchives.forEach(vo -> {
                vo.setPostList(userPostMap.get(vo.getSysName()));
                vo.setAge(personnelOnboardingService.getAge(vo.getIdCard()));
            });
        }
        List<Long> longs = personnelArchives.stream().map(PersonnelArchivesVo::getId).collect(Collectors.toList());
        PersonnelFileVo personnelFile = new PersonnelFileVo();
        personnelFile.setCorrelationIds(longs);
        personnelFile.setFileState("1");
        personnelFile.setFileType("6");
        List<PersonnelFile> personnelFiles = personnelFileService.selectPersonnelFileList(personnelFile);
        Map<Long, List<PersonnelFile>> map = personnelFiles.stream().collect(Collectors.groupingBy(PersonnelFile::getCorrelationId));
        personnelArchives.forEach(archivesVo ->{
            archivesVo.setFiles(map.get(archivesVo.getId()));
        });
        return personnelArchives;
    }

    @Override
    public List<PersonnelArchivesVo> selectPersonnelArchivesVoList(PersonnelArchivesVo personnelArchivesVo){
        return personnelArchivesMapper.selectPersonnelArchivesList(personnelArchivesVo);
    }

    /**
     * 新增人员档案
     *
     * @param personnelArchives 人员档案
     * @return 结果
     */
    @Override
    public int insertPersonnelArchives(PersonnelArchives personnelArchives)
    {
        //获取上级的id
        Long directSuperior = personnelArchives.getDirectSuperior();
        //获取用户的id
        SysUser sysUser = new SysUser();
        sysUser.setStatus("0");
        sysUser.setUserName(personnelArchives.getSysName());
        List<SysUser> sysUsers = sysUserService.selectUserList(sysUser);
        //2024.05.23找所有的人事档案的表，用于获取直接上级的ancestors
        PersonnelArchivesVo personnelArchivesVo = new PersonnelArchivesVo();
        personnelArchivesVo.setDirectSuperior(directSuperior);
        List<PersonnelArchivesVo> personnelArchivesVos = personnelArchivesMapper.selectPersonnelArchivesList(personnelArchivesVo);
        personnelArchives.setCreateTime(DateUtils.getNowDate());
        personnelArchives.setCreateBy(getUsername());
        int count = getCountByCreateTime(DateUtils.getDate()) + 1;
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        personnelArchives.setArchivesCode("RYDA" + createTimeNum + String.format("%03d", count));
        //然后从查询到的list当中获取到直接上级的用户层级ancestors，进行拼接自己的ancestors
        PersonnelArchivesVo personnelArchivesVo1 = personnelArchivesVos.stream().filter(t -> directSuperior.equals(t.getUserId())).findFirst().orElse(null);
        if (personnelArchivesVo1 != null) {
            //说明有上级，那么拼接ancestors时，先获取上级的ancestors
            String ancestors = personnelArchivesVo1.getAncestors();
            //拼接用户的ancestors
            String sysName = personnelArchives.getSysName();
            Long userId = sysUsers.stream().filter(t -> sysName.equals(t.getUserName())).map(SysUser::getUserId).findFirst().orElse(null);
            if (ancestors == null) {
                ancestors = StringUtils.EMPTY;
            }
            personnelArchives.setAncestors(ancestors + "," + userId);
        } else {
            //说明该用户的上级不存在于人事档案当中
            //拼接用户的ancestors
            String sysName = personnelArchives.getSysName();
            Long userId = sysUsers.stream().filter(t -> sysName.equals(t.getUserName())).map(SysUser::getUserId).findFirst().orElse(null);
            personnelArchives.setAncestors(userId.toString());
        }
        return personnelArchivesMapper.insertPersonnelArchives(personnelArchives);
    }

    /**
     * 修改人员档案
     *
     * @param personnelArchives 人员档案
     * @return 结果
     */
    @Override
    public int updatePersonnelArchives(PersonnelArchives personnelArchives)
    {
        personnelArchives.setUpdateTime(DateUtils.getNowDate());


        //关联最新的文件
        if(!personnelArchives.getFileIds().isEmpty()){
            //修改前先取消关联文件
            PersonnelFile deletelFile = new PersonnelFile();
            deletelFile.setFileType("6");
            deletelFile.setCorrelationId(personnelArchives.getId());
            personnelFileService.deleteByCorrelationId(deletelFile);

            PersonnelFileVo personnelFile = new PersonnelFileVo();
            personnelFile.setIds(personnelArchives.getFileIds());
            personnelFile.setCorrelationId(personnelArchives.getId());
            personnelFileService.correlationFile(personnelFile);
        }
        return personnelArchivesMapper.updatePersonnelArchives(personnelArchives);
    }

    /**
     * 批量删除人员档案
     *
     * @param ids 需要删除的人员档案主键
     * @return 结果
     */
    @Override
    public int deletePersonnelArchivesByIds(Long[] ids)
    {
        return personnelArchivesMapper.deletePersonnelArchivesByIds(ids);
    }

    /**
     * 删除人员档案信息
     *
     * @param id 人员档案主键
     * @return 结果
     */
    @Override
    public int deletePersonnelArchivesById(Long id)
    {
        return personnelArchivesMapper.deletePersonnelArchivesById(id);
    }

    /**
     * 批量新增
     *
     * @param personnelArchivesList
     */
    @Override
    public int batchInsert(List<PersonnelArchives> personnelArchivesList) {
        return personnelArchivesMapper.batchInsert(personnelArchivesList);
    }

    @Override
    public String importData(List<PersonnelArchivesExcel> personnelArchivesList) {
        List<SysCompanyVo> sysUnits = companyService.selectSysCompanyList(new SysCompanyVo());
        Map<String, Long> unitShortNameMap = sysUnits.stream()
                .collect(Collectors.toMap(
                        SysCompanyVo::getCompanyShortName,
                        SysCompanyVo::getId,
                        (first, second) -> first  // 冲突时保留第一个值
                ));
        SysUser sysUser = new SysUser();
        sysUser.setStatus("0");
        List<SysUser> sysUsers = sysUserService.selectUserList(sysUser);
        Map<String, Long> userMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getNickName, SysUser::getUserId, (existing, replacement) -> existing));

        //获取数据字典: 性别
        List<SysDictData> userSex = sysDictDataService.selectDictLabelByType("sys_user_sex");
        Map<String, String> userSexMap = userSex.stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        //获取数据字典: 人员状态
        List<SysDictData> personnelStatus = sysDictDataService.selectDictLabelByType("personnel_status");
        Map<String, String> personnelStatusMap = personnelStatus.stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        //获取数据字典: 人员类别
        List<SysDictData> personnelCategory = sysDictDataService.selectDictLabelByType("personnel_category");
        Map<String, String> personnelCategoryMap = personnelCategory.stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        //获取数据字典: 入职级别
        List<SysDictData> entryRank = sysDictDataService.selectDictLabelByType("entry_rank");
        Map<String, String> entryRankMap = entryRank.stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        personnelArchivesList.forEach(archives -> {
            if ("".equals(archives.getName()) || archives.getName() == null){
                throw new ServiceException("人员姓名不能为空");
            }
            if (unitShortNameMap.get(archives.getArchivesCompanyName()) == null){
                throw new ServiceException("请检查" + archives.getName() + "的公司是否正确规范");
            }
            archives.setArchivesCompany(unitShortNameMap.get(archives.getArchivesCompanyName()));

            if (archives.getOnboardingTime() == null){
                throw new ServiceException("请检查" + archives.getName() + "的入职时间是否填写规范");
            }

            if ("".equals(archives.getSysName()) || archives.getSysName() == null){
                throw new ServiceException("请检查" + archives.getName() + "的系统名称是否填写");
            }

            if ("".equals(archives.getOfficeSpace()) || archives.getOfficeSpace() == null){
                throw new ServiceException("请检查" + archives.getName() + "的办公场地是否填写");
            }

            if ("".equals(archives.getPhoneNum()) || archives.getPhoneNum() == null){
                throw new ServiceException("请检查" + archives.getName() + "的手机号是否填写");
            } else {
                archives.setPhoneNum(archives.getPhoneNum().trim());
            }

             if ("".equals(archives.getSalaryAccount()) || archives.getSalaryAccount() == null){
                 throw new ServiceException("请检查" + archives.getName() + "的工资账号是否填写");
             } else {
                 archives.setSalaryAccount(archives.getSalaryAccount().trim());
             }

             if ("".equals(archives.getIdCard()) || archives.getIdCard() == null){
                 throw new ServiceException("请检查" + archives.getName() + "的身份证号是否填写");
             } else {
                 archives.setIdCard(archives.getIdCard().trim());
             }


           if (userSexMap.containsKey(archives.getSex())){
               archives.setSex(userSexMap.get(archives.getSex()));
           } else {
               throw new ServiceException("请检查" + archives.getName() + "的性别是否正确规范");
           }

           if(!"".equals(archives.getPoliticalLandscape()) && archives.getPoliticalLandscape() != null){
               String value = PoliticalLandscapeEnum.getValueByComment(archives.getPoliticalLandscape());
               if ("".equals(value)){
                    throw new ServiceException("请检查" + archives.getName() + "的政治面貌是否正确规范");
               }
               archives.setPoliticalLandscape(value);
           } else {
                   throw new ServiceException("请检查" + archives.getName() + "的政治面貌是否正确规范");
           }

            if (personnelStatusMap.containsKey(archives.getPersonnelState())){
                archives.setPersonnelState(personnelStatusMap.get(archives.getPersonnelState()));
            } else {
                throw new ServiceException("请检查" + archives.getName() + "的人员状态是否正确规范");
            }

            if (personnelCategoryMap.containsKey(archives.getPersonnelType())){
                archives.setPersonnelType(personnelCategoryMap.get(archives.getPersonnelType()));
            } else {
                throw new ServiceException("请检查" + archives.getName() + "的人员类别是否正确规范");
            }

            if(!"".equals(archives.getLeaderName()) && archives.getLeaderName() != null){
                if (userMap.get(archives.getLeaderName()) == null){
                    throw new ServiceException("请检查" + archives.getName() + "的直接上级是否正确规范");
                }
                archives.setDirectSuperior(userMap.get(archives.getLeaderName()));
            } else {
                throw new ServiceException("请检查" + archives.getName() + "的直接上级是否填写");
            }

            if(!"".equals(archives.getAccessCard()) && archives.getAccessCard() != null){
                if ("是".equals(archives.getAccessCard())){
                    archives.setAccessCard("0");
                } else if ("否".equals(archives.getAccessCard())){
                    archives.setAccessCard("1");
                } else {
                    throw new ServiceException("请检查" + archives.getName() + "的门禁卡是否正确规范");
                }
            }

            if(!"".equals(archives.getAttendanceEntry()) && archives.getAttendanceEntry() != null){
                if ("是".equals(archives.getAttendanceEntry())){
                    archives.setAttendanceEntry("0");
                } else if ("否".equals(archives.getAttendanceEntry())){
                    archives.setAttendanceEntry("1");
                } else {
                    throw new ServiceException("请检查" + archives.getName() + "的考勤录入是否正确规范");
                }
            }

            if (entryRankMap.containsKey(archives.getOnboardingRank())){
                archives.setOnboardingRank(entryRankMap.get(archives.getOnboardingRank()));
            } else {
                throw new ServiceException("请检查" + archives.getName() + "的人员职级是否正确规范");
            }

        });

        String[] idCardList = personnelArchivesList.stream().map(PersonnelArchivesExcel::getIdCard).toArray(String[]::new);
        String[] sysNameList = personnelArchivesList.stream().map(PersonnelArchivesExcel::getSysName).toArray(String[]::new);
        //系统存在的sysNames
        String[] userSysNameList = sysUsers.stream().map(SysUser::getUserName).toArray(String[]::new);
        //过滤导入中系统登陆名,现在的用户数据库不存在
        List<PersonnelArchivesExcel> noSysNameList = personnelArchivesList.stream()
                .filter(personnel -> !Arrays.asList(userSysNameList).contains(personnel.getSysName()))
                .collect(Collectors.toList());
        //根据身份证号查询人员档案
        List<PersonnelArchives> listByIdCard = selectListByIdCard(idCardList);
        //根据系统登陆名查询人员档案
        List<PersonnelArchives> listByIdSysName = selectListBySysName(sysNameList);

        List<PersonnelArchivesExcel> resultList = personnelArchivesList.stream()
                //过滤数据库身份证相同的数据
                .filter(element -> listByIdCard.stream().noneMatch(other1 -> other1.getIdCard().equals(element.getIdCard())))
                //系统名相同
                .filter(element -> listByIdSysName.stream().noneMatch(other2 -> other2.getSysName().equals(element.getSysName())))
                //user表不存在的系统名
                .filter(element -> noSysNameList.stream().noneMatch(other3 -> other3.getSysName().equals(element.getSysName())))
                .collect(Collectors.toList());

        //身份证格式校验
        List<PersonnelArchivesExcel> errorIdCardList =  new ArrayList<>();
        List<PersonnelArchivesExcel> trueIdCardList =  new ArrayList<>();
        AtomicInteger count = new AtomicInteger (getCountByCreateTime(DateUtils.getDate()));
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        //2024.05.23找所有的人事档案的表，用于获取直接上级的ancestors
        List<PersonnelArchivesVo> personnelArchivesVos = personnelArchivesMapper.selectPersonnelArchivesList(new PersonnelArchivesVo());
        resultList.forEach(personnelArchives -> {
                    if (IdCodeUtil.validate(personnelArchives.getIdCard())){
                        Date birthDate = null;
                        try {
                            // 提取出生日期
                            String birthDateString = personnelArchives.getIdCard().substring(6, 14);
                            // 转换日期格式
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                            birthDate = dateFormat.parse(birthDateString);
                        } catch (ParseException e) {
                            throw new ServiceException(personnelArchives.getName()+"身份证格式错误！");
                        }
                        personnelArchives.setArchivesCode("RYDA" + createTimeNum + String.format("%03d", count.incrementAndGet()));
                        personnelArchives.setBirthday(birthDate);
                        personnelArchives.setCreateBy(getUsername());
                        personnelArchives.setCreateTime(DateUtils.getNowDate());
                        personnelArchives.setDataSources("1");
                        //获取直接上级的id
                        Long directSuperior = personnelArchives.getDirectSuperior();
                        //然后从查询到的list当中获取到直接上级的用户层级ancestors，进行拼接自己的ancestors
                        PersonnelArchivesVo personnelArchivesVo = personnelArchivesVos.stream().filter(t -> directSuperior.equals(t.getUserId())).findFirst().orElse(null);
                        if (personnelArchivesVo != null) {
                            //说明有上级，那么拼接ancestors时，先获取上级的ancestors
                            String ancestors = personnelArchivesVo.getAncestors();
                            //拼接用户的ancestors
                            String sysName = personnelArchives.getSysName();
                            Long userId = sysUsers.stream().filter(t -> sysName.equals(t.getUserName())).map(SysUser::getUserId).findFirst().orElse(null);
                            if (ancestors == null) {
                                ancestors = StringUtils.EMPTY;
                            }
                            personnelArchives.setAncestors(ancestors + "," + userId);
                        } else {
                            //说明该用户的上级不存在于人事档案当中
                            //拼接用户的ancestors
                            String sysName = personnelArchives.getSysName();
                            Long userId = sysUsers.stream().filter(t -> sysName.equals(t.getUserName())).map(SysUser::getUserId).findFirst().orElse(null);
                            personnelArchives.setAncestors(userId.toString());
                        }
                        trueIdCardList.add(personnelArchives);
                    } else {
                        errorIdCardList.add(personnelArchives);
                    }
                }
        );
        if (!trueIdCardList.isEmpty()) {
            try {
                int i = personnelArchivesMapper.batchInsertExcel(trueIdCardList);
            } catch(Exception e) {
                throw new ServiceException( "请检查导入数据格式是否规范");
            }
        }
        String error = "";
        if (!listByIdCard.isEmpty()){
            error += "以下数据身份证号码重复：" +listByIdCard.stream().map(p -> "【" + p.getName() + "】").collect(Collectors.joining(""));
        }
        if (!errorIdCardList.isEmpty()){
            error += "以下数据身份证号码不符合校验格式：" +errorIdCardList.stream().map(p -> "【" + p.getName() + "】").collect(Collectors.joining(""));
        }
        if (!listByIdSysName.isEmpty()){
            error += "以下数据系统登陆名重复：" +listByIdSysName.stream().map(p -> "【" + p.getName() + "】").collect(Collectors.joining(""));
        }
        if (!noSysNameList.isEmpty()){
            error += "以下数据系统登陆名不存在：" +noSysNameList.stream().map(p -> "【" + p.getName() + "】").collect(Collectors.joining(""));
        }

        return "此次导入共计"+personnelArchivesList.size()+"条数据,导入成功"+trueIdCardList.size()+"条数据。" + error;
    }

    public List<PersonnelArchives> selectListByIdCard(String[] idCards){
        return personnelArchivesMapper.selectListByIdCard(idCards);
    }

    public List<PersonnelArchives> selectListBySysName(String[] sysNames){
        return personnelArchivesMapper.selectListBySysName(sysNames);
    }

    public int getCountByIdCard(String idCard){
        return personnelArchivesMapper.getCountByIdCard(idCard);
    }

    @Override
    public int getCountByCreateTime(String createTime) {
        return personnelArchivesMapper.getCountByCreateTime(createTime);
    }
    @Override
    public List<PersonnelArchivesVo> selectPersonnelArchivesListForTransfer(PersonnelArchivesVo personnelArchives){
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = getDataRange(loginUser);
        personnelArchives.setDeptIds(dataRange.get("deptIds"));
        personnelArchives.setUnitIds(dataRange.get("unitIds"));
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<SysRole> oneself = roles.stream().filter(role ->
                ("6".equals(role.getDataScope()) || "1".equals(role.getDataScope())) && "3".equals(role.getRoleType()) && "0".equals(role.getDelFlag()) && "0".equals(role.getStatus())
        ).collect(Collectors.toList());
        if (!oneself.isEmpty()){
            personnelArchives.setCreateBy(loginUser.getUser().getUserName());
        }
        //没有任何权限直接返回空
        if(personnelArchives.getDeptIds().isEmpty() && personnelArchives.getUnitIds().isEmpty() && oneself.isEmpty()){
            return new ArrayList<>();
        }
        PageUtil.startPage();
        List<PersonnelArchivesVo> archivesVoList = personnelArchivesMapper.selectPersonnelArchivesListForTransfer(personnelArchives);
         if(archivesVoList.isEmpty()){
             return archivesVoList;
         }
        //收集每一个人的登陆名,用来查询岗位
        String[] userName= archivesVoList.stream().map(PersonnelArchivesVo::getSysName).toArray(String[]::new);
         if (userName.length > 0){
             //根据所有人登陆名来查的岗位
            List<SysUserPostVo> postListByUserName = sysPostService.getPostListByUserName(userName);
             //根据每一个人的登陆名来分每一个的岗位List,然后组成Map,(key为登陆名,value为岗位List)
             Map<String, List<SysUserPostVo>> userPostMap = postListByUserName.stream().collect(Collectors.groupingBy(SysUserPostVo::getUserName));
            archivesVoList.forEach(vo -> {
                vo.setPostList(userPostMap.get(vo.getSysName()));
            });
         }

        List<Long> longs = archivesVoList.stream().map(PersonnelArchivesVo::getTransferId).collect(Collectors.toList());
        PersonnelFileVo personnelFile = new PersonnelFileVo();
        personnelFile.setCorrelationIds(longs);
        personnelFile.setFileState("1");
        personnelFile.setFileType("3");
        //查询数据关联文件
        List<PersonnelFile> personnelFiles = personnelFileService.selectPersonnelFileList(personnelFile);
        Map<Long, List<PersonnelFile>> map = personnelFiles.stream().collect(Collectors.groupingBy(PersonnelFile::getCorrelationId));

        archivesVoList.forEach(archivesVo -> {
            archivesVo.setFiles(map.get(archivesVo.getId()));
        });
         return archivesVoList;
    }

    /**
     * 查询人员未转正列表
     *
     * @param personnelArchives 人员档案
     * @return 人员档案集合
     */
    @Override
    public List<PersonnelArchives> getFormalBeforeList(PersonnelArchivesVo personnelArchives){
         return personnelArchivesMapper.getFormalBeforeList(personnelArchives);
    }

    /**
     * 离职上传文件
     * @param file
     * @return
     */
    @Override
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.PERSONNEL_SYSTEM, file);
            PersonnelFile personnelFile = new PersonnelFile();
            personnelFile.setFileUrl(url);
            personnelFile.setFileName(name);
            personnelFile.setFileState("0");
            personnelFile.setFileType("6");
            personnelFile.setCreateTime(DateUtils.getNowDate());
            personnelFile.setCreateBy(getUsername());
            personnelFileService.insertPersonnelFile(personnelFile);
            return AjaxResult.success(personnelFile);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    public List<PersonnelArchivesVo> listForResignation(PersonnelArchivesVo personnelArchivesVo){
        LoginUser loginUser = getLoginUser();
        //离职数据最少查询自己的名字
        personnelArchivesVo.setSysName(loginUser.getUsername());
        Map<String, List<Long>> dataRange = getDataRange(loginUser);
        personnelArchivesVo.setDeptIds(dataRange.get("deptIds"));
        personnelArchivesVo.setUnitIds(dataRange.get("unitIds"));

        PageUtil.startPage();  //解决若依框架一个接口查询多个list分页失效问题
        List<PersonnelArchivesVo> archivesVoList = personnelArchivesMapper.listForResignation(personnelArchivesVo);

        //收集每一个人的登陆名,用来查询岗位
        String[] userName= archivesVoList.stream().map(PersonnelArchivesVo::getSysName).toArray(String[]::new);
        if(userName.length > 0){
            //根据所有人登陆名来查的岗位
            List<SysUserPostVo> postListByUserName = sysPostService.getPostListByUserName(userName);
            //根据每一个人的登陆名来分每一个的岗位List,然后组成Map,(key为登陆名,value为岗位List)
            Map<String, List<SysUserPostVo>> userPostMap = postListByUserName.stream().collect(Collectors.groupingBy(SysUserPostVo::getUserName));
            archivesVoList.forEach(vo -> {
                //通过登陆名来获取岗位的List
                vo.setPostList(userPostMap.get(vo.getSysName()));
                //通过身份证来计算年龄
                vo.setAge(personnelOnboardingService.getAge(vo.getIdCard()));
            });
        }
        return archivesVoList;
    }

    @Override
    public int updatePersonnelStateBySysName(PersonnelArchives personnelArchives){
        return personnelArchivesMapper.updatePersonnelStateBySysName(personnelArchives);
    }

    /**
     * 获取数据范围
     */
    @Override
    public Map<String, List<Long>> getDataRange(LoginUser loginUser){
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<Long> deptIds = new ArrayList<>();
        List<Long> unitIds = new ArrayList<>();
        Map<String, List<Long>> dataRange = new HashMap<>();
        for (SysRole role : roles) {
            //角色权限为3
            if("3".equals(role.getRoleType()) && "0".equals(role.getDelFlag()) && "0".equals(role.getStatus())){
            //1.全部数据权限
            if ("1".equals(role.getDataScope())) {
                 deptIds = sysDeptService.selectDeptList(new SysDept()).stream().map(SysDept::getDeptId).collect(Collectors.toList());
                 unitIds = sysUnitService.selectSysUnitList(new SysUnit()).stream().map(SysUnit::getUnitId).collect(Collectors.toList());
                dataRange.put("deptIds",deptIds);
                dataRange.put("unitIds",unitIds);
                return dataRange;
            }
            //3.本部门数据权限
            if ("3".equals(role.getDataScope())) {
                deptIds.add(loginUser.getUser().getDeptId());
            }
            //4.本部门及子部门数据权限
            if ("4".equals(role.getDataScope())) {
                List<SysDept> sysDepts = sysDeptService.selectChildrenDeptById(loginUser.getUser().getDeptId());
                deptIds.add(loginUser.getDeptId());
                deptIds.addAll(sysDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
            }
            //5.自定义部门权限
            if ("5".equals(role.getDataScope())) {
                SysRole sysRole = sysRoleService.selectRoleByIdToScope(role.getRoleId());
                List<Long> ids = sysRole.getRoleDept().stream().map(SysRoleDept::getDeptId).collect(Collectors.toList());
                deptIds.addAll(ids);
            }
            //7.本公司权限
            if ("7".equals(role.getDataScope())) {
                unitIds.add(loginUser.getUser().getUnit().getUnitId());
            }
            //8、自定义公司权限
            if ("8".equals(role.getDataScope())) {
                SysRole sysRole = sysRoleService.selectRoleByIdToScope(role.getRoleId());
                List<Long> ids = sysRole.getRoleUnit().stream().map(SysRoleUnit::getUnitId).collect(Collectors.toList());
                unitIds.addAll(ids);
            }
            }
        }
        dataRange.put("deptIds",deptIds);
        dataRange.put("unitIds",unitIds);
        return dataRange;
    }

    @Override
    public Map<String, List<Long>> getDataRangeFilterRoleKey(LoginUser loginUser,String roleKey){
        List<SysRole> roles = loginUser.getUser().getRoles().stream()
                                          .filter(role -> role.getRoleKey().contains("rz-"))
                                          .collect(Collectors.toList());;
        List<Long> deptIds = new ArrayList<>();
        List<Long> unitIds = new ArrayList<>();
        Map<String, List<Long>> dataRange = new HashMap<>();
        for (SysRole role : roles) {
            //角色权限为3
            if("3".equals(role.getRoleType()) && "0".equals(role.getDelFlag()) && "0".equals(role.getStatus())){
            //1.全部数据权限
            if ("1".equals(role.getDataScope())) {
                 deptIds = sysDeptService.selectDeptList(new SysDept()).stream().map(SysDept::getDeptId).collect(Collectors.toList());
                 unitIds = sysUnitService.selectSysUnitList(new SysUnit()).stream().map(SysUnit::getUnitId).collect(Collectors.toList());
                dataRange.put("deptIds",deptIds);
                dataRange.put("unitIds",unitIds);
                return dataRange;
            }
            //3.本部门数据权限
            if ("3".equals(role.getDataScope())) {
                deptIds.add(loginUser.getUser().getDeptId());
            }
            //4.本部门及子部门数据权限
            if ("4".equals(role.getDataScope())) {
                List<SysDept> sysDepts = sysDeptService.selectChildrenDeptById(loginUser.getUser().getDeptId());
                deptIds.add(loginUser.getDeptId());
                deptIds.addAll(sysDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
            }
            //5.自定义部门权限
            if ("5".equals(role.getDataScope())) {
                SysRole sysRole = sysRoleService.selectRoleByIdToScope(role.getRoleId());
                List<Long> ids = sysRole.getRoleDept().stream().map(SysRoleDept::getDeptId).collect(Collectors.toList());
                deptIds.addAll(ids);
            }
            //7.本公司权限
            if ("7".equals(role.getDataScope())) {
                unitIds.add(loginUser.getUser().getUnit().getUnitId());
            }
            //8、自定义公司权限
            if ("8".equals(role.getDataScope())) {
                SysRole sysRole = sysRoleService.selectRoleByIdToScope(role.getRoleId());
                List<Long> ids = sysRole.getRoleUnit().stream().map(SysRoleUnit::getUnitId).collect(Collectors.toList());
                unitIds.addAll(ids);
            }
            }
        }
        dataRange.put("deptIds",deptIds);
        dataRange.put("unitIds",unitIds);
        return dataRange;
    }


    @Override
    public List<PersonnelArchivesVo> exportList(PersonnelArchivesVo personnelArchivesVo)
    {
        if (!"[]".equals(personnelArchivesVo.getIds())){
            personnelArchivesVo.setIds(personnelArchivesVo.getIds().substring(1, personnelArchivesVo.getIds().length() - 1));  // 去除首尾的方括号
        String[] array = personnelArchivesVo.getIds().split(",");
        List<Long> list = new ArrayList<>();
        for (String s : array) {
            list.add(Long.parseLong(s.replaceAll("\"", "")));
        }
            personnelArchivesVo.setIdArray(list);
         }
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = getDataRange(loginUser);
        personnelArchivesVo.setDeptIds(dataRange.get("deptIds"));
        personnelArchivesVo.setUnitIds(dataRange.get("unitIds"));
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<SysRole> oneself = roles.stream().filter(role ->
                ("6".equals(role.getDataScope()) || "1".equals(role.getDataScope())) && "3".equals(role.getRoleType()) && "0".equals(role.getDelFlag()) && "0".equals(role.getStatus())
        ).collect(Collectors.toList());
        if (!oneself.isEmpty()){
            personnelArchivesVo.setCreateBy(loginUser.getUser().getUserName());
        }
        //没有任何权限直接返回空
        if(personnelArchivesVo.getDeptIds().isEmpty() && personnelArchivesVo.getUnitIds().isEmpty() && oneself.isEmpty()){
            return new ArrayList<>();
        }
        List<PersonnelArchivesVo> personnelArchives = personnelArchivesMapper.selectPersonnelArchivesList(personnelArchivesVo);

        if(personnelArchives.isEmpty()){
            return personnelArchives;
        }
        String[] userName= personnelArchives.stream().map(PersonnelArchivesVo::getSysName).toArray(String[]::new);
        List<SysUnit> sysUnits = sysUnitService.selectSysUnitList(new SysUnit());
        Map<Long, String> unitShortNameMap = sysUnits.stream().collect(Collectors.toMap( SysUnit::getUnitId, SysUnit::getUnitShortName));
        if(userName.length > 0){
            List<SysUserPostVo> postListByUserName = sysPostService.getPostListByUserName(userName);
            Map<String, List<SysUserPostVo>> userPostMap = postListByUserName.stream().collect(Collectors.groupingBy(SysUserPostVo::getUserName));
            //获取数据字典: 性别
            List<SysDictData> userSex = sysDictDataService.selectDictLabelByType("sys_user_sex");
            Map<String, String> userSexMap = userSex.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
            //获取数据字典: 人员状态
            List<SysDictData> personnelStatus = sysDictDataService.selectDictLabelByType("personnel_status");
            Map<String, String> personnelStatusMap = personnelStatus.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
            //获取数据字典: 人员类别
            List<SysDictData> personnelCategory = sysDictDataService.selectDictLabelByType("personnel_category");
            Map<String, String> personnelCategoryMap = personnelCategory.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
            //获取数据字典: 入职级别
            List<SysDictData> entryRank = sysDictDataService.selectDictLabelByType("entry_rank");
            Map<String, String> entryRankMap = entryRank.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

            personnelArchives.forEach(vo -> {
                if( vo.getSysName() != null &&userPostMap.containsKey(vo.getSysName())){
                    String postNames = userPostMap.get(vo.getSysName()).stream()
                            .map(SysUserPostVo::getPostName)
                            .collect(Collectors.joining(", "));
                    vo.setOnboardingPostName(postNames);
                }

                vo.setSex(userSexMap.get(vo.getSex()));

                vo.setPersonnelType(personnelCategoryMap.get(vo.getPersonnelType()));

                if ("0".equals(vo.getAccessCard())){
                    vo.setAccessCard("已申领");
                } else {
                    vo.setAccessCard("未申领");
                }
                if ("0".equals(vo.getCardPrinting())){
                    vo.setCardPrinting("已印制");
                } else {
                    vo.setCardPrinting("未印制");
                }

                vo.setOnboardingRank(entryRankMap.get(vo.getOnboardingRank()));

                if ("0".equals(vo.getAttendanceEntry())){
                    vo.setAttendanceEntry("已录入");
                } else {
                    vo.setAttendanceEntry("未录入");
                }

                if ("0".equals(vo.getDataSources())){
                    vo.setDataSources("人员入职");
                } else {
                    vo.setDataSources("历史数据导入");
                }

                vo.setPersonnelState(personnelStatusMap.get(vo.getPersonnelState()));
                vo.setUnitName(unitShortNameMap.get(vo.getArchivesCompany()));
                vo.setPoliticalLandscapeName(PoliticalLandscapeEnum.getCommentByValue(vo.getPoliticalLandscape()));
            });
        }
        return personnelArchives;
    }

    /**
     * 获取自己和自己的下级
     * @return
     */
    @Override
    public PersonnelArchivesVo subordinate(Long principalId){
        LoginUser loginUser = getLoginUser();
        Long userId = loginUser.getUser().getUserId();
        if (principalId != null) {
            userId = principalId;
        }
        Long finalUserId = userId;
        List<PersonnelArchivesVo> archivesVoList = personnelArchivesMapper.subordinateList(userId);
        Optional<PersonnelArchivesVo> archivesVo = archivesVoList.stream().filter(vo -> finalUserId.equals(vo.getUserId())).findFirst();

        SysDept sysDept = new SysDept();
        List<SysDept> deptList = sysDeptService.selectDeptList(sysDept);

        Map<Long, List<PersonnelArchivesVo>> directSuperiorMap = archivesVoList.stream()
                // 设置部门链路
                .peek(vo -> vo.setDeptChain(buildDeptChain(vo.getDeptId(), deptList)))
                // 过滤掉没有直接上级的对象
                .filter(vo -> vo.getDirectSuperior() != null)
                // 根据直接上级进行分组
                .collect(Collectors.groupingBy(PersonnelArchivesVo::getDirectSuperior));

        if (!archivesVo.isPresent()){
            throw new ServiceException("未生成人员档案");
        } else {
            PersonnelArchivesVo personnelArchivesVo = archivesVo.get();
            personnelArchivesVo.setSubordinateList(directSuperiorMap.get(finalUserId));
            return personnelArchivesVo;
        }
    }

    /**
     * 通过遍历设置每一个对象的部门链路
     * @param deptId
     * @param deptList
     * @return
     */
    public static String buildDeptChain(Long deptId, List<SysDept> deptList) {
        StringBuilder chain = new StringBuilder();
        SysDept dept = findDeptById(deptId, deptList);
        if (dept != null) {
            chain.insert(0, dept.getDeptName());
            buildChainRecursive(dept.getParentId(), deptList, chain);
        }
        return chain.toString();
    }

    private static void buildChainRecursive(Long parentId, List<SysDept> deptList, StringBuilder chain) {
        if (parentId != 0) {
            SysDept dept = findDeptById(parentId, deptList);
            if (dept != null) {
                chain.insert(0, ">");
                chain.insert(0, dept.getDeptName());
                buildChainRecursive(dept.getParentId(), deptList, chain);
            }
        }
    }

    private static SysDept findDeptById(Long deptId, List<SysDept> deptList) {
        for (SysDept dept : deptList) {
            if (Objects.equals(dept.getDeptId(), deptId)) {
                return dept;
            }
        }
        return null;
    }


    /**
     * 获取全量公司的人员架构
     * @return
     */
    @Override
    public List<PersonnelOrganizationVo> getPersonnelOrganizationList(){
        //查询公司信息
        List<SysUnit> sysUnits = sysUnitService.selectSysUnitList(new SysUnit());
        Map<Long, String> unitNameMap = sysUnits.stream().collect(Collectors.toMap(SysUnit::getUnitId, SysUnit::getUnitName));


        PersonnelArchivesVo personnelArchivesVo = new PersonnelArchivesVo();
        List<PersonnelArchivesVo> personnelArchives = personnelArchivesMapper.selectPersonnelArchivesList(personnelArchivesVo)
                                .stream().filter(vo -> !"3".equals(vo.getPersonnelState())) //过滤已离职的数据
                                .collect(Collectors.toList());
        //收集每一个人的登陆名,用来查询岗位
        String[] userName= personnelArchives.stream().map(PersonnelArchivesVo::getSysName).toArray(String[]::new);
        Map<Long, List<SysUserPostVo>> userPostMap;
        Map<Long, String> usersStatus;
        if(userName.length > 0){
            //根据所有人登陆名来查的岗位,并过滤主岗位
            List<SysUserPostVo> postListByUserName = sysPostService.getPostListByUserName(userName).stream()
                        .filter(vo -> "1".equals(vo.getHomePost())).collect(Collectors.toList());
            //根据每一个人的登陆名来分每一个的岗位List,然后组成Map,(key为userId,value为岗位List)
             userPostMap = postListByUserName.stream().collect(Collectors.groupingBy(SysUserPostVo::getUserId));
             //去获取账号状态
            List<Long> userIds = personnelArchives.stream().map(PersonnelArchivesVo::getUserId).collect(Collectors.toList());
            usersStatus = sysUserService.selectUserByUserIds(userIds).stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getStatus));
        } else {
            //下方Lambda 表达式报红需要
            userPostMap = new HashMap<>();
            usersStatus = new HashMap<>();
        }

        // 使用 Stream 流将 personnelArchives 转换成 OrganizationVo
        List<PersonnelOrganizationVo> personnelOrganizationVoList = personnelArchives.stream()
                .map(vo -> new PersonnelOrganizationVo(
                        vo.getUserId(),
                        vo.getDirectSuperior(),
                        vo.getName(),
                        vo.getDeptName(),
                        vo.getPostName(),
                        unitNameMap.get(vo.getArchivesCompany()),
                        usersStatus.get(vo.getUserId()),
                        userPostMap.get(vo.getUserId())
                )).filter(orgVo -> "0".equals(orgVo.getStatus())) //账号停用的数据
                .collect(Collectors.toList());
        //根据他们的上级id来分组,然后组成树形结构
        Map<Long, List<PersonnelOrganizationVo>> personnelOrgListMap = personnelOrganizationVoList.stream()
                //过滤掉没有上级id和账号停用的数据
                .filter(orgVo -> orgVo.getPid() != null && orgVo.getPid() != 0)
                .collect(Collectors.groupingBy(PersonnelOrganizationVo::getPid));
        //组合每一个人的子集
        personnelOrganizationVoList.forEach(vo -> {
            vo.setChildren(personnelOrgListMap.get(vo.getId()));
        });
        return personnelOrganizationVoList.stream()
                //计算好出每一个子集 以及子集的子集数量
                .peek(vo -> vo.setNumbers(countNumbers(vo)))
                //最高一层没有上级
                .filter(orgVo -> orgVo.getPid() == null)
                .collect(Collectors.toList());
    }
    /**
     * 获取自己公司的人员架构
     * @return
     */
    @Override
    public List<PersonnelOrganizationVo> getPersonnelOrganizationOfSelf(){
        //查询公司信息
        List<SysUnit> sysUnits = sysUnitService.selectSysUnitList(new SysUnit());
        Map<Long, String> unitNameMap = sysUnits.stream().collect(Collectors.toMap(SysUnit::getUnitId, SysUnit::getUnitName));

        PersonnelArchivesVo personnelArchivesVo = new PersonnelArchivesVo();
        List<PersonnelArchivesVo> personnelArchives = personnelArchivesMapper.selectPersonnelArchivesList(personnelArchivesVo)
                .stream().filter(vo -> !"3".equals(vo.getPersonnelState())) //过滤已离职的数据
                .collect(Collectors.toList());
        //收集每一个人的登陆名,用来查询岗位
        String[] userName= personnelArchives.stream().map(PersonnelArchivesVo::getSysName).toArray(String[]::new);
        Map<Long, List<SysUserPostVo>> userPostMap;
        Map<Long, String> usersStatus;
        if(userName.length > 0){
            //根据所有人登陆名来查的岗位,并过滤主岗位
            List<SysUserPostVo> postListByUserName = sysPostService.getPostListByUserName(userName).stream()
                    .filter(vo -> "1".equals(vo.getHomePost())).collect(Collectors.toList());
            //根据每一个人的登陆名来分每一个的岗位List,然后组成Map,(key为userId,value为岗位List)
            userPostMap = postListByUserName.stream().collect(Collectors.groupingBy(SysUserPostVo::getUserId));
            //去获取账号状态
            List<Long> userIds = personnelArchives.stream().map(PersonnelArchivesVo::getUserId).collect(Collectors.toList());
            usersStatus = sysUserService.selectUserByUserIds(userIds).stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getStatus));
        } else {
            //下方Lambda 表达式报红需要
            userPostMap = new HashMap<>();
            usersStatus = new HashMap<>();
        }

        Long userId = getLoginUser().getUser().getUserId();
        Optional<PersonnelArchivesVo> elsfVo = personnelArchives.stream().filter(vo -> Objects.equals(vo.getUserId(), userId)).findFirst();
        //领导id
        Long  leaderId = elsfVo.map(PersonnelArchivesVo::getDirectSuperior).orElse(null);
        if (leaderId == null ){
            leaderId = 0L;
        }
        // 使用 Stream 流将 personnelArchives 转换成 OrganizationVo
        List<PersonnelOrganizationVo> personnelOrganizationVoList = personnelArchives.stream()
                .map(vo -> new PersonnelOrganizationVo(
                        vo.getUserId(),
                        vo.getDirectSuperior(),
                        vo.getName(),
                        vo.getDeptName(),
                        vo.getPostName(),
                        unitNameMap.get(vo.getArchivesCompany()),
                        usersStatus.get(vo.getUserId()),
                        userPostMap.get(vo.getUserId())
                )).filter(orgVo -> "0".equals(orgVo.getStatus())) //账号停用的数据
                .collect(Collectors.toList());
        //根据他们的上级id来分组,然后组成树形结构
        Map<Long, List<PersonnelOrganizationVo>> personnelOrgListMap = personnelOrganizationVoList.stream()
                //过滤掉没有上级id和账号停用的数据
                .filter(orgVo -> orgVo.getPid() != null && orgVo.getPid() != 0)
                .collect(Collectors.groupingBy(PersonnelOrganizationVo::getPid));

        Long finalLeaderId = leaderId; //Lambda表 报红需要转换
        personnelOrganizationVoList.forEach(vo -> {
            vo.setChildren(personnelOrgListMap.get(vo.getId()));
            if (vo.getChildren() != null && vo.getId().equals(finalLeaderId) && finalLeaderId !=0){
                vo.setChildren(vo.getChildren().stream().filter(ch -> Objects.equals(ch.getId(), userId)).collect(Collectors.toList()));
            }
        });

        if ( finalLeaderId != 0L){ //有直接上级时执行
            return personnelOrganizationVoList.stream()
                    //计算好出每一个子集 以及子集的子集数量
                    .peek(vo -> vo.setNumbers(countNumbers(vo)))
                    //最高一层没有上级
                    .filter(orgVo -> orgVo.getId() != null && orgVo.getId().equals( finalLeaderId))
                    .collect(Collectors.toList());
        } else { //没有直接上级时执行
            return personnelOrganizationVoList.stream()
                    //计算好出每一个子集 以及子集的子集数量
                    .peek(vo -> vo.setNumbers(countNumbers(vo)))
                    //最高一层没有上级
                    .filter(orgVo -> orgVo.getId() != null && orgVo.getId().equals( userId))
                    .collect(Collectors.toList());
        }

    }



    /**
        计算人员架构下面每一个人的数量
     */
    private int countNumbers(PersonnelOrganizationVo vo) {
        int count = 0; // 不包括自己
        if (vo.getChildren() != null) {
            for (PersonnelOrganizationVo child : vo.getChildren()) {
                count += countNumbers(child) + 1; // 包括子集及子集的子集
            }
        }
        return count;
    }

    @Override
    public List<PersonnelArchivesVo> selectListOfMonthLog(){
      return personnelArchivesMapper.selectListOfMonthLog();
    }

    @Override
    public List<PersonnelArchivesVo> getSubordinateList(){
      return personnelArchivesMapper.getSubordinateList(getUsername());
    }

    /**
     * 根据userName查询用户信息
     * @param userName
     * @return
     */
    @Override
    public PersonnelArchivesVo getPersonnelOrganization(String userName) {
        //查询用户
        PersonnelArchivesVo personnelArchivesVo = personnelArchivesMapper.queryPersonnelOrganization(userName);
        // getDeptPostName(personnelArchivesVo);
        return personnelArchivesVo;
    }

    public PersonnelArchivesVo getDeptPostName(PersonnelArchivesVo personVo){
        if (personVo.getSubordinateList() != null && personVo.getSubordinateList().size() > 0){
            List<PersonnelArchivesVo> subordinateList = personVo.getSubordinateList();
            for (PersonnelArchivesVo personnelArchivesVo : subordinateList) {
                //根据人员id获取人员主岗位
                SysPostVo homePost = personnelArchivesMapper.selectPerHomePostById(personnelArchivesVo.getId());
                if (!Objects.isNull(homePost)){
                    //根据岗位id获取岗位详细信息
                    SysPost sysPost = sysPostService.selectPostById(homePost.getPostId());
                    //拼接部门
                    String deptBreadcrumb = getDeptBreadcrumb(sysPost.getDept());
                    //拼接岗位
                    System.out.println(deptBreadcrumb);
                    deptBreadcrumb = deptBreadcrumb + ">" + homePost.getPostName();
                    personnelArchivesVo.setDeptPostName(deptBreadcrumb);
                    getDeptPostName(personnelArchivesVo);
                }
            }
        }
        return personVo;
    }

    @Override
    public List<PersonnelArchivesVo> getPerOrganizationList(PersonnelArchivesVo vos) {
        List<PersonnelArchivesVo> list = new ArrayList<>();
        list.add(vos);
        List<PersonnelArchivesVo> perList = getPerList(vos,list);
        return perList;
    }

    @Override
    public PersonnelArchivesVo subordinateForAgencyAuth() {
        LoginUser loginUser = getLoginUser();
        Long userId = loginUser.getUserId();
        PersonnelArchivesVo subordinate = subordinate(userId);
        List<PersonnelArchivesVo> subordinateList = subordinate.getSubordinateList();
        //获取当前用户的代理人
//        List<QueryAgentVO> agentList = newAuthorityService.getAgentList("1", "0", loginUser);
//        List<Long> agentIdList = agentList.stream().map(QueryAgentVO::getAgentId).collect(Collectors.toList());
        if (subordinateList != null) {
//            List<Map<String, Object>> agentList = newAuthorityService.agentListByQueryParam("1", loginUser.getUserId(), null);
//            List<Long> agentIdList = agentList.stream().map(t -> Long.parseLong(t.get("agentId").toString())).collect(Collectors.toList());
//            subordinate.setSubordinateList(subordinateList.stream().filter(t -> !agentIdList.contains(t.getUserId())).collect(Collectors.toList()));
            subordinate.setSubordinateList(subordinateList);
        }
        return subordinate;
    }

    @Override
    public PersonnelArchivesVo selectPersonnelArchivesByName(String userName) {
        PersonnelArchivesVo personnelArchives = new PersonnelArchivesVo();
        personnelArchives = personnelArchivesMapper.selectPersonnelArchivesByName(userName);
        if (!Objects.isNull(personnelArchives)) {
            // getDeptPostName(personnelArchives);
            //根据人员id获取人员主岗位
            SysPostVo homePost = personnelArchivesMapper.selectPerHomePostById(personnelArchives.getId());
            if (!Objects.isNull(homePost)) {
                //根据岗位id获取岗位详细信息
                SysPost sysPost = sysPostService.selectPostById(homePost.getPostId());
                //拼接部门
                String deptBreadcrumb = getDeptBreadcrumb(sysPost.getDept());
                //拼接岗位
                System.out.println(deptBreadcrumb);
                deptBreadcrumb = deptBreadcrumb + ">" + homePost.getPostName();
                personnelArchives.setDeptPostName(deptBreadcrumb);
            }
        }
        return personnelArchives;
    }

    private List<PersonnelArchivesVo> getPerList(PersonnelArchivesVo vo, List<PersonnelArchivesVo> list) {
        List<PersonnelArchivesVo> subordinateList = vo.getSubordinateList();
        for (PersonnelArchivesVo personnelArchivesVo : subordinateList) {
            list.add(personnelArchivesVo);
            if (personnelArchivesVo.getSubordinateList() != null && personnelArchivesVo.getSubordinateList().size() > 0){
                getPerList(personnelArchivesVo,list);
            }
        }
        return list;
    }


    @Override
    public PersonnelArchivesVo selectPersonnelArchivesInfoBysysName(String sysName){
        return personnelArchivesMapper.selectPersonnelArchivesInfoBysysName(sysName);
    }
    @Override
    public List<PersonnelArchives> selectListBySysNames(String[] sysNames){
        return personnelArchivesMapper.selectListBySysName(sysNames);
    }

}
