package org.ruoyi.core.personnel.service;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.information.domain.Information;
import org.ruoyi.core.personnel.domain.PersonnelOnboarding;
import org.ruoyi.core.personnel.domain.vo.PersonnelFormalVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelOnboardingVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 人员入职Service接口
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
public interface IPersonnelOnboardingService
{
    /**
     * 查询人员入职
     *
     * @param id 人员入职主键
     * @return 人员入职
     */
    public PersonnelOnboardingVo selectPersonnelOnboardingById(Long id);

    /**
     * 查询人员入职列表
     *
     * @param personnelOnboardingVo 人员入职
     * @return 人员入职集合
     */
    public List<PersonnelOnboardingVo> selectPersonnelOnboardingList(PersonnelOnboardingVo personnelOnboardingVo);

    /**
     * 新增人员入职
     *
     * @param personnelOnboarding 人员入职
     * @return 结果
     */
    public int insertPersonnelOnboarding(PersonnelOnboarding personnelOnboarding);

    /**
     * 修改人员入职
     *
     * @param personnelOnboarding 人员入职
     * @return 结果
     */
    public int updatePersonnelOnboarding(PersonnelOnboarding personnelOnboarding);

    public int updateByApprovaling(PersonnelOnboarding personnelOnboarding);
    /**
     * 批量删除人员入职
     *
     * @param ids 需要删除的人员入职主键集合
     * @return 结果
     */
    public int deletePersonnelOnboardingByIds(Long[] ids);

    /**
     * 删除人员入职信息
     *
     * @param id 人员入职主键
     * @return 结果
     */
    public int deletePersonnelOnboardingById(Long id);

    public int getCountByIdCard(String idCard);

    public int getCountByCreateTime(String createTime);

    List<PersonnelOnboarding> getCommitStateList(Long[] ids);

    public int commitOnboardingProcess(Long id);

    public String batchPersonnelArchives(Long[] ids);



    /**
     * 按ids 查询人员入职列表
     *
     * @return 人员入职集合
     */
    public List<PersonnelOnboarding> selectPersonnelOnboardingListByIds(Long[] ids);

    public List<PersonnelOnboardingVo> exportPersonnelOnboardingList(PersonnelOnboardingVo personnelOnboardingVo);

    public List<PersonnelFormalVo> getFormalBeforeList(PersonnelOnboardingVo personnelOnboardingVo);

    public int passOnboardingById(Long id);

    public int unpassOnboardingById(Long id);

    public int checkFormalById(Long id);

    /**
     * 新增人员入职
     *
     * @param personnelOnboarding 人员入职
     * @return 结果
     */
    public AjaxResult insertCommitOnboarding(PersonnelOnboardingVo personnelOnboarding);

    public AjaxResult uploadFile(MultipartFile file);

    public int getAge(String idCard);

    public AjaxResult getFormalBeforeBySysName(PersonnelOnboardingVo personnelOnboardingVo);
}
