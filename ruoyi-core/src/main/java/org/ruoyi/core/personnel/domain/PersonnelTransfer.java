package org.ruoyi.core.personnel.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 人员调动对象 rs_personnel_transfer
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@Data
public class PersonnelTransfer extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 人员档案id */
    //@Excel(name = "人员档案id")
    private Long archivesId;

    /** 人员名称 */
    @Excel(name = "人员名称")
    private String archivesName;

    /** 入职申请编号 */
    @Excel(name = "人员档案编号")
    private String archivesCode;

    /** 调出公司 */
    //@Excel(name = "调出公司")
    private Long outCompany;

    /** 调入公司 */
    //@Excel(name = "调入公司")
    private Long inCompany;

    /** 调出部门 */
    //@Excel(name = "调出部门")
    private Long outDept;

    /** 调入部门 */
    //@Excel(name = "调入部门")
    private Long inDept;

    /** 调出岗位 */
   // @Excel(name = "调出岗位")
    private Long outPost;

    /** 调入岗位 */
   //@Excel(name = "调入岗位")
    private Long inPost;

    /** 调动前上级领导 */
    @Excel(name = "调动前上级领导")
    private String outLeader;

    /** 调动后上级领导 */
    @Excel(name = "调动后上级领导")
    private Long inLeader;

    /** 调动生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "调动生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectivedate;

    /** 是否调整薪资(0 否 1 是 ) */
    @Excel(name = "是否调整薪资(0 否 1 是 )")
    private String isSalary;

    /** 调整薪资类型 */
    private String salaryType;

    /** 调薪前薪酬(元) */
    @Excel(name = "调薪前薪酬(元)")
    private BigDecimal oldSalary;

    /** 调薪后薪酬(元) */
    @Excel(name = "调薪后薪酬(元)")
    private BigDecimal newSalary;

    /** 调动原因 */
    @Excel(name = "调动原因")
    private String reason;

    /** 是否执行 1.不执行  2.待执行 3.已执行 4.废弃 */
    private String isExecute;

    @JSONField(serialize = false, deserialize = false)
    private List<Long> fileIds;

    private String sysName;

    private Long userId;

    private List<PersonnelFile> files;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("archivesId", getArchivesId())
                .append("archivesName", getArchivesName())
                .append("archivesCode", getArchivesCode())
                .append("outDept", getOutDept())
                .append("inDept", getInDept())
                .append("outPost", getOutPost())
                .append("inPost", getInPost())
                .append("outLeader", getOutLeader())
                .append("inLeader", getInLeader())
                .append("effectivedate", getEffectivedate())
                .append("isSalary", getIsSalary())
                .append("oldSalary", getOldSalary())
                .append("newSalary", getNewSalary())
                .append("reason", getReason())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
