package org.ruoyi.core.cwproject.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind;
import org.ruoyi.core.cwproject.domain.TopNotify;

import java.util.Date;
import java.util.List;

/**
 * 通知待办信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
public interface TopNotifyMapper
{
    /**
     * 查询通知待办信息
     *
     * @param id 通知待办信息主键
     * @return 通知待办信息
     */
    public TopNotify selectTopNotifyById(Long id);

    /**
     * 查询通知待办信息列表
     *
     * @param topNotify 通知待办信息
     * @return 通知待办信息集合
     */
    public List<TopNotify> selectTopNotifyList(@Param("topNotify") TopNotify topNotify,@Param("userName") String userName);

    List<TopNotify> selectTopNotifyList1(TopNotify topNotify);

    /**
     * 新增通知待办信息
     *
     * @param topNotify 通知待办信息
     * @return 结果
     */
    public int insertTopNotify(TopNotify topNotify);

    /**
     * 修改通知待办信息
     *
     * @param topNotify 通知待办信息
     * @return 结果
     */
    public int updateTopNotify(TopNotify topNotify);
    int updateTopNotifyIOA(TopNotify topNotify);
    /**
     * 删除通知待办信息
     *
     * @param id 通知待办信息主键
     * @return 结果
     */
    public int deleteTopNotifyById(Long id);

    /**
     * 批量删除通知待办信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTopNotifyByIds(Long[] ids);

    int updateByproductId(@Param("productId") Long productId,@Param("notifyModel") String notifyModel);


    void updateByInmAndProAndStat(@Param("projectId") Long projectId, @Param("inComeId") Long inComeId, @Param("phaseStatus") String phaseStatus, @Param("updateDate") Date updateDate, @Param("updateName")String updateName);

    void updateStatus(@Param("id") Long id, @Param("status") String status);

    //法催项目 - 通过期次id和期次状态删除代办表
    int deleteTopNotifyByByIncomeIdAndPhaseStatus(@Param("phaseId") Long phaseId, @Param("phaseStatus") String phaseStatus);

    //输入项目id、期次id、期次状态 修改为禁用状态
    int updateTopNotifyByProjectIdAndIncomeIdAndPhaseStatus(@Param("projectId") Long projectId, @Param("incomeId") Long incomeId, @Param("phaseStatus") String phaseStatus);


    List<TopNotify> selectTopNotifyListAndRemind(@Param("topNotify") TopNotify topNotify,@Param("date") List<NoaWorkflowRemind> date);
    //通过待办通知表id集合，把待办事项转换为通知事项
    int updateTopNotifyTypeAndViewFlagByIds(@Param("ids") List<Long> ids, @Param("notifyType") String notifyType, @Param("viewFlag") String viewFlag, @Param("status") String status);

    //根据OA通知相关的项目类型和相关OA申请id删除通知
    int deleteTopNotifyByOaNotifyTypeAndOaApplyId(@Param("oaNotifyType") String oaNotifyType, @Param("oaApplyId") Long oaApplyId);
    //根据流程id查询代办通知信息
    TopNotify selectTopNotifyInfoByProcessId(TopNotify topNotify);

    int updateCuiShenByIds(@Param("ids") List<String> ids, @Param("notifyType") String notifyType, @Param("viewFlag") String viewFlag);
}
