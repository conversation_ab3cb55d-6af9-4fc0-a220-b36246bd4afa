package org.ruoyi.core.cwproject.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.cwproject.domain.CwProjectAck;
import org.ruoyi.core.cwproject.domain.CwProjectIncome;
import org.ruoyi.core.cwproject.domain.dto.CwProjectIncomeForLawDto;
import org.ruoyi.core.cwproject.domain.dto.CwProjectPhaseDto;
import org.ruoyi.core.cwproject.domain.view.CwProjectLawDetailView;
import org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo;
import org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-收入Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface CwProjectIncomeMapper 
{
    /**
     * 查询财务项目管理-收入
     * 
     * @param id 财务项目管理-收入主键
     * @return 财务项目管理-收入
     */
    public CwProjectIncome selectCwProjectIncomeById(Long id);

    /**
     * 查询财务项目管理-收入列表
     * 
     * @param cwProjectIncome 财务项目管理-收入
     * @return 财务项目管理-收入集合
     */
    public List<CwProjectIncome> selectCwProjectIncomeList(CwProjectIncome cwProjectIncome);

    /**
     * 新增财务项目管理-收入
     * 
     * @param cwProjectIncome 财务项目管理-收入
     * @return 结果
     */
    public int insertCwProjectIncome(CwProjectIncome cwProjectIncome);

    /**
     * 修改财务项目管理-收入
     * 
     * @param cwProjectIncome 财务项目管理-收入
     * @return 结果
     */
    public int updateCwProjectIncome(CwProjectIncome cwProjectIncome);

    /**
     * 删除财务项目管理-收入
     * 
     * @param id 财务项目管理-收入主键
     * @return 结果
     */
    public int deleteCwProjectIncomeById(Long id);

    /**
     * 批量删除财务项目管理-收入
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCwProjectIncomeByIds(Long[] ids);

    /**
     * 查询财务项目管理-待录入收入状态
     *
     * @return 财务项目管理-收入集合
     */
    List<Map<String, Object>> selectCwProjectIncomeListFlagZero(Long projectId);

    /**
     * 查询财务项目管理-已收录未确认收入状态
     *
     * @return 财务项目管理-收入集合
     */
    List<Map<String, Object>> selectCwProjectIncomeListFlagOne(Long projectId);

    CwProjectIncome selectCwProjectIncomeListFlagTwo(@Param("projectId")Long id, @Param("startDate")String startDate, @Param("endDate")String endDate, @Param("sumFlag")Integer sumFlag);

    CwProjectIncome selectCwProjectFeeDetailsByProjectId(Long projectId);

    CwProjectIncome selectCwProjectIncomeListById(Long id);

    List<CwProjectIncome> selectcwprojectincomeListByProjectId(@Param("projectId") Long projectId, @Param("sumFlag") Integer sumFlag);

    List<Map<String, Object>> selectCwProjectIncomeListFlagOneByAdmin();

    List<Map<String, Object>> selectCwProjectIncomeListFlagZeroByAdmin();

    List<CwProjectIncome> selectcwprojectincomeListAllByProjectId(Long projectId);

    List<CwProjectIncome> selectCwProjectIncomeListAll();


    CwProjectAck selectCwProjectIncomeByProjectId(Long projectId);

    // List<CwProjectIncome> selectCwProjectIncomeTermByProjectId(Long projectId);
    //
    // List<CwProjectIncome> selectCwProjectIncomeAllTermByProjectId(Long projectId);

    CwProjectIncome selectCwProjectIncomeByIdAndTime(@Param("id")Long id, @Param("startDate")String startDate, @Param("endDate")String endDate, @Param("sumFlag")Integer sumFlag);

    List<CwProjectIncome> selectCwProjectIncomeAllInfoByProjectId(Long projectId);

    CwProjectIncome selectCwProjectIncomeTerm2ById(@Param("id")Long id, @Param("startDate")String startDate, @Param("endDate")String endDate);

    CwProjectIncome selectCwProjectIncomeTerm3ById(@Param("id")Long id, @Param("startDate")String startDate, @Param("endDate")String endDate);

    Map<String, Object> selectCwProjectIncomeDiffMonthById(@Param("id")Long id);

    List<Map<String, Object>> selectCwProjectIncomeDiffMonthListByTermEndAndMonthDiff(@Param("termEnd")String termEnd, @Param("monthDiff")int monthDiff);

    int selectCwProjectIncomePhaseNoFinishCountById(@Param("id")Long id);

    List<CwProjectIncome> selectCwProject(@Param("projectId") Long projectId, @Param("term") String term, @Param("termMonth") String termMonth);

    //通过项目id来找到法催项目的期次
//    CwProjectIncome selectPhaseLawByProjectId(Long projectId);

    //插入法催项目
    Long insertLawIncome(CwProjectIncomeForLawDto dto);

    //法催项目 - 期次信息
    List<CwProjectPhaseDto> selectLawProjectPhaseInfo(Long projectId);

    //法催项目 - 通过项目id查所有的期次，以及期次下的收入、返费、打款信息
    List<CwProjectLawDetailView> selectLawCwProjectOverListDetileByProjectIdV2(Long id);

    //法催项目 - 通过期次id查所有的期次，以及期次下的收入、返费、打款信息
    List<CwProjectLawDetailView> selectLawCwProjectOverListDetileByPhaseIdList(@Param("phaseIdList") List<Long> phaseIdList);

    //通过收入id来找到法催项目的期次
    CwProjectIncome selectPhaseLawByIncomeId(Long id);

    //通过法催项目id查找收入表下的期次以及收入
    List<CwProjectIncome> selectcwprojectincomeLawListAllByProjectId(Long id);

    //法催项目，查询期次未完结
    int selectCwProjectLawIncomePhaseNoFinishCountById(Long id);

    //根据最近的期次id查找出返费和返费公司
    Map<String, Object> findRecentlyCustFeeCompanyAndFeeCompany1(@Param("phaseId") Long phaseId);

    //查找期次最近一个月的出返费公司和返费公司 - 业务期次类型为非整月
//    Map<String, Object> findRecentlyCustFeeCompanyAndFeeCompany2(@Param("phaseId") Long phaseId, @Param("date") String date);

    //通过项目id和输入的月份时间查最近的一个期次日期 - 整月
    Map<String, Object> findLawRecentlyPhaseIdByProjectIdAndTremMonth(@Param("projectId") Long projectId, @Param("date") String date);

    //通过项目id和输入的月份时间查最近的一个期次日期 - 非整月
    Map<String, Object> findLawRecentlyPhaseIdByProjectIdAndTremMonthTermOne(@Param("projectId") Long projectId, @Param("date") String date);

    //通过服务商名称，查询挂起的钱
    List<Map<String, Object>> selectLawSuspendFlagIsOneSumByServiceProviderName(List<String> serviceProviderNameList);

    //法催项目 - 录入收入与返费添加至录入收入列表 - 超管
    List<Map<String, Object>> selectLawCwProjectIncomeListFlagSevenByAdmin();

    //法催项目 - 录入收入与返费添加至录入收入列表 - 普通用户
    List<Map<String, Object>> selectLawCwProjectIncomeListFlagSeven(Long id);

    //法催项目 - 确认收入与返费添加至录入收入列表 - 超管
    List<Map<String, Object>> selectLawCwProjectIncomeListFlagEightByAdmin();

    //法催项目 - 确认收入与返费添加至录入收入列表 - 普通用户
    List<Map<String, Object>> selectLawCwProjectIncomeListFlagEight(Long id);

    //法催项目 - 通过期次id查该期次下的收入信息
    List<CwProjectIncome> selectLawIncomeByPhaseId(Long phaseId);

    //法催项目 - 批量修改收入
    int updateLawCwProjectIncome(List<CwProjectIncome> incomeList);

    //法催项目 - 通过返费id去查挂起清除总金额
    BigDecimal selectSuspendAmtByFeeId(Long projectFeeId);

    //法催项目 - 通过期次id找返费的id集合
    List<Long> selectCwprojectFeeIdListByPhase(Long phaseId);

    //法催项目 - 通过期次id删除收入和返费
    int deleteCwProjectIncomeByPhaseId(Long phaseId);

    int deleteCwProjectFeeByFeeIds(List<Long> feeIds);

    //普通项目 - 查所有的返费未结清期次
    List<CwProjectFeeNoAlreadyVo> selectFeeNoAlreadyQueryDetailByQueryTime(@Param("queryTime") String queryTime, @Param("projectType") String projectType);

    //法催项目 - 查所有的返费未结清期次
    List<CwProjectFeeNoAlreadyVo> selectLawFeeNoAlreadyQueryDetailByQueryTime(String queryTime);

    //普通项目 - 查用户参与的返费未结清期次
    List<CwProjectFeeNoAlreadyVo> selectFeeNoAlreadyQueryDetailByQueryTimeAndUserId(@Param("queryTime") String queryTime, @Param("userId") Long userId, @Param("projectType") String projectType);

    //法催项目 - 查用户参与的返费未结清期次
    List<CwProjectFeeNoAlreadyVo> selectLawFeeNoAlreadyQueryDetailByQueryTimeAndUserId(@Param("queryTime") String queryTime, @Param("userId") Long userId);

    //普通项目 - 按照打款时间范围查所有的期次
    List<CwProjectPayDateVo> selectPayDateQueryDetailByQueryTime(@Param("queryStartTime") String queryStartTime, @Param("queryEndTime") String queryEndTime, @Param("projectType") String projectType);

    //法催项目 - 按照打款时间范围查所有的期次
    List<CwProjectPayDateVo> selectLawPayDateQueryDetailByQueryTime(@Param("queryStartTime") String queryStartTime, @Param("queryEndTime") String queryEndTime);

    //普通项目 - 查用户参与的打款时间范围的期次
    List<CwProjectPayDateVo> selectPayDateQueryDetailByQueryTimeAndUserId(@Param("queryStartTime") String queryStartTime, @Param("queryEndTime") String queryEndTime, @Param("userId") Long userId, @Param("projectType") String projectType);

    //法催项目 - 查用户参与的打款时间范围的期次
    List<CwProjectPayDateVo> selectLawPayDateQueryDetailByQueryTimeAndUserId(@Param("queryStartTime") String queryStartTime, @Param("queryEndTime") String queryEndTime, @Param("userId") Long userId);

    //普通项目 - 按照收款时间查询范围查所有的期次
    List<CwProjectPayDateVo> selectCollectionTimeQueryDetailByQueryTime(@Param("queryStartTime") String queryStartTime, @Param("queryEndTime") String queryEndTime, @Param("projectType") String projectType, @Param("projectIds") List<Long> projectIds);

    //法催项目 - 按照收款时间查询范围查所有的期次
    List<CwProjectPayDateVo> selectLawCollectionTimeQueryDetailByQueryTime(@Param("queryStartTime") String queryStartTime, @Param("queryEndTime") String queryEndTime, @Param("projectIds") List<Long> projectIds);

    //普通项目 - 查用户参与的收款时间范围的期次
    List<CwProjectPayDateVo> selectCollectionTimeQueryDetailByQueryTimeAndUserId(@Param("queryStartTime") String queryStartTime, @Param("queryEndTime") String queryEndTime, @Param("userId") Long userId, @Param("projectType") String projectType);

    //法催项目 - 查用户参与的收款时间范围的期次
    List<CwProjectPayDateVo> selectLawCollectionTimeQueryDetailByQueryTimeAndUserId(@Param("queryStartTime") String queryStartTime, @Param("queryEndTime") String queryEndTime, @Param("userId") Long userId);

    //普通项目 - 根据输入的备注查期次
    List<CwProjectFeeNoAlreadyVo> selectRemarkQueryDetailByQueryRemark(@Param("remark") String remark, @Param("projectIds") List<Long> projectIds);

    //法催项目 - 根据输入的备注查期次
    List<CwProjectFeeNoAlreadyVo> selectLawRemarkQueryDetailByQueryRemark(@Param("remark") String remark, @Param("projectIds") List<Long> projectIds);

    //普通项目 - 根据输入的备注查用户参与的期次
    List<CwProjectFeeNoAlreadyVo> selectRemarkQueryDetailByQueryRemarkAndUserId(@Param("remark") String remark, @Param("userId") Long userId);

    //法催项目 - 根据输入的备注查用户参与的期次
    List<CwProjectFeeNoAlreadyVo> selectLawRemarkQueryDetailByQueryRemarkAndUserId(@Param("remark") String remark, @Param("userId") Long userId);

    //普通项目 - 查这个期次是否存在（用于所有期次的累加）
    int selectCwProjectIncomeAllPhaseCountById(Long id);

    //法催项目 - 查这个期次是否存在（用于所有期次的累加）
    int selectCwProjectLawIncomeAllPhaseCountById(Long id);

    //完结项目归档，通过项目id查所有期次
    int selectAllPhaseByProjectId(Long projectId);

    //财务项目管理四期，通过期次id，查找是具体哪个期次
    String selectCwprojectIncomePhaseByIncomeId(Long incomeId);

    //完结项目归档列表查询，找列表的每一条信息，该信息里的phaseStatus为未完成期次（特殊处理）
    CwProjectIncome selectCwProjectLawIncomeListFlagTwo(@Param("projectId")Long id, @Param("startDate")String startDate, @Param("endDate")String endDate, @Param("sumFlag")Integer sumFlag);

    //完结项目归档，通过项目id查所有法催期次
    int selectAllLawPhaseByProjectId(Long id);

    //找到所有挂起返费挂起的信息
    List<Map<String, Object>> selectAllSuspendAmtInfo();

    //修改已清除返费的标识和返费id
    int updateLawCwProjectFeeSuspendSumByFeeIds(List<Long> feeIds);

    //返费未结清查询 - 法催 - 通过期次id查该期次所有的打款信息
    List<Map<String, Object>> selectCwprojectLawPayInfoByPhaseId(Long phaseId);

    //通过项目id查法催项目的利润是否大于5万
    CwProjectAck selectLawCwProjectIncomeByProjectId(Long projectId);

    List<Long> selectCwprojectIncomeLawPhaseListByProjectIdAndCustIds(@Param("projectId") Long projectId, @Param("custIdList") List<Long> custIdList);

    List<Long> selectCwprojectIncomeListByProjectIdAndCustIds(@Param("projectId") Long projectId, @Param("custIdList") List<Long> custIdList);

    //2024.06.12 新权限财务项目管理未录入金额的查询
    List<Map<String, Object>> selectCwProjectIncomeListNotEnterByOaProjectDeployId(@Param("cwProjectIncome") CwProjectIncome cwProjectIncome, @Param("projectIds") List<Long> projectIds);

    //2024.06.12 新权限财务项目管理未确认金额的查询
    List<Map<String, Object>> selectCwProjectIncomeListNotVerifyByOaProjectDeployId(@Param("cwProjectIncome") CwProjectIncome cwProjectIncome, @Param("projectIds") List<Long> projectIds);

    //根据用户有权限的项目，查找对应的期次
    List<Map<String, Object>> queryIncomeIdByOaProjectIdListAndFlag(@Param("projectIds") List<Long> projectIds, @Param("flag") String flag);

    //重构财务项目管理  待录入金额
    List<Map<String, Object>> queryIncomePhaseStatusToBeRecorded(@Param("cwProjectIncome") CwProjectIncome cwProjectIncome, @Param("incomeIdList") List<Long> incomeId);

    //重构财务项目管理  待确认金额
    List<Map<String, Object>> queryIncomePhaseStatusToBeConfirmed(@Param("cwProjectIncome") CwProjectIncome cwProjectIncome, @Param("incomeIdList") List<Long> phaseId);

    //查询期次的信息
    CwProjectIncome selectCwProjectIncome(CwProjectIncome cwProjectIncome);

    //通过income表部分数据（项目id、期次相关信息），找期次id
//    List<CwProjectIncome> selectCwProjectIncomeListByQueryParam(@Param("projectId") Long projectId, @Param("termMonthList") List<String> termMonthList, @Param("termBeginList") List<Date> termBeginList, @Param("termEndList") List<Date> termEndList);
}
