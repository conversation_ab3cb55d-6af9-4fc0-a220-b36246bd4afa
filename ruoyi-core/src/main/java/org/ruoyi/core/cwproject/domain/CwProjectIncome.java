package org.ruoyi.core.cwproject.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 财务项目管理-收入对象 cw_project_income
 * 
 * <AUTHOR>
 * @date 2022-11-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CwProjectIncome extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目管理表主键 */
    @Excel(name = "项目管理表主键")
    private Long projectId;

    /** 业务期次类型 0整月 1非整月 */
    @Excel(name = "业务期次类型 0整月 1非整月")
    private String term;

    /** 业务期次所属月份 */
    @Excel(name = "业务期次所属月份")
    private String termMonth;

    /** 业务期次所属开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务期次所属开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date termBegin;

    /** 业务期次所属结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务期次所属结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date termEnd;

    /** 收入金额 元 */
    @Excel(name = "收入金额 元")
    private BigDecimal incomeAmt;

    /** 毛利金额 元 */
    @Excel(name = "毛利金额 元")
    private BigDecimal grossProfitAmt;

    /** 提成毛利金额 元 */
    @Excel(name = "提成毛利金额 元")
    private BigDecimal grossProfitAmt2;

    /** 返费已结清金额 元 */
    @Excel(name = "返费已结清金额 元")
    private BigDecimal feeAmt;

    /** 返费未结清金额 元 */
    @Excel(name = "返费未结清金额 元")
    private BigDecimal unfeeAmt;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    /** 录入状态，0未录入 1已录入 2已确认 */
    @Excel(name = "录入状态，0未录入 1已录入 2已确认")
    private String incomeFlag;

    /** 期次状态：a1已完成;a2未完成;a3待录入;a4待出纳打款 */
    @Excel(name = "期次状态：a1已完成;a2未完成;a3待录入;a4待出纳打款")
    private String phaseStatus;

    /** 进行期次的时间排序 */
    private String termMonthCompare;

    /** 期次标识 */
    private String phaseFlag;

    private Long phaseId;

    private String rejectionFlag;

    @JsonFormat(pattern = "yyyy年MM月dd日")
    @Excel(name = "普通项目 - 收入驳回时间", width = 30, dateFormat = "yyyy年MM月dd日")
    private Date incomeRejectionTime;

    @Excel(name = "普通项目 - 收入驳回人")
    private String incomeRejectionUser;

    @Excel(name = "普通项目 - 收入驳回原因")
    private String incomeRejectionReason;

    @JsonFormat(pattern = "yyyy年MM月dd日")
    @Excel(name = "普通项目 - 返费驳回时间", width = 30, dateFormat = "yyyy年MM月dd日")
    private Date feeRejectionTime;

    @Excel(name = "普通项目 - 返费驳回人")
    private String feeRejectionUser;

    @Excel(name = "普通项目 - 返费驳回原因")
    private String feeRejectionReason;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收款时间", width = 30, dateFormat = "yyyy年MM月dd日")
    private Date collectionTime;

    private Date collectionTimeBefore;

    //驳回收入后修改时所使用的标识
    private String rejectionIncomeAndChangeIncome;

    private BigDecimal trueComeAmt;

    private BigDecimal serviceFee;
}
