package org.ruoyi.core.cwproject.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.cwproject.domain.CwProject;
import org.ruoyi.core.cwproject.domain.projectVO.AddParojectVo;
import org.ruoyi.core.cwproject.domain.projectVO.CwProjectDetail;
import org.ruoyi.core.cwproject.domain.projectVO.CwProjectShowVo;
import org.ruoyi.core.cwproject.domain.projectVO.ProjectDataParticularsVo;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface ICwProjectShowService {
  List<CwProjectShowVo> selectCwProjectShowList(CwProjectShowVo cwProjectShowVo);

//  int insertCwProjectShow(CwProjectShowDao cwProjectShowDao);

  List<ProjectDataParticularsVo>  selectprjectShowById(Long id);

  List<CwProjectDetail> listDetail(CwProjectDetail cwProjectDetail);

  List<CwProjectShowVo> selectCwProjectShowListAll();

  List<Map<String,Object>> listKuaiji();

  List<Map<String,Object>> listchuna();

  List<Map<String,Object>> listYewu();

  int addFeeIncomePay(ProjectDataParticularsVo projectDataParticularsVo);

//  int updateProjectShow(CwProjectShowDao cwProjectShowDao);

//  int updatePsalesman(List<Long> userId,Long projectId);

//  List<CwProjectShowVo> selectCwProjectList(CwProjectShowVo cwProjectShowVo);

  String selectprjectShowflagAll(Long id);

  List<Map<String,Object>> getCustNameXiaLa();

  List<Map<String,Object>> getIncomeCustNameXiaLa();

  /**
   * 新增项目
   * @param addParojectVo
   */
  Long addProject(AddParojectVo addParojectVo,String userName);

  void closeProject(Long id);

    void updateProject(AddParojectVo addParojectVo, String username);

    Map getUserListName(CwProject cwProject);

  List<Map<String, Object>> getFeeCustLixt();

    List<SysUser> listAllUser();

  Map<String, Object> getRoleSelectList(LoginUser loginUser);

    String batchExcelZip(LoginUser loginUser) throws IOException;

  Map<String, Object> checkLoginRole(LoginUser loginUser,CwProject cwProject);

  Map<String, Object> checkExportProNum(LoginUser loginUser);

  List<Map<String, Object>> getServiceProvider();

  List<Map<String, Object>> getServiceProviderSecond();

  //获取出返费公司下拉框
  List<Map<String, Object>> getCustFeeListForLaw();

  //获取担保公司公司下拉框 - 取字典表数据
  List<Map<String, Object>> getCustNameXiaLaDanBao();


//  String selectprjectShowflagAll();
}
