package org.ruoyi.core.cwproject.export;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ExportExcelCwProjectPayDateQuery {

	
	
	public static void exportCwProject(HttpServletResponse response, Map<String, List<CwProjectPayDateVo>> resultMap, String projectType) throws IOException{
		List<CwProjectPayDateVo> list = null;
		List<CwProjectPayDateVo> lawList = null;
		if (resultMap.containsKey("list")) {
			list = resultMap.get("list");
		}
		if (resultMap.containsKey("lawList")) {
			lawList = resultMap.get("lawList");
		}
		String sheet1Name="打款时间";

		
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        int colSize1 = 9;//列数
 
        
        /**
         * 第一个工作表
         */
		//todo 先判断普通项目的集合是多少
		int indexForOne = 0;
		if (list != null) {
			//这里要多加一个1，因为类型多占了一行
			indexForOne = list.size() + 1;
		}
        
        //创建Sheet（工作簿）
        SXSSFSheet sheet1 = workbook.createSheet(sheet1Name);
		if (list != null) {
			//todo 先给类型，再给标题
			//给标题(第一行)
			SXSSFRow rowType = sheet1.createRow(0);
			SXSSFCell cell = rowType.createCell(0);
			if ("all".equals(projectType)) {
				cell.setCellValue("通道业务、分润业务：");
			} else if ("0".equals(projectType)) {
				cell.setCellValue("通道业务：");
			} else if ("2".equals(projectType)) {
				cell.setCellValue("分润业务：");
			}
			cell.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
		}
		SXSSFRow sheetTitleRow = null;
		if (list != null) {
			//创建主标题行(第二行)
			sheetTitleRow = sheet1.createRow(1);
		} else {
			//创建主标题行(第二行)
			sheetTitleRow = sheet1.createRow(0);
		}
//        //创建主标题行(第一行)
//        SXSSFRow sheetTitleRow = sheet1.createRow(0);
		//合并用
//		CTWorksheet ctWorksheet;
		CellRangeAddress cellAddresses = new CellRangeAddress(0, 0, 0, 0);
		if (list != null) {
			for (int i = 0; i < colSize1; i++) {
				SXSSFCell headCell = sheetTitleRow.createCell(i);
				switch (i) {
					case 0:
						headCell.setCellValue("项目名称");
						break;
					case 1:
						headCell.setCellValue("业务期次");
						break;
					case 2:
						headCell.setCellValue("收入");
						break;
					case 3:
						headCell.setCellValue("收款时间");
						break;
					case 4:
						headCell.setCellValue("出信息费公司");
						break;
					case 5:
						headCell.setCellValue("信息费公司");
						break;
					case 6:
						headCell.setCellValue("实付信息费");
						break;
					case 7:
						headCell.setCellValue("毛利");
						break;
//					case 8:
//						headCell.setCellValue("信息费已结清");
//						break;
//					case 9:
//						headCell.setCellValue("信息费未结清");
//						break;
//					case 10:
//						headCell.setCellValue("打款日期");
//						break;
//					case 11:
//						headCell.setCellValue("实际打款金额");
//						break;
//					case 12:
//						headCell.setCellValue("抹平差额");
//						break;
					case 8:
						headCell.setCellValue("备注");
						break;
					default:
						break;
				}
				headCell.setCellStyle(getTitleFont(sheet1.getWorkbook()));//设置样式
			}

			//先查找合并的projectId
			Map<Long, Long> hebingProject = list.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId, Collectors.counting()));
			//合并期次
			Map<Long, Long> hebingPhase = list.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getPhaseId, Collectors.counting()));
			//合并信息费
			Map<Long, Long> hebingFee = list.stream().filter(t -> t.getProjectFeeId() != null).collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectFeeId, Collectors.counting()));

			//遍历表头名称，创建表头单元格
			for (int i = 0; i < list.size(); i++) {
//			ctWorksheet = sheet1.getWorkbook().getXSSFWorkbook().getSheetAt(0).getCTWorksheet();
				//todo 创建数据行（第三行）第一行是类型，第二行是标题，第三行是数据开始
				SXSSFRow sheetHeadRowIncome = sheet1.createRow(1 + i + 1);
				//项目名称
				SXSSFCell cell1 = sheetHeadRowIncome.createCell(0);
				cell1.setCellValue(list.get(i).getProjectName());
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//业务期次
				cell1 = sheetHeadRowIncome.createCell(1);
				cell1.setCellValue(list.get(i).getTerm());
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//收入
				cell1 = sheetHeadRowIncome.createCell(2);
				if (list.get(i).getIncomeAmt() != null) {
					cell1.setCellValue(list.get(i).getIncomeAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//收款时间
				cell1 = sheetHeadRowIncome.createCell(3);
				if (list.get(i).getCollectionTime() != null) {
					cell1.setCellValue(list.get(i).getCollectionTime() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//出信息费公司
				cell1 = sheetHeadRowIncome.createCell(4);
				if (list.get(i).getCustName() != null) {
					cell1.setCellValue(list.get(i).getCustName() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//信息费公司
				cell1 = sheetHeadRowIncome.createCell(5);
				if (list.get(i).getFeeCustName() != null) {
					cell1.setCellValue(list.get(i).getFeeCustName() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//实付信息费
				cell1 = sheetHeadRowIncome.createCell(6);
				if (list.get(i).getActuallyPayFeeAmt() != null) {
					cell1.setCellValue(list.get(i).getActuallyPayFeeAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//毛利
				cell1 = sheetHeadRowIncome.createCell(7);
				if (list.get(i).getGrossProfitAmt() != null) {
					cell1.setCellValue(list.get(i).getGrossProfitAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//信息费已结清
//				cell1 = sheetHeadRowIncome.createCell(8);
//				if (list.get(i).getFeeAmtSum() != null) {
//					cell1.setCellValue(list.get(i).getFeeAmtSum() + "");
//				} else {
//					cell1.setCellValue("");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//信息费未结清
//				cell1 = sheetHeadRowIncome.createCell(9);
//				if (list.get(i).getUnfeeAmtSum() != null) {
//					cell1.setCellValue(list.get(i).getUnfeeAmtSum() + "");
//				} else {
//					cell1.setCellValue("");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//打款日期
//				cell1 = sheetHeadRowIncome.createCell(10);
//				if (list.get(i).getPayDate() != null) {
//					cell1.setCellValue(list.get(i).getPayDate() + "");
//				} else {
//					cell1.setCellValue("-");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//实际打款金额
//				cell1 = sheetHeadRowIncome.createCell(11);
//				if (list.get(i).getPayAmt() != null) {
//					cell1.setCellValue(list.get(i).getPayAmt() + "");
//				} else {
//					cell1.setCellValue("");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//抹平差额
//				cell1 = sheetHeadRowIncome.createCell(12);
//				if (list.get(i).getDifferenceAmt() != null) {
//					cell1.setCellValue(list.get(i).getDifferenceAmt() + "");
//				} else {
//					cell1.setCellValue("");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//备注
				cell1 = sheetHeadRowIncome.createCell(8);
				if (list.get(i).getRemark() != null) {
					cell1.setCellValue(list.get(i).getRemark() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
			}
			//合并项目
			for (Map.Entry<Long, Long> m : hebingProject.entrySet()) {
				if (m.getValue() != 1L) {
					CwProjectPayDateVo cwProjectPayDateVo = list.stream().filter(t -> t.getProjectId().equals(m.getKey())).findFirst().get();
					int startIndex = list.indexOf(cwProjectPayDateVo) + 1;
					int endIndex = startIndex + Integer.parseInt(String.valueOf(m.getValue()));
					//找到了开始合并的其实索引
					cellAddresses.setFirstRow((1 + startIndex));
					cellAddresses.setLastRow(endIndex);
					cellAddresses.setFirstColumn(0);
					cellAddresses.setLastColumn(0);
					sheet1.addMergedRegion(cellAddresses);
//					addMergedReigon(ctWorksheet, cellAddresses);
				}
			}
			//合并未结清的期次
			for (Map.Entry<Long, Long> m : hebingPhase.entrySet()) {
				if (m.getValue() != 1L) {
					CwProjectPayDateVo cwProjectPayDateVo = list.stream().filter(t -> t.getPhaseId().equals(m.getKey())).findFirst().get();
					int startIndex = list.indexOf(cwProjectPayDateVo) + 1;
					int endIndex = startIndex + Integer.parseInt(String.valueOf(m.getValue()));
					//找到了开始合并的其实索引
					cellAddresses.setFirstRow((1 + startIndex));
					cellAddresses.setLastRow(endIndex);
					cellAddresses.setFirstColumn(1);
					cellAddresses.setLastColumn(1);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(2);
					cellAddresses.setLastColumn(2);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(3);
					cellAddresses.setLastColumn(3);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(7);
					cellAddresses.setLastColumn(7);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(8);
					cellAddresses.setLastColumn(8);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(9);
					cellAddresses.setLastColumn(9);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(13);
					cellAddresses.setLastColumn(13);
					sheet1.addMergedRegion(cellAddresses);
//					addMergedReigon(ctWorksheet, cellAddresses);
				}
			}
			//合并信息费
			for (Map.Entry<Long, Long> m : hebingFee.entrySet()) {
				if (m.getValue() != 1L) {
					CwProjectPayDateVo cwProjectPayDateVo = list.stream().filter(t -> t.getProjectFeeId().equals(m.getKey())).findFirst().get();
					int startIndex = list.indexOf(cwProjectPayDateVo) + 1;
					int endIndex = startIndex + Integer.parseInt(String.valueOf(m.getValue()));
					//找到了开始合并的其实索引
					cellAddresses.setFirstRow((1 + startIndex));
					cellAddresses.setLastRow(endIndex);
					cellAddresses.setFirstColumn(4);
					cellAddresses.setLastColumn(4);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(5);
					cellAddresses.setLastColumn(5);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(6);
					cellAddresses.setLastColumn(6);
					sheet1.addMergedRegion(cellAddresses);
//					cellAddresses.setFirstColumn(10);
//					cellAddresses.setLastColumn(10);
//					sheet1.addMergedRegion(cellAddresses);
//					cellAddresses.setFirstColumn(11);
//					cellAddresses.setLastColumn(11);
//					sheet1.addMergedRegion(cellAddresses);
//					cellAddresses.setFirstColumn(12);
//					cellAddresses.setLastColumn(12);
//					sheet1.addMergedRegion(cellAddresses);
//					addMergedReigon(ctWorksheet, cellAddresses);
				}
			}
		}

		//todo 上面搞定了普通项目，开始搞法催项目
		if (lawList != null) {
			//todo 先给类型，再给标题
			int indexForTwo = 0;
			if (list != null) {
				//这里要再多加一个，因为要献给类型，类型在下面代码给了
				indexForTwo = 1 + indexForOne + 1 + 1;
				sheetTitleRow = sheet1.createRow(indexForTwo);
			} else {
				indexForTwo = 1 + indexForOne;
				sheetTitleRow = sheet1.createRow(indexForTwo);
			}
			//给类型
			if ("all".equals(projectType) || "1".equals(projectType)) {
				SXSSFRow rowType = null;
				if (list != null) {
					rowType = sheet1.createRow(1 + indexForOne + 1);
				} else {
					rowType = sheet1.createRow(indexForOne);
				}
				SXSSFCell cell = rowType.createCell(0);
				cell.setCellValue("法催业务：");
				cell.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
			}


			//先查找合并的projectId
			Map<Long, Long> hebingProject = lawList.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId, Collectors.counting()));
			//合并期次
			Map<Long, Long> hebingPhase = lawList.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getPhaseId, Collectors.counting()));
			//合并信息费
			Map<Long, Long> hebingFee = lawList.stream().filter(t -> t.getProjectFeeId() != null).collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectFeeId, Collectors.counting()));

			//做表头
			for(int i = 0 ; i < colSize1 ; i++) {
				SXSSFCell headCell = sheetTitleRow.createCell(i);
				switch (i) {
					case 0: headCell.setCellValue("项目名称"); break;
					case 1: headCell.setCellValue("业务期次"); break;
					case 2: headCell.setCellValue("法催收入"); break;
					case 3: headCell.setCellValue("收款时间"); break;
					case 4: headCell.setCellValue("出信息费公司"); break;
					case 5: headCell.setCellValue("信息费公司"); break;
					case 6: headCell.setCellValue("信息费取整"); break;
					case 7: headCell.setCellValue("法催利润"); break;
//					case 8: headCell.setCellValue("信息费已结清"); break;
//					case 9: headCell.setCellValue("信息费未结清"); break;
//					case 10: headCell.setCellValue("打款日期"); break;
//					case 11: headCell.setCellValue("实际打款金额"); break;
//					case 12: headCell.setCellValue("抹平差额"); break;
					case 8: headCell.setCellValue("备注"); break;
					default: break;
				}
				headCell.setCellStyle(getTitleFont(sheet1.getWorkbook()));//设置样式
			}
			//遍历表头名称，创建表头单元格
			for (int i = 0; i < lawList.size(); i++) {
//			ctWorksheet = sheet1.getWorkbook().getXSSFWorkbook().getSheetAt(0).getCTWorksheet();
				//创建数据行（第二行）
				SXSSFRow sheetHeadRowIncome = sheet1.createRow(1 + i + indexForTwo);
				//项目名称
				SXSSFCell cell1 = sheetHeadRowIncome.createCell(0);
				cell1.setCellValue(lawList.get(i).getProjectName());
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//业务期次
				cell1 = sheetHeadRowIncome.createCell(1);
				cell1.setCellValue(lawList.get(i).getTerm());
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//法催收入
				cell1 = sheetHeadRowIncome.createCell(2);
				if (lawList.get(i).getIncomeAmt() != null) {
					cell1.setCellValue(lawList.get(i).getIncomeAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//收款时间
				cell1 = sheetHeadRowIncome.createCell(3);
				if (lawList.get(i).getCollectionTime() != null) {
					cell1.setCellValue(lawList.get(i).getCollectionTime() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//出信息费公司
				cell1 = sheetHeadRowIncome.createCell(4);
				if (lawList.get(i).getCustName() != null) {
					cell1.setCellValue(lawList.get(i).getCustName() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//信息费公司
				cell1 = sheetHeadRowIncome.createCell(5);
				if (lawList.get(i).getFeeCustName() != null) {
					cell1.setCellValue(lawList.get(i).getFeeCustName() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//信息费取整
				cell1 = sheetHeadRowIncome.createCell(6);
				if (lawList.get(i).getFeeRound() != null) {
					cell1.setCellValue(lawList.get(i).getFeeRound() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//法催利润
				cell1 = sheetHeadRowIncome.createCell(7);
				if (lawList.get(i).getLawProfit() != null) {
					cell1.setCellValue(lawList.get(i).getLawProfit() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//信息费已结清
//				cell1 = sheetHeadRowIncome.createCell(8);
//				if (lawList.get(i).getFeeAmtSum() != null) {
//					cell1.setCellValue(lawList.get(i).getFeeAmtSum() + "");
//				} else {
//					cell1.setCellValue("");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//信息费未结清
//				cell1 = sheetHeadRowIncome.createCell(9);
//				if (lawList.get(i).getUnfeeAmtSum() != null) {
//					cell1.setCellValue(lawList.get(i).getUnfeeAmtSum() + "");
//				} else {
//					cell1.setCellValue("");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//打款日期
//				cell1 = sheetHeadRowIncome.createCell(10);
//				if (lawList.get(i).getPayDate() != null) {
//					cell1.setCellValue(lawList.get(i).getPayDate() + "");
//				} else {
//					cell1.setCellValue("-");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//实际打款金额
//				cell1 = sheetHeadRowIncome.createCell(11);
//				if (lawList.get(i).getPayAmt() != null) {
//					cell1.setCellValue(lawList.get(i).getPayAmt() + "");
//				} else {
//					cell1.setCellValue("");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
//				//抹平差额
//				cell1 = sheetHeadRowIncome.createCell(12);
//				if (lawList.get(i).getDifferenceAmt() != null) {
//					cell1.setCellValue(lawList.get(i).getDifferenceAmt() + "");
//				} else {
//					cell1.setCellValue("");
//				}
//				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
				//备注
				cell1 = sheetHeadRowIncome.createCell(8);
				if (lawList.get(i).getRemark() != null) {
					cell1.setCellValue(lawList.get(i).getRemark() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));
			}

			//合并项目
			for (Map.Entry<Long, Long> m : hebingProject.entrySet()) {
				if (m.getValue() != 1L) {
					CwProjectPayDateVo cwProjectPayDateVo = lawList.stream().filter(t -> t.getProjectId().equals(m.getKey())).findFirst().get();
					int startIndex = lawList.indexOf(cwProjectPayDateVo);
					int endIndex = startIndex + Integer.parseInt(String.valueOf(m.getValue()));
					//找到了开始合并的其实索引
					cellAddresses.setFirstRow((1 + startIndex + indexForTwo));
					cellAddresses.setLastRow(endIndex + indexForTwo);
					cellAddresses.setFirstColumn(0);
					cellAddresses.setLastColumn(0);
					sheet1.addMergedRegion(cellAddresses);
//					addMergedReigon(ctWorksheet, cellAddresses);
				}
			}
			//合并未结清的期次
			for (Map.Entry<Long, Long> m : hebingPhase.entrySet()) {
				if (m.getValue() != 1L) {
					CwProjectPayDateVo cwProjectPayDateVo = lawList.stream().filter(t -> t.getPhaseId().equals(m.getKey())).findFirst().get();
					int startIndex = lawList.indexOf(cwProjectPayDateVo);
					int endIndex = startIndex + Integer.parseInt(String.valueOf(m.getValue()));
					//找到了开始合并的其实索引
					cellAddresses.setFirstRow((1 + startIndex + indexForTwo));
					cellAddresses.setLastRow(endIndex + indexForTwo);
					cellAddresses.setFirstColumn(1);
					cellAddresses.setLastColumn(1);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(2);
					cellAddresses.setLastColumn(2);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(3);
					cellAddresses.setLastColumn(3);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(4);
					cellAddresses.setLastColumn(4);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(5);
					cellAddresses.setLastColumn(5);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(6);
					cellAddresses.setLastColumn(6);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(7);
					cellAddresses.setLastColumn(7);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(8);
					cellAddresses.setLastColumn(8);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(9);
					cellAddresses.setLastColumn(9);
					sheet1.addMergedRegion(cellAddresses);
					cellAddresses.setFirstColumn(13);
					cellAddresses.setLastColumn(13);
					sheet1.addMergedRegion(cellAddresses);
//					addMergedReigon(ctWorksheet, cellAddresses);
				}
			}
//			//合并信息费
//			for (Map.Entry<Long, Long> m : hebingFee.entrySet()) {
//				if (m.getValue() != 1L) {
//					CwProjectPayDateVo cwProjectPayDateVo = lawList.stream().filter(t -> t.getProjectFeeId().equals(m.getKey())).findFirst().get();
//					int startIndex = lawList.indexOf(cwProjectPayDateVo);
//					int endIndex = startIndex + Integer.parseInt(String.valueOf(m.getValue()));
//					//找到了开始合并的其实索引
//					cellAddresses.setFirstRow((1 + startIndex + indexForTwo));
//					cellAddresses.setLastRow(endIndex + indexForTwo);
////					cellAddresses.setFirstColumn(3);
////					cellAddresses.setLastColumn(3);
////					sheet1.addMergedRegion(cellAddresses);
//					cellAddresses.setFirstColumn(4);
//					cellAddresses.setLastColumn(4);
//					sheet1.addMergedRegion(cellAddresses);
//					cellAddresses.setFirstColumn(5);
//					cellAddresses.setLastColumn(5);
//					sheet1.addMergedRegion(cellAddresses);
//					cellAddresses.setFirstColumn(9);
//					cellAddresses.setLastColumn(9);
//					sheet1.addMergedRegion(cellAddresses);
//					cellAddresses.setFirstColumn(10);
//					cellAddresses.setLastColumn(10);
//					sheet1.addMergedRegion(cellAddresses);
//					cellAddresses.setFirstColumn(11);
//					cellAddresses.setLastColumn(11);
//					sheet1.addMergedRegion(cellAddresses);
////					addMergedReigon(ctWorksheet, cellAddresses);
//				}
//			}
		}


        // 自动调整列宽
        sheet1.trackAllColumnsForAutoSizing();
        for (int j = 0; j < colSize1; j++) {
        	sheet1.autoSizeColumn(j,true);
			if (sheet1.getColumnWidth(j) * 12 / 10 > 65280) {
				sheet1.setColumnWidth(j, 9000);
			} else {
				sheet1.setColumnWidth(j, sheet1.getColumnWidth(j) * 12 / 10);
			}
		}
        sheet1.setColumnWidth(3, 5000);


        //生成完成，输出下载
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {

			workbook.write(response.getOutputStream());
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
            if (workbook != null) {
                try {
                	workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (response.getOutputStream() != null) {
                try {
                    response.getOutputStream().close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        
    }

	
	//设置单元格合并样式
	
	public static void setBorder(CellRangeAddress a,SXSSFSheet sheet) {
        RegionUtil.setBorderTop(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, a, sheet);
	}
	
	 //标题样式
    public static CellStyle getHeaderFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 16);//字体大小
        font.setBold(true);//加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }
 
    //表头样式
    public static CellStyle getTitleFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 13);//字体大小
        font.setBold(true);//加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }
 
    //内容样式
    public static CellStyle getDataFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);//字体大小
        font.setBold(false);//不加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setWrapText(true);
        return cellStyle;
    }
 
    //处理数据
    public static String getValue(Object object){
        if (object==null){
            return "";
        }else {
            return object.toString();
        }
    }

//	/**
//	 *  合并单元格
//	 */
//	private static void addMergedReigon(CTWorksheet sheetX, CellRangeAddress cellRangeAddress) {
//		CTMergeCells ctMergeCells;
//		if (sheetX.isSetMergeCells()) {
//			ctMergeCells = sheetX.getMergeCells();
//		} else {
//			ctMergeCells = sheetX.addNewMergeCells();
//		}
//
//		CTMergeCell ctMergeCell = ctMergeCells.addNewMergeCell();
//		ctMergeCell.setRef(cellRangeAddress.formatAsString());
//	}
}
