package org.ruoyi.core.cwproject.export;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.ruoyi.core.cwproject.domain.export.ExcelCwProject;
import org.ruoyi.core.cwproject.domain.export.ExcelCwProjectCust;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class ExportExcelCwProject {

	
	
	public static void exportCwProject(HttpServletResponse response, ExcelCwProject proj ) throws IOException{
		String sheet1Name = StringUtils.EMPTY;
		String sheet2Name = StringUtils.EMPTY;
//		String sheet3Name = StringUtils.EMPTY;
//		if ("1".equals(proj.getPrestoreIncomeFlag())) {
//			sheet1Name="信息费明细";
//			sheet2Name="预存收入表";
//			sheet3Name="信息费公司及费率";
//		} else {
		sheet1Name="信息费明细";
		sheet2Name="信息费公司及费率";
//		}

		
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        int colSize1 = 14;//列数
 
        
        /**
         * 第一个工作表
         */
        
        //创建Sheet（工作簿）
        SXSSFSheet sheet1 = workbook.createSheet(sheet1Name);
        //创建主标题行(第一行)
        SXSSFRow sheetTitleRow = sheet1.createRow(0);
        for(int i = 0 ; i < colSize1 ; i++){
        	SXSSFCell headCell = sheetTitleRow.createCell(i);
            switch (i) {
            case 0: headCell.setCellValue("业务期次"); break;
            case 1: headCell.setCellValue("担保公司"); break;
            case 2: headCell.setCellValue("汇款公司"); break;
            case 3: headCell.setCellValue("收入"); break;
            case 4: headCell.setCellValue("收款时间"); break;
            case 5: headCell.setCellValue("出信息费公司"); break;
            case 6: headCell.setCellValue("信息费公司"); break;
            case 7: headCell.setCellValue("应付信息费"); break;
            case 8: headCell.setCellValue("实付信息费"); break;
            case 9: headCell.setCellValue("提成信息费"); break;
            case 10: headCell.setCellValue("毛利"); break;
            case 11: headCell.setCellValue("提成毛利"); break;
//            case 12: headCell.setCellValue("信息费已结清"); break;
//            case 13: headCell.setCellValue("信息费未结清"); break;
            case 12: headCell.setCellValue("期次状态"); break;
//            case 15: headCell.setCellValue("打款状态"); break;
//            case 16: headCell.setCellValue("打款日期"); break;
//            case 17: headCell.setCellValue("实际打款金额"); break;
//            case 18: headCell.setCellValue("抹平差额"); break;
            case 13: headCell.setCellValue("备注"); break;
            default: break;
            }
            headCell.setCellStyle(getTitleFont(sheet1.getWorkbook()));//设置样式
        }
        int addcounts=0;
        //收入合并起始行
        int incomBeginRow=1;
        //信息费合并起始行
        int feeBeginRow=1;
        
        //遍历表头名称，创建表头单元格
        for(int i = 0 ; i < proj.getIncomeList().size() ; i++){
            //创建数据行（第二行）
        	SXSSFRow sheetHeadRowIncome = sheet1.createRow(1+addcounts);
            //业务期次
        	SXSSFCell cell1 = sheetHeadRowIncome.createCell(0);
            if(proj.getIncomeList().get(i).getTerm().equals("0")) {
            	cell1.setCellValue(DateUtils.parseDateToStr("yyyy年MM月", DateUtils.parseDate(proj.getIncomeList().get(i).getTermMonth())));
            }else {
            	cell1.setCellValue(DateUtils.parseDateToStr("yyyy.MM.dd",proj.getIncomeList().get(i).getTermBegin())+"-"+DateUtils.parseDateToStr("yyyy.MM.dd",proj.getIncomeList().get(i).getTermEnd()));
            }
            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
            //担保公司
            cell1 = sheetHeadRowIncome.createCell(1);
            cell1.setCellValue(proj.getCustName());
            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
            //汇款公司
            cell1 = sheetHeadRowIncome.createCell(2);
            cell1.setCellValue(proj.getIncomeCustName());
            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
            //收入
            cell1 = sheetHeadRowIncome.createCell(3);
			if (Optional.ofNullable(proj.getIncomeList().get(i).getIncomeAmt()).isPresent()) {
				cell1.setCellValue(proj.getIncomeList().get(i).getIncomeAmt() + "");
			}
			cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
			//收款时间
			cell1 = sheetHeadRowIncome.createCell(4);
			if (Optional.ofNullable(proj.getIncomeList().get(i).getCollectionTime()).isPresent()) {
				cell1.setCellValue(DateUtils.parseDateToStr("yyyy年MM月dd日", DateUtils.parseDate(proj.getIncomeList().get(i).getCollectionTime() + "")));
			} else {
				cell1.setCellValue("");
			}
			cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
			//期次状态
			cell1 = sheetHeadRowIncome.createCell(12);
			switch (proj.getIncomeList().get(i).getPhaseStatus()) {
				case "0": cell1.setCellValue("待录入金额"); break;
				case "1": cell1.setCellValue("待确认金额"); break;
				case "2": cell1.setCellValue("已完成"); break;
				default: break;
			}
			if ("2".equals(proj.getIncomeList().get(i).getPhaseStatus()) && "1".equals(proj.getIncomeList().get(i).getRejectionFlag())){
				cell1.setCellValue("业务驳回");
			}
//			if ("4".equals(proj.getIncomeList().get(i).getPhaseStatus()) && "2".equals(proj.getIncomeList().get(i).getRejectionFlag())){
//				cell1.setCellValue("信息费金额驳回");
//			}
            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
            for (int j = 0; j < proj.getIncomeList().get(i).getFeeList().size(); j++) {
				if(j==0) {
		            //出信息费公司
		            cell1 = sheetHeadRowIncome.createCell(5);
		            cell1.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getCustName());
		            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
		            //信息费公司
		            cell1 = sheetHeadRowIncome.createCell(6);
		            cell1.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getFeeCustName());
		            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
		            //应付信息费
		            cell1 = sheetHeadRowIncome.createCell(7);
					if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getShouldPayFeeAmt()).isPresent()) {
						cell1.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getShouldPayFeeAmt() + "");
					} else {
						cell1.setCellValue("");
					}
		            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
					//实付信息费
					cell1 = sheetHeadRowIncome.createCell(8);
					if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getActuallyPayFeeAmt()).isPresent()) {
						cell1.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getActuallyPayFeeAmt() + "");
					} else {
						cell1.setCellValue("");
					}
					cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
		            //提成信息费
		            cell1 = sheetHeadRowIncome.createCell(9);
					if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getFeeAmt2()).isPresent()) {
						cell1.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getFeeAmt2() + "");
					} else {
						cell1.setCellValue("");
					}
		            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
                    //毛利
    	            cell1 = sheetHeadRowIncome.createCell(10);
					if (Optional.ofNullable(proj.getIncomeList().get(i).getGrossProfitAmt()).isPresent()) {
						cell1.setCellValue(proj.getIncomeList().get(i).getGrossProfitAmt() + "");
					} else {
						cell1.setCellValue("");
					}
    	            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
                    //提成毛利
    	            cell1 = sheetHeadRowIncome.createCell(11);
					if (Optional.ofNullable(proj.getIncomeList().get(i).getGrossProfitAmt2()).isPresent()) {
						cell1.setCellValue(proj.getIncomeList().get(i).getGrossProfitAmt2() + "");
					} else {
						cell1.setCellValue("");
					}
    	            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//    	            //信息费已结清
//    	            cell1 = sheetHeadRowIncome.createCell(12);
//					if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeAmt()).isPresent()) {
//						cell1.setCellValue(proj.getIncomeList().get(i).getFeeAmt() + "");
//					} else {
//						cell1.setCellValue("-");
//					}
//    	            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//    	            //信息费未结清
//    	            cell1 = sheetHeadRowIncome.createCell(13);
//					if (Optional.ofNullable(proj.getIncomeList().get(i).getUnfeeAmt()).isPresent()) {
//						cell1.setCellValue(proj.getIncomeList().get(i).getUnfeeAmt() + "");
//					} else {
//						cell1.setCellValue("-");
//					}
//    	            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//    	            //期次状态
//    	            cell1 = sheetHeadRowIncome.createCell(12);
//    	            switch (proj.getIncomeList().get(i).getPhaseStatus()) {
//	    	            case "0": cell1.setCellValue("待录入收入"); break;
//	    	            case "1": cell1.setCellValue("待确认收入"); break;
//	    	            case "2": cell1.setCellValue("待录入信息费"); break;
//	    	            case "3": cell1.setCellValue("待确认信息费"); break;
//	    	            case "4": cell1.setCellValue("待出纳打款"); break;
//	    	            case "5": cell1.setCellValue("部分打款已完成"); break;
//	    	            case "6": cell1.setCellValue("已完成"); break;
//	    	            default: break;
//    	            }
//    	            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式


//    	            for (int j2 = 0; j2 < proj.getIncomeList().get(i).getFeeList().get(j).getPayList().size(); j2++) {
//	            		if(j==0 && j2==0) {
//	                        //打款状态
//	        	            cell1 = sheetHeadRowIncome.createCell(15);
//	        	            switch (proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayFlag()) {
//		        	            case "0": cell1.setCellValue("未打款"); break;
//		        	            case "1": cell1.setCellValue("已打款"); break;
//		        	            case "2": cell1.setCellValue("已确认"); break;
//		        	            default: break;
//	        	            }
//	        	            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //打款日期
//	        	            cell1 = sheetHeadRowIncome.createCell(16);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayDate()).isPresent()) {
//								cell1.setCellValue(DateUtils.parseDateToStr("yyyy.MM.dd", proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayDate()));
//							} else {
//								cell1.setCellValue("");
//							}
//	        	            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //实际打款金额
//	        	            cell1 = sheetHeadRowIncome.createCell(17);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayAmt()).isPresent()) {
//								cell1.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayAmt() + "");
//							} else {
//								cell1.setCellValue("");
//							}
//	        	            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //抹平差额
//	        	            cell1 = sheetHeadRowIncome.createCell(18);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getDifferenceAmt()).isPresent()) {
//								cell1.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getDifferenceAmt() + "");
//							} else {
//								cell1.setCellValue("");
//							}
//	        	            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	    				}else {
//
//	    					SXSSFRow sheetHeadRowPay = sheet1.createRow(2+addcounts);
//	    					//打款状态
//	    					SXSSFCell cell3 = sheetHeadRowPay.createCell(15);
//	        	            switch (proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayFlag()) {
//		        	            case "0": cell3.setCellValue("未打款"); break;
//		        	            case "1": cell3.setCellValue("已打款"); break;
//		        	            case "2": cell3.setCellValue("已确认"); break;
//		        	            default: break;
//	        	            }
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //打款日期
//	        	            cell3 = sheetHeadRowPay.createCell(16);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayDate()).isPresent()) {
//								cell3.setCellValue(DateUtils.parseDateToStr("yyyy.MM.dd", proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayDate()));
//							} else {
//								cell3.setCellValue("");
//							}
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //实际打款金额
//	        	            cell3 = sheetHeadRowPay.createCell(17);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayAmt()).isPresent()) {
//								cell3.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayAmt() + "");
//							} else {
//								cell3.setCellValue("");
//							}
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //抹平差额
//	        	            cell3 = sheetHeadRowPay.createCell(18);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getDifferenceAmt()).isPresent()) {
//								cell3.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getDifferenceAmt() + "");
//							} else {
//								cell3.setCellValue("");
//							}
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	    					addcounts++;
//	    				}
//					}


				}else {
					SXSSFRow sheetHeadRowFee = sheet1.createRow(2+addcounts);
		            //出信息费公司
					SXSSFCell cell2 = sheetHeadRowFee.createCell(5);
					cell2.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getCustName());
					cell2.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
		            //信息费公司
					cell2 = sheetHeadRowFee.createCell(6);
					cell2.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getFeeCustName());
					cell2.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
		            //应付信息费
					cell2 = sheetHeadRowFee.createCell(7);
					if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getShouldPayFeeAmt()).isPresent()) {
						cell2.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getShouldPayFeeAmt() + "");
					} else {
						cell2.setCellValue("");
					}
					cell2.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
					//实付信息费
					cell2 = sheetHeadRowFee.createCell(8);
					if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getActuallyPayFeeAmt()).isPresent()) {
						cell2.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getActuallyPayFeeAmt() + "");
					} else {
						cell2.setCellValue("");
					}
					cell2.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
		            //提成信息费
					cell2 = sheetHeadRowFee.createCell(9);
					if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getFeeAmt2()).isPresent()) {
						cell2.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getFeeAmt2() + "");
					} else {
						cell2.setCellValue("");
					}
					cell2.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//					for (int j2 = 0; j2 < proj.getIncomeList().get(i).getFeeList().get(j).getPayList().size(); j2++) {
//						if(j2==0) {
//							//打款状态
//							SXSSFCell cell3 = sheetHeadRowFee.createCell(15);
//	        	            switch (proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayFlag()) {
//		        	            case "0": cell3.setCellValue("未打款"); break;
//		        	            case "1": cell3.setCellValue("已打款"); break;
//		        	            case "2": cell3.setCellValue("已确认"); break;
//		        	            default: break;
//	        	            }
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //打款日期
//	        	            cell3 = sheetHeadRowFee.createCell(16);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayDate()).isPresent()) {
//								cell3.setCellValue(DateUtils.parseDateToStr("yyyy.MM.dd", proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayDate()));
//							} else {
//								cell3.setCellValue("");
//							}
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //实际打款金额
//	        	            cell3 = sheetHeadRowFee.createCell(17);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayAmt()).isPresent()) {
//								cell3.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayAmt() + "");
//							} else {
//								cell3.setCellValue("");
//							}
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //抹平差额
//	        	            cell3 = sheetHeadRowFee.createCell(18);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getDifferenceAmt()).isPresent()) {
//								cell3.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getDifferenceAmt() + "");
//							} else {
//								cell3.setCellValue("");
//							}
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//						}else {
//							SXSSFRow sheetHeadRowPay = sheet1.createRow(3+addcounts);
//	    					//打款状态
//							SXSSFCell cell3 = sheetHeadRowPay.createCell(15);
//	        	            switch (proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayFlag()) {
//		        	            case "0": cell3.setCellValue("未打款"); break;
//		        	            case "1": cell3.setCellValue("已打款"); break;
//		        	            case "2": cell3.setCellValue("已确认"); break;
//		        	            default: break;
//	        	            }
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //打款日期
//	        	            cell3 = sheetHeadRowPay.createCell(16);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayDate()).isPresent()) {
//								cell3.setCellValue(DateUtils.parseDateToStr("yyyy.MM.dd", proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayDate()));
//							} else {
//								cell3.setCellValue("");
//							}
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //实际打款金额
//	        	            cell3 = sheetHeadRowPay.createCell(17);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayAmt()).isPresent()) {
//								cell3.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getPayAmt() + "");
//							} else {
//								cell3.setCellValue("");
//							}
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//	                        //抹平差额
//	        	            cell3 = sheetHeadRowPay.createCell(18);
//							if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getDifferenceAmt()).isPresent()) {
//								cell3.setCellValue(proj.getIncomeList().get(i).getFeeList().get(j).getPayList().get(j2).getDifferenceAmt() + "");
//							} else {
//								cell3.setCellValue("");
//							}
//	        	            cell3.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
//		    				addcounts++;
//						}
//					}
					addcounts++;
				}
				if(feeBeginRow<(1+addcounts) ) {
    	            sheet1.addMergedRegion(new CellRangeAddress(feeBeginRow, 1+addcounts, 5, 5));
    	            setBorder(new CellRangeAddress(1, 1+addcounts, 5, 5), sheet1);
    	            sheet1.addMergedRegion(new CellRangeAddress(feeBeginRow, 1+addcounts, 6, 6));
    	            setBorder(new CellRangeAddress(1, 1+addcounts, 6, 6), sheet1);
    	            sheet1.addMergedRegion(new CellRangeAddress(feeBeginRow, 1+addcounts, 7, 7));
    	            setBorder(new CellRangeAddress(1, 1+addcounts, 7, 7), sheet1);
    	            sheet1.addMergedRegion(new CellRangeAddress(feeBeginRow, 1+addcounts, 8, 8));
    	            setBorder(new CellRangeAddress(1, 1+addcounts, 8, 8), sheet1);
					sheet1.addMergedRegion(new CellRangeAddress(feeBeginRow, 1+addcounts, 9, 9));
					setBorder(new CellRangeAddress(1, 1+addcounts, 9, 9), sheet1);
				}
	            feeBeginRow=2+addcounts;
			}
            //备注
            cell1 = sheetHeadRowIncome.createCell(13);
			if (Optional.ofNullable(proj.getIncomeList().get(i).getRemark()).isPresent()) {
				cell1.setCellValue(proj.getIncomeList().get(i).getRemark());
			} else {
				cell1.setCellValue("");
			}
            cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
            //合并单元格
            if(incomBeginRow<(1+addcounts)) {
	            sheet1.addMergedRegion(new CellRangeAddress(incomBeginRow, 1+addcounts, 0, 0));
	            setBorder(new CellRangeAddress(1, 1+addcounts, 0, 0), sheet1);
	            sheet1.addMergedRegion(new CellRangeAddress(incomBeginRow, 1+addcounts, 3, 3));
	            setBorder(new CellRangeAddress(1, 1+addcounts, 3, 3), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(incomBeginRow, 1+addcounts, 4, 4));
				setBorder(new CellRangeAddress(1, 1+addcounts, 4, 4), sheet1);
	            sheet1.addMergedRegion(new CellRangeAddress(incomBeginRow, 1+addcounts, 10, 10));
	            setBorder(new CellRangeAddress(1, 1+addcounts, 10, 10), sheet1);
	            sheet1.addMergedRegion(new CellRangeAddress(incomBeginRow, 1+addcounts, 11, 11));
	            setBorder(new CellRangeAddress(1, 1+addcounts, 11, 11), sheet1);
	            sheet1.addMergedRegion(new CellRangeAddress(incomBeginRow, 1+addcounts, 12, 12));
	            setBorder(new CellRangeAddress(1, 1+addcounts, 12, 12), sheet1);
	            sheet1.addMergedRegion(new CellRangeAddress(incomBeginRow, 1+addcounts, 13, 13));
	            setBorder(new CellRangeAddress(1, 1+addcounts, 13, 13), sheet1);
	            sheet1.addMergedRegion(new CellRangeAddress(incomBeginRow, 1+addcounts, 14, 14));
	            setBorder(new CellRangeAddress(1, 1+addcounts, 14, 14), sheet1);
	            sheet1.addMergedRegion(new CellRangeAddress(incomBeginRow, 1+addcounts, 19, 19));
	            setBorder(new CellRangeAddress(1, 1+addcounts, 19, 19), sheet1);
            }
            
            addcounts++;
            incomBeginRow=1+addcounts;
        }
        
        if(1<addcounts) {
	        sheet1.addMergedRegion(new CellRangeAddress(1, addcounts, 1, 1));
	        setBorder(new CellRangeAddress(1, addcounts, 1, 1), sheet1);
	        sheet1.addMergedRegion(new CellRangeAddress(1, addcounts, 2, 2));
	        setBorder(new CellRangeAddress(1, addcounts, 2, 2), sheet1);
        }
        // 自动调整列宽
        sheet1.trackAllColumnsForAutoSizing();
        for (int j = 0; j < colSize1; j++) {
        	sheet1.autoSizeColumn(j,true);
			if (sheet1.getColumnWidth(j) * 12 / 10 > 65280) {
				sheet1.setColumnWidth(j, 65280);
			} else {
				sheet1.setColumnWidth(j, sheet1.getColumnWidth(j) * 12 / 10);
			}
		}
        
        
        
        
        /**
         * 第二个工作表
         */


        //创建Sheet（工作簿）
        SXSSFSheet sheet2 = workbook.createSheet(sheet2Name);


      //创建主标题行(第一行)
        SXSSFRow sheetTitleRow2 = sheet2.createRow(0);
        for(int i = 0 ; i < 5 ; i++) {
        	SXSSFCell headCell = sheetTitleRow2.createCell(i);
            switch (i) {
            case 0: headCell.setCellValue("方案"); break;
            case 1: headCell.setCellValue("信息费公司名称"); break;
            case 2: headCell.setCellValue("费率"); break;
            case 3: headCell.setCellValue("税率"); break;
            case 4: headCell.setCellValue("方案已使用次数"); break;
            default: break;
            }
            headCell.setCellStyle(getTitleFont(sheet2.getWorkbook()));//设置样式
        }
        SXSSFRow sheetHeadRowCust;
        for (int k = 0; k < proj.getCustList().size(); k++) {
        	sheetHeadRowCust = sheet2.createRow(1+k);
        	SXSSFCell cellCust = sheetHeadRowCust.createCell(0);
			//方案
			cellCust.setCellValue(proj.getCustList().get(k).getSchemeFlag());
			cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
            //信息费公司
			cellCust = sheetHeadRowCust.createCell(1);
    		cellCust.setCellValue(proj.getCustList().get(k).getCustName());
    		cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
            //费率
    		cellCust = sheetHeadRowCust.createCell(2);
			if (Optional.ofNullable(proj.getCustList().get(k).getRate()).isPresent()) {
				cellCust.setCellValue(proj.getCustList().get(k).getRate().stripTrailingZeros().toPlainString() + "%");
			} else {
				cellCust.setCellValue("未设置");
			}
    		cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
    		//税率
    		cellCust = sheetHeadRowCust.createCell(3);
			if (Optional.ofNullable(proj.getCustList().get(k).getTaxRate()).isPresent()) {
				cellCust.setCellValue(proj.getCustList().get(k).getTaxRate().stripTrailingZeros().toPlainString() + "%");
			} else {
				cellCust.setCellValue("未设置");
			}
    		cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
			//方案已使用次数
			cellCust = sheetHeadRowCust.createCell(4);
			cellCust.setCellValue(proj.getCustList().get(k).getSchemeFlagUseSituation());
			cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
		}
		//进行单元格的合并操作，要合并的单元格 ---> 方案、防范已使用次数
		Map<String, Long> margeMap = proj.getCustList().stream().collect(Collectors.groupingBy(ExcelCwProjectCust::getSchemeFlag, Collectors.counting()));
		for (Map.Entry<String, Long> m:margeMap.entrySet()) {
			if (m.getValue() != 1L) {
				ExcelCwProjectCust excelCwProjectCust = proj.getCustList().stream().filter(t -> t.getSchemeFlag().equals(m.getKey())).findFirst().get();
				int index = proj.getCustList().indexOf(excelCwProjectCust);
				//找到了第一个元素的索引
				//进行合并
				sheet2.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 0, 0));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 0, 0), sheet2);
				sheet2.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 4, 4));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 4, 4), sheet2);
			}
		}
		//财务项目管理四期，新增项目成员展示
		int memberBeginIndex = proj.getCustList().size() + 2;
		//创建主标题行(第一行)
		sheetTitleRow2 = sheet2.createRow(memberBeginIndex);
		for(int i = 0 ; i < 2 ; i++){
			SXSSFCell headCell = sheetTitleRow2.createCell(i);
			switch (i) {
				case 0: headCell.setCellValue("角色"); break;
				case 1: headCell.setCellValue("用户姓名"); break;
				default: break;
			}
			headCell.setCellStyle(getTitleFont(sheet2.getWorkbook()));//设置样式
		}
		//然后把项目成员给列出来
		for (int k = 0; k < proj.getMemberList().size(); k++) {
			sheetHeadRowCust = sheet2.createRow(memberBeginIndex + 1 + k);
			SXSSFCell cellCust = sheetHeadRowCust.createCell(0);
			//角色
			cellCust.setCellValue(proj.getMemberList().get(k).getRole());
			cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
			//用户姓名
			cellCust = sheetHeadRowCust.createCell(1);
			cellCust.setCellValue(proj.getMemberList().get(k).getNickName());
			cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
		}

		// 自动调整列宽
        sheet2.trackAllColumnsForAutoSizing();
        for (int j = 0; j < 5; j++) {
        	sheet2.autoSizeColumn(j);
        	sheet2.setColumnWidth(j, sheet2.getColumnWidth(j) * 12 / 10);
		}
		sheet2.setColumnWidth(0, 3000);
		sheet2.setColumnWidth(4, 6000);
//		sheet2.setDefaultRowHeightInPoints(60);


        
        
        //生成完成，输出下载
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {

			workbook.write(response.getOutputStream());
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
            if (workbook != null) {
                try {
                	workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (response.getOutputStream() != null) {
                try {
                    response.getOutputStream().close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        
    }

	
	//设置单元格合并样式
	
	public static void setBorder(CellRangeAddress a,SXSSFSheet sheet) {
        RegionUtil.setBorderTop(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, a, sheet);
	}
	
	 //标题样式
    public static CellStyle getHeaderFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 16);//字体大小
        font.setBold(true);//加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }
 
    //表头样式
    public static CellStyle getTitleFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 13);//字体大小
        font.setBold(true);//加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }
 
    //内容样式
    public static CellStyle getDataFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);//字体大小
        font.setBold(false);//不加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setWrapText(true);
        return cellStyle;
    }
 
    //处理数据
    public static String getValue(Object object){
        if (object==null){
            return "";
        }else {
            return object.toString();
        }
    }
    
    
    
    
    
    
    
    
    
    
    
    
    
    

}
