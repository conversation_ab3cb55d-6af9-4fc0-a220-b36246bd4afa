package org.ruoyi.core.cwproject.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.cwproject.domain.CwProjectPay;
import org.ruoyi.core.cwproject.service.ICwProjectPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-打款信息Controller
 * 
 * <AUTHOR>
 * @date 2022-11-10
 */
@RestController
@RequestMapping("/cwxmgl/pay")
public class CwProjectPayController extends BaseController
{
    @Autowired
    private ICwProjectPayService cwProjectPayService;

    /**
     * 查询财务项目管理-打款信息列表
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:pay:list')")
    @GetMapping("/list")
    public TableDataInfo list(CwProjectPay cwProjectPay)
    {
        startPage();
        List<CwProjectPay> list = cwProjectPayService.selectCwProjectPayList(cwProjectPay);
        return getDataTable(list);
    }

    /**
     * 导出财务项目管理-打款信息列表
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:pay:export')")
    @Log(title = "财务项目管理-打款信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CwProjectPay cwProjectPay)
    {
        List<CwProjectPay> list = cwProjectPayService.selectCwProjectPayList(cwProjectPay);
        ExcelUtil<CwProjectPay> util = new ExcelUtil<CwProjectPay>(CwProjectPay.class);
        util.exportExcel(response, list, "财务项目管理-打款信息数据");
    }

    /**
     * 获取财务项目管理-打款信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:pay:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cwProjectPayService.selectCwProjectPayById(id));
    }

    /**
     * 新增财务项目管理-打款信息
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:pay:add')")
    @Log(title = "财务项目管理-打款信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CwProjectPay cwProjectPay)
    {
        return toAjax(cwProjectPayService.insertCwProjectPay(cwProjectPay));
    }

    /**
     * 修改财务项目管理-打款信息
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:pay:edit')")
    @Log(title = "财务项目管理-打款信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CwProjectPay cwProjectPay)
    {
        return toAjax(cwProjectPayService.updateCwProjectPay(cwProjectPay));
    }

    /**
     * 删除财务项目管理-打款信息
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:pay:remove')")
    @Log(title = "财务项目管理-打款信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cwProjectPayService.deleteCwProjectPayByIds(ids));
    }

    /**
     * 查询财务项目管理-待出纳打款状态
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:pay:list')")
    @GetMapping("/list/flagzero")
    public TableDataInfo listFlagZero(CwProjectPay cwProjectPay)
    {
        LoginUser loginUser = getLoginUser();
        Long userId = loginUser.getUserId();
        startPage();
        List<Map<String, Object>> data = cwProjectPayService.selectCwProjectPayListFlagZero(cwProjectPay, userId, loginUser);
        return getPageDataTable(data);
    }
}
