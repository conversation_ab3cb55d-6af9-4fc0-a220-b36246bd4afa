package org.ruoyi.core.cwproject.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.cwproject.domain.TopNotify;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ITopNotifyService.java
 * @Description TODO
 * @createTime 2022年11月08日 17:16:00
 */
public interface ITopNotifyService {
    /**
     * 查询通知待办信息
     *
     * @param id 通知待办信息主键
     * @return 通知待办信息
     */
    public TopNotify selectTopNotifyById(Long id);

    /**
     * 查询通知待办信息列表
     *
     * @param topNotify 通知待办信息
     * @return 通知待办信息
     */
    public List<TopNotify> selectTopNotifyList(TopNotify topNotify, LoginUser loginUser);
    public List<TopNotify> selectTopNotifyListNotUser(TopNotify topNotify);

    /**
     * 查询通知待办信息列表
     *
     * @param topNotify 通知待办信息
     * @return 通知待办信息
     */
    public List<TopNotify> selectAccompLishList(TopNotify topNotify,LoginUser loginUser);
    /**
     * 新增通知待办信息
     *
     * @param topNotify 通知待办信息
     * @return 结果
     */
    public int insertTopNotify(TopNotify topNotify);


    /**
     * 修改通知待办信息
     *
     * @param topNotify 通知待办信息
     * @return 结果
     */
    public int updateTopNotify(TopNotify topNotify);

    /**
     * 批量删除通知待办信息
     *
     * @param ids 需要删除的通知待办信息主键
     * @return 结果
     */
    public int deleteTopNotifyByIds(Long[] ids);
    /**
     * 删除通知待办信息信息
     *
     * @param id 通知待办信息主键
     * @return 结果
     */
    public int deleteTopNotifyById(Long id);

    /**
     * 更新代办状态产品id
     *
     * @param productId 产品id
     * @return int
     */
    public int updateByProductId(Long productId,String notifyModel);

    int updateTopNotifyIOA(TopNotify topNotify);

    /**
     * 根据流程id查询代办通知信息
     * @param topNotify
     * @return
     */
    TopNotify selectTopNotifyInfoByProcessId(TopNotify topNotify);


    void updateCuiShenStatus(TopNotify topNotify);
}
