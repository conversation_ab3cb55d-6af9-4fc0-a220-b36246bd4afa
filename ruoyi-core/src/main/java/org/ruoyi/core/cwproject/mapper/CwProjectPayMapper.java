package org.ruoyi.core.cwproject.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.cwproject.domain.CwProjectPay;

import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-打款信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-10
 */
public interface CwProjectPayMapper 
{
    /**
     * 查询财务项目管理-打款信息
     * 
     * @param id 财务项目管理-打款信息主键
     * @return 财务项目管理-打款信息
     */
    public CwProjectPay selectCwProjectPayById(Long id);

    /**
     * 查询财务项目管理-打款信息列表
     * 
     * @param cwProjectPay 财务项目管理-打款信息
     * @return 财务项目管理-打款信息集合
     */
    public List<CwProjectPay> selectCwProjectPayList(CwProjectPay cwProjectPay);

    /**
     * 新增财务项目管理-打款信息
     * 
     * @param cwProjectPay 财务项目管理-打款信息
     * @return 结果
     */
    public int insertCwProjectPay(CwProjectPay cwProjectPay);

    /**
     * 修改财务项目管理-打款信息
     * 
     * @param cwProjectPay 财务项目管理-打款信息
     * @return 结果
     */
    public int updateCwProjectPay(CwProjectPay cwProjectPay);

    /**
     * 删除财务项目管理-打款信息
     * 
     * @param id 财务项目管理-打款信息主键
     * @return 结果
     */
    public int deleteCwProjectPayById(Long id);

    /**
     * 批量删除财务项目管理-打款信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCwProjectPayByIds(Long[] ids);

    /**
     * 查询财务项目管理-待出纳打款状态
     *
     * @return 财务项目管理-打款集合
     */
    List<Map<String, Object>> selectCwProjectPayListFlagZero(Long projectId);

    /**
     * 查询财务项目管理-根据主表id来查找对应的打款记录
     *
     * @return 财务项目管理-打款集合
     */
    // List<Map<String, Object>> selectCwProjectPayListDetail(Long Id);

    /**
     * 查询财务项目管理-根据收入表id来查找对应的打款记录
     *
     * @return 财务项目管理-打款集合
     */
    List<Map<String, Object>>  selectCwProjectPayListDetailByIncomeId(@Param("incomeId") Long incomeId, @Param("custName") String custName);

    List<Map<String, Object>> selectCwProjectPayListDetailByFeeId(Long feeId);

    List<Map<String, Object>> selectCwProjectPayListFlagZeroByAdmin();

    /**
     * 查询财务项目管理-根据返费表id，查所有打款信息
     */
    List<Map<String, Object>> selectCwProjectPayListDetailByFeeId2(Long feeId);

    void deleteByProidAndInc(@Param("projectId") Long projectId, @Param("projectIncomeId") Long projectIncomeId, @Param("projectFeeId") Long projectFeeId);

    //法催项目 - 待出纳打款 - 超管
    List<Map<String, Object>> selectLawCwProjectPayListFlagNineByAdmin();

    //法催项目 - 待出纳打款表 - 普通用户
    List<Map<String, Object>> selectLawCwProjectPayListFlagNine(Long id);
}
