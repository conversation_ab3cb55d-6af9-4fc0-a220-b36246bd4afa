package org.ruoyi.core.cwproject.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 财务项目管理-提成基数确认及返费关联对象 cw_project_ack_ref
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
public class CwProjectAckRef extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 提成基数确认表主键 */
    @Excel(name = "提成基数确认表主键")
    private Long ackId;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    /** 返费表主键 */
    @Excel(name = "返费表主键")
    private Long projectFeeId;

    /** 项目收入表主键 */
    @Excel(name = "项目收入表主键")
    private Long projectIncomeId;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setAckId(Long ackId)
    {
        this.ackId = ackId;
    }

    public Long getAckId()
    {
        return ackId;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setProjectFeeId(Long projectFeeId)
    {
        this.projectFeeId = projectFeeId;
    }

    public Long getProjectFeeId()
    {
        return projectFeeId;
    }
    public void setProjectIncomeId(Long projectIncomeId)
    {
        this.projectIncomeId = projectIncomeId;
    }

    public Long getProjectIncomeId()
    {
        return projectIncomeId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("ackId", getAckId())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("projectFeeId", getProjectFeeId())
                .append("projectIncomeId", getProjectIncomeId())
                .toString();
    }
}
