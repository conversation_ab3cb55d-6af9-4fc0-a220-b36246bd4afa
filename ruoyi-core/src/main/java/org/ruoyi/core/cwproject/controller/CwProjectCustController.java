package org.ruoyi.core.cwproject.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.dto.AuthorizedFeatureDetailDTO;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.ruoyi.core.cwproject.domain.*;
import org.ruoyi.core.cwproject.domain.dto.CwProjectExportDto;
import org.ruoyi.core.cwproject.domain.dto.CwProjectLawFeeDto;
import org.ruoyi.core.cwproject.domain.dto.CwProjectOverDetailCompanyDto;
import org.ruoyi.core.cwproject.domain.export.*;
import org.ruoyi.core.cwproject.domain.view.CwProjectDetailView;
import org.ruoyi.core.cwproject.domain.view.CwProjectLawDetailView;
import org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo;
import org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo;
import org.ruoyi.core.cwproject.export.ExportExcelCwProject;
import org.ruoyi.core.cwproject.export.ExportExcelCwProjectPayDateQuery;
import org.ruoyi.core.cwproject.export.ExportExcelCwProjectQuery;
import org.ruoyi.core.cwproject.export.ExportExcelLawCwProject;
import org.ruoyi.core.cwproject.mapper.CwProjectRejectionMapper;
import org.ruoyi.core.cwproject.mapper.ExportCwProjectMapper;
import org.ruoyi.core.cwproject.service.ICwProjectCustService;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.domain.OaTrader;
import org.ruoyi.core.oasystem.domain.vo.OaPayRebateRecordVo;
import org.ruoyi.core.oasystem.domain.vo.OaTraderVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 财务项目管理-返费公司与费率Controller
 *
 * <AUTHOR>
 * @date 2022-11-10
 */
@RestController
@RequestMapping("/cwxmgl/cust")
public class CwProjectCustController extends BaseController
{
    @Autowired
    private ICwProjectCustService cwProjectCustService;
    @Autowired
    private ExportCwProjectMapper exportCwProjectMapper;
    @Autowired
    private CwProjectRejectionMapper cwProjectRejectionMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityService;

    /**
     * 查询财务项目管理-返费公司与费率列表
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:cust:list')")
    @GetMapping("/list")
    public TableDataInfo list(CwProjectCust cwProjectCust)
    {
        startPage();
        List<CwProjectCust> list = cwProjectCustService.selectCwProjectCustList(cwProjectCust);
        return getDataTable(list);
    }

    /**
     * 查询所有
     */
    @PreAuthorize("@ss.hasPermi('system:cust:list')")
    @PostMapping("/list")
    public TableDataInfo list()
    {
        List<CwProjectCust> list = cwProjectCustService.selectCwProjectCustListAll();
        return getDataTable(list);
    }

    /**
     * 导出财务项目管理-返费公司与费率列表
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:cust:export')")
    @Log(title = "财务项目管理-返费公司与费率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CwProjectCust cwProjectCust)
    {
        List<CwProjectCust> list = cwProjectCustService.selectCwProjectCustList(cwProjectCust);
        ExcelUtil<CwProjectCust> util = new ExcelUtil<CwProjectCust>(CwProjectCust.class);
        util.exportExcel(response, list, "财务项目管理-返费公司与费率数据");
    }

    /**
     * 获取财务项目管理-返费公司与费率详细信息
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:cust:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cwProjectCustService.selectCwProjectCustById(id));
    }

    /**
     * 新增财务项目管理-返费公司与费率
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:cust:add')")
    @Log(title = "财务项目管理-返费公司与费率", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CwProjectCust cwProjectCust)
    {
        return toAjax(cwProjectCustService.insertCwProjectCust(cwProjectCust));
    }

    /**
     * 修改财务项目管理-返费公司与费率
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:cust:edit')")
    @Log(title = "财务项目管理-返费公司与费率", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CwProjectCust cwProjectCust)
    {
        return toAjax(cwProjectCustService.updateCwProjectCust(cwProjectCust));
    }

    /**
     * 删除财务项目管理-返费公司与费率
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:cust:remove')")
    @Log(title = "财务项目管理-返费公司与费率", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cwProjectCustService.deleteCwProjectCustByIds(ids));
    }

    /**
     * 查询财务项目管理-返费公司明细查询-初次进入页面-查询所有返费公司名称
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:list')")
    @GetMapping("/list/first")
    public Map<String, Object> listFirst(CwProject cwProject)
    {
        LoginUser loginUser = getLoginUser();
        List<Long> projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.FINANCEPROJ.getCode());
        Map<String, Object> data = cwProjectCustService.selectCwProjectListFirst(loginUser, projectIds);
        return data;
    }

    /**
     * 查询财务项目管理-返费公司明细查询
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:list')")
    @GetMapping("/list/detail")
    public Map<String, Object> listDetail(CwProjectCust cwProjectCust)
    {
        LoginUser loginUser = getLoginUser();
        List<Long> projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.FINANCEPROJ.getCode());
        Map<String, Object> data = cwProjectCustService.selectCwProjectCustListDetail(cwProjectCust, loginUser, projectIds);
        return data;
//        return getDataTable(data);
    }

    /**
     * 查询财务项目管理-完结项目归档查询-初次进入页面-查询所有已经完结的项目
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:list')")
    @GetMapping("/list/over/first")
    public Map<String, Object> overFirst(CwProject cwProject)
    {
        LoginUser loginUser = getLoginUser();
        Map<String, Object> data = cwProjectCustService.selectCwProjectOverListFirst(loginUser);
        return data;
    }

    /**
     * 查询财务项目管理-列表页面-条件查询
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:list')")
    @GetMapping("/list/overs")
    @SuppressWarnings("unchecked")
    public TableDataInfo overs(CwProject cwProject, String startDate, String endDate, Integer sumFlag)
    {
        LoginUser loginUser = getLoginUser();
        Long userId = loginUser.getUserId();
//        startPage();
        if ("".equals(startDate)) {
            startDate = null;
        }
        if ("".equals(endDate)) {
            endDate = null;
        }
        // List<Map<String, Object>> data = cwProjectCustService.selectCwProjectOverList(cwProject, startDate, endDate, sumFlag);
        List<Long> projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(userId, AuthModuleEnum.FINANCEPROJ.getCode());
        startPage();
        for (Long i = 0L; i < 300; i++) {
            projectIds.add(i);
        }
        Map<String, Object> data = cwProjectCustService.selectCwProjectOversList(cwProject, startDate, endDate, sumFlag, userId, loginUser, projectIds);
        if (2 == data.size()) {
//            startPage();
            return getDataTableSum(data);
        } else {
//            startPage();
            List<Map<String, Object>> list  = (List<Map<String, Object>>) data.get("resp");
            return getPageDataTable(list);
        }
        // return getDataTableSum(data);
    }

    /**
     * 查询财务项目管理-完结项目归档查询-条件查询
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:list')")
    @GetMapping("/list/over")
    @SuppressWarnings("unchecked")
    public TableDataInfo over(CwProject cwProject, String startDate, String endDate, Integer sumFlag)
    {
        LoginUser loginUser = getLoginUser();
        Long userId = loginUser.getUserId();
//        startPage();
        // List<Map<String, Object>> data = cwProjectCustService.selectCwProjectOverList(cwProject, startDate, endDate, sumFlag);
        List<Long> projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(userId, AuthModuleEnum.FINANCEPROJ.getCode());
        startPage();
        Map<String, Object> data = cwProjectCustService.selectCwProjectOverList(cwProject, startDate, endDate, sumFlag, userId, loginUser, projectIds);
        if (2 == data.size()) {
//            startPage();
            return getDataTableSum(data);
        } else {
//            startPage();
            List<Map<String, Object>> list  = (List<Map<String, Object>>) data.get("resp");
            return getPageDataTable(list);
        }
        // return getDataTableSum(data);
    }

    /**
     * 查询财务项目管理-完结项目归档查询详情-条件查询
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:list')")
    @GetMapping("/list/over/detail")
    public Map<String, Object> overDetail(CwProject cwProject, Integer sumFlag)
    {
        Map<String, Object> data = cwProjectCustService.selectCwProjectOverListDetileByProjectId(cwProject, sumFlag);
        List<CwProjectDetailView> projList = exportCwProjectMapper.selectCwProjectOverListDetileByProjectIdV2(cwProject.getId());
        //在这里把之前所有的返费公司进行替换，为啥在这里进行替换。因为重新写一遍逻辑,资源消耗巨大，并且在data中的fee_list中随时包含最新的返费公司
        List<CwProjectOverDetailCompanyDto> feeList = (List<CwProjectOverDetailCompanyDto>) data.get("fee_list");
        //从feeList当中找所有的oaTraderId，不管是新的旧的还是原来的，都找到，查名字（因为替换可能要显示出来）
        List<CwProjectReplaceFeeCompanyInfo> replaceCompanyInfo = (List<CwProjectReplaceFeeCompanyInfo>) data.get("replaceCompanyInfo");
        Set<OaTrader> revealFeeCompanyList = new HashSet<>();
        for (CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo : replaceCompanyInfo) {
            Long oldOaTraderId = cwProjectReplaceFeeCompanyInfo.getOldOaTraderId();
            String oldOaTraderUserName = cwProjectReplaceFeeCompanyInfo.getOldOaTraderUserName();
            OaTrader oldOaTrader = new OaTrader();
            oldOaTrader.setId(oldOaTraderId);
            oldOaTrader.setUserName(oldOaTraderUserName);
            revealFeeCompanyList.add(oldOaTrader);
            Long newOaTraderId = cwProjectReplaceFeeCompanyInfo.getNewOaTraderId();
            String newOaTraderUserName = cwProjectReplaceFeeCompanyInfo.getNewOaTraderUserName();
            OaTrader newOaTrader = new OaTrader();
            newOaTrader.setId(newOaTraderId);
            newOaTrader.setUserName(newOaTraderUserName);
            revealFeeCompanyList.add(newOaTrader);
        }
        for (CwProjectDetailView cpdv:projList) {
            if (cpdv.getRevealFeeCompanyId() == null) {
                Long feeCustId = cpdv.getFeeCustId();
                String feeCustName = StringUtils.EMPTY;
                if (feeCustId != null) {
                    feeCustName = feeList.stream().filter(t -> t.getId().equals(feeCustId)).findFirst().map(CwProjectOverDetailCompanyDto::getCustName).orElse("数据错误，没有找到对应的返费公司");
                    if (feeCustId == -999L) {
                        feeCustName = "暂不确定公司";
                    }
                }
                cpdv.setFeeCustName(feeCustName);
            } else {
                Long feeCustId = cpdv.getRevealFeeCompanyId();
                String feeCustName = StringUtils.EMPTY;
                if (feeCustId != null) {
                    feeCustName = revealFeeCompanyList.stream().filter(t -> t.getId().equals(feeCustId)).findFirst().map(OaTrader::getUserName).orElse("数据错误，没有找到对应的返费公司");
                    if (feeCustId == -999L) {
                        feeCustName = "暂不确定公司";
                    }
                }
                cpdv.setFeeCustName(feeCustName);
            }
        }
        Map<Long, List<CwProjectDetailView>> collect1 = projList.stream().collect(Collectors.groupingBy(CwProjectDetailView::getProjectIncomeId));
        AtomicInteger a = new AtomicInteger();
        collect1.forEach((k,v) -> {
            if (!"已完成".equals(v.get(0).getPhaseStatus())) {
                a.getAndIncrement();
            }
        });
        //对期次进行排序
        List<CwProjectDetailView> collect = projList.stream().sorted(Comparator.comparing(CwProjectDetailView::getTermMonthCompare).reversed()).collect(Collectors.toList());
        data.put("detail_list_v2", collect);
        data.put("phaseNoFinish", a);
        return data;
    }

    /**
     * 法催项目 - 项目详情 （可用于详情页和完结项目归档）
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:list')")
    @GetMapping("/list/over/lawDetail")
    public Map<String, Object> overLawDetail(CwProject cwProject, Integer sumFlag)
    {
        Map<String, Object> data = cwProjectCustService.selectCwProjectLawDetileByProjectId(cwProject, sumFlag);
//        List<CwProjectDetailView> projList = exportCwProjectMapper.selectCwProjectOverListDetileByProjectIdV2(cwProject.getId());
        //对期次进行排序
//        List<CwProjectDetailView> collect = projList.stream().sorted(Comparator.comparing(CwProjectDetailView::getTermMonthCompare).reversed()).collect(Collectors.toList());
//        data.put("detail_list_v2", collect);
        return data;
    }

    @GetMapping("/list/over/detailV2")
    public Map<String, Object> overDetailV2(CwProject cwProject, Integer sumFlag)
    {
    	Map<String, Object> data = new HashMap<String, Object>();
        List<CwProjectDetailView> projList = exportCwProjectMapper.selectCwProjectOverListDetileByProjectIdV2(cwProject.getId());
        data.put("detail_list_v2", projList);
        return data;
    }

    /**
     * 导出财务项目管理-完结项目归档查询详情-导出报表
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:export')")
    @Log(title = "财务项目管理-完结项目归档查询详情", businessType = BusinessType.EXPORT)
    @PostMapping("/over/detail/export")
    public void overDetailExport(HttpServletResponse response, CwProjectExportDto cwProjectExportDto) throws IOException {
//        Map<String, Object> map = cwProjectCustService.overDetailExport(Long.parseLong(cwProjectExportDto.getProjecId())
//                , cwProjectExportDto.getProjectName(), cwProjectExportDto.getCustName(), cwProjectExportDto.getIncomeCustName());
//            // List<CwProjectOverDetailTitleDto> titleList = (List<CwProjectOverDetailTitleDto>) map.get("CwProjectOverDetailTitleDto");
//            List<CwProjectOverDetailDto> list = (List<CwProjectOverDetailDto>) map.get("CwProjectOverDetailDto");
//            List<CwProjectOverDetailCompanyDto> cwProjectCusts = (List<CwProjectOverDetailCompanyDto>) map.get("CwProjectOverDetailFeeDto");
//            // List<CwProjectUserAndRoleDto> peopleList = (List<CwProjectUserAndRoleDto>) map.get("CwProjectUserAndRoleDto");
//            // ExcelExp e1 = new ExcelExp("项目简介", titleList, CwProjectOverDetailTitleDto.class);
//            ExcelExp e2 = new ExcelExp("返费明细表", list, CwProjectOverDetailDto.class);
//            ExcelExp e3 = new ExcelExp("返费公司与费率", cwProjectCusts, CwProjectOverDetailCompanyDto.class);
//            // ExcelExp e4 = new ExcelExp("项目配置信息-项目成员", peopleList, CwProjectUserAndRoleDto.class);
//            List<ExcelExp> mySheet = new ArrayList<>();
//            // mySheet.add(e1);
//            mySheet.add(e2);
//            mySheet.add(e3);
//            // mySheet.add(e4);
//            ExcelUtilManySheet<List<ExcelExp>> util = new ExcelUtilManySheet<>(mySheet);
//            util.exportExcelManySheet(response,mySheet);
//
//
        //新导出方法
    	ExcelCwProject excelCwProject =exportCwProjectMapper.selectExportCwProjectInfo(Long.parseLong(cwProjectExportDto.getProjecId()));
        CwProject cwProject = new CwProject();
        cwProject.setId(Long.parseLong(cwProjectExportDto.getProjecId()));
        Map<String, Object> map2 = cwProjectCustService.selectCwprojectDetailThree(cwProject);
        excelCwProject.setProjectName(map2.get("projectName").toString());
        excelCwProject.setCustName(map2.get("custName").toString());
        excelCwProject.setIncomeCustName(map2.get("incomeCustName").toString());
        Map<String, Object> data = cwProjectCustService.selectCwProjectOverListDetileByProjectId(cwProject, 1);
        List<CwProjectOverDetailCompanyDto> feeList = (List<CwProjectOverDetailCompanyDto>) data.get("fee_list");
    	excelCwProject.setCustList(exportCwProjectMapper.selectExportCwProjectCustList(Long.parseLong(cwProjectExportDto.getProjecId())));
        for (ExcelCwProjectCust ecc:excelCwProject.getCustList()) {
            Long id = ecc.getId();
            String s = feeList.stream().filter(t -> t.getId().equals(id)).findFirst().map(CwProjectOverDetailCompanyDto::getCustName).orElse("数据错误，没有找到对应的返费公司");
            ecc.setCustName(s);
        }
    	excelCwProject.setIncomeList(exportCwProjectMapper.selectExportCwProjectIncomeList(Long.parseLong(cwProjectExportDto.getProjecId())));
    	for (int i = 0; i < excelCwProject.getIncomeList().size(); i++) {
            //找返费，因为返费与返费成员与费率表有联系
            List<ExcelCwProjectFee> excelCwProjectFees = exportCwProjectMapper.selectExportCwProjectFeeList(Long.parseLong(cwProjectExportDto.getProjecId()), excelCwProject.getIncomeList().get(i).getId());
            for (ExcelCwProjectFee ecf:excelCwProjectFees) {
                Long custId = ecf.getCustId();
                String s = feeList.stream().filter(t -> t.getId().equals(custId)).findFirst().map(CwProjectOverDetailCompanyDto::getCustName).orElse("数据错误，没有找到对应的返费公司");
                if (custId == -999L) {
                    s = "暂不确定公司";
                }
                ecf.setFeeCustName(s);
            }
            //如果有返费，那么进行返费方案的统计   PS：财务项目管理四期只要有收入，肯定就有返费，所以以下情况只是为了保证现有项目不报错的问题
            if (excelCwProjectFees.size() != 0) {
                String schemeFlag = excelCwProjectFees.get(0).getSchemeFlag();
                if (schemeFlag == null) {
                    schemeFlag = excelCwProjectFees.get(0).getCustRemark();
                }
                String finalSchemeFlag = schemeFlag;
                List<ExcelCwProjectCust> collect = excelCwProject.getCustList().stream().filter(t -> finalSchemeFlag.equals(t.getSchemeFlag())).collect(Collectors.toList());
                for (ExcelCwProjectCust excelCwProjectCust:collect) {
                    int schemeFlagUseSituation = excelCwProjectCust.getSchemeFlagUseSituation();
                    schemeFlagUseSituation++;
                    excelCwProjectCust.setSchemeFlagUseSituation(schemeFlagUseSituation);
                }
            }
            excelCwProject.getIncomeList().get(i).setFeeList(excelCwProjectFees);
//    		for (int j = 0; j < excelCwProject.getIncomeList().get(i).getFeeList().size(); j++) {
//    			excelCwProject.getIncomeList().get(i).getFeeList().get(j).setPayList(exportCwProjectMapper.selectExportCwProjectPayList(Long.parseLong(cwProjectExportDto.getProjecId()),excelCwProject.getIncomeList().get(i).getId(),excelCwProject.getIncomeList().get(i).getFeeList().get(j).getId()));
//			}
    	}
        //财务项目管理四期，查询本项目所有角色和用户姓名
        List<ExcelCwProjectMember> excelCwProjectMemberList = new ArrayList<>();
//        List<ExcelCwProjectMember> excelCwProjectMemberList = exportCwProjectMapper.selectExportCwProjectMemberInfoByProjectId(Long.parseLong(cwProjectExportDto.getProjecId()));
//        List<ExcelCwProjectMember> projectView = excelCwProjectMemberList.stream().filter(t -> "查看权限".equals(t.getRole())).collect(Collectors.toList());
//        List<ExcelCwProjectMember> projectExprot = excelCwProjectMemberList.stream().filter(t -> "导出权限".equals(t.getRole())).collect(Collectors.toList());
//        if (projectView.size() == 0) {
//            ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
//            excelCwProjectMember.setRole("查看权限");
//            excelCwProjectMember.setNickName(StringUtils.EMPTY);
//            excelCwProjectMemberList.add(excelCwProjectMember);
//        }
//        if (projectExprot.size() == 0) {
//            ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
//            excelCwProjectMember.setRole("导出权限");
//            excelCwProjectMember.setNickName(StringUtils.EMPTY);
//            excelCwProjectMemberList.add(excelCwProjectMember);
//        }
//        excelCwProject.setMemberList(excelCwProjectMemberList);
        Long oaProjectDeployId = (Long) data.get("oaProjectDeployId");
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(oaProjectDeployId, AuthModuleEnum.FINANCEPROJ.getCode());
        //会计
        List<Map<String, Object>> accountantList = userList.stream().filter(a -> "1".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().collect(Collectors.toList());
        if (accountantList.size() == 0) {
            ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
            excelCwProjectMember.setRole("会计");
            excelCwProjectMember.setNickName(StringUtils.EMPTY);
            excelCwProjectMemberList.add(excelCwProjectMember);
        } else {
            String authorizedUserNickName = accountantList.stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
            ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
            excelCwProjectMember.setRole("会计");
            excelCwProjectMember.setNickName(authorizedUserNickName);
            excelCwProjectMemberList.add(excelCwProjectMember);
        }
        //业务
        List<Map<String, Object>> businessList = userList.stream().filter(a -> "3".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().collect(Collectors.toList());
        if (businessList.size() == 0) {
            ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
            excelCwProjectMember.setRole("业务");
            excelCwProjectMember.setNickName(StringUtils.EMPTY);
            excelCwProjectMemberList.add(excelCwProjectMember);
        } else {
            String authorizedUserNickName = businessList.stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
            ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
            excelCwProjectMember.setRole("业务");
            excelCwProjectMember.setNickName(authorizedUserNickName);
            excelCwProjectMemberList.add(excelCwProjectMember);
        }
        //查看权限
        List<Map<String, Object>> selectList = userList.stream().filter(a -> "88".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().collect(Collectors.toList());
        if (selectList.size() == 0) {
            ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
            excelCwProjectMember.setRole("查看权限");
            excelCwProjectMember.setNickName(StringUtils.EMPTY);
            excelCwProjectMemberList.add(excelCwProjectMember);
        } else {
            String authorizedUserNickName = selectList.stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
            ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
            excelCwProjectMember.setRole("查看权限");
            excelCwProjectMember.setNickName(authorizedUserNickName);
            excelCwProjectMemberList.add(excelCwProjectMember);
        }
        excelCwProject.setMemberList(excelCwProjectMemberList);


        //财务项目管理四期，如果有预存收入，那么就给一个预存收入的标识
//        if ("1".equals(excelCwProject.getPrestoreIncomeFlag())) {
//            Map<String, Object> map = cwProjectCustService.selectPrestoreIncomeListByProjectId(Long.parseLong(cwProjectExportDto.getProjecId()));
//            List<CwProjectPrestoreIncome> prestoreIncomeList = (List<CwProjectPrestoreIncome>) map.get("info");
//            excelCwProject.setPrestoreIncomeList(prestoreIncomeList);
//        }
    	ExportExcelCwProject.exportCwProject(response, excelCwProject);

    }

    /**
     * 查询财务项目管理-查询该项目下是否有正在进行的状态
     */
    @GetMapping("/list/conduct")
    public List<Map<String, Object>> conduct(CwProject cwProject)
    {
        return cwProjectCustService.selectCwProjectConduct(cwProject);
    }

    /**
     * 法催项目 - 查询正在进行的期次
     */
    @GetMapping("/list/conductLaw")
    public List<Map<String, Object>> conductLaw(CwProject cwProject)
    {
        return cwProjectCustService.selectCwProjectConductLaw(cwProject);
    }

    /**
     * 查询财务项目管理-修改该项目是否是整月非整月
     */
    @GetMapping("/list/conduct/change")
    public Map<String, Object> conductChange(CwProjectIncome cwProjectIncome)
    {
        return cwProjectCustService.selectCwProjectConductChange(cwProjectIncome);
    }

    /**
     * 查询财务项目管理-根据收入表id，查该期次的进行状态
     */
    @GetMapping("/list/conduct/detail")
    public Map<String, Object> conductDetail(CwProjectIncome cwProjectIncome)
    {
        return cwProjectCustService.selectCwProjectConductDetail(cwProjectIncome);
    }

    /**
     * 法催项目 - 查正在进行中的期次的收入详情
     */
    @GetMapping("/list/conduct/lawDetail")
    public Map<String, Object> conductLawDetail(CwProjectIncome cwProjectIncome)
    {
        return cwProjectCustService.selectCwProjectConductLawDetail(cwProjectIncome);
    }

    /**
     * 查询财务项目管理-根据收入表id，查询返费公司和其总返费
     */
    @GetMapping("/list/fee/detail")
    public List<CwProjectFee> feeDetail(CwProjectIncome cwProjectIncome)
    {
        return cwProjectCustService.selectCwprojectFeeDetail(cwProjectIncome);
    }

    /**
     * 查询财务项目管理-根据返费表list，查对应的所有打款表信息
     */
    @PostMapping("/list/pay/detail")
    public Map<String,Object> payDetail(@RequestBody List<CwProjectFee> cwProjectFeeList)
    {
        return cwProjectCustService.selectCwprojectPayListDetail(cwProjectFeeList);
    }

    /**
     * 查询财务项目管理-根据返费表list，查对应的所有打款表信息
     */
    @PostMapping("/list/pay/lawDetail")
    public Map<String,Object> lawDetail(@RequestBody List<CwProjectLawFeeDto> cwProjectFeeList)
    {
        return cwProjectCustService.selectCwprojectPayListLawDetail(cwProjectFeeList);
    }

    /**
     * 查询财务项目管理-根据主表id，查该项目的各种信息供修改
     */
    @GetMapping("/list/project/detail")
    public Map<String,Object> projectDetail(CwProject cwProject)
    {
        return cwProjectCustService.selectCwprojectDetail(cwProject);
    }

    /**
     * 查询财务项目管理-根据主表id，查该项目三个字段
     */
    @GetMapping("/list/project/detail/three")
    public Map<String,Object> projectDetailThree(CwProject cwProject)
    {
        return cwProjectCustService.selectCwprojectDetailThree(cwProject);
    }

    /**
     * 查找期次最近一个月的出返费公司和返费公司
     */
    @GetMapping("/findLawRecentlyCustFeeCompanyAndFeeCompany")
    public Map<String,Object> findLawRecentlyCustFeeCompanyAndFeeCompany(Long projectId, String termMonth)
    {
        return cwProjectCustService.findLawRecentlyCustFeeCompanyAndFeeCompany(projectId, termMonth);
    }

    /**
     * 查找期次最近一个月的出返费公司和返费公司
     */
    @PostMapping("/findLawSuspendFlagIsOneSum")
    public List<Map<String, Object>> findLawSuspendFlagIsOneSum(@RequestBody List<String> serviceProviderNameList)
    {
        if (serviceProviderNameList.size() == 0) {
            return null;
        } else {
//            List<Map<String, Object>> s = cwProjectCustService.findLawSuspendFlagIsOneSum(serviceProviderNameList);
//        return cwProjectCustService.findLawRecentlyCustFeeCompanyAndFeeCompany(projectId, termMonth);
            return cwProjectCustService.findLawSuspendFlagIsOneSum(serviceProviderNameList);
        }
    }

    /**
     * 导出财务项目管理-返费明细汇总表-导出报表 - 法催项目
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:export')")
    @Log(title = "财务项目管理-查询项目详情-法催项目", businessType = BusinessType.EXPORT)
    @PostMapping("/over/detail/exportForLaw")
    public void exportForLaw(HttpServletResponse response, CwProjectExportDto cwProjectExportDto) throws IOException {
        CwProject cwProject = new CwProject();
        cwProject.setId(Long.parseLong(cwProjectExportDto.getProjecId()));
        Map<String, Object> data = cwProjectCustService.selectCwProjectLawDetileByProjectId(cwProject, 1);
        //新导出方法
        ExcelLawCwProject excelCwProject =exportCwProjectMapper.selectExportLawCwProjectInfo(Long.parseLong(cwProjectExportDto.getProjecId()));
        excelCwProject.setCustList(exportCwProjectMapper.selectExportCwProjectCustList(Long.parseLong(cwProjectExportDto.getProjecId())));
        List<CwProjectLawDetailView> detailList = (List<CwProjectLawDetailView>) data.get("detail_list_v2");
        List<Map<String, Object>> peopleList = (List<Map<String, Object>>) data.get("people_list");
        List<CwProjectLawDetailView> collect = detailList.stream().filter(t -> !"本期次合计".equals(t.getServiceProvider())).collect(Collectors.toList());
        collect.forEach(t -> {
            if (t.getPhaseId() == null) {
                t.setPhaseId(t.getProjectIncomeId());
            }
        });
        excelCwProject.setIncomeList(collect);
        excelCwProject.setMemberList(peopleList);
        ExportExcelLawCwProject.exportLawCwProject(response, excelCwProject);
    }

    /**
     * 普通项目 - 使用收入表id查对应的驳回信息
     */
    @GetMapping("/getRejectionDetails")
    public List<CwProjectRejection> getRejectionDetails(Long projectIncomeId)
    {
        return cwProjectRejectionMapper.selectCwProjectRejectionByProjectIncomeId(projectIncomeId);
    }

    /**
     * 返费未结清查询
     */
    @GetMapping("/feeNoAlreadyQueryDetail")
    public Map<String, Object> feeNoAlreadyQueryDetail(String queryTime, String projectType)
    {
        LoginUser loginUser = getLoginUser();
        return cwProjectCustService.selectFeeNoAlreadyQueryDetailByQueryTime(queryTime, loginUser, projectType);
    }

    /**
     * 返费未结清导出
     */
    @PostMapping("/exportFeeNoAlreadyQueryDetail")
    @SuppressWarnings("unchecked")
    public void exportFeeNoAlreadyQueryDetail(HttpServletResponse response, String queryTime, String projectType) throws IOException {
        LoginUser loginUser = getLoginUser();
        Map<String, Object> map = cwProjectCustService.selectFeeNoAlreadyQueryDetailByQueryTime(queryTime, loginUser, projectType);
        Map<String, List<CwProjectFeeNoAlreadyVo>> resultMap = new HashMap<>();
        if ("all".equals(projectType) || "0".equals(projectType) || "2".equals(projectType)) {
            List<CwProjectFeeNoAlreadyVo> list = (List<CwProjectFeeNoAlreadyVo>) map.get("list");
            resultMap.put("list", list);
        }
        if ("all".equals(projectType) || "1".equals(projectType)) {
            List<CwProjectFeeNoAlreadyVo> lawList = (List<CwProjectFeeNoAlreadyVo>) map.get("lawList");
            resultMap.put("lawList", lawList);
        }
        ExportExcelCwProjectQuery.exportCwProject(response, resultMap, 1, projectType);
//        return cwProjectCustService.selectFeeNoAlreadyQueryDetailByQueryTime(queryTime, loginUser);
    }

    /**
     * 打款时间查询
     */
    @GetMapping("/payDateQueryDetail")
    public Map<String, Object> payDateQueryDetail(String queryStartTime, String queryEndTime, String projectType)
    {
        LoginUser loginUser = getLoginUser();
        return cwProjectCustService.selectPayDateQueryDetailQueryTime(queryStartTime, queryEndTime, loginUser, projectType);
    }

    /**
     * 打款时间导出
     */
    @PostMapping("/exportPayDateQueryDetail")
    @SuppressWarnings("unchecked")
    public void exportPayDateQueryDetail(HttpServletResponse response, String queryStartTime, String queryEndTime, String projectType) throws IOException {
        LoginUser loginUser = getLoginUser();
        Map<String, Object> map = cwProjectCustService.selectPayDateQueryDetailQueryTime(queryStartTime, queryEndTime, loginUser, projectType);
        Map<String, List<CwProjectPayDateVo>> resultMap = new HashMap<>();
        if ("all".equals(projectType) || "0".equals(projectType) || "2".equals(projectType)) {
            List<CwProjectPayDateVo> list = (List<CwProjectPayDateVo>) map.get("list");
            resultMap.put("list", list);
        }
        if ("all".equals(projectType) || "1".equals(projectType)) {
            List<CwProjectPayDateVo> lawList = (List<CwProjectPayDateVo>) map.get("lawList");
            resultMap.put("lawList", lawList);
        }
//        List<CwProjectPayDateVo> list = (List<CwProjectPayDateVo>) map.get("list");
        ExportExcelCwProjectPayDateQuery.exportCwProject(response, resultMap, projectType);
    }

    /**
     * 收款时间查询
     */
    @GetMapping("/collectionTimeQueryDetail")
    public Map<String, Object> collectionTimeQueryDetail(String queryStartTime, String queryEndTime, String projectType)
    {
        //收款时间返回前端的VO与打款时间返回前端的VO可以通用
        LoginUser loginUser = getLoginUser();
        List<Long> projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.FINANCEPROJ.getCode());
        return cwProjectCustService.selectCollectionTimeQueryDetailQueryTime(queryStartTime, queryEndTime, loginUser, projectType, projectIds);
    }

    /**
     * 收款时间导出
     */
    @PostMapping("/exportCollectionTimeQueryDetail")
    @SuppressWarnings("unchecked")
    public void exportCollectionTimeQueryDetail(HttpServletResponse response, String queryStartTime, String queryEndTime, String projectType) throws IOException {
        //收款时间返回前端的VO与打款时间返回前端的VO可以通用
        LoginUser loginUser = getLoginUser();
        List<Long> projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.FINANCEPROJ.getCode());
        Map<String, Object> map = cwProjectCustService.selectCollectionTimeQueryDetailQueryTime(queryStartTime, queryEndTime, loginUser, projectType, projectIds);
        Map<String, List<CwProjectPayDateVo>> resultMap = new HashMap<>();
        if ("all".equals(projectType) || "0".equals(projectType) || "2".equals(projectType)) {
            List<CwProjectPayDateVo> list = (List<CwProjectPayDateVo>) map.get("list");
            resultMap.put("list", list);
        }
        if ("all".equals(projectType) || "1".equals(projectType)) {
            List<CwProjectPayDateVo> lawList = (List<CwProjectPayDateVo>) map.get("lawList");
            resultMap.put("lawList", lawList);
        }
//        List<CwProjectPayDateVo> list = (List<CwProjectPayDateVo>) map.get("list");
        ExportExcelCwProjectPayDateQuery.exportCwProject(response, resultMap, projectType);
    }

    /**
     * 备注查询
     */
    @GetMapping("/remarkQueryDetail")
    public Map<String, Object> remarkQueryDetail(String remark)
    {
        //此时入参的remark是关键字
        LoginUser loginUser = getLoginUser();
        List<Long> projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.FINANCEPROJ.getCode());
        return cwProjectCustService.selectRemarkQueryDetailByQueryRemark(remark, loginUser, projectIds);
    }

//    /**
//     * 备注查询导出
//     */
//    @PostMapping("/exportRemarkQueryDetail")
//    @SuppressWarnings("unchecked")
//    public void exportRemarkQueryDetail(HttpServletResponse response, String remark) throws IOException {
//        //收款时间返回前端的VO与打款时间返回前端的VO可以通用
//        LoginUser loginUser = getLoginUser();
//        Map<String, Object> map = cwProjectCustService.selectRemarkQueryDetailByQueryRemark(remark, loginUser);
//        List<CwProjectFeeNoAlreadyVo> list = (List<CwProjectFeeNoAlreadyVo>) map.get("list");
//        ExportExcelCwProjectQuery.exportCwProject(response, list, 2);
//    }

    /**
     * 查询预存收入 - 所有，预存和抵扣 todo --> 财务项目管理五期，废弃预存收入表
     */
    @GetMapping("/prestoreIncomeList")
    public Map<String, Object> prestoreIncomeList(Long id)
    {
        return cwProjectCustService.selectPrestoreIncomeListByProjectId(id);
    }

    /**
     * 查询预存收入 - 只查预存 todo --> 财务项目管理五期，废弃预存收入表
     */
    @GetMapping("/prestoreIncomeListForIn")
    public List<CwProjectPrestoreIncome> prestoreIncomeListForIn(Long id)
    {
        return cwProjectCustService.selectPrestoreIncomeListForInByProjectId(id);
    }

    /**
     * 新增/修改 预存收入 todo --> 财务项目管理五期，废弃预存收入表
     */
    @PostMapping("/insertPrestoreIncomeList")
    public AjaxResult insertPrestoreIncomeList(@RequestBody List<CwProjectPrestoreIncome> cwProjectPrestoreIncomes)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(cwProjectCustService.insertPrestoreIncomeList(cwProjectPrestoreIncomes, loginUser));
    }

    /**
     * 项目详情（通道+分润）数据统计接口
     */
    @GetMapping("/getProjectSumByProjectId")
    public AjaxResult getProjectSumByProjectId(Long projectId)
    {
        return AjaxResult.success("操作成功", cwProjectCustService.getProjectSumByProjectId(projectId));
    }

    /**
     * 法催详情数据统计接口
     */
    @GetMapping("/getLawProjectSumByProjectId")
    public AjaxResult getLawProjectSumByProjectId(Long projectId)
    {
        return AjaxResult.success("操作成功", cwProjectCustService.getLawProjectSumByProjectId(projectId));
    }

    /**
     * 项目详情，记账凭证规则 - 所有项目通用
     */
    @GetMapping("/getProjectCertificateFlagByProjectId")
    public AjaxResult getProjectCertificateFlagByProjectId(Long projectId)
    {
        return AjaxResult.success("操作成功", cwProjectCustService.getProjectCertificateFlagByProjectId(projectId));
    }

    /**
     * 通过项目id,获取本项目的所有返费公司  --->  后续替换公司用
     */
    @GetMapping("/getProjectFeeCompanyInfoByProjectId")
    public AjaxResult getProjectFeeCompanyInfoByProjectId(Long projectId)
    {
        return AjaxResult.success("操作成功", cwProjectCustService.getProjectFeeCompanyInfoByProjectId(projectId));
    }

    /**
     * 通过项目id,oaTraderId,oldOaTraderId,newOaTraderId新增一个替换数据
     */
    @PostMapping("/addNewReplaceFeeCompanyInfo")
    public AjaxResult addNewReplaceFeeCompanyInfo(@RequestBody CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(cwProjectCustService.addNewReplaceFeeCompanyInfo(cwProjectReplaceFeeCompanyInfo, loginUser));
    }

    /**
     * 通过替换返费公司信息表id，撤回修改
     */
    @PutMapping("/revocationReplaceFeeCompanyInfo")
    public AjaxResult revocationReplaceFeeCompanyInfo(@RequestBody CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo)
    {
        return toAjax(cwProjectCustService.revocationReplaceFeeCompanyInfo(cwProjectReplaceFeeCompanyInfo));
    }

    /**
     * 过滤掉已经被选择的项目名称
     */
    @GetMapping("/getFilterOaProjectDeployList")
    public TableDataInfo getFilterOaProjectDeployList()
    {
        LoginUser loginUser = getLoginUser();
        List<OaProjectDeploy> list = cwProjectCustService.getFilterOaProjectDeployList(loginUser);
        return getDataTable(list);
    }

    /**
     * 通过页面传过来的项目id，找项目类型的下拉框
     */
    @GetMapping("/getSelectProjectTypeByOaProjectDeployId")
    public AjaxResult getSelectProjectTypeByOaProjectDeployId(Long oaProjectDeployId)
    {
        return AjaxResult.success(cwProjectCustService.getSelectProjectTypeByOaProjectDeployId(oaProjectDeployId));
    }

    /**
     * 通过页面传过来的项目id，找对应的项目角色
     */
    @GetMapping("/getProjectRoleByOaProjectDeployId")
    public AjaxResult getProjectRoleByOaProjectDeployId(Long oaProjectDeployId)
    {
        return AjaxResult.success(newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(oaProjectDeployId, AuthModuleEnum.FINANCEPROJ.getCode()));
    }

    /**
     * 项目详情页 - OA流程支付信息费记录标签页
     */
    @GetMapping("/getFlowAlreadyPayInfoFromOA")
    public TableDataInfo getFlowAlreadyPayInfoFromOA(Long projectId)
    {
        List<OaPayRebateRecordVo> list = cwProjectCustService.getFlowAlreadyPayInfoFromOA(projectId);
        return getDataTable(list);
    }

    /**
     * 项目详情页 - OA流程支付信息费记录标签页
     */
    @PostMapping("/updateFeeCompany")
    public AjaxResult updateFeeCompany(@RequestBody List<CwProjectFee> cwProjectFeeList)
    {
        return toAjax(cwProjectCustService.updateFeeCompany(cwProjectFeeList));
    }

    /**
     * 获取当前用户 有权限的项目 所拥有的返费公司下拉框数据
     */
    @GetMapping("/getOaTraderSelectByUserId")
    public List<OaTraderVo> getOaTraderSelectByUserId(Long userId)
    {
        return cwProjectCustService.getOaTraderInfoByUserId(userId);
    }

    /**
     * 获取当前用户 在项目当中 是哪些角色
     */
    @GetMapping("/getUserRoleByProjectId")
    public Map<String, Object> getUserRoleByProjectId(Long projectId)
    {
        LoginUser loginUser = getLoginUser();
        return cwProjectCustService.getUserRoleByProjectId(loginUser, projectId);
    }

    /**
     * 替换已经完成的期次时,请求期次信息的分页查询
     */
    @GetMapping("/getProjectDetailByPageParam")
    public TableDataInfo getProjectDetailPageByPageParam(Long projectId, String projectType, Integer pageNum, Integer pageSize) {
        List<?> list = cwProjectCustService.getProjectDetailPageByPageParam(projectId, projectType, pageNum, pageSize);
        return getDataTable(list);
    }
}
