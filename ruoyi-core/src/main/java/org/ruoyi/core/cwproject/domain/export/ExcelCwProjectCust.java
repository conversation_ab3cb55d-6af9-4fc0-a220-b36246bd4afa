package org.ruoyi.core.cwproject.domain.export;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 财务项目管理-返费公司与费率对象 cw_project_cust
 * 
 * <AUTHOR>
 * @date 2022-11-10
 */
@Data
public class ExcelCwProjectCust extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目管理表主键 */
    @Excel(name = "项目管理表主键")
    private Long projectId;

    /** 返费公司名称 */
    @Excel(name = "返费公司名称")
    private String custName;

    /** 费率 % */
    @Excel(name = "费率 %")
    private BigDecimal rate;

    /** 税率 % */
    @Excel(name = "税率 %")
    private BigDecimal taxRate;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    /** 方案标识 */
    @Excel(name = "方案标识")
    private String schemeFlag;

    /** 方案已使用次数 */
    @Excel(name = "方案已使用次数")
    private int schemeFlagUseSituation;

}
