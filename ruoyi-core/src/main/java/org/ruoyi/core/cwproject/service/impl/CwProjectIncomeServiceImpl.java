package org.ruoyi.core.cwproject.service.impl;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PinYinUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.financial.controller.JsonResult;
import com.ruoyi.financial.domain.FinancialSubject;
import com.ruoyi.financial.domain.FinancialVoucher;
import com.ruoyi.financial.domain.FinancialVoucherDetails;
import com.ruoyi.financial.mapper.FinancialSubjectMapper;
import com.ruoyi.financial.service.IFinancialOpenService;
import com.ruoyi.system.domain.dto.AuthorizedFeatureDetailDTO;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.ruoyi.core.cwproject.domain.*;
import org.ruoyi.core.cwproject.domain.dto.CwProjectIncomeForLawDto;
import org.ruoyi.core.cwproject.domain.dto.ExcelImportOfCwProjectDto;
import org.ruoyi.core.cwproject.domain.dto.LawProjectFeeIdAndFeeAmt;
import org.ruoyi.core.cwproject.domain.projectVO.AddFeePay;
import org.ruoyi.core.cwproject.domain.projectVO.AddParojectVo;
import org.ruoyi.core.cwproject.domain.projectVO.AddfeeVO;
import org.ruoyi.core.cwproject.domain.vo.CwSubject;
import org.ruoyi.core.cwproject.excelimport.ExcelImportOfCwProject;
import org.ruoyi.core.cwproject.mapper.*;
import org.ruoyi.core.cwproject.service.ICwProjectIncomeService;
import org.ruoyi.core.mapper.CwProjectAckRefMapper;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 财务项目管理-收入Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@Service
@Slf4j
public class CwProjectIncomeServiceImpl implements ICwProjectIncomeService {
    @Autowired
    private CwProjectIncomeMapper cwProjectIncomeMapper;

    @Autowired
    private CwProjectUserMapper cwProjectUserMapper;

    @Autowired
    private CwProjectFeeMapper cwProjectFeeMapper;
    @Autowired
    private CwProjectPayMapper cwProjectPayMapper;
    @Autowired
    private CwProjectDynamicMapper cwProjectDynamicMapper;
    @Autowired
    private CwProjectMapper cwProjectMapper;
    @Autowired
    private TopNotifyMapper topNotifyMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private CwProjectAckRefMapper cwProjectAckRefMapper;
    @Autowired
    private CwProjectAckMapper cwProjectAckMapper;
    @Autowired
    private CwProjectRejectionMapper cwProjectRejectionMapper;
    @Autowired
    private CwProjectCustMapper cwProjectCustMapper;

    @Autowired
    private IFinancialOpenService financialOpenService;
    @Autowired
    private FinancialSubjectMapper financialSubjectMapper;
    @Autowired
    private OaTraderMapper oaTraderMapper;
    @Autowired
    private OaDocumentMonitorMapper oaDocumentMonitorMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityService;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;

    @Autowired
    private OaProjectDeployMapper oaProjectDeployMapper;

    @Autowired
    private OaPayRebateRecordMapper oaPayRebateRecordMapper;

    @Autowired
    private OaDataManageMapper oaDataManageMapper;
    /**
     * 查询财务项目管理-收入
     *
     * @param id 财务项目管理-收入主键
     * @return 财务项目管理-收入
     */
    @Override
    public CwProjectIncome selectCwProjectIncomeById(Long id) {
        return cwProjectIncomeMapper.selectCwProjectIncomeById(id);
    }

    /**
     * 查询财务项目管理-收入列表
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 财务项目管理-收入
     */
    @Override
    public List<CwProjectIncome> selectCwProjectIncomeList(CwProjectIncome cwProjectIncome) {
        return cwProjectIncomeMapper.selectCwProjectIncomeList(cwProjectIncome);
    }

    /**
     * 新增财务项目管理-收入
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 结果
     */
    @Override
    public int insertCwProjectIncome(CwProjectIncome cwProjectIncome) {
        cwProjectIncome.setCreateTime(DateUtils.getNowDate());
        return cwProjectIncomeMapper.insertCwProjectIncome(cwProjectIncome);
    }

    /**
     * 修改财务项目管理-收入
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 结果
     */
    @Override
    public int updateCwProjectIncome(CwProjectIncome cwProjectIncome) {
        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        if ("1".equals(cwProject.getPrestoreIncomeFlag())) {
            //删除期次，直接删除之前存的抵扣的金额
            //通过项目id和收款时间来删
            String dateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, cwProjectIncome.getCollectionTimeBefore());
            CwProjectPrestoreIncome cwProjectPrestoreIncome = cwProjectCustMapper.selectCwProjectPrestoreIncomeByProjectIncomeIdAndDeductionIncomeDate(cwProjectIncome.getId(), dateStr);
            List<Long> ids = new ArrayList<>();
            ids.add(cwProjectPrestoreIncome.getId());
            int i = cwProjectCustMapper.deleteCwProjectPrestoreIncomeById(ids);
        }
        cwProjectIncome.setUpdateTime(DateUtils.getNowDate());
        return cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
    }

    /**
     * 批量删除财务项目管理-收入
     *
     * @param ids 需要删除的财务项目管理-收入主键
     * @return 结果
     */
    @Override
    public int deleteCwProjectIncomeByIds(Long[] ids) {
        return cwProjectIncomeMapper.deleteCwProjectIncomeByIds(ids);
    }

    /**
     * 删除财务项目管理-收入信息
     *
     * @param id 财务项目管理-收入主键
     * @return 结果
     */
    @Override
    public int deleteCwProjectIncomeById(Long id) {
        return cwProjectIncomeMapper.deleteCwProjectIncomeById(id);
    }

    /**
     * 查询财务项目管理-查询待录入收入状态列表
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 财务项目管理-收入集合
     */
    @Override
    public List<Map<String, Object>> selectCwProjectIncomeListFlagZero(CwProjectIncome cwProjectIncome, Long userId, LoginUser loginUser, List<Long> projectIds) {
        List<Map<String, Object>> list = new ArrayList<>();
        //首先判断是否为超级管理员，如果是，所有的信息都可以查到
        List<String> role = cwProjectUserMapper.selectUserRoleIsAdmin(userId);
        boolean present = loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "caiwuAdmin".equals(r.getRoleKey()));
//        boolean present = role.stream().anyMatch(r -> "admin".equals(r) || "caiwuAdmin".equals(r));
        DateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy.MM.dd");
//        Long role = cwProjectUserMapper.selectUserRoleIsAdmin(userId);
        Collator orderByChinese = Collator.getInstance(Locale.CHINESE);
        //根据本人所可以看到的项目权限，查找对应的待录入金额
        if (projectIds.size() != 0) {
            List<Map<String, Object>> cwProjectIncomes = cwProjectIncomeMapper.selectCwProjectIncomeListNotEnterByOaProjectDeployId(cwProjectIncome, projectIds);
            Collections.sort(cwProjectIncomes, (e1, e2) -> {
                //两个参数是第一个是这个list，后面是进入的第一个和第二个，然后他的projectName进行比较
                return orderByChinese.compare(e1.get("project_name"), e2.get("project_name"));
            });
            //遍历list
            cwProjectIncomes.forEach(t -> {
                if (Optional.ofNullable(t).isPresent()) {
                    if ("0".equals(t.get("term"))) {
                        String termMonth = t.get("term_month").toString();
                        termMonth = termMonth.replace("-", "年") + "月";
                        t.put("term_month", termMonth);
                    } else if ("1".equals(t.get("term"))) {
                        String format1 = simpleDateFormat1.format(t.get("term_begin"));
                        String format2 = simpleDateFormat1.format(t.get("term_end"));
                        t.put("term_month", format1 + "-" + format2);
                    } else {
                        t.put("term_month", "期次有误，请按照流程处理！");
                    }
                }
                //找对应的项目的用户情况
                List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(Long.parseLong(t.get("oaProjectDeployId").toString()), AuthModuleEnum.FINANCEPROJ.getCode());
                //会计
                List<String> accountantList = userList.stream().filter(a -> "1".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                //出纳
                List<String> cashierList = userList.stream().filter(a -> "2".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                //业务
                List<String> businessList = userList.stream().filter(a -> "3".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                //查看权限
                List<String> selectList = userList.stream().filter(a -> "88".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                t.put("accountant_list", accountantList);
                t.put("cashier_list", cashierList);
                t.put("business_list", businessList);
                t.put("select_list", selectList);
                list.add(t);
            });
        }
        return list;
    }

    /**
     * 查询财务项目管理-查询待录入收入状态列表
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 财务项目管理-收入集合
     */
    @Override
    public List<Map<String, Object>> selectCwProjectIncomeListFlagOne(CwProjectIncome cwProjectIncome, Long userId, LoginUser loginUser,List<Long> projectIds) {
        List<Map<String, Object>> list = new ArrayList<>();
        //首先判断是否为超级管理员，如果是，所有的信息都可以查到
        List<String> role = cwProjectUserMapper.selectUserRoleIsAdmin(userId);
        boolean present = loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "caiwuAdmin".equals(r.getRoleKey()) || "yewuAdmin".equals(r.getRoleKey()));
//        boolean present = role.stream().anyMatch(r -> "admin".equals(r) || "caiwuAdmin".equals(r) || "yewuAdmin".equals(r));
        DateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy.MM.dd");
//        Long role = cwProjectUserMapper.selectUserRoleIsAdmin(userId);
        Collator orderByChinese = Collator.getInstance(Locale.CHINESE);
        //根据本人所可以看到的项目权限，查找对应的待确认金额
        if (projectIds.size() != 0) {
            List<Map<String, Object>> cwProjectIncomes = cwProjectIncomeMapper.selectCwProjectIncomeListNotVerifyByOaProjectDeployId(cwProjectIncome, projectIds);
            Collections.sort(cwProjectIncomes, (e1, e2) -> {
                //两个参数是第一个是这个list，后面是进入的第一个和第二个，然后他的projectName进行比较
                return orderByChinese.compare(e1.get("project_name"), e2.get("project_name"));
            });
            //遍历list
            cwProjectIncomes.forEach(t -> {
                if (Optional.ofNullable(t).isPresent()) {
                    if ("0".equals(t.get("term"))) {
                        String termMonth = t.get("term_month").toString();
                        termMonth = termMonth.replace("-", "年") + "月";
                        t.put("term_month", termMonth);
                    } else if ("1".equals(t.get("term"))) {
                        String format1 = simpleDateFormat1.format(t.get("term_begin"));
                        String format2 = simpleDateFormat1.format(t.get("term_end"));
                        t.put("term_month", format1 + "-" + format2);
                    } else {
                        t.put("term_month", "期次有误，请按照流程处理！");
                    }
                }
                //找对应的项目的用户情况
                List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(Long.parseLong(t.get("oaProjectDeployId").toString()), AuthModuleEnum.FINANCEPROJ.getCode());
                //会计
                List<String> accountantList = userList.stream().filter(a -> "1".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                //出纳
                List<String> cashierList = userList.stream().filter(a -> "2".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                //业务
                List<String> businessList = userList.stream().filter(a -> "3".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                //查看权限
                List<String> selectList = userList.stream().filter(a -> "88".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                t.put("accountant_list", accountantList);
                t.put("cashier_list", cashierList);
                t.put("business_list", businessList);
                t.put("select_list", selectList);
                list.add(t);
            });
        }


//        if (present) {
//            //首先去查收入表当中的状态为0-正常并且录入状态为0-未录入的信息
//            List<Map<String, Object>> cwProjectIncomes = new ArrayList<>();
//            List<Map<String, Object>> cwProjectIncomes1 = new ArrayList<>();
//            PageUtils.startPage();
//            if ("1".equals(cwProjectIncome.getRejectionIncomeAndChangeIncome())) {
//                cwProjectIncomes = cwProjectIncomeMapper.selectCwProjectIncomeListFlagOneByAdmin();
//            } else {
//                cwProjectIncomes1 = cwProjectIncomeMapper.selectLawCwProjectIncomeListFlagEightByAdmin();
//            }
//            cwProjectIncomes.addAll(cwProjectIncomes1);
//            Collections.sort(cwProjectIncomes, (e1, e2) -> {
//                //两个参数是第一个是这个list，后面是进入的第一个和第二个，然后他的projectName进行比较
//                return orderByChinese.compare(e1.get("project_name"), e2.get("project_name"));
//            });
//            //遍历list
//            cwProjectIncomes.forEach(t -> {
//                if (Optional.ofNullable(t).isPresent()) {
//                    if ("0".equals(t.get("term"))) {
//                        String termMonth = t.get("term_month").toString();
//                        termMonth = termMonth.replace("-", "年") + "月";
//                        t.put("term_month", termMonth);
//                    } else if ("1".equals(t.get("term"))) {
//                        String format1 = simpleDateFormat1.format(t.get("term_begin"));
//                        String format2 = simpleDateFormat1.format(t.get("term_end"));
//                        t.put("term_month", format1 + "-" + format2);
//                    } else {
//                        t.put("term_month", "期次有误，请按照流程处理！");
//                    }
//                }
//                List<String> accountantList = new ArrayList<>();
//                List<String> cashierList = new ArrayList<>();
//                List<String> businessList = new ArrayList<>();
//                List<String> selectList = new ArrayList<>();
//                List<String> exportList = new ArrayList<>();
//                //通过收入表的id去查成员表当中涉及到收入表可操作的的（正常状态-0）的人员信息，并遍历
//                cwProjectUserMapper.selectCwProjectUserByProjectId((Long) t.get("project_id")).forEach(u -> {
//                    if (CwxmglConstants.USER_FLAG_0.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                        String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                        accountantList.add(o);
//                    } else if (CwxmglConstants.USER_FLAG_1.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                        String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                        cashierList.add(o);
//                    } else if (CwxmglConstants.USER_FLAG_2.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                        String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                        businessList.add(o);
//                    } else if (CwxmglConstants.USER_FLAG_3.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                        String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                        selectList.add(o);
//                    } else if (CwxmglConstants.USER_FLAG_4.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                        String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                        exportList.add(o);
//                    }
//                    t.put("accountant_list", accountantList);
//                    t.put("cashier_list", cashierList);
//                    t.put("business_list", businessList);
//                    t.put("select_list", selectList);
//                    t.put("export_list", exportList);
//                });
//                list.add(t);
//            });
//        } else {
//            List<CwProject> cwProjectUsers = cwProjectUserMapper.selectCwProjectUserByUserIdWhenUserFlagTwo(userId);
//            Collections.sort(cwProjectUsers, (e1, e2) -> {
//                //两个参数是第一个是这个list，后面是进入的第一个和第二个，然后他的projectName进行比较
//                return orderByChinese.compare(e1.getProjectName(), e2.getProjectName());
//            });
//            cwProjectUsers.forEach(p -> {
//                //首先去查收入表当中的状态为0-正常并且录入状态为0-未录入的信息
//                List<Map<String, Object>> cwProjectIncomes = new ArrayList<>();
//                List<Map<String, Object>> cwProjectIncomes1 = new ArrayList<>();
//                if ("1".equals(cwProjectIncome.getRejectionIncomeAndChangeIncome())) {
//                    cwProjectIncomes = cwProjectIncomeMapper.selectCwProjectIncomeListFlagOne(p.getId());
//                } else {
//                    cwProjectIncomes1 = cwProjectIncomeMapper.selectLawCwProjectIncomeListFlagEight(p.getId());
//                }
//                cwProjectIncomes.addAll(cwProjectIncomes1);
//                //遍历list
//                cwProjectIncomes.forEach(t -> {
//                    if (Optional.ofNullable(t).isPresent()) {
//                        if ("0".equals(t.get("term"))) {
//                            String termMonth = t.get("term_month").toString();
//                            termMonth = termMonth.replace("-", "年") + "月";
//                            t.put("term_month", termMonth);
//                        } else if ("1".equals(t.get("term"))) {
//                            String format1 = simpleDateFormat1.format(t.get("term_begin"));
//                            String format2 = simpleDateFormat1.format(t.get("term_end"));
//                            t.put("term_month", format1 + "-" + format2);
//                        } else {
//                            t.put("term_month", "期次有误，请按照流程处理！");
//                        }
//                    }
//                    List<String> accountantList = new ArrayList<>();
//                    List<String> cashierList = new ArrayList<>();
//                    List<String> businessList = new ArrayList<>();
//                    List<String> selectList = new ArrayList<>();
//                    List<String> exportList = new ArrayList<>();
//                    //通过收入表的id去查成员表当中涉及到收入表可操作的的（正常状态-0）的人员信息，并遍历
//                    cwProjectUserMapper.selectCwProjectUserByProjectId((Long) t.get("project_id")).forEach(u -> {
//                        if (CwxmglConstants.USER_FLAG_0.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            accountantList.add(o);
//                        } else if (CwxmglConstants.USER_FLAG_1.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            cashierList.add(o);
//                        } else if (CwxmglConstants.USER_FLAG_2.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            businessList.add(o);
//                        } else if (CwxmglConstants.USER_FLAG_3.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            selectList.add(o);
//                        } else if (CwxmglConstants.USER_FLAG_4.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            exportList.add(o);
//                        }
//                        t.put("accountant_list", accountantList);
//                        t.put("cashier_list", cashierList);
//                        t.put("business_list", businessList);
//                        t.put("select_list", selectList);
//                        t.put("export_list", exportList);
//                    });
//                    list.add(t);
//                });
//            });
//        }
        return list;
    }

    @Override
    public List<CwProjectIncome> selectCwProjectIncomeListAll() {
        return cwProjectIncomeMapper.selectCwProjectIncomeListAll();
    }


    /**
     * 新增业务期次
     */
    @Override
    @Transactional
    public  Map<String, Object> addIncomeQiCi(CwProjectIncome cwProjectIncome, LoginUser loginUser) {
        Map<String, Object> returnMap = new HashMap<>();
        Long incomeId = null;
        boolean b = false;
        Date now = new Date();
        //获取项目名称
        CwProject cwProject = cwProjectMapper.selectCwProjectById(cwProjectIncome.getProjectId());
        String projectName = cwProject.getProjectName();
        String username = loginUser.getUsername();

        cwProjectIncome.setCreateBy(username);
        cwProjectIncome.setCreateTime(now);
        cwProjectIncome.setUpdateTime(now);
        cwProjectIncome.setPhaseStatus("0");

        //查询数据库有没有期次为整月的并且期次相同的期次信息
        List<CwProjectIncome> cwProjectIncomes  = cwProjectIncomeMapper.selectCwProject(cwProjectIncome.getProjectId(),cwProjectIncome.getTerm(),cwProjectIncome.getTermMonth());
        if(cwProjectIncome.getTerm().equals("0")){
            if (cwProjectIncomes.size()==0){
                incomeId = this.addCWIncome(cwProjectIncome, projectName, username);
                b = true;
            }else {
                b = false;
            }
        }else if(cwProjectIncome.getTerm().equals("1")){

            boolean add = this.isAdd(cwProjectIncome, cwProjectIncomes);
            if (add){
                incomeId = this.addCWIncome(cwProjectIncome, projectName, username);
                b = true;
            }else {
                b = false;
            }
        }
        returnMap.put("isok", b);
        returnMap.put("incomeId", incomeId);
        return returnMap;

//        return b;
    }
    public Long addCWIncome( CwProjectIncome cwProjectIncome,String projectName,String username){
        Date now = new Date();
        cwProjectIncomeMapper.insertCwProjectIncome(cwProjectIncome);
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        Long incomeId = cwProjectIncome.getId();
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(incomeId);
        //待办表新增数据(当前状态待处理人有几个就添加几条)
        //得到会计代办人id
        CwProject cwProject = cwProjectMapper.selectCwProjectById(cwProjectIncome.getProjectId());
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(cwProject.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
        List<Long> kuaijiID = userList.stream().filter(t -> "1".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList());
//        List<Long> kuaijiID = cwProjectUserMapper.getUserIdListByProId(cwProjectIncome.getProjectId(), "0");
//        //得到出纳代办人id
//        List<Long> chunaId = cwProjectUserMapper.getUserIdListByProId(cwProjectIncome.getProjectId(), "1");
        //得到业务代办人id
//        List<Long> yewuId = cwProjectUserMapper.getUserIdListByProId(cwProjectIncome.getProjectId(), "2");
        for (Long aLong : kuaijiID) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyModule("财务项目管理");
            topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-待录入金额");
            //刚插入为代办
            topNotify.setNotifyType("1");
            topNotify.setUrl("/caiwu/projectDetails");
            topNotify.setProjectId(cwProjectIncome.getProjectId());
            topNotify.setIncomeId(cwProjectIncome.getId());
            topNotify.setPhaseStatus("0");
            topNotify.setDisposeUser(aLong);
            topNotify.setViewFlag("0");
            topNotify.setCreateBy(username);
            topNotify.setCreateTime(now);
            topNotify.setUpdateTime(now);
            topNotifyMapper.insertTopNotify(topNotify);
        }

        // 添加提成基数确认及返费关联表数据
        CwProjectAck cwProjectAck = cwProjectAckMapper.selectCwProjectAckByProjectId(cwProjectIncome.getProjectId());
        if (ObjectUtils.isEmpty(cwProjectAck)) {
            cwProjectAck = new CwProjectAck();
            cwProjectAck.setProjectId(cwProjectIncome.getProjectId());
            cwProjectAck.setAckFlag("0");
            cwProjectAck.setStatus("0");
            cwProjectAck.setCreateBy(username);
            cwProjectAck.setCreateTime(now);
            cwProjectAck.setUpdateBy(username);
            cwProjectAck.setUpdateTime(now);
            cwProjectAckMapper.insertCwProjectAck(cwProjectAck);
        }
        CwProjectAckRef cwProjectAckRef = new CwProjectAckRef();
        cwProjectAckRef.setAckId(cwProjectAck.getId());
        cwProjectAckRef.setStatus("0");
        cwProjectAckRef.setCreateBy(username);
        cwProjectAckRef.setCreateTime(now);
        cwProjectAckRef.setUpdateBy(username);
        cwProjectAckRef.setUpdateTime(now);
        cwProjectAckRef.setProjectIncomeId(cwProjectIncome.getId());
        cwProjectAckRefMapper.insertCwProjectAckRef(cwProjectAckRef);
        return incomeId;
    }

    /**
     * 修改业务期次
     *
     * @param cwProjectIncome
     * @param
     */
    @Override
    @Transactional
    public boolean updateIncomeQiCi(CwProjectIncome cwProjectIncome, LoginUser loginUser) {

        boolean b = false;
        //获取项目名称
        CwProject cwProject = cwProjectMapper.selectCwProjectById(cwProjectIncome.getProjectId());
        String projectName = cwProject.getProjectName();
        String username = loginUser.getUsername();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        Date now = new Date();
        //如果要修改的是整月的则判断有没有相同
        List<CwProjectIncome> cwProjectIncomes  = cwProjectIncomeMapper.selectCwProject(cwProjectIncome.getProjectId(),cwProjectIncome.getTerm(),cwProjectIncome.getTermMonth());
        if(cwProjectIncome.getTerm().equals("0")){

            //如果返回的数据列表条数大于0则证明有相同的
            if(cwProjectIncomes.size()>0){
                b = false;
            }else {
                cwProjectIncome.setUpdateBy(loginUser.getUsername());
                cwProjectIncome.setUpdateTime(now);
                cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
                b = true;
            }

        }else if(cwProjectIncome.getTerm().equals("1")){

            boolean add = this.isAdd(cwProjectIncome, cwProjectIncomes);
            if (add){
                cwProjectIncome.setUpdateBy(loginUser.getUsername());
                cwProjectIncome.setUpdateTime(now);
                cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
                b = true;
            }else {
                b = false;
            }
        }

        return b;
    }

    /**
     * 判断要插入的期次是不是已经有已存在的日期范围
     *
     * @param addData     添加数据
     * @param verifyDatas 验证数据
     * @return boolean
     */
    public boolean isAdd(CwProjectIncome addData, List<CwProjectIncome> verifyDatas){
        boolean b = true;
        DateFormat sf =new SimpleDateFormat("yyyy-MM-dd");
        try {
            //插入的生效时间以及失效时间


            long addSTime = sf.parse(sf.format(addData.getTermBegin())).getTime();
            long addETime = sf.parse(sf.format(addData.getTermEnd())).getTime();
            for (CwProjectIncome verifyData : verifyDatas) {
                //校验的时间
                long verifySTime = sf.parse(sf.format(verifyData.getTermBegin())).getTime();
                long verifyETime = sf.parse(sf.format(verifyData.getTermEnd())).getTime();
                if(null == addData.getId()){
                        if((addSTime < verifySTime && addETime <verifySTime) || (addSTime > verifyETime && addETime > verifyETime)){
                            b = true;
                        } else {
                            b = false;
                            break;
                        }
                }else {
                    if((addSTime < verifySTime && addETime <verifySTime) || (addSTime > verifyETime && addETime > verifyETime)){
                        b = true;
                    } else {
                        if(addData.getId().toString().equals(verifyData.getId().toString())){
                            b = true;
                        }else {
                            b = false;
                            break;
                        }
                    }
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return b;
    }

    /**
     * 新增收入
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addIncomeShouRu(CwProjectIncome cwProjectIncome, LoginUser loginUser) {
        //根据期次id获取项目信息
        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        String nickName = loginUser.getUser().getNickName();
        Long projectId = cwProject.getId();
        String projectName = cwProject.getProjectName();
        Date now = new Date();
        String username = loginUser.getUsername();
        cwProjectIncome.setUpdateBy(username);
        cwProjectIncome.setUpdateTime(now);
        if ("1".equals(cwProject.getPrestoreIncomeFlag())) {
            //减数 - 本期次收入
            BigDecimal incomeAmt = cwProjectIncome.getIncomeAmt();
            //被减数 - 预存收入余额
//            Map<String, Object> map = cwProjectCustService.selectPrestoreIncomeListByProjectId(cwProject.getId());
//            BigDecimal prestoreBalanceSum = (BigDecimal) map.get("prestoreBalanceSum");
            //差 - 抵扣后余额



            //入库
            CwProjectPrestoreIncome cwProjectPrestoreIncome = new CwProjectPrestoreIncome();
            cwProjectPrestoreIncome.setProjectId(cwProject.getId());
            cwProjectPrestoreIncome.setProjectIncomeId(cwProjectIncome.getId());
            cwProjectPrestoreIncome.setFlag("1");
            cwProjectPrestoreIncome.setDeductionIncomeDate(cwProjectIncome.getCollectionTime());
            cwProjectPrestoreIncome.setDeductionIncomeAmt(incomeAmt);
            cwProjectPrestoreIncome.setStatus("0");
            cwProjectPrestoreIncome.setCreateBy(nickName);
            cwProjectPrestoreIncome.setCreateTime(now);
            cwProjectPrestoreIncome.setUpdateBy(nickName);
            cwProjectPrestoreIncome.setUpdateTime(now);
            int i = cwProjectCustMapper.insertPrestoreIncome(cwProjectPrestoreIncome);
        }





//        //修改待录入收入的待办表数据状态
//        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "0", now, username);
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        return true;


    }

    /**
     * 修改收入
     */
    @Override
    public boolean updateIncomeShouRu(CwProjectIncome cwProjectIncome, String username) {
        Date now = new Date();
        cwProjectIncome.setUpdateBy(username);
        cwProjectIncome.setUpdateTime(now);
        cwProjectIncome.setPhaseStatus("1");
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        return true;
    }

    /**
     * 确认收入状态
     *
     * @param
     * @param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSubmitStatus(CwProjectIncome cwProjectIncome, LoginUser loginUser) {

        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        Long projectId = cwProject.getId();
        String projectName = cwProject.getProjectName();
        String username = loginUser.getUsername();
        Date now = new Date();
        cwProjectIncome.setUpdateBy(loginUser.getUsername());
        cwProjectIncome.setUpdateTime(now);
        String nickName = loginUser.getUser().getNickName();
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(cwProjectIncome.getId());

        // todo 财务项目管理四期 确认完金额之后，就开始走打款流程
        //通过收入表id查询返费表当前的所有返费金额
        Map<String, Object> feeAmt = cwProjectFeeMapper.selectByIncomeId(cwProjectIncome.getId());
        Object feeAmt1 = feeAmt.get("feeAmt");

        //放入收入表返费未结清金额
        cwProjectIncome.setUnfeeAmt(new BigDecimal(feeAmt1.toString()));
        cwProjectIncome.setUpdateBy(username);
        cwProjectIncome.setUpdateTime(now);
        //财务项目管理五期，不需要考虑 返费已结清
        //只需要考虑 返费总计 = 实付返费       返费未结清 = 返费合计 - OA打款的金额
        cwProjectIncome.setPhaseStatus("2");
        //流程表新增数据
        CwProjectDynamic cwProjectDynamic = new CwProjectDynamic();
        cwProjectDynamic.setProjectId(projectId);
        cwProjectDynamic.setIncomeId(cwProjectIncome.getId());
        cwProjectDynamic.setDynamicMsg("已完成");
        cwProjectDynamic.setDynamicTime(now);
        cwProjectDynamic.setPhaseStatus("1");
        cwProjectDynamic.setOperId(loginUser.getUserId());
        cwProjectDynamic.setOperName(nickName);
        cwProjectDynamicMapper.insertCwProjectDynamic(cwProjectDynamic);
        //修改待确认返费的待办表数据状态
        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "1", now, username);
        CwProjectIncome cwProjectIncome1 = cwProjectIncomeMapper.selectCwProjectIncomeById(cwProjectIncome.getId());
        CwProjectAck cwProjectAck = cwProjectIncomeMapper.selectCwProjectIncomeByProjectId(cwProjectIncome1.getProjectId());
        //得到人事代办人
        if (cwProjectAck != null) {
            List<Long> hrUserList = sysRoleMapper.getUserIdListByRoleKey(8L);
            if (hrUserList != null && hrUserList.size() > 0) {
                for (Long aLong : hrUserList) {
                    TopNotify topNotify = new TopNotify();
                    topNotify.setNotifyModule("财务项目管理");
//                            topNotify.setNotifyMsg(projectName + "-待确认提成基数");
                    topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-待确认提成基数");
                    //刚插入为代办
                    topNotify.setNotifyType("1");
                    topNotify.setUrl("/caiwu/undetails");
                    topNotify.setProjectId(cwProjectAck.getProjectId());
                    topNotify.setIncomeId(cwProjectAck.getId());
                    topNotify.setPhaseStatus("7");
                    topNotify.setDisposeUser(aLong);
                    topNotify.setViewFlag("0");
                    topNotify.setCreateBy(loginUser.getUsername());
                    topNotify.setCreateTime(now);
                    topNotify.setUpdateTime(now);
                    topNotifyMapper.insertTopNotify(topNotify);
                }
            }
        }








        //修改待确认收入的待办表数据状态
//        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "1", now, username);
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        //修改返费表状态
        String feeFlag = "2";
        cwProjectFeeMapper.updateCwProjectFeeByincomeId(feeFlag, cwProjectIncome.getId(), username);
        return true;
    }

    @Override

    public boolean updateYewuUser(AddParojectVo addParojectVo, String username) {
        Date now = new Date();
        //删除原本的项目所属的业务人员
        cwProjectUserMapper.deleteByprojectId(addParojectVo.getId(), "2");
        //新增新的业务人员
        //业务
        List<Long> salesmans = addParojectVo.getSalesmans();

        for (Long accountant : salesmans) {
            CwProjectUser cwProjectUser = new CwProjectUser();
            cwProjectUser.setProjectId(addParojectVo.getId());
            cwProjectUser.setUserId(accountant);
            cwProjectUser.setUserFlag("2");
            cwProjectUser.setCreateBy(username);
            cwProjectUser.setCreateTime(now);
            cwProjectUser.setUpdateTime(now);
            cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
        }
        return true;
    }

    /**
     * 新增返费
     *
     * @param
     * @param loginUser
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addFeeList(AddCwProjectIncomeAndFee addCwProjectIncomeAndFee, LoginUser loginUser) {
        AddfeeVO addfeeVO = addCwProjectIncomeAndFee.getAddfeeVO();
        CwProject cwProject = cwProjectMapper.selectByIncomeId(addfeeVO.getIncomeId());
        //项目id
        Long projectId = cwProject.getId();
        String projectName = cwProject.getProjectName();
        String username = loginUser.getUsername();
        Date now = new Date();
        String nickName = loginUser.getUser().getNickName();
        //收入表id
        Long incomeId = addfeeVO.getIncomeId();
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(incomeId);
        //修改收入表
        CwProjectIncome cwProjectIncome = new CwProjectIncome();
        //id
        cwProjectIncome.setId(addfeeVO.getIncomeId());
        //毛利
        cwProjectIncome.setGrossProfitAmt(addfeeVO.getMaoli());
        //提成毛利
        cwProjectIncome.setGrossProfitAmt2(addfeeVO.getTichengmaoli());
        //改变状态
//        cwProjectIncome.setPhaseStatus(addfeeVO.getPhaseStatus());
        //备注
        cwProjectIncome.setRemark(addfeeVO.getRemark());
        //修改人
        cwProjectIncome.setUpdateBy(username);
        //修改时间
        cwProjectIncome.setUpdateTime(now);


        //流程表新增数据
        CwProjectDynamic cwProjectDynamic = new CwProjectDynamic();
        cwProjectDynamic.setProjectId(projectId);
        cwProjectDynamic.setIncomeId(cwProjectIncome.getId());
        cwProjectDynamic.setDynamicMsg("录入金额");
        cwProjectDynamic.setDynamicTime(now);
        cwProjectDynamic.setPhaseStatus("0");
        cwProjectDynamic.setOperId(loginUser.getUserId());
        cwProjectDynamic.setOperName(nickName);
        cwProjectDynamicMapper.insertCwProjectDynamic(cwProjectDynamic);
        //待办表新增数据(当前状态待处理人有几个就添加几条)

        //得到业务代办人id
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(cwProject.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
        List<Long> yewuList = userList.stream().filter(t -> "3".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList());
//        List<Long> yewuList = cwProjectUserMapper.getUserIdListByProId(projectId, "2");
        for (Long aLong : yewuList) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyModule("财务项目管理");
            topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-待确认金额");
            //刚插入为代办
            topNotify.setNotifyType("1");
            topNotify.setUrl("/caiwu/projectDetails");

            topNotify.setProjectId(projectId);

            topNotify.setIncomeId(cwProjectIncome.getId());
            //todo 之前状态为3，财务项目管理四期改为1
//            topNotify.setPhaseStatus("3");
            topNotify.setPhaseStatus("1");
            topNotify.setDisposeUser(aLong);
            topNotify.setViewFlag("0");
            topNotify.setCreateBy(username);
            topNotify.setCreateTime(now);
            topNotify.setUpdateTime(now);
            topNotifyMapper.insertTopNotify(topNotify);
        }

        //todo 旧 - 修改待录入返费的待办表数据状态
//        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "2", now, username);
        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "0", now, username);

        //提交修改
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        //新增返费表
        List<CwProjectFee> fee = addfeeVO.getFee();
        //2024.10.24在新增返费公司的时候，对revealFeeCompanyId进行赋值
        //具体赋值步骤：通过现在的custId，去找找看是否有替换的返费公司在，如果有，那么就赋给最新的oaTraderId，如果没有，那么就用custId所对应的oaTraderId
        //找是否有替换的标识
        CwProjectCust cwProjectCust = new CwProjectCust();
        cwProjectCust.setProjectId(projectId);
        List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustList(cwProjectCust);
        for (CwProjectFee cwProjectFee : fee) {
            if (cwProjectFee.getCustId() == null) {
                cwProjectFee.setCustId(-999L);
                cwProjectFee.setCustRemark(addCwProjectIncomeAndFee.getCustRemark());
            } else  {
                //看这个项目的cust信息里，入库的那个替换标识是什么
                Long custId = cwProjectFee.getCustId();
                CwProjectCust cwProjectCust1 = cwProjectCusts.stream().filter(t -> t.getId().equals(custId)).findFirst().orElse(null);
                if ("1".equals(cwProjectCust1.getReplaceFlag())) {
                    //发生过替换，那么找最新的一条替换记录中的newOaTraderId
                    Long oaTraderId = cwProjectCust1.getOaTraderId();
                    Long newOaTraderId = cwProjectCustMapper.queryLatestNewOaTraderIdByProjectIdAndOaTraderIdAndStatus(projectId, oaTraderId, "0");
                    cwProjectFee.setRevealFeeCompanyId(newOaTraderId);
                }
            }
            //循环放入项目、收入表id 名字  创建时间 创建人 修改时间
            cwProjectFee.setProjectId(projectId);
            cwProjectFee.setProjectIncomeId(incomeId);
            cwProjectFee.setFeeFlag("1");
            cwProjectFee.setCreateBy(username);
            cwProjectFee.setCreateTime(now);
            cwProjectFee.setUpdateTime(now);
            cwProjectFeeMapper.insertCwProjectFee(cwProjectFee);
        }

        return true;
    }

    @Override
    @Transactional
    public boolean updateFeeList(AddfeeVO addfeeVO, String username) {
        Date now = new Date();
        //项目id
        Long projectId = addfeeVO.getProjectId();
        //收入表id
        Long incomeId = addfeeVO.getIncomeId();
        //修改收入表
        CwProjectIncome cwProjectIncome = new CwProjectIncome();
        //id
        cwProjectIncome.setId(addfeeVO.getIncomeId());
        //毛利
        cwProjectIncome.setGrossProfitAmt(addfeeVO.getMaoli());
        //提成毛利
        cwProjectIncome.setGrossProfitAmt2(addfeeVO.getTichengmaoli());
        //备注
        cwProjectIncome.setRemark(addfeeVO.getRemark());
        //修改人
        cwProjectIncome.setUpdateBy(username);
        //修改时间
        cwProjectIncome.setUpdateTime(now);
        //收入表提交修改
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        //根据收入id和项目id删除返费
        cwProjectFeeMapper.deleteFeeByInAndPro(projectId, incomeId);
        //新增返费表
        List<CwProjectFee> fee = addfeeVO.getFee();
        for (CwProjectFee cwProjectFee : fee) {
            //循环放入项目、收入表id 名字  创建时间 创建人 修改时间
            cwProjectFee.setProjectId(projectId);
            cwProjectFee.setProjectIncomeId(incomeId);

            cwProjectFee.setFeeFlag("1");

            cwProjectFee.setCreateBy(username);
            cwProjectFee.setCreateTime(now);
            cwProjectFee.setUpdateTime(now);
            cwProjectFeeMapper.insertCwProjectFee(cwProjectFee);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean querenFee(CwProjectIncome cwProjectIncome, LoginUser loginUser) {

        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(cwProjectIncome.getId());
        Long projectId = cwProject.getId();
        String projectName = cwProject.getProjectName();
        String username = loginUser.getUsername();
        String nickName = loginUser.getUser().getNickName();
        //修改收入表状态
        Date now = new Date();


        //通过收入表id查询返费表当前的所有返费金额
        Map<String, Object> feeAmt = cwProjectFeeMapper.selectByIncomeId(cwProjectIncome.getId());
        Object feeAmt1 = feeAmt.get("feeAmt");

        //放入收入表返费未结清金额
        cwProjectIncome.setUnfeeAmt(new BigDecimal(feeAmt1.toString()));
        cwProjectIncome.setUpdateBy(username);
        cwProjectIncome.setUpdateTime(now);
        //如果未结清为0，说明没有需要打款的。
        if (cwProjectIncome.getUnfeeAmt().compareTo(BigDecimal.ZERO) == 0) {
            cwProjectIncome.setPhaseStatus("2");



            //修改待确认返费的待办表数据状态
            topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "4", now, username);


            if ("2".equals(cwProjectIncome.getPhaseStatus())) {
                CwProjectIncome cwProjectIncome1 = cwProjectIncomeMapper.selectCwProjectIncomeById(cwProjectIncome.getId());
                CwProjectAck cwProjectAck = cwProjectIncomeMapper.selectCwProjectIncomeByProjectId(cwProjectIncome1.getProjectId());
                //得到业务代办人
                if (cwProjectAck != null) {
                    List<Long> hrUserList = sysRoleMapper.getUserIdListByRoleKey(8l);
                    if (hrUserList != null && hrUserList.size() > 0) {
                        for (Long aLong : hrUserList) {
                            TopNotify topNotify = new TopNotify();
                            topNotify.setNotifyModule("财务项目管理");
                            topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-待确认提成基数");
                            //刚插入为代办
                            topNotify.setNotifyType("1");
                            topNotify.setUrl("/caiwu/undetails");
                            topNotify.setProjectId(cwProjectAck.getProjectId());
                            topNotify.setIncomeId(cwProjectAck.getId());
                            topNotify.setPhaseStatus("7");
                            topNotify.setDisposeUser(aLong);
                            topNotify.setViewFlag("0");
                            topNotify.setCreateBy(loginUser.getUsername());
                            topNotify.setCreateTime(now);
                            topNotify.setUpdateTime(now);
                            topNotifyMapper.insertTopNotify(topNotify);
                        }
                    }
                }
            }
        } else {
            //有需要打款的，则状态为 - 待出纳打款状态
            cwProjectIncome.setPhaseStatus("2");
            //流程表新增数据
            CwProjectDynamic cwProjectDynamic = new CwProjectDynamic();
            cwProjectDynamic.setProjectId(projectId);
            cwProjectDynamic.setIncomeId(cwProjectIncome.getId());
            cwProjectDynamic.setDynamicMsg("确认返费");
            cwProjectDynamic.setDynamicTime(now);
            cwProjectDynamic.setPhaseStatus("3");
            cwProjectDynamic.setOperId(loginUser.getUserId());
            cwProjectDynamic.setOperName(nickName);
            cwProjectDynamicMapper.insertCwProjectDynamic(cwProjectDynamic);
            //待办表新增数据(当前状态待处理人有几个就添加几条)

            //得到出纳代办人id
            List<Long> yewuList = cwProjectUserMapper.getUserIdListByProId(projectId, "1");


            //修改待确认返费的待办表数据状态
            topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "3", now, username);
           // todo 普通项目加入 待出纳打款 状态的提成毛利大于5万的期次给待办
            if ("2".equals(cwProjectIncome.getPhaseStatus())) {
                CwProjectIncome cwProjectIncome1 = cwProjectIncomeMapper.selectCwProjectIncomeById(cwProjectIncome.getId());
                CwProjectAck cwProjectAck = cwProjectIncomeMapper.selectCwProjectIncomeByProjectId(cwProjectIncome1.getProjectId());
                //得到业务代办人
                if (cwProjectAck != null) {
                    List<Long> hrUserList = sysRoleMapper.getUserIdListByRoleKey(8L);
                    if (hrUserList != null && hrUserList.size() > 0) {
                        for (Long aLong : hrUserList) {
                            TopNotify topNotify = new TopNotify();
                            topNotify.setNotifyModule("财务项目管理");
                            topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-待确认提成基数");
                            //刚插入为代办
                            topNotify.setNotifyType("1");
                            topNotify.setUrl("/caiwu/undetails");
                            topNotify.setProjectId(cwProjectAck.getProjectId());
                            topNotify.setIncomeId(cwProjectAck.getId());
                            topNotify.setPhaseStatus("7");
                            topNotify.setDisposeUser(aLong);
                            topNotify.setViewFlag("0");
                            topNotify.setCreateBy(loginUser.getUsername());
                            topNotify.setCreateTime(now);
                            topNotify.setUpdateTime(now);
                            topNotifyMapper.insertTopNotify(topNotify);
                        }
                    }
                }
            }
        }
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);

        //修改返费表状态
        String feeFlag = "2";
        cwProjectFeeMapper.updateCwProjectFeeByincomeId(feeFlag, cwProjectIncome.getId(), username);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addFeePayList(AddFeePay addFeePay, String username) {
        Long projectId = addFeePay.getProjectId();
        Long projectIncomeId = addFeePay.getProjectIncomeId();
        Long projectFeeId = addFeePay.getProjectFeeId();
        Long custId = addFeePay.getCustId();
        if (null != custId) {
            CwProjectFee cwProjectFee = new CwProjectFee();
            //说明有需要修改出返费公司或者返费公司的
            String custName = addFeePay.getCustName();
            String feeCustName = null;
            Long payCustId = null;
            if (custId == -999) {
                feeCustName = addFeePay.getFeeCustName();
                payCustId = addFeePay.getPayCustId();
            }
            cwProjectFee.setId(projectFeeId);
            cwProjectFee.setCustName(custName);
            cwProjectFee.setFeeCustName(feeCustName);
            cwProjectFee.setCustId(payCustId);
            cwProjectFeeMapper.updateCwProjectFee(cwProjectFee);
        }

        //通过收入表id查询返费表当前的所有返费金额
        //todo 财务项目管理四期，由之前的返费改为实付返费
        Map<String, Object> feeAmtMap = cwProjectFeeMapper.selectByIncomeId(addFeePay.getProjectIncomeId());
        //返费总金额
        BigDecimal feeAmt = new BigDecimal(feeAmtMap.get("feeAmt").toString());
        //修改主表
        //得到打款集合中的所有实际打款金额+抹平差额的合计
        //未结清
        BigDecimal subtract = new BigDecimal("0");

        List<CwProjectPay> payDataList = addFeePay.getPayDataList();

        Date now = new Date();
        //删除打款表相应信息
        cwProjectPayMapper.deleteByProidAndInc(projectId, projectIncomeId, projectFeeId);
//        todo 删除后重新打款，有一个打款，则改为部分打款状态
//        int i = 0;
        if (payDataList.size() != 0) {
            for (CwProjectPay cwProjectPay : payDataList) {
                cwProjectPay.setProjectId(projectId);
                cwProjectPay.setProjectIncomeId(projectIncomeId);
                cwProjectPay.setProjectFeeId(projectFeeId);
                cwProjectPay.setPayFlag("1");
                cwProjectPay.setCreateBy(username);
                cwProjectPay.setCreateTime(now);
                cwProjectPay.setUpdateTime(now);
                int i1 = cwProjectPayMapper.insertCwProjectPay(cwProjectPay);
//                i = i + i1;
            }
        }


        //查询打款表得到打款合计
        Map<String, Object> map = cwProjectFeeMapper.selectPayByIncomeId(addFeePay.getProjectIncomeId());
        //已打款总金额
        BigDecimal payAmt = new BigDecimal(map.get("payAmt").toString());

        if (feeAmt.compareTo(new BigDecimal(0)) == 1) {
            subtract = feeAmt.subtract(payAmt);
        }

//        todo 只要有一个打款，则状态改为部分打款
        CwProjectIncome cwProjectIncome = new CwProjectIncome();
//        if (i > 0) {
//            cwProjectIncome.setPhaseStatus("3");
//        }
        cwProjectIncome.setId(addFeePay.getProjectIncomeId());
        cwProjectIncome.setUpdateBy(username);
        cwProjectIncome.setUpdateTime(now);
        cwProjectIncome.setRemark(addFeePay.getRemark());
        cwProjectIncome.setUnfeeAmt(subtract);

        cwProjectIncome.setFeeAmt(payAmt);
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        return true;
    }

    @Override
    public List getyewuByIdList(CwProject cwProject) {
        List longs = cwProjectUserMapper.selectByprojectid(cwProject.getId());
        return longs;
    }

    @Override
    @Transactional
    public boolean querenDakuan(CwProjectIncome cwProjectIncome, LoginUser loginUser) {

        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(cwProjectIncome.getId());
        Long projectId = cwProject.getId();
        String projectName = cwProject.getProjectName();
        String nickName = loginUser.getUser().getNickName();
        String username = loginUser.getUsername();
        //修改收入表状态
        Date now = new Date();

        cwProjectIncome.setUpdateBy(username);
        cwProjectIncome.setUpdateTime(now);


        //流程表新增数据
        CwProjectDynamic cwProjectDynamic = new CwProjectDynamic();
        cwProjectDynamic.setProjectId(projectId);
        cwProjectDynamic.setIncomeId(cwProjectIncome.getId());
        cwProjectDynamic.setDynamicMsg("打款已完成");
        cwProjectDynamic.setDynamicTime(now);
        cwProjectDynamic.setPhaseStatus("5");
        cwProjectDynamic.setOperId(loginUser.getUserId());
        cwProjectDynamic.setOperName(nickName);
        cwProjectDynamicMapper.insertCwProjectDynamic(cwProjectDynamic);
        //待办表新增数据(当前状态待处理人有几个就添加几条)


        //修改待确认返费的待办表数据状态
        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "4", now, username);
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);


        if ("6".equals(cwProjectIncome.getPhaseStatus())) {
            CwProjectIncome cwProjectIncome1 = cwProjectIncomeMapper.selectCwProjectIncomeById(cwProjectIncome.getId());
            CwProjectAck cwProjectAck = cwProjectIncomeMapper.selectCwProjectIncomeByProjectId(cwProjectIncome1.getProjectId());
            //得到业务代办人
            if (cwProjectAck != null) {
                List<Long> hrUserList = sysRoleMapper.getUserIdListByRoleKey(8l);
                if (hrUserList != null && hrUserList.size() > 0) {
                    for (Long aLong : hrUserList) {
                        TopNotify topNotify = new TopNotify();
                        topNotify.setNotifyModule("财务项目管理");
//                        topNotify.setNotifyMsg(projectName + "-待确认提成基数");
                        topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-待确认提成基数");
                        //刚插入为代办
                        topNotify.setNotifyType("1");
                        topNotify.setUrl("/caiwu/undetails");
                        topNotify.setProjectId(cwProjectAck.getProjectId());
                        topNotify.setIncomeId(cwProjectAck.getId());
                        topNotify.setPhaseStatus("7");
                        topNotify.setDisposeUser(aLong);
                        topNotify.setViewFlag("0");
                        topNotify.setCreateBy(loginUser.getUsername());
                        topNotify.setCreateTime(now);
                        topNotify.setUpdateTime(now);
                        topNotifyMapper.insertTopNotify(topNotify);
                    }
                }
            }
        }
        return true;

    }

    @Override
    public Map<String, Object> addQiCiForLaw(CwProjectIncome cwProjectIncome, LoginUser loginUser) {
        Map<String, Object> returnMap = new HashMap<>();
        Long incomeId = null;
        boolean b = false;
        Date now = new Date();
        //获取项目名称
        CwProject cwProject = cwProjectMapper.selectCwProjectById(cwProjectIncome.getProjectId());
        String projectName = cwProject.getProjectName();
        String username = loginUser.getUsername();

        cwProjectIncome.setCreateBy(username);
        cwProjectIncome.setCreateTime(now);
        cwProjectIncome.setUpdateTime(now);
        cwProjectIncome.setPhaseStatus("7");
        //期次标识为0代表是法催项目的期次
        cwProjectIncome.setPhaseFlag("0");

        //查询数据库有没有期次为整月的并且期次相同的期次信息
        List<CwProjectIncome> cwProjectIncomes  = cwProjectIncomeMapper.selectCwProject(cwProjectIncome.getProjectId(),cwProjectIncome.getTerm(),cwProjectIncome.getTermMonth());
        if("0".equals(cwProjectIncome.getTerm())) {
            if (cwProjectIncomes.size()==0) {
                incomeId = this.addCWIncomeLaw(cwProjectIncome, projectName, username);
                b = true;
            } else {
                b = false;
            }
        } else if ("1".equals(cwProjectIncome.getTerm())) {

            boolean add = this.isAdd(cwProjectIncome, cwProjectIncomes);
            if (add) {
                incomeId = this.addCWIncomeLaw(cwProjectIncome, projectName, username);
                b = true;
            } else {
                b = false;
            }
        }
        returnMap.put("isok", b);
        returnMap.put("incomeId", incomeId);
        return returnMap;

//        return b;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long addCWIncomeLaw(CwProjectIncome cwProjectIncome, String projectName, String username) {
        Date now = new Date();
        cwProjectIncomeMapper.insertCwProjectIncome(cwProjectIncome);
        Long incomeId = cwProjectIncome.getId();
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(cwProjectIncome.getId());
        //待办表新增数据(当前状态待处理人有几个就添加几条)
        //得到会计代办人id
//        List<Long> kuaijiID = cwProjectUserMapper.getUserIdListByProId(cwProjectIncome.getProjectId(), "0");
        //得到出纳代办人id
        CwProject cwProject = cwProjectMapper.selectCwProjectById(cwProjectIncome.getProjectId());
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(cwProject.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
        List<Long> chunaId = userList.stream().filter(t -> "2".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList());
//        List<Long> chunaId = cwProjectUserMapper.getUserIdListByProId(cwProjectIncome.getProjectId(), "1");

        for (Long aLong2 : chunaId) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyModule("财务项目管理");
            topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-法催项目-录入收入与返费");
            //刚插入为代办
            topNotify.setNotifyType("1");
            topNotify.setUrl("/caiwu/projectDetailsLaw");
            topNotify.setProjectId(cwProjectIncome.getProjectId());
            topNotify.setIncomeId(cwProjectIncome.getId());
            topNotify.setPhaseStatus("5");
            topNotify.setDisposeUser(aLong2);
            topNotify.setViewFlag("0");
            topNotify.setCreateBy(username);
            topNotify.setCreateTime(now);
            topNotify.setUpdateTime(now);
            topNotifyMapper.insertTopNotify(topNotify);
        }
        // 添加提成基数确认及返费关联表数据
        CwProjectAck cwProjectAck = cwProjectAckMapper.selectCwProjectAckByProjectId(cwProjectIncome.getProjectId());
        if (ObjectUtils.isEmpty(cwProjectAck)) {
            cwProjectAck = new CwProjectAck();
            cwProjectAck.setProjectId(cwProjectIncome.getProjectId());
            cwProjectAck.setAckFlag("0");
            cwProjectAck.setStatus("0");
            cwProjectAck.setCreateBy(username);
            cwProjectAck.setCreateTime(now);
            cwProjectAck.setUpdateBy(username);
            cwProjectAck.setUpdateTime(now);
            cwProjectAckMapper.insertCwProjectAck(cwProjectAck);
        }
        CwProjectAckRef cwProjectAckRef = new CwProjectAckRef();
        cwProjectAckRef.setAckId(cwProjectAck.getId());
        cwProjectAckRef.setStatus("0");
        cwProjectAckRef.setCreateBy(username);
        cwProjectAckRef.setCreateTime(now);
        cwProjectAckRef.setUpdateBy(username);
        cwProjectAckRef.setUpdateTime(now);
        cwProjectAckRef.setProjectIncomeId(cwProjectIncome.getId());
        cwProjectAckRefMapper.insertCwProjectAckRef(cwProjectAckRef);
        return incomeId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addIncomeForLaw(List<CwProjectIncomeForLawDto> cwProjectIncomeForLawDtoList, LoginUser loginUser) {
        Stream<CwProjectIncomeForLawDto> cwProjectIncomeForLawDtoStream = cwProjectIncomeForLawDtoList.stream().filter(t -> t.getServiceProviderJtfrAmt() != null);
        cwProjectIncomeForLawDtoStream.forEach(t -> {
            t.setJtfrAmt(t.getServiceProviderJtfrAmt());
        });
        //todo 分组然后进行判断
        Map<Integer, List<CwProjectIncomeForLawDto>> collect = cwProjectIncomeForLawDtoList.stream().collect(Collectors.groupingBy(CwProjectIncomeForLawDto::getHebingId));
        //判断每个list的大小是否为1，为1，则这个服务商收入返费过程只有一个一级服务商，则正常该挂起挂起
        //如果不为1，那么就给这个过程中的一级服务商挂起标识设置为0
        collect.forEach((k, v) -> {
            if (v.size() != 1) {
                List<CwProjectIncomeForLawDto> collect1 = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).collect(Collectors.toList());
                //每个过程只有一个一级服务商，所以直接get索引0得到一级服务商，然后给挂起标识设为0
                collect1.get(0).setSuspendFlag("0");
            }
        });
        BigDecimal zero = new BigDecimal("0.00");
        //找到期次
        CwProjectIncome cwProjectIncome = cwProjectIncomeMapper.selectCwProjectIncomeById(cwProjectIncomeForLawDtoList.get(0).getPhaseId());
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(cwProjectIncome.getId());
        //更新期次状态
        cwProjectIncome.setPhaseStatus("8");
        cwProjectIncome.setGrossProfitAmt(cwProjectIncomeForLawDtoList.get(0).getGrossProfitAmt());
        cwProjectIncome.setGrossProfitAmt2(cwProjectIncomeForLawDtoList.get(0).getGrossProfitAmt2());
        cwProjectIncome.setRemark(cwProjectIncomeForLawDtoList.get(0).getRemark());
        cwProjectIncome.setCollectionTime(cwProjectIncomeForLawDtoList.get(0).getCollectionTime());
        Map<Integer, BigDecimal> trueComeAmt = cwProjectIncomeForLawDtoList.stream().filter(t -> !("1".equals(t.getServiceProviderFlag()) && t.getTrueComeAmt() == null)).collect(Collectors.groupingBy(CwProjectIncomeForLawDto::getHebingId, Collectors.reducing(BigDecimal.ZERO, CwProjectIncomeForLawDto::getTrueComeAmt, BigDecimal::add)));
        Map<Integer, BigDecimal> serviceFee = cwProjectIncomeForLawDtoList.stream().filter(t -> !("1".equals(t.getServiceProviderFlag()) && t.getServiceFee() == null)).collect(Collectors.groupingBy(CwProjectIncomeForLawDto::getHebingId, Collectors.reducing(BigDecimal.ZERO, CwProjectIncomeForLawDto::getServiceFee, BigDecimal::add)));
        Map<Integer, BigDecimal> principal = cwProjectIncomeForLawDtoList.stream().filter(t -> !("1".equals(t.getServiceProviderFlag()) && t.getPrincipal() == null)).collect(Collectors.groupingBy(CwProjectIncomeForLawDto::getHebingId, Collectors.reducing(BigDecimal.ZERO, CwProjectIncomeForLawDto::getPrincipal, BigDecimal::add)));
        //todo 修复法催项目 当一个期次有一二级服务商时，二级服务商有挂起时，不计入
        Date nowDate = DateUtils.getNowDate();
        //数据分别插入收入表和返费表
        for (CwProjectIncomeForLawDto dto:cwProjectIncomeForLawDtoList) {
            if ("1".equals(dto.getServiceProviderFlag())) {
                //服务商标识
                dto.setTrueComeAmt(trueComeAmt.get(dto.getHebingId()));
                dto.setServiceFee(serviceFee.get(dto.getHebingId()));
                dto.setPrincipal(principal.get(dto.getHebingId()));
            }
            //组装收入表对象
            //9 - 代表法催项目的收入信息
            dto.setTerm("9");
            //法催项目的期次标识
            dto.setPhaseFlag("1");
            dto.setCreateBy(loginUser.getUsername());
            dto.setCreateTime(nowDate);
            dto.setUpdateTime(nowDate);
            //对真实回款金额、服务费、本金进行两位取整
            if (dto.getTrueComeAmt() != null) {
                dto.setTrueComeAmt(dto.getTrueComeAmt().setScale(2, RoundingMode.DOWN));
            }
            if (dto.getServiceFee() != null) {
                dto.setServiceFee(dto.getServiceFee().setScale(2, RoundingMode.DOWN));
            }
            if (dto.getPrincipal() != null) {
                dto.setPrincipal(dto.getPrincipal().setScale(2, RoundingMode.DOWN));
            }
            //收入表入库
            Long row1 = cwProjectIncomeMapper.insertLawIncome(dto);
            //组装返费表对象
            //公共的字段
            if ("-".equals(dto.getFeeRound())) {
                dto.setFeeRoundDecimal(zero);
            } else {
                dto.setFeeRoundDecimal(new BigDecimal(dto.getFeeRound()).setScale(2, RoundingMode.DOWN));
            }
            if (dto.getSuspendFlag() == null) {
                if (dto.getFeeAmt().compareTo(BigDecimal.ZERO) == 0) {
                    //如果返费为0，也无需打款
                    dto.setFeeAmt2Decimal(new BigDecimal(dto.getFeeAmt2()).setScale(2, RoundingMode.DOWN));
                    dto.setSuspendFlag("0");
                    //代表该返费已录入，不需打款
                    dto.setFeeFlag("3");
                }
                if (dto.getCurrentFee().compareTo(BigDecimal.ZERO) < 0) {
                    //如果返费取整为"-"，说明返费是负数，在这里把返费挂起标识
                    dto.setFeeRoundDecimal(zero);
                    dto.setFeeAmt2Decimal(zero);
                    dto.setSuspendFlag("1");
                    //代表该返费已录入，不需打款
                    dto.setFeeFlag("3");
                } else {
//                dto.setFeeRoundDecimal(new BigDecimal(dto.getFeeRound()));
                    dto.setFeeAmt2Decimal(new BigDecimal(dto.getFeeAmt2()).setScale(2, RoundingMode.DOWN));
                    dto.setSuspendFlag("0");
                    //代表该返费已录入
                    dto.setFeeFlag("1");
                }
            } else {
                if (dto.getFeeAmt().compareTo(BigDecimal.ZERO) == 0) {
                    //如果返费为0，也无需打款
                    dto.setFeeAmt2Decimal(new BigDecimal(dto.getFeeAmt2()).setScale(2, RoundingMode.DOWN));
                    //代表该返费已录入，不需打款
                    dto.setFeeFlag("3");
                }
                if (dto.getCurrentFee().compareTo(BigDecimal.ZERO) < 0) {
                    //如果返费取整为"-"，说明返费是负数，在这里把返费挂起标识
                    dto.setFeeRoundDecimal(zero);
                    dto.setFeeAmt2Decimal(zero);
                    //代表该返费已录入，不需打款
                    dto.setFeeFlag("3");
                } else {
//                dto.setFeeRoundDecimal(new BigDecimal(dto.getFeeRound()));
                    dto.setFeeAmt2Decimal(new BigDecimal(dto.getFeeAmt2()).setScale(2, RoundingMode.DOWN));
                    //代表该返费已录入
                    dto.setFeeFlag("1");
                }
            }
            if (dto.getGuaqiqingchuFlag() == 1) {
                //代表本期有清除之前挂起的，有清除挂起的话，也就是挂起的被算完了。就看返费的大小
                //返费表入库
                int row2 = cwProjectFeeMapper.insertLawFee(dto);
                if (row2 > 0) {
                    //得到插入的id
                    Long feeId = dto.getFeeId();
                    //先更新被清除掉的挂起的返费的状态
                    String feeAmtSuspendFlagIsOneId = dto.getFeeAmtSuspendFlagIsOneId();
                    String[] split = feeAmtSuspendFlagIsOneId.split(",");
                    for (String s : split) {
                        //把id转成Long然后修改状态
                        Long id = Long.parseLong(s);
                        CwProjectFee cwProjectFee = new CwProjectFee();
                        cwProjectFee.setId(id);
                        cwProjectFee.setSuspendFlag("2");
                        cwProjectFee.setSuspendClearId(feeId);
                        int row = cwProjectFeeMapper.updateCwProjectFee(cwProjectFee);
                    }
                }
            } else {
                //本期不涉及之前挂起的，就看本期返费。本期返费也就是返费
                int row2 = cwProjectFeeMapper.insertLawFee(dto);
            }
        }
        //计算期次的总收入
        BigDecimal qiciIncomeSum = cwProjectIncomeForLawDtoList.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectIncomeForLawDto::getServiceProviderIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        cwProjectIncome.setIncomeAmt(qiciIncomeSum);

        int i = cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);

        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        //流程表新增数据
        CwProjectDynamic cwProjectDynamic = new CwProjectDynamic();
        cwProjectDynamic.setProjectId(cwProject.getId());
        cwProjectDynamic.setIncomeId(cwProjectIncome.getId());
        cwProjectDynamic.setDynamicMsg("录入收入与返费");
        cwProjectDynamic.setDynamicTime(nowDate);
        cwProjectDynamic.setPhaseStatus("6");
        cwProjectDynamic.setOperId(loginUser.getUserId());
        cwProjectDynamic.setOperName(loginUser.getUser().getNickName());
        cwProjectDynamicMapper.insertCwProjectDynamic(cwProjectDynamic);
        //待办表新增数据(当前状态待处理人有几个就添加几条)

        //得到会计代办人id
        CwProject cwProject11 = cwProjectMapper.selectCwProjectById(cwProject.getId());
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(cwProject11.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
        List<Long> kuaijiId = userList.stream().filter(t -> "1".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList());
//        List<Long> kuaijiId = cwProjectUserMapper.getUserIdListByProId(cwProjectIncome.getProjectId(), "0");
        for (Long aLong : kuaijiId) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyModule("财务项目管理");
            topNotify.setNotifyMsg(cwProject.getProjectName() + "-" + phaseString + "期次" + "-法催项目-确认收入与返费");
            //刚插入为代办
            topNotify.setNotifyType("1");
            topNotify.setUrl("/caiwu/projectDetailsLaw");
            topNotify.setProjectId(cwProject.getId());
            topNotify.setIncomeId(cwProjectIncome.getId());
            topNotify.setPhaseStatus("6");
            topNotify.setDisposeUser(aLong);
            topNotify.setViewFlag("0");
            topNotify.setCreateBy(loginUser.getUsername());
            topNotify.setCreateTime(nowDate);
            topNotify.setUpdateTime(nowDate);
            topNotifyMapper.insertTopNotify(topNotify);
        }
        //修改待录入收入的待办表数据状态
        topNotifyMapper.updateByInmAndProAndStat(cwProject.getId(), cwProjectIncome.getId(), "5", nowDate, loginUser.getUsername());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean querenIncomeAndFeeForLaw(CwProjectIncome cwProjectIncome, LoginUser loginUser) {
        //通过期次id找期次
        cwProjectIncome = cwProjectIncomeMapper.selectCwProjectIncomeById(cwProjectIncome.getId());
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(cwProjectIncome.getId());
        //通过传入的期次id查项目
        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        Long projectId = cwProject.getId();
        String projectName = cwProject.getProjectName();
        String userName = loginUser.getUsername();
        String nickName = loginUser.getUser().getNickName();
        //修改收入表状态
        Date now = new Date();


        //通过期次id查询该期次返费表当前的所有返费金额
        List<LawProjectFeeIdAndFeeAmt> feeAmt = cwProjectFeeMapper.selectLawFeeByIncomeId(cwProjectIncome.getId());
        //原型标注：返费未结清，应打款金额均取返费取整金额。
        BigDecimal feeAmtSum = feeAmt.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(LawProjectFeeIdAndFeeAmt::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);

        //放入本期次返费未结清金额
        cwProjectIncome.setUnfeeAmt(feeAmtSum);
        cwProjectIncome.setUpdateBy(userName);
        cwProjectIncome.setUpdateTime(now);
        cwProjectIncome.setPhaseStatus("9");
        //流程表新增数据
        CwProjectDynamic cwProjectDynamic = new CwProjectDynamic();
        cwProjectDynamic.setProjectId(projectId);
        cwProjectDynamic.setIncomeId(cwProjectIncome.getId());
        cwProjectDynamic.setDynamicMsg("已完成");
        cwProjectDynamic.setDynamicTime(now);
        cwProjectDynamic.setPhaseStatus("7");
        cwProjectDynamic.setOperId(loginUser.getUserId());
        cwProjectDynamic.setOperName(nickName);
        cwProjectDynamicMapper.insertCwProjectDynamic(cwProjectDynamic);
        //修改待确认返费的待办表数据状态
        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "8", now, userName);
        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "6", now, userName);

        CwProjectAck cwProjectAck = cwProjectIncomeMapper.selectLawCwProjectIncomeByProjectId(cwProject.getId());
//                CwProjectAck cwProjectAck = cwProjectIncomeMapper.selectCwProjectIncomeByProjectId(cwProject.getId());
        //得到业务代办人
        if (cwProjectAck != null) {
            List<Long> hrUserList = sysRoleMapper.getUserIdListByRoleKey(8L);
            if (hrUserList != null && hrUserList.size() > 0) {
                for (Long aLong : hrUserList) {
                    TopNotify topNotify = new TopNotify();
                    topNotify.setNotifyModule("财务项目管理");
                    topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-法催项目-待确认提成基数");
                    //刚插入为代办
                    topNotify.setNotifyType("1");
                    topNotify.setUrl("/caiwu/undetails");
                    topNotify.setProjectId(cwProjectAck.getProjectId());
                    topNotify.setIncomeId(cwProjectAck.getId());
                    topNotify.setPhaseStatus("9");
                    topNotify.setDisposeUser(aLong);
                    topNotify.setViewFlag("0");
                    topNotify.setCreateBy(loginUser.getUsername());
                    topNotify.setCreateTime(now);
                    topNotify.setUpdateTime(now);
                    topNotifyMapper.insertTopNotify(topNotify);
                }
            }
        }






        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);

        //返费等于0，不需要打款
        List<LawProjectFeeIdAndFeeAmt> collect1 = feeAmt.stream().filter(t -> t.getFeeAmt().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        //返费大于0，需要打款
        List<LawProjectFeeIdAndFeeAmt> collect2 = feeAmt.stream().filter(t -> t.getFeeAmt().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (collect1.size() > 0) {
            //修改返费表状态
            String feeFlag1 = "3";
            //批量修改返费表的状态
            cwProjectFeeMapper.updateCwProjectLawFeeByincomeId(feeFlag1, collect1, userName);
        }
        if (collect2.size() > 0) {
            //修改返费表状态
            String feeFlag2 = "2";
            //批量修改返费表的状态
            cwProjectFeeMapper.updateCwProjectLawFeeByincomeId(feeFlag2, collect2, userName);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addLawFeePayList(AddFeePay addFeePay, String username) {
        Long projectId = addFeePay.getProjectId();
        Long projectIncomeId = addFeePay.getProjectIncomeId();
        Long projectFeeId = addFeePay.getProjectFeeId();

        //通过收入id查询到该期次id，然后找该期次返费表当前的所有返费金额
        List<BigDecimal> feeAmtList = cwProjectFeeMapper.selectLawAllFeeByPhaseId(addFeePay.getPhaseId());
        //返费总金额
        BigDecimal feeAmt = feeAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        //修改主表
        //得到打款集合中的所有实际打款金额+抹平差额的合计
        //未结清
        BigDecimal subtract = new BigDecimal("0.00");

        List<CwProjectPay> payDataList = addFeePay.getPayDataList();

        Date now = new Date();
        //删除打款表相应信息
        cwProjectPayMapper.deleteByProidAndInc(projectId, projectIncomeId, projectFeeId);
        if (payDataList.size() != 0) {
            for (CwProjectPay cwProjectPay : payDataList) {
                cwProjectPay.setProjectId(projectId);
                cwProjectPay.setProjectIncomeId(projectIncomeId);
                cwProjectPay.setProjectFeeId(projectFeeId);
                cwProjectPay.setPayFlag("1");
                cwProjectPay.setCreateBy(username);
                cwProjectPay.setCreateTime(now);
                cwProjectPay.setUpdateTime(now);
                cwProjectPayMapper.insertCwProjectPay(cwProjectPay);
            }
        }


        //查询打款表得到打款合计
        Map<String, Object> map = cwProjectFeeMapper.selectLawAllPayByPhaseId(addFeePay.getPhaseId());
        //已打款总金额
        BigDecimal payAmt = new BigDecimal(map.get("payAmt").toString());

        if (feeAmt.compareTo(new BigDecimal("0")) == 1) {
            subtract = feeAmt.subtract(payAmt);
        }

        //找到期次表，更新期次的信息。
        CwProjectIncome cwProjectIncome = cwProjectIncomeMapper.selectPhaseLawByIncomeId(projectIncomeId);
        cwProjectIncome.setUpdateBy(username);
        cwProjectIncome.setUpdateTime(now);
        cwProjectIncome.setRemark(addFeePay.getRemark());
        cwProjectIncome.setUnfeeAmt(subtract);

        cwProjectIncome.setFeeAmt(payAmt);
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean querenDakuanFowLaw(CwProjectIncome cwProjectIncome, LoginUser loginUser) {
        //通过期次id找期次
        CwProjectIncome phase = cwProjectIncomeMapper.selectCwProjectIncomeById(cwProjectIncome.getId());
        CwProject cwProject = cwProjectMapper.selectByIncomeId(phase.getId());
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(cwProjectIncome.getId());
        Long projectId = cwProject.getId();
        String projectName = cwProject.getProjectName();
        String nickName = loginUser.getUser().getNickName();
        String username = loginUser.getUsername();
        String phaseStatus = cwProjectIncome.getPhaseStatus();
        //修改收入表状态
        Date now = new Date();

        phase.setUpdateBy(username);
        phase.setUpdateTime(now);
        phase.setPhaseStatus(phaseStatus);


        //流程表新增数据
        CwProjectDynamic cwProjectDynamic = new CwProjectDynamic();
        cwProjectDynamic.setProjectId(projectId);
        cwProjectDynamic.setIncomeId(phase.getId());
        cwProjectDynamic.setDynamicMsg("打款已完成");
        cwProjectDynamic.setDynamicTime(now);
        cwProjectDynamic.setPhaseStatus("8");
        cwProjectDynamic.setOperId(loginUser.getUserId());
        cwProjectDynamic.setOperName(nickName);
        cwProjectDynamicMapper.insertCwProjectDynamic(cwProjectDynamic);
        //待办表新增数据(当前状态待处理人有几个就添加几条)


        //修改待确认返费的待办表数据状态
        topNotifyMapper.updateByInmAndProAndStat(projectId, phase.getId(), "8", now, username);
        cwProjectIncomeMapper.updateCwProjectIncome(phase);


        if ("10".equals(phaseStatus)) {
            CwProjectAck cwProjectAck = cwProjectIncomeMapper.selectLawCwProjectIncomeByProjectId(cwProject.getId());
//            CwProjectAck cwProjectAck = cwProjectIncomeMapper.selectCwProjectIncomeByProjectId(cwProject.getId());
            //得到业务代办人
            if (cwProjectAck != null) {
                List<Long> hrUserList = sysRoleMapper.getUserIdListByRoleKey(8L);
                if (hrUserList != null && hrUserList.size() > 0) {
                    for (Long aLong : hrUserList) {
                        TopNotify topNotify = new TopNotify();
                        topNotify.setNotifyModule("财务项目管理");
                        topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-法催项目-待确认提成基数");
                        //刚插入为代办
                        topNotify.setNotifyType("1");
                        topNotify.setUrl("/caiwu/undetails");
                        topNotify.setProjectId(cwProjectAck.getProjectId());
                        topNotify.setIncomeId(cwProjectAck.getId());
                        topNotify.setPhaseStatus("9");
                        topNotify.setDisposeUser(aLong);
                        topNotify.setViewFlag("0");
                        topNotify.setCreateBy(loginUser.getUsername());
                        topNotify.setCreateTime(now);
                        topNotify.setUpdateTime(now);
                        topNotifyMapper.insertTopNotify(topNotify);
                    }
                }
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCwProjectIncomeLaw(CwProjectIncome cwProjectIncome) {
        Date nowDate = DateUtils.getNowDate();
        //输入期次的状态
        cwProjectIncome.setStatus("1");
        cwProjectIncome.setUpdateTime(nowDate);
        //查找期次下面的收入
        List<CwProjectIncome> incomeList = cwProjectIncomeMapper.selectLawIncomeByPhaseId(cwProjectIncome.getId());
        incomeList.forEach(t -> {
            t.setStatus("1");
            t.setUpdateTime(nowDate);
        });
        int i = 0;
        //更新期次
        i = cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        if (i > 0) {
            if (incomeList.size() > 0) {
                int a = cwProjectIncomeMapper.updateLawCwProjectIncome(incomeList);
                i = i + a;
            }
        }
        return i;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteCwProjectIncomeByPhaseId(Long phaseId) {
        //通过期次id找对应的返费id
        List<Long> feeIds = cwProjectIncomeMapper.selectCwprojectFeeIdListByPhase(phaseId);
        //流程表中把期次id和状态为6的给删掉
        int q = cwProjectDynamicMapper.deleteCwProjectDynamicByIncomeIdAndPhaseStatus(phaseId, "6");
        int w = 0;
        int i = 0;
        if (q > 0) {
            w = topNotifyMapper.deleteTopNotifyByByIncomeIdAndPhaseStatus(phaseId, "6");
        }
        if (w > 0) {
            i = cwProjectIncomeMapper.deleteCwProjectIncomeByPhaseId(phaseId);
        }
        //todo 删除返费之前，如果有挂起的清零，那么就把清零id给清除掉，状态改回来
        int a = cwProjectIncomeMapper.updateLawCwProjectFeeSuspendSumByFeeIds(feeIds);
        if (i > 0) {
            i = cwProjectIncomeMapper.deleteCwProjectFeeByFeeIds(feeIds);
        }
        //组装一个收入id和返费id的 两个集合
        return i;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectionIncome(CwProjectIncome cwProjectIncome, LoginUser loginUser) {
        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(cwProjectIncome.getId());
        if ("1".equals(cwProject.getPrestoreIncomeFlag())) {
            //驳回，直接删除之前存的抵扣的金额
            //通过项目id和收款时间来删
            String dateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, cwProjectIncome.getCollectionTimeBefore());
            CwProjectPrestoreIncome cwProjectPrestoreIncome = cwProjectCustMapper.selectCwProjectPrestoreIncomeByProjectIncomeIdAndDeductionIncomeDate(cwProjectIncome.getId(), dateStr);
            List<Long> ids = new ArrayList<>();
            ids.add(cwProjectPrestoreIncome.getId());
            int i = cwProjectCustMapper.deleteCwProjectPrestoreIncomeById(ids);
        }
        Long projectId = cwProject.getId();
        String projectName = cwProject.getProjectName();
        String username = loginUser.getUsername();
        Date now = new Date();
        cwProjectIncome.setUpdateBy(loginUser.getUsername());
        cwProjectIncome.setUpdateTime(now);
        String nickName = loginUser.getUser().getNickName();
        //驳回表入库，因为每个期次可以有多个驳回原因
        CwProjectRejection cwProjectRejection = new CwProjectRejection();
        cwProjectRejection.setIncomeId(cwProjectIncome.getId());
        cwProjectRejection.setParameterCode("3");
        cwProjectRejection.setRejectionTime(now);
        cwProjectRejection.setRejectionUserName(nickName);
        cwProjectRejection.setRejectionReason(cwProjectIncome.getIncomeRejectionReason());
        cwProjectRejectionMapper.insertCwProjectRejection(cwProjectRejection);


        topNotifyMapper.updateTopNotifyByProjectIdAndIncomeIdAndPhaseStatus(projectId, cwProjectIncome.getId(), "1");
        //得到会计代办人id
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(cwProject.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
        List<Long> kuaijiID = userList.stream().filter(t -> "1".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList());
//        List<Long> kuaijiID = cwProjectUserMapper.getUserIdListByProId(projectId, "0");
        //得到出纳代办人id
//        List<Long> chunaId = cwProjectUserMapper.getUserIdListByProId(projectId, "1");
        for (Long aLong : kuaijiID) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyModule("财务项目管理");
            topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-金额被驳回，待修改");
            //刚插入为代办
            topNotify.setNotifyType("1");
            topNotify.setUrl("/caiwu/projectDetails");
            topNotify.setProjectId(projectId);
            topNotify.setIncomeId(cwProjectIncome.getId());
            topNotify.setPhaseStatus("10");
            topNotify.setDisposeUser(aLong);
            topNotify.setViewFlag("0");
            topNotify.setCreateBy(username);
            topNotify.setCreateTime(now);
            topNotify.setUpdateTime(now);
            topNotifyMapper.insertTopNotify(topNotify);
        }


        //修改待确认收入的待办表数据状态
//        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "1", now, username);
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        List<CwProjectDynamic> cwProjectDynamic = cwProjectDynamicMapper.selectCwProjectDynamicByIncomeIdAndPhaseStatus(cwProjectIncome.getId(), "1");
        if (cwProjectDynamic.size() == 0) {
            CwProjectDynamic cwProjectDynamic1 = new CwProjectDynamic();
            cwProjectDynamic1.setProjectId(projectId);
            cwProjectDynamic1.setIncomeId(cwProjectIncome.getId());
            cwProjectDynamic1.setDynamicMsg("确认金额");
            cwProjectDynamic1.setDynamicTime(now);
            cwProjectDynamic1.setPhaseStatus("1");
            cwProjectDynamic1.setOperId(loginUser.getUserId());
            cwProjectDynamic1.setOperName(nickName);
            cwProjectDynamicMapper.insertCwProjectDynamic(cwProjectDynamic1);
        } else {
            cwProjectDynamicMapper.updateCwProjectDynamicOperNameAndDynamicTimeByIncomeIdAndPhaseStatus(nickName, now, cwProjectIncome.getId(), "1");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateIncomeSubmit(AddCwProjectIncomeAndFee addCwProjectIncomeAndFee, LoginUser loginUser) {
        //要用到的主表id
        Long projectId = addCwProjectIncomeAndFee.getAddfeeVO().getProjectId();
        //收入表对象
        CwProjectIncome cwProjectIncome = addCwProjectIncomeAndFee.getCwProjectIncome();
        //要修改的income表主键
        Long incomeId = cwProjectIncome.getId();
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(incomeId);
        //修改的收入
        BigDecimal incomeAmt = cwProjectIncome.getIncomeAmt();
        //修改的毛利
        BigDecimal maoli = addCwProjectIncomeAndFee.getAddfeeVO().getMaoli();
        //修改的提成毛利
        BigDecimal tichengmaoli = addCwProjectIncomeAndFee.getAddfeeVO().getTichengmaoli();
        //修改的备注
        String remark = addCwProjectIncomeAndFee.getAddfeeVO().getRemark();
        //查主表
        CwProject cwProject = cwProjectMapper.selectCwProjectById(projectId);
//        cwProjectIncome.setRejectionFlag(StringUtils.EMPTY);
        Date now = new Date();

        String projectName = cwProject.getProjectName();
        String userName = loginUser.getUsername();
        String nickName = loginUser.getUser().getNickName();
        if ("1".equals(cwProject.getPrestoreIncomeFlag())) {
            //入库
            CwProjectPrestoreIncome cwProjectPrestoreIncome = new CwProjectPrestoreIncome();
            cwProjectPrestoreIncome.setProjectId(projectId);
            cwProjectPrestoreIncome.setProjectIncomeId(incomeId);
            cwProjectPrestoreIncome.setFlag("1");
            cwProjectPrestoreIncome.setDeductionIncomeDate(cwProjectIncome.getCollectionTime());
            cwProjectPrestoreIncome.setDeductionIncomeAmt(incomeAmt);
            cwProjectPrestoreIncome.setStatus("0");
            cwProjectPrestoreIncome.setCreateBy(nickName);
            cwProjectPrestoreIncome.setCreateTime(now);
            cwProjectPrestoreIncome.setUpdateBy(nickName);
            cwProjectPrestoreIncome.setUpdateTime(now);
            int i = cwProjectCustMapper.insertPrestoreIncome(cwProjectPrestoreIncome);
//            int rows = cwProjectCustMapper.updateCwProjectPrestoreIncomeByProjectIncomeId(cwProjectPrestoreIncome);
        }
        //组装要修改的income对象
        cwProjectIncome.setGrossProfitAmt(maoli);
        cwProjectIncome.setGrossProfitAmt2(tichengmaoli);
        cwProjectIncome.setRemark(remark);
        cwProjectIncome.setUpdateBy(userName);
        cwProjectIncome.setUpdateTime(now);
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        //修改返费流程
        //根据收入id和项目id删除返费
        cwProjectFeeMapper.deleteFeeByInAndPro(projectId, incomeId);
        //新增返费表
        List<CwProjectFee> fee = addCwProjectIncomeAndFee.getAddfeeVO().getFee();
        for (CwProjectFee cwProjectFee:fee) {
            //循环放入项目、收入id 名字 创建时间 创建人 修改时间
            if ("暂不确定公司".equals(cwProjectFee.getFeeCustName())) {
                cwProjectFee.setCustId(-999L);
                cwProjectFee.setCustRemark(addCwProjectIncomeAndFee.getCustRemark());
            }
            cwProjectFee.setProjectId(projectId);
            cwProjectFee.setProjectIncomeId(incomeId);
            cwProjectFee.setFeeFlag("1");
            cwProjectFee.setCreateBy(userName);
            cwProjectFee.setCreateTime(now);
            cwProjectFee.setUpdateBy(userName);
            cwProjectFee.setUpdateTime(now);
            cwProjectFeeMapper.insertCwProjectFee(cwProjectFee);
        }
        //得到业务代办人id
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(cwProject.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
        List<Long> yewuList = userList.stream().filter(t -> "3".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList());
//        List<Long> yewuList = cwProjectUserMapper.getUserIdListByProId(projectId, "2");
        for (Long aLong : yewuList) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyModule("财务项目管理");
            topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-待确认金额");
            //刚插入为代办
            topNotify.setNotifyType("1");
            topNotify.setUrl("/caiwu/projectDetails");
            topNotify.setProjectId(projectId);
            topNotify.setIncomeId(cwProjectIncome.getId());
            topNotify.setPhaseStatus("1");
            topNotify.setDisposeUser(aLong);
            topNotify.setViewFlag("0");
            topNotify.setCreateBy(userName);
            topNotify.setCreateTime(now);
            topNotify.setUpdateTime(now);
            topNotifyMapper.insertTopNotify(topNotify);
        }
        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "10", now, userName);
        cwProjectDynamicMapper.updateCwProjectDynamicOperNameAndDynamicTimeByIncomeIdAndPhaseStatus(nickName, now, cwProjectIncome.getId(), "0");
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectionFee(CwProjectIncome cwProjectIncome, LoginUser loginUser) {
        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(cwProjectIncome.getId());
        Long projectId = cwProject.getId();
        String projectName = cwProject.getProjectName();
        String username = loginUser.getUsername();
        Date now = new Date();
        cwProjectIncome.setUpdateBy(loginUser.getUsername());
        cwProjectIncome.setUpdateTime(now);
        String nickName = loginUser.getUser().getNickName();
        //驳回表入库，因为每个期次可以有多个驳回原因
        CwProjectRejection cwProjectRejection = new CwProjectRejection();
        cwProjectRejection.setIncomeId(cwProjectIncome.getId());
        cwProjectRejection.setParameterCode("1");
        cwProjectRejection.setRejectionTime(now);
        cwProjectRejection.setRejectionUserName(nickName);
        cwProjectRejection.setRejectionReason(cwProjectIncome.getFeeRejectionReason());
        cwProjectRejectionMapper.insertCwProjectRejection(cwProjectRejection);
//        cwProjectIncome.setFeeRejectionTime(now);
//        cwProjectIncome.setFeeRejectionUser(nickName);
        topNotifyMapper.updateTopNotifyByProjectIdAndIncomeIdAndPhaseStatus(projectId, cwProjectIncome.getId(), "3");
        //得到会计代办人id
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(cwProject.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
        List<Long> kuaijiID = userList.stream().filter(t -> "1".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList());
//        List<Long> kuaijiID = cwProjectUserMapper.getUserIdListByProId(projectId, "0");
        for (Long aLong : kuaijiID) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyModule("财务项目管理");
            topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-返费金额被驳回，待修改");
            //刚插入为代办
            topNotify.setNotifyType("1");
            topNotify.setUrl("/caiwu/projectDetails");
            topNotify.setProjectId(projectId);
            topNotify.setIncomeId(cwProjectIncome.getId());
            topNotify.setPhaseStatus("11");
            topNotify.setDisposeUser(aLong);
            topNotify.setViewFlag("0");
            topNotify.setCreateBy(username);
            topNotify.setCreateTime(now);
            topNotify.setUpdateTime(now);
            topNotifyMapper.insertTopNotify(topNotify);
        }

        //修改待确认收入的待办表数据状态
//        topNotifyMapper.updateByInmAndProAndStat(projectId, cwProjectIncome.getId(), "1", now, username);
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        List<CwProjectDynamic> cwProjectDynamic = cwProjectDynamicMapper.selectCwProjectDynamicByIncomeIdAndPhaseStatus(cwProjectIncome.getId(), "3");
        if (cwProjectDynamic.size() == 0) {
            CwProjectDynamic cwProjectDynamic1 = new CwProjectDynamic();
            cwProjectDynamic1.setProjectId(projectId);
            cwProjectDynamic1.setIncomeId(cwProjectIncome.getId());
            cwProjectDynamic1.setDynamicMsg("确认返费");
            cwProjectDynamic1.setDynamicTime(now);
            cwProjectDynamic1.setPhaseStatus("3");
            cwProjectDynamic1.setOperId(loginUser.getUserId());
            cwProjectDynamic1.setOperName(nickName);
            cwProjectDynamicMapper.insertCwProjectDynamic(cwProjectDynamic1);
        } else {
            cwProjectDynamicMapper.updateCwProjectDynamicOperNameAndDynamicTimeByIncomeIdAndPhaseStatus(nickName, now, cwProjectIncome.getId(), "3");
        }
//        cwProjectDynamicMapper.updateCwProjectDynamicOperNameAndDynamicTimeByIncomeIdAndPhaseStatus(nickName, now, cwProjectIncome.getId(), "3");
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFeeSubmit(AddfeeVO addfeeVO, LoginUser loginUser) {
        String username = loginUser.getUsername();

        Date now = new Date();
        //项目id
        Long projectId = addfeeVO.getProjectId();
        //收入表id
        Long incomeId = addfeeVO.getIncomeId();
        //todo 财务项目管理四期，新增待办事项的具体期次说明
        String phaseString = cwProjectIncomeMapper.selectCwprojectIncomePhaseByIncomeId(incomeId);
        //修改收入表
        CwProjectIncome cwProjectIncome = new CwProjectIncome();
        //id
        cwProjectIncome.setId(addfeeVO.getIncomeId());
        //毛利
        cwProjectIncome.setGrossProfitAmt(addfeeVO.getMaoli());
        //提成毛利
        cwProjectIncome.setGrossProfitAmt2(addfeeVO.getTichengmaoli());
        //备注
        cwProjectIncome.setRemark(addfeeVO.getRemark());
        //修改人
        cwProjectIncome.setUpdateBy(username);
        //修改时间
        cwProjectIncome.setUpdateTime(now);
        //修改期次状态
        cwProjectIncome.setPhaseStatus(addfeeVO.getPhaseStatus());
        //重置驳回标识
        cwProjectIncome.setRejectionFlag(StringUtils.EMPTY);
        //收入表提交修改
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        //根据收入id和项目id删除返费
        cwProjectFeeMapper.deleteFeeByInAndPro(projectId, incomeId);
        //新增返费表
        List<CwProjectFee> fee = addfeeVO.getFee();
        for (CwProjectFee cwProjectFee : fee) {
            //循环放入项目、收入表id 名字  创建时间 创建人 修改时间
            cwProjectFee.setProjectId(projectId);
            cwProjectFee.setProjectIncomeId(incomeId);

            cwProjectFee.setFeeFlag("1");

            cwProjectFee.setCreateBy(username);
            cwProjectFee.setCreateTime(now);
            cwProjectFee.setUpdateTime(now);
            cwProjectFeeMapper.insertCwProjectFee(cwProjectFee);
        }
        //修改待录入收入的待办表数据状态
        CwProject cwProject = cwProjectMapper.selectByIncomeId(cwProjectIncome.getId());
        Long projectId1 = cwProject.getId();
        String projectName = cwProject.getProjectName();

        String nickName = loginUser.getUser().getNickName();
        //得到业务代办人id
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(cwProject.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
        List<Long> yewuList = userList.stream().filter(t -> "3".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList());
//        List<Long> yewuList = cwProjectUserMapper.getUserIdListByProId(projectId, "2");
        for (Long aLong : yewuList) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyModule("财务项目管理");
            topNotify.setNotifyMsg(projectName + "-" + phaseString + "期次" + "-待确认返费");
            //刚插入为代办
            topNotify.setNotifyType("1");
            topNotify.setUrl("/caiwu/projectDetails");
            topNotify.setProjectId(projectId);
            topNotify.setIncomeId(cwProjectIncome.getId());
            topNotify.setPhaseStatus("3");
            topNotify.setDisposeUser(aLong);
            topNotify.setViewFlag("0");
            topNotify.setCreateBy(username);
            topNotify.setCreateTime(now);
            topNotify.setUpdateTime(now);
            topNotifyMapper.insertTopNotify(topNotify);
        }
        topNotifyMapper.updateByInmAndProAndStat(projectId1, cwProjectIncome.getId(), "11", now, username);
        cwProjectDynamicMapper.updateCwProjectDynamicOperNameAndDynamicTimeByIncomeIdAndPhaseStatus(nickName, now, cwProjectIncome.getId(), "0");
        return true;


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateIncomeAndFee(AddCwProjectIncomeAndFee addCwProjectIncomeAndFee, LoginUser loginUser) {
        //要用到的主表id
        Long projectId = addCwProjectIncomeAndFee.getAddfeeVO().getProjectId();
        //收入表对象
        CwProjectIncome cwProjectIncome = addCwProjectIncomeAndFee.getCwProjectIncome();
        //要修改的income表主键
        Long incomeId = cwProjectIncome.getId();
        //修改的收入
        BigDecimal incomeAmt = cwProjectIncome.getIncomeAmt();
        //修改的毛利
        BigDecimal maoli = addCwProjectIncomeAndFee.getAddfeeVO().getMaoli();
        //修改的提成毛利
        BigDecimal tichengmaoli = addCwProjectIncomeAndFee.getAddfeeVO().getTichengmaoli();
        //修改的备注
        String remark = addCwProjectIncomeAndFee.getAddfeeVO().getRemark();
        //查主表
        CwProject cwProject = cwProjectMapper.selectCwProjectById(projectId);
        Date nowDate = DateUtils.getNowDate();
        String nickName = loginUser.getUser().getNickName();
        String userName = loginUser.getUsername();
        if ("1".equals(cwProject.getPrestoreIncomeFlag())) {
            //先删除本期次已经存在的预存收入信息，再新增
//            int rows = cwProjectCustMapper.deleteCwProjectPrestoreIncomeByProjectIncomeId(id);
//            int rows = cwProjectCustMapper.updateCwProjectPrestoreIncomeByProjectIncomeId(id);
            CwProjectPrestoreIncome cwProjectPrestoreIncome = new CwProjectPrestoreIncome();
            cwProjectPrestoreIncome.setProjectId(projectId);
            cwProjectPrestoreIncome.setProjectIncomeId(incomeId);
            cwProjectPrestoreIncome.setFlag("1");
            cwProjectPrestoreIncome.setDeductionIncomeDate(cwProjectIncome.getCollectionTime());
            cwProjectPrestoreIncome.setDeductionIncomeAmt(incomeAmt);
            cwProjectPrestoreIncome.setStatus("0");
            cwProjectPrestoreIncome.setUpdateBy(nickName);
            cwProjectPrestoreIncome.setUpdateTime(nowDate);
//            int rows = cwProjectCustMapper.updateCwProjectPrestoreIncome(cwProjectPrestoreIncome);
            int rows = cwProjectCustMapper.updateCwProjectPrestoreIncomeByProjectIncomeId(cwProjectPrestoreIncome);
        }
        //组装要修改的income对象
        cwProjectIncome.setGrossProfitAmt(maoli);
        cwProjectIncome.setGrossProfitAmt2(tichengmaoli);
        cwProjectIncome.setRemark(remark);
        cwProjectIncome.setUpdateBy(userName);
        cwProjectIncome.setUpdateTime(nowDate);
        cwProjectIncomeMapper.updateCwProjectIncome(cwProjectIncome);
        //修改返费流程
        //根据收入id和项目id删除返费
        cwProjectFeeMapper.deleteFeeByInAndPro(projectId, incomeId);
        //新增返费表
        List<CwProjectFee> fee = addCwProjectIncomeAndFee.getAddfeeVO().getFee();
        //2024.10.24在新增返费公司的时候，对revealFeeCompanyId进行赋值
        //具体赋值步骤：通过现在的custId，去找找看是否有替换的返费公司在，如果有，那么就赋给最新的oaTraderId，如果没有，那么就用custId所对应的oaTraderId
        //找是否有替换的标识
        CwProjectCust cwProjectCust = new CwProjectCust();
        cwProjectCust.setProjectId(projectId);
        List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustList(cwProjectCust);
        for (CwProjectFee cwProjectFee:fee) {
            //循环放入项目、收入id 名字 创建时间 创建人 修改时间
            if ("暂不确定公司".equals(cwProjectFee.getFeeCustName())) {
                cwProjectFee.setCustId(-999L);
                cwProjectFee.setCustRemark(addCwProjectIncomeAndFee.getCustRemark());
            } else {
                //看这个项目的cust信息里，入库的那个替换标识是什么
                Long custId = cwProjectFee.getCustId();
                CwProjectCust cwProjectCust1 = cwProjectCusts.stream().filter(t -> t.getId().equals(custId)).findFirst().orElse(null);
                if ("1".equals(cwProjectCust1.getReplaceFlag())) {
                    //发生过替换，那么找最新的一条替换记录中的newOaTraderId
                    Long oaTraderId = cwProjectCust1.getOaTraderId();
                    Long newOaTraderId = cwProjectCustMapper.queryLatestNewOaTraderIdByProjectIdAndOaTraderIdAndStatus(projectId, oaTraderId, "0");
                    cwProjectFee.setRevealFeeCompanyId(newOaTraderId);
                }
            }
            cwProjectFee.setProjectId(projectId);
            cwProjectFee.setProjectIncomeId(incomeId);
            cwProjectFee.setFeeFlag("1");
            cwProjectFee.setCreateBy(userName);
            cwProjectFee.setCreateTime(nowDate);
            cwProjectFee.setUpdateBy(userName);
            cwProjectFee.setUpdateTime(nowDate);
            cwProjectFeeMapper.insertCwProjectFee(cwProjectFee);
        }
        return true;
    }

    public void createProof(Long incomeId,String status){
        //查询收入表
        CwProjectIncome cwProjectIncome = cwProjectIncomeMapper.selectCwProjectIncomeById(incomeId);
        //查询相关联的项目表
        CwProject cwProject = cwProjectMapper.selectByIncomeId(incomeId);
        //生成担保费收入凭证 总计两条
        this.createIncomeProof(cwProjectIncome,cwProject,status);

        //生成返费凭证一条
    }

    /**
     * 处理生成凭证需要的数据并生成凭证
     * @param cwProjectIncome
     * @param cwProject
     * @param status  用来判断生成收入还是返费凭证 0为收入 1为返费
     */
    public void createIncomeProof(CwProjectIncome cwProjectIncome,CwProject cwProject,String status){
        String word = "记";
        Long userId = null;
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(cwProject.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());

        //出纳
//        String cashierList = userList.stream().filter(a -> "2".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
        //根据项目类型查询制单人
//        制单人
//        通道、分润项目 - 取该项目的会计取第一人
//        法催项目 - 取该项目的出纳取第一人
        if(cwProject.getProjectType().equals("1")){
            userId = userList.stream().filter(a -> "2".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList()).get(0);

        }else {
            //会计
            userId = userList.stream().filter(a -> "1".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> Long.parseLong(b.get("featureUserId").toString())).collect(Collectors.toList()).get(0);
        }
        //生成除科目外的其他信息
        FinancialVoucher financialVoucher = this.handleVoucherRulesData(word, cwProject.getAccountSetsId(), userId);
        if(status.equals("0")){
            if(cwProject.getGuaranteeIncomeType().equals("0")){
                //担保费收入直接收款
                CwSubject subject = this.createSubject(cwProjectIncome, cwProject, "0");
                this.handleCwSubjectData(financialVoucher,subject,"担保费收入-直接收款",cwProject.getCustName());
            }else if(cwProject.getGuaranteeIncomeType().equals("1")){
                //混合平台收益
                CwSubject subject1 = this.createSubject(cwProjectIncome, cwProject, "1");
                this.handleCwSubjectData(financialVoucher,subject1,"担保费收入-混合平台收益",cwProject.getCustName());
            }
        }else if(status.equals("1")) {
            //返费
            CwSubject subject2 = this.createSubject(cwProjectIncome, cwProject, "2");
            this.handleCwSubjectData(financialVoucher,subject2,"信息费",cwProject.getCustName());
        }




    }


    /**
     * 生成凭证并添加记录
     * @param financialVoucher
     * @param subject
     * @param model
     * @param companyName
     */
    public synchronized void handleCwSubjectData (FinancialVoucher financialVoucher,CwSubject subject,String model,String companyName){
        String abstractName = subject.getAbstractName();
        List<OaVoucherRulesSubject> oaVoucherRulesSubjects = subject.getOaVoucherRulesSubjects();
        List<FinancialVoucherDetails> detailsList = new ArrayList<>();
        String borrowSubject = "";
        String loanSubject = "";
        BigDecimal amount =  new BigDecimal("0.00");

        for (OaVoucherRulesSubject oaVoucherRulesSubject : oaVoucherRulesSubjects) {
            FinancialSubject financialSubject = financialSubjectMapper.selectById(oaVoucherRulesSubject.getFirstSubjectId());
            OaVoucherRulesSubject oaVoucherRulesSubject1 = this.addRulesByJson(oaVoucherRulesSubject, financialSubject);
            FinancialVoucherDetails financialVoucherDetails = this.handlevoucherSubject(oaVoucherRulesSubject1, abstractName, oaVoucherRulesSubject.getAccountingField());
            if(oaVoucherRulesSubject.getSubjectType().equals("0")){
                borrowSubject = borrowSubject+"("+financialVoucherDetails.getSubjectName()+")+";
                amount = amount.add(BigDecimal.valueOf(financialVoucherDetails.getDebitAmount()));
            } else if(oaVoucherRulesSubject.getSubjectType().equals("1")){
                loanSubject = loanSubject+"("+financialVoucherDetails.getSubjectName()+")+";
            }

            detailsList.add(financialVoucherDetails);
        }


        financialVoucher.setDetails(detailsList);
        //o表示oa系统
        financialVoucher.setSource("O");
        //生成凭证接口
        JsonResult jsonResult = financialOpenService.thirdAddVoucher(financialVoucher);
        //添加记录
        this.addVoucharRuler(financialVoucher,"成功",borrowSubject,loanSubject,amount,"财务项目管理",companyName,model);


    }

    /**
     * 添加记账凭证记录
     * @param financialVoucher
     */
    public void addVoucharRuler(FinancialVoucher financialVoucher,String documentStatus,String borrowSubject,String loanSubject,BigDecimal amount,String theme,String companyName,String model){


        OaDocumentMonitor oaDocumentMonitor = new OaDocumentMonitor();
        oaDocumentMonitor.setDocumentCreateTime(DateUtils.getNowDate());
        oaDocumentMonitor.setDocumentStatus(documentStatus);
        //主题
        oaDocumentMonitor.setFlowTheme(theme);
        //申请人
        oaDocumentMonitor.setApplyId(financialVoucher.getCreateMember());
        //公司
        oaDocumentMonitor.setDocumentCompany(companyName);
        //模板
        oaDocumentMonitor.setFlowTemplate(model);
        //账套
        oaDocumentMonitor.setAccountSetName(financialVoucher.getAccountSetsId().toString());


        //借方科目
        oaDocumentMonitor.setBorrowSubject(borrowSubject);
        //贷方科目
        oaDocumentMonitor.setLoanSubject(loanSubject);
        //金额
        oaDocumentMonitor.setDocumentAmount(amount);

        //制单人
        oaDocumentMonitor.setDocumentCreateName(financialVoucher.getCreateMember().toString());

        oaDocumentMonitor.setCreateTime(DateUtils.getNowDate());
        oaDocumentMonitor.setUpdateTime(DateUtils.getNowDate());
        oaDocumentMonitorMapper.insertOaDocumentMonitor(oaDocumentMonitor);

    }
    /**
     * 生成科目信息
     * @param cwProjectIncome
     * @param cwProject
     * @param type
     * @return {@link CwSubject}
     */
    public CwSubject  createSubject(CwProjectIncome cwProjectIncome, CwProject cwProject, String type){
        CwSubject cwSubject = new CwSubject();
        List<OaVoucherRulesSubject> subjects = new ArrayList<>();
        BigDecimal incomeAmt = cwProjectIncome.getIncomeAmt();
        String abstractName = "";
        if(type.equals("0") || type.equals("1")){
            if(cwProjectIncome.getTerm().equals(0)){
                abstractName= cwProject.getProjectName()+cwProjectIncome.getTermMonth()+"担保费收入";
            }else if(cwProjectIncome.getTerm().equals(1)) {
                abstractName= cwProject.getProjectName()+cwProjectIncome.getTermBegin()+"-"+cwProjectIncome.getTermEnd()+"担保费收入";
            }

        }else if(type.equals("2")){
            if(cwProjectIncome.getTerm().equals(0)){
                abstractName= cwProject.getProjectName()+cwProjectIncome.getTermMonth()+"应支付信息费";
            }else if(cwProjectIncome.getTerm().equals(1)) {
                abstractName= cwProject.getProjectName()+cwProjectIncome.getTermBegin()+"-"+cwProjectIncome.getTermEnd()+"应支付信息费";
            }
        }

        cwSubject.setAbstractName(abstractName);
        //直接收款
        if (type.equals("0")){
            //查询在本项目下的账套下有没有银行存款一级科目信息
            //直接收款借方科目
            //查询借方二级科目收款人简称
            OaTrader oaTrader = oaTraderMapper.selectOaTraderById(cwProject.getGuarantyPayee());

            Integer integer = this.addSubject("1", "0", "资产", "银行存款", "借", cwProject.getAccountSetsId().toString());

            OaVoucherRulesSubject oaVoucherRulesSubject = new OaVoucherRulesSubject();
            //金额
            oaVoucherRulesSubject.setAccountingField(incomeAmt.toString());
            oaVoucherRulesSubject.setFirstSubjectId(new Long(integer));
            oaVoucherRulesSubject.setFirstSubjectName("银行存款");
            oaVoucherRulesSubject.setSubjectType("0");
            oaVoucherRulesSubject.setIsSubjectSecond("0");
            oaVoucherRulesSubject.setSecondValueMode("0");
            oaVoucherRulesSubject.setSecondSubjectName(oaTrader.getAbbreviation());
            oaVoucherRulesSubject.setIsSubjectThird("1");
            oaVoucherRulesSubject.setIsSubjectFourth("1");
            subjects.add(oaVoucherRulesSubject);
            //直接收款贷方科目
            OaVoucherRulesSubject loanSubject1 = new OaVoucherRulesSubject();
            Integer loanId1 = this.addSubject("1", "0", "损益", "担保费收入", "贷", cwProject.getAccountSetsId().toString());
            //金额  收入÷ 1.06(四舍五入到分)1.17原型修改为原收入不经过计算
//            BigDecimal divide = incomeAmt.divide(new BigDecimal("1.06"), BigDecimal.ROUND_HALF_UP);
            loanSubject1.setAccountingField(incomeAmt.toString());
            //
            loanSubject1.setFirstSubjectId(new Long(loanId1));
            loanSubject1.setFirstSubjectName("担保费收入");
            loanSubject1.setSubjectType("1");
            loanSubject1.setIsSubjectSecond("0");
            loanSubject1.setSecondValueMode("0");
            loanSubject1.setSecondSubjectName(cwProject.getProjectName());
            loanSubject1.setIsSubjectThird("1");
            loanSubject1.setIsSubjectFourth("1");
            subjects.add(loanSubject1);

//            //贷方2科目 1.17日原型修改不要第二个贷方
//            OaVoucherRulesSubject loanSubject2 = new OaVoucherRulesSubject();
//            Integer loanId2 = this.addSubject("1", "0", "负债", "应交税费", "贷", cwProject.getAccountSetsId().toString());
//            //金额
//            loanSubject2.setAccountingField(incomeAmt.subtract(divide).toString());
//            //
//            loanSubject2.setFirstSubjectId(new Long(loanId2));
//            loanSubject2.setFirstSubjectName("应交税费");
//            loanSubject2.setSubjectType("1");
//            loanSubject2.setIsSubjectSecond("0");
//            loanSubject2.setSecondValueMode("0");
//            loanSubject2.setSecondSubjectName("应交增值税");
//            loanSubject2.setIsSubjectThird("0");
//            loanSubject2.setThirdValueMode("0");
//            loanSubject2.setThirdSubjectName("销项税额");
//            loanSubject2.setIsSubjectFourth("1");
//            subjects.add(loanSubject2);

        }//混合平台收益
        else if(type.equals("1")){
            Integer integer = this.addSubject("1", "0", "负债", "预收账款", "借", cwProject.getAccountSetsId().toString());

            OaVoucherRulesSubject oaVoucherRulesSubject = new OaVoucherRulesSubject();
            //金额
            oaVoucherRulesSubject.setAccountingField(incomeAmt.toString());
            oaVoucherRulesSubject.setFirstSubjectId(new Long(integer));
            oaVoucherRulesSubject.setFirstSubjectName("预收账款");
            oaVoucherRulesSubject.setSubjectType("0");
            oaVoucherRulesSubject.setIsSubjectSecond("0");
            oaVoucherRulesSubject.setSecondValueMode("0");
            oaVoucherRulesSubject.setSecondSubjectName(cwProject.getProjectName());
            oaVoucherRulesSubject.setIsSubjectThird("1");
            oaVoucherRulesSubject.setIsSubjectFourth("1");
            subjects.add(oaVoucherRulesSubject);
            //直接收款贷方科目
            OaVoucherRulesSubject loanSubject1 = new OaVoucherRulesSubject();
            Integer loanId1 = this.addSubject("1", "0", "损益", "担保费收入", "贷", cwProject.getAccountSetsId().toString());
            //金额  收入÷ 1.06(四舍五入到分) 1.17原型修改
//            BigDecimal divide = incomeAmt.divide(new BigDecimal("1.06"), BigDecimal.ROUND_HALF_UP);

            loanSubject1.setAccountingField(incomeAmt.toString());
            //
            loanSubject1.setFirstSubjectId(new Long(loanId1));
            loanSubject1.setFirstSubjectName("担保费收入");
            loanSubject1.setSubjectType("1");
            loanSubject1.setIsSubjectSecond("0");
            loanSubject1.setSecondValueMode("0");
            loanSubject1.setSecondSubjectName(cwProject.getProjectName());
            loanSubject1.setIsSubjectThird("1");
            loanSubject1.setIsSubjectFourth("1");
            subjects.add(loanSubject1);

//            //贷方2科目
//            OaVoucherRulesSubject loanSubject2 = new OaVoucherRulesSubject();
//            Integer loanId2 = this.addSubject("1", "0", "负债", "应交税费", "贷", cwProject.getAccountSetsId().toString());
//            //金额
//            loanSubject2.setAccountingField(incomeAmt.subtract(divide).toString());
//            //
//            loanSubject2.setFirstSubjectId(new Long(loanId2));
//            loanSubject2.setFirstSubjectName("应交税费");
//            loanSubject2.setSubjectType("1");
//            loanSubject2.setIsSubjectSecond("0");
//            loanSubject2.setSecondValueMode("0");
//            loanSubject2.setSecondSubjectName("应交增值税");
//            loanSubject2.setIsSubjectThird("0");
//            loanSubject2.setThirdValueMode("0");
//            loanSubject2.setThirdSubjectName("销项税额");
//            loanSubject2.setIsSubjectFourth("1");
//            subjects.add(loanSubject2);
        }else if(type.equals("2")){

            //查询返费信息
            List<CwProjectFee> cwProjectFees = cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId2(cwProjectIncome.getId());

            Integer integer = this.addSubject("1", "0", "损益", "主营业务成本", "借", cwProject.getAccountSetsId().toString());

            OaVoucherRulesSubject oaVoucherRulesSubject = new OaVoucherRulesSubject();

            if(cwProject.getProjectType().equals("1")){
                //查询以当前期次法催期次
                BigDecimal amount = new BigDecimal(0.00);
                List<CwProjectFee> cwProjectFees1 = cwProjectFeeMapper.selectDataByIncomeId(cwProjectIncome.getId().toString());
                if(cwProjectFees1.size()>0){
                    for (CwProjectFee cwProjectFee : cwProjectFees1) {
                       amount =  amount.add(cwProjectFee.getCurrentFee());
                    }
                    oaVoucherRulesSubject.setAccountingField(amount.toString());
                } else {
                    oaVoucherRulesSubject.setAccountingField(amount.toString());
                }
            }else {
                //金额

                    //普通项目直接相加所有返费
                    BigDecimal amount = new BigDecimal(0.00);
                    boolean b = true;
                    for (CwProjectFee cwProjectFee : cwProjectFees) {
                        if(null == cwProjectFee.getShouldPayFeeAmt()){
                            b = false;
                        }
                    }
                    if(b){
                        for (CwProjectFee cwProjectFee : cwProjectFees) {
                            amount =  amount.add(cwProjectFee.getShouldPayFeeAmt());
                        }
                    }else {
                        for (CwProjectFee cwProjectFee : cwProjectFees) {
                            amount =  amount.add(cwProjectFee.getActuallyPayFeeAmt());
                        }
                    }
                    oaVoucherRulesSubject.setAccountingField(amount.toString());
                }



            oaVoucherRulesSubject.setFirstSubjectId(new Long(integer));
            oaVoucherRulesSubject.setFirstSubjectName("主营业务成本");
            oaVoucherRulesSubject.setSubjectType("0");
            oaVoucherRulesSubject.setIsSubjectSecond("0");
            oaVoucherRulesSubject.setSecondValueMode("0");
            oaVoucherRulesSubject.setSecondSubjectName(cwProject.getProjectName());
            oaVoucherRulesSubject.setIsSubjectThird("1");
            oaVoucherRulesSubject.setThirdValueMode("1");
//            oaVoucherRulesSubject.setThirdSubjectName("渠道信息费");
            oaVoucherRulesSubject.setIsSubjectFourth("1");
            subjects.add(oaVoucherRulesSubject);
            //贷方的营业外收入
            OaVoucherRulesSubject loanSubject1 = new OaVoucherRulesSubject();
            Integer loan1 = this.addSubject("1", "0", "损益", "营业外收入", "贷", cwProject.getAccountSetsId().toString());

            if(cwProject.getProjectType().equals("1")){
                List<CwProjectFee> cwProjectFees1 = cwProjectFeeMapper.selectDataByIncomeId(cwProjectIncome.getId().toString());
                boolean isok = true;
                BigDecimal loan1Amount = new BigDecimal(0.00);
                //所有返费公司的(应付返费-实付返费)之和
                for (CwProjectFee cwProjectFee : cwProjectFees1) {
                    if (null == cwProjectFee.getShouldPayFeeAmt()) {
                        isok = false;
                    } else {
                        BigDecimal subtract = cwProjectFee.getShouldPayFeeAmt().subtract(cwProjectFee.getActuallyPayFeeAmt());
                        loan1Amount = loan1Amount.add(subtract);
                    }
                }
                if (isok) {
                    //金额
                    loanSubject1.setAccountingField(loan1Amount.toString());
                    //
                    loanSubject1.setFirstSubjectId(new Long(loan1));
                    loanSubject1.setFirstSubjectName("营业外收入");
                    loanSubject1.setSubjectType("1");
                    loanSubject1.setIsSubjectSecond("1");
                    subjects.add(loanSubject1);
                }


                //贷方应付账款-贷款公司
                for (CwProjectFee cwProjectFee : cwProjectFees1) {
                    OaVoucherRulesSubject loanSubject = new OaVoucherRulesSubject();
                    Integer loan = this.addSubject("1", "0", "负债", "应付账款", "贷", cwProject.getAccountSetsId().toString());
                    //因为法催项目的应付实付都为空  所以这里金额取录入的返费
                    loanSubject.setAccountingField(cwProjectFee.getCurrentFee().toString());
                    loanSubject.setFirstSubjectId(new Long(loan));
                    loanSubject.setFirstSubjectName("应付账款");
                    loanSubject.setSubjectType("1");
                    loanSubject.setIsSubjectSecond("0");
                    loanSubject.setSecondValueMode("0");
                    loanSubject.setSecondSubjectName(cwProject.getProjectName());
                    loanSubject.setIsSubjectThird("0");
                    loanSubject.setThirdValueMode("0");
                    loanSubject.setThirdSubjectName(cwProjectFee.getFeeCustName());
                    loanSubject.setIsSubjectFourth("1");
                    subjects.add(loanSubject);
                }




            }else {
                boolean isok = true;
                BigDecimal loan1Amount = new BigDecimal(0.00);
                //所有返费公司的(应付返费-实付返费)之和
                for (CwProjectFee cwProjectFee : cwProjectFees) {
                    if (null == cwProjectFee.getShouldPayFeeAmt()) {
                        isok = false;
                    } else {
                        BigDecimal subtract = cwProjectFee.getShouldPayFeeAmt().subtract(cwProjectFee.getActuallyPayFeeAmt());
                        loan1Amount = loan1Amount.add(subtract);
                    }
                }
                if (isok) {
                    //金额
                    loanSubject1.setAccountingField(loan1Amount.toString());
                    //
                    loanSubject1.setFirstSubjectId(new Long(loan1));
                    loanSubject1.setFirstSubjectName("营业外收入");
                    loanSubject1.setSubjectType("1");
                    loanSubject1.setIsSubjectSecond("1");
                    subjects.add(loanSubject1);
                }
                //贷方应付账款-贷款公司
                for (CwProjectFee cwProjectFee : cwProjectFees) {
                    OaVoucherRulesSubject loanSubject = new OaVoucherRulesSubject();
                    Integer loan = this.addSubject("1", "0", "负债", "应付账款", "贷", cwProject.getAccountSetsId().toString());
                    loanSubject.setAccountingField(cwProjectFee.getActuallyPayFeeAmt().toString());
                    loanSubject.setFirstSubjectId(new Long(loan));
                    loanSubject.setFirstSubjectName("应付账款");
                    loanSubject.setSubjectType("1");
                    loanSubject.setIsSubjectSecond("0");
                    loanSubject.setSecondValueMode("0");
                    loanSubject.setSecondSubjectName(cwProject.getProjectName());
                    loanSubject.setIsSubjectThird("0");
                    loanSubject.setThirdValueMode("0");
                    loanSubject.setThirdSubjectName(cwProjectFee.getFeeCustName());
                    loanSubject.setIsSubjectFourth("1");
                    subjects.add(loanSubject);
                }
            }

        }
        cwSubject.setOaVoucherRulesSubjects(subjects);
        return cwSubject;

    }

    /**
     * @param oaVoucherRulesSubject   科目实体
     * @param financialSubject  一级科目详细信息
     * @return {@link OaVoucherRulesSubject}
     *///多借多贷新增科目
    public OaVoucherRulesSubject addRulesByJson(OaVoucherRulesSubject oaVoucherRulesSubject,FinancialSubject financialSubject){
        String type = "";
        if(oaVoucherRulesSubject.getSubjectType().equals("0")){
            type= "借";
        }else if(oaVoucherRulesSubject.getSubjectType().equals("1")) {
            type= "贷";
        }
        //是否有二级科目
        if(oaVoucherRulesSubject.getIsSubjectSecond().equals("0")){
            //判断二级科目id是否为空 为空则进行新增等操作
            if (null == oaVoucherRulesSubject.getSecondSubjectId() || "".equals(oaVoucherRulesSubject.getSecondSubjectId().toString())) {
                //判断取值方式是固定还是动态
                if(oaVoucherRulesSubject.getSecondValueMode().equals("0")){
                    //固定
                    Integer integer = this.addSubject("2", financialSubject.getId().toString(), financialSubject.getType().toString(), oaVoucherRulesSubject.getSecondSubjectName(), type, financialSubject.getAccountSetsId().toString());
                    oaVoucherRulesSubject.setSecondSubjectId(Long.valueOf(integer));
                }
            }

            //三级科目处理，判断二级是否有二级科目，因为三级科目字段是否有值不固定，有二级则是否有三级科目字段必不为空
            //如果存在二级科目
            //判断是否有三级科目
            if (oaVoucherRulesSubject.getIsSubjectThird().equals("0")) {
                //判断id是否为空
                if(null == oaVoucherRulesSubject.getThirdSubjectId() || "".equals(oaVoucherRulesSubject.getThirdSubjectId().toString())){
                    //判断三级科目取值方式
                    if(oaVoucherRulesSubject.getThirdValueMode().equals("0")){
                        //固定
                        Integer integer = this.addSubject("3", oaVoucherRulesSubject.getSecondSubjectId().toString(), financialSubject.getType().toString(), oaVoucherRulesSubject.getThirdSubjectName(), type, financialSubject.getAccountSetsId().toString());
                        oaVoucherRulesSubject.setThirdSubjectId(Long.valueOf(integer));
                    }
                }

                // 这个判断内已经确定有了三级科目，有三级科目则四级科目字段必不能为空
                // 处理四级科目
                //判断是否有四级科目
                if(oaVoucherRulesSubject.getIsSubjectFourth().equals("0")){
                    //判断四级科目id是否为空
                    if (null == oaVoucherRulesSubject.getFourthSubjectId() || "".equals(oaVoucherRulesSubject.getFourthSubjectId().toString())) {
                        //判断四级取值方式
                        if (oaVoucherRulesSubject.getFourthValueMode().equals("0")) {
                            //固定
                            Integer integer = this.addSubject("4", oaVoucherRulesSubject.getThirdSubjectId().toString(), financialSubject.getType().toString(), oaVoucherRulesSubject.getFourthSubjectName(), type, financialSubject.getAccountSetsId().toString());
                            oaVoucherRulesSubject.setFourthSubjectId(Long.valueOf(integer));

                        }

                    }
                }

            }

        }

        return oaVoucherRulesSubject;

    }


    /**
     * 生成凭证数据 仅科目信息
     * @param oaVoucherRulesSubject1
     * @param abstractJson
     * @param amt 金额
     * @return {@link FinancialVoucherDetails}
     */
    private FinancialVoucherDetails handlevoucherSubject(OaVoucherRulesSubject oaVoucherRulesSubject1, String abstractJson,String amt) {

        FinancialVoucherDetails financialVoucherDetails = new FinancialVoucherDetails();
        Double amount = new Double(amt);
        //如果“是否存在四级科目”字段为空或者 等于1 则证明没有四级科目信息则继续判断三级
        if (null == oaVoucherRulesSubject1.getIsSubjectFourth() || oaVoucherRulesSubject1.getIsSubjectFourth().equals("1")) {

            //判断三级
            if(null == oaVoucherRulesSubject1.getIsSubjectThird() || oaVoucherRulesSubject1.getIsSubjectThird().equals("1")){
                //判断二级 不存在二级则取一级
                if(oaVoucherRulesSubject1.getIsSubjectSecond().equals("1")){
                    //金额
                    if(oaVoucherRulesSubject1.getSubjectType().equals("0")){
                        //借方金额
                        financialVoucherDetails.setDebitAmount(amount);

                    }else if(oaVoucherRulesSubject1.getSubjectType().equals("1")){
                        //贷方金额
                        financialVoucherDetails.setCreditAmount(amount);
                    }
                    //科目ID
                    String subjectId = oaVoucherRulesSubject1.getFirstSubjectId().toString();
                    financialVoucherDetails.setSubjectId(Integer.parseInt(subjectId));
                    FinancialSubject financialSubject = financialSubjectMapper.selectById(subjectId);
                    //科目名称
                    financialVoucherDetails.setSubjectName(financialSubject.getCode()+"-"+financialSubject.getName());
                    //科目编码
                    financialVoucherDetails.setSubjectCode(financialSubject.getCode());
                    //辅助名称
                    financialVoucherDetails.setAuxiliaryTitle("");
                    //摘要
                    financialVoucherDetails.setSummary(abstractJson);

                }else  //存在二级则取二级
                    if(oaVoucherRulesSubject1.getIsSubjectSecond().equals("0")) {
                        //金额
                        if(oaVoucherRulesSubject1.getSubjectType().equals("0")){
                            //借方金额
                            financialVoucherDetails.setDebitAmount(amount);

                        }else if(oaVoucherRulesSubject1.getSubjectType().equals("1")){
                            //贷方金额
                            financialVoucherDetails.setCreditAmount(amount);
                        }
                        //科目ID
                        String subjectId = oaVoucherRulesSubject1.getSecondSubjectId().toString();
                        financialVoucherDetails.setSubjectId(Integer.parseInt(subjectId));
                        //二级科目
                        FinancialSubject financialSubject = financialSubjectMapper.selectById(subjectId);
                        //一级
                        FinancialSubject financialSubject2 = financialSubjectMapper.selectById(financialSubject.getParentId());
                        //科目名称
                        financialVoucherDetails.setSubjectName(financialSubject.getCode()+"-"+financialSubject2.getName()+"-"+financialSubject.getName());
                        //科目编码
                        financialVoucherDetails.setSubjectCode(financialSubject.getCode());
                        //辅助名称
                        financialVoucherDetails.setAuxiliaryTitle("");
                        //摘要
                        financialVoucherDetails.setSummary(abstractJson);

                    }

            }else if(oaVoucherRulesSubject1.getIsSubjectThird().equals("0")){
                //金额
                if(oaVoucherRulesSubject1.getSubjectType().equals("0")){
                    //借方金额
                    financialVoucherDetails.setDebitAmount(amount);

                }else if(oaVoucherRulesSubject1.getSubjectType().equals("1")){
                    //贷方金额
                    financialVoucherDetails.setCreditAmount(amount);
                }
                //科目ID
                String subjectId = oaVoucherRulesSubject1.getThirdSubjectId().toString();
                financialVoucherDetails.setSubjectId(Integer.parseInt(subjectId));
                //三级科目
                FinancialSubject financialSubject = financialSubjectMapper.selectById(subjectId);
                //二级
                FinancialSubject financialSubject2 = financialSubjectMapper.selectById(financialSubject.getParentId());
                //一级
                FinancialSubject financialSubject3 = financialSubjectMapper.selectById(financialSubject2.getParentId());
                //科目名称
                financialVoucherDetails.setSubjectName(financialSubject.getCode()+"-"+financialSubject3.getName()+"-"+financialSubject2.getName()+"-"+financialSubject.getName());
                //科目编码
                financialVoucherDetails.setSubjectCode(financialSubject.getCode());
                //辅助名称
                financialVoucherDetails.setAuxiliaryTitle("");
                //摘要
                financialVoucherDetails.setSummary(abstractJson);

            }
        }else if(oaVoucherRulesSubject1.getIsSubjectFourth().equals("0")) {
            //金额
            if(oaVoucherRulesSubject1.getSubjectType().equals("0")){
                //借方金额
                financialVoucherDetails.setDebitAmount(amount);

            }else if(oaVoucherRulesSubject1.getSubjectType().equals("1")){
                //贷方金额
                financialVoucherDetails.setCreditAmount(amount);
            }
            //科目ID
            String subjectId = oaVoucherRulesSubject1.getFourthSubjectId().toString();
            financialVoucherDetails.setSubjectId(Integer.parseInt(subjectId));
            //四级科目
            FinancialSubject financialSubject = financialSubjectMapper.selectById(subjectId);
            //三级
            FinancialSubject financialSubject2 = financialSubjectMapper.selectById(financialSubject.getParentId());
            //二级
            FinancialSubject financialSubject3 = financialSubjectMapper.selectById(financialSubject2.getParentId());
            //一级
            FinancialSubject financialSubject4 = financialSubjectMapper.selectById(financialSubject3.getParentId());
            //科目名称
            financialVoucherDetails.setSubjectName(financialSubject.getCode()+"-"+financialSubject4.getName()+"-"+financialSubject3.getName()+"-"+financialSubject2.getName()+"-"+financialSubject.getName());
            //科目编码
            financialVoucherDetails.setSubjectCode(financialSubject.getCode());
            //辅助名称
            financialVoucherDetails.setAuxiliaryTitle("");
            //摘要
            financialVoucherDetails.setSummary(abstractJson);

        }

        return financialVoucherDetails;
    }


    /**
     * 添加科目
     *
     * @param leval           要插入的级别
     * @param parentId         父id
     * @param type             类型
     * @param subjectName   科目名称
     * @param balanceDirection 余额方向  借 贷
     * @param accountSetsId 账套id
     * @return {@link Integer}
     */
    public Integer addSubject(String leval,String parentId,String type,String subjectName,String balanceDirection,String accountSetsId){

        Integer id = null;
        //根据需要插入的条件查询是否又此条科目 如果有则直接获取ID  如果无 则添加
        FinancialSubject financialSubjectParam = new FinancialSubject();
        financialSubjectParam.setLevel(Short.valueOf(leval));
        financialSubjectParam.setParentId(Integer.parseInt(parentId));
        financialSubjectParam.setType(type);
        financialSubjectParam.setName(subjectName);
        financialSubjectParam.setBalanceDirection(balanceDirection);
        financialSubjectParam.setAccountSetsId(Integer.parseInt(accountSetsId));

        List<Map<String, Object>> maps = financialSubjectMapper.queryByParamList(financialSubjectParam);
        if(maps.size()==0){
            //查询要插入的级别 获取到编号
            List<Map<String,Object>> levalSubjectDataList =  financialSubjectMapper.getLevalSubjectDataList(leval,parentId);
            FinancialSubject parentData =  financialSubjectMapper.selectById(parentId);
            String pinYinHeadChar = PinYinUtil.getPinYinHeadChar(subjectName);
            FinancialSubject financialSubject = new FinancialSubject();
            if(levalSubjectDataList.size()>0){
                //如果其下有数据则取倒数1+1为编码
                int i = Integer.parseInt(levalSubjectDataList.get(0).get("code").toString());
                i++;
                financialSubject.setType(type);
                financialSubject.setCode(i+"");
                financialSubject.setName(subjectName);
                if(null == parentData ||null==parentData.getMnemonicCode()){
                    financialSubject.setMnemonicCode(pinYinHeadChar);
                }else {
                    financialSubject.setMnemonicCode(parentData.getMnemonicCode()+"_"+pinYinHeadChar);
                }

                financialSubject.setBalanceDirection(balanceDirection);
                financialSubject.setStatus(true);
                financialSubject.setParentId(Integer.parseInt(parentId));
                financialSubject.setLevel(Short.valueOf(leval));
                financialSubject.setSystemDefault(false);
                financialSubject.setAccountSetsId(Integer.parseInt(accountSetsId));
            } else if (levalSubjectDataList.size()==0) {
                //如果其下无数据则查询父级的编码后加01
                int i = Integer.parseInt(parentData.getCode());
                financialSubject.setType(type);
                financialSubject.setCode(i+"01");
                financialSubject.setName(subjectName);
                if(null == parentData ||null==parentData.getMnemonicCode()){
                    financialSubject.setMnemonicCode(pinYinHeadChar);
                }else {
                    financialSubject.setMnemonicCode(parentData.getMnemonicCode()+"_"+pinYinHeadChar);
                }
                financialSubject.setBalanceDirection(balanceDirection);
                financialSubject.setStatus(true);
                financialSubject.setParentId(Integer.parseInt(parentId));
                financialSubject.setLevel(Short.valueOf(leval));
                financialSubject.setSystemDefault(false);
                financialSubject.setAccountSetsId(Integer.parseInt(accountSetsId));
            }

            //取到后直接添加
            financialSubjectMapper.insertFinancialSubject(financialSubject);
            id = financialSubject.getId();
        }else {
            id = Integer.parseInt( maps.get(0).get("id").toString());
        }

        return id;
    }

    /**
     * 生成凭证数据 科目信息除外
     * @param finanicalWord  字
     * @param accountSetsId  账套id
     * @param userId 制单人
     * @return {@link FinancialVoucher}
     */
    public FinancialVoucher handleVoucherRulesData(String finanicalWord, Long accountSetsId, Long userId){

        FinancialVoucher financialVoucher = new FinancialVoucher();
        Date date = DateUtils.getNowDate();
        financialVoucher.setWord(finanicalWord);
        financialVoucher.setVoucherDate(date);
        financialVoucher.setAccountSetsId(Integer.parseInt(accountSetsId.toString()));
        financialVoucher.setCreateMember(userId);
        return  financialVoucher;
    };

    /**
     * Excel导入
     *
     * @param file
     * @return 暂无
     */
    @Transactional(rollbackFor = Exception.class)
    public void importExcelOfCwProject(MultipartFile file) throws Exception {
//        List<SysDictData> projectTypeInfo = sysDictDataMapper.selectDictDataByType("project_type");
        List<OaDataManage> projectTypeInfo = oaDataManageMapper.selectDataManageListByCode("project_type");
        ExcelImportOfCwProject excelImportOfCwProject = new ExcelImportOfCwProject();
        excelImportOfCwProject.init(file.getInputStream());
        List<ExcelImportOfCwProjectDto> excelImportOfCwProjectDtoList = excelImportOfCwProject.importExcelHandle(projectTypeInfo);
        //获取到返费的基础信息之后，进行查询获得项目id，OA项目id，期次id
        //通过项目名称来获取OA项目id，先进行分组
        Map<String, List<ExcelImportOfCwProjectDto>> collect = excelImportOfCwProjectDtoList.stream().collect(Collectors.groupingBy(ExcelImportOfCwProjectDto::getProjectName));
        //把两个要入库的集合创建
        List<CwProjectPayFormOa> cwProjectPayFormOaList = new ArrayList<>();
        List<OaPayRebateRecord> oaPayRebateRecordList = new ArrayList<>();
        collect.forEach((projectName, list) -> {
            log.info("当前项目名称：{}", projectName);
            OaProjectDeploy oaProjectDeploy = oaProjectDeployMapper.selectOaprojectDeployByProjectName(projectName);
            Long oaProjectDeployId = oaProjectDeploy.getId();
            CwProject cwProject = cwProjectMapper.selectCwProjectByOaProjectDeployId(oaProjectDeployId);
            Long projectId = cwProject.getId();
//            List<String> termMounthList = list.stream().filter(t -> t.getTermMonth() != null).map(ExcelImportOfCwProjectDto::getTermMonth).collect(Collectors.toList());
//            List<Date> termBeginList = list.stream().filter(t -> t.getTermMonth() == null).map(ExcelImportOfCwProjectDto::getTermBegin).collect(Collectors.toList());
//            List<Date> termEndList = list.stream().filter(t -> t.getTermMonth() == null).map(ExcelImportOfCwProjectDto::getTermEnd).collect(Collectors.toList());
            //找期次id
//            List<CwProjectIncome> phaseList = cwProjectIncomeMapper.selectCwProjectIncomeListByQueryParam(projectId, termMounthList, null, null);
//            List<CwProjectIncome> phaseList1 = cwProjectIncomeMapper.selectCwProjectIncomeListByQueryParam(projectId, null, termBeginList, termEndList);
            Date nowDate = DateUtils.getNowDate();
            for (ExcelImportOfCwProjectDto excelImportOfCwProjectDto : list) {
                CwProjectPayFormOa cwProjectPayFormOa = new CwProjectPayFormOa();
                cwProjectPayFormOa.setOaProjectDeployId(oaProjectDeployId);
                cwProjectPayFormOa.setProjectId(projectId);
                if (!"1".equals(excelImportOfCwProjectDto.getProjectType())) {
                    cwProjectPayFormOa.setActuallyPayFeeAmt(excelImportOfCwProjectDto.getAmount());
                } else {
                    cwProjectPayFormOa.setFeeRound(excelImportOfCwProjectDto.getAmount());
                }
                cwProjectPayFormOa.setOverFlag("0");
                cwProjectPayFormOa.setNoAlreadyPayFee(BigDecimal.ZERO);
                cwProjectPayFormOa.setStatus("0");
                cwProjectPayFormOa.setCreateBy("脚本初始化对冲记录入库");
                cwProjectPayFormOa.setCreateTime(nowDate);
                cwProjectPayFormOa.setUpdateBy("脚本初始化对冲记录入库");
                cwProjectPayFormOa.setUpdateTime(nowDate);
                OaPayRebateRecord oaPayRebateRecord = new OaPayRebateRecord();
                oaPayRebateRecord.setOaCompleteTime(nowDate);
                oaPayRebateRecord.setProjectId(oaProjectDeployId);
                oaPayRebateRecord.setProjectTypeId(Long.parseLong(excelImportOfCwProjectDto.getProjectType()));
                oaPayRebateRecord.setIsRebateCom("Y");
                oaPayRebateRecord.setAmount(excelImportOfCwProjectDto.getAmount());
                oaPayRebateRecord.setSynStatus("Y");
                oaPayRebateRecord.setCreateBy("脚本初始化打款记录入库");
                oaPayRebateRecord.setCreateTime(nowDate);
                CwProjectIncome cwProjectIncome = new CwProjectIncome();
                cwProjectIncome.setProjectId(projectId);
                if (excelImportOfCwProjectDto.getTermMonth() != null) {
                    cwProjectIncome.setTerm("0");
                    cwProjectIncome.setTermMonth(excelImportOfCwProjectDto.getTermMonth());
                } else {
                    cwProjectIncome.setTerm("1");
                    cwProjectIncome.setTermBegin(excelImportOfCwProjectDto.getTermBegin());
                    cwProjectIncome.setTermEnd(excelImportOfCwProjectDto.getTermEnd());
                }
                log.info("要查询的期次对象为：{}", cwProjectIncome);
                CwProjectIncome cwProjectIncome1 = cwProjectIncomeMapper.selectCwProjectIncome(cwProjectIncome);
                log.info("查库得到的期次对象：{}", cwProjectIncome1);
                cwProjectPayFormOa.setProjectIncomeId(cwProjectIncome1.getId());
                oaPayRebateRecord.setIncomeId(cwProjectIncome1.getId());
                //入库
                oaPayRebateRecordMapper.insertOaPayRebateRecord(oaPayRebateRecord);
                cwProjectCustMapper.insertCwProjectPayFormOa(cwProjectPayFormOa);
            }
        });
    }
}
