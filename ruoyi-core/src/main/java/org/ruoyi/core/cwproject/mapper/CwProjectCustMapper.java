package org.ruoyi.core.cwproject.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.cwproject.domain.CwProjectCust;
import org.ruoyi.core.cwproject.domain.CwProjectPayFormOa;
import org.ruoyi.core.cwproject.domain.CwProjectPrestoreIncome;
import org.ruoyi.core.cwproject.domain.CwProjectReplaceFeeCompanyInfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-返费公司与费率Mapper接口
 *
 * <AUTHOR>
 * @date 2022-11-10
 */
public interface CwProjectCustMapper {
    /**
     * 查询财务项目管理-返费公司与费率
     *
     * @param id 财务项目管理-返费公司与费率主键
     * @return 财务项目管理-返费公司与费率
     */
    public CwProjectCust selectCwProjectCustById(Long id);

    /**
     * 查询财务项目管理-返费公司与费率列表
     *
     * @param cwProjectCust 财务项目管理-返费公司与费率
     * @return 财务项目管理-返费公司与费率集合
     */
    public List<CwProjectCust> selectCwProjectCustList(CwProjectCust cwProjectCust);

    /**
     * 新增财务项目管理-返费公司与费率
     *
     * @param cwProjectCust 财务项目管理-返费公司与费率
     * @return 结果
     */
    public int insertCwProjectCust(CwProjectCust cwProjectCust);

    /**
     * 修改财务项目管理-返费公司与费率
     *
     * @param cwProjectCust 财务项目管理-返费公司与费率
     * @return 结果
     */
    public int updateCwProjectCust(CwProjectCust cwProjectCust);

    /**
     * 删除财务项目管理-返费公司与费率
     *
     * @param id 财务项目管理-返费公司与费率主键
     * @return 结果
     */
    public int deleteCwProjectCustById(Long id);

    /**
     * 批量删除财务项目管理-返费公司与费率
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCwProjectCustByIds(Long[] ids);

    /**
     * 查询财务项目管理-返费公司与费率详情
     *
     * @param custName 财务项目管理-公司名称
     * @return 财务项目管理-返费公司明细查询
     */
    List<Map<String, Object>> selectCwProjectCustListDetail(@Param("userId") Long userId, @Param("custName") String custName);

    /**
     * 查询财务项目管理-查找相关返费公司名称以及费率
     *
     * @param projectId 财务项目管理-主表id
     * @return 财务项目管理-返费公司明细查询
     */
    List<CwProjectCust> selectCwProjectCustListByProjectId(Long projectId);
    List<CwProjectCust> selectCwProjectCustListByProjectIdForLaw(Long projectId);

    List<Map<String, Object>> selectCwProjectCustListDetailByAdmin(CwProjectCust cwProjectCust);

    List<CwProjectCust> selectCwProjectCustListAll();

    int updateProjectCust(CwProjectCust cwProjectCust);

    List<CwProjectCust> selectCwProjectCustListAllProjectId(@Param("id") Long projectId);

    /**
     * 查询财务项目管理-查询该项目下是否有正在进行的状态
     */
    List<Map<String, Object>> selectCwProjectCounductByProjectId(Long peojectId);

    /**
     * 查询财务项目管理-修改该项目是否是整月非整月
     */
    Map<String, Object> selectCwProjectCounductByIncomeId(Long incomeId);

    /**
     * 查询财务项目管理-根据收入表id，查该期次的进行状态
     */
    List<Map<String, Object>> selectCwProjectCounductDatailByIncomeId(Long incomeId);

    Map<String, Object> getPhaseStatus(@Param("id") Long id);

    int deleteCustByProId(@Param("id") Long id);

    List<CwProjectCust> selectCwProjectListFirstByadmin(@Param("projectIds") List<Long> projectIds);

    //法催项目 - 查询正在进行的期次
    List<Map<String, Object>> selectCwProjectCounductLawByProjectId(Long projectId);

    //法催项目 - 查正在进行中的期次的收入详情
    List<Map<String, Object>> selectCwProjectCounductLawDatailByIncomeId(Long phaseId);

    //普通项目 - 查询预存收入 - 所有，预存和抵扣
    List<CwProjectPrestoreIncome> selectPrestoreIncomeListByProjectId(Long projectId);

    //普通项目 - 查询预存收入 - 只查预存
    List<CwProjectPrestoreIncome> selectPrestoreIncomeListForInByProjectId(Long projectId);

    //找预存收入表要删除的数据（即没有更新的，是需要删除的）
    List<Long> selectDeleteCwProjectPrestoreIncomeById(@Param("updateList") List<CwProjectPrestoreIncome> updateList);

    //删除预存收入表的数据删多个
    int deleteCwProjectPrestoreIncomeById(@Param("idList") List<Long> idList);

    //更新预存收入表的数据，更新多个
    int updateCwProjectPrestoreIncomeList(@Param("updateList") List<CwProjectPrestoreIncome> updateList);

    //新插入预存收入表，单个插
    int insertPrestoreIncome(CwProjectPrestoreIncome cppi);

    //通过收入id和抵扣时间查找标识为抵扣的信息
    CwProjectPrestoreIncome selectCwProjectPrestoreIncomeByProjectIncomeIdAndDeductionIncomeDate(@Param("projectIncomeId") Long projectIncomeId, @Param("deductionIncomeDate") String dateStr);

    //通过项目id查返费公司与费率的方案使用情况
    List<CwProjectCust> selectCwprojectCustSchemeFlagUseSituationByProjectId(Long projectId);

    //普通项目 - 有预存收入项目，通过收入id删除发生过抵扣的信息
    int deleteCwProjectPrestoreIncomeByProjectIncomeId(Long projectIncomeId);

    //普通项目 - 有预存收入项目，修改发生过抵扣的信息
    int updateCwProjectPrestoreIncome(CwProjectPrestoreIncome cwProjectPrestoreIncome);

    //普通项目 - 有预存收入项目，通过projectIncomeId修改发生过抵扣的信息
    int updateCwProjectPrestoreIncomeByProjectIncomeId(CwProjectPrestoreIncome cwProjectPrestoreIncome);

    //todo 财务项目管理四期 - 法催项目根据期次id查找返费公司与出返费公司
    Map<String, Object> selectLawCwProjectCustByPhaseId(Long phaseId);

    //找所有普通项目的暂不确定公司的返费公司与费率表（实际上是自己从返费表拿数据组装的cust表）
    List<CwProjectCust> cwProjectCustSchemeFlagUseSituationForNotSureCompanyByProjectId(Long projectId);

    //按返费公司查询 - 查询条件 返费公司对象（返费公司名称、项目类型） 用户id
    List<Map<String, Object>> selectCwProjectCustListDetailByUserId(@Param("cwProjectCust") CwProjectCust cwProjectCust, @Param("userId") Long userId);

    BigDecimal selectCwprojectIncomeAmtByProject(Long projectId);

    BigDecimal selectCwprojectActuallyPayFeeAmtByProject(@Param("projectId") Long projectId, @Param("incomeIdList") List<Long> incomeIdList);

    BigDecimal selectLawCwprojectIncomeAmtByProject(Long projectId);

    BigDecimal selectLawCwprojectActuallyPayFeeAmtByProject(Long projectId);

    Map<String, Object> selectCwprojectCertficateFlagInfo(Long projectId);

    int selectNotSureCompanyPhaseByPhaseId(Long phaseId);

    List<CwProjectReplaceFeeCompanyInfo> selectCwProjectReplaceFeeCompanyInfoListByProjectId(Long projectId);

    List<Map<String, Object>> selectProjectFeeCompanyInfoByProjectId(Long projectId);

    int insertCwProjectReplaceFeeCompanyInfo(CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo);

    int updateCwProjectCustReplaceFlagByProjectIdAndOaTraderId(@Param("projectId") Long projectId, @Param("oaTraderId") Long oaTraderId, @Param("replaceFlag") String replaceFlag, @Param("schemeFlag") String schemeFlag);

    int updateCwprojectReplaceFeeCompanyInfo(CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo);

    List<CwProjectReplaceFeeCompanyInfo> selectCwProjectReplaceFeeCompanyInfoListByProjectIdAndOaTraderId(@Param("projectId") Long projectId, @Param("oaTraderId") Long oaTraderId, @Param("schemeFlag") String schemeFlag);

    List<CwProjectReplaceFeeCompanyInfo> selectCwProjectReplaceFeeCompanyInfoListByProjectIdListAndStatus(@Param("projectIdList") List<Long> projectIdList, @Param("status") String status);

    List<CwProjectReplaceFeeCompanyInfo> selectCwProjectReplaceFeeCompanyInfoListByOaTraderIdsAndStatus(@Param("oaTraderIdList") List<Long> oaTraderIdList, @Param("status") String status);

    List<Map<String, Object>> selectCwProjectCustListDetailByAdminAndOaTraderIdList(@Param("cwProjectCust") CwProjectCust cwProjectCust, @Param("oaTraderIdList") List<Long> oaTraderIdList);

    List<Map<String, Object>> selectCwProjectCustListDetailByUserIdAndOaTraderIdList(@Param("cwProjectCust") CwProjectCust cwProjectCust, @Param("userId") Long userId, @Param("oaTraderIdList") List<Long> oaTraderIdList);

    List<Map<String, Object>> selectCwProjectCustListDetailByProjectIds(@Param("projectIdList") List<Long> projectIdList, @Param("projectType") String projectType, @Param("projectIds") List<Long> projectIds);

    String selectCwprojectFeeNameByCustId(@Param("custId") Long custId);

    String selectOldOaTraderUserNameByCustIdAndNewOaTraderUserName(@Param("custId") Long custId, @Param("feeName") String feeName);

    //法催项目用
    Map<String, Object> selectCwProjectcustByPhaseId(Long phaseId);

    //通道、分润项目用
    List<Map<String, Object>> selectCwProjectcustByIncomeId(Long incomeId);

    /**
     * 查询财务项目管理-查询该项目下已经完结但是有未确定公司的期次
     */
    List<Map<String, Object>> selectCwProjectCounductByProjectId1(Long peojectId);

    //返费公司没有发生替换，找返费公司的oa_trader_id对应的名字
    String selectOatraderUserNameByCustId(Long custId);

    /**
     * 通过oaTraderId集合，查询所有的替换信息
     */
    List<CwProjectReplaceFeeCompanyInfo> selectCwProjectReplaceFeeCompanyInfoListByOaTraderId(@Param("oaTraderId") Long oaTraderId);

    /**
     * 通过项目id和oaTraderId，查询所有替换过符合条件的返费公司信息
     */
    List<CwProjectCust> selectCwProjectCustListByProjectIdAndOaTraderId(@Param("projectId") Long projectId, @Param("oaTraderId") Long oaTraderId);

    /**
     * 通过oaTraderId，查询所有没有替换过符合条件的返费公司信息
     */
    List<CwProjectCust> selectNoReplaceCwProjectCustListByOaTraderId(@Param("oaTraderId") Long oaTraderId);

    /**
     * 通过custId集合，查询所有要对冲的信息
     */
    List<Map<String, Object>> selectCwprojectAndIncomeInfoByCustIds(@Param("custIdList") List<Long> custIdList, @Param("projectType") String projectType, @Param("projectIdList") List<Long> projectIdList);

    /**
     * 通过incomeId集合，查询对冲表是否包含有这些期次的对冲信息
     */
    List<CwProjectPayFormOa> selectCwProjectPayFormOaByIncomeIdList(@Param("incomeIdList") List<Long> incomeIdList);

    /**
     * 新增财务项目管理-财务项目管理OA流程支付对冲记录
     *
     * @param cwProjectPayFormOa 财务项目管理-OA流程支付对冲记录
     * @return 结果
     */
    int insertCwProjectPayFormOa(CwProjectPayFormOa cwProjectPayFormOa);

    //2024.06.07
    // 第一个参数，是用户可以查看的项目id集合（如果第一个参数不为空，oaProjectDeployId为空，则查询所有下拉框可以选择的）
    // 根据项目id查询可以选择的项目id以及项目类型
    List<Map<String, Object>> selectOaProjectDeployAndProjectType(@Param("oaProjectDeployIds") List<Long> oaProjectDeployIds, @Param("oaProjectDeployId") Long oaProjectDeployId);

    //通过项目id,找项目的收入id(非法催的项目收入id = 期次id)
    List<Long> queryIncomeIdListByProjectId(Long projectId);

    //通过项目id,找项目的期次id(法催项目用)
    List<Long> queryPhaseIdListByProjectId(Long projectId);

    //通过项目id，oaTraderId，状态字符串，找替换返费信息表中最新的一条newOaTraderId
    Long queryLatestNewOaTraderIdByProjectIdAndOaTraderIdAndStatus(@Param("projectId") Long projectId, @Param("oaTraderId") Long oaTraderId, @Param("status") String status);
}
