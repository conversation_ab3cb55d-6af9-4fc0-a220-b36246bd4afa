package org.ruoyi.core.cwproject.export;

import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.ruoyi.core.cwproject.domain.export.ExcelLawCwProject;
import org.ruoyi.core.cwproject.domain.view.CwProjectLawDetailView;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class BeachExportLawCwProject {

	
	
	public static String exportLawCwProject(ExcelLawCwProject proj) throws IOException{
		String sheet1Name="返费明细";
		String sheet2Name="返费公司及费率";
		String fileName = "";


		SXSSFWorkbook workbook = new SXSSFWorkbook();
		int colSize1 = 28;//列数


		/**
		 * 第一个工作表
		 */

		//创建Sheet（工作簿）
		SXSSFSheet sheet1 = workbook.createSheet(sheet1Name);
		//创建主标题行(第一行)
		SXSSFRow sheetTitleRow = sheet1.createRow(0);
		for (int i = 0; i < colSize1; i++) {
			SXSSFCell headCell = sheetTitleRow.createCell(i);
			switch (i) {
				case 0:
					headCell.setCellValue("业务期次");
					break;
				case 1:
					headCell.setCellValue("担保公司");
					break;
				case 2:
					headCell.setCellValue("汇款公司");
					break;
				case 3:
					headCell.setCellValue("收款时间");
					break;
				case 4:
					headCell.setCellValue("服务商");
					break;
				case 5:
					headCell.setCellValue("法催收入");
					break;
				case 6:
					headCell.setCellValue("二级服务商");
					break;
				case 7:
					headCell.setCellValue("二级服务商收入");
					break;
				case 8:
					headCell.setCellValue("出返费公司");
					break;
				case 9:
					headCell.setCellValue("返费公司");
					break;
				case 10:
					headCell.setCellValue("真实回款金额");
					break;
				case 11:
					headCell.setCellValue("服务费");
					break;
				case 12:
					headCell.setCellValue("本金");
					break;
				case 13:
					headCell.setCellValue("本期返费");
					break;
				case 14:
					headCell.setCellValue("挂起金额");
					break;
				case 15:
					headCell.setCellValue("挂起金额累计");
					break;
				case 16:
					headCell.setCellValue("返费");
					break;
				case 17:
					headCell.setCellValue("返费取整");
					break;
				case 18:
					headCell.setCellValue("借条分润");
					break;
				case 19:
					headCell.setCellValue("法催利润");
					break;
				case 20:
					headCell.setCellValue("返费已结清");
					break;
				case 21:
					headCell.setCellValue("返费未结清");
					break;
				case 22:
					headCell.setCellValue("期次状态");
					break;
				case 23:
					headCell.setCellValue("打款状态");
					break;
				case 24:
					headCell.setCellValue("打款日期");
					break;
				case 25:
					headCell.setCellValue("实际打款金额");
					break;
				case 26:
					headCell.setCellValue("抹平差额");
					break;
				case 27:
					headCell.setCellValue("备注");
					break;
				default:
					break;
			}
			headCell.setCellStyle(getTitleFont(sheet1.getWorkbook()));//设置样式
		}
		//查找每个phase出现的次数，key -> phaseId     value -> 出现的次数
		Map<Long, Long> collect = proj.getIncomeList().stream().collect(Collectors.groupingBy(CwProjectLawDetailView::getPhaseId, Collectors.counting()));

		//查找每个feeId出现的次数，key -> feeId     value -> 出现的次数
		Map<Long, Long> collect1 = proj.getIncomeList().stream().filter(t -> t.getProjectFeeId() != null).collect(Collectors.groupingBy(CwProjectLawDetailView::getProjectFeeId, Collectors.counting()));

		//list大小大于0
		if (proj.getIncomeList().size() > 0) {
			//遍历表头名称，创建表头单元格
			for (int i = 0; i < proj.getIncomeList().size(); i++) {
				//创建数据行（第二行）
				SXSSFRow sheetHeadRowIncome = sheet1.createRow(1 + i);
				//业务期次
				SXSSFCell cell1 = sheetHeadRowIncome.createCell(0);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getTermMonth()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getTermMonth());
				} else {
					cell1.setCellValue(proj.getIncomeList().get(i).getTermMonth());
				}
//            if(proj.getIncomeList().get(i).getTerm().equals("0")) {
//            	cell1.setCellValue(proj.getIncomeList().get(i).getTermMonth());
//            } else if (proj.getIncomeList().get(i).getTerm().equals("1")) {
//            	cell1.setCellValue(DateUtils.parseDateToStr("yyyy.MM.dd",proj.getIncomeList().get(i).getTermBegin())+"-"+DateUtils.parseDateToStr("yyyy.MM.dd",proj.getIncomeList().get(i).getTermEnd()));
//            } else {
//				cell1.setCellValue("");
//			}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//担保公司
				cell1 = sheetHeadRowIncome.createCell(1);
				cell1.setCellValue(proj.getCustName());
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//汇款公司
				cell1 = sheetHeadRowIncome.createCell(2);
				cell1.setCellValue(proj.getIncomeCustName());
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//收款时间
				cell1 = sheetHeadRowIncome.createCell(3);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getCollectionTime()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getCollectionTime());
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//服务商
				cell1 = sheetHeadRowIncome.createCell(4);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getServiceProvider()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getServiceProvider());
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//收入
				cell1 = sheetHeadRowIncome.createCell(5);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getIncomeAmt()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getIncomeAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//二级服务商
				cell1 = sheetHeadRowIncome.createCell(6);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getServiceProviderSecond()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getServiceProviderSecond());
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//二级服务商收入
				cell1 = sheetHeadRowIncome.createCell(7);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getServiceProviderSecondIncome()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getServiceProviderSecondIncome() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//出返费公司
				cell1 = sheetHeadRowIncome.createCell(8);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getCustName()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getCustName());
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//返费公司
				cell1 = sheetHeadRowIncome.createCell(9);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeCustName()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getFeeCustName());
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//真实回款金额
				cell1 = sheetHeadRowIncome.createCell(10);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getTrueComeAmt()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getTrueComeAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//服务费
				cell1 = sheetHeadRowIncome.createCell(11);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getServiceFee()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getServiceFee() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//本金
				cell1 = sheetHeadRowIncome.createCell(12);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getPrincipal()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getPrincipal() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//本期返费
				cell1 = sheetHeadRowIncome.createCell(13);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getCurrentFee()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getCurrentFee() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//挂起金额
				cell1 = sheetHeadRowIncome.createCell(14);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getCurrentFee()).isPresent()) {
					if (proj.getIncomeList().get(i).getSuspendAmtSum() != null && proj.getIncomeList().get(i).getCurrentFee().compareTo(BigDecimal.ZERO) < 0) {
						cell1.setCellValue("+" + proj.getIncomeList().get(i).getCurrentFee().abs() + "");
					} else if (proj.getIncomeList().get(i).getSuspendClearAmt() != null && proj.getIncomeList().get(i).getSuspendAmtSum() != null && proj.getIncomeList().get(i).getCurrentFee().compareTo(BigDecimal.ZERO) > 0){
						cell1.setCellValue(proj.getIncomeList().get(i).getSuspendClearAmt() + "");
					} else {
						cell1.setCellValue("");
					}
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//挂起金额累计
				cell1 = sheetHeadRowIncome.createCell(15);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getSuspendAmtSum()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getSuspendAmtSum() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//返费
				cell1 = sheetHeadRowIncome.createCell(16);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeAmt()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getFeeAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//返费取整
				cell1 = sheetHeadRowIncome.createCell(17);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeRound()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getFeeRound() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//借条分润
				cell1 = sheetHeadRowIncome.createCell(18);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getJtfrAmt()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getJtfrAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//法催利润
				cell1 = sheetHeadRowIncome.createCell(19);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getLawProfit()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getLawProfit() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//返费已结清
				cell1 = sheetHeadRowIncome.createCell(20);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getFeeAmtSum()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getFeeAmtSum() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//返费未结清
				cell1 = sheetHeadRowIncome.createCell(21);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getUnfeeAmtSum()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getUnfeeAmtSum() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//期次状态
				cell1 = sheetHeadRowIncome.createCell(22);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getPhaseStatus()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getPhaseStatus());
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//打款状态
				cell1 = sheetHeadRowIncome.createCell(23);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getPayFlag()).isPresent()) {
//					if (proj.getIncomeList().get(i).getSuspendAmtSum() != null) {
					if (proj.getIncomeList().get(i).getSuspendAmtSum() != null && "不需打款".equals(proj.getIncomeList().get(i).getPayFlag())) {
						cell1.setCellValue("不需打款");
					}
					if (!"不需打款".equals(proj.getIncomeList().get(i).getPayFlag())) {
						cell1.setCellValue(proj.getIncomeList().get(i).getPayFlag());
					}
//					}
//					else {
//						if ("不需打款".equals(proj.getIncomeList().get(i).getPayFlag())) {
//							cell1.setCellValue("");
//						} else {
//							cell1.setCellValue(proj.getIncomeList().get(i).getPayFlag());
//						}
//					}
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//打款日期
				cell1 = sheetHeadRowIncome.createCell(24);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getPayDate()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getPayDate());
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//实际打款金额
				cell1 = sheetHeadRowIncome.createCell(25);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getPayAmt()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getPayAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//抹平差额
				cell1 = sheetHeadRowIncome.createCell(26);
				if (Optional.ofNullable(proj.getIncomeList().get(i).getDifferenceAmt()).isPresent()) {
					cell1.setCellValue(proj.getIncomeList().get(i).getDifferenceAmt() + "");
				} else {
					cell1.setCellValue("");
				}
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
				//备注
				cell1 = sheetHeadRowIncome.createCell(27);
				cell1.setCellValue(proj.getIncomeList().get(i).getRemark());
				cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
			}
		} else {
			//创建数据行（第二行）
			SXSSFRow sheetHeadRowIncome = sheet1.createRow(1);
			//业务期次
			SXSSFCell cell1 = sheetHeadRowIncome.createCell(0);
			//担保公司
			cell1 = sheetHeadRowIncome.createCell(1);
			cell1.setCellValue(proj.getCustName());
			cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
			//汇款公司
			cell1 = sheetHeadRowIncome.createCell(2);
			cell1.setCellValue(proj.getIncomeCustName());
			cell1.setCellStyle(getDataFont(sheet1.getWorkbook()));//设置样式
		}
		//合并期次单元格
		for (Map.Entry<Long, Long> m:collect.entrySet()) {
			if (m.getValue() != 1L) {
				CwProjectLawDetailView slv = proj.getIncomeList().stream().filter(t -> t.getPhaseId().equals(m.getKey())).findFirst().get();
				int index = proj.getIncomeList().indexOf(slv);
				//找到了第一个元素的索引
				//进行合并
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 0, 0));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 0, 0), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 3, 3));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 3, 3), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 8, 8));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 8, 8), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 9, 9));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 9, 9), sheet1);
//				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 18, 18));
//				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 18, 18), sheet1);
//				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 19, 19));
//				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 19, 19), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 20, 20));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 20, 20), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 21, 21));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 21, 21), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 22, 22));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 22, 22), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 27, 27));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 27, 27), sheet1);
			}
		}
		//合并返费单元格
		for (Map.Entry<Long, Long> m:collect1.entrySet()) {
			if (m.getValue() != 1L) {
				CwProjectLawDetailView slv = proj.getIncomeList().stream().filter(t -> t.getProjectFeeId() != null && t.getProjectFeeId().equals(m.getKey())).findFirst().get();
				int index = proj.getIncomeList().indexOf(slv);
				//找到了第一个元素的索引
				//进行合并
//				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 3, 3));
//				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 3, 3), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 4, 4));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 4, 4), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 5, 5));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 5, 5), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 6, 6));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 6, 6), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 7, 7));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 7, 7), sheet1);
//				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 8, 8));
//				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 8, 8), sheet1);
//				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 9, 9));
//				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 9, 9), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 10, 10));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 10, 10), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 11, 11));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 11, 11), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 12, 12));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 12, 12), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 13, 13));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 13, 13), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 14, 14));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 14, 14), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 15, 15));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 15, 15), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 16, 16));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 16, 16), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 17, 17));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 17, 17), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 18, 18));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 18, 18), sheet1);
				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 19, 19));
				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 19, 19), sheet1);
//				sheet1.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 16, 16));
//				setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 16, 16), sheet1);
			}
		}
		if (proj.getIncomeList().size() > 1) {
			sheet1.addMergedRegion(new CellRangeAddress(1, proj.getIncomeList().size(), 1, 1));
			setBorder(new CellRangeAddress(1, proj.getIncomeList().size(), 1, 1), sheet1);
			sheet1.addMergedRegion(new CellRangeAddress(1, proj.getIncomeList().size(), 2, 2));
			setBorder(new CellRangeAddress(1, proj.getIncomeList().size(), 2, 2), sheet1);
		}
//        if(1<addcounts) {
//	        sheet1.addMergedRegion(new CellRangeAddress(1, addcounts, 1, 1));
//	        setBorder(new CellRangeAddress(1, addcounts, 1, 1), sheet1);
//	        sheet1.addMergedRegion(new CellRangeAddress(1, addcounts, 2, 2));
//	        setBorder(new CellRangeAddress(1, addcounts, 2, 2), sheet1);
//        }
		// 自动调整列宽
		sheet1.trackAllColumnsForAutoSizing();
		for (int j = 0; j < colSize1; j++) {
			sheet1.autoSizeColumn(j,true);
			if (sheet1.getColumnWidth(j) * 12 / 10 > 65280) {
				sheet1.setColumnWidth(j, 65280);
			} else {
				sheet1.setColumnWidth(j, sheet1.getColumnWidth(j) * 12 / 10);
			}
		}




		/**
		 * 第二个工作表
		 */


		//创建Sheet（工作簿）
		SXSSFSheet sheet2 = workbook.createSheet(sheet2Name);

		//创建主标题行(第一行)
		SXSSFRow sheetTitleRow2 = sheet2.createRow(0);
		for(int i = 0 ; i < 2 ; i++){
			SXSSFCell headCell = sheetTitleRow2.createCell(i);
			switch (i) {
				case 0: headCell.setCellValue("返费公司名称"); break;
				case 1: headCell.setCellValue("费率"); break;
//            case 2: headCell.setCellValue("税率"); break;
				default: break;
			}
			headCell.setCellStyle(getTitleFont(sheet2.getWorkbook()));//设置样式
		}
		SXSSFRow sheetHeadRowCust;
		for (int k = 0; k < proj.getCustList().size(); k++) {
			sheetHeadRowCust = sheet2.createRow(1+k);
			SXSSFCell cellCust = sheetHeadRowCust.createCell(0);
			//返费公司
			cellCust.setCellValue(proj.getCustList().get(k).getCustName());
			cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
			//费率
			cellCust = sheetHeadRowCust.createCell(1);
			if (Optional.ofNullable(proj.getCustList().get(k).getRate()).isPresent()) {
				cellCust.setCellValue(proj.getCustList().get(k).getRate().stripTrailingZeros().toPlainString() + "%");
			} else {
				cellCust.setCellValue("未设置");
			}
			cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
//    		//税率
//    		cellCust = sheetHeadRowCust.createCell(2);
//			if (Optional.ofNullable(proj.getCustList().get(k).getTaxRate()).isPresent()) {
//				cellCust.setCellValue(proj.getCustList().get(k).getTaxRate().stripTrailingZeros().toPlainString() + "%");
//			} else {
//				cellCust.setCellValue("未设置");
//			}
//    		cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
		}

		//财务项目管理四期，新增项目成员展示
		int memberBeginIndex = proj.getCustList().size() + 2;
		//创建主标题行(第一行)
		sheetTitleRow2 = sheet2.createRow(memberBeginIndex);
		for(int i = 0 ; i < 2 ; i++){
			SXSSFCell headCell = sheetTitleRow2.createCell(i);
			switch (i) {
				case 0: headCell.setCellValue("角色"); break;
				case 1: headCell.setCellValue("用户姓名"); break;
				default: break;
			}
			headCell.setCellStyle(getTitleFont(sheet2.getWorkbook()));//设置样式
		}
		//然后把项目成员给列出来
		for (int k = 0; k < proj.getMemberList().size(); k++) {
			sheetHeadRowCust = sheet2.createRow(memberBeginIndex + 1 + k);
			SXSSFCell cellCust = sheetHeadRowCust.createCell(0);
			//角色
			cellCust.setCellValue(proj.getMemberList().get(k).get("flag").toString());
			cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
			//用户姓名
			cellCust = sheetHeadRowCust.createCell(1);
			cellCust.setCellValue(proj.getMemberList().get(k).get("people_name").toString());
			cellCust.setCellStyle(getDataFont(sheet2.getWorkbook()));//设置样式
		}

		// 自动调整列宽
		sheet2.trackAllColumnsForAutoSizing();
		sheet2.setColumnWidth(0, 6000);
		sheet2.setColumnWidth(1, 6000);
//        for (int j = 0; j < 3; j++) {
//        	sheet2.autoSizeColumn(j);
//        	sheet2.setColumnWidth(j, sheet2.getColumnWidth(j) * 12 / 10);
//		}
        
        
        //生成完成，输出下载
		//时间戳
		long ltime = System.currentTimeMillis();
		String filename = "";
        try {

			String absoluteFile = ExcelUtil.getAbsoluteFile(proj.getProjectName()+"_"+ltime+".xlsx");
			OutputStream out = new FileOutputStream(absoluteFile);
			workbook.write(out);

			filename =  absoluteFile ;
			return filename;
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
            if (workbook != null) {
                try {
                	workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
		return fileName;
    }

	
	//设置单元格合并样式
	
	public static void setBorder(CellRangeAddress a,SXSSFSheet sheet) {
        RegionUtil.setBorderTop(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, a, sheet);
	}
	
	 //标题样式
    public static CellStyle getHeaderFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 16);//字体大小
        font.setBold(true);//加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }
 
    //表头样式
    public static CellStyle getTitleFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 13);//字体大小
        font.setBold(true);//加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }
 
    //内容样式
    public static CellStyle getDataFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);//字体大小
        font.setBold(false);//不加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setWrapText(true);
        return cellStyle;
    }
 
    //处理数据
    public static String getValue(Object object){
        if (object==null){
            return "";
        }else {
            return object.toString();
        }
    }
    
    
    
    
    
    
    
    
    
    
    
    
    
    

}
