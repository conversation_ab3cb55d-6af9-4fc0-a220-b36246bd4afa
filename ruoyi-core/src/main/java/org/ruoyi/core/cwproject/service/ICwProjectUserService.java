package org.ruoyi.core.cwproject.service;

import org.ruoyi.core.cwproject.domain.CwProjectUser;

import java.util.List;

/**
 * 财务项目管理-成员Service接口
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface ICwProjectUserService
{
    /**
     * 查询财务项目管理-成员
     *
     * @param id 财务项目管理-成员主键
     * @return 财务项目管理-成员
     */
    public CwProjectUser selectCwProjectUserById(Long id);

    /**
     * 查询财务项目管理-成员列表
     *
     * @param cwProjectUser 财务项目管理-成员
     * @return 财务项目管理-成员集合
     */
    public List<CwProjectUser> selectCwProjectUserList(CwProjectUser cwProjectUser);

    /**
     * 新增财务项目管理-成员
     *
     * @param cwProjectUser 财务项目管理-成员
     * @return 结果
     */
    public int insertCwProjectUser(CwProjectUser cwProjectUser);

    /**
     * 修改财务项目管理-成员
     *
     * @param cwProjectUser 财务项目管理-成员
     * @return 结果
     */
    public int updateCwProjectUser(CwProjectUser cwProjectUser);

    /**
     * 批量删除财务项目管理-成员
     *
     * @param ids 需要删除的财务项目管理-成员主键集合
     * @return 结果
     */
    public int deleteCwProjectUserByIds(Long[] ids);

    /**
     * 删除财务项目管理-成员信息
     *
     * @param id 财务项目管理-成员主键
     * @return 结果
     */
    public int deleteCwProjectUserById(Long id);

    List<CwProjectUser> selectUserByProjectId(Long projectId);
}