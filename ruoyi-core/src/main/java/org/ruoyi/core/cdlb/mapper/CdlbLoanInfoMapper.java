package org.ruoyi.core.cdlb.mapper;

import org.ruoyi.core.cdlb.domain.CdlbLoanInfo;

import java.util.List;


/**
 * 车贷借据信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */
public interface CdlbLoanInfoMapper 
{
    /**
     * 查询车贷借据信息
     * 
     * @param id 车贷借据信息主键
     * @return 车贷借据信息
     */
    public CdlbLoanInfo selectCdlbLoanInfoById(Long id);

    /**
     * 查询车贷借据信息列表
     * 
     * @param cdlbLoanInfo 车贷借据信息
     * @return 车贷借据信息集合
     */
    public List<CdlbLoanInfo> selectCdlbLoanInfoList(CdlbLoanInfo cdlbLoanInfo);
    public List<CdlbLoanInfo> selectCdlbLoanInfos(CdlbLoanInfo cdlbLoanInfo);

    /**
     * 新增车贷借据信息
     * 
     * @param cdlbLoanInfo 车贷借据信息
     * @return 结果
     */
    public int insertCdlbLoanInfo(CdlbLoanInfo cdlbLoanInfo);

    /**
     * 修改车贷借据信息
     * 
     * @param cdlbLoanInfo 车贷借据信息
     * @return 结果
     */
    public int updateCdlbLoanInfo(CdlbLoanInfo cdlbLoanInfo);

    /**
     * 删除车贷借据信息
     * 
     * @param id 车贷借据信息主键
     * @return 结果
     */
    public int deleteCdlbLoanInfoById(Long id);
    public int selectinfoIdcounts(String clientCardId);

    /**
     * 批量删除车贷借据信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCdlbLoanInfoByIds(Long[] ids);
}