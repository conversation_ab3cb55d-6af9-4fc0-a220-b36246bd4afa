package org.ruoyi.core.cdlb.service;

import org.ruoyi.core.cdlb.domain.CdlbInOutApply;
import org.ruoyi.core.cwproject.domain.TopNotify;

import java.util.List;

/**
 * 车贷绿本出入库申请Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-20
 */
public interface ICdlbInOutApplyService 
{
    /**
     * 查询车贷绿本出入库申请
     * 
     * @param id 车贷绿本出入库申请主键
     * @return 车贷绿本出入库申请
     */
    public CdlbInOutApply selectCdlbInOutApplyById(Long id);
    public CdlbInOutApply selectCdlbInOutApplyByIdA(Long id);

    CdlbInOutApply selectCdlbInOutApplyByIdDto(Long id);

    /**
     * 查询车贷绿本出入库申请列表
     * 
     * @param cdlbInOutApply 车贷绿本出入库申请
     * @return 车贷绿本出入库申请集合
     */
    public List<CdlbInOutApply> selectCdlbInOutApplyList(CdlbInOutApply cdlbInOutApply);

    /**
     * 新增车贷绿本出入库申请
     * 
     * @param cdlbInOutApply 车贷绿本出入库申请
     * @return 结果
     */
    public int insertCdlbInOutApply(CdlbInOutApply cdlbInOutApply);

    /**
     * 修改车贷绿本出入库申请
     * 
     * @param cdlbInOutApply 车贷绿本出入库申请
     * @return 结果
     */
    public int updateCdlbInOutApply(CdlbInOutApply cdlbInOutApply);
    public int updateCdlbInOutApplyByProjectId(CdlbInOutApply cdlbInOutApply);

    /**
     * 批量删除车贷绿本出入库申请
     * 
     * @param ids 需要删除的车贷绿本出入库申请主键集合
     * @return 结果
     */
    public int deleteCdlbInOutApplyByIds(Long[] ids);

    /**
     * 删除车贷绿本出入库申请信息
     * 
     * @param id 车贷绿本出入库申请主键
     * @return 结果
     */
    public int deleteCdlbInOutApplyById(Long id);

    Integer  selectCdlbInOutApplyListCounts(CdlbInOutApply cdlbInOutApply);
    Integer  selectCdlbInOutApplyListCountschu(CdlbInOutApply cdlbInOutApply);

    /**
     * 消除本次撤销申请产生的代办通知
     * @param topNotify
     */
    void updateTopNotifyByApplyId(TopNotify topNotify);
}