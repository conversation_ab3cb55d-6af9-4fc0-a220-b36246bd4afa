package org.ruoyi.core.cdlb.domain;

import java.util.List;

/**
 *接收参数
 */
public class Param {
 public Long   id;     //项目id
 public Long   counts; //l绿本条数
    public List<tableData> tableData;
    public String rkremark;   //出入库申请说明
    public String bremark;   //驳回说明
    public List<CdlbInfo> cdlbInfos; //lvben 绿本集合


    @Override
    public String toString() {
        return "Param{" +
                "id=" + id +
                ", counts=" + counts +
                ", tableData=" + tableData +
                ", rkremark='" + rkremark + '\'' +
                ", bremark='" + bremark + '\'' +
                ", cdlbInfos=" + cdlbInfos +
                '}';
    }

    public Long getId() {

        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<tableData> getTableData() {
        return tableData;
    }

    public void setTableData(List<tableData> tableData) {
        this.tableData = tableData;
    }

    public String getRkremark() {
        return rkremark;
    }

    public void setRkremark(String rkremark) {
        this.rkremark = rkremark;
    }

    public Long getCounts() {
        return counts;
    }

    public void setCounts(Long counts) {
        this.counts = counts;
    }

    public String getBremark() {
        return bremark;
    }

    public void setBremark(String bremark) {
        this.bremark = bremark;
    }

    public List<CdlbInfo> getCdlbInfos() {
        return cdlbInfos;
    }

    public void setCdlbInfos(List<CdlbInfo> cdlbInfos) {
        this.cdlbInfos = cdlbInfos;
    }
}
