package org.ruoyi.core.cdlb.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 车贷绿本信息对象 cdlb_info
 *
 * <AUTHOR>
 * @date 2023-03-09
 */
public class CdlbInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

  private String lvbenandjiejvmap;
  private List<Paramtwo> fileNameList;

    public String getLvbenandjiejvmap() {
        return lvbenandjiejvmap;
    }

    public void setLvbenandjiejvmap(String lvbenandjiejvmap) {
        this.lvbenandjiejvmap = lvbenandjiejvmap;
    }


    private Long xvhaoId;

    /**
     *
     * 主键
     */
    private Long id;
    private Long loanInfoId;

    public String rkremark;   //出入库申请说明
    public String bremark;   //驳回说明
    public String lbNumber;   //绿本编号

    /**
     * 子标识(未审核：01，已审核：02，已驳回：03)
     */
    private String   childFlag;
    private String   temporaryFlag;
    //出库库状态
    private String   garageState;
    private List<Long>   idList;
    /**
     * 合同编号为多个
     */
    private Long  contractCodeQuantity = 0l;
    /**
     * 身份证件编号为多个
     */
    private Long  clientCardIdQuantity= 0l;;
    private Long counts;
    private Integer as;

    private Integer bs;
    /**
     * 是
     */
    private Long isId;
    /*
     * 多id*/
    private String ids;
    private String notIds;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 担保公司编号
     */
    private String custId;
    private String cdlbRecord;
    /*借据关联id*/
    private Long cdlbId;
    /**
     * 关联借据情况
     */
    private String relevance;
    //选择标识
    private Long relevanceID;
    //重新选择标识
    private Long relevanceIDB;
    private String cdlbBinding;
    private String statusB;

    /**
     * 车贷绿本管理表主键
     */
    @Excel(name = "车贷绿本管理表主键")
    private Long projectId;

    /**
     * 车贷绿本管理-入库申请表主键
     */
    @Excel(name = "车贷绿本管理-入库申请表主键")
    private Long projectInId;
   private Long projectInIdDto;

    /**
     * 车贷绿本管理-出库申请表主键
     */
    @Excel(name = "车贷绿本管理-出库申请表主键")
    private Long projectOutId;
    private Long projectOutIdDto;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号")
    private String contractCode;

    /**
     * 客户姓名
     */
    @Excel(name = "客户姓名")
    private String clientName;

    /**
     * 身份证号码
     */
    @Excel(name = "身份证号码")
    private String clientCardId;

    /**
     * 是否绑定借据 Y是 N否
     */
    @Excel(name = "是否绑定借据 Y是 N否")
    private String loanBinding;

    /**
     * 已绑定的借据申请编号,未绑定时默认为N
     */
    @Excel(name = "已绑定的借据申请编号,未绑定时默认为N")
    private String loanNo;
    private String loanIds;
    /**
     * 借据申请编号s
     */
    private String loanNos;
    private String loanNonull;

    private Long userId;
    /**
     * 邮寄日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "邮寄日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date mailDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date  reconciliationTime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerTime;


    /**
     * 绿本状态标识：10入库录入11入库申请12入库登记13入库完成20出库申请21出库审核22出库登记23出库完成
     */
    @Excel(name = "绿本状态标识：10入库录入11入库申请12入库登记13入库完成20出库申请21出库审核22出库登记23出库完成")
    private String lbFlag;
    private String notlbFlag;
    /*绿本状态集合*/
    private List<String> lbFlagList;
    private List<String> notlbFlagList;

    /**
     * 状态，0正常 1禁用
     */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    /**
     * 状态名称
     */
    private String statusName;

 /*   @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "邮寄日期", width = 30, dateFormat = "yyyy-MM-dd")*/


    //进件时间
    private Date applyTime;
    //借款金额（元）
    private BigDecimal loanAmt;
    //在贷余额
    private BigDecimal balanceAmt;
    //外部系统平台编码
    private String custNo;
    //借据信息所属担保公司
    private String custName;
    //所属担保共公司
    private String custNameA;
    private Long loanId;

    public Long getXvhaoId() {
        return xvhaoId;
    }

    public void setXvhaoId(Long xvhaoId) {
        this.xvhaoId = xvhaoId;
    }

    public BigDecimal getBalanceAmt() {
        return balanceAmt;
    }

    public void setBalanceAmt(BigDecimal balanceAmt) {
        this.balanceAmt = balanceAmt;
    }

    public List<Paramtwo> getFileNameList() {
        return fileNameList;
    }

    public void setFileNameList(List<Paramtwo> fileNameList) {
        this.fileNameList = fileNameList;
    }

    public String getGarageState() {
        return garageState;
    }

    public void setGarageState(String garageState) {
        this.garageState = garageState;
    }

    public Long getProjectOutIdDto() {
        return projectOutIdDto;
    }

    public void setProjectOutIdDto(Long projectOutIdDto) {
        this.projectOutIdDto = projectOutIdDto;
    }

    public String getLbNumber() {
        return lbNumber;
    }

    public void setLbNumber(String lbNumber) {
        this.lbNumber = lbNumber;
    }

    public String getNotlbFlag() {
        return notlbFlag;
    }

    public void setNotlbFlag(String notlbFlag) {
        this.notlbFlag = notlbFlag;
    }

    public Long getProjectInIdDto() {
        return projectInIdDto;
    }

    public void setProjectInIdDto(Long projectInIdDto) {
        this.projectInIdDto = projectInIdDto;
    }

    public String getRkremark() {
        return rkremark;
    }

    public void setRkremark(String rkremark) {
        this.rkremark = rkremark;
    }

    public String getBremark() {
        return bremark;
    }

    public void setBremark(String bremark) {
        this.bremark = bremark;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }

    public String getTemporaryFlag() {
        return temporaryFlag;
    }

    public void setTemporaryFlag(String temporaryFlag) {
        this.temporaryFlag = temporaryFlag;
    }

    public String getChildFlag() {

        return childFlag;
    }

    public void setChildFlag(String childFlag) {
        this.childFlag = childFlag;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getReconciliationTime() {
        return reconciliationTime;
    }

    public void setReconciliationTime(Date reconciliationTime) {
        this.reconciliationTime = reconciliationTime;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public Long getLoanInfoId() {
        return loanInfoId;
    }

    public void setLoanInfoId(Long loanInfoId) {
        this.loanInfoId = loanInfoId;
    }

    public String getLoanIds() {
        return loanIds;
    }

    public void setLoanIds(String loanIds) {
        this.loanIds = loanIds;
    }



    public Long getContractCodeQuantity() {
        return contractCodeQuantity;
    }

    public void setContractCodeQuantity(Long contractCodeQuantity) {
        this.contractCodeQuantity = contractCodeQuantity;
    }

    public Long getClientCardIdQuantity() {
        return clientCardIdQuantity;
    }

    public void setClientCardIdQuantity(Long clientCardIdQuantity) {
        this.clientCardIdQuantity = clientCardIdQuantity;
    }

    public Long getCounts() {
        return counts;
    }

    public void setCounts(Long counts) {
        this.counts = counts;
    }

    public List<String> getNotlbFlagList() {
        return notlbFlagList;
    }

    public void setNotlbFlagList(List<String> notlbFlagList) {
        this.notlbFlagList = notlbFlagList;
    }

    public String getNotIds() {
        return notIds;
    }

    public void setNotIds(String notIds) {
        this.notIds = notIds;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getIsId() {
        return isId;
    }

    public void setIsId(Long isId) {
        this.isId = isId;
    }

    public Integer getAs() {
        return as;
    }

    public void setAs(Integer as) {
        this.as = as;
    }

    public Integer getBs() {
        return bs;
    }

    public void setBs(Integer bs) {
        this.bs = bs;
    }

    public Long getRelevanceIDB() {
        return relevanceIDB;
    }

    public void setRelevanceIDB(Long relevanceIDB) {
        this.relevanceIDB = relevanceIDB;
    }

    public String getCdlbRecord() {
        return cdlbRecord;
    }

    public void setCdlbRecord(String cdlbRecord) {
        this.cdlbRecord = cdlbRecord;
    }

    public String getLoanNonull() {
        return loanNonull;
    }

    public void setLoanNonull(String loanNonull) {
        this.loanNonull = loanNonull;
    }

    public Long getRelevanceID() {
        return relevanceID;
    }

    public void setRelevanceID(Long relevanceID) {
        this.relevanceID = relevanceID;
    }

    public Long getLoanId() {
        return loanId;
    }

    public void setLoanId(Long loanId) {
        this.loanId = loanId;
    }



    public String getCustNameA() {
        return custNameA;
    }

    public void setCustNameA(String custNameA) {
        this.custNameA = custNameA;
    }

    public String getLoanNos() {
        return loanNos;
    }

    public void setLoanNos(String loanNos) {
        this.loanNos = loanNos;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public List<String> getLbFlagList() {
        return lbFlagList;
    }

    public void setLbFlagList(List<String> lbFlagList) {
        this.lbFlagList = lbFlagList;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getRelevance() {
        return relevance;
    }

    public void setRelevance(String relevance) {
        this.relevance = relevance;
    }

    public String getCdlbBinding() {
        return cdlbBinding;
    }

    public void setCdlbBinding(String cdlbBinding) {
        this.cdlbBinding = cdlbBinding;
    }

    public String getStatusB() {
        return statusB;
    }

    public void setStatusB(String statusB) {
        this.statusB = statusB;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectInId(Long projectInId) {
        this.projectInId = projectInId;
    }

    public Long getProjectInId() {
        return projectInId;
    }

    public void setProjectOutId(Long projectOutId) {
        this.projectOutId = projectOutId;
    }

    public Long getProjectOutId() {
        return projectOutId;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientCardId(String clientCardId) {
        this.clientCardId = clientCardId;
    }

    public String getClientCardId() {
        return clientCardId;
    }

    public void setLoanBinding(String loanBinding) {
        this.loanBinding = loanBinding;
    }

    public String getLoanBinding() {
        return loanBinding;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setMailDate(Date mailDate) {
        this.mailDate = mailDate;
    }

    public Date getMailDate() {
        return mailDate;
    }

    public void setLbFlag(String lbFlag) {
        this.lbFlag = lbFlag;
    }

    public String getLbFlag() {
        return lbFlag;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public Long getCdlbId() {
        return cdlbId;
    }

    public void setCdlbId(Long cdlbId) {
        this.cdlbId = cdlbId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    @Override
    public String toString() {
        return "CdlbInfo{" +
                "lvbenandjiejvmap='" + lvbenandjiejvmap + '\'' +
                ", fileNameList=" + fileNameList +
                ", id=" + id +
                ", loanInfoId=" + loanInfoId +
                ", rkremark='" + rkremark + '\'' +
                ", bremark='" + bremark + '\'' +
                ", lbNumber='" + lbNumber + '\'' +
                ", childFlag='" + childFlag + '\'' +
                ", temporaryFlag='" + temporaryFlag + '\'' +
                ", garageState='" + garageState + '\'' +
                ", idList=" + idList +
                ", contractCodeQuantity=" + contractCodeQuantity +
                ", clientCardIdQuantity=" + clientCardIdQuantity +
                ", counts=" + counts +
                ", as=" + as +
                ", bs=" + bs +
                ", isId=" + isId +
                ", ids='" + ids + '\'' +
                ", notIds='" + notIds + '\'' +
                ", projectName='" + projectName + '\'' +
                ", custId='" + custId + '\'' +
                ", cdlbRecord='" + cdlbRecord + '\'' +
                ", cdlbId=" + cdlbId +
                ", relevance='" + relevance + '\'' +
                ", relevanceID=" + relevanceID +
                ", relevanceIDB=" + relevanceIDB +
                ", cdlbBinding='" + cdlbBinding + '\'' +
                ", statusB='" + statusB + '\'' +
                ", projectId=" + projectId +
                ", projectInId=" + projectInId +
                ", projectInIdDto=" + projectInIdDto +
                ", projectOutId=" + projectOutId +
                ", projectOutIdDto=" + projectOutIdDto +
                ", contractCode='" + contractCode + '\'' +
                ", clientName='" + clientName + '\'' +
                ", clientCardId='" + clientCardId + '\'' +
                ", loanBinding='" + loanBinding + '\'' +
                ", loanNo='" + loanNo + '\'' +
                ", loanIds='" + loanIds + '\'' +
                ", loanNos='" + loanNos + '\'' +
                ", loanNonull='" + loanNonull + '\'' +
                ", userId=" + userId +
                ", mailDate=" + mailDate +
                ", reconciliationTime=" + reconciliationTime +
                ", registerTime=" + registerTime +
                ", lbFlag='" + lbFlag + '\'' +
                ", notlbFlag='" + notlbFlag + '\'' +
                ", lbFlagList=" + lbFlagList +
                ", notlbFlagList=" + notlbFlagList +
                ", status='" + status + '\'' +
                ", statusName='" + statusName + '\'' +
                ", applyTime=" + applyTime +
                ", loanAmt=" + loanAmt +
                ", balanceAmt=" + balanceAmt +
                ", custNo='" + custNo + '\'' +
                ", custName='" + custName + '\'' +
                ", custNameA='" + custNameA + '\'' +
                ", loanId=" + loanId +
                '}';
    }
}