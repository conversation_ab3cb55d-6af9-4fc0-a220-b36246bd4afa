package org.ruoyi.core.esign3.factory.response.data;

import java.util.List;

/**
 * 查询认证授权流程详情数据类
 * <AUTHOR>
 * @date 2025/05/06
 */
public class QryAuthFlowData {
    
    private String authFlowId;           // 认证授权流程ID
    private String authType;            // 认证授权主体类型
    private String realNameOrWillingness; // 流程中使用的认证类型
    private String realNameOrWillingnessFlowId; // 认证流程ID
    private Integer realNameStatus;     // 认证流程状态
    private Integer authorizedStatus;   // 授权流程状态
    private String authUrl;             // 认证授权长链接
    private AuthInfo authInfo;          // 认证详情
    private List<AuthorizedInfo> authorizedInfo; // 本次授权详情
    
    // 认证详情内部类
    public static class AuthInfo {
        private String willingnessAuthModes; // 流程中使用的意愿认证方式
        private String psnAuthMode;         // 本次流程中使用的个人/经办人认证方式
        private String orgAuthMode;         // 本次流程中机构实名认证使用的认证方式
        private Long authFlowCreateTime;    // 流程创建时间
        private Long authFlowUpdateTime;    // 流程最后更新时间
        private Person person;              // 个人以及机构经办人信息
        private Organization organization;  // 实名认证的机构信息
        
        // getter and setter
        public String getWillingnessAuthModes() {
            return willingnessAuthModes;
        }
        
        public void setWillingnessAuthModes(String willingnessAuthModes) {
            this.willingnessAuthModes = willingnessAuthModes;
        }
        
        public String getPsnAuthMode() {
            return psnAuthMode;
        }
        
        public void setPsnAuthMode(String psnAuthMode) {
            this.psnAuthMode = psnAuthMode;
        }
        
        public String getOrgAuthMode() {
            return orgAuthMode;
        }
        
        public void setOrgAuthMode(String orgAuthMode) {
            this.orgAuthMode = orgAuthMode;
        }
        
        public Long getAuthFlowCreateTime() {
            return authFlowCreateTime;
        }
        
        public void setAuthFlowCreateTime(Long authFlowCreateTime) {
            this.authFlowCreateTime = authFlowCreateTime;
        }
        
        public Long getAuthFlowUpdateTime() {
            return authFlowUpdateTime;
        }
        
        public void setAuthFlowUpdateTime(Long authFlowUpdateTime) {
            this.authFlowUpdateTime = authFlowUpdateTime;
        }
        
        public Person getPerson() {
            return person;
        }
        
        public void setPerson(Person person) {
            this.person = person;
        }
        
        public Organization getOrganization() {
            return organization;
        }
        
        public void setOrganization(Organization organization) {
            this.organization = organization;
        }
    }
    
    // 个人信息内部类
    public static class Person {
        private String psnId;            // 个人账号ID
        private PsnAccount psnAccount;   // 个人账号标识
        private PsnInfo psnInfo;         // 个人身份信息
        private FaceRecognitionInfo faceRecognitionInfo; // 人脸识别信息
        
        // getter and setter
        public String getPsnId() {
            return psnId;
        }
        
        public void setPsnId(String psnId) {
            this.psnId = psnId;
        }
        
        public PsnAccount getPsnAccount() {
            return psnAccount;
        }
        
        public void setPsnAccount(PsnAccount psnAccount) {
            this.psnAccount = psnAccount;
        }
        
        public PsnInfo getPsnInfo() {
            return psnInfo;
        }
        
        public void setPsnInfo(PsnInfo psnInfo) {
            this.psnInfo = psnInfo;
        }
        
        public FaceRecognitionInfo getFaceRecognitionInfo() {
            return faceRecognitionInfo;
        }
        
        public void setFaceRecognitionInfo(FaceRecognitionInfo faceRecognitionInfo) {
            this.faceRecognitionInfo = faceRecognitionInfo;
        }
    }
    
    // 个人账号标识内部类
    public static class PsnAccount {
        private String accountMobile;    // 手机号
        private String accountEmail;     // 邮箱
        
        // getter and setter
        public String getAccountMobile() {
            return accountMobile;
        }
        
        public void setAccountMobile(String accountMobile) {
            this.accountMobile = accountMobile;
        }
        
        public String getAccountEmail() {
            return accountEmail;
        }
        
        public void setAccountEmail(String accountEmail) {
            this.accountEmail = accountEmail;
        }
    }
    
    // 个人身份信息内部类
    public static class PsnInfo {
        private String psnName;          // 个人姓名
        private String psnNationality;   // 个人用户已认证的国籍/地区
        private String psnIDCardNum;     // 个人用户已认证的证件号
        private String psnIDCardType;    // 个人证件类型
        private String bankCardNum;      // 个人用户已认证的银行卡号
        private String psnMobile;        // 个人用户已认证的手机号
        
        // getter and setter
        public String getPsnName() {
            return psnName;
        }
        
        public void setPsnName(String psnName) {
            this.psnName = psnName;
        }
        
        public String getPsnNationality() {
            return psnNationality;
        }
        
        public void setPsnNationality(String psnNationality) {
            this.psnNationality = psnNationality;
        }
        
        public String getPsnIDCardNum() {
            return psnIDCardNum;
        }
        
        public void setPsnIDCardNum(String psnIDCardNum) {
            this.psnIDCardNum = psnIDCardNum;
        }
        
        public String getPsnIDCardType() {
            return psnIDCardType;
        }
        
        public void setPsnIDCardType(String psnIDCardType) {
            this.psnIDCardType = psnIDCardType;
        }
        
        public String getBankCardNum() {
            return bankCardNum;
        }
        
        public void setBankCardNum(String bankCardNum) {
            this.bankCardNum = bankCardNum;
        }
        
        public String getPsnMobile() {
            return psnMobile;
        }
        
        public void setPsnMobile(String psnMobile) {
            this.psnMobile = psnMobile;
        }
    }
    
    // 人脸识别信息内部类
    public static class FaceRecognitionInfo {
        private String facePhotoUrl;     // 刷脸照片
        private String similarityScore;  // 刷脸照片相似度得分
        private String livingScore;      // 刷脸活体检测得分
        
        // getter and setter
        public String getFacePhotoUrl() {
            return facePhotoUrl;
        }
        
        public void setFacePhotoUrl(String facePhotoUrl) {
            this.facePhotoUrl = facePhotoUrl;
        }
        
        public String getSimilarityScore() {
            return similarityScore;
        }
        
        public void setSimilarityScore(String similarityScore) {
            this.similarityScore = similarityScore;
        }
        
        public String getLivingScore() {
            return livingScore;
        }
        
        public void setLivingScore(String livingScore) {
            this.livingScore = livingScore;
        }
    }
    
    // 组织机构信息内部类
    public static class Organization {
        private String orgId;            // 机构账号ID
        private String orgName;          // 组织机构名称
        private OrgInfo orgInfo;         // 组织机构信息
        
        // getter and setter
        public String getOrgId() {
            return orgId;
        }
        
        public void setOrgId(String orgId) {
            this.orgId = orgId;
        }
        
        public String getOrgName() {
            return orgName;
        }
        
        public void setOrgName(String orgName) {
            this.orgName = orgName;
        }
        
        public OrgInfo getOrgInfo() {
            return orgInfo;
        }
        
        public void setOrgInfo(OrgInfo orgInfo) {
            this.orgInfo = orgInfo;
        }
    }
    
    // 组织机构详细信息内部类
    public static class OrgInfo {
        private String orgIDCardNum;      // 组织机构证件号
        private String orgIDCardType;     // 组织机构证件类型
        private String legalRepName;      // 法定代表人姓名
        private String legalRepIDCardNum; // 法定代表人证件号
        private String legalRepIDCardType; // 法定代表人证件类型
        
        // getter and setter
        public String getOrgIDCardNum() {
            return orgIDCardNum;
        }
        
        public void setOrgIDCardNum(String orgIDCardNum) {
            this.orgIDCardNum = orgIDCardNum;
        }
        
        public String getOrgIDCardType() {
            return orgIDCardType;
        }
        
        public void setOrgIDCardType(String orgIDCardType) {
            this.orgIDCardType = orgIDCardType;
        }
        
        public String getLegalRepName() {
            return legalRepName;
        }
        
        public void setLegalRepName(String legalRepName) {
            this.legalRepName = legalRepName;
        }
        
        public String getLegalRepIDCardNum() {
            return legalRepIDCardNum;
        }
        
        public void setLegalRepIDCardNum(String legalRepIDCardNum) {
            this.legalRepIDCardNum = legalRepIDCardNum;
        }
        
        public String getLegalRepIDCardType() {
            return legalRepIDCardType;
        }
        
        public void setLegalRepIDCardType(String legalRepIDCardType) {
            this.legalRepIDCardType = legalRepIDCardType;
        }
    }
    
    // 授权信息内部类
    public static class AuthorizedInfo {
        private String authorizedScope;   // 用户授权范围
        private Long effectiveTime;       // 授权生效时间
        private Long expireTime;          // 授权失效时间
        
        // getter and setter
        public String getAuthorizedScope() {
            return authorizedScope;
        }
        
        public void setAuthorizedScope(String authorizedScope) {
            this.authorizedScope = authorizedScope;
        }
        
        public Long getEffectiveTime() {
            return effectiveTime;
        }
        
        public void setEffectiveTime(Long effectiveTime) {
            this.effectiveTime = effectiveTime;
        }
        
        public Long getExpireTime() {
            return expireTime;
        }
        
        public void setExpireTime(Long expireTime) {
            this.expireTime = expireTime;
        }
    }
    
    // getter and setter for main class
    public String getAuthFlowId() {
        return authFlowId;
    }
    
    public void setAuthFlowId(String authFlowId) {
        this.authFlowId = authFlowId;
    }
    
    public String getAuthType() {
        return authType;
    }
    
    public void setAuthType(String authType) {
        this.authType = authType;
    }
    
    public String getRealNameOrWillingness() {
        return realNameOrWillingness;
    }
    
    public void setRealNameOrWillingness(String realNameOrWillingness) {
        this.realNameOrWillingness = realNameOrWillingness;
    }
    
    public String getRealNameOrWillingnessFlowId() {
        return realNameOrWillingnessFlowId;
    }
    
    public void setRealNameOrWillingnessFlowId(String realNameOrWillingnessFlowId) {
        this.realNameOrWillingnessFlowId = realNameOrWillingnessFlowId;
    }
    
    public Integer getRealNameStatus() {
        return realNameStatus;
    }
    
    public void setRealNameStatus(Integer realNameStatus) {
        this.realNameStatus = realNameStatus;
    }
    
    public Integer getAuthorizedStatus() {
        return authorizedStatus;
    }
    
    public void setAuthorizedStatus(Integer authorizedStatus) {
        this.authorizedStatus = authorizedStatus;
    }
    
    public String getAuthUrl() {
        return authUrl;
    }
    
    public void setAuthUrl(String authUrl) {
        this.authUrl = authUrl;
    }
    
    public AuthInfo getAuthInfo() {
        return authInfo;
    }
    
    public void setAuthInfo(AuthInfo authInfo) {
        this.authInfo = authInfo;
    }
    
    public List<AuthorizedInfo> getAuthorizedInfo() {
        return authorizedInfo;
    }
    
    public void setAuthorizedInfo(List<AuthorizedInfo> authorizedInfo) {
        this.authorizedInfo = authorizedInfo;
    }
}
