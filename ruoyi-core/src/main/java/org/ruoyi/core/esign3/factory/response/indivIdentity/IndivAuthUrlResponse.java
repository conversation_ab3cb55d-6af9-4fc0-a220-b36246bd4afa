package org.ruoyi.core.esign3.factory.response.indivIdentity;


import org.ruoyi.core.esign3.factory.response.Response;
import org.ruoyi.core.esign3.factory.response.data.IndivIdentityUrlData;

/**
 * 实名认证
 * <AUTHOR>
 * @date  2020/11/17 14:00
 * @version
 */
public class IndivAuthUrlResponse extends Response {
    private IndivIdentityUrlData data;

    public IndivIdentityUrlData getData() {
        return data;
    }

    public void setData(IndivIdentityUrlData data) {
        this.data = data;
    }
}
