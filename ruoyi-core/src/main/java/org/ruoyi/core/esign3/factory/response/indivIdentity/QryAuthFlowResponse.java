package org.ruoyi.core.esign3.factory.response.indivIdentity;

import org.ruoyi.core.esign3.factory.response.Response;
import org.ruoyi.core.esign3.factory.response.data.QryAuthFlowData;

/**
 * 查询认证授权流程详情响应类
 * <AUTHOR>
 * @date 2025/05/06
 */
public class QryAuthFlowResponse extends Response {
    
    private QryAuthFlowData data;
    
    public QryAuthFlowData getData() {
        return data;
    }
    
    public void setData(QryAuthFlowData data) {
        this.data = data;
    }
}
