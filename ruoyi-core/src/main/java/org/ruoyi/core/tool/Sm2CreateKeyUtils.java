package org.ruoyi.core.tool;


import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.*;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * @className: KeyUtils
 * @author: zuo
 * @description: SM2 秘钥创建加密解密工具类
 **/

public class Sm2CreateKeyUtils {

    // 创建秘钥方式
    public static final String SM2P256V1 ="sm2p256v1";
    /**
     * 生成国密公私钥对
     * <p>
     * <code>String[0]</code> 公钥
     * <p>
     * <code>String[1]</code> 私钥
     *
     * @return 公钥和私钥数组
     * @throws Exception 异常
     */
    public static String[] generateSmKey(int keySize) throws Exception {
        KeyPairGenerator keyPairGenerator = null;
        SecureRandom secureRandom = new SecureRandom();
        ECGenParameterSpec sm2Spec = new ECGenParameterSpec(SM2P256V1);
        keyPairGenerator = KeyPairGenerator.getInstance("EC", new BouncyCastleProvider());
        keyPairGenerator.initialize(keySize);
        keyPairGenerator.initialize(sm2Spec, secureRandom);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        PrivateKey privateKey = keyPair.getPrivate();
        PublicKey publicKey = keyPair.getPublic();
        String[] result = {new String(Base64.getUrlEncoder().encode(publicKey.getEncoded())), new String(Base64.getUrlEncoder().encode(privateKey.getEncoded()))};
        return result;
    }

    /**
     * 将Base64转码的公钥串，转化为公钥对象
     *
     * @param publicKey 公钥
     * @return 公钥对象
     */
    public static PublicKey createPublicKey(String publicKey) {
        PublicKey publickey = null;
        try {
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(Base64.getUrlDecoder().decode(publicKey));
            KeyFactory keyFactory = KeyFactory.getInstance("EC", new BouncyCastleProvider());
            publickey = keyFactory.generatePublic(publicKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return publickey;
    }

    /**
     * 将Base64转码的私钥串，转化为私钥对象
     *
     * @param privateKey 私钥
     * @return 私钥对象
     */
    public static PrivateKey createPrivateKey(String privateKey) {
        PrivateKey publickey = null;
        try {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.getUrlDecoder().decode(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance("EC", new BouncyCastleProvider());
            publickey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return publickey;
    }
}