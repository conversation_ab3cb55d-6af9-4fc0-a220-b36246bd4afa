package org.ruoyi.core.domain.Data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 外部系统平台运营情况数据对象 d_data
 *
 * <AUTHOR>
 * @date 2022-10-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DDataT extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 外部系统平台编码 */
    private String platformNo;

    /** 担保公司编码 */
    private String custNo;

    /** 合作方编码 */
    private String partnerNo;

    /** 资金方编码 */
    private String fundNo;

    /** 产品编码 */
    private String productNo;

    /** 是否映射成功（Y映射成功N映射失败） */
    private String isMapping;

    /** 数据统计时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据统计时间", width = 30, dateFormat = "yyyy年MM月dd日")
    private Date reconDate;

    @Excel(name = "累计解保笔数")
    private Long solution_stroke;

    /** 历史累计-贷款笔数（笔） */
    @Excel(name = "历史累计-贷款笔数")
    private Long totalCount;

    /** 历史累计-累计贷款本金（元） */
    @Excel(name = "历史累计-累计贷款本金")
    private BigDecimal totalAmount;

    /** 历史累计-累计贷款本金余额（元） */
    @Excel(name = "历史累计-累计贷款本金余额")
    private BigDecimal totalBalanceAmount;

    /** 累计还款笔数 */
    @Excel(name = "累计还款笔数")
    private Long totalRepayCount;

    /** 累计还款本金 */
    @Excel(name = "累计还款本金")
    private BigDecimal totalRepayPrintAmount;

    /** 当期新增-新增贷款笔数（笔） */
    @Excel(name = "当期新增-新增贷款笔数")
    private Long addCount;

    /** 当期新增-新增贷款本金（元） */
    @Excel(name = "当期新增-新增贷款本金")
    private BigDecimal addAmount;

    /** 当期新增还款笔数 */
    @Excel(name = "当期新增还款笔数")
    private Long addRepayCount;

    /** 当期新增还款本金 */
    @Excel(name = "当期新增还款本金")
    private BigDecimal addRepayPrintAmount;

    List<String> dateRange;

    private Map<String, List<String>> moreSearchMap;

    private String moreSearch;
}
