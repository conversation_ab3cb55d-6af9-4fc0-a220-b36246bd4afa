package org.ruoyi.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 sys_select_data_ref
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
public class SysSelectDataRef extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 外部系统id */
    @Excel(name = "外部系统id")
    private Long platformId;

    /** 担保公司id */
    @Excel(name = "担保公司id")
    private String custId;

    /** 合作方id */
    @Excel(name = "合作方id")
    private String partnerId;

    /** 资金方id */
    @Excel(name = "资金方id")
    private String fundId;

    /** 产品id */
    @Excel(name = "产品id")
    private Long productId;

    /** 产品编码 */
    @Excel(name = "产品编码")
    private String productNo;

    private Long projectId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPlatformId(Long platformId) 
    {
        this.platformId = platformId;
    }

    public Long getPlatformId() 
    {
        return platformId;
    }
    public void setCustId(String custId)
    {
        this.custId = custId;
    }

    public String getCustId()
    {
        return custId;
    }
    public void setPartnerId(String partnerId)
    {
        this.partnerId = partnerId;
    }

    public String getPartnerId()
    {
        return partnerId;
    }
    public void setFundId(String fundId)
    {
        this.fundId = fundId;
    }

    public String getFundId()
    {
        return fundId;
    }
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    public void setProductNo(String productNo) 
    {
        this.productNo = productNo;
    }

    public String getProductNo() 
    {
        return productNo;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    @Override
    public String toString() {
        return "SysSelectDataRef{" +
                "id=" + id +
                ", platformId=" + platformId +
                ", custId=" + custId +
                ", partnerId=" + partnerId +
                ", fundId=" + fundId +
                ", productId=" + productId +
                ", productNo='" + productNo + '\'' +
                ", projectId=" + projectId +
                '}';
    }
}
