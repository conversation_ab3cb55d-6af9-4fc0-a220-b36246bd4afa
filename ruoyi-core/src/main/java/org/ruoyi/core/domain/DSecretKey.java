package org.ruoyi.core.domain;

import com.ruoyi.common.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 系统对接秘钥表(DSecretKey)实体类
 *
 * <AUTHOR>
 * @since 2022-03-31 11:29:31
 */
public class DSecretKey implements Serializable {
    private static final long serialVersionUID = -73431611357952098L;
    /**
     * 主键ID
     */
    @Excel(name = "主键ID")

    private Integer id;
    /**
     * 外部系统平台编码
     */
    @Excel(name = "外部系统平台编码")
    @NotNull(message = "系统平台编码不能为空")
    private String platformNo;
    /**
     * 私钥使用类型（0数据平台使用 1外部平台使用）
     */
    @Excel(name = "私钥使用类型", readConverterExp = "0=数据平台使用,1=外部平台使用")
    @NotNull(message = "私钥使用类型不能为空")
    private String useType;
    /**
     * 秘钥类型  1对称加密 2非对称加密
     */
    @NotNull(message = "秘钥类型不能为空")
    @Excel(name = "秘钥类型", readConverterExp = "1=对称加密,2=非对称加密")
    private String keyType;
    /**
     * 秘钥算法  AES RSA ECC SM2 SM4
     */
    @NotNull(message = "秘钥算法不能为空")
    @Excel(name = "秘钥算法")
    private String keyAlgorithm;
    /**
     * 工作模式
     */
    @NotNull(message = "工作模式不能为空")
    @Excel(name = "工作模式")
    private String keySignatureAlgorithm;
    /**
     * 秘钥长度
     */
    @NotNull(message = "秘钥长度不能为空")
    @Excel(name = "秘钥长度")
    private String keyLength;
    /**
     * 字符编码
     */
    @NotNull(message = "字符编码不能为空")
    @Excel(name = "字符编码")
    private String keyCharset;
    /**
     * 非对称加密私钥使用公司名称
     */
    @Excel(name = "非对称加密私钥使用公司名称")
    private String privateCustName1;
    /**
     * 非对称加密私钥使用系统名称
     */
    @Excel(name = "非对称加密私钥使用系统名称")
    private String privateSysName1;
    /**
     * 非对称加密私钥
     */
    @Excel(name = "非对称加密私钥")
    private String privateKey;
    /**
     * 非对称加密公钥使用公司名称
     */
    @Excel(name = "非对称加密公钥使用公司名称")
    private String publicCustName2;
    /**
     * 非对称加密公钥使用系统名称
     */
    @Excel(name = "非对称加密公钥使用系统名称")
    private String publicSysName2;
    /**
     * 非对称加密公钥
     */
    @Excel(name = "非对称加密公钥")
    private String publicKey;
    /**
     * 对称加密秘钥使用公司名称1
     */
    @Excel(name = "对称加密秘钥使用公司名称1")
    private String secretCustName1;
    /**
     * 对称加密秘钥使用系统名称1
     */
    @Excel(name = "对称加密秘钥使用系统名称1")
    private String secretSysName1;
    /**
     * 对称加密秘钥使用公司名称2
     */
    @Excel(name = "对称加密秘钥使用公司名称2")
    private String secretCustName2;
    /**
     * 对称加密秘钥使用系统名称2
     */
    @Excel(name = "对称加密秘钥使用系统名称2")
    private String secretSysName2;
    /**
     * 对称加密秘钥
     */
    @Excel(name = "对称加密秘钥")
    private String secretKey;
    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;
    /**
     * 状态（0正常 1停用）
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @NotNull(message = "状态不能为空")
    private String status;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd")
    private Date createTime;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", dateFormat = "yyyy-MM-dd")
    private Date updateTime;
    /**
     * 创建名称
     */
    @Excel(name = "创建名称")
    private String createName;
    /**
     * 修改名称
     */
    @Excel(name = "修改人名称")
    private String updateName;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPlatformNo() {
        return platformNo;
    }

    public void setPlatformNo(String platformNo) {
        this.platformNo = platformNo;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getKeyAlgorithm() {
        return keyAlgorithm;
    }

    public void setKeyAlgorithm(String keyAlgorithm) {
        this.keyAlgorithm = keyAlgorithm;
    }

    public String getKeySignatureAlgorithm() {
        return keySignatureAlgorithm;
    }

    public void setKeySignatureAlgorithm(String keySignatureAlgorithm) {
        this.keySignatureAlgorithm = keySignatureAlgorithm;
    }

    public String getKeyLength() {
        return keyLength;
    }

    public void setKeyLength(String keyLength) {
        this.keyLength = keyLength;
    }

    public String getKeyCharset() {
        return keyCharset;
    }

    public void setKeyCharset(String keyCharset) {
        this.keyCharset = keyCharset;
    }


    public String getPrivateCustName1() {
        return privateCustName1;
    }

    public void setPrivateCustName1(String privateCustName1) {
        this.privateCustName1 = privateCustName1;
    }

    public String getPrivateSysName1() {
        return privateSysName1;
    }

    public void setPrivateSysName1(String privateSysName1) {
        this.privateSysName1 = privateSysName1;
    }

    public String getPublicCustName2() {
        return publicCustName2;
    }

    public void setPublicCustName2(String publicCustName2) {
        this.publicCustName2 = publicCustName2;
    }

    public String getPublicSysName2() {
        return publicSysName2;
    }

    public void setPublicSysName2(String publicSysName2) {
        this.publicSysName2 = publicSysName2;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }


    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getSecretCustName1() {
        return secretCustName1;
    }

    public void setSecretCustName1(String secretCustName1) {
        this.secretCustName1 = secretCustName1;
    }

    public String getSecretSysName1() {
        return secretSysName1;
    }

    public void setSecretSysName1(String secretSysName1) {
        this.secretSysName1 = secretSysName1;
    }

    public String getSecretCustName2() {
        return secretCustName2;
    }

    public void setSecretCustName2(String secretCustName2) {
        this.secretCustName2 = secretCustName2;
    }

    public String getSecretSysName2() {
        return secretSysName2;
    }

    public void setSecretSysName2(String secretSysName2) {
        this.secretSysName2 = secretSysName2;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

}

