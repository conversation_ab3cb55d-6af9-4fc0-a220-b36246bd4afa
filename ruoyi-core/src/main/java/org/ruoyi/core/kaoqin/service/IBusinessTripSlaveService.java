package org.ruoyi.core.kaoqin.service;

import org.ruoyi.core.kaoqin.domain.BusinessTripSlave;

import java.util.List;

/**
 * 出差申请-从Service接口
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
public interface IBusinessTripSlaveService
{
    /**
     * 查询出差申请-从
     *
     * @param id 出差申请-从主键
     * @return 出差申请-从
     */
    public BusinessTripSlave selectBusinessTripSlaveById(Long id);

    /**
     * 查询出差申请-从列表
     *
     * @param businessTripSlave 出差申请-从
     * @return 出差申请-从集合
     */
    public List<BusinessTripSlave> selectBusinessTripSlaveList(BusinessTripSlave businessTripSlave);

    /**
     * 新增出差申请-从
     *
     * @param businessTripSlave 出差申请-从
     * @return 结果
     */
    public int insertBusinessTripSlave(BusinessTripSlave businessTripSlave);

    /**
     * 修改出差申请-从
     *
     * @param businessTripSlave 出差申请-从
     * @return 结果
     */
    public int updateBusinessTripSlave(BusinessTripSlave businessTripSlave);

    /**
     * 批量删除出差申请-从
     *
     * @param ids 需要删除的出差申请-从主键集合
     * @return 结果
     */
    public int deleteBusinessTripSlaveByIds(Long[] ids);

    /**
     * 删除出差申请-从信息
     *
     * @param id 出差申请-从主键
     * @return 结果
     */
    public int deleteBusinessTripSlaveById(Long id);

    public int insertBusinessTripSlaveBatch(List<BusinessTripSlave> businessTripSlaveList);

    public int deleteBusinessTripSlaveByMainId(Long mainId);
}
