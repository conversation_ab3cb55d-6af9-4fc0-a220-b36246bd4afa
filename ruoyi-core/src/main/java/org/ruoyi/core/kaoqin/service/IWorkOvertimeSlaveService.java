package org.ruoyi.core.kaoqin.service;

import org.ruoyi.core.kaoqin.domain.WorkOvertimeSlave;
import java.util.List;

/**
 * 加班从Service接口
 *
 * <AUTHOR>
 * @date 2024-07-04
 */
public interface IWorkOvertimeSlaveService
{
    /**
     * 查询加班从
     *
     * @param id 加班从主键
     * @return 加班从
     */
    public WorkOvertimeSlave selectWorkOvertimeSlaveById(Long id);

    /**
     * 查询加班从列表
     *
     * @param workOvertimeSlave 加班从
     * @return 加班从集合
     */
    public List<WorkOvertimeSlave> selectWorkOvertimeSlaveList(WorkOvertimeSlave workOvertimeSlave);

    /**
     * 新增加班从
     *
     * @param workOvertimeSlave 加班从
     * @return 结果
     */
    public int insertWorkOvertimeSlave(WorkOvertimeSlave workOvertimeSlave);
    /**
    * 批量新增
     * @param workOvertimeSlave
     * @return
    */
    public int insertWorkOvertimeSlaveBatch(List<WorkOvertimeSlave> workOvertimeSlave);
    /**
     * 修改加班从
     *
     * @param workOvertimeSlave 加班从
     * @return 结果
     */
    public int updateWorkOvertimeSlave(WorkOvertimeSlave workOvertimeSlave);

    /**
     * 批量删除加班从
     *
     * @param ids 需要删除的加班从主键集合
     * @return 结果
     */
    public int deleteWorkOvertimeSlaveByIds(Long[] ids);

    /**
     * 删除加班从信息
     *
     * @param id 加班从主键
     * @return 结果
     */
    public int deleteWorkOvertimeSlaveById(Long id);

    public int deleteWorkOvertimeSlaveByOverTimeId(Long overTimeId);
}
