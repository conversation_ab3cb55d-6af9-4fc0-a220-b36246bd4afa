package org.ruoyi.core.kaoqin.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.kaoqin.domain.BusinessTripSlave;
import org.ruoyi.core.kaoqin.service.IBusinessTripSlaveService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 出差申请-从Controller
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
@RestController
@RequestMapping("/business/trip/slave")
public class BusinessTripSlaveController extends BaseController
{
    @Autowired
    private IBusinessTripSlaveService businessTripSlaveService;

    /**
     * 查询出差申请-从列表
     */
    @PreAuthorize("@ss.hasPermi('system:slave:list')")
    @GetMapping("/list")
    public TableDataInfo list(BusinessTripSlave businessTripSlave)
    {
        startPage();
        List<BusinessTripSlave> list = businessTripSlaveService.selectBusinessTripSlaveList(businessTripSlave);
        return getDataTable(list);
    }

    /**
     * 导出出差申请-从列表
     */
    @PreAuthorize("@ss.hasPermi('system:slave:export')")
    @Log(title = "出差申请-从", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusinessTripSlave businessTripSlave)
    {
        List<BusinessTripSlave> list = businessTripSlaveService.selectBusinessTripSlaveList(businessTripSlave);
        ExcelUtil<BusinessTripSlave> util = new ExcelUtil<BusinessTripSlave>(BusinessTripSlave.class);
        util.exportExcel(response, list, "出差申请-从数据");
    }

    /**
     * 获取出差申请-从详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:slave:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(businessTripSlaveService.selectBusinessTripSlaveById(id));
    }

    /**
     * 新增出差申请-从
     */
    @PreAuthorize("@ss.hasPermi('system:slave:add')")
    @Log(title = "出差申请-从", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BusinessTripSlave businessTripSlave)
    {
        return toAjax(businessTripSlaveService.insertBusinessTripSlave(businessTripSlave));
    }

    /**
     * 修改出差申请-从
     */
    @PreAuthorize("@ss.hasPermi('system:slave:edit')")
    @Log(title = "出差申请-从", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BusinessTripSlave businessTripSlave)
    {
        return toAjax(businessTripSlaveService.updateBusinessTripSlave(businessTripSlave));
    }

    /**
     * 删除出差申请-从
     */
    @PreAuthorize("@ss.hasPermi('system:slave:remove')")
    @Log(title = "出差申请-从", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(businessTripSlaveService.deleteBusinessTripSlaveByIds(ids));
    }
}
