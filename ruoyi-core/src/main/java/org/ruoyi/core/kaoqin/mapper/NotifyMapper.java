package org.ruoyi.core.kaoqin.mapper;

import org.ruoyi.core.kaoqin.domain.Notify;
import org.ruoyi.core.kaoqin.domain.vo.NotifyVo;
import org.ruoyi.core.personnel.domain.vo.ProcessEndTime;

import java.util.List;

/**
 * 月报提醒Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface NotifyMapper
{
    /**
     * 查询月报提醒
     *
     * @param id 月报提醒主键
     * @return 月报提醒
     */
    public Notify selectMonthLogNotifyById(Long id);

    /**
     * 查询月报提醒列表
     *
     * @param monthLogNotify 月报提醒
     * @return 月报提醒集合
     */
    public List<Notify> selectMonthLogNotifyList(Notify monthLogNotify);

    /**
     * 新增月报提醒
     *
     * @param monthLogNotify 月报提醒
     * @return 结果
     */
    public int insertMonthLogNotify(Notify monthLogNotify);

    int insertMonthLogNotifyBatch(List<NotifyVo> notifyList);

    /**
     * 修改月报提醒
     *
     * @param monthLogNotify 月报提醒
     * @return 结果
     */
    public int updateMonthLogNotify(Notify monthLogNotify);

    /**
     * 删除月报提醒
     *
     * @param id 月报提醒主键
     * @return 结果
     */
    public int deleteMonthLogNotifyById(Long id);

    /**
     * 批量删除月报提醒
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthLogNotifyByIds(Long[] ids);

    public int reviewedNotify(Notify monthLogNotify);

    public int confirmNotify(Notify monthLogNotify);

    List<ProcessEndTime> getProcessEndTime(String businessKey);

    List<Long> getVoidUserId();
}
