package org.ruoyi.core.kaoqin.service.impl;

import java.text.SimpleDateFormat;
import java.util.List;import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.kaoqin.domain.AskLeave;
import org.ruoyi.core.kaoqin.domain.AskLeaveSlave;
import org.ruoyi.core.kaoqin.domain.vo.AskLeaveVo;
import org.ruoyi.core.kaoqin.domain.vo.Handover;
import org.ruoyi.core.kaoqin.domain.vo.NotifyVo;
import org.ruoyi.core.kaoqin.mapper.AskLeaveMapper;
import org.ruoyi.core.kaoqin.service.IAskLeaveService;
import org.ruoyi.core.kaoqin.service.IAskLeaveSlaveService;
import org.ruoyi.core.kaoqin.service.INotifyService;
import org.ruoyi.core.personnel.service.IPersonnelArchivesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;


/**
 * 请假Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Service
public class AskLeaveServiceImpl implements IAskLeaveService
{
    @Autowired
    private AskLeaveMapper askLeaveMapper;
    @Autowired
    private IAskLeaveSlaveService askLeaveSlaveService;
    @Autowired
    private INotifyService notifyService;
    @Autowired
    private IPersonnelArchivesService personnelArchivesService;

    /**
     * 查询请假
     *
     * @param id 请假主键
     * @return 请假
     */
    @Override
    public AskLeave selectAskLeaveByHandleId(Long id)
    {
        return askLeaveMapper.selectAskLeaveByHandleId(id);
    }

    @Override
    public AskLeave selectAskLeaveById(Long id)
    {
        AskLeave leave = askLeaveMapper.selectAskLeaveById(id);
        if (leave.getHandover() != null && !leave.getHandover().isEmpty()){
            try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<Handover> handoverList = objectMapper.readValue(leave.getHandover(),  new TypeReference<List<Handover>>() {});
            leave.setHandoverList(handoverList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return leave;
    }

    @Override
    public AskLeave selectAskLeaveByProcessId(String processId){
        AskLeave leave = askLeaveMapper.selectAskLeaveByProcessId(processId);
        if (leave.getHandover() != null && !leave.getHandover().isEmpty()){
            try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<Handover> handoverList = objectMapper.readValue(leave.getHandover(),  new TypeReference<List<Handover>>() {});
            leave.setHandoverList(handoverList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return leave;
    }
    /**
     * 查询请假列表
     *
     * @param askLeave 请假
     * @return 请假
     */
    @Override
    public List<AskLeave> selectAskLeaveList(AskLeaveVo askLeave)
    {
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRange(loginUser);
        askLeave.setDeptIds(dataRange.get("deptIds"));
        askLeave.setUnitIds(dataRange.get("unitIds"));
        askLeave.setCreateBy(loginUser.getUser().getUserName());

        PageUtil.startPage();
        List<AskLeave> askLeaves = askLeaveMapper.selectAskLeaveList(askLeave);
        askLeaves.forEach(row -> {
            if (row.getHandover() != null && !row.getHandover().isEmpty()){
                try {
                ObjectMapper objectMapper = new ObjectMapper();
                List<Handover> handoverList = objectMapper.readValue(row.getHandover(),  new TypeReference<List<Handover>>() {});
                row.setHandoverList(handoverList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        return askLeaves;
    }

    @Override
    public List<AskLeave> selectAskLeaveListSelf(AskLeave askLeave)
    {
        return askLeaveMapper.selectAskLeaveListSelf(askLeave);
    }

    /**
     * 新增请假
     *
     * @param askLeave 请假
     * @return 结果
     */
    @Override
    public AskLeave insertAskLeave(AskLeaveVo askLeave)
    {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
        List<String> dates = askLeave.getAskLeaveSlaveList().stream()
                                     .flatMap(slave -> Stream.of(
                                             dateFormat.format(slave.getStartTime()) + " "+timeFormat.format(slave.getStartTimePeriod())
                                                   , dateFormat.format(slave.getEndTime()) + " " + timeFormat.format(slave.getEndTimePeriod())))
                                      .collect(Collectors.toList());
        askLeave.setDates(dates);
        askLeave.setCreateBy(getLoginUser().getUsername());
        int i = askLeaveMapper.inspectionTime(askLeave);
        if (i > 0){
            throw new ServiceException("所选时间段与已提交请假申请重叠");
        }
        if (askLeave.getHandoverList() != null){
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                askLeave.setHandover(objectMapper.writeValueAsString(askLeave.getHandoverList()));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        if (askLeave.getId() == null){
            askLeave.setCreateTime(DateUtils.getNowDate());
            askLeave.setIsDelete("1");
            askLeave.setState("1");
            askLeave.setNickName(getLoginUser().getUser().getNickName());
            //系统编号逻辑
            int count = getCountByCreateTime(DateUtils.getDate()) + 1;
            String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
            askLeave.setAskLeaveCode("QJSQ" + createTimeNum + String.format("%03d", count));
            List<AskLeaveSlave> leaveSlaves =  askLeave.getAskLeaveSlaveList();
            askLeaveMapper.insertAskLeave(askLeave);
            leaveSlaves.forEach( slave ->{
                slave.setLeaveId(askLeave.getId());
                slave.setCreateBy(getLoginUser().getUsername());
            });
            askLeaveSlaveService.insertAskLeaveSlaveBatch(leaveSlaves);
        } else {
            //审核不通过重新提交,生成新的数据
            if(askLeave.getState() != null && "4".equals(askLeave.getState())){
                //逻辑删除原来的数据
                AskLeave leave = askLeaveMapper.selectAskLeaveById(askLeave.getId());
                leave.setIsDelete("0");
                askLeaveMapper.updateAskLeave(leave);
                //系统编号逻辑
                int count = getCountByCreateTime(DateUtils.getDate()) + 1;
                String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
                askLeave.setAskLeaveCode("QJSQ" + createTimeNum + String.format("%03d", count));
                askLeave.setProcessId("");
                askLeave.setState("1");
                askLeave.setNickName(getLoginUser().getUser().getNickName());
                askLeaveMapper.insertAskLeave(askLeave);
                List<AskLeaveSlave> leaveSlaves =  askLeave.getAskLeaveSlaveList();
                leaveSlaves.forEach( slave ->{
                    slave.setLeaveId(askLeave.getId());
                    slave.setCreateBy(getLoginUser().getUsername());
                });
                askLeaveSlaveService.insertAskLeaveSlaveBatch(leaveSlaves);
                return askLeave;
            }
            askLeaveSlaveService.deleteAskLeaveSlaveByLeaveId(askLeave.getId());
            List<AskLeaveSlave> leaveSlaves =  askLeave.getAskLeaveSlaveList();
            leaveSlaves.forEach( slave ->{
                slave.setLeaveId(askLeave.getId());
                slave.setCreateBy(getLoginUser().getUsername());
            });
            askLeaveSlaveService.insertAskLeaveSlaveBatch(leaveSlaves);
            askLeave.setUpdateTime(DateUtils.getNowDate());
            askLeave.setUpdateBy(getLoginUser().getUsername());
            askLeaveMapper.updateAskLeave(askLeave);
        }
        return askLeave;
    }

    @Override
    public int getCountByCreateTime(String createTime){
        return askLeaveMapper.getCountByCreateTime(createTime);
    }

    /**
     * 修改请假
     *
     * @param askLeave 请假
     * @return 结果
     */
    @Override
    public int updateAskLeave(AskLeave askLeave)
    {
        askLeave.setUpdateTime(DateUtils.getNowDate());
        return askLeaveMapper.updateAskLeave(askLeave);
    }

    @Override
    public int commitAskLeave(AskLeave askLeave)
    {
        askLeave.setUpdateTime(DateUtils.getNowDate());
        askLeave.setApplicationTime(DateUtils.getNowDate());
        askLeave.setState("2");
        return askLeaveMapper.updateAskLeave(askLeave);
    }
    /**
     * 批量删除请假
     *
     * @param ids 需要删除的请假主键
     * @return 结果
     */
    @Override
    public int deleteAskLeaveByIds(Long[] ids)
    {
        return askLeaveMapper.deleteAskLeaveByIds(ids);
    }

    /**
     * 删除请假信息
     *
     * @param id 请假主键
     * @return 结果
     */
    @Override
    public int deleteAskLeaveById(Long id)
    {
        return askLeaveMapper.deleteAskLeaveById(id);
    }

    @Override
    public int passAskLeaveById(Long id) {
        return askLeaveMapper.passAskLeaveById(id);
    }

    @Override
    public int unpassAskLeaveById(Long id) {
        return askLeaveMapper.unpassAskLeaveById(id);
    }

    @Override
    public int voidAskLeave(AskLeave askLeave){
        askLeave.setUpdateTime(DateUtils.getNowDate());
        askLeave.setVoidTime(DateUtils.getNowDate());
        NotifyVo notifyVo = new NotifyVo();
        notifyVo.setProcessId(askLeave.getProcessId());
        notifyVo.setCorrelationId(askLeave.getId());
        notifyService.insertAskLeaveNotify(notifyVo);
        return askLeaveMapper.voidAskLeave(askLeave);
    }
}
