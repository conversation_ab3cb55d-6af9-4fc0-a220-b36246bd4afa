package org.ruoyi.core.kaoqin.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.kaoqin.domain.WorkOvertime;
import org.ruoyi.core.kaoqin.domain.vo.WorkOvertimeVo;
import org.ruoyi.core.kaoqin.service.IWorkOvertimeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 加班申请Controller
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
@RestController
@RequestMapping("/work/overtime")
public class WorkOvertimeController extends BaseController
{
    @Autowired
    private IWorkOvertimeService workOvertimeService;

    /**
     * 查询加班申请列表
     */
   //@PreAuthorize("@ss.hasPermi('system:overtime:list')")
    @GetMapping("/list")
    public TableDataInfo list(WorkOvertimeVo workOvertime)
    {
        List<WorkOvertime> list = workOvertimeService.selectWorkOvertimeList(workOvertime);
        return getDataTable(list);
    }

    /**
     * 导出加班申请列表
     */
   //@PreAuthorize("@ss.hasPermi('system:overtime:export')")
    @Log(title = "加班申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WorkOvertimeVo workOvertime)
    {
        List<WorkOvertime> list = workOvertimeService.selectWorkOvertimeList(workOvertime);
        ExcelUtil<WorkOvertime> util = new ExcelUtil<WorkOvertime>(WorkOvertime.class);
        util.exportExcel(response, list, "加班申请数据");
    }

    /**
     * 获取加班申请详细信息
     */
   //@PreAuthorize("@ss.hasPermi('system:overtime:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(workOvertimeService.selectWorkOvertimeById(id));
    }

    @GetMapping(value = "/handle/{id}")
    public AjaxResult getHandleInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(workOvertimeService.selectWorkOvertimeByHandleId(id));
    }

    /**
     * 获取加班详细信息,processId
     *
     * */
   //@PreAuthorize("@ss.hasPermi('system:leave:query')")
    @GetMapping(value = "/processId/{processId}")
    public AjaxResult getWorkOvertime(@PathVariable("processId") String processId)
    {
        return AjaxResult.success(workOvertimeService.selectWorkOvertimeByProcessId(processId));
    }

    /**
     * 新增加班申请
     */
   //@PreAuthorize("@ss.hasPermi('system:overtime:add')")
    @Log(title = "加班申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WorkOvertimeVo workOvertime)
    {
        return AjaxResult.success(workOvertimeService.insertWorkOvertime(workOvertime));
    }

    /**
     * 修改加班申请
     */
   //@PreAuthorize("@ss.hasPermi('system:overtime:edit')")
    @Log(title = "加班申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WorkOvertime workOvertime)
    {
        return toAjax(workOvertimeService.updateWorkOvertime(workOvertime));
    }

    /**
     * 删除加班申请
     */
   //@PreAuthorize("@ss.hasPermi('system:overtime:remove')")
    @Log(title = "加班申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(workOvertimeService.deleteWorkOvertimeByIds(ids));
    }

    /**
     * 提交加班
     */
   //@PreAuthorize("@ss.hasPermi('system:leave:edit')")
    @Log(title = "提交请假", businessType = BusinessType.UPDATE)
    @PutMapping("/commitWorkOvertime")
    public AjaxResult commitWorkOvertime(@RequestBody WorkOvertime workOvertime)
    {
        return toAjax(workOvertimeService.commitWorkOvertime(workOvertime));
    }

    /**
     * 审核通过加班申请
     */
    @Log(title = "审核通过加班申请", businessType = BusinessType.UPDATE)
    @PostMapping("/passWorkOvertime")
    public AjaxResult passWorkOvertimeById(@RequestBody Long id)
    {
        return toAjax(workOvertimeService.passWorkOvertimeById(id));
    }

    /**
     * 审核不通过加班申请
     */
    @Log(title = "审核不通过加班申请", businessType = BusinessType.UPDATE)
    @PostMapping("/unpassWorkOvertime")
    public AjaxResult unpassWorkOvertimeById(@RequestBody Long id)
    {
        return toAjax(workOvertimeService.unpassWorkOvertimeById(id));
    }

    @Log(title = "加班申请废弃", businessType = BusinessType.UPDATE)
    @PutMapping("/voidWorkOvertime")
    public AjaxResult voidWorkOvertime(@RequestBody WorkOvertime workOvertime)
    {
        return toAjax(workOvertimeService.voidWorkOvertime(workOvertime));
    }
}
