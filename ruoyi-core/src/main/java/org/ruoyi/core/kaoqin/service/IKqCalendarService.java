package org.ruoyi.core.kaoqin.service;

import java.util.List;
import org.ruoyi.core.kaoqin.domain.KqCalendar;
import org.ruoyi.core.kaoqin.domain.vo.DayLogListVo;

/**
 * 考勤日历Service接口
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
public interface IKqCalendarService
{
    /**
     * 查询考勤日历
     *
     * @param id 考勤日历主键
     * @return 考勤日历
     */
    public KqCalendar selectKqCalendarById(Long id);

    /**
     * 查询考勤日历列表
     *
     * @param kqCalendar 考勤日历
     * @return 考勤日历集合
     */
    public List<KqCalendar> selectKqCalendarList(KqCalendar kqCalendar);

    public Long selectKqCalendarCount(KqCalendar kqCalendar);

    public List<DayLogListVo> selectKqCalendarListForMonth(DayLogListVo dayLogListVo);

    public List<DayLogListVo> selectKqCalendarListForMonthDesc(DayLogListVo dayLogListVo);

    /**
     * 新增考勤日历
     *
     * @param kqCalendar 考勤日历
     * @return 结果
     */
    public int insertKqCalendar(KqCalendar kqCalendar);

    public int insertKqCalendars(KqCalendar kqCalendar);

    /**
     * 修改考勤日历
     *
     * @param kqCalendar 考勤日历
     * @return 结果
     */
    public int updateKqCalendar(KqCalendar kqCalendar);

    /**
     * 批量删除考勤日历
     *
     * @param ids 需要删除的考勤日历主键集合
     * @return 结果
     */
    public int deleteKqCalendarByIds(Long[] ids);

    /**
     * 删除考勤日历信息
     *
     * @param id 考勤日历主键
     * @return 结果
     */
    public int deleteKqCalendarById(Long id);

    public int replaceKqCalendarBatch(List<KqCalendar> kqCalendarList);
}
