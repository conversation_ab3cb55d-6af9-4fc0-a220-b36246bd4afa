package org.ruoyi.core.kaoqin.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.kaoqin.domain.DayLogGroup;
import org.ruoyi.core.kaoqin.service.IDayLogGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日志查询-快速分组列Controller
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/day/log/group")
public class DayLogGroupController extends BaseController
{
    @Autowired
    private IDayLogGroupService dayLogGroupService;

    /**
     * 查询日志查询-快速分组列列表
     */
    //@PreAuthorize("@ss.hasPermi('system:group:list')")
    @GetMapping("/list")
    public TableDataInfo list(DayLogGroup dayLogGroup)
    {
        startPage();
        List<DayLogGroup> list = dayLogGroupService.selectDayLogGroupList(dayLogGroup);
        return getDataTable(list);
    }

    /**
     * 导出日志查询-快速分组列列表
     */
    //@PreAuthorize("@ss.hasPermi('system:group:export')")
    @Log(title = "日志查询-快速分组列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DayLogGroup dayLogGroup)
    {
        List<DayLogGroup> list = dayLogGroupService.selectDayLogGroupList(dayLogGroup);
        ExcelUtil<DayLogGroup> util = new ExcelUtil<DayLogGroup>(DayLogGroup.class);
        util.exportExcel(response, list, "日志查询-快速分组列数据");
    }

    /**
     * 获取日志查询-快速分组列详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:group:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(dayLogGroupService.selectDayLogGroupById(id));
    }

    @GetMapping(value = "/user")
    public TableDataInfo selectGroupUserList(DayLogGroup group)
    {
        List<SysUser> sysUsers = dayLogGroupService.selectGroupUserList(group);
        return getDataTable(sysUsers);
    }

    /**
     * 新增日志查询-快速分组列
     */
    //@PreAuthorize("@ss.hasPermi('system:group:add')")
    @Log(title = "日志查询-快速分组列", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DayLogGroup dayLogGroup)
    {
        return toAjax(dayLogGroupService.insertDayLogGroup(dayLogGroup));
    }

    /**
     * 修改日志查询-快速分组列
     */
    //@PreAuthorize("@ss.hasPermi('system:group:edit')")
    @Log(title = "日志查询-快速分组列", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DayLogGroup dayLogGroup)
    {
        return toAjax(dayLogGroupService.updateDayLogGroup(dayLogGroup));
    }

    /**
     * 删除日志查询-快速分组列
     */
    //@PreAuthorize("@ss.hasPermi('system:group:remove')")
    @Log(title = "日志查询-快速分组列", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dayLogGroupService.deleteDayLogGroupByIds(ids));
    }
}
