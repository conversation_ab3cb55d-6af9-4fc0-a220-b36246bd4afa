package org.ruoyi.core.kaoqin.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.kaoqin.domain.KqFile;
import org.ruoyi.core.kaoqin.domain.RewardPunishment;
import org.ruoyi.core.kaoqin.domain.VoidHandle;
import org.ruoyi.core.kaoqin.domain.vo.RewardPunishmentVo;
import org.ruoyi.core.kaoqin.mapper.RewardPunishmentMapper;
import org.ruoyi.core.kaoqin.service.INotifyService;
import org.ruoyi.core.kaoqin.service.IRewardPunishmentService;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.service.impl.PersonnelArchivesServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;
import static com.ruoyi.system.service.impl.SysDeptServiceImpl.buildDeptChain;
import static org.ruoyi.core.kaoqin.service.impl.MonthLogMainServiceImpl.findSubordinates;


/**
 * 奖惩Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Service
public class RewardPunishmentServiceImpl implements IRewardPunishmentService
{
    @Autowired
    private RewardPunishmentMapper rewardPunishmentMapper;
    @Autowired
    private KqFileServiceImpl kqFileService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private INotifyService notifyService;
    @Autowired
    private PersonnelArchivesServiceImpl personnelArchivesService;
    @Autowired
    private ISysUserService sysUserService;
    /**
     * 查询奖惩
     *
     * @param id 奖惩主键
     * @return 奖惩
     */
    @Override
    public RewardPunishmentVo selectRewardPunishmentById(Long id)
    {
        List<SysDept> deptList = sysDeptService.selectDeptList(new SysDept());
        RewardPunishmentVo rewardPunishmentVo = rewardPunishmentMapper.selectRewardPunishmentById(id);
        rewardPunishmentVo.setDeptChain(buildDeptChain(rewardPunishmentVo.getDeptId(),deptList));
        return rewardPunishmentVo;
    }

    /**
     * 查询奖惩列表
     *
     * @param rewardPunishment 奖惩
     * @return 奖惩
     */
    @Override
    public List<RewardPunishmentVo> selectRewardPunishmentList(RewardPunishmentVo rewardPunishment)
    {

        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRange(loginUser);
        rewardPunishment.setDeptIds(dataRange.get("deptIds"));
        rewardPunishment.setUnitIds(dataRange.get("unitIds"));
        rewardPunishment.setCreateBy(loginUser.getUser().getUserName());

        PageUtil.startPage();
        List<RewardPunishmentVo> rewardPunishmentVos = rewardPunishmentMapper.selectRewardPunishmentList(rewardPunishment);
        List<SysDept> deptList = sysDeptService.selectDeptList(new SysDept());
        rewardPunishmentVos.forEach(item -> {
            item.setDeptChain(buildDeptChain(item.getDeptId(),deptList));
        });
        return rewardPunishmentVos;
    }

    /**
     * 新增奖惩
     *
     * @param rewardPunishment 奖惩
     * @return 结果
     */
    @Override
    @Transactional
    public RewardPunishmentVo insertRewardPunishment(RewardPunishmentVo rewardPunishment)
    {
        if(rewardPunishment.getId() == null){
            rewardPunishment.setCreateTime(DateUtils.getNowDate());
            rewardPunishment.setCreateBy(getUsername());
            String rewardPunishmentCode = getRewardPunishmentCode(rewardPunishment);
            rewardPunishment.setRewardPunishmentCode(rewardPunishmentCode);
            rewardPunishmentMapper.insertRewardPunishment(rewardPunishment);

            if (rewardPunishment.getFileIds() != null){
                KqFile kqFile = new KqFile();
                kqFile.setIds(rewardPunishment.getFileIds());
                kqFile.setFileType("1");
                kqFile.setCorrelationId(rewardPunishment.getId());
                kqFileService.correlationFile(kqFile);
            }
        } else {
            if (rewardPunishment.getStatus() != null && "4".equals(rewardPunishment.getStatus())){
                RewardPunishment oldData = selectRewardPunishmentById(rewardPunishment.getId());
                oldData.setIsDelete("1");
                rewardPunishmentMapper.updateRewardPunishment(oldData);
                //初始化数据
                rewardPunishment.setId(null);
                rewardPunishment.setProcessId("");
                rewardPunishment.setStatus("1");
                rewardPunishment.setAuditCompletionTime(null);
                insertRewardPunishment(rewardPunishment);
            }
            rewardPunishment.setUpdateTime(DateUtils.getNowDate());
            rewardPunishment.setUpdateBy(getUsername());
            String rewardPunishmentCode = getRewardPunishmentCode(rewardPunishment);
            rewardPunishment.setRewardPunishmentCode(rewardPunishmentCode);

            KqFile file = new KqFile();
            file.setCorrelationId(rewardPunishment.getId());
            file.setFileType("1");
            kqFileService.deleteByCorrelationId(file);

            if (rewardPunishment.getFileIds() != null){
                KqFile kqFile = new KqFile();
                kqFile.setIds(rewardPunishment.getFileIds());
                kqFile.setFileType("1");
                kqFile.setCorrelationId(rewardPunishment.getId());
                kqFileService.correlationFile(kqFile);
            }
            rewardPunishmentMapper.updateRewardPunishment(rewardPunishment);
        }
        return rewardPunishment;
    }

    /**
     * 修改奖惩
     *
     * @param rewardPunishment 奖惩
     * @return 结果
     */
    @Override
    public int updateRewardPunishment(RewardPunishment rewardPunishment)
    {
        rewardPunishment.setUpdateTime(DateUtils.getNowDate());
        return rewardPunishmentMapper.updateRewardPunishment(rewardPunishment);
    }

    /**
     * 批量删除奖惩
     *
     * @param ids 需要删除的奖惩主键
     * @return 结果
     */
    @Override
    public int deleteRewardPunishmentByIds(Long[] ids)
    {
        return rewardPunishmentMapper.deleteRewardPunishmentByIds(ids);
    }

    /**
     * 删除奖惩信息
     *
     * @param id 奖惩主键
     * @return 结果
     */
    @Override
    public int deleteRewardPunishmentById(Long id)
    {
        return rewardPunishmentMapper.deleteRewardPunishmentById(id);
    }

    public String getRewardPunishmentCode(RewardPunishment rewardPunishment){
        String code = "";
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        if("1".equals(rewardPunishment.getType())){
            int count = rewardPunishmentMapper.getCountByCreateTimeOfReward(DateUtils.getDate()) + 1;
            code = "JL" + createTimeNum + String.format("%03d", count);
        }
        if("2".equals(rewardPunishment.getType())){
            int count = rewardPunishmentMapper.getCountByCreateTimeOfPunishment(DateUtils.getDate()) + 1;
            code = "CF" + createTimeNum + String.format("%03d", count);
        }
        return code;
    }

    @Override
    public int voidRewardPunishment(RewardPunishment rewardPunishment){
        rewardPunishment.setUpdateTime(DateUtils.getNowDate());
        rewardPunishment.setVoidTime(DateUtils.getNowDate());

        VoidHandle voidHandle = new VoidHandle();
        voidHandle.setCorrelationId(rewardPunishment.getId());
        voidHandle.setListState("1");
        voidHandle.setType("3");
        voidHandle.setCreateById(getLoginUser().getUserId());
        voidHandle.setCreateBy(getLoginUser().getUsername());
        notifyService.insertVoidHandle(voidHandle);

        return rewardPunishmentMapper.voidRewardPunishment(rewardPunishment);
    }

    @Override
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.KQ, file);
            KqFile kqFile = new KqFile();
            kqFile.setFileUrl(url);
            kqFile.setFileName(name);
            kqFile.setFileState("0");
            kqFile.setFileType("1");
            kqFile.setCreateTime(DateUtils.getNowDate());
            kqFile.setCreateBy(getUsername());
            kqFileService.insertKqFile(kqFile);
            return AjaxResult.success(kqFile);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @Override
    public int processRewardPunishment(RewardPunishmentVo rewardPunishment){
        //防流程保存时草稿
        RewardPunishmentVo punishmentVo = new RewardPunishmentVo();
        punishmentVo.setProcessId("");
        punishmentVo.setIds(rewardPunishment.getIds());
        rewardPunishmentMapper.processRewardPunishment(punishmentVo);

        return rewardPunishmentMapper.processRewardPunishment(rewardPunishment);
    }

    @Override
    public int passRewardPunishment(RewardPunishmentVo rewardPunishment){
        rewardPunishment.setAuditCompletionTime(DateUtils.getNowDate());
        return rewardPunishmentMapper.passRewardPunishment(rewardPunishment);
    }

    @Override
    public int unpassRewardPunishment(RewardPunishmentVo rewardPunishment){
        rewardPunishment.setAuditCompletionTime(DateUtils.getNowDate());
        return rewardPunishmentMapper.unpassRewardPunishment(rewardPunishment);
    }

    /**
     * 查询奖惩
     *
     * @param rewardPunishment 奖惩主键
     * @return 奖惩
     */
    @Override
    public RewardPunishmentVo selectRewardPunishmentByProcessId(RewardPunishmentVo rewardPunishment)
    {
        List<RewardPunishmentVo> rewardPunishmentVos = rewardPunishmentMapper.selectRewardPunishmentList(rewardPunishment);
        return  rewardPunishmentVos.stream().findFirst().orElse(null);
    }

    @Override
    public RewardPunishmentVo selectRewardPunishmentByHandleId(Long id){
        return rewardPunishmentMapper.selectRewardPunishmentByHandleId(id);
    }

    @Override
    public List<SysUser> selectUserList(SysUser user)
    {
/**
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRangeFilterRoleKey(loginUser,"人事档案");
        user.setDeptIds(dataRange.get("deptIds"));
        user.setUnitIds(dataRange.get("unitIds"));
        //List<String> createByList = personnelArchivesService.getSubordinateList().stream().map(PersonnelArchivesVo::getSysName).collect(Collectors.toList());

        List<PersonnelArchivesVo> archivesVoList = personnelArchivesService.selectListOfMonthLog();
        Long userId = getLoginUser().getUser().getUserId();
        //查询登陆人 直属下属人员档案  以及下属的下属
        List<PersonnelArchivesVo> collect = archivesVoList.stream()
                .filter(vo -> vo != null && vo.getDirectSuperior() != null && vo.getDirectSuperior().equals(userId))
                .flatMap(vo -> Stream.concat(Stream.of(vo), findSubordinates(archivesVoList, vo.getId()).stream()))  //获取下属的下属
                .collect(Collectors.toList());
        //根据人员档案list,获取人员的登陆名 用来查询月报
        List<String> createByList = collect.stream().map(PersonnelArchivesVo::getSysName).collect(Collectors.toList());
        //至少权限有自己数据
        createByList.add(loginUser.getUser().getUserName());
//        if (user.getUserNames()!= null &&!user.getUserNames().isEmpty()){
//            createByList = user.getUserNames();
//        }
*/
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRange(loginUser);
        user.setDeptIds(dataRange.get("deptIds"));
        user.setUnitIds(dataRange.get("unitIds"));
        user.setCreateBy(loginUser.getUser().getUserName());
        List<String> createByList = new ArrayList<>();
        createByList.add(loginUser.getUser().getUserName());
        user.setUserNames(createByList);
        PageUtil.startPage();
        List<SysUser> sysUsers = sysUserService.selectUserListOfDayLog(user);

        List<SysDept> deptList = sysDeptService.selectDeptList(new SysDept());
        sysUsers.forEach(item -> {
            List<SysUserPost> userPostList = item.getUserPostList();
            Long deptId = userPostList.stream()
                    .filter(post -> "0".equals(post.getHomePost()))  // 筛选 homepost=0 的对象
                    .map(SysUserPost::getDeptId)              // 提取 deptId
                    .findFirst()                              // 获取第一个匹配项
                    .orElse(null);// 若未找到，返回 null（可替换为默认值或抛出异常）
            item.setDeptChain(buildDeptChain(deptId,deptList));
        });

        return sysUsers;
    }
}
