package org.ruoyi.core.kaoqin.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysUser;
import org.ruoyi.core.kaoqin.domain.DayLog;
import org.ruoyi.core.kaoqin.domain.vo.DayLogExport;
import org.ruoyi.core.kaoqin.domain.vo.DayLogListVo;
import org.ruoyi.core.kaoqin.service.IDayLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 日志填报Controller
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@RestController
@RequestMapping("/day/log")
public class DayLogController extends BaseController
{
    @Autowired
    private IDayLogService dayLogService;

    /**
     * 查询日志填报列表
     * @ignore
     */
    //@PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(DayLog dayLog)
    {
        startPage();
        List<DayLog> list = dayLogService.selectDayLogList(dayLog);
        return getDataTable(list);
    }

    /**
     * 导出日志填报列表
     */
    //@PreAuthorize("@ss.hasPermi('system:log:export')")
    @Log(title = "日志填报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DayLog dayLog)
    {
        List<DayLog> list = dayLogService.selectDayLogList(dayLog);
        ExcelUtil<DayLog> util = new ExcelUtil<DayLog>(DayLog.class);
        util.exportExcel(response, list, "日志填报数据");
    }

    /**
     * 获取日志填报详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(dayLogService.selectDayLogById(id));
    }

    /**
     * 新增日志填报
     */
    //@PreAuthorize("@ss.hasPermi('system:log:add')")
    @Log(title = "日志填报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DayLog dayLog)
    {
        return toAjax(dayLogService.insertDayLog(dayLog));
    }

    /**
     * 修改日志填报
     */
    //@PreAuthorize("@ss.hasPermi('system:log:edit')")
    @Log(title = "日志填报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DayLog dayLog)
    {
        return toAjax(dayLogService.updateDayLog(dayLog));
    }

    /**
     * 删除日志填报
     */
    //@PreAuthorize("@ss.hasPermi('system:log:remove')")
    @Log(title = "日志填报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dayLogService.deleteDayLogByIds(ids));
    }

    /**
     * 查询日志填报列表
     */
    //@PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/getDayLoglist")
    public AjaxResult getDayLogList(DayLogListVo dayLog)
    {
        return AjaxResult.success(dayLogService.getDayLogList(dayLog)) ;

    }

    /**
     * 查询日志填报列表-查看其他人日志列表
     */
    //@PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/getDayLogListOther")
    public TableDataInfo getDayLogListOther(DayLogListVo dayLog)
    {
        List<DayLogListVo> dayLogListOther = dayLogService.getDayLogListOther(dayLog);
        return getDataTable(dayLogListOther);
    }

    /**
     * 导出日志填报列表
     */
    //@PreAuthorize("@ss.hasPermi('system:log:export')")
    @Log(title = "日志填报导出", businessType = BusinessType.EXPORT)
    @PostMapping("/getDayLogListOther/export")
    public void getDayLogListOtherExport(HttpServletResponse response, DayLogListVo dayLog)
    {
        List<DayLogExport> dayLogListOther = dayLogService.getDayLogListOtherExport(dayLog);
        ExcelUtil<DayLogExport> util = new ExcelUtil<DayLogExport>(DayLogExport.class);
        util.exportExcel(response, dayLogListOther, "日志填报数据");
    }
    /**
     * 保存日志填报
     */
    //@PreAuthorize("@ss.hasPermi('system:log:add')")
    @Log(title = "保存日志填报", businessType = BusinessType.INSERT)
    @PostMapping("/saveDayLogList")
    public AjaxResult saveDayLogList(@RequestBody List<DayLog> dayLogList)
    {
        return toAjax(dayLogService.saveDayLogList(dayLogList));
    }

    /**
     * 保存并提交日志填报
     */
    //@PreAuthorize("@ss.hasPermi('system:log:add')")
    @Log(title = "保存并提交日志填报", businessType = BusinessType.INSERT)
    @PostMapping("/commintDayLogList")
    public AjaxResult commintDayLogList(@RequestBody List<DayLog> dayLogList)
    {
        return toAjax(dayLogService.commintDayLogList(dayLogList));
    }

    /**
     * 查询日志填报列表
     */
    //@PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/getDayLogListOfLeader")
    public TableDataInfo getDayLogListOfLeader(DayLogListVo dayLog)
    {
        List<DayLogListVo> list = dayLogService.getDayLogListOfLeader(dayLog);
        return getDataTable(list);
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/queryUserInfoList")
    public TableDataInfo queryUserInfoList(SysUser user)
    {
        //startPage();
        List<SysUser> list = dayLogService.selectUserList(user);
        return getDataTable(list);
    }
}
