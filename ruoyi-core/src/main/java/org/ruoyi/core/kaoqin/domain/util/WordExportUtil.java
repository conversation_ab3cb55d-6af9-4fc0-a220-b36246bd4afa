package org.ruoyi.core.kaoqin.domain.util;

import freemarker.template.Configuration;
import freemarker.template.Template;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.ByteArrayOutputStream;
import java.util.Map;


@Component
public class WordExportUtil {
    @Autowired
    private Configuration freemarkerConfig;

    public byte[] exportToDocx(Map<String, Object> data) throws Exception {
        // 渲染HTML模板
        Template template = freemarkerConfig.getTemplate("RewardsPunishment.html");
        String htmlContent = FreeMarkerTemplateUtils.processTemplateIntoString(template, data);



        // 创建Word文档
        WordprocessingMLPackage wordPackage = WordprocessingMLPackage.createPackage();
        MainDocumentPart documentPart = wordPackage.getMainDocumentPart();

        // 导入XHTML内容
        documentPart.getContent().addAll(
                new org.docx4j.convert.in.xhtml.XHTMLImporterImpl(wordPackage)
                        .convert(htmlContent, null)
        );

        // 转换为字节数组
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        wordPackage.save(out);
        return out.toByteArray();
    }

}
