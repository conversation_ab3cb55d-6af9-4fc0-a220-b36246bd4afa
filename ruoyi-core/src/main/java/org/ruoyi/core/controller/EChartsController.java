package org.ruoyi.core.controller;

import com.ruoyi.common.core.controller.BaseController;
import org.ruoyi.core.domain.*;
import org.ruoyi.core.service.EchartsService;
import org.ruoyi.core.service.impl.SysSelectDataRefServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName EChartsController.java
 * @createTime 2022年04月24日 17:39:00
 */
@RestController
@RequestMapping("/system/eChart")
public class EChartsController extends BaseController {

    @Autowired
    private EchartsService echartsService;

    @Autowired
    private SysSelectDataRefServiceImpl sysSelectDataRefService;

    /**
     * vintage统计数据
     *
     * @param dVintageMonth Vintage数据实体
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/vintage")
    public Map<String, Object> vintageChartData(DVintageMonth dVintageMonth){
        DData dData = new DData();
        dData.setPlatformNo(dVintageMonth.getPlatformNo());
        dData.setCustNo(dVintageMonth.getCustNo());
        dData.setPartnerNo(dVintageMonth.getPartnerNo());
        dData.setFundNo(dVintageMonth.getFundNo());
        dData.setProductNo(dVintageMonth.getProductNo());
        dData.setMoreSearch(dVintageMonth.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dVintageMonth.setProductNo(productCode);
        }
        return echartsService.getVintageEChartData(dVintageMonth);
    }


    /**
     * 余额分布图表数据
     *
     * @param dBalanceDistributionMonthVO 月vo d平衡分布
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/balanceChart")
    public Map<String, Object> balanceEChartData(DBalanceDistributionMonth dBalanceDistributionMonthVO){

        DData dData = new DData();
        dData.setPlatformNo(dBalanceDistributionMonthVO.getPlatformNo());
        dData.setCustNo(dBalanceDistributionMonthVO.getCustNo());
        dData.setPartnerNo(dBalanceDistributionMonthVO.getPartnerNo());
        dData.setFundNo(dBalanceDistributionMonthVO.getFundNo());
        dData.setProductNo(dBalanceDistributionMonthVO.getProductNo());
        dBalanceDistributionMonthVO.setMoreSearch(dBalanceDistributionMonthVO.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dBalanceDistributionMonthVO.setProductNo(productCode);
        }
        Map<String, Object> cakeChartData = echartsService.BalanceCharts(dBalanceDistributionMonthVO);
        return cakeChartData;
    }



    /**
     * 运行数据
     *
     * @param dDataCope 系统运营统计数据
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @GetMapping("/dDataEChartData")
    public  Map<String, Object> dDataEChartData(DData dDataCope){
        return echartsService.dDataEChartData(dDataCope);
    }



    /**
     * 运行情况统计堆叠数据
     *
     * @param dDataCope 系统运营统计数据
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @GetMapping("/dDataEChartDataStack")
    public  Map<String, Object> dDataEChartDataStack(DData dDataCope){
//
//        String productCode = sysSelectDataRefService.getProductCode(dDataCope);
//        if (productCode != null) {
//            dDataCope.setProductNo(productCode);
//        }
        return echartsService.dDataEChartDataStack(dDataCope);
    }

    /**
     * 利润计算echart数据
     *
     * @param dProfitData d利润数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/profixEchart")
    public  Map<String, Object> profitCalculateEChartData(DProfitData dProfitData){
        DData dData = new DData();
        dData.setPlatformNo(dProfitData.getPlatformNo());
        dData.setCustNo(dProfitData.getCustNo());
        dData.setPartnerNo(dProfitData.getPartnerNo());
        dData.setFundNo(dProfitData.getFundNo());
        dData.setProductNo(dProfitData.getProductNo());
        dData.setMoreSearch(dProfitData.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dProfitData.setProductNo(productCode);
        }
        return echartsService.profitCalculateEChart(dProfitData);
    }

    /**
     * 合作方分布统计echart数据
     *
     * @param dDataCope 维数据处理
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/partnerEchartData")
    public  Map<String, Object> partnerEchartData(DData dDataCope){
        String productCode = sysSelectDataRefService.getProductCode(dDataCope);
        if (productCode != null) {
            dDataCope.setProductNo(productCode);
        }
        return echartsService.partnerEchartData(dDataCope);
    }


    /**
     * 资产方分布统计echart数据
     *
     * @param dDataCope 维数据处理
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/fundEchartData")
    public  Map<String, Object> fundEchartData(DData dDataCope){
        String productCode = sysSelectDataRefService.getProductCode(dDataCope);
        if (productCode != null) {
            dDataCope.setProductNo(productCode);
        }
        return echartsService.fundEchartData(dDataCope);
    }


    /**
     * 坏账率
     *
     * @param dProfitData d利润数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/badDebtRate")
    public  Map<String, Object> badDebtRate(DProfitData dProfitData){
        DData dData = new DData();
        dData.setPlatformNo(dProfitData.getPlatformNo());
        dData.setCustNo(dProfitData.getCustNo());
        dData.setPartnerNo(dProfitData.getPartnerNo());
        dData.setFundNo(dProfitData.getFundNo());
        dData.setProductNo(dProfitData.getProductNo());
        dData.setMoreSearch(dProfitData.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dProfitData.setProductNo(productCode);
        }
        return echartsService.badDebtRateData(dProfitData);
    }


    /**
     * 坏账数据日期
     *
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/badDebtDataDate")
    public  Map<String, Object> badDebtDataDate(){
        HashMap<String, Object> returnMap = new HashMap<>();
        String nearDataDate = echartsService.getNearDataDate();
        returnMap.put("date",nearDataDate);
        return returnMap;
    }


    /**
     * 担保公司分布统计echart数据
     *
     * @param dDataCope 维数据处理
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/custEchartData")
    public  Map<String, Object> custEchartData(DData dDataCope){
        String productCode = sysSelectDataRefService.getProductCode(dDataCope);
        if (productCode != null) {
            dDataCope.setProductNo(productCode);
        }
        return echartsService.custEchartData(dDataCope);
    }

    /**
     * 利润测算排名
     *
     * @param profitRank 维数据处理
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/profitRank")
    public  Map<String, Object> profitRank(ProfitRank profitRank){
        return echartsService.profitRank(profitRank);
    }



}
