package org.ruoyi.core.yybbsc.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.yybbsc.domain.vo.ConfigurationByParameterCodeVo;
import org.ruoyi.core.yybbsc.domain.vo.ConfigurationVo;
import org.ruoyi.core.yybbsc.service.IStsIncomeForecastConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收入预测报配置Controller
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@RestController
@RequestMapping("/yybbsc/configuration")
public class StsIncomeForecastConfigurationController extends BaseController {
    @Autowired
    private IStsIncomeForecastConfigurationService stsIncomeForecastConfigurationService;

    /**
     * 查询配置的列表页
     */
    @GetMapping("/list")
    public List<ConfigurationVo> list() {
        return stsIncomeForecastConfigurationService.selectStsIncomeForecastConfigurationList();
    }

    /**
     * 用参数码 查询具体的某一个参数列表
     */
    @GetMapping("/configurationListByParameterCode")
    public List<ConfigurationByParameterCodeVo> configurationListByParameterCode(String parameterCode) {
        return stsIncomeForecastConfigurationService.selectStsIncomeForecastConfigurationListByParameterCode(parameterCode);
    }

    /**
     * 收入预测报配置 - 校验日期是否符合逻辑要求
     */
    @PostMapping("/checkConfiguration")
    public Boolean checkConfiguration(@RequestBody List<ConfigurationByParameterCodeVo> configurations) {
        return stsIncomeForecastConfigurationService.checkConfiguration(configurations);
    }

    /**
     * 收入预测报配置 - 修改数据
     */
    @PostMapping("/changeConfiguration")
    public AjaxResult changeConfiguration(@RequestBody List<ConfigurationByParameterCodeVo> configurations) {
        LoginUser loginUser = getLoginUser();
        return toAjax(stsIncomeForecastConfigurationService.changeConfiguration(configurations, loginUser));
    }
}
