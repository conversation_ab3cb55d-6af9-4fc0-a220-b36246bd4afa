package org.ruoyi.core.yybbsc.service;

import org.ruoyi.core.yybbsc.domain.vo.StsIncomeForecastInfoVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 收入预测报Service接口
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
public interface IStsIncomeForecastService {
    /**
     * 导入对账单
     *
     * @param file 页面上传的对账单文件
     * @return int 是否是成功的（主要是给前端用，前端必须是>0的数才能正确显示）
     */
    int stsIncomeForecastByImportStaOfAcc(MultipartFile file) throws Exception;

    /**
     * 导入还款表
     *
     * @param file 页面上传的还款表文件
     * @return int 是否是成功的（主要是给前端用，前端必须是>0的数才能正确显示）
     */
    int stsIncomeForecastByImportRepay(MultipartFile file) throws Exception;

    /**
     * 导入代偿明细表
     *
     * @param file 页面上传的代偿明细表文件
     * @return int 是否是成功的（主要是给前端用，前端必须是>0的数才能正确显示）
     */
    int stsIncomeForecastByImportCompensatory(MultipartFile file) throws Exception;

    /**
     * 根据放款月份查页面所需要数据
     *
     * @param loanMonth 放款月份
     * @return StsIncomeForecastInfoVo 页面数据对象
     */
    StsIncomeForecastInfoVo queryListByLoanMonth(String loanMonth);

    /**
     * 进入页面首先查库里已有的数据的放款月份供用户进行选择查询
     *
     * @return List<String> 日期的集合
     */
    List<String> queryLoanMonthListFirst();

    /**
     * 导出所有放款月份的数据
     *
     * @return List<StsIncomeForecastInfoVo> 页面结果集合
     */
//    List<StsIncomeForecastInfoVo> queryAllExport();
}
