package org.ruoyi.core.yybbsc.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 每日运营统计对象 sts_operate_day
 *
 * <AUTHOR>
 * @date 2022-12-21
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class StsOperateDayDto
{
    private static final long serialVersionUID = 1L;

    /** 业务日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reconDate;

    /** 放款金额 */
    @Excel(name = "放款金额")
    private BigDecimal loanAmt;

    /** 实还本金 */
    @Excel(name = "实还本金")
    private BigDecimal actPrintAmt;

    /** 用户实还息费 */
    @Excel(name = "用户实还息费")
    private BigDecimal actIntAmt;

    /** 借条分润 */
    @Excel(name = "借条分润")
    private BigDecimal jtFrAmt;

    /** 中保分润 */
    @Excel(name = "中保分润")
    private BigDecimal zbFrAmt;

    /** 客户贷款余额 */
    @Excel(name = "客户贷款余额")
    private BigDecimal userBalanceAmt;
}
