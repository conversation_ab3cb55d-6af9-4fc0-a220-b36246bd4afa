package org.ruoyi.core.qrReport.service;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.qrReport.domain.QrCodeAuditReport;

import java.util.List;
import java.util.Map;

/**
 * 审计报告二维码生成Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-06
 */
public interface IQrCodeAuditReportService 
{
    /**
     * 查询审计报告二维码生成
     * 
     * @param uniqueFlag 审计报告二维码生成唯一标识
     * @return 审计报告二维码生成
     */
    public QrCodeAuditReport selectQrCodeAuditReportById(String uniqueFlag);

    /**
     * 查询审计报告二维码生成列表
     * 
     * @param qrCodeAuditReport 审计报告二维码生成
     * @return 审计报告二维码生成集合
     */
    public List<QrCodeAuditReport> selectQrCodeAuditReportList(QrCodeAuditReport qrCodeAuditReport);

    /**
     * 新增审计报告二维码生成
     * 
     * @param qrCodeAuditReport 审计报告二维码生成
     * @return 结果
     */
    public AjaxResult insertQrCodeAuditReport(QrCodeAuditReport qrCodeAuditReport);

    /**
     * 修改审计报告二维码生成
     * 
     * @param qrCodeAuditReport 审计报告二维码生成
     * @return 结果
     */
    public int updateQrCodeAuditReport(QrCodeAuditReport qrCodeAuditReport);

    /**
     * 批量删除审计报告二维码生成
     * 
     * @param ids 需要删除的审计报告二维码生成主键集合
     * @return 结果
     */
    public int deleteQrCodeAuditReportByIds(Long[] ids);

    /**
     * 删除审计报告二维码生成信息
     * 
     * @param id 审计报告二维码生成主键
     * @return 结果
     */
    public int deleteQrCodeAuditReportById(Long id);

    /**
     * 批量导入
     * @param qrCodeAuditReport
     * @return
     */
    String importFileData(List<QrCodeAuditReport> qrCodeAuditReport);
}
