package org.ruoyi.core.loanMonitor.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 在贷余额监控预警对象 loan_balance_warning
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
public class LoanBalanceWarning extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键ID */
    private Integer id;

    /** 项目唯一标识码 */
    private Integer projectCode;

    /** 产品线编码 */
    private String productCode;

    /** 在贷余额 */
    private BigDecimal loanBalance;

    /** 保证金比例 */
    private BigDecimal marginRate;

    /** 保证金总金额：单位元 */
    private BigDecimal marginAmount;

    /** 保证金占用比例下限 */
    private BigDecimal ratioMin;

    /** 保证金占用比例上限 */
    private BigDecimal ratioMax;

    /** 发送通知时的阈值 */
    private Integer currentThreshold;

    /** 预警开始标志：1-开启 0-关闭 */
    private String warningFlag;

    /** 乐观锁版本号-默认为0 */
    private Integer version;

    public Integer getCurrentThreshold() {
        return currentThreshold;
    }

    public void setCurrentThreshold(Integer currentThreshold) {
        this.currentThreshold = currentThreshold;
    }

    public void setId(Integer id)
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }

    public Integer getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(Integer projectCode) {
        this.projectCode = projectCode;
    }

    public void setProductCode(String productCode)
    {
        this.productCode = productCode;
    }

    public String getProductCode() 
    {
        return productCode;
    }
    public void setLoanBalance(BigDecimal loanBalance) 
    {
        this.loanBalance = loanBalance;
    }

    public BigDecimal getLoanBalance() 
    {
        return loanBalance;
    }
    public void setMarginRate(BigDecimal marginRate) 
    {
        this.marginRate = marginRate;
    }

    public BigDecimal getMarginRate() 
    {
        return marginRate;
    }
    public void setMarginAmount(BigDecimal marginAmount) 
    {
        this.marginAmount = marginAmount;
    }

    public BigDecimal getMarginAmount() 
    {
        return marginAmount;
    }
    public void setRatioMin(BigDecimal ratioMin) 
    {
        this.ratioMin = ratioMin;
    }

    public BigDecimal getRatioMin() 
    {
        return ratioMin;
    }
    public void setRatioMax(BigDecimal ratioMax) 
    {
        this.ratioMax = ratioMax;
    }

    public BigDecimal getRatioMax() 
    {
        return ratioMax;
    }
    public void setWarningFlag(String warningFlag) 
    {
        this.warningFlag = warningFlag;
    }

    public String getWarningFlag() 
    {
        return warningFlag;
    }
    public void setVersion(Integer version) 
    {
        this.version = version;
    }

    public Integer getVersion() 
    {
        return version;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectCode", getProjectCode())
            .append("productCode", getProductCode())
            .append("loanBalance", getLoanBalance())
            .append("marginRate", getMarginRate())
            .append("marginAmount", getMarginAmount())
            .append("ratioMin", getRatioMin())
            .append("ratioMax", getRatioMax())
            .append("warningFlag", getWarningFlag())
            .append("remark", getRemark())
            .append("updateTime", getUpdateTime())
            .append("version", getVersion())
            .toString();
    }
}
