package org.ruoyi.core.loanMonitor.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.loanMonitor.domain.LoanBalanceWarning;
import org.ruoyi.core.loanMonitor.domain.MarginUpdateLog;
import org.ruoyi.core.loanMonitor.domain.vo.MarginMonitoringVo;
import org.ruoyi.core.loanMonitor.service.ILoanBalanceWarningService;
import org.ruoyi.core.loanMonitor.service.IMarginUpdateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 实时在贷数据监控
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
@RestController
@RequestMapping("/loan/monitor")
public class LoanMonitorController extends BaseController
{
    @Autowired
    private ILoanBalanceWarningService loanBalanceWarningService;

    @Autowired
    private IMarginUpdateLogService marginUpdateLogService;

    /**
     * 查询大列表
     */
    @PostMapping("/loanList")
    public TableDataInfo loanList(@RequestBody MarginMonitoringVo marginMonitoringVo)
    {
        List<MarginMonitoringVo> list = loanBalanceWarningService.selectMarginMonitoringList(marginMonitoringVo);
        return getDataTable(list);
    }

    /**
     * 新增保证金更新记录
     */
    @Log(title = "保证金更新记录", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('balanceMonitoring:add')")
    @PostMapping("/marginAdd")
    public AjaxResult add(@RequestBody MarginUpdateLog marginUpdateLog)
    {
        return toAjax(marginUpdateLogService.insertMarginUpdateLog(marginUpdateLog));
    }

    /**
     * 查询追加保证金更新记录列表
     */
    @GetMapping("/marginList")
    public TableDataInfo marginList(MarginUpdateLog marginUpdateLog)
    {
        startPage();
        List<MarginUpdateLog> list = marginUpdateLogService.selectMarginUpdateLogList(marginUpdateLog);
        return getDataTable(list);
    }

    /**
     * 查询追加保证金更新记录详情
     */
    //@GetMapping("/marginInfo/{projectCode}")
    //public AjaxResult marginInfo(@PathVariable("projectCode") Integer projectCode)
    //{
    //    return AjaxResult.success(marginUpdateLogService.selectMarginInfoByProjectCode(projectCode));
    //}

    /**
     * 新增保证金比例预警阈值
     */
    //@Log(title = "在贷余额监控预警", businessType = BusinessType.INSERT)
    //@PostMapping
    //public AjaxResult add(@RequestBody LoanBalanceWarning loanBalanceWarning)
    //{
    //    return toAjax(loanBalanceWarningService.insertLoanBalanceWarning(loanBalanceWarning));
    //}

    /**
     * 获取保证金比例预警阈值
     */
    @GetMapping("/getRate/{projectCode}")
    public AjaxResult getRateInfo(@PathVariable("projectCode") Integer projectCode)
    {
        return AjaxResult.success(loanBalanceWarningService.selectLoanBalanceWarningByProjectCode(projectCode));
    }

    /**
     * 修改在贷余额监控预警
     */
    @PreAuthorize("@ss.hasPermi('balanceMonitoring:update')")
    @Log(title = "在贷余额监控预警", businessType = BusinessType.UPDATE)
    @PutMapping("/updateWarning")
    public AjaxResult edit(@RequestBody LoanBalanceWarning loanBalanceWarning)
    {
        return toAjax(loanBalanceWarningService.updateLoanBalanceWarning(loanBalanceWarning));
    }

    /**
     * 根据项目唯一编码查询当前账户总金额
     * @param projectCode
     * @return
     */
    @GetMapping("/getTotalMargin")
    public AjaxResult getTotalMargin(@RequestParam("projectCode") Integer projectCode) {
        BigDecimal totalMargin = loanBalanceWarningService.selectTotalMarginByProjectCode(projectCode);
        return AjaxResult.success(totalMargin);
    }
}
