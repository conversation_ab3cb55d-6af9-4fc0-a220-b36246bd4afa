package org.ruoyi.core.oasystem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 项目信息-收付款信息表（对照数据库实体）
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/12/23 15:27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaProjectDeployReceiptAndPaymentInfo {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目表id */
    private Long oaProjectDeployId;

    /** 收付款类型:1-常规业务收支款项（含助贷平台和资金方） 2-技术服务方支出款项 */                    //分组用这个字段
    private String receiptAndPaymentType;

    /** 事项，详情见OaProjectDeployReceiptAndPaymentInfoEnum枚举 */
    private String itemName;

    /** 备注 */
    private String remark;

    /** 序号，item_name内排序用 */
    private Integer serialNum;

    /** 录入方式:1-选择 2-填写 */
    private String inputType;

    /** 收付款人表id */
    private Long oaTraderId;

    /** 类型 */
    private String traderType;

    /** 账户名称 */
    private String accountName;

    /** 账号 */
    private String accountNumber;

    /** 开户行 */
    private String bankOfDeposit;

    /** 简称 */
    private String abbreviation;

    /** 账套 */
    private String accountIdOfName;

    /** 状态，0正常 1停用（失效） */
    private String status;

    /** 创建者 */
    private String createBy;

    /** 创建者id */
    private Long createId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新者id */
    private Long updateId;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
