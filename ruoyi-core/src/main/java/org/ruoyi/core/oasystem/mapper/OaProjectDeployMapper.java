package org.ruoyi.core.oasystem.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.domain.OaProjectDeployCwProjectFeeInfo;
import org.ruoyi.core.oasystem.domain.OaProjectDeployReceiptAndPaymentInfo;
import org.ruoyi.core.oasystem.domain.bo.OaProjectDeployBo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
public interface OaProjectDeployMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaProjectDeploy selectOaProjectDeployById(Long id);


    public OaProjectDeploy selectDataById(@Param("id") Long id);
    /**
     * 查询【请填写功能名称】列表
     *
     * @param oaProjectDeploy 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<OaProjectDeploy> selectOaProjectDeployList(OaProjectDeploy oaProjectDeploy);
    public List<OaProjectDeploy> newSelectOaProjectDeployList(OaProjectDeploy oaProjectDeploy);
    /**
     * 新增【请填写功能名称】
     *
     * @param oaProjectDeploy 【请填写功能名称】
     * @return 结果
     */
    public int insertOaProjectDeploy(OaProjectDeploy oaProjectDeploy);

    /**
     * 修改【请填写功能名称】
     *
     * @param oaProjectDeploy 【请填写功能名称】
     * @return 结果
     */
    public int updateOaProjectDeploy(OaProjectDeploy oaProjectDeploy);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaProjectDeployById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaProjectDeployByIds(Long[] ids);

    /**
     * <AUTHOR>
     * @Description 查询项目与收款人信息的映射关系
     * @Date 2023/8/31 15:48
     * @Param [projectId]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     **/
    List<Map<String, Object>> selectProjectMapCollByProjectId(Integer projectId);

    List<OaProjectDeployBo> selectByData();

    //过滤掉已经有
    List<OaProjectDeploy> getFilterOaProjectDeployList(@Param("oaProjectDeployIdList") List<Long> oaProjectDeployIdList);

    //获取所有的
    List<OaProjectDeploy> getAllOaProjectDeployList();

    //根据各种过滤后的参数条件，查询列表
    List<OaProjectDeploy> selectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdList(@Param("oaProjectDeploy") OaProjectDeploy oaProjectDeploy, @Param("companyIdList") List<Long> companyIdList, @Param("filterOaApplyId") List<Long> filterOaApplyId);
    //根据各种过滤后的参数条件，查询列表  new 2024-04-18 wangzeyu
    List<OaProjectDeploy> newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdList(OaProjectDeploy oaProjectDeploy);

    //查询总条数
    Long selectOaProjectDeployListTotal(@Param("oaProjectDeploy") OaProjectDeploy oaProjectDeploy, @Param("companyIdList") List<Long> companyIdList);

    //根据过滤条件查询总条数
    Long selectOaProjectDeployListTotalByOaProjectDeployAndCompanyIdListAndFilterOaApplyIdList(@Param("oaProjectDeploy") OaProjectDeploy oaProjectDeploy, @Param("companyIdList") List<Long> companyIdList, @Param("filterOaApplyId") List<Long> filterOaApplyId);

    List<OaProjectDeploy> selectProjectByCompanyId(@Param("id") Long id);

    Map<String, Object> queryCooperationUnit(@Param("companyId") Long companyId, @Param("companyType") String companyType);

    List<OaProjectDeploy> getDataByTypelist(@Param("typeData") String typeData, @Param("typeId") String typeId);

    //通过项目名称id集合，找项目名称相关信息
    List<OaProjectDeploy> selectOaProjectDeployListByOaProjectDeployIdList(@Param("oaProjectDeployIdList") List<Long> oaProjectDeployIdList);

    List<Map<String, Object>> getProjectDataById(@Param("projectIds") List<Long> projectIds);

    List<Map<String, Object>> getProjectTypeList(@Param("id") Long id);

    List<OaProjectDeploy> newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdListAndUser(@Param("oaProjectDeploy") OaProjectDeploy oaProjectDeploy,@Param("userId") Long userId);

    Long newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdListTotal(OaProjectDeploy oaProjectDeploy);

    Long newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdListAndUserTotal(@Param("oaProjectDeploy") OaProjectDeploy oaProjectDeploy,@Param("userId") Long userId);

    //通过oaProjectDeployId查询 项目信息-收付款信息
    List<OaProjectDeployReceiptAndPaymentInfo> selectReceiptAndPaymentInfoByOaProjectDeployId(Long id);

    //新增项目信息-收付款信息表
    int insertReceiptAndPaymentInfo(OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo);

    //修改项目信息-收付款信息表
    int updateReceiptAndPaymentInfo(OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo);

    //删除项目信息-收付款信息表
    int deleteReceiptAndPaymentInfoById(Long id);

    //删除项目信息-收付款信息表 （集合删除）
    int deleteReceiptAndPaymentInfoByIds(List<Long> ids);

    //通过oaProjectDeployId查询 项目信息-信息费信息
    List<OaProjectDeployCwProjectFeeInfo> selectCwProjectFeeInfoByOaProjectDeployId(Long id);

    //新增项目信息-收付款信息表
    int insertCwProjectFeeInfo(OaProjectDeployCwProjectFeeInfo  oaProjectDeployCwProjectFeeInfo);

    //修改项目信息-信息费信息表（原）
    int updateCwProjectFeeInfo(OaProjectDeployCwProjectFeeInfo  oaProjectDeployCwProjectFeeInfo);

    //删除项目信息-信息费信息表
    int deleteCwProjectFeeInfoById(Long id);

    //删除项目信息-信息费信息表 （集合删除）
    int deleteCwProjectFeeInfoByIds(List<Long> ids);

    //修改项目信息-信息费信息表（做修改流程的的时候用）
    int updateCwProjectFeeInfo1(OaProjectDeployCwProjectFeeInfo oaProjectDeployCwProjectFeeInfo);

    //通过项目名称集合（set集合），查询项目信息集合
    List<OaProjectDeploy> selectOaProjectDeployListByProjectNameSet(Set<String> projectNameSet);
}
