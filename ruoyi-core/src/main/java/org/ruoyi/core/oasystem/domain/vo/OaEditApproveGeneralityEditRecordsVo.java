package org.ruoyi.core.oasystem.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 记账凭证规则详情 - 实体类
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/01/02 14:13
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaEditApproveGeneralityEditRecordsVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    //主键
    private Long id;

    //代表的相关功能。1付款人配置，2收款人配置，3项目与流程关联，4项目名称配置，9收/付款人配置
    private String oaApplyType;

    //记账凭证规则主表id
    private Long oaApplyId;

    //记录表修改前id
    private Long oaApplyRecordsOldId;

    //记录表修改后id
    private Long oaApplyRecordsNewId;

    //编辑人员id
    private Long editUserId;

    //编辑时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date editTime;

    //修改说明
    private String editInfo;

    //审核人id
    private Long checkUserId;

    //审批时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    //审核状态，0待业务审核，1待财务审核
    private String checkStatus;

    //驳回标识，0通过，1驳回
    private String rejectFlag;

    //驳回原因，当驳回标识为1时，本字段不能为空
    private String checkRejectInfo;

    //是否知悉，0未知悉，1已知悉
    private String confirmFlag;




    //联表展示数据
    //记录表修改前数据
    private String oaApplyRecordsOldData;

    //记录表修改后数据
    private String oaApplyRecordsNewData;

    //编辑人员姓名
    private String editUserNickName;

    //审批人姓名
    private String checkUserNickName;




    //与原对象不同，多加了一个属性
    //提交身份人
    private String identity;


    //申请类型（页面展示新增修改或者删除） 1-修改 2-删除
    private String applyType;

    //视图类型 从哪个视图跳进来做业务的
    private String oaNotifyStep;

    //财务负责人（新增）
    private List<Long> salesmanList;

    //业务负责人（新增）
    private List<Long> financialStaffList;



    //为了满足所有编辑记录所加的字段
    //编辑人身份 0-财务负责人 1-业务负责人 2-财务管理员 3-业务管理员
    private String editIdentity;

    //审核人身份 0-财务负责人 1-业务负责人 2-财务管理员 3-业务管理员
    private String checkIdentity;

    //流程关联id
    private String processId;

    //删除标识;0-正常 1-删除
    private String delFlag;

    //操作的类型;0-新增,1-修改,2-删除
    private String editType;
}
