package org.ruoyi.core.oasystem.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】对象 oa_project_deploy
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaProjectDeploy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键（入库方便使用） */
    private Long id;

    //主键（业务逻辑使用）
    private Long oaApplyId;

    //公司编码
    private Long companyNo;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    //渠道方类型
    private String channelType;

    //渠道方名称
    private String channelName;

    //授信金额 万元
    private BigDecimal creditAmount;

    //审核状态 0（-），1修改项目审核中，2删除项目审核中
    private String checkStatus;

    //页面的 我担任责任人的项目  的按钮  0关闭  1开启
    private String selectType;

    /** 0通道业务1分润业务2法催业务 */
    @Excel(name = "0通道业务1分润业务2法催业务3保函业务")
    private String projectType;

    /** 是否启用Y启用N禁用 */
    @Excel(name = "是否启用Y启用N禁用")
    private String isEnable;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createBr;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updateBr;

    //是否与财务项目管理关联，0-否，1-是
    private String cwRelevanceStatus;
    //新增未审批标识。当新增未审批过的为9，新增审批过的为0
    private String addNotApprove;
    //2024-04-17新增参数 wangzeyu
//担保公司
    private String custNo;
    //资产方
    private String partnerNo;
    //资金方
    private String fundNo;

    //所属公司
    private String companyName;

    //用户id
    private Long userid;
    //用户类型
    private String userFlag;

    private String typeId;
    private String typeData;
    //公司 id
    private Long companyId;

    //是否有信息费标识;0-无 1-有
    private String cwProjectFeeFlag;

    //项目是否发起了项目信息修改，0或空 不是，1-新增审批状态，2-修改审批状态，3-删除审批状态
    private String deployProcessFlag;

    //项目是否发起了收付款信息修改，0或空 不是，1-是
    private String traderProcessFlag;

    //项目是否发起了信息费信息修改，0或空 不是，1-是
    private String feeProcessFlag;
    //更多查询条件
    private Map<String, List<Long>> moreSearch;

    private Long pageNum;

    private Long pageSize;

    //查询项目类型
    private List<Long> projectTypeIds;
    //查询产品类型
    private List<Long> businessTypeIds;
    //定义法催类型
    private String projectPortfolioCode;
}
