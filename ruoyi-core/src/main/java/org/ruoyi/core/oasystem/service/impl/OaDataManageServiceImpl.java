package org.ruoyi.core.oasystem.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.ruoyi.core.oasystem.domain.OaDataManage;
import org.ruoyi.core.oasystem.domain.ProjectTypeRelevance;
import org.ruoyi.core.oasystem.domain.vo.OaPostCompanyVo;
import org.ruoyi.core.oasystem.mapper.OaDataManageMapper;
import org.ruoyi.core.oasystem.service.IOaDataManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.ruoyi.core.oasystem.service.impl.OaProjectNameRuleServiceImpl.getDataNameWithParents;

/**
 * 数据集管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Service
public class OaDataManageServiceImpl implements IOaDataManageService
{
    @Autowired
    private OaDataManageMapper oaDataManageMapper;

    /**
     * 查询数据集管理
     *
     * @param id 数据集管理主键
     * @return 数据集管理
     */
    @Override
    public OaDataManage selectOaDataManageById(Long id)
    {
        return oaDataManageMapper.selectOaDataManageById(id);
    }

    /**
     * 查询数据集管理列表
     *
     * @param oaDataManage 数据集管理
     * @return 数据集管理
     */
    @Override
    public List<OaDataManage> selectOaDataManageList(OaDataManage oaDataManage)
    {
        List<OaDataManage> oaDataManageList = oaDataManageMapper.selectOaDataManageList(oaDataManage);
        if(oaDataManage.getDataCode() != null || oaDataManage.getDataName() != null || oaDataManage.getStatus() != null){
            return oaDataManageList;
        }
        return this.getTree(oaDataManageList);
    }

    /**
     * 组装树形列表
     * @param oaDataManageList
     * @return
     */
    public List<OaDataManage> getTree(List<OaDataManage> oaDataManageList){
        // 父级，用于存放最终结果
        List<OaDataManage> fp = new ArrayList<>();
        // 筛选出的子集
        List<OaDataManage> fpson = new ArrayList<>();
        // 先提取出顶级目录和子集
        for (int i = 0; i < oaDataManageList.size(); i++) {
            if (null == oaDataManageList.get(i).getParentId()) {
                fp.add(oaDataManageList.get(i));
            } else {
                fpson.add(oaDataManageList.get(i));
            }
        }
        // 从顶级目录开始，递归穿插数据
        for (int i = 0; i < fp.size(); i++) {
            getChildData(fp.get(i), fpson);
        }
        return fp;
    }

    /**
     * @param fp    父集
     * @param fpson 子集
     */
    private void getChildData(OaDataManage fp, List<OaDataManage> fpson) {
        List<OaDataManage> stessoLive = new ArrayList<>();
        for (int j = 0; j < fpson.size(); j++) {
            // 如果是其子类，则存储在子类的list中，循环完统一set
            if (fpson.get(j).getParentId().equals(fp.getId())) {
                stessoLive.add(fpson.get(j));
                getChildData(fpson.get(j), fpson);
            }
        }
        // 设置子数据
        fp.setfPiattaformas(stessoLive);
    }

    /**
     * 新增数据集管理
     *
     * @param oaDataManage 数据集管理
     * @return 结果
     */
    @Override
    public int insertOaDataManage(OaDataManage oaDataManage)
    {
        //不是一级目录
        if (oaDataManage.getParentId() != null){
            //校验编号是否唯一
            OaDataManage dataManage = new OaDataManage();
            if (null != oaDataManage.getDataCode() && !oaDataManage.getDataCode().equals("")){
                dataManage= oaDataManageMapper.checkRepetition(oaDataManage.getDataCode(),oaDataManage.getFirstDataCode());
                if (!Objects.isNull(dataManage)){
                    throw new ServiceException("当前分类已存在相同编码，请重新输入！");
                }
                //校验上级状态，停用状态不允许新增子节点
                OaDataManage oaDataManage1 = oaDataManageMapper.selectOaDataManageById(oaDataManage.getParentId());
                if (!Objects.isNull(oaDataManage1)) {
                    if (oaDataManage1.getStatus().equals("1")){
                        throw new ServiceException("所选上级已停用，不允许新增");
                    }
                }
                //设置默认父级编码
                if (null == oaDataManage.getFirstDataCode() && oaDataManage.getFirstDataCode().equals("")){
                    int countNum = oaDataManageMapper.selectParentData();
                    oaDataManage.setFirstDataCode("dataManage" + countNum);
                }
            }
        }else {
            //是第一级目录，校验是否和其他第一级目录的编码重复
            OaDataManage dataManage = new OaDataManage();
            if (null != oaDataManage.getDataCode() && !oaDataManage.getDataCode().equals("")) {
                dataManage = oaDataManageMapper.checkFirstRepetition(oaDataManage.getDataCode());
                if (!Objects.isNull(dataManage)) {
                    throw new ServiceException("编码已存在，请重新输入！");
                }
            }
            //设置默认父级编码
            int countNum = oaDataManageMapper.selectParentData();
            oaDataManage.setFirstDataCode("dataManage"+countNum);
        }

        SysUser user = SecurityUtils.getLoginUser().getUser();
        oaDataManage.setCreateTime(DateUtils.getNowDate());
        oaDataManage.setCreateBy(user.getUserName());
        //获取创建人的所属公司和所属部门
        OaPostCompanyVo oaPostCompanyVo = oaDataManageMapper.selectUserHomePostAndCompanyByUserId(user.getUserId());
        oaDataManage.setDeptId(oaPostCompanyVo.getDeptId() == null ? -1 : oaPostCompanyVo.getDeptId());
        oaDataManage.setCompanyId(oaPostCompanyVo.getCompanyId() == null ? -1 : oaPostCompanyVo.getCompanyId());
        return oaDataManageMapper.insertOaDataManage(oaDataManage);
    }

    /**
     * 修改数据集管理
     *
     * @param oaDataManage 数据集管理
     * @return 结果
     */
    @Override
    public int updateOaDataManage(OaDataManage oaDataManage)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        //不是一级目录
        if (oaDataManage.getParentId() != null){
            //校验编号是否唯一
            if (null != oaDataManage.getDataCode() && !oaDataManage.getDataCode().equals("")) {
                OaDataManage dataManage = oaDataManageMapper.checkRepetition(oaDataManage.getDataCode(), oaDataManage.getFirstDataCode());
                if (!Objects.isNull(dataManage)) {
                    if (!dataManage.getId().toString().equals(oaDataManage.getId().toString())) {
                        throw new ServiceException("当前分类已存在相同编码，请重新输入！");
                    }
                }
                //校验上级状态，停用状态不允许新增子节点
                OaDataManage oaDataManage1 = oaDataManageMapper.selectOaDataManageById(oaDataManage.getParentId());
                if (!Objects.isNull(oaDataManage1)) {
                    if (oaDataManage1.getStatus().equals("1")) {
                        throw new ServiceException("所选上级已停用，不允许新增");
                    }
                }
            }
        }else {
            //是第一级目录，校验是否和其他第一级目录的编码重复
            if (null != oaDataManage.getDataCode() && !oaDataManage.getDataCode().equals("")) {
                OaDataManage dataManage = oaDataManageMapper.checkFirstRepetition(oaDataManage.getDataCode());
                if (!Objects.isNull(dataManage)) {
                    if (!dataManage.getId().toString().equals(oaDataManage.getId().toString())) {
                        throw new ServiceException("编码已存在，请重新输入！");
                    }
                }
            }
        }
        oaDataManage.setUpdateBy(user.getUserName());
        oaDataManage.setUpdateTime(DateUtils.getNowDate());
        return oaDataManageMapper.updateOaDataManage(oaDataManage);
    }

    /**
     * 批量删除数据集管理
     *
     * @param ids 需要删除的数据集管理主键
     * @return 结果
     */
    @Override
    public int deleteOaDataManageByIds(Long[] ids)
    {
        return oaDataManageMapper.deleteOaDataManageByIds(ids);
    }

    /**
     * 删除数据集管理信息
     *
     * @param id 数据集管理主键
     * @return 结果
     */
    @Override
    public int deleteOaDataManageById(Long id)
    {
        //判断要删除的节点是否是第一级节点
        OaDataManage oaDataManage = oaDataManageMapper.selectOaDataManageById(id);
        if (oaDataManage.getParentId() == null){
            //是，则删除所有子节点
            return oaDataManageMapper.deleteAllChild(oaDataManage.getFirstDataCode());
        }else {
            //不是，则获取当前节点下所有子节点
            List<Long> dataManageIdList = new ArrayList<>();
            List<Long> idList = this.getChild(id, dataManageIdList);
            Long[] ids = idList.toArray(new Long[idList.size()]);
            return oaDataManageMapper.deleteOaDataManageByIds(ids);
        }
    }

    /**
     * 获取所有子节点
     * @param id
     */
    private List<Long> getChild(Long id, List<Long> dataManageIdList) {
        List<OaDataManage> oaDataManageList = oaDataManageMapper.selectOaDataManageByParentId(id);
        dataManageIdList.add(id);
        if (!CollectionUtils.isEmpty(oaDataManageList)){
            for (OaDataManage oaDataManage : oaDataManageList) {
                getChild(oaDataManage.getId(), dataManageIdList);
            }
        }
        return dataManageIdList;
    }

    /**
     * 根据第一层目录编码查询数据集字典
     * @param firstDataCode
     * @return
     */
    @Override
    public List<OaDataManage> selectDataManageListByCode(String firstDataCode) {
        List<OaDataManage> dataManageList = new ArrayList<>();
        List<OaDataManage> oaDataManageList = oaDataManageMapper.selectDataManageListByCode(firstDataCode);
        if (!CollectionUtils.isEmpty(oaDataManageList)){
            dataManageList = this.getTree(oaDataManageList);
        }
        return dataManageList;
    }

    /**
     * 根据第一层目录编码查询数据集字典 并拼接
     * @param firstDataCode
     * @return
     */
    @Override
    public List<OaDataManage> selectSplicingListByCode(String firstDataCode){
        List<OaDataManage> oaDataManageList = oaDataManageMapper.selectDataManageListByCode(firstDataCode).stream()
                            .filter(vo -> vo.getParentId() != null) //过滤最上一级节点;
                            .collect(Collectors.toList());
        Map<Long,OaDataManage> type = oaDataManageList.stream().collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));

        for (OaDataManage businessTypeRe : oaDataManageList) {
            String dataNameWithParents = getDataNameWithParents(businessTypeRe.getId(), type);
            if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                dataNameWithParents = dataNameWithParents.substring(1);
            }
            businessTypeRe.setDataName(dataNameWithParents);
        }
        return oaDataManageList;
    }
}
