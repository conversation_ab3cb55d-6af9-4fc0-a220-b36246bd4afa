package org.ruoyi.core.oasystem.service;

import org.ruoyi.core.oasystem.domain.OaProjectTypeCorrelation;
import org.ruoyi.core.oasystem.domain.vo.OaProjectTypeCorrelationVo;

import java.util.List;

/**
 * 项目类型与功能关联Service接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IOaProjectTypeCorrelationService
{
    /**
     * 查询项目类型与功能关联
     *
     * @param id 项目类型与功能关联主键
     * @return 项目类型与功能关联
     */
    public OaProjectTypeCorrelationVo selectOaProjectTypeCorrelationById(Long id);

    public OaProjectTypeCorrelationVo selectOaProjectTypeCorrelationByProjectPortfolioCode(String projectPortfolioCode);

    /**
     * 查询项目类型与功能关联列表
     *
     * @param oaProjectTypeCorrelation 项目类型与功能关联
     * @return 项目类型与功能关联集合
     */
    public List<OaProjectTypeCorrelationVo> selectOaProjectTypeCorrelationList(OaProjectTypeCorrelationVo oaProjectTypeCorrelation);

    /**
     * 新增项目类型与功能关联
     *
     * @param oaProjectTypeCorrelation 项目类型与功能关联
     * @return 结果
     */
    public int insertOaProjectTypeCorrelation(OaProjectTypeCorrelationVo oaProjectTypeCorrelation);

    /**
     * 修改项目类型与功能关联
     *
     * @param oaProjectTypeCorrelation 项目类型与功能关联
     * @return 结果
     */
    public int updateOaProjectTypeCorrelation(OaProjectTypeCorrelationVo oaProjectTypeCorrelation);

    /**
     * 批量删除项目类型与功能关联
     *
     * @param ids 需要删除的项目类型与功能关联主键集合
     * @return 结果
     */
    public int deleteOaProjectTypeCorrelationByIds(Long[] ids);

    /**
     * 删除项目类型与功能关联信息
     *
     * @param id 项目类型与功能关联主键
     * @return 结果
     */
    public int deleteOaProjectTypeCorrelationById(Long id);
}
