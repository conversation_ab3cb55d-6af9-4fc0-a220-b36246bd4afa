package org.ruoyi.core.oasystem.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.oasystem.domain.OaPayRebateRecord;
import org.ruoyi.core.oasystem.service.IOaPayRebateRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
@RestController
@RequestMapping("/oasystem/record")
public class OaPayRebateRecordController extends BaseController
{
    @Autowired
    private IOaPayRebateRecordService oaPayRebateRecordService;

    /**
     * 查询【请填写功能名称】列表
     */
//    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaPayRebateRecord oaPayRebateRecord)
    {
        LoginUser loginUser = getLoginUser();
        startPage();
//        List<OaPayRebateRecord> list = oaPayRebateRecordService.selectOaPayRebateRecordList(oaPayRebateRecord);
        List<Map<String, Object>> list = oaPayRebateRecordService.selectOaPayRebateRecordData(oaPayRebateRecord,loginUser.getUserId());
        return getDataTable(list);
    }


    @GetMapping("/listNoAuthority")
    public TableDataInfo listNoAuthority(OaPayRebateRecord oaPayRebateRecord)
    {
        LoginUser loginUser = getLoginUser();
        startPage();
//        List<OaPayRebateRecord> list = oaPayRebateRecordService.selectOaPayRebateRecordList(oaPayRebateRecord);
        List<Map<String, Object>> list = oaPayRebateRecordService.selectOaPayRebateRecordDataNoAuthority(oaPayRebateRecord);
        return getDataTable(list);
    }


    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaPayRebateRecord oaPayRebateRecord)
    {
        List<OaPayRebateRecord> list = oaPayRebateRecordService.selectOaPayRebateRecordList(oaPayRebateRecord);
        ExcelUtil<OaPayRebateRecord> util = new ExcelUtil<OaPayRebateRecord>(OaPayRebateRecord.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaPayRebateRecordService.selectOaPayRebateRecordById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaPayRebateRecord oaPayRebateRecord)
    {
        return toAjax(oaPayRebateRecordService.insertOaPayRebateRecord(oaPayRebateRecord));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaPayRebateRecord oaPayRebateRecord)
    {
        return toAjax(oaPayRebateRecordService.updateOaPayRebateRecord(oaPayRebateRecord));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(oaPayRebateRecordService.deleteOaPayRebateRecordByIds(ids));
    }
}
