package org.ruoyi.core.oasystem.domain;

import com.ruoyi.common.annotation.Excel;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AddOaAccountRulers.java
 * @Description TODO
 * @createTime 2023年10月24日 14:01:00
 */
public class AddOaAccountRulers {

    private Long conpanyNo;
    private Long flowModelId;
    /** 流程模板名称 */
    @Excel(name = "流程模板名称")
    private String flowModelName;
    /**
     * 备注
     */
    private String remark;

    /** 账套id */

    private Long accountSetsId;
    /**
     * 收款方金额字段
     */
    private String collAccountingField;
    /** 关联id */
    private String associationId;

    /** 收款方ID */
    @Excel(name = "收款方字段")
    private String collectionField;
    /** 收款方科目类型 */
    @Excel(name = "收款方科目类型")
    private String collSubjectType;

    /** 收款方字 */
    @Excel(name = "收款方字")
    private String collFinanicalWord;

    /** 收款方摘要 */
    @Excel(name = "收款方摘要")
    private String collAbstractJson;

    /** 收款方生成凭证Y是N否 */
    @Excel(name = "收款方生成凭证Y是N否")
    private String isVoucher;
    /**
     * 付款方
     */
    private List<OaAccountingVoucherRulesUtils> paymentList;

    private List<OaAccountingVoucherRules> dataList;
    /**
     * 收款方
     */
    private List<Map<String,Object>> tableList5;
    private List<Map<String,Object>> tableList6;
    private List<Map<String,Object>> tableList7;
    private List<Map<String,Object>> tableList8;

    public Long getConpanyNo() {
        return conpanyNo;
    }

    public void setConpanyNo(Long conpanyNo) {
        this.conpanyNo = conpanyNo;
    }

    public Long getFlowModelId() {
        return flowModelId;
    }

    public void setFlowModelId(Long flowModelId) {
        this.flowModelId = flowModelId;
    }

    public List<OaAccountingVoucherRulesUtils> getPaymentList() {
        return paymentList;
    }

    public void setPaymentList(List<OaAccountingVoucherRulesUtils> paymentList) {
        this.paymentList = paymentList;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCollectionField() {
        return collectionField;
    }

    public void setCollectionField(String collectionField) {
        this.collectionField = collectionField;
    }

    public String getCollSubjectType() {
        return collSubjectType;
    }

    public void setCollSubjectType(String collSubjectType) {
        this.collSubjectType = collSubjectType;
    }

    public String getCollFinanicalWord() {
        return collFinanicalWord;
    }

    public void setCollFinanicalWord(String collFinanicalWord) {
        this.collFinanicalWord = collFinanicalWord;
    }

    public String getCollAbstractJson() {
        return collAbstractJson;
    }

    public void setCollAbstractJson(String collAbstractJson) {
        this.collAbstractJson = collAbstractJson;
    }

    public String getIsVoucher() {
        return isVoucher;
    }

    public void setIsVoucher(String isVoucher) {
        this.isVoucher = isVoucher;
    }

    public List<Map<String, Object>> getTableList5() {
        return tableList5;
    }

    public void setTableList5(List<Map<String, Object>> tableList5) {
        this.tableList5 = tableList5;
    }

    public List<Map<String, Object>> getTableList6() {
        return tableList6;
    }

    public void setTableList6(List<Map<String, Object>> tableList6) {
        this.tableList6 = tableList6;
    }

    public List<Map<String, Object>> getTableList7() {
        return tableList7;
    }

    public void setTableList7(List<Map<String, Object>> tableList7) {
        this.tableList7 = tableList7;
    }

    public List<Map<String, Object>> getTableList8() {
        return tableList8;
    }

    public void setTableList8(List<Map<String, Object>> tableList8) {
        this.tableList8 = tableList8;
    }

    public AddOaAccountRulers() {
    }

    public String getFlowModelName() {
        return flowModelName;
    }

    public void setFlowModelName(String flowModelName) {
        this.flowModelName = flowModelName;
    }

    public List<OaAccountingVoucherRules> getDataList() {
        return dataList;
    }

    public void setDataList(List<OaAccountingVoucherRules> dataList) {
        this.dataList = dataList;
    }

    public String getAssociationId() {
        return associationId;
    }

    public void setAssociationId(String associationId) {
        this.associationId = associationId;
    }

    public String getCollAccountingField() {
        return collAccountingField;
    }

    public void setCollAccountingField(String collAccountingField) {
        this.collAccountingField = collAccountingField;
    }

    public Long getAccountSetsId() {
        return accountSetsId;
    }

    public void setAccountSetsId(Long accountSetsId) {
        this.accountSetsId = accountSetsId;
    }

    @Override
    public String toString() {
        return "AddOaAccountRulers{" +
                "conpanyNo=" + conpanyNo +
                ", flowModelId=" + flowModelId +
                ", flowModelName='" + flowModelName + '\'' +
                ", remark='" + remark + '\'' +
                ", accountSetsId=" + accountSetsId +
                ", collAccountingField='" + collAccountingField + '\'' +
                ", associationId='" + associationId + '\'' +
                ", collectionField='" + collectionField + '\'' +
                ", collSubjectType='" + collSubjectType + '\'' +
                ", collFinanicalWord='" + collFinanicalWord + '\'' +
                ", collAbstractJson='" + collAbstractJson + '\'' +
                ", isVoucher='" + isVoucher + '\'' +
                ", paymentList=" + paymentList +
                ", dataList=" + dataList +
                ", tableList5=" + tableList5 +
                ", tableList6=" + tableList6 +
                ", tableList7=" + tableList7 +
                ", tableList8=" + tableList8 +
                '}';
    }

    public AddOaAccountRulers(Long conpanyNo, Long flowModelId, String flowModelName, String remark, Long accountSetsId, String collAccountingField, String associationId, String collectionField, String collSubjectType, String collFinanicalWord, String collAbstractJson, String isVoucher, List<OaAccountingVoucherRulesUtils> paymentList, List<OaAccountingVoucherRules> dataList, List<Map<String, Object>> tableList5, List<Map<String, Object>> tableList6, List<Map<String, Object>> tableList7, List<Map<String, Object>> tableList8) {
        this.conpanyNo = conpanyNo;
        this.flowModelId = flowModelId;
        this.flowModelName = flowModelName;
        this.remark = remark;
        this.accountSetsId = accountSetsId;
        this.collAccountingField = collAccountingField;
        this.associationId = associationId;
        this.collectionField = collectionField;
        this.collSubjectType = collSubjectType;
        this.collFinanicalWord = collFinanicalWord;
        this.collAbstractJson = collAbstractJson;
        this.isVoucher = isVoucher;
        this.paymentList = paymentList;
        this.dataList = dataList;
        this.tableList5 = tableList5;
        this.tableList6 = tableList6;
        this.tableList7 = tableList7;
        this.tableList8 = tableList8;
    }
}
