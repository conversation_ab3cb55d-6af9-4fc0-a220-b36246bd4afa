package org.ruoyi.core.oasystem.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 记账凭证规则记录表 - 实体类
 *
 * @Description
 * <AUTHOR>
 * @Date 2023/12/28 16:53
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaEditApproveGeneralityRecords extends BaseEntity {
    private static final long serialVersionUID = 1L;

    //主键
    private Long id;

    //代表的相关功能。1付款人配置，2收款人配置，3项目与流程关联，4项目名称配置
    private String oaApplyType;

    //OA功能相关的申请id
    private Long oaApplyId;

    //存的数据JSON
    private String data;

    //状态，0正常，1禁用
    private String status;
}
