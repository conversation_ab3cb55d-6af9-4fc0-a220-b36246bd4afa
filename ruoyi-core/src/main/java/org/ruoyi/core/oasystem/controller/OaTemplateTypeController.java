package org.ruoyi.core.oasystem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.ruoyi.core.oasystem.domain.OaTemplateType;
import org.ruoyi.core.oasystem.service.IOaTemplateTypeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2024-10-21
 */
@RestController
@RequestMapping("/oaSystem/templateType")
public class OaTemplateTypeController extends BaseController
{
    @Autowired
    private IOaTemplateTypeService oaTemplateTypeService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:type:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaTemplateType oaTemplateType)
    {
        startPage();
        List<OaTemplateType> list = oaTemplateTypeService.selectOaTemplateTypeList(oaTemplateType);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:type:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaTemplateType oaTemplateType)
    {
        List<OaTemplateType> list = oaTemplateTypeService.selectOaTemplateTypeList(oaTemplateType);
        ExcelUtil<OaTemplateType> util = new ExcelUtil<OaTemplateType>(OaTemplateType.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:type:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaTemplateTypeService.selectOaTemplateTypeById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PostMapping("/addOrUpdateTemplateType")
    public AjaxResult add(@RequestBody OaTemplateType oaTemplateType)
    {
        LoginUser loginUser = getLoginUser();
        oaTemplateTypeService.insertOaTemplateType(oaTemplateType,loginUser);
        return toAjax(1);
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:type:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaTemplateType oaTemplateType)
    {
        return toAjax(oaTemplateTypeService.updateOaTemplateType(oaTemplateType));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:type:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(oaTemplateTypeService.deleteOaTemplateTypeByIds(ids));
    }


    /**
     * 查询批量配置时的流程模板列表
     * @param oaProcessTemplate
     * @return {@link TableDataInfo}
     */
    @GetMapping("/getTemplateList")
    public TableDataInfo getTemplateList(OaProcessTemplate oaProcessTemplate)
    {
        startPage();
        List<OaProcessTemplate> list = oaTemplateTypeService.selectOaProcessTemplateList(oaProcessTemplate);
        return getDataTable(list);
    }

}
