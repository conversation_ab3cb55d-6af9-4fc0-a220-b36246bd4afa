package org.ruoyi.core.oasystem.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 oa_pfd_vouchar_details
 * 
 * <AUTHOR>
 * @date 2024-03-27
 */
public class OaPfdVoucharDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 流程id */
    @Excel(name = "流程id")
    private String pfdId;

    /** 账套id */

    private Long accountSetId;
    @Excel(name = "账套名称")
    private String accountSetName;

    /**
     *凭证id
     */
    private Long voucharId;
//生成状态 0成功1失败
    private String generateStatus;
// 生成成功或者失败原因
    @Excel(name = "凭证生成状态")
    private String voucharStatus;
    /** 凭证编号 */
    @Excel(name = "凭证编号")
    private String voucharCode;

    /** 凭证日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "凭证日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date voucharDate;

    /** 借方科目 */
    @Excel(name = "借方科目")
    private String borrowSubject;

    /** 贷方科目 */
    @Excel(name = "贷方科目")
    private String loanSubject;

    /** 借方金额 */
    @Excel(name = "借方金额")
    private BigDecimal borrowAmount;

    /** 贷方金额 */
    @Excel(name = "贷方金额")
    private BigDecimal loanAmount;

    /** 摘要 */
    @Excel(name = "摘要")
    private String voucharAbstract;

    /** 制单人id */

    private Long makeVoucharUser;
    @Excel(name = "制单人")
    private String userName;

    /** 合计金额 */
    @Excel(name = "合计金额")
    private BigDecimal amountSum;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPfdId(String pfdId) 
    {
        this.pfdId = pfdId;
    }

    public String getPfdId() 
    {
        return pfdId;
    }
    public void setAccountSetId(Long accountSetId) 
    {
        this.accountSetId = accountSetId;
    }

    public Long getAccountSetId() 
    {
        return accountSetId;
    }
    public void setVoucharCode(String voucharCode) 
    {
        this.voucharCode = voucharCode;
    }

    public String getVoucharCode() 
    {
        return voucharCode;
    }
    public void setVoucharDate(Date voucharDate) 
    {
        this.voucharDate = voucharDate;
    }

    public Date getVoucharDate() 
    {
        return voucharDate;
    }
    public void setBorrowSubject(String borrowSubject) 
    {
        this.borrowSubject = borrowSubject;
    }

    public String getBorrowSubject() 
    {
        return borrowSubject;
    }
    public void setLoanSubject(String loanSubject) 
    {
        this.loanSubject = loanSubject;
    }

    public String getLoanSubject() 
    {
        return loanSubject;
    }
    public void setBorrowAmount(BigDecimal borrowAmount) 
    {
        this.borrowAmount = borrowAmount;
    }

    public BigDecimal getBorrowAmount() 
    {
        return borrowAmount;
    }
    public void setLoanAmount(BigDecimal loanAmount) 
    {
        this.loanAmount = loanAmount;
    }

    public BigDecimal getLoanAmount() 
    {
        return loanAmount;
    }
    public void setVoucharAbstract(String voucharAbstract) 
    {
        this.voucharAbstract = voucharAbstract;
    }

    public String getVoucharAbstract() 
    {
        return voucharAbstract;
    }
    public void setMakeVoucharUser(Long makeVoucharUser) 
    {
        this.makeVoucharUser = makeVoucharUser;
    }

    public Long getMakeVoucharUser() 
    {
        return makeVoucharUser;
    }
    public void setAmountSum(BigDecimal amountSum) 
    {
        this.amountSum = amountSum;
    }

    public BigDecimal getAmountSum() 
    {
        return amountSum;
    }

    public String getAccountSetName() {
        return accountSetName;
    }

    public void setAccountSetName(String accountSetName) {
        this.accountSetName = accountSetName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getVoucharId() {
        return voucharId;
    }

    public void setVoucharId(Long voucharId) {
        this.voucharId = voucharId;
    }

    public String getGenerateStatus() {
        return generateStatus;
    }

    public void setGenerateStatus(String generateStatus) {
        this.generateStatus = generateStatus;
    }

    public String getVoucharStatus() {
        return voucharStatus;
    }

    public void setVoucharStatus(String voucharStatus) {
        this.voucharStatus = voucharStatus;
    }

    @Override
    public String toString() {
        return "OaPfdVoucharDetails{" +
                "id=" + id +
                ", pfdId='" + pfdId + '\'' +
                ", accountSetId=" + accountSetId +
                ", accountSetName='" + accountSetName + '\'' +
                ", voucharId=" + voucharId +
                ", generateStatus='" + generateStatus + '\'' +
                ", voucharStatus='" + voucharStatus + '\'' +
                ", voucharCode='" + voucharCode + '\'' +
                ", voucharDate=" + voucharDate +
                ", borrowSubject='" + borrowSubject + '\'' +
                ", loanSubject='" + loanSubject + '\'' +
                ", borrowAmount=" + borrowAmount +
                ", loanAmount=" + loanAmount +
                ", voucharAbstract='" + voucharAbstract + '\'' +
                ", makeVoucharUser=" + makeVoucharUser +
                ", userName='" + userName + '\'' +
                ", amountSum=" + amountSum +
                '}';
    }
}
