package org.ruoyi.core.oasystem.service.impl;

import com.google.common.collect.Lists;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthPermissionEnum;
import com.ruoyi.common.enums.ProcFormDataStatusEnums;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.system.mapper.SysRoleMapper;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.EndEvent;
import org.activiti.bpmn.model.FlowNode;
import org.activiti.bpmn.model.SequenceFlow;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.domain.dto.*;
import org.ruoyi.core.oasystem.domain.vo.*;
import org.ruoyi.core.oasystem.mapper.*;
import org.ruoyi.core.oasystem.service.IOaProcessTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OA系统-流程模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-25
 */
@Slf4j
@Service
public class OaProcessTemplateServiceImpl implements IOaProcessTemplateService
{
    @Autowired
    private OaProcessTemplateMapper oaProcessTemplateMapper;

    @Autowired
    private OaProcessClassificationMapper oaProcessClassificationMapper;

    @Autowired
    private OaMyUsualProcessMapper oaMyUsualProcessMapper;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private OaProcessClassificationServiceImpl oaProcessClassificationService;

    @Autowired
    private OaFormFieldMapper oaFormFieldMapper;

    @Autowired
    private ProcFormMapper procFormMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private OaTemplateTypeMapper oaTemplateTypeMapper;
    /**
     * 查询OA系统-流程模板主
     *
     * @param id OA系统-流程模板主主键
     * @return OA系统-流程模板主
     */
    @Override
    public ProcessTemplateVo selectOaProcessTemplateById(Long id,String status)
    {
        //如果当前表单当前状态为已驳回，则查询流程历史表
        OaProcessTemplate oaProcessTemplate;
        OaTemplateType oaTemplateType = new OaTemplateType();
        if(StringUtils.isNotEmpty(status) && ProcFormDataStatusEnums.REJECTED.getCode().equals(status)){
            oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateHisById(id);
            oaTemplateType.setTemplateId(oaProcessTemplate.getParentId());
            oaTemplateType.setConfigurationItemsType("yspTemplate_type");
            List<OaTemplateType> oaTemplateTypes = oaTemplateTypeMapper.selectOaTemplateTypeList(oaTemplateType);
            List<String> ysp = new ArrayList<>();
            if(oaTemplateTypes.size()>0){
                for (OaTemplateType templateType : oaTemplateTypes) {
                    ysp.add(templateType.getConfigurationItemsValue());
                }
            }
            oaProcessTemplate.setYspTypeList(ysp);
        }else{
            oaProcessTemplate = oaProcessTemplateMapper.selectOaProcessTemplateById(id);
            oaTemplateType.setTemplateId(oaProcessTemplate.getParentId());
            oaTemplateType.setConfigurationItemsType("yspTemplate_type");
            List<OaTemplateType> oaTemplateTypes = oaTemplateTypeMapper.selectOaTemplateTypeList(oaTemplateType);
            List<String> ysp = new ArrayList<>();
            if(oaTemplateTypes.size()>0){
                for (OaTemplateType templateType : oaTemplateTypes) {
                    ysp.add(templateType.getConfigurationItemsValue());
                }
            }
            oaProcessTemplate.setYspTypeList(ysp);
        }
        //判断是否有默认可阅览人员;0-有，1-没有
        List<SysUserForOaProcessTemplate> userList = oaProcessTemplateMapper.selectOaProcessTemplateUserByTemplateId(id);
        oaProcessTemplate.setUserList(userList);
        //判断是否有默认的可阅览岗位;0-有，1没有
        List<SysPostForOaProcessTemplate> postList = oaProcessTemplateMapper.selectOaProcessTemplatePostByTemplateId(id);
        oaProcessTemplate.setPostList(postList);
        //查通知方式
        List<OaProcessTemplateNotification> oaProcessTemplateNotificationList = oaProcessTemplateMapper.selectOaProcessTemplateNotificationByTemplateId(id);
        oaProcessTemplate.setOaProcessTemplateNotificationList(oaProcessTemplateNotificationList);
        //查历史修订记录
        List<OaProcessTemplateUpdateInfo> oaProcessTemplateUpdateInfoList = oaProcessTemplateMapper.selectOaProcessTemplateUpdateInfoByTemplateId(oaProcessTemplate.getParentId());
        oaProcessTemplate.setOaProcessTemplateUpdateInfoList(oaProcessTemplateUpdateInfoList);
        ProcessTemplateVo processTemplateVo = new ProcessTemplateVo();
        processTemplateVo.setOaProcessTemplate(oaProcessTemplate);
        //表单内容
        String formDefination = oaProcessTemplateMapper.getFormDefinationByFormId(oaProcessTemplate.getFormId());
        processTemplateVo.setDefination(formDefination);
        //todo dyh 后续要补充的逻辑。。。      流程处理
        return processTemplateVo;
    }

    /**
     * 查询OA系统-流程模板主列表
     *
     * @param oaProcessTemplate OA系统-流程模板主
     * @return OA系统-流程模板主
     */
    @Override
    public List<OaProcessTemplate> selectOaProcessTemplateList(OaProcessTemplate oaProcessTemplate)
    {
//        if (oaProcessTemplate.getClassificationId() == null) {
//            return new ArrayList<OaProcessTemplate>();
//        }
        return oaProcessTemplateMapper.selectOaProcessTemplateList(oaProcessTemplate);
    }

    /**
     * 新增OA系统-流程模板主
     *
     * @param oaProcessTemplate OA系统-流程模板主
     * @param loginUser 用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertOaProcessTemplate(OaProcessTemplate oaProcessTemplate, LoginUser loginUser)
    {
        String nickName = loginUser.getUser().getNickName();
        Date nowDate = DateUtils.getNowDate();
        oaProcessTemplate.setCreateTime(nowDate);
        oaProcessTemplate.setEndUpdateTime(nowDate);
        oaProcessTemplate.setUpdateTime(nowDate);
        oaProcessTemplate.setCreateBy(nickName);
        oaProcessTemplate.setUpdateBy(nickName);
        //检验是否存在相同OA关联模版
        if (oaProcessTemplate.getOaModuleType() != null && !oaProcessTemplate.getOaModuleType().isEmpty()){
            OaProcessTemplate template = oaProcessTemplateMapper.queryTemplateByOaModuleTypeClassificationId(oaProcessTemplate);
          if (template != null){
              throw new ServiceException("模版所属公司已有所选OA模块关联,删除或关闭启用重复模版后再添加");
          }
        }
        int i = oaProcessTemplateMapper.insertOaProcessTemplate(oaProcessTemplate);
        if (i > 0) {
            //获得自增id
            Long id = oaProcessTemplate.getId();
            oaProcessTemplate.setParentId(id);
            //插入模版类型表数据
            this.insertOrUpdateTemplateType(oaProcessTemplate,loginUser);
            //修改父模板ID
            oaProcessTemplateMapper.updateParentId(id);
            //插入流程模板历史表数据
            oaProcessTemplateMapper.insertOaProcessTemplateHis(oaProcessTemplate);
            //人员入库
            //删除之前的人员
            List<Long> selectList = oaProcessTemplate.getSelectList();
            if(!CollectionUtils.isEmpty(selectList)){
                for (Long userId:selectList) {
                    TemplateUserDto templateUserDto = new TemplateUserDto();
                    templateUserDto.setTemplateId(id);
                    templateUserDto.setUserId(userId);
                    templateUserDto.setStatus("0");
                    int a = oaProcessTemplateMapper.insertOaProcessTemplateUser(templateUserDto);
                }
            }


            List<Long> postSelectList = oaProcessTemplate.getPostSelectList();
            if(!CollectionUtils.isEmpty(postSelectList)){
                for (Long postId:postSelectList) {
                    TemplatePostDto templatePostDto = new TemplatePostDto();
                    templatePostDto.setTemplateId(id);
                    templatePostDto.setPostId(postId);
                    templatePostDto.setStatus("0");
                    int a = oaProcessTemplateMapper.insertOaProcessTemplatePost(templatePostDto);
                }
            }

            //通知方式入库
            List<OaProcessTemplateNotification> oaProcessTemplateNotificationList = oaProcessTemplate.getOaProcessTemplateNotificationList();
            if(!CollectionUtils.isEmpty(oaProcessTemplateNotificationList)){
                for (OaProcessTemplateNotification oaProcessTemplateNotification:oaProcessTemplateNotificationList) {
                    oaProcessTemplateNotification.setTemplateId(id);
                    oaProcessTemplateNotification.setCreateBy(nickName);
                    oaProcessTemplateNotification.setUpdateBy(nickName);
                    oaProcessTemplateNotification.setCreateTime(nowDate);
                    oaProcessTemplateNotification.setUpdateTime(nowDate);
                    int q = oaProcessTemplateMapper.insertOaProcessTemplateNotification(oaProcessTemplateNotification);
                }
            }
        }
        return i;
    }
    /**
     * 同步类型表数据
     * @param oaProcessTemplate
     * @param loginUser
     */
    public void insertOrUpdateTemplateType(OaProcessTemplate oaProcessTemplate, LoginUser loginUser){
        // pzx_type :以下各个类型
        // cwTemplate_type 财务流程 ：PT普通流程0、CW财务流程1
        // gdTemplate_type 合同归档 ：N 无需归档 HT合同档案库 JCXZ基础行政档案库
        // yspTemplate_type 预审批流程分类：审批流程SP、用印流程YY、归档流程GD
        // oaModule_type 关联OA模块功能 取字典
        List<OaTemplateType> oaTemplateTypes = new ArrayList<>();
        if(null != oaProcessTemplate.getTemplateType()){
            OaTemplateType caiwu = new OaTemplateType();
            caiwu.setTemplateId(oaProcessTemplate.getParentId());
            caiwu.setConfigurationItemsType("cwTemplate_type");
            caiwu.setCreateBy(loginUser.getUsername());
            caiwu.setCreateTime(DateUtils.getNowDate());
            caiwu.setUpdateBy(loginUser.getUsername());
            caiwu.setUpdateTime(DateUtils.getNowDate());
            if("0".equals(oaProcessTemplate.getTemplateType())){
                caiwu.setConfigurationItemsValue("PT");
            }else if("1".equals(oaProcessTemplate.getTemplateType())){
                caiwu.setConfigurationItemsValue("CW");
            }
            oaTemplateTypes.add(caiwu);
        }
        OaTemplateType htgd = new OaTemplateType();
        htgd.setTemplateId(oaProcessTemplate.getParentId());
        htgd.setConfigurationItemsType("gdTemplate_type");
        htgd.setCreateBy(loginUser.getUsername());
        htgd.setCreateTime(DateUtils.getNowDate());
        htgd.setUpdateBy(loginUser.getUsername());
        htgd.setUpdateTime(DateUtils.getNowDate());
//        if("N".equals(oaProcessTemplate.getIsFiling())){
//            htgd.setConfigurationItemsValue("N");
//        }else  if("HT".equals(oaProcessTemplate.getIsFiling())){
//            htgd.setConfigurationItemsValue("HT");
//        }else  if("JCXZ".equals(oaProcessTemplate.getIsFiling())){
//            htgd.setConfigurationItemsValue("JCXZ");
//        }
        htgd.setConfigurationItemsValue(oaProcessTemplate.getIsFiling());
        oaTemplateTypes.add(htgd);
        if(null != oaProcessTemplate.getYspTypeList() && oaProcessTemplate.getYspTypeList().size()>0){
            List<String> strings = oaProcessTemplate.getYspTypeList();
            for (String string : strings) {
                OaTemplateType ysp = new OaTemplateType();
                ysp.setTemplateId(oaProcessTemplate.getParentId());
                ysp.setConfigurationItemsType("yspTemplate_type");
                ysp.setCreateBy(loginUser.getUsername());
                ysp.setCreateTime(DateUtils.getNowDate());
                ysp.setUpdateBy(loginUser.getUsername());
                ysp.setUpdateTime(DateUtils.getNowDate());
                ysp.setConfigurationItemsValue(string);
                oaTemplateTypes.add(ysp);
            }
        }
        if(null != oaProcessTemplate.getOaModuleType()){
            OaTemplateType oa = new OaTemplateType();
            oa.setTemplateId(oaProcessTemplate.getParentId());
            oa.setConfigurationItemsType("oaModule_type");
            oa.setCreateBy(loginUser.getUsername());
            oa.setCreateTime(DateUtils.getNowDate());
            oa.setUpdateBy(loginUser.getUsername());
            oa.setUpdateTime(DateUtils.getNowDate());
            oa.setConfigurationItemsValue(oaProcessTemplate.getOaModuleType());
            oaTemplateTypes.add(oa);
        }
        oaTemplateTypeMapper.batchInsert(oaTemplateTypes);
    }
    /**
     * 修改OA系统-流程模板主
     *
     * @param oaProcessTemplate OA系统-流程模板主
     * @param loginUser 用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOaProcessTemplate(OaProcessTemplate oaProcessTemplate, LoginUser loginUser)
    {
        String nickName = loginUser.getUser().getNickName();
        Date nowDate = DateUtils.getNowDate();
        oaProcessTemplate.setUpdateTime(nowDate);
        oaProcessTemplate.setUpdateBy(nickName);
        oaProcessTemplate.setEndUpdateTime(nowDate);
        //获取被修改的旧模板ID
        Long oldId = oaProcessTemplate.getId();
        //检验是否存在相同OA关联模版
        if (oaProcessTemplate.getOaModuleType() != null && !oaProcessTemplate.getOaModuleType().isEmpty()){
            OaProcessTemplate template = oaProcessTemplateMapper.queryTemplateByOaModuleTypeClassificationId(oaProcessTemplate);
            if (template != null && !Objects.equals(template.getId(), oaProcessTemplate.getId())){
                throw new ServiceException("模版所属公司已有所选OA模块关联,删除或关闭启用重复模版后再修改");
            }
        }
        //通过删除待修改的模板
        oaProcessTemplateMapper.deleteOaProcessTemplateById(oaProcessTemplate.getId());
        //插入新模板
        int i = oaProcessTemplateMapper.insertOaProcessTemplate(oaProcessTemplate);

        if (i > 0) {
            //更新模版类型表数据首先进行删除
            oaTemplateTypeMapper.deleteDataByTemplate(oaProcessTemplate.getParentId());
            this.insertOrUpdateTemplateType(oaProcessTemplate,loginUser);
            //插入历史纪录表
            oaProcessTemplateMapper.insertOaProcessTemplateHis(oaProcessTemplate);
            //获得模板id
            Long id = oaProcessTemplate.getId();
            //将老模板的OA权限赋值给新模板
            sysRoleMapper.updateRoleOa(oldId,id);

            oaProcessTemplateMapper.deleteTemplUserByClass(id);
            List<Long> selectList = oaProcessTemplate.getSelectList();
            for (Long userId:selectList) {
                TemplateUserDto templateUserDto = new TemplateUserDto();
                templateUserDto.setTemplateId(id);
                templateUserDto.setUserId(userId);
                templateUserDto.setStatus("0");
                oaProcessTemplateMapper.insertOaProcessTemplateUser(templateUserDto);
            }

            //岗位入库
            List<Long> postSelectList = oaProcessTemplate.getPostSelectList();
            for (Long postId:postSelectList) {
                TemplatePostDto templatePostDto = new TemplatePostDto();
                templatePostDto.setTemplateId(id);
                templatePostDto.setPostId(postId);
                templatePostDto.setStatus("0");
                oaProcessTemplateMapper.insertOaProcessTemplatePost(templatePostDto);
            }

            //通知方式入库
            List<OaProcessTemplateNotification> oaProcessTemplateNotificationList = oaProcessTemplate.getOaProcessTemplateNotificationList();
            if(!CollectionUtils.isEmpty(oaProcessTemplateNotificationList)){
                for (OaProcessTemplateNotification oaProcessTemplateNotification:oaProcessTemplateNotificationList) {
                    oaProcessTemplateNotification.setTemplateId(id);
                    oaProcessTemplateNotification.setCreateBy(nickName);
                    oaProcessTemplateNotification.setUpdateBy(nickName);
                    oaProcessTemplateNotification.setCreateTime(nowDate);
                    oaProcessTemplateNotification.setUpdateTime(nowDate);
                    oaProcessTemplateMapper.insertOaProcessTemplateNotification(oaProcessTemplateNotification);
                }
            }
            //新增修订说明
            String updateInfo = oaProcessTemplate.getUpdateInfo();
            OaProcessTemplateUpdateInfo oaProcessTemplateUpdateInfo = new OaProcessTemplateUpdateInfo();
            oaProcessTemplateUpdateInfo.setTemplateId(oaProcessTemplate.getParentId());
            oaProcessTemplateUpdateInfo.setUserId(loginUser.getUser().getUserId());
            oaProcessTemplateUpdateInfo.setInformation(updateInfo);
            oaProcessTemplateUpdateInfo.setCreateBy(nickName);
            oaProcessTemplateUpdateInfo.setCreateTime(nowDate);
            oaProcessTemplateUpdateInfo.setUpdateBy(nickName);
            oaProcessTemplateUpdateInfo.setUpdateTime(nowDate);
            oaProcessTemplateMapper.insertOaProcessTemplateUpdateInfo(oaProcessTemplateUpdateInfo);
        }
        return i;
    }

    /**
     * 批量删除OA系统-流程模板主
     *
     * @param ids 需要删除的OA系统-流程模板主主键
     * @return 结果
     */
    @Override
    public int deleteOaProcessTemplateByIds(Long[] ids)
    {
        return oaProcessTemplateMapper.deleteOaProcessTemplateByIds(ids);
    }

    /**
     * 删除OA系统-流程模板主信息
     *
     * @param id OA系统-流程模板主主键
     * @return 结果
     */
    @Override
    public int deleteOaProcessTemplateById(Long id)
    {
        return oaProcessTemplateMapper.deleteOaProcessTemplateById(id);
    }

    @Override
    public int updateEnable(OaProcessTemplate oaProcessTemplate, LoginUser loginUser) {
        oaProcessTemplate.setUpdateTime(DateUtils.getNowDate());
        if ("Y".equals(oaProcessTemplate.getIsEnable())){
            OaProcessTemplate nowTemplate = oaProcessTemplateMapper.selectOaProcessTemplateById(oaProcessTemplate.getId());
            OaProcessTemplate template = oaProcessTemplateMapper.queryTemplateByOaModuleTypeClassificationId(nowTemplate);
            if (template != null && !Objects.equals(template.getId(), nowTemplate.getId())){
                throw new ServiceException("模版所属公司已有所选OA模块关联,删除或关闭启用重复模版后再启用");
            }
        }
        return oaProcessTemplateMapper.updateOaProcessTemplate(oaProcessTemplate);
    }

    @Override
    public int changeNotificationProcessEnable(OaProcessTemplateNotification oaProcessTemplateNotification, LoginUser loginUser) {
        oaProcessTemplateNotification.setUpdateTime(DateUtils.getNowDate());
        return oaProcessTemplateMapper.updateOaProcessTemplateNotification(oaProcessTemplateNotification);
    }

    @Override
    public List<SysUserForOaProcessTemplate> selectUserListForOaProcessTemplate(SysUser user) {
        return oaProcessTemplateMapper.selectUserListForOaProcessTemplate(user);
    }

    @Override
    public List<OaProcessTemplateGlobalSetting> selectOaProcessTemplateGlobalSetting() {
        return oaProcessTemplateMapper.selectOaProcessTemplateGlobalSetting();
    }

    @Override
    public int updateGlobalSettingEnableStatus(OaProcessTemplateGlobalSetting oaProcessTemplateGlobalSetting, LoginUser loginUser) {
        oaProcessTemplateGlobalSetting.setUpdateTime(DateUtils.getNowDate());
        return oaProcessTemplateMapper.updateOaProcessTemplateGlobalSetting(oaProcessTemplateGlobalSetting);
    }

    @Override
    public List<OaProcessTemplateUpdateInfo> selectOaProcessTemplateUpdateInfoList(OaProcessTemplate oaProcessTemplate) {
        return oaProcessTemplateMapper.selectOaProcessTemplateUpdateInfoByTemplateId(oaProcessTemplate.getId());
    }

    @Override
    public Map<String, Object> selectMyUsualProcessList(MyUsualProcess myUsualProcess, LoginUser loginUser) {
        myUsualProcess.setUserId(loginUser.getUserId());
        List<MyUsualProcess> myUsualProcesses = oaProcessTemplateMapper.selectMyUsualProcessListByCompanyIdAndUserId(myUsualProcess.getCompanyId(), myUsualProcess.getUserId());
        //找所有公司下的部门的节点
        List<OaProcessClassification> oaProcessClassifications = oaProcessClassificationMapper.selectOaProcessClassificationByParentIdAndCompanyId(1L, myUsualProcess.getCompanyId());
        //先处理找到的所有条目的祖级列表
        for (MyUsualProcess myUsualProcess1:myUsualProcesses) {
            String ancestors = myUsualProcess1.getAncestors();
            String[] split = ancestors.split(",");
            Long[] longs = new Long[split.length];
            for (int i = 0; i < split.length; i++) {
                longs[i] = Long.parseLong(split[i]);
            }
            myUsualProcess1.setArrayOfAncestor(longs);
        }
        //对现有的节点进行数据重写，重写为一级分类
        for (OaProcessClassification oaProcessClassification:oaProcessClassifications) {
            myUsualProcesses.stream().filter(t -> Arrays.stream(t.getArrayOfAncestor()).anyMatch(a -> a.equals(oaProcessClassification.getId()))).collect(Collectors.toList()).forEach(obj -> {
                Map<String, String> templateObj = obj.getTemplateObj();
                templateObj.put("classificationName", oaProcessClassification.getName());
                obj.setTemplateObj(templateObj);
            });
        }
        int chunkSize = 4;
        List<List<MyUsualProcess>> partition = Lists.partition(myUsualProcesses, chunkSize);
        //分割成了四个一组的List --> partition
        //新建一个Map，然后遍历每一个partition，以此
        Map<String, Object> resultMap = new HashMap<>();
        List<MyUsualProcess> list1 = new ArrayList<>();
        List<MyUsualProcess> list2 = new ArrayList<>();
        List<MyUsualProcess> list3 = new ArrayList<>();
        List<MyUsualProcess> list4 = new ArrayList<>();
        for (List<MyUsualProcess> myUsualProcessList:partition) {
            if (myUsualProcessList.size() > 0) {
                list1.add(myUsualProcessList.get(0));
            }
            if (myUsualProcessList.size() > 1) {
                list2.add(myUsualProcessList.get(1));
            }
            if (myUsualProcessList.size() > 2) {
                list3.add(myUsualProcessList.get(2));
            }
            if (myUsualProcessList.size() > 3) {
                list4.add(myUsualProcessList.get(3));
            }
        }
        resultMap.put("templateObj1", list1);
        resultMap.put("templateObj2", list2);
        resultMap.put("templateObj3", list3);
        resultMap.put("templateObj4", list4);
        return resultMap;
    }

    @Override
    public List<Map<String, Object>> companyList(LoginUser loginUser) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        //查询自己有权限的公司如果是admin 则查询所有公司
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<Long> roleList = new ArrayList<>();
        boolean b = false;
        for (SysRole role : roles) {
            roleList.add(role.getRoleId());
            if(role.getRoleKey().equals("admin")){
                b = true;
                break;
            }
        }
        if(b){

            //查询所有
            resultList=  oaProcessTemplateMapper.queryAllCompany();
        }else {
            //查询所有角色的
          resultList=  oaProcessTemplateMapper.queryCompanyByRole(roleList);
        }

        return resultList;
//        return oaProcessTemplateMapper.selectCompanyList();
    }

    @Override
    public List<Map<String, Object>> getTabsCompanyList(LoginUser loginUser) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        //查询自己有权限的公司如果是admin 则查询所有公司
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<Long> roleList = new ArrayList<>();
        boolean b = false;
        for (SysRole role : roles) {
            roleList.add(role.getRoleId());
            if(role.getRoleKey().equals("admin")){
                b = true;
                break;
            }
        }
        if(!b && (oaProcessTemplateMapper.queryAuthMainByUserId(SecurityUtils.getUserId(), AuthPermissionEnum.UNIT0.getCode()))>0 ){
            b = true;
        }
        if(b){
            //查询所有
            resultList=  oaProcessTemplateMapper.queryTabsAllCompany();
        }else {
            //查询所有角色的
            if(roleList.size()>0){
                resultList=  oaProcessTemplateMapper.queryTabsCompanyByRole(roleList);
            }
            resultList.addAll(oaProcessTemplateMapper.queryTabsCompanyByUserID(SecurityUtils.getUserId()));
            resultList = resultList.stream()
                    .collect(Collectors.toMap(
                            map -> map.get("id"),
                            map -> map,
                            (existing, replacement) -> existing))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
        }
        for (Map<String, Object> map : resultList) {
            String name = map.get("name").toString();
            map.put("name",name);
        }

        return resultList;
    }

    @Override
    public void addMyUseualTempl(MyUseulTemplVo myUseulTemplVo, LoginUser loginUser) {
        Long userId = loginUser.getUserId();
        List<OaMyUsualProcess> myUseulList = myUseulTemplVo.getMyUseulList();
        Long classificationId = null;
        Long companyId = null;
        if (myUseulList.size() > 0) {
            classificationId = myUseulList.get(0).getClassificationId();
//            companyId = this.getCompanyId(classificationId);
            companyId = myUseulList.get(0).getCompanyId();
        }
        List<OaMyUsualProcess> myUsualProcessList = oaProcessTemplateMapper.selectMyUsualProcessListByCompanyIdAndClassificationId(companyId, classificationId, userId);
        for (OaMyUsualProcess oaMyUsualProcess : myUseulList) {
            oaMyUsualProcess.setCompanyId(companyId);
            oaMyUsualProcess.setUserId(userId);
            oaMyUsualProcess.setCreateBy(loginUser.getUsername());
            oaMyUsualProcess.setCreateTime(DateUtils.getNowDate());
            oaMyUsualProcess.setUpdateBy(loginUser.getUsername());
            oaMyUsualProcess.setUpdateTime(DateUtils.getNowDate());
        }
        List<OaMyUsualProcess> collect = myUseulList.stream().filter(t -> !myUsualProcessList.stream().anyMatch(p -> p.getCompanyId().equals(t.getCompanyId())
                && p.getClassificationId().equals(t.getClassificationId()) && p.getTemplateId().equals(t.getTemplateId()) && p.getUserId().equals(t.getUserId()))).collect(Collectors.toList());
//        List<OaMyUsualProcess> myUseulList1 = new ArrayList<>();
//        if (myUsualProcessList.size() > 0) {
//            for (MyUsualProcess myUsualProcess:myUsualProcessList) {
//                for (OaMyUsualProcess myUsualProcess1:myUseulList) {
//                    if (!myUsualProcess.getCompanyId().equals(myUsualProcess1.getCompanyId()) && !myUsualProcess.getClassificationId().equals(myUsualProcess1.getClassificationId())
//                            && !myUsualProcess.getTemplateId().equals(myUsualProcess1.getTemplateId()) && !myUsualProcess.getUserId().equals(myUsualProcess1.getUserId())) {
//                        myUseulList1.add(myUsualProcess1);
//                    }
//                }
//            }
//        } else {
//            myUseulList1 = myUseulList;
//        }
        if(collect.size()>0){
            int i= oaMyUsualProcessMapper.batchInsert(collect);
        }

    }

    @Override
    public List<OaProcessTemplate> queryTemplateListByClass(OaProcessTemplate oaProcessTemplate) {

        return  oaProcessTemplateMapper.getTemplByClass(oaProcessTemplate);
    }

    @Override
    public List<Map<String, Object>> getAllCompany() {
        return oaProcessTemplateMapper.getAllCompany();
    }

    @Override
    public List<OaProcessTemplate> queryTemplateListByCompanyId(OaProcessTemplate oaProcessTemplate) {

        List<OaProcessTemplate> objects = new ArrayList<>();
        //传过来的是公司ID 通过公司ID查询到所属的分类ID
       OaProcessClassification oaProcessClassification  =  oaProcessClassificationMapper.queryDataByCompanyId(oaProcessTemplate.getClassificationId());
       if(null!=oaProcessClassification){
           oaProcessTemplate.setClassificationId(oaProcessClassification.getId());
           objects= oaProcessTemplateMapper.getTemplByClassNoFinance(oaProcessTemplate);
       }

        return objects;
    }

    @Override
    public Map<String, Object> uploadFile(MultipartFile file, String stepId, String businessId, LoginUser loginUser, String soleFlag, String commitStatus) {
//        String uploadPath = RuoYiConfig.getUploadPath();
//        String profile = RuoYiConfig.getProfile();
        String name = file.getOriginalFilename();
        Map<String, Object> a = new HashMap<>();
        try {
            String fileName = FileUploadUtils.uploadOSS(UploadFeatureConstants.OA_SYSTEM, file);
//            String replace = fileName.replace(Constants.RESOURCE_PREFIX, "");
            a.put("url", fileName);
            // todo 添加一个随机生成的标识位，以保证上传的文件被驳回之后是唯一的，作为审批记录的id
            if (soleFlag == null || StringUtils.EMPTY.equals(soleFlag)) {
                soleFlag = UUID.randomUUID().toString();
            }
            a.put("soleFlag", soleFlag);
            FlowFileDto flowFileDto = new FlowFileDto();
            flowFileDto.setBusinessId(businessId);
            flowFileDto.setStepId(stepId);
            flowFileDto.setSoleFlag(soleFlag);
            flowFileDto.setFileName(name);
            flowFileDto.setUrl(fileName);
            flowFileDto.setUploadUserId(loginUser.getUserId());
            flowFileDto.setCreateBy(loginUser.getUsername());
            flowFileDto.setUpdateBy(loginUser.getUsername());
            Date nowDate = DateUtils.getNowDate();
            flowFileDto.setCreateTime(nowDate);
            flowFileDto.setUpdateTime(nowDate);
            flowFileDto.setCommitStatus(commitStatus);
            int i = oaProcessTemplateMapper.insertFlowFileInfo(flowFileDto);
//            a.put("yulanUrl", profile + replace);
        } catch (IOException e) {
            e.printStackTrace();
            a.put("url", null);
            a.put("soleFlag", null);
            log.error("上传文件失败");
//            a.put("yulanUrl", null);
        }
        return a;
    }

    @Override
    public AjaxResult deleteFileByUrl(Map<String, String> obj) {
        String url = obj.get("url");
//        String profile = RuoYiConfig.getProfile();
//        String replace = url.replace(Constants.RESOURCE_PREFIX, "");
//        String deletePath = profile + replace;
//        System.out.println("deletePath = " + deletePath);
        if (FileUtils.deleteFileFromOSS(url)) {
            int i = oaProcessTemplateMapper.deleteFlowFileInfo(obj);
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    @Override
    public AjaxResult deleteMyUsualProcessById(Long id, LoginUser loginUser) {
        //首先看一下是否有这个id
        Long delId = oaMyUsualProcessMapper.selectOaMyUsualProcessByIdAndUserId(id, loginUser.getUserId());
        if (delId == null) {
            return AjaxResult.error(401, "不允许越权删除！");
        }
        int i = oaMyUsualProcessMapper.deleteOaMyUsualProcessById(delId);
        return i > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 根据类型id获取公司的id
     *
     * @param classId 类id
     * @return {@link Long}
     */
    @Override
    public Integer getCompanyId(Long classId){
        Integer integer = new Integer("0");

        OaProcessClassification oaProcessClassification = oaProcessClassificationMapper.selectOaProcessClassificationById(classId);
        String ancestors = oaProcessClassification.getAncestors();
        List<String> strings = Arrays.asList(ancestors.split(","));
        List<OaProcessClassification> oaProcessClassifications = oaProcessClassificationMapper.queryDataById(strings);

        if(oaProcessClassifications.size() == 0){
            integer = oaProcessClassification.getCompanyId();
        }else {
            for (OaProcessClassification processClassification : oaProcessClassifications) {
                if (processClassification.getIsCompany().equals("0")) {
                    integer = processClassification.getCompanyId();
                }
            }
        }
        return integer;
    }

    @Override
    public List<Map<String, Object>> allCompanyList() {
        List<Map<String, Object>> resultList = new ArrayList<>();

            //查询所有
            resultList=  oaProcessTemplateMapper.queryAllCompany();
        return resultList;
//        return oaProcessTemplateMapper.selectCompanyList();
    }

    @Override
    public List<SysUserForOaProcessTemplate> selectUserListForOaProcessTemplateForFlow(SysUser user, Long templateId) {
        List<SysUserForOaProcessTemplate> sysUserForOaProcessTemplates = oaProcessTemplateMapper.selectUserListForOaProcessTemplate(user);
        //找模板已经存在的所有用户表，把已存在的给剔除掉
        List<SysUserForOaProcessTemplate> sysUserForOaProcessTemplateList = oaProcessTemplateMapper.selectTemplateUserListByTemplateId(user, templateId);
        //找模板已经存在的岗位包含的用户表，把岗位的用户加在模板用户集合里面
        List<SysUserForOaProcessTemplate> sysUserForOaProcessTemplateList1 = oaProcessTemplateMapper.selectTemplatePostUserDiffTemplateUser(templateId, sysUserForOaProcessTemplateList);
        sysUserForOaProcessTemplateList.addAll(sysUserForOaProcessTemplateList1);
        //先找到
        List<Long> templateUserIdList = sysUserForOaProcessTemplateList.stream().map(SysUserForOaProcessTemplate::getUserId).collect(Collectors.toList());
        Iterator<SysUserForOaProcessTemplate> iterator = sysUserForOaProcessTemplates.iterator();
        while (iterator.hasNext()) {
            SysUserForOaProcessTemplate sysUserForOaProcessTemplate = iterator.next();
            for (Long userId:templateUserIdList) {
                if (sysUserForOaProcessTemplate.getUserId().equals(userId)) {
                    iterator.remove();
                }
            }
        }


//        for (SysUserForOaProcessTemplate sysUserForOaProcessTemplate:sysUserForOaProcessTemplates) {
//            for (Long userId:templateUserIdList) {
//                if (sysUserForOaProcessTemplate.getUserId().equals(userId)) {
//                    sysUserForOaProcessTemplates.remove(sysUserForOaProcessTemplate);
//                }
//            }
//        }
        return sysUserForOaProcessTemplates;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int flowAddUserListAndNotification(FlowUserAndNotificationDto flowUserAndNotificationDto, LoginUser loginUser) {
        InitiateFlowDto initiateFlowDto = flowUserAndNotificationDto.getUser();
        String procFormDataId1 = initiateFlowDto.getProcFormDataId();
        String userName = loginUser.getUser().getUserName();
        //通知方式入库操作
        List<InitiateFlowNotificationDto> notificationList = flowUserAndNotificationDto.getNotificationList();
        //入库前删除之前的
        int d1 = oaProcessTemplateMapper.deleteFlowNotificationByBusinessId(procFormDataId1);
        Date nowDate = DateUtils.getNowDate();
        for (InitiateFlowNotificationDto initiateFlowNotificationDto:notificationList) {
            initiateFlowNotificationDto.setCreateBy(userName);
            initiateFlowNotificationDto.setUpdateBy(userName);
            initiateFlowNotificationDto.setCreateTime(nowDate);
            initiateFlowNotificationDto.setUpdateTime(nowDate);
            int s = oaProcessTemplateMapper.insertFlowNotification(initiateFlowNotificationDto);
        }



        //用户入库操作
        Long templateId = initiateFlowDto.getTemplateId();
        //找所有模板用户
        SysUser user = new SysUser();
        List<SysUserForOaProcessTemplate> sysUserForOaProcessTemplateList = oaProcessTemplateMapper.selectTemplateUserListByTemplateId(user, templateId);
        //找所有模板的岗位下的用户 - 不包含上面模板里的用户，会剔除掉
//        List<SysUserForOaProcessTemplate> oaProcessTemplates = oaProcessTemplateMapper.selectTemplatePostForUserListByTemplateId(templateId);
        List<SysUserForOaProcessTemplate> oaProcessTemplates1 = oaProcessTemplateMapper.selectTemplatePostUserDiffTemplateUser(templateId, sysUserForOaProcessTemplateList);
        //模板岗位里有的用户，在添加模板用户的时候要剔除掉
//        List<Long> collect = sysUserForOaProcessTemplateList.stream().map(SysUserForOaProcessTemplate::getUserId).collect(Collectors.toList());
//        List<SysUserForOaProcessTemplate> collect1 = oaProcessTemplates.stream().filter(t -> !collect.contains(t.getUserId())).collect(Collectors.toList());
        String procFormDataId = initiateFlowDto.getProcFormDataId();
        List<Long> userIdList = initiateFlowDto.getUserIdList();
        if (userIdList == null) {
            userIdList = new ArrayList<>();
        }
        //删除已经存在的用户，重新入库
        int d2 = oaProcessTemplateMapper.deleteFlowUserByBusinessId1(procFormDataId1);
        int a = 0;
        //流程的用户
        for (Long userId:userIdList) {
            InitiateFlowUserDto initiateFlowUserDto = new InitiateFlowUserDto();
            initiateFlowUserDto.setProcFormDataId(procFormDataId);
            initiateFlowUserDto.setUserId(userId);
            initiateFlowUserDto.setStatus("0");
            initiateFlowUserDto.setUserType("0");
            int i = oaProcessTemplateMapper.insertFlowUser(initiateFlowUserDto);
            if (i > 0) {
                a++;
            }
        }
        //模板岗位的用户
        for (SysUserForOaProcessTemplate sysUserForOaProcessTemplate:oaProcessTemplates1) {
            InitiateFlowUserDto initiateFlowUserDto = new InitiateFlowUserDto();
            initiateFlowUserDto.setProcFormDataId(procFormDataId);
            initiateFlowUserDto.setUserId(sysUserForOaProcessTemplate.getUserId());
            initiateFlowUserDto.setStatus("0");
            initiateFlowUserDto.setUserType("1");
            int i = oaProcessTemplateMapper.insertFlowUser(initiateFlowUserDto);
            if (i > 0) {
                a++;
            }
        }
        //模板用户
        for (SysUserForOaProcessTemplate sysUserForOaProcessTemplate:sysUserForOaProcessTemplateList) {
            InitiateFlowUserDto initiateFlowUserDto = new InitiateFlowUserDto();
            initiateFlowUserDto.setProcFormDataId(procFormDataId);
            initiateFlowUserDto.setUserId(sysUserForOaProcessTemplate.getUserId());
            initiateFlowUserDto.setStatus("0");
            initiateFlowUserDto.setUserType("1");
            int i = oaProcessTemplateMapper.insertFlowUser(initiateFlowUserDto);
            if (i > 0) {
                a++;
            }
        }
        if (a > 0) {
            return a;
        }
        return 1;
    }

    @Override
    public Map<String, Object> flowUserListInfoAndNotificationInfo(String businessId) {
        //找流程可阅览人
        List<FlowUser> flowUserList = oaProcessTemplateMapper.selectUserListForFlowByBusinessId(businessId);
        //找待办通知方式
        List<FlowNotification> flowNotificationList = oaProcessTemplateMapper.selectNotificationForFlowByBusinessId(businessId);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("userList", flowUserList);
        resultMap.put("notificationList", flowNotificationList);
        return resultMap;
    }

    @Override
    public Map<String, Object> getGlobalSettingStatus() {
        Map<String, Object> resultMap = new HashMap<>();
        //查询全局参数设置
        List<OaProcessTemplateGlobalSetting> oaProcessTemplateGlobalSettings = oaProcessTemplateMapper.selectOaProcessTemplateGlobalSetting();
        for (OaProcessTemplateGlobalSetting oaProcessTemplateGlobalSetting:oaProcessTemplateGlobalSettings) {
            if ("0".equals(oaProcessTemplateGlobalSetting.getType())) {
                //类型是审批记录
                if ("1".equals(oaProcessTemplateGlobalSetting.getIsEnable())) {
                    //开启
                    resultMap.put("approveStatusFlag", "0");
                } else {
                    //关闭
                    resultMap.put("approveStatusFlag", "999");
                }
            }
            if ("1".equals(oaProcessTemplateGlobalSetting.getType())) {
                //类型是审批记录
                if ("1".equals(oaProcessTemplateGlobalSetting.getIsEnable())) {
                    //开启
                    resultMap.put("printStatusFlag", "0");
                } else {
                    //关闭
                    resultMap.put("printStatusFlag", "999");
                }
            }
        }
        return resultMap;
    }

    @Override
    public List<FlowPrintHistoryVo> flowPrintHistory(String businessId) {
        return oaProcessTemplateMapper.selectFlowPrintHistoryByBusinessId(businessId);
    }

    @Override
    public int addPrintHistory(String businessId, LoginUser loginUser) {
        FlowPrintHistory flowPrintHistory = new FlowPrintHistory();
        flowPrintHistory.setProcFormDataId(businessId);
        flowPrintHistory.setPrintBy(loginUser.getUser().getNickName());
        flowPrintHistory.setPrintTime(DateUtils.getNowDate());
        return oaProcessTemplateMapper.insertFlowPrintHistory(flowPrintHistory);
    }


    /**
     * 结束任务
     * @param taskId    当前任务ID
     */
    public void endTask(String businessId,String taskId,boolean flag) {
        //  当前任务
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();

        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        List endEventList = bpmnModel.getMainProcess().findFlowElementsOfType(EndEvent.class);
        FlowNode endFlowNode = (FlowNode) endEventList.get(0);
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(task.getTaskDefinitionKey());

        //  临时保存当前活动的原始方向
        List originalSequenceFlowList = new ArrayList<>();
        originalSequenceFlowList.addAll(currentFlowNode.getOutgoingFlows());

        try {
            //  清理活动方向
            currentFlowNode.getOutgoingFlows().clear();

            //  建立新方向
            SequenceFlow newSequenceFlow = new SequenceFlow();
            newSequenceFlow.setId("newSequenceFlowId");
            newSequenceFlow.setSourceFlowElement(currentFlowNode);
            newSequenceFlow.setTargetFlowElement(endFlowNode);
            List newSequenceFlowList = new ArrayList<>();
            newSequenceFlowList.add(newSequenceFlow);
            //  当前节点指向新的方向
            currentFlowNode.setOutgoingFlows(newSequenceFlowList);
            if(flag){
                String pass = procFormMapper.selectLastPassByBusinessKey(businessId);
                if (StringUtils.isNotEmpty(pass) && "6".equals(pass)) {
                    taskService.resolveTask(task.getId());//加签解决任务
                }
            }
            //废弃通过修改节点审批状态为：3-废弃，避免在此节点废弃仍然生成凭证
            Map<String, Object> variables = new LinkedHashMap<>();
            variables.put("approveStatus","3");
            //  完成当前任务
            taskService.complete(task.getId(),variables,true);
        }catch (Exception e){
            throw e;
        }finally {
            //  可以不用恢复原始方向，不影响其它的流程
            currentFlowNode.setOutgoingFlows(originalSequenceFlowList);
        }
    }

    @Override
    public int flowUpdateUserListAndNotification(FlowUserAndNotificationDto flowUserAndNotificationDto, LoginUser loginUser) {
        String userName = loginUser.getUser().getUserName();
        //通知方式入库操作
        List<InitiateFlowNotificationDto> notificationList = flowUserAndNotificationDto.getNotificationList();
        Date nowDate = DateUtils.getNowDate();
        List<FlowNotification> flowNotificationList = oaProcessTemplateMapper.selectNotificationForFlowByBusinessId(flowUserAndNotificationDto.getUser().getProcFormDataId());
        for (FlowNotification flowNotification:flowNotificationList) {
            for (InitiateFlowNotificationDto initiateFlowNotificationDto : notificationList) {
                if (flowNotification.getNotificationType().equals(initiateFlowNotificationDto.getNotificationType())) {
                    initiateFlowNotificationDto.setId(flowNotification.getId());
                }
            }
        }
        for (InitiateFlowNotificationDto initiateFlowNotificationDto : notificationList) {
            initiateFlowNotificationDto.setUpdateBy(userName);
            initiateFlowNotificationDto.setUpdateTime(nowDate);
            int s = oaProcessTemplateMapper.updateNotification(initiateFlowNotificationDto);
        }
        //用户入库操作
        InitiateFlowDto initiateFlowDto = flowUserAndNotificationDto.getUser();
        //删除现在的自定义用户，重新新增自定义用户
        String procFormDataId = initiateFlowDto.getProcFormDataId();
        int d = oaProcessTemplateMapper.deleteFlowUserByBusinessId(procFormDataId);
        List<Long> userIdList = initiateFlowDto.getUserIdList();
        int a = 0;
        for (Long userId:userIdList) {
            InitiateFlowUserDto initiateFlowUserDto = new InitiateFlowUserDto();
            initiateFlowUserDto.setProcFormDataId(procFormDataId);
            initiateFlowUserDto.setUserId(userId);
            initiateFlowUserDto.setStatus("0");
            initiateFlowUserDto.setUserType("0");
            int i = oaProcessTemplateMapper.insertFlowUser(initiateFlowUserDto);
            if (i > 0) {
                a++;
            }
        }
        if (a > 0) {
            return a;
        }
        return 1;
    }

    public List<OaProcessTemplate> queryOaProcessTemplateList(OaProcessTemplate oaProcessTemplate)
    {

        return oaProcessTemplateMapper.selectOaProcessTemplateList(oaProcessTemplate);
    }

    @Override
    public List<OaProcessTemplate> queryTemplByRole(OaProcessTemplate oaProcessTemplate,LoginUser loginUser) {
        Long userId = loginUser.getUserId();
        Long companyId = oaProcessTemplate.getCompanyId();
        //找本角色已经存在的我的流程
        List<MyUsualProcess> myUsualProcessList = oaProcessTemplateMapper.selectMyUsualProcessListByCompanyIdAndUserId(companyId, userId);


        Long classificationId = oaProcessTemplate.getClassificationId();
        List<OaProcessTemplate> resultList = new ArrayList<>();
        //查询自己有权限的公司如果是admin 则查询所有公司
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<Long> roleList = new ArrayList<>();
        boolean b = false;
        for (SysRole role : roles) {
            roleList.add(role.getRoleId());
            if(role.getRoleKey().equals("admin")){
                b = true;
                break;
            }
        }
        if(b){

            //查询所有
            resultList=  this.selectOaProcessTemplateList(oaProcessTemplate);
        }else {
            //查询所有角色的
            resultList=  oaProcessTemplateMapper.queryTemplByRole(null,classificationId,oaProcessTemplate.getTemplateName());
        }
//        //找到我的流程中所有的模板id
//        List<Long> collect = myUsualProcessList.stream().map(MyUsualProcess::getTemplateId).collect(Collectors.toList());
//        //过滤查询出来的流程中包含我的流程的模板
//        resultList = resultList.stream().filter(t -> !collect.contains(t.getId())).collect(Collectors.toList());

        List<OaProcessTemplate> resultLists= new ArrayList<>();
        if(resultList.size()>0){
        for (OaProcessTemplate processTemplate : resultList) {
            //缺少公司id，可能会导致bug，流程入库缺少公司id
            processTemplate.setCompanyId(companyId);
            OaProcessClassification companyData = oaProcessClassificationService.getCompanyData(processTemplate.getClassificationId());
            if(null != companyData){
                if(companyData.getCompanyId().toString().equals(companyId.toString())){
                    resultLists.add(processTemplate);
                }
            }

        }
}


        return resultLists;
    }
    @Override
    public OaProcessTemplate getTemplateByModuleType(OaProcessTemplate oaProcessTemplate){
        return  oaProcessTemplateMapper.getTemplateByModuleType(oaProcessTemplate);
    }

    @Override
    public OaProcessTemplate queryTemplateByOaModuleTypeCompanyId(OaProcessTemplate oaProcessTemplate) {
        return oaProcessTemplateMapper.queryTemplateByOaModuleTypeCompanyId(oaProcessTemplate);
    }

    @Override
    public OaProcessTemplate queryTemplateByProcIdAndCompanyId(OaProcessTemplate oaProcessTemplate) {
        return oaProcessTemplateMapper.selectTemplateByProcIdAndCompanyId(oaProcessTemplate);
    }

    @Override
    public Long selectTemplateIdByParentId(Long parentId) {
        return oaProcessTemplateMapper.selectTemplateIdByParentId(parentId);
    }

    @Override
    public Map<String, Object> getDataByTemplName(OaProcessTemplate oaProcessTemplate, LoginUser loginUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        Long unitId = loginUser.getUser().getUnit().getUnitId();
        if ("1".equals(oaProcessTemplate.getIsEnableCompanyId())) {
            unitId = oaProcessTemplate.getCompanyId();
        }
        OaProcessTemplate oaTemplate =  oaProcessTemplateMapper.getTemplateByName(oaProcessTemplate.getTemplateName(),unitId.toString());
        returnMap.put("companyId",unitId);
        if(null == oaTemplate || null == oaTemplate.getId()){
            returnMap.put("code",268);
        }else {
            returnMap.put("code",200);
            returnMap.put("classificationId",oaTemplate.getClassificationId());
            returnMap.put("templateId",oaTemplate.getId());

        }
        return returnMap;
    }

    @Override
    public List<Map<String, Object>> getCheckTableField(OaFormField oaFormField) {
        return  oaFormFieldMapper.getDataByFormIdAndFieldId(oaFormField.getFormId(),oaFormField.getCheckTableCode());
    }



}
