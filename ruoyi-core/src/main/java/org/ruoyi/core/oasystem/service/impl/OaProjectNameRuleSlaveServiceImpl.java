package org.ruoyi.core.oasystem.service.impl;

import java.util.List;

import org.ruoyi.core.oasystem.domain.OaProjectNameRuleSlave;
import org.ruoyi.core.oasystem.mapper.OaProjectNameRuleSlaveMapper;
import org.ruoyi.core.oasystem.service.IOaProjectNameRuleSlaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 特殊产品分类配置-组合Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class OaProjectNameRuleSlaveServiceImpl implements IOaProjectNameRuleSlaveService
{
    @Autowired
    private OaProjectNameRuleSlaveMapper oaProjectNameRuleSlaveMapper;

    /**
     * 查询特殊产品分类配置-组合
     *
     * @param id 特殊产品分类配置-组合主键
     * @return 特殊产品分类配置-组合
     */
    @Override
    public OaProjectNameRuleSlave selectOaProjectNameRuleSlaveById(Long id)
    {
        return oaProjectNameRuleSlaveMapper.selectOaProjectNameRuleSlaveById(id);
    }

    /**
     * 查询特殊产品分类配置-组合列表
     *
     * @param oaProjectNameRuleSlave 特殊产品分类配置-组合
     * @return 特殊产品分类配置-组合
     */
    @Override
    public List<OaProjectNameRuleSlave> selectOaProjectNameRuleSlaveList(OaProjectNameRuleSlave oaProjectNameRuleSlave)
    {
        return oaProjectNameRuleSlaveMapper.selectOaProjectNameRuleSlaveList(oaProjectNameRuleSlave);
    }

    /**
     * 新增特殊产品分类配置-组合
     *
     * @param oaProjectNameRuleSlave 特殊产品分类配置-组合
     * @return 结果
     */
    @Override
    public int insertOaProjectNameRuleSlave(OaProjectNameRuleSlave oaProjectNameRuleSlave)
    {
        return oaProjectNameRuleSlaveMapper.insertOaProjectNameRuleSlave(oaProjectNameRuleSlave);
    }

    /**
     * 修改特殊产品分类配置-组合
     *
     * @param oaProjectNameRuleSlave 特殊产品分类配置-组合
     * @return 结果
     */
    @Override
    public int updateOaProjectNameRuleSlave(OaProjectNameRuleSlave oaProjectNameRuleSlave)
    {
        return oaProjectNameRuleSlaveMapper.updateOaProjectNameRuleSlave(oaProjectNameRuleSlave);
    }

    /**
     * 批量删除特殊产品分类配置-组合
     *
     * @param ids 需要删除的特殊产品分类配置-组合主键
     * @return 结果
     */
    @Override
    public int deleteOaProjectNameRuleSlaveByIds(Long[] ids)
    {
        return oaProjectNameRuleSlaveMapper.deleteOaProjectNameRuleSlaveByIds(ids);
    }

    /**
     * 删除特殊产品分类配置-组合信息
     *
     * @param id 特殊产品分类配置-组合主键
     * @return 结果
     */
    @Override
    public int deleteOaProjectNameRuleSlaveById(Long id)
    {
        return oaProjectNameRuleSlaveMapper.deleteOaProjectNameRuleSlaveById(id);
    }

    /**
     * 批量新增特殊产品分类配置-组合
     *
     * @param oaProjectNameRuleSlave 特殊产品分类配置-组合
     * @return 结果
     */
    @Override
    public int batchOaProjectNameRuleSlave(List<OaProjectNameRuleSlave> oaProjectNameRuleSlave){
        return oaProjectNameRuleSlaveMapper.batchOaProjectNameRuleSlave(oaProjectNameRuleSlave);
    }

    /**
     * 删除特殊产品分类配置-组合 根据关联规则
     *
     * @param ruleId 特殊产品分类配置-组合主键
     * @return 结果
     */
    @Override
    public int deleteOaProjectNameRuleSlaveByRuleId(Long ruleId){
        return oaProjectNameRuleSlaveMapper.deleteOaProjectNameRuleSlaveByRuleId(ruleId);
    }
}
