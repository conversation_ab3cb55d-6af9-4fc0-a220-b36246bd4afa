package org.ruoyi.core.oasystem.service.impl;

import org.ruoyi.core.oasystem.domain.OaProcessTemplateNotification;
import org.ruoyi.core.oasystem.mapper.OaProcessTemplateNotificationMapper;
import org.ruoyi.core.oasystem.service.IOaProcessTemplateNotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * OA系统-流程模板通知Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
@Service
public class OaProcessTemplateNotificationServiceImpl implements IOaProcessTemplateNotificationService
{
    @Autowired
    private OaProcessTemplateNotificationMapper oaProcessTemplateNotificationMapper;

    /**
     * 查询OA系统-流程模板通知
     * 
     * @param templateId OA系统-流程模板通知主键
     * @return OA系统-流程模板通知
     */
    @Override
    public List<OaProcessTemplateNotification> selectByTemplateId(Long templateId)
    {
        return oaProcessTemplateNotificationMapper.selectByTemplateId(templateId);
    }

    /**
     * 查询OA系统-流程模板通知列表
     * 
     * @param oaProcessTemplateNotificationList OA系统-流程模板通知
     * @return OA系统-流程模板通知
     */
    @Override
    public int batchAddOaProcessTemplateNotification(List<OaProcessTemplateNotification> oaProcessTemplateNotificationList)
    {
        return oaProcessTemplateNotificationMapper.batchAddOaProcessTemplateNotification(oaProcessTemplateNotificationList);
    }


}
