package org.ruoyi.core.oasystem.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 oa_dict_data
 * 
 * <AUTHOR>
 * @date 2023-07-11
 */
public class OaDictData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 字典名称 */
    @Excel(name = "字典名称")
    private String dictName;
    /** 字典值 */
    @Excel(name = "字典值")
    private String dictValue;

    /** 字典类型名称 */
    @Excel(name = "字典类型名称")
    private String dictTypeName;

    /** 字典类型唯一标识 */
    @Excel(name = "字典类型唯一标识")
    private String dictTypeIdent;

    /** 字典类型 */
    @Excel(name = "字典类型")
    private String dictType;

    /** 启用状态 */
    @Excel(name = "启用状态")
    private String isEnable;

    /** 最后修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endUpdateTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDictName(String dictName) 
    {
        this.dictName = dictName;
    }

    public String getDictName() 
    {
        return dictName;
    }
    public void setDictType(String dictType) 
    {
        this.dictType = dictType;
    }

    public String getDictType() 
    {
        return dictType;
    }
    public void setIsEnable(String isEnable) 
    {
        this.isEnable = isEnable;
    }

    public String getIsEnable() 
    {
        return isEnable;
    }
    public void setEndUpdateTime(Date endUpdateTime) 
    {
        this.endUpdateTime = endUpdateTime;
    }

    public Date getEndUpdateTime() 
    {
        return endUpdateTime;
    }
    public void setDictValue(String dictValue)
    {
        this.dictValue = dictValue;
    }

    public String getDictValue()
    {
        return dictValue;
    }
    public void setDictTypeName(String dictTypeName)
    {
        this.dictTypeName = dictTypeName;
    }

    public String getDictTypeName()
    {
        return dictTypeName;
    }
    public void setDictTypeIdent(String dictTypeIdent)
    {
        this.dictTypeIdent = dictTypeIdent;
    }

    public String getDictTypeIdent()
    {
        return dictTypeIdent;
    }
    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("dictName", getDictName())
                .append("dictValue", getDictValue())
                .append("dictTypeName", getDictTypeName())
                .append("dictTypeIdent", getDictTypeIdent())
                .append("dictType", getDictType())
                .append("isEnable", getIsEnable())
                .append("endUpdateTime", getEndUpdateTime())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
