package org.ruoyi.core.oasystem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 oa_project_payer_association
 * 
 * <AUTHOR>
 * @date 2023-07-06
 */
public class OaProjectPayerAssociation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目流程关联表id */
    @Excel(name = "项目流程关联表id")
    private Long pfaId;

    /** 收款人id */
    @Excel(name = "收款人id")
    private Long collId;

    /** 收款人姓名 */
    @Excel(name = "收款人姓名")
    private String collName;

    /** 收款人开户行 */
    @Excel(name = "收款人开户行")
    private String collBankOfDeposit;

    /** 收款人账号 */
    @Excel(name = "收款人账号")
    private String collAccountNumber;

    /** 收款人简称 */
    @Excel(name = "收款人简称")
    private String collAbbreviation;

    /** 付款人id */
    @Excel(name = "付款人id")
    private Long payId;

    /** 付款人名称 */
    @Excel(name = "付款人名称")
    private String payName;

    /** 付款人开户行 */
    @Excel(name = "付款人开户行")
    private String payBankOfDeposit;

    /** 付款人账号 */
    @Excel(name = "付款人账号")
    private String payAccountNumber;

    /** 付款人简称 */
    @Excel(name = "付款人简称")
    private String payAbbreviation;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createBr;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updateBr;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setPfaId(Long pfaId)
    {
        this.pfaId = pfaId;
    }

    public Long getPfaId()
    {
        return pfaId;
    }
    public void setCollId(Long collId)
    {
        this.collId = collId;
    }

    public Long getCollId()
    {
        return collId;
    }
    public void setCollName(String collName)
    {
        this.collName = collName;
    }

    public String getCollName()
    {
        return collName;
    }
    public void setCollBankOfDeposit(String collBankOfDeposit)
    {
        this.collBankOfDeposit = collBankOfDeposit;
    }

    public String getCollBankOfDeposit()
    {
        return collBankOfDeposit;
    }
    public void setCollAccountNumber(String collAccountNumber)
    {
        this.collAccountNumber = collAccountNumber;
    }

    public String getCollAccountNumber()
    {
        return collAccountNumber;
    }
    public void setCollAbbreviation(String collAbbreviation)
    {
        this.collAbbreviation = collAbbreviation;
    }

    public String getCollAbbreviation()
    {
        return collAbbreviation;
    }
    public void setPayId(Long payId)
    {
        this.payId = payId;
    }

    public Long getPayId()
    {
        return payId;
    }
    public void setPayName(String payName)
    {
        this.payName = payName;
    }

    public String getPayName()
    {
        return payName;
    }
    public void setPayBankOfDeposit(String payBankOfDeposit)
    {
        this.payBankOfDeposit = payBankOfDeposit;
    }

    public String getPayBankOfDeposit()
    {
        return payBankOfDeposit;
    }
    public void setPayAccountNumber(String payAccountNumber)
    {
        this.payAccountNumber = payAccountNumber;
    }

    public String getPayAccountNumber()
    {
        return payAccountNumber;
    }
    public void setPayAbbreviation(String payAbbreviation)
    {
        this.payAbbreviation = payAbbreviation;
    }

    public String getPayAbbreviation()
    {
        return payAbbreviation;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setCreateBr(String createBr)
    {
        this.createBr = createBr;
    }

    public String getCreateBr()
    {
        return createBr;
    }
    public void setUpdateBr(String updateBr)
    {
        this.updateBr = updateBr;
    }

    public String getUpdateBr()
    {
        return updateBr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("pfaId", getPfaId())
                .append("collId", getCollId())
                .append("collName", getCollName())
                .append("collBankOfDeposit", getCollBankOfDeposit())
                .append("collAccountNumber", getCollAccountNumber())
                .append("collAbbreviation", getCollAbbreviation())
                .append("payId", getPayId())
                .append("payName", getPayName())
                .append("payBankOfDeposit", getPayBankOfDeposit())
                .append("payAccountNumber", getPayAccountNumber())
                .append("payAbbreviation", getPayAbbreviation())
                .append("status", getStatus())
                .append("createBr", getCreateBr())
                .append("createTime", getCreateTime())
                .append("updateBr", getUpdateBr())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}