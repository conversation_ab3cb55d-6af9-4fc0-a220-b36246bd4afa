package org.ruoyi.core.oasystem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.common.enums.OaProjectDeployReceiptAndPaymentInfoEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.CompanyTypeMapping;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISysOperLogService;
import org.ruoyi.core.cwproject.domain.CwProject;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.mapper.CwProjectMapper;
import org.ruoyi.core.cwproject.mapper.TopNotifyMapper;
import org.ruoyi.core.domain.DProjectParameter;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.domain.bo.OaProjectDeployBo;
import org.ruoyi.core.oasystem.domain.dto.ExcelImportOfProjectDeployInfoDto;
import org.ruoyi.core.oasystem.domain.dto.ExcelImportOfReceiptAndPaymentInfoDto;
import org.ruoyi.core.oasystem.domain.dto.OaProjectDeployDto;
import org.ruoyi.core.oasystem.domain.vo.*;
import org.ruoyi.core.oasystem.mapper.*;
import org.ruoyi.core.oasystem.service.IOaProjectDeployService;
import org.ruoyi.core.oasystem.util.ExcelImportOfProjectDeployInfo;
import org.ruoyi.core.oasystem.util.ExcelImportOfReceiptAndPaymentInfo;
import org.ruoyi.core.service.IDProjectParameterService;
import org.ruoyi.core.service.impl.SysSelectDataRefServiceImpl;
import org.ruoyi.core.xmglproject.constant.XmglProjectEnum;
import org.ruoyi.core.xmglproject.domain.XmglAddTemporarily;
import org.ruoyi.core.xmglproject.domain.XmglDeployProject;
import org.ruoyi.core.xmglproject.domain.XmglProject;
import org.ruoyi.core.xmglproject.mapper.XmglProjectDeployMapper;
import org.ruoyi.core.xmglproject.mapper.XmglProjectMapper;
import org.ruoyi.core.xmglproject.mapper.XmglProjectUserMapper;
import org.ruoyi.core.xmglproject.mapper.XmglRelevanceMapper;
import org.ruoyi.core.xmglproject.service.IXmgAddTemporarilyService;
import org.ruoyi.core.xmglproject.service.IXmglProjectService;
import org.ruoyi.core.xmglproject.service.impl.XmglProjectDeployServiceImpl;
import org.ruoyi.core.xmglproject.service.impl.XmglRelevanceServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.ruoyi.core.oasystem.service.impl.OaProjectNameRuleServiceImpl.getDataNameWithParents;


/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Service
public class OaProjectDeployServiceImpl implements IOaProjectDeployService
{
    @Autowired
    private OaProjectDeployMapper oaProjectDeployMapper;

    @Autowired
    private CwProjectMapper cwProjectMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private TopNotifyMapper topNotifyMapper;

    @Autowired
    private OaEditApproveGeneralityUserMapper oaEditApproveGeneralityUserMapper;

    @Autowired
    private OaEditApproveGeneralityEditRecordsMapper oaEditApproveGeneralityEditRecordsMapper;

    @Autowired
    private OaEditApproveGeneralityRecordsMapper oaEditApproveGeneralityRecordsMapper;

    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysPostMapper sysPostMapper;

    @Autowired
    private ProjectTypeRelevanceMapper projectTypeRelevanceMapper;
    @Autowired
    private IDProjectParameterService projectParameterService;

    @Autowired
    private ProjectCompanyRelevanceMapper projectCompanyRelevanceMapper;
    private static final String currentOaApplyType = "4";

    @Autowired
    private ISysOperLogService sysOperLogService;

    @Autowired
    private IXmglProjectService xmglProjectService;

    @Autowired
    private XmglRelevanceServiceImpl xmglRelevanceServiceImpl;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;

    @Autowired
    private XmglProjectUserMapper xmglProjectUserMapper;

    @Autowired
    private XmglProjectDeployServiceImpl xmglProjectDeployServiceImpl;

    @Autowired
    private ProjectChannelUserMapper projectChannelUserMapper;

    @Autowired
    private IXmgAddTemporarilyService xmgAddTemporarilyService;

    @Resource
    private XmglRelevanceMapper xmglRelevanceMapper;

    @Resource
    private XmglProjectMapper xmglProjectMapper;

    @Autowired
    private SysSelectDataRefServiceImpl sysSelectDataRefService;

    @Autowired
    private XmglProjectDeployMapper xmglProjectDeployMapper;

    @Autowired
    private SysCompanyMapper sysCompanyMapper;

    @Autowired
    private CompanyTypeMappingMapper companyTypeMappingMapper;
    @Autowired
    private OaDataManageMapper oaDataManageService;

    @Autowired
    private OaTraderMapper oaTraderMapper;
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public OaProjectDeployBo selectOaProjectDeployById(Long id)
    {
        String oaApplyType = "4";
        OaProjectDeploy oaProjectDeploy = oaProjectDeployMapper.selectDataById(id);
        OaProjectDeployBo oaProjectDeployBo = new OaProjectDeployBo();
        BeanUtil.copyProperties(oaProjectDeploy, oaProjectDeployBo);
        //组装财务责任人和业务责任人
        //项目类型
//        List<ProjectTypeRelevance> projectTypeData  =   projectTypeRelevanceMapper.queryProjectObject("0",oaProjectDeploy.getId());
//        oaProjectDeployBo.setProjectTypeList(projectTypeData);
        //业务类型 改 产品类型
        //List<ProjectTypeRelevance> businessTypeData =  projectTypeRelevanceMapper.queryProjectObject("1",oaProjectDeploy.getId());
        List<ProjectTypeRelevance> typeByProjectId = projectTypeRelevanceMapper.getTypeByProjectId(oaProjectDeploy.getId());
        List<ProjectTypeRelevance> businessTypeData = typeByProjectId.stream().filter(vo -> "1".equals(vo.getDataType())).collect(Collectors.toList());
        //拼接前端需要的一二级typeName
        if(businessTypeData != null && !businessTypeData.isEmpty()){
            Map<Long,OaDataManage> businessType = oaDataManageService.selectDataManageListByCode("business_type").stream()
                    .filter(vo -> vo.getParentId() != null) //过滤最上一级节点
                    .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
            for (ProjectTypeRelevance businessTypeRe : businessTypeData) {
                String dataNameWithParents = getDataNameWithParents(businessTypeRe.getTypeId(), businessType);
                if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                    dataNameWithParents = dataNameWithParents.substring(1);
                }
                businessTypeRe.setTypeName(dataNameWithParents);
            }
        }
        oaProjectDeployBo.setBusinessTypeList(businessTypeData);

        List<ProjectTypeRelevance> projectTypeData = typeByProjectId.stream().filter(vo -> "0".equals(vo.getDataType())).collect(Collectors.toList());
        if(projectTypeData != null && !projectTypeData.isEmpty()){
            Map<Long,OaDataManage> businessType = oaDataManageService.selectDataManageListByCode("project_type").stream()
                    .filter(vo -> vo.getParentId() != null) //过滤最上一级节点
                    .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
            for (ProjectTypeRelevance businessTypeRe : projectTypeData) {
                String dataNameWithParents = getDataNameWithParents(businessTypeRe.getTypeId(), businessType);
                if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                    dataNameWithParents = dataNameWithParents.substring(1);
                }
                businessTypeRe.setTypeName(dataNameWithParents);
            }
        }
        oaProjectDeployBo.setProjectTypeList(projectTypeData);
        //业务负责人
        List<OaEditApproveGeneralityUser> yewuList = oaEditApproveGeneralityUserMapper.queryDataObjectByOaApplyTypeAndOaApplyId(currentOaApplyType, oaProjectDeploy.getOaApplyId(), "1");
        oaProjectDeployBo.setYewuList(yewuList);

        //脏数据处理 筛选运营岗位用户带入到新增立项项目的项目负责人 add by niey
        List<SysUser> userList = xmglProjectService.dirtyDataProcessing(yewuList);
        oaProjectDeployBo.setOperateUserList(userList);

        //财务负责人
        List<OaEditApproveGeneralityUser> caiwuList = oaEditApproveGeneralityUserMapper.queryDataObjectByOaApplyTypeAndOaApplyId(currentOaApplyType, oaProjectDeploy.getOaApplyId(), "0");
        oaProjectDeployBo.setCaiwuList(caiwuList);

//        //担保公司
//        List<ProjectCompanyRelevance> custList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("0", id);
//        oaProjectDeployBo.setCustList(custList);
//        //资产方
//        List<ProjectCompanyRelevance> partnerList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("1", id);
//        oaProjectDeployBo.setPartnerList(partnerList);
//        //资金方
//        List<ProjectCompanyRelevance> fundList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("2", id);
//        oaProjectDeployBo.setFundList(fundList);
        //其他
        List<ProjectCompanyRelevance> otherUnitList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("3", id);
        oaProjectDeployBo.setOtherUnitList(otherUnitList);

        Map<String, List<ProjectCompanyRelevance>> tableList = projectCompanyRelevanceMapper.queryDataObjectByProjectId(id).stream()
                .filter(pr -> !pr.getUnitType().equals("3"))
                .collect(Collectors.groupingBy(pr -> pr.getUnitType() + "List"));
        if (!tableList.isEmpty()){
            tableList = this.repliceTpyeMap(tableList);
        }
        oaProjectDeployBo.setTableList(tableList);

        //渠道方
        List<ProjectChannelUser> qudaoList = projectChannelUserMapper.queryDataByProjectId(id);
        oaProjectDeployBo.setQudaofangList(qudaoList);
        //目前仅有财务项目管理模块使用的项目名称是从这里来的 所以先只查询这个
        List<Map<String,Object>> modelList = new ArrayList<>();
        HashMap<String, Object> cwProject = new HashMap<>();
        cwProject.put("model","财务项目管理");
        int numByprojectId = cwProjectMapper.getNumByprojectId(id.toString());
        if(numByprojectId>0){
            cwProject.put("isUse","true");
        }else {
            cwProject.put("isUse","false");
        }
        HashMap<String, Object> cdProject = new HashMap<>();
        cdProject.put("model","车抵贷绿本出入库");
        cdProject.put("isUse","false");
        HashMap<String, Object> xmlxProject = new HashMap<>();
        xmlxProject.put("model","项目立项管理");
        //add by niey 查询当前项目是否跟立项项目有关联
        XmglDeployProject deployProject = xmglProjectDeployMapper.selectXmglProjectDeployByDeployId(id);
        if (!Objects.isNull(deployProject)){
            xmlxProject.put("isUse","true");
        }else {
            xmlxProject.put("isUse","false");
        }

        modelList.add(cdProject);
        modelList.add(xmlxProject);

        modelList.add(cwProject);
        oaProjectDeployBo.setFeatureModelList(modelList);
//        List<OaEditApproveGeneralityUser> oaEditApproveGeneralityUsers = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(currentOaApplyType, id);
//        List<Long> salesmanList = oaEditApproveGeneralityUsers.stream().filter(t -> "0".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserId).collect(Collectors.toList());
//        List<Long> financialStaffList = oaEditApproveGeneralityUsers.stream().filter(t -> "1".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserId).collect(Collectors.toList());
//        oaProjectDeployBo.setSalesmanList(salesmanList);
//        oaProjectDeployBo.setFinancialStaffList(financialStaffList);
        //组装修改前JSON对应的id
        //找最新的、已经知悉的审批记录表（未知悉进不来修改页面，放心处理）
        PageHelper.clearPage();
//        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, id, "1");
//        //查出来的最新的记录表修改后id是要修改的之前的id
//        if (oaEditApproveGeneralityEditRecords != null) {
//            oaProjectDeployBo.setOaProjectDeployBoOldDataId(oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId());
//        } else {
//            oaProjectDeployBo.setOaProjectDeployBoOldDataId(null);
//        }
//        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaProjectDeploy.getId(), "0");
//        if (oaEditApproveGeneralityEditRecords1 == null) {
//            PageHelper.clearPage();
//            oaEditApproveGeneralityEditRecords1 = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyIdAndOaNotifyStepThree(oaApplyType, oaProjectDeploy.getId(), "0");
//        }
//        if (oaEditApproveGeneralityEditRecords1 != null) {
//            oaProjectDeployBo.setCheckStatus(oaEditApproveGeneralityEditRecords1.getCheckStatus());
//        }
        return oaProjectDeployBo;
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param oaProjectDeploy 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<OaProjectDeployVo1> selectOaProjectDeployList(OaProjectDeploy oaProjectDeploy,LoginUser loginUser)
    {
        List<OaProjectDeploy> oaProjectDeploys = new ArrayList<>();
        if(null == oaProjectDeploy.getSelectType() || oaProjectDeploy.getSelectType().equals("0")){
            oaProjectDeploy.setSelectType("0");
        }
            //为了避免接口在别的地方应用进行新增接口
//            List<OaProjectDeploy> oaProjectDeploys = oaProjectDeployMapper.selectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdList(oaProjectDeploy, companyIdList, filterOaApplyId);
        PageUtil.startPage();
        if(oaProjectDeploy.getSelectType().equals("0")){
            oaProjectDeploys = oaProjectDeployMapper.newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdList(oaProjectDeploy);
        }else {
            oaProjectDeploy.setUserid(loginUser.getUserId());
            oaProjectDeploys =  oaProjectDeployMapper.newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdListAndUser(oaProjectDeploy);
        }

            List<OaProjectDeployVo1> oaProjectDeployVo1s = this.getOaProjectDeployVoListByOaProjectDeployList(oaProjectDeploys);

            List<Long> projectIds = oaProjectDeploys.stream().map(OaProjectDeploy::getId).collect(Collectors.toList());
            List<ProjectTypeRelevance> typeByProjectIds=new ArrayList<>();
            if (!projectIds.isEmpty()) {
                typeByProjectIds = projectTypeRelevanceMapper.getTypeByProjectIds(projectIds);
            }
            //业务类型 改 产品类型  查询项目的产品类型
            Map<Long, List<ProjectTypeRelevance>> businessTypeMap = typeByProjectIds.stream().filter(vo -> "1".equals(vo.getDataType()))
                .collect(Collectors.groupingBy(ProjectTypeRelevance::getProjectId));
            //查询项目的项目类型
            Map<Long, List<ProjectTypeRelevance>> projectTypeMap = typeByProjectIds.stream().filter(vo -> "0".equals(vo.getDataType()))
                .collect(Collectors.groupingBy(ProjectTypeRelevance::getProjectId));
            //查询数据集 组合一级二级名称
            Map<Long,OaDataManage> businessType = oaDataManageService.selectDataManageListByCode("business_type").stream()
                .filter(vo -> vo.getParentId() != null) //过滤最上一级节点
                .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
            Map<Long,OaDataManage> projectType = oaDataManageService.selectDataManageListByCode("project_type").stream()
                .filter(vo -> vo.getParentId() != null) //过滤最上一级节点
                .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));

            for (OaProjectDeployVo1 deployVo1 : oaProjectDeployVo1s) {
                List<ProjectTypeRelevance> businessTypeData = businessTypeMap.get(deployVo1.getId());
                if(businessTypeData != null && !businessTypeData.isEmpty()){
                    for (ProjectTypeRelevance businessTypeRe : businessTypeData) {
                        String dataNameWithParents = getDataNameWithParents(businessTypeRe.getTypeId(), businessType);
                        if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                            dataNameWithParents = dataNameWithParents.substring(1);
                        }
                        businessTypeRe.setTypeName(dataNameWithParents);
                    }
                }
                deployVo1.setBusinessTypeList(businessTypeData);

                List<ProjectTypeRelevance> projectTypeData = projectTypeMap.get(deployVo1.getId());
                if(projectTypeData != null && !projectTypeData.isEmpty()){
                    for (ProjectTypeRelevance projectTypeRe : projectTypeData) {
                        String dataNameWithParents = getDataNameWithParents(projectTypeRe.getTypeId(), projectType);
                        if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                            dataNameWithParents = dataNameWithParents.substring(1);
                        }
                        projectTypeRe.setTypeName(dataNameWithParents);
                    }
                }
                deployVo1.setProjectTypeList(projectTypeData);
            }
        return oaProjectDeployVo1s;
//        //老查询接口为了避免出现问题 所以重写 2024-04-18 wangzeyu
////        List<OaProjectDeploy> oaProjectDeploys = oaProjectDeployMapper.selectOaProjectDeployList(oaProjectDeploy);
//        List<OaProjectDeploy> oaProjectDeploys = oaProjectDeployMapper.newSelectOaProjectDeployList(oaProjectDeploy);
//        List<OaProjectDeployVo1> oaProjectDeployVo1s = this.getOaProjectDeployVoListByOaProjectDeployList(oaProjectDeploys, selectType);
    }


    @Override
    public Long getProjectTotal(OaProjectDeploy oaProjectDeploy, LoginUser loginUser) {
        Long total = null;
        oaProjectDeploy.setPageNum(null);
        oaProjectDeploy.setPageSize(null);
        if(null == oaProjectDeploy.getSelectType() || oaProjectDeploy.getSelectType().equals("0")){
            oaProjectDeploy.setSelectType("0");
        }
        //为了避免接口在别的地方应用进行新增接口
//            List<OaProjectDeploy> oaProjectDeploys = oaProjectDeployMapper.selectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdList(oaProjectDeploy, companyIdList, filterOaApplyId);
        if(oaProjectDeploy.getSelectType().equals("0")){
            total = oaProjectDeployMapper.newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdListTotal(oaProjectDeploy);
        }else {
            total =  oaProjectDeployMapper.newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdListAndUserTotal(oaProjectDeploy,loginUser.getUserId());
        }
        return total;
    }

    @Override
    public OaProjectDeployReceiptAndPaymentInfoVo1 selectReceiptAndPaymentInfoByOaProjectDeployId(Long id) {
        List<OaProjectDeployReceiptAndPaymentInfo> oaProjectDeployReceiptAndPaymentInfoList = oaProjectDeployMapper.selectReceiptAndPaymentInfoByOaProjectDeployId(id);
        OaProjectDeployReceiptAndPaymentInfoVo1 oaProjectDeployReceiptAndPaymentInfoVo1 = new OaProjectDeployReceiptAndPaymentInfoVo1();
        if (oaProjectDeployReceiptAndPaymentInfoList.size() > 0) {
            oaProjectDeployReceiptAndPaymentInfoVo1.setEditFlag("1");
        } else {
            oaProjectDeployReceiptAndPaymentInfoVo1.setEditFlag("0");
        }
        //获取从库里拿到的 事项
        List<String> itemNameList = oaProjectDeployReceiptAndPaymentInfoList.stream().map(OaProjectDeployReceiptAndPaymentInfo::getItemName).distinct().collect(Collectors.toList());
        //枚举里的 事项
        List<Map<String, Object>> list = OaProjectDeployReceiptAndPaymentInfoEnum.getList();
        List<String> codeList = list.stream().map(t -> t.get("code").toString()).collect(Collectors.toList());
        //找两个 事项 的差
        if (codeList.size() > itemNameList.size()) {
            List<String> collect = codeList.stream().filter(t -> !itemNameList.contains(t)).collect(Collectors.toList());
            for (String s : collect) {
                OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo = new OaProjectDeployReceiptAndPaymentInfo();
                oaProjectDeployReceiptAndPaymentInfo.setOaProjectDeployId(id);
                oaProjectDeployReceiptAndPaymentInfo.setReceiptAndPaymentType(OaProjectDeployReceiptAndPaymentInfoEnum.getReceiptAndPaymentTypeByCode(s));
                oaProjectDeployReceiptAndPaymentInfo.setItemName(s);
                oaProjectDeployReceiptAndPaymentInfo.setSerialNum(1);
                oaProjectDeployReceiptAndPaymentInfoList.add(oaProjectDeployReceiptAndPaymentInfo);
            }
        }
        //对于要处理的那个id进行组装
        List<Long> oldIdCountList = new ArrayList<>();
        //对查询到数据进行处理
        //首先对receiptAndPaymentType进行分组
        Map<String, List<OaProjectDeployReceiptAndPaymentInfo>> collect = oaProjectDeployReceiptAndPaymentInfoList.stream().collect(Collectors.groupingBy(OaProjectDeployReceiptAndPaymentInfo::getReceiptAndPaymentType));
        //对好分组的数据进行处理
        List<OaProjectDeployReceiptAndPaymentInfoVo> oaProjectDeployReceiptAndPaymentInfoVos = new ArrayList<>();
        collect.forEach((receiptAndPaymentType, oaProjectDeployReceiptAndPaymentInfoList1) -> {
            OaProjectDeployReceiptAndPaymentInfoVo oaProjectDeployReceiptAndPaymentInfoVo = new OaProjectDeployReceiptAndPaymentInfoVo();
            oaProjectDeployReceiptAndPaymentInfoVo.setReceiptAndPaymentType(receiptAndPaymentType);
            oaProjectDeployReceiptAndPaymentInfoVo.setOaProjectDeployId(id);
            List<Long> oldIdList = oaProjectDeployReceiptAndPaymentInfoList1.stream().filter(t -> t.getId() != null).map(OaProjectDeployReceiptAndPaymentInfo::getId).collect(Collectors.toList());
            oaProjectDeployReceiptAndPaymentInfoVo.setOldIdList(oldIdList);
            oldIdCountList.addAll(oldIdList);
            //创建返回对象最大的Map
            Map<String, Object> maximumMap = new HashMap<>();
//            List<Map<String, Object>> maximumMapList = new ArrayList<>();
            List<Map<String, Object>> secondMapList = new ArrayList<>();
            //然后再对分好组的数据再次进行精确分组，分组字段为itemName
            Map<String, List<OaProjectDeployReceiptAndPaymentInfo>> collect1 = oaProjectDeployReceiptAndPaymentInfoList1.stream().collect(Collectors.groupingBy(OaProjectDeployReceiptAndPaymentInfo::getItemName));
            //对再次分组好的数据进行处理
            collect1.forEach((itemName, oaProjectDeployReceiptAndPaymentInfoList2) -> {
//                maximumMap.put("itemName", itemName);
                Map<String, Object> secondMap = new HashMap<>();
                secondMap.put("itemName", itemName);
                secondMap.put("remark", oaProjectDeployReceiptAndPaymentInfoList2.get(0).getRemark());
                Integer orderByCode = OaProjectDeployReceiptAndPaymentInfoEnum.getOrderByCode(itemName);
                secondMap.put("orderByCode", orderByCode);
                List<Map<String, Object>> thirdMapList = new ArrayList<>();
                //然后再对分好组的数据再次进行精确分组，分组字段为serialNum
                Map<Integer, List<OaProjectDeployReceiptAndPaymentInfo>> collect2 = oaProjectDeployReceiptAndPaymentInfoList2.stream().collect(Collectors.groupingBy(OaProjectDeployReceiptAndPaymentInfo::getSerialNum));
                //对再次分组好的数据进行处理
                collect2.forEach((serialNum, oaProjectDeployReceiptAndPaymentInfoList3) -> {
//                    secondMap.put("serialNum", serialNum);
                    Map<String, Object> thirdMap = new HashMap<>();
                    List<Map<String, Object>> fourthMapList = new ArrayList<>();
                    thirdMap.put("serialNum", serialNum);
                    for (OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo : oaProjectDeployReceiptAndPaymentInfoList3) {
                            Map<String, Object> fourthMap = new HashMap<>();
                            fourthMap.put("id", oaProjectDeployReceiptAndPaymentInfo.getId());
                            fourthMap.put("traderType", oaProjectDeployReceiptAndPaymentInfo.getTraderType());
                            fourthMap.put("inputType", oaProjectDeployReceiptAndPaymentInfo.getInputType());
                            fourthMap.put("oaTraderId", oaProjectDeployReceiptAndPaymentInfo.getOaTraderId());
                            fourthMap.put("accountName", oaProjectDeployReceiptAndPaymentInfo.getAccountName());
                            fourthMap.put("accountNumber", oaProjectDeployReceiptAndPaymentInfo.getAccountNumber());
                            fourthMap.put("bankOfDeposit", oaProjectDeployReceiptAndPaymentInfo.getBankOfDeposit());
                            fourthMap.put("abbreviation", oaProjectDeployReceiptAndPaymentInfo.getAbbreviation());
                            fourthMap.put("accountIdOfName", oaProjectDeployReceiptAndPaymentInfo.getAccountIdOfName());
                            fourthMap.put("status", oaProjectDeployReceiptAndPaymentInfo.getStatus());
                            fourthMapList.add(fourthMap);
                    }
                    thirdMap.put("orderBySerialNum", fourthMapList);
                    thirdMapList.add(thirdMap);
                });
                secondMap.put("orderByItemName", thirdMapList);
                secondMapList.add(secondMap);
                //进行排序，让对应的顺序排一下
                Collections.sort(secondMapList, (o1, o2) -> {
                    Integer o1Value = Integer.valueOf(o1.get("orderByCode").toString());
                    Integer o2Value = Integer.valueOf(o2.get("orderByCode").toString());
                    return o1Value.compareTo(o2Value);
                });
            });
            maximumMap.put("orderByrRceiptAndPaymentType", secondMapList);
            oaProjectDeployReceiptAndPaymentInfoVo.setOaProjectDeployReceiptAndPaymentInfo(maximumMap);
            oaProjectDeployReceiptAndPaymentInfoVos.add(oaProjectDeployReceiptAndPaymentInfoVo);
        });
        if (oaProjectDeployReceiptAndPaymentInfoVos.size() != 0) {
            oaProjectDeployReceiptAndPaymentInfoVo1.setOaProjectDeployReceiptAndPaymentInfoVoList(oaProjectDeployReceiptAndPaymentInfoVos);
        }
        //todo 暂时注释
//        String collect1 = oldIdCountList.stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
//        oaProjectDeployReceiptAndPaymentInfoVo1.setOldIdListStr(collect1);
//        return oaProjectDeployReceiptAndPaymentInfoVos;
//        insertReceiptAndPaymentInfo(oaProjectDeployReceiptAndPaymentInfoVo1, SecurityUtils.getLoginUser());
        return oaProjectDeployReceiptAndPaymentInfoVo1;
    }

    @Override
    public OaProjectDeployCwProjectFeeInfoVo selectCwProjectFeeInfoByOaProjectDeployId(Long id) {
        OaProjectDeploy oaProjectDeploy = oaProjectDeployMapper.selectOaProjectDeployById(id);
        //根据项目id，找对应的信息费信息
        List<OaProjectDeployCwProjectFeeInfo> oaProjectDeployCwProjectFeeInfoList = oaProjectDeployMapper.selectCwProjectFeeInfoByOaProjectDeployId(id);
        OaProjectDeployCwProjectFeeInfoVo oaProjectDeployCwProjectFeeInfoVo = new OaProjectDeployCwProjectFeeInfoVo();
        oaProjectDeployCwProjectFeeInfoVo.setOaProjectDeployCwProjectFeeInfoList(oaProjectDeployCwProjectFeeInfoList);
        oaProjectDeployCwProjectFeeInfoVo.setCwProjectFeeFlag(oaProjectDeploy.getCwProjectFeeFlag());
        oaProjectDeployCwProjectFeeInfoVo.setOldIdList(oaProjectDeployCwProjectFeeInfoList.stream().map(OaProjectDeployCwProjectFeeInfo::getId).collect(Collectors.toList()));
        return oaProjectDeployCwProjectFeeInfoVo;
    }

    /**
     * Excel导入
     *
     * @param file
     * @return 暂无
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importExcel(MultipartFile file, LoginUser loginUser) throws Exception {
        String nickName = loginUser.getUser().getNickName();
        String userName = loginUser.getUsername();
        Long userId = loginUser.getUserId();
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("company_type");
        sysDictData.setAuxiliaryField("外部");
        sysDictData.setDictLabel("信息服务方");
        List<SysDictData> projectTypeInfo = sysDictDataMapper.selectDictDataList(sysDictData);
        SysDictData sysDictData1 = projectTypeInfo.get(0);
        Long dictCode = sysDictData1.getDictCode();
        //获取导入的excel中公司的信息
        ExcelImportOfProjectDeployInfo excelImportOfProjectDeployInfo = new ExcelImportOfProjectDeployInfo();
        excelImportOfProjectDeployInfo.init(file.getInputStream());
        List<ExcelImportOfProjectDeployInfoDto> excelImportOfProjectDeployInfoDtoList = excelImportOfProjectDeployInfo.importExcelHandle();
        //通过获取的对象，进行数据的处理
        //首先查看公司是否都有，如果有，那么去新增一个公司类型为 信息服务方 的类型
        //如果没有，那么把对应的公司新增 新增一个 信息服务方 的类型
        List<String> companyNameList = excelImportOfProjectDeployInfoDtoList.stream().map(ExcelImportOfProjectDeployInfoDto::getCompanyName).distinct().collect(Collectors.toList());
        List<SysCompany> sysCompanyList = sysCompanyMapper.selectSysCompanyListByCompanyNameList(companyNameList);
        //找到名字以后，进行比对查出来的名字跟导入的名字有没有差别
        List<String> queryCompanyNameList = sysCompanyList.stream().map(SysCompany::getCompanyName).collect(Collectors.toList());
        Set<String> queryCompanyNameSet = new HashSet<>(queryCompanyNameList);
        //导入的比查到的多出来以下这些，说明要进行新的公司入库。
        List<String> differenceCompanyNameList = companyNameList.stream().filter(t -> !queryCompanyNameSet.contains(t)).collect(Collectors.toList());
        Date nowDate = DateUtils.getNowDate();
        String userNameString = "[" + nickName + "]通过excel导入的数据";
        if (differenceCompanyNameList.size() > 0) {
            //进行新的入库处理
            for (String newCompanyName : differenceCompanyNameList) {
                ExcelImportOfProjectDeployInfoDto excelImportOfProjectDeployInfoDto = excelImportOfProjectDeployInfoDtoList.stream().filter(t -> newCompanyName.equals(t.getCompanyName())).findFirst().orElse(null);
                if (excelImportOfProjectDeployInfoDto != null) {
                    String newCompanyShortName = excelImportOfProjectDeployInfoDto.getCompanyShortName();
                    SysCompanyVo sysCompanyVo = new SysCompanyVo();
                    sysCompanyVo.setCompanyName(newCompanyName);
                    sysCompanyVo.setCompanyShortName(newCompanyShortName);
                    sysCompanyVo.setIsInside("0");
                    sysCompanyVo.setStatus("0");
                    sysCompanyVo.setSource("1");
                    sysCompanyVo.setCheckStatus("3");
                    sysCompanyVo.setCreateBy(userName);
                    sysCompanyVo.setCreateTime(nowDate);
                    sysCompanyVo.setUpdateBy(userName);
                    sysCompanyVo.setUpdateTime(nowDate);
                    sysCompanyVo.setIsDelete("0");
                    int i = sysCompanyMapper.insertSysCompany(sysCompanyVo);
                    Long companyId = sysCompanyVo.getId();
                    CompanyTypeMapping companyTypeMapping = new CompanyTypeMapping();
                    companyTypeMapping.setCompanyId(companyId);
                    companyTypeMapping.setCompanyTypeCode(dictCode);
                    companyTypeMapping.setCreateBy(userName);
                    companyTypeMapping.setCreateTime(nowDate);
                    companyTypeMapping.setUpdateBy(userName);
                    companyTypeMapping.setUpdateTime(nowDate);
                    int i1 = companyTypeMappingMapper.insertCompanyTypeMapping(companyTypeMapping);
                }
            }
        }
        //处理完新增的，处理之前已经存在于库里的公司，新增 信息服务方 的类型
        for (SysCompany sysCompany : sysCompanyList) {
            Long companyId = sysCompany.getId();
            CompanyTypeMapping companyTypeMapping = new CompanyTypeMapping();
            companyTypeMapping.setCompanyId(companyId);
            companyTypeMapping.setCompanyTypeCode(dictCode);
            companyTypeMapping.setCreateBy(userName);
            companyTypeMapping.setCreateTime(nowDate);
            companyTypeMapping.setUpdateBy(userName);
            companyTypeMapping.setUpdateTime(nowDate);
            int i1 = companyTypeMappingMapper.insertCompanyTypeMapping(companyTypeMapping);
        }
        //处理完公司以后，开始处理项目信息 - 信息费信息
        //先对导入的项目名称进行分组
        Map<String, List<ExcelImportOfProjectDeployInfoDto>> collect = excelImportOfProjectDeployInfoDtoList.stream().collect(Collectors.groupingBy(ExcelImportOfProjectDeployInfoDto::getProjectName));
        collect.forEach((projectName, list) -> {
            //先去查对应的项目
            OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
            oaProjectDeploy.setProjectName(projectName);
            List<OaProjectDeploy> oaProjectDeploys = oaProjectDeployMapper.newSelectOaProjectDeployList(oaProjectDeploy);
            if (oaProjectDeploys.size() == 0) {
                throw new RuntimeException("请检查 项目信息 中是否包含：[" + projectName + "]项目");
            } else {
                OaProjectDeploy oaProjectDeploy1 = oaProjectDeploys.stream().filter(t -> projectName.equals(t.getProjectName())).findFirst().orElse(null);
                oaProjectDeploy1.setCwProjectFeeFlag("1");
                oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy1);
                //更新完之后，然后进行 项目信息-返费公司信息表 信息补充
                List<ExcelImportOfProjectDeployInfoDto> collect1 = excelImportOfProjectDeployInfoDtoList.stream().filter(t -> projectName.equals(t.getProjectName())).collect(Collectors.toList());
                if (collect1.size() > 0) {
                    for (int i = 0; i < collect1.size(); i++) {
                        ExcelImportOfProjectDeployInfoDto excelImportOfProjectDeployInfoDto = collect1.get(i);
                        String companyName = excelImportOfProjectDeployInfoDto.getCompanyName();
                        String accountNumber = excelImportOfProjectDeployInfoDto.getAccountNumber();
                        String bankOfDeposit = excelImportOfProjectDeployInfoDto.getBankOfDeposit();
                        BigDecimal rate = excelImportOfProjectDeployInfoDto.getRate();
                        BigDecimal taxRate = excelImportOfProjectDeployInfoDto.getTaxRate();
                        SysCompany sysCompany = new SysCompany();
                        sysCompany.setCompanyName(companyName);
                        List<SysCompany> sysCompanyList1 = sysCompanyMapper.selectSysCompanyListByCompanyName(sysCompany);
                        SysCompany sysCompany1 = sysCompanyList1.get(0);
                        Long companyId = sysCompany1.getId();
                        OaProjectDeployCwProjectFeeInfo oaProjectDeployCwProjectFeeInfo = new OaProjectDeployCwProjectFeeInfo();
                        oaProjectDeployCwProjectFeeInfo.setOaProjectDeployId(oaProjectDeploy1.getId());
                        oaProjectDeployCwProjectFeeInfo.setCompanyId(companyId);
                        oaProjectDeployCwProjectFeeInfo.setAccountNumber(accountNumber);
                        oaProjectDeployCwProjectFeeInfo.setBankOfDeposit(bankOfDeposit);
                        oaProjectDeployCwProjectFeeInfo.setRate(rate);
                        oaProjectDeployCwProjectFeeInfo.setTaxRate(taxRate);
                        oaProjectDeployCwProjectFeeInfo.setOrderNum(i + 1);
                        oaProjectDeployCwProjectFeeInfo.setStatus("0");
                        oaProjectDeployCwProjectFeeInfo.setCreateBy(userNameString);
                        oaProjectDeployCwProjectFeeInfo.setCreateId(userId);
                        oaProjectDeployCwProjectFeeInfo.setCreateTime(nowDate);
                        oaProjectDeployCwProjectFeeInfo.setUpdateBy(userNameString);
                        oaProjectDeployCwProjectFeeInfo.setUpdateId(userId);
                        oaProjectDeployCwProjectFeeInfo.setUpdateTime(nowDate);
                        int i1 = oaProjectDeployMapper.insertCwProjectFeeInfo(oaProjectDeployCwProjectFeeInfo);
                    }
                }
            }
        });
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importExcelForReceiptAndPaymentInfo(MultipartFile file, LoginUser loginUser) throws Exception {
        String nickName = loginUser.getUser().getNickName();
        String userName = loginUser.getUsername();
        Long userId = loginUser.getUserId();
        String traderTypeNeibu = "内部";
        String traderTypeWaibu = "外部";
        //获取导入的excel中的信息
        ExcelImportOfReceiptAndPaymentInfo excelImportOfReceiptAndPaymentInfo = new ExcelImportOfReceiptAndPaymentInfo();
        excelImportOfReceiptAndPaymentInfo.init(file.getInputStream());
        List<ExcelImportOfReceiptAndPaymentInfoDto> excelImportOfReceiptAndPaymentInfoDtoList = excelImportOfReceiptAndPaymentInfo.importExcelHandle();
        //读取到了数据以后，进行数据的处理

        //先按照项目进行分组处理
        Map<String, List<ExcelImportOfReceiptAndPaymentInfoDto>> groupByProjectNameList = excelImportOfReceiptAndPaymentInfoDtoList.stream().collect(Collectors.groupingBy(ExcelImportOfReceiptAndPaymentInfoDto::getProjectName));
        Set<String> projectNameSet = groupByProjectNameList.keySet();
        //通过获取的项目名称，获取项目的id
        List<OaProjectDeploy> oaProjectDeployList = oaProjectDeployMapper.selectOaProjectDeployListByProjectNameSet(projectNameSet);
        //通过获取收付款人信息，看导入的时的oaTraderId
        OaTrader oaTrader = new OaTrader();
        oaTrader.setType("0");
        List<OaTrader> oaTraders = oaTraderMapper.selectOaTraderList(oaTrader);
        //获取枚举，用来与导入的excel种的事项进行匹配
        List<Map<String, Object>> list = OaProjectDeployReceiptAndPaymentInfoEnum.getList();
        Date nowDate = DateUtils.getNowDate();
//        //要入库的集合
//        List<OaProjectDeployReceiptAndPaymentInfo> oaProjectDeployReceiptAndPaymentInfoList = new ArrayList<>();
        groupByProjectNameList.forEach((projectName, receiptAndPaymentInfoDtoList) -> {
            OaProjectDeploy oaProjectDeploy = oaProjectDeployList.stream().filter(t -> projectName.equals(t.getProjectName())).findFirst().orElse(null);
            if (oaProjectDeploy != null) {
                Long id = oaProjectDeploy.getId();
                //然后对事项进行分组
                Map<String, List<ExcelImportOfReceiptAndPaymentInfoDto>> groupByItemName = receiptAndPaymentInfoDtoList.stream().collect(Collectors.groupingBy(ExcelImportOfReceiptAndPaymentInfoDto::getItemName));
                groupByItemName.forEach((itemName, receiptAndPaymentInfoDtoList1) -> {
                    Map<String, Object> map = list.stream().filter(t -> t.get("info").toString().equals(itemName)).findFirst().orElse(null);
                    if (map != null) {
                        //说明导入的事项是枚举里的其中一个，那么就进行下一步的处理
                        String code = map.get("code").toString();
                        String receiptAndPaymentType = map.get("receiptAndPaymentType").toString();
                        int serialNum = 1;
                        for (ExcelImportOfReceiptAndPaymentInfoDto excelImportOfReceiptAndPaymentInfoDto : receiptAndPaymentInfoDtoList1) {
                            //因为excel导入的是收付款是在一起的，所以有两个组装的对象，一个是收款，一个是付款
                            //收款对象组装
                            boolean insertFlag1 = false;
                            boolean insertFlag2 = false;
                            OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo = new OaProjectDeployReceiptAndPaymentInfo();
                            oaProjectDeployReceiptAndPaymentInfo.setOaProjectDeployId(id);
                            oaProjectDeployReceiptAndPaymentInfo.setReceiptAndPaymentType(receiptAndPaymentType);
                            oaProjectDeployReceiptAndPaymentInfo.setItemName(code);
                            oaProjectDeployReceiptAndPaymentInfo.setSerialNum(serialNum);
                            //先去判断一下当前收款人是否是内部
                            if (traderTypeWaibu.equals(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeOneTraderType())) {
                                oaProjectDeployReceiptAndPaymentInfo.setInputType("2");
                                oaProjectDeployReceiptAndPaymentInfo.setTraderType("1");
                                oaProjectDeployReceiptAndPaymentInfo.setAccountName(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeOneAccountName());
                                oaProjectDeployReceiptAndPaymentInfo.setAccountNumber(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeOneAccountNumber());
                                oaProjectDeployReceiptAndPaymentInfo.setBankOfDeposit(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeOneBankOfDeposit());
                                insertFlag1 = true;
                            } else if (traderTypeNeibu.equals(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeOneTraderType())) {
                                //找对应的收付款人id
                                OaTrader oaTrader1 = oaTraders.stream().filter(t -> excelImportOfReceiptAndPaymentInfoDto.getTraderTypeOneAccountName().equals(t.getUserName()) && excelImportOfReceiptAndPaymentInfoDto.getTraderTypeOneAccountNumber().equals(t.getAccountNumber())
                                        && excelImportOfReceiptAndPaymentInfoDto.getTraderTypeOneBankOfDeposit().equals(t.getBankOfDeposit()) && ("1".equals(t.getTraderType()) || "9".equals(t.getTraderType()))).findFirst().orElse(null);
                                if (oaTrader1 != null) {
                                    oaProjectDeployReceiptAndPaymentInfo.setInputType("1");
                                    oaProjectDeployReceiptAndPaymentInfo.setOaTraderId(oaTrader1.getId());
                                    insertFlag1 = true;
                                }
                            }
                            oaProjectDeployReceiptAndPaymentInfo.setStatus("0");
                            oaProjectDeployReceiptAndPaymentInfo.setCreateBy(nickName);
                            oaProjectDeployReceiptAndPaymentInfo.setCreateId(userId);
                            oaProjectDeployReceiptAndPaymentInfo.setCreateTime(nowDate);
                            oaProjectDeployReceiptAndPaymentInfo.setUpdateBy(nickName);
                            oaProjectDeployReceiptAndPaymentInfo.setUpdateId(userId);
                            oaProjectDeployReceiptAndPaymentInfo.setUpdateTime(nowDate);
                            //付款对象组装
                            OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo1 = new OaProjectDeployReceiptAndPaymentInfo();
                            oaProjectDeployReceiptAndPaymentInfo1.setOaProjectDeployId(id);
                            oaProjectDeployReceiptAndPaymentInfo1.setReceiptAndPaymentType(receiptAndPaymentType);
                            oaProjectDeployReceiptAndPaymentInfo1.setItemName(code);
                            oaProjectDeployReceiptAndPaymentInfo1.setSerialNum(serialNum);
                            //先去判断一下当前收款人是否是内部
                            if (traderTypeWaibu.equals(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeZeroTraderType())) {
                                oaProjectDeployReceiptAndPaymentInfo1.setInputType("2");
                                oaProjectDeployReceiptAndPaymentInfo1.setTraderType("0");
                                oaProjectDeployReceiptAndPaymentInfo1.setAccountName(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeZeroAccountName());
                                oaProjectDeployReceiptAndPaymentInfo1.setAccountNumber(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeZeroAccountNumber());
                                oaProjectDeployReceiptAndPaymentInfo1.setBankOfDeposit(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeZeroBankOfDeposit());
                                insertFlag2 = true;
                            } else if (traderTypeNeibu.equals(excelImportOfReceiptAndPaymentInfoDto.getTraderTypeZeroTraderType())) {
                                //找对应的收付款人id
                                OaTrader oaTrader1 = oaTraders.stream().filter(t -> excelImportOfReceiptAndPaymentInfoDto.getTraderTypeZeroAccountName().equals(t.getUserName()) && excelImportOfReceiptAndPaymentInfoDto.getTraderTypeZeroAccountNumber().equals(t.getAccountNumber())
                                        && excelImportOfReceiptAndPaymentInfoDto.getTraderTypeZeroBankOfDeposit().equals(t.getBankOfDeposit()) && ("0".equals(t.getTraderType()) || "9".equals(t.getTraderType()))).findFirst().orElse(null);
                                if (oaTrader1 != null) {
                                    oaProjectDeployReceiptAndPaymentInfo1.setInputType("1");
                                    oaProjectDeployReceiptAndPaymentInfo1.setOaTraderId(oaTrader1.getId());
                                    insertFlag2 = true;
                                }
                            }
                            oaProjectDeployReceiptAndPaymentInfo1.setStatus("0");
                            oaProjectDeployReceiptAndPaymentInfo1.setCreateBy(nickName);
                            oaProjectDeployReceiptAndPaymentInfo1.setCreateId(userId);
                            oaProjectDeployReceiptAndPaymentInfo1.setCreateTime(nowDate);
                            oaProjectDeployReceiptAndPaymentInfo1.setUpdateBy(nickName);
                            oaProjectDeployReceiptAndPaymentInfo1.setUpdateId(userId);
                            oaProjectDeployReceiptAndPaymentInfo1.setUpdateTime(nowDate);
                            //把这两个对象都加入要入库的集合当中
//                            oaProjectDeployReceiptAndPaymentInfoList.add(oaProjectDeployReceiptAndPaymentInfo);
//                            oaProjectDeployReceiptAndPaymentInfoList.add(oaProjectDeployReceiptAndPaymentInfo1);
                            //可以直接入库，不需要多走一步集合的这一步骤
                            if (insertFlag1) {
                                oaProjectDeployMapper.insertReceiptAndPaymentInfo(oaProjectDeployReceiptAndPaymentInfo);
                            }
                            if (insertFlag2) {
                                oaProjectDeployMapper.insertReceiptAndPaymentInfo(oaProjectDeployReceiptAndPaymentInfo1);
                            }
                            serialNum++;
                        }
                    }
                });
            }
        });
        return 1;
    }

    /**
     * 老查询接口    2024.03.13合并冲突时产生，用于保证之前的逻辑不会出现问题
     *
     * @param oaProjectDeploy
     * @return OaProjectDeploy
     */
    @Override
    public List<OaProjectDeploy> selectOaProjectDeployList1(OaProjectDeploy oaProjectDeploy)
    {
        return oaProjectDeployMapper.selectOaProjectDeployList(oaProjectDeploy);
    }

    private List<OaProjectDeployVo1> getOaProjectDeployVoListByOaProjectDeployList(List<OaProjectDeploy> oaProjectDeploys) {
//        Long currentUserId = SecurityUtils.getLoginUser().getUserId();
//        String oaApplyType = "4";
//        List<SysUser> yewuAdminList = sysUserMapper.selectUserByRoleKey("yewuAdmin");
//        //当前用户是业务管理员
//        boolean yewuAdminFlag = yewuAdminList.stream().anyMatch(t -> t.getUserId().equals(currentUserId));
//        TopNotify topNotify = new TopNotify();
//        topNotify.setOaNotifyType(oaApplyType);
//        topNotify.setNotifyType("1");
//        topNotify.setOaNotifyStep("3");
//        TopNotify topNotify1 = new TopNotify();
//        topNotify1.setOaNotifyType(oaApplyType);
//        topNotify1.setNotifyType("1");
//        topNotify1.setOaNotifyStep("2");
        List<OaProjectDeployVo1> oaProjectDeployVo1s = new ArrayList<>();
        for (OaProjectDeploy oaProjectDeploy:oaProjectDeploys) {
            if(null==oaProjectDeploy){
                continue;
            }
            OaProjectDeployVo1 oaProjectDeployVo1 = new OaProjectDeployVo1();
            BeanUtil.copyProperties(oaProjectDeploy, oaProjectDeployVo1);
//            //补充页面缺失数据
//            //查找对应的负责人
//            List<OaEditApproveGeneralityUser> oaEditApproveGeneralityUsers = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(oaApplyType, oaProjectDeploy.getId());
//            String salesman= oaEditApproveGeneralityUsers.stream().filter(t -> "0".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserNickName).collect(Collectors.joining(","));
//            String financialStaff = oaEditApproveGeneralityUsers.stream().filter(t -> "1".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserNickName).collect(Collectors.joining(","));
//            oaProjectDeployVo1.setSalesman(salesman);
//            oaProjectDeployVo1.setFinancialStaff(financialStaff);
//            //查最新的审核状态 - 未知悉的情况，说明流程未结束
//            PageHelper.clearPage();
//            OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaProjectDeploy.getId(), "0");
//            if (oaEditApproveGeneralityEditRecords == null) {
//                PageHelper.clearPage();
//                oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyIdAndOaNotifyStepThree(oaApplyType, oaProjectDeploy.getId(), "0");
//            }
//            if (oaEditApproveGeneralityEditRecords != null) {
//                oaProjectDeployVo1.setCheckStatus(oaEditApproveGeneralityEditRecords.getCheckStatus());
//                oaProjectDeployVo1.setRejectFlag(oaEditApproveGeneralityEditRecords.getRejectFlag());
//                oaProjectDeployVo1.setConfirmFlag(oaEditApproveGeneralityEditRecords.getConfirmFlag());
//                oaProjectDeployVo1.setResponsibilityConfirmFlag(oaEditApproveGeneralityEditRecords.getResponsibilityConfirmFlag());
//                //修改的判断方法 oa_apply_records_old_id和oa_apply_records_new_id都不为空而且不相等
//                if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() != null && !oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId().equals(oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId())) {
//                    oaProjectDeployVo1.setUpdateType("1");
//                }
//                //删除的判断方法 oa_apply_records_old_id 不为空 并且oa_apply_records_new_id 为空
//                if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() == null) {
//                    oaProjectDeployVo1.setUpdateType("2");
//                }
//                if ("1".equals(selectType)) {
//                    //查询的是待我审批，找到提交人  --->  也就是找编辑人员的姓名
//                    oaProjectDeployVo1.setEditUserNickName(oaEditApproveGeneralityEditRecords.getEditUserNickName());
//                    //找到提交时间
//                    oaProjectDeployVo1.setEditTime(oaEditApproveGeneralityEditRecords.getEditTime());
//                    //修改说明
//                    oaProjectDeployVo1.setEditInfo(oaEditApproveGeneralityEditRecords.getEditInfo());
//                } else if ("2".equals(selectType)) {
//                    //查询的是我的提交，找到审批人
//                    oaProjectDeployVo1.setCheckUserNickName(oaEditApproveGeneralityEditRecords.getCheckUserNickName());
//                    //找到审核时间
//                    oaProjectDeployVo1.setCheckTime(oaEditApproveGeneralityEditRecords.getCheckTime());
//                }
//
//                //查询当前用户的权限问题，是否是有本条记录的编辑，是否有本条记录的已知悉操作
//                Long checkUserId = oaEditApproveGeneralityEditRecords.getCheckUserId();
//                if (yewuAdminFlag) {
//                    //只有业务管理员可以审核，所以这块审核标识为1
//                    oaProjectDeployVo1.setShowCheckFlag("1");
//                    oaProjectDeployVo1.setShowConfirmFlag("1");
//                } else {
//                    //其余的人不允许审核，所以审核标识为0
//                    oaProjectDeployVo1.setShowCheckFlag("0");
////                    if (checkUserId != null && checkUserId.equals(currentUserId)) {
////                        oaProjectDeployVo.setShowCheckFlag("1");
////                    } else {
////                        oaProjectDeployVo.setShowCheckFlag("0");
////                    }
//                    //找项目的负责人
//                    topNotify.setOaApplyId(oaProjectDeploy.getOaApplyId());
//                    topNotify1.setOaApplyId(oaProjectDeploy.getOaApplyId());
//                    List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
//                    List<TopNotify> topNotifies1 = topNotifyMapper.selectTopNotifyList1(topNotify);
//                    //当前责任人是否负责已知悉
//                    boolean responsibilityConfirmFlag = topNotifies.stream().anyMatch(t -> t.getDisposeUser().equals(currentUserId));
//                    boolean confirmFlag = topNotifies1.stream().anyMatch(t -> t.getDisposeUser().equals(currentUserId));
//                    if (!confirmFlag && !responsibilityConfirmFlag) {
//                        oaProjectDeployVo1.setShowConfirmFlag("0");
//                    } else {
//                        oaProjectDeployVo1.setShowConfirmFlag("1");
//                    }
//                }
//            } else {
//                oaProjectDeployVo1.setConfirmFlag("1");
//                oaProjectDeployVo1.setResponsibilityConfirmFlag("1");
//                oaProjectDeployVo1.setShowCheckFlag("0");
//                oaProjectDeployVo1.setShowConfirmFlag("0");
//            }

            //新增数据展示

            //产品暂时得等
            DProjectParameter dProjectParameter = new DProjectParameter();
            dProjectParameter.setProjectId(oaProjectDeploy.getId());
            List<DProjectParameter> dProjectParameters = projectParameterService.selectDProjectParameterList(dProjectParameter);

            //项目类型
//            List<Map<String,Object>> projectTypeData  =   projectTypeRelevanceMapper.queryProjectData("0",oaProjectDeploy.getId());
            //业务类型
            //List<Map<String,Object>> businessTypeData  =   projectTypeRelevanceMapper.queryProjectData("1",oaProjectDeploy.getId());
            //业务负责人
            List<Map<String, Object>> yewuList = oaEditApproveGeneralityUserMapper.queryDataByOaApplyTypeAndOaApplyId(currentOaApplyType, oaProjectDeploy.getOaApplyId(), "1");
            //财务负责人
            List<Map<String, Object>> caiwuList = oaEditApproveGeneralityUserMapper.queryDataByOaApplyTypeAndOaApplyId(currentOaApplyType, oaProjectDeploy.getOaApplyId(), "0");
            //渠道方
            List<Map<String, Object>> qudaoList = projectChannelUserMapper.selectProjectuserList(oaProjectDeploy.getOaApplyId());
            oaProjectDeployVo1.setQudaofangList(qudaoList);
//            oaProjectDeployVo1.setProjectTypeList(projectTypeData);
            //oaProjectDeployVo1.setBusinessTypeList(businessTypeData);
            oaProjectDeployVo1.setYewuList(yewuList);
            oaProjectDeployVo1.setCaiwuList(caiwuList);
            oaProjectDeployVo1.setCompanyName(oaProjectDeploy.getCompanyName());
            oaProjectDeployVo1s.add(oaProjectDeployVo1);
            oaProjectDeployVo1.setProductList(dProjectParameters);
        }
        return oaProjectDeployVo1s;
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param oaProjectDeploy 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertOaProjectDeploy(OaProjectDeploy oaProjectDeploy)
    {
        oaProjectDeploy.setCreateTime(DateUtils.getNowDate());
        return oaProjectDeployMapper.insertOaProjectDeploy(oaProjectDeploy);
    }

    public Map<String,Object> newInsertOaProjectDeploy(OaProjectDeployBo oaProjectDeployBo,LoginUser loginUser)
    {
        Map<String,Object> map = new HashMap<>();
        Boolean flag = false;
        String addUuid = oaProjectDeployBo.getAddUuid();
        //在项目立项模块新增的项目名称，直接入库 add by niey 2024-7-31 开始
        if(null != oaProjectDeployBo.getAddSource() && !oaProjectDeployBo.getAddSource().equals("") && oaProjectDeployBo.getAddSource().equals("xmgl")){
            flag = true;
        }
        if (flag){
            oaProjectDeployBo.setCheckStatus("0");//0:新增项目审核中
        }
        //在项目立项模块新增的项目名称，直接入库 add by niey 2024-7-31 结束
        Long aLong = this.newInsertOrUpdateOaProjectDeploy(oaProjectDeployBo, loginUser);
        //立项项目模块新增项目名称时，插入临时表 add by niey 2024-7-31 开始
        if (flag) {
            XmglAddTemporarily addTemporarily = new XmglAddTemporarily();
            if (null == addUuid || addUuid.equals("")) {
                addUuid = IdUtils.fastSimpleUUID();
                addTemporarily.setAddUuid(addUuid);
            }else {
                addTemporarily.setAddUuid(oaProjectDeployBo.getAddUuid());
            }
            addTemporarily.setDeployTabId(aLong);
            addTemporarily.setAddType("1");//1项目 2公司 3公司类型
            xmgAddTemporarilyService.insertXmglAddTemporarily(addTemporarily);
        }
        map.put("addUuid",addUuid);
        map.put("returnId",aLong);
        //立项项目模块新增项目名称时，插入临时表 add by niey 2024-7-31 结束
//        OaProjectDeployDto oaProjectDeployDto = new OaProjectDeployDto();
//        Long id = oaProjectDeployBo.getId();
//        Long oaApplyId = id;
//        Date nowDate = DateUtils.getNowDate();
//        String checkStatus = "2";
//        //当前用户的姓名
//        String nickName = loginUser.getUser().getNickName();
//        if(null == id || id.equals("")){
//            oaApplyId = aLong;
//            oaProjectDeployBo.setId(oaApplyId);
//            BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeployDto);
//            oaProjectDeployDto.setAddNotApprove("0");
//            String jsonString = JSONObject.toJSONString(oaProjectDeployDto);
//            //新增一条记录到oa_edit_approve_generality_records表中
//            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
//            oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
//            oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
//            oaEditApproveGeneralityRecords.setData(jsonString);
//            oaEditApproveGeneralityRecords.setStatus("0");
//            oaEditApproveGeneralityRecords.setCreateBy(nickName);
//            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
//            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
//            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
//            int i1 = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
//            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录",1,null,null);
//            if (i1 > 0) {
//                Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
//                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
//                oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
//                oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
//                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaApplyRecordsNewId);
//                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
//                oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
//                oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
//                oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectDeployBo.getEditInfo());
//                oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
//                oaEditApproveGeneralityEditRecords.setCheckUserId(loginUser.getUserId());
//                oaEditApproveGeneralityEditRecords.setRejectFlag("0");
//                oaEditApproveGeneralityEditRecords.setConfirmFlag("1");
//                oaEditApproveGeneralityEditRecords.setResponsibilityConfirmFlag("1");
//                int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
//                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录详情信息",1,null,null);
//            }
//        }else {
//            //获取JSON字符串
//            BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeployDto);
//            oaProjectDeployDto.setAddNotApprove("0");
//            String jsonString = JSONObject.toJSONString(oaProjectDeployDto);
//            //新增一条记录到oa_edit_approve_generality_records表中
//            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
//            oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
//            oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
//            oaEditApproveGeneralityRecords.setData(jsonString);
//            oaEditApproveGeneralityRecords.setStatus("0");
//            oaEditApproveGeneralityRecords.setCreateBy(nickName);
//            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
//            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
//            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
//            int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
//            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录",1,null,null);
//            if (i > 0) {
//                Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
//                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
//                oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
//                oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
//                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaProjectDeployBo.getOaProjectDeployBoOldDataId());
//                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
//                oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
//                oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
//                oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectDeployBo.getEditInfo());
//                oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
//                oaEditApproveGeneralityEditRecords.setCheckUserId(loginUser.getUserId());
//                oaEditApproveGeneralityEditRecords.setRejectFlag("0");
//                oaEditApproveGeneralityEditRecords.setConfirmFlag("1");
//                oaEditApproveGeneralityEditRecords.setResponsibilityConfirmFlag("1");
//                int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
//                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录详情信息",1,null,null);
//            }
//        }

        return map;
    }

    /**
     * 2024-07-08  2.0修改
     * 项目新增和修改 返回为项目的id
     * @param oaProjectDeployBo
     * @return {@link Long}
     */
    public Long newInsertOrUpdateOaProjectDeploy(OaProjectDeployBo oaProjectDeployBo,LoginUser loginUser)
    {
        Long id = oaProjectDeployBo.getId();
        String nickName = loginUser.getUser().getNickName();
        Date nowDate = DateUtils.getNowDate();
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        //新增
        BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeploy);
        if(null == id || id.equals("")){
            //新增
            oaProjectDeploy.setCreateBr(nickName);
            oaProjectDeploy.setCreateTime(nowDate);
            oaProjectDeployMapper.insertOaProjectDeploy(oaProjectDeploy);
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增项目信息",1,null,null);
            id = oaProjectDeploy.getId();
            //新增类型表
                //项目类型
            List<ProjectTypeRelevance> projectTypeList = oaProjectDeployBo.getProjectTypeList();
            if(null!=projectTypeList && projectTypeList.size()>0){
                for (ProjectTypeRelevance projectTypeRelevance : projectTypeList) {
                    projectTypeRelevance.setProjectId(id);
                    projectTypeRelevance.setCreateBy(nickName);
                    projectTypeRelevance.setCreateTime(nowDate);
                }
                projectTypeRelevanceMapper.batchInsert(projectTypeList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增项目类型",1,null,null);
            }

                //业务类型
            List<ProjectTypeRelevance> businessTypeList = oaProjectDeployBo.getBusinessTypeList();
            if(null!=businessTypeList && businessTypeList.size()>0){
                for (ProjectTypeRelevance projectTypeRelevance : businessTypeList) {
                    projectTypeRelevance.setProjectId(id);
                    projectTypeRelevance.setCreateBy(nickName);
                    projectTypeRelevance.setCreateTime(nowDate);
                }
                projectTypeRelevanceMapper.batchInsert(businessTypeList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增业务类型",1,null,null);
            }
            //公司
                //其他公司
            List<ProjectCompanyRelevance> otherUnitList = oaProjectDeployBo.getOtherUnitList();
            if(null!=otherUnitList && otherUnitList.size()>0){
                for (ProjectCompanyRelevance projectCompanyRelevance : otherUnitList) {
                    projectCompanyRelevance.setProjectId(id);
                    projectCompanyRelevance.setUnitType("3");
                    projectCompanyRelevance.setCreateBy(nickName);
                    projectCompanyRelevance.setCreateTime(nowDate);
                }
                projectCompanyRelevanceMapper.batchInsert(otherUnitList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增其他公司信息",1,null,null);
            }

/*           //担保公司
            List<ProjectCompanyRelevance> custList = oaProjectDeployBo.getCustList();
            if(custList.size()>0){
                for (ProjectCompanyRelevance projectCompanyRelevance : custList) {
                    projectCompanyRelevance.setProjectId(id);
                    projectCompanyRelevance.setCreateBy(nickName);
                    projectCompanyRelevance.setCreateTime(nowDate);
                }
                projectCompanyRelevanceMapper.batchInsert(custList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增担保公司信息",1,null,null);
            }

            //资产方
            List<ProjectCompanyRelevance> partnerList = oaProjectDeployBo.getPartnerList();
            if(partnerList.size()>0){
                for (ProjectCompanyRelevance projectCompanyRelevance : partnerList) {
                    projectCompanyRelevance.setProjectId(id);
                    projectCompanyRelevance.setCreateBy(nickName);
                    projectCompanyRelevance.setCreateTime(nowDate);
                }
                projectCompanyRelevanceMapper.batchInsert(partnerList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增资产方信息",1,null,null);
            }

            //资金方
            List<ProjectCompanyRelevance> fundList = oaProjectDeployBo.getFundList();
            if(fundList.size()>0){
                for (ProjectCompanyRelevance projectCompanyRelevance : fundList) {
                    projectCompanyRelevance.setProjectId(id);
                    projectCompanyRelevance.setCreateBy(nickName);
                    projectCompanyRelevance.setCreateTime(nowDate);
                }
                projectCompanyRelevanceMapper.batchInsert(fundList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增资金方信息",1,null,null);
            }
*/

            //2025-02-08 改为动态新增
            Map<String, List<ProjectCompanyRelevance>> tableList = oaProjectDeployBo.getTableList();
            if(tableList != null && !tableList.isEmpty()){
                tableList = this.repliceMap(tableList);
                List<ProjectCompanyRelevance> mergedList = new ArrayList<>();
                for (Map.Entry<String, List<ProjectCompanyRelevance>> entry : tableList.entrySet()) {
                    String key = entry.getKey();
                    List<ProjectCompanyRelevance> projectList = entry.getValue();
                    String unitType = key.replace("List", "");
                    // 设置 unitType 并合并到一个列表
                    for (ProjectCompanyRelevance project : projectList) {
                        project.setUnitType(unitType);  // 设置 unitType
                        project.setProjectId(id);
                        project.setCreateBy(nickName);
                        project.setCreateTime(nowDate);
                        mergedList.add(project);  // 添加到合并后的列表
                    }
                }
                projectCompanyRelevanceMapper.batchInsert(mergedList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增项目公司信息",1,null,null);
            }

            //渠道方  2024-07-24暂时取消渠道方字段

//            if(null != oaProjectDeployBo.getChannelType() && !"".equals(oaProjectDeployBo.getChannelType())) {
//                if (oaProjectDeployBo.getChannelType().equals("0")) {
//                    List<ProjectChannelUser> qudaoList = oaProjectDeployBo.getQudaofangList();
//                    if (null != qudaoList && qudaoList.size() > 0) {
//                        for (ProjectChannelUser projectChannelUser : qudaoList) {
//                            projectChannelUser.setProjectId(id);
//                            projectChannelUser.setStatus("0");
//                            projectChannelUser.setCreateBy(nickName);
//                            projectChannelUser.setUpdateTime(nowDate);
//                        }
//                    }
//                    projectChannelUserMapper.batchInsert(qudaoList);
//                    sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增内部渠道方信息",1,null,null);
//                }
//            }

        }else {
            oaProjectDeploy.setUpdateBy(nickName);
            oaProjectDeploy.setUpdateTime(nowDate);
            oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
            /** add by niey 更新项目信息同时修改立项项目表 开始 */
            XmglProject xmglProject = new XmglProject();
            xmglProject.setDeployId(oaProjectDeploy.getId());
            xmglProject.setProjectStatus(XmglProjectEnum.XGXMSH.getCode());
            xmglProject.setProjectName(oaProjectDeploy.getProjectName());
            xmglProjectService.updateXmglProjectAnyStatus(xmglProject);
            /** add by niey 更新项目信息同时修改立项项目表 结束 */
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"更新项目信息",2,null,null);
            //删除对多集合数据 并新增
            projectTypeRelevanceMapper.deleteByProjectId(id);
            projectCompanyRelevanceMapper.deleteByProjectId(id);
            projectChannelUserMapper.deleteDataByProjectId(id);

            /** 修改项目属性时，同步修改立项项目属性 add by niey */
            //根据项目名称id查询项目立项信息
            XmglDeployProject xmglDeployProject = xmglProjectDeployServiceImpl.selectXmglProjectDeployByDeployId(id);
            if (!Objects.isNull(xmglDeployProject)) {
                //根据关联表的立项项目id查询立项项目信息
                XmglProject project = xmglProjectMapper.selectXmglProjectById(xmglDeployProject.getProjectId());
                //有关联关系且关联状态是1正常，且立项项目状态不是'已终止'或'已上线'时，更新
                if (xmglDeployProject.getStatus().equals("1") && !project.getProjectStatus().equals(XmglProjectEnum.YZZ.getCode())
                        && !project.getProjectStatus().equals(XmglProjectEnum.YSX.getCode())) {
                    //删除旧的公司信息
                    xmglRelevanceMapper.deleteProjectCompanyRelevance(id, xmglDeployProject.getProjectId());
                    //删除旧的类型信息
                    xmglRelevanceMapper.deleteProjectTypeRelevance(id, xmglDeployProject.getProjectId());
                }
            }

            //项目类型
            List<ProjectTypeRelevance> projectTypeList = oaProjectDeployBo.getProjectTypeList();
            if(null!=projectTypeList && projectTypeList.size()>0){
                for (ProjectTypeRelevance projectTypeRelevance : projectTypeList) {
                    projectTypeRelevance.setProjectId(id);
                    projectTypeRelevance.setCreateBy(nickName);
                    projectTypeRelevance.setCreateTime(nowDate);
                }
                projectTypeRelevanceMapper.batchInsert(projectTypeList);
                xmglRelevanceServiceImpl.updateProjectTypeRelevance(projectTypeList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增项目类型",1,null,null);
            }

            //业务类型
            List<ProjectTypeRelevance> businessTypeList = oaProjectDeployBo.getBusinessTypeList();
            if(null!=businessTypeList && businessTypeList.size()>0){
                for (ProjectTypeRelevance projectTypeRelevance : businessTypeList) {
                    projectTypeRelevance.setProjectId(id);
                    projectTypeRelevance.setCreateBy(nickName);
                    projectTypeRelevance.setCreateTime(nowDate);
                }
                projectTypeRelevanceMapper.batchInsert(businessTypeList);
                xmglRelevanceServiceImpl.updateProjectTypeRelevance(businessTypeList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增业务类型",1,null,null);
            }
            //公司
            //其他公司
            List<ProjectCompanyRelevance> otherUnitList = oaProjectDeployBo.getOtherUnitList();
            if(null!=otherUnitList && otherUnitList.size()>0){
                for (ProjectCompanyRelevance projectCompanyRelevance : otherUnitList) {
                    projectCompanyRelevance.setProjectId(id);
                    projectCompanyRelevance.setUnitType("3");
                    projectCompanyRelevance.setCreateBy(nickName);
                    projectCompanyRelevance.setCreateTime(nowDate);
                }
                projectCompanyRelevanceMapper.batchInsert(otherUnitList);
                xmglRelevanceServiceImpl.updateProjectCompanyRelevance(otherUnitList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增其他公司信息",1,null,null);
            }

/*            //担保公司
            List<ProjectCompanyRelevance> custList = oaProjectDeployBo.getCustList();
            if(custList.size()>0){
                for (ProjectCompanyRelevance projectCompanyRelevance : custList) {
                    projectCompanyRelevance.setProjectId(id);
                    projectCompanyRelevance.setCreateBy(nickName);
                    projectCompanyRelevance.setCreateTime(nowDate);
                }
                projectCompanyRelevanceMapper.batchInsert(custList);
                xmglRelevanceServiceImpl.updateProjectCompanyRelevance(custList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增担保公司信息",1,null,null);
            }

            //资产方
            List<ProjectCompanyRelevance> partnerList = oaProjectDeployBo.getPartnerList();
            if(partnerList.size()>0){
                for (ProjectCompanyRelevance projectCompanyRelevance : partnerList) {
                    projectCompanyRelevance.setProjectId(id);
                    projectCompanyRelevance.setCreateBy(nickName);
                    projectCompanyRelevance.setCreateTime(nowDate);
                }
                projectCompanyRelevanceMapper.batchInsert(partnerList);
                xmglRelevanceServiceImpl.updateProjectCompanyRelevance(partnerList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增资产方信息",1,null,null);
            }

            //资金方
            List<ProjectCompanyRelevance> fundList = oaProjectDeployBo.getFundList();
            if(fundList.size()>0){
                for (ProjectCompanyRelevance projectCompanyRelevance : fundList) {
                    projectCompanyRelevance.setProjectId(id);
                    projectCompanyRelevance.setCreateBy(nickName);
                    projectCompanyRelevance.setCreateTime(nowDate);
                }
                projectCompanyRelevanceMapper.batchInsert(fundList);
                xmglRelevanceServiceImpl.updateProjectCompanyRelevance(fundList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增资金方信息",1,null,null);
            }
            */

            //2025-02-08 改为动态修改
            Map<String, List<ProjectCompanyRelevance>> tableList = oaProjectDeployBo.getTableList();
            if(tableList != null && !tableList.isEmpty()){
                tableList = this.repliceMap(tableList);
                List<ProjectCompanyRelevance> mergedList = new ArrayList<>();
                for (Map.Entry<String, List<ProjectCompanyRelevance>> entry : tableList.entrySet()) {
                    String key = entry.getKey();
                    List<ProjectCompanyRelevance> projectList = entry.getValue();
                    String unitType = key.replace("List", "");
                    // 设置 unitType 并合并到一个列表
                    for (ProjectCompanyRelevance project : projectList) {
                        project.setUnitType(unitType);  // 设置 unitType
                        project.setProjectId(id);
                        project.setCreateBy(nickName);
                        project.setCreateTime(nowDate);
                        mergedList.add(project);  // 添加到合并后的列表
                    }
                }
                projectCompanyRelevanceMapper.batchInsert(mergedList);
                xmglRelevanceServiceImpl.updateProjectCompanyRelevance(mergedList);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量修改项目公司信息",2,null,null);
            }

            sysSelectDataRefService.projectUpdateRef(oaProjectDeploy.getId());
            //渠道方 2024-07-24  暂时取消渠道方字段
//            if(null != oaProjectDeployBo.getChannelType() && !"".equals(oaProjectDeployBo.getChannelType())) {
//                if (oaProjectDeployBo.getChannelType().equals("0")) {
//                    List<ProjectChannelUser> qudaoList = oaProjectDeployBo.getQudaofangList();
//                    if (null != qudaoList && qudaoList.size() > 0) {
//                        for (ProjectChannelUser projectChannelUser : qudaoList) {
//                            projectChannelUser.setProjectId(id);
//                            projectChannelUser.setStatus("0");
//                            projectChannelUser.setCreateBy(nickName);
//                            projectChannelUser.setUpdateTime(nowDate);
//                        }
//                    }
//                    projectChannelUserMapper.batchInsert(qudaoList);
//                    sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"批量新增内部渠道方信息",1,null,null);
//                }
//            }



        }
        return id;
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param oaProjectDeploy 【请填写功能名称】
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOaProjectDeploy(OaProjectDeploy oaProjectDeploy)
    {
        Date nowDate = DateUtils.getNowDate();
        oaProjectDeploy.setUpdateTime(nowDate);
        int i = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);

        if (i > 0 && oaProjectDeploy.getProjectName() != null) {
            CwProject cwProject = new CwProject();
            cwProject.setOaProjectDeployId(oaProjectDeploy.getId());
            cwProject.setProjectName(oaProjectDeploy.getProjectName());
            cwProject.setUpdateTime(nowDate);
            cwProjectMapper.updateCwProjectByOaProjectDeployId(cwProject);
        }
        sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"更新项目信息",2,null,null);
        return i;
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaProjectDeployByIds(Long[] ids)
    {
        return oaProjectDeployMapper.deleteOaProjectDeployByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaProjectDeployById(Long id)
    {

        int i = topNotifyMapper.deleteTopNotifyByOaNotifyTypeAndOaApplyId(currentOaApplyType, id);
        int i1 = oaEditApproveGeneralityEditRecordsMapper.deleteOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, id);
        int i2 = oaEditApproveGeneralityRecordsMapper.deleteOaEditApproveGeneralityRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, id);
        int i3 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(currentOaApplyType, id);
        int i4 = oaProjectDeployMapper.deleteOaProjectDeployById(id);
        int i5 = projectTypeRelevanceMapper.deleteByProjectId(id);
        int i6 = projectCompanyRelevanceMapper.deleteByProjectId(id);
        return 1;
    }

    @Override
    public int updateEnable(OaProjectDeploy user,LoginUser loginUser) {
        return oaProjectDeployMapper.updateOaProjectDeploy(user);
    }

    /**
     * <AUTHOR>
     * @Description 查询项目与收款人映射关系
     * @Date 2023/8/31 15:53
     * @Param [projectId]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     **/
    public List<Map<String, Object>> selectProjectMapCollByProjectId(Integer projectId){
        return oaProjectDeployMapper.selectProjectMapCollByProjectId(projectId);
    }

    @Override
    public Map<String, String> queryIsRepeat(OaProjectDeployBo oaProjectDeployBo) {
        HashMap<String, String> returnMap = new HashMap<>();
        returnMap.put("isOk", "Y");
        // 查询各个项目的担保公司/资金方/资产方/技术服务方/倒流方/其他自定义类型
        List<OaProjectDeployBo> oaProjectDeploys = oaProjectDeployMapper.selectByData();
        // 获取所有的公司类型数据字典
        List<SysDictData> sysDictData = sysDictDataMapper.selectDictDataByType("company_type");
        Long backflowSide = 0L; // 倒流方
        Long technicaProvider = 0L;// 技术服务方
        for (SysDictData sysDictDatum : sysDictData) {
            if (sysDictDatum.getDictLabel().equals("倒流方")) {
                backflowSide = sysDictDatum.getDictCode();
            }
            if (sysDictDatum.getDictLabel().equals("技术服务方")) {
                technicaProvider = sysDictDatum.getDictCode();
            }
        }
        // 获取新增时填写的担保公司、资金方、资产方、倒流方、技术服务方
//        List<ProjectCompanyRelevance> newCustList = oaProjectDeployBo.getCustList();// 担保公司
//        List<ProjectCompanyRelevance> newPartnerList = oaProjectDeployBo.getPartnerList();// 资产方
//        List<ProjectCompanyRelevance> newFundList = oaProjectDeployBo.getFundList();// 资金方
        List<ProjectCompanyRelevance> newOtherUnitList = oaProjectDeployBo.getOtherUnitList();// 其他公司(包括倒流方、技术服务方)
        List<Long> newBackflowList = new ArrayList<>();// 倒流方
        List<Long> newProviderList = new ArrayList<>();// 技术服务方
        // 从前端传递过来的'其他公司'参数中，单独取出倒流方和技术服务方
        if (!CollectionUtils.isEmpty(newOtherUnitList) && newOtherUnitList.size() > 0) {
            for (ProjectCompanyRelevance projectCompanyRelevance : newOtherUnitList) {
                // 倒流方
                if (null != projectCompanyRelevance.getUnitTypeId() && projectCompanyRelevance.getUnitTypeId() == backflowSide) {
                    newBackflowList.add(projectCompanyRelevance.getUnitId());
                }
                // 技术服务方
                if (null != projectCompanyRelevance.getUnitTypeId() && projectCompanyRelevance.getUnitTypeId() == technicaProvider) {
                    newProviderList.add(projectCompanyRelevance.getUnitId());
                }
            }
        }
        List<Long> oldCustList = new ArrayList<>();// 担保公司
        List<Long> oldPartnerList = new ArrayList<>();// 资产方
        List<Long> oldFundList = new ArrayList<>();// 资金方
        List<Long> oldBackflowList = new ArrayList<>();// 倒流方
        List<Long> oldProviderList = new ArrayList<>();// 技术服务方
        /** 转换为参数id集合 */
        List<Long> newCustList1 = new ArrayList<>();
        List<Long> newPartnerList1= new ArrayList<>();
        List<Long> newFundList1 = new ArrayList<>();
        // 循环表中数据进行分类
        for (OaProjectDeployBo o : oaProjectDeploys) {
            // 获取项目名称详细信息
            OaProjectDeployBo projectDeployBo = this.selectProjectDeployInfoById(o.getId());
            /** 循环查询出来的项目名称集合，获取各个项目的担保公司、资金方、资产方、倒流方、技术服务方 */

//            // 担保公司
//            if (!CollectionUtils.isEmpty(projectDeployBo.getCustList()) && projectDeployBo.getCustList().size() > 0) {
//                oldCustList = projectDeployBo.getCustList().stream().map(ProjectCompanyRelevance::getUnitId).collect(Collectors.toList());
//            }
//            // 资产方
//            if (!CollectionUtils.isEmpty(projectDeployBo.getPartnerList()) && projectDeployBo.getPartnerList().size() > 0) {
//                oldPartnerList = projectDeployBo.getPartnerList().stream().map(ProjectCompanyRelevance::getUnitId).collect(Collectors.toList());
//            }
//            // 资金方
//            if (!CollectionUtils.isEmpty(projectDeployBo.getFundList()) && projectDeployBo.getFundList().size() > 0) {
//                oldFundList = projectDeployBo.getFundList().stream().map(ProjectCompanyRelevance::getUnitId).collect(Collectors.toList());
//            }
            /** 获取其他类型的公司(倒流方、技术服务方) */
            List<ProjectCompanyRelevance> otherUnitList = projectDeployBo.getOtherUnitList();
            if (!CollectionUtils.isEmpty(otherUnitList)) {
                for (ProjectCompanyRelevance projectCompanyRelevance : otherUnitList) {
                    // 倒流方
                    if (projectCompanyRelevance.getUnitType().equals("3") && projectCompanyRelevance.getUnitTypeId() == backflowSide) {
                        oldBackflowList.add(projectCompanyRelevance.getUnitId());
                    }
                    // 技术服务方
                    if (projectCompanyRelevance.getUnitType().equals("3") && projectCompanyRelevance.getUnitTypeId() == technicaProvider) {
                        oldProviderList.add(projectCompanyRelevance.getUnitId());
                    }
                }
            }
            Boolean custFlag = false;
            Boolean partnerFlag = false;
            Boolean fundFlag = false;
            Boolean backFlowFlag = false;
            Boolean providerFlag = false;

            // 担保公司
//            if (!CollectionUtils.isEmpty(newCustList) && newCustList.size() > 0){
//                if (oldCustList.size() == newCustList.size()) {
//                    newCustList1 = newCustList.stream().map(ProjectCompanyRelevance::getUnitId).collect(Collectors.toList());
//                    if (oldCustList.containsAll(newCustList1)) {
//                        custFlag = true;
//                    }
//                }
//            }
//
//            // 资产方
//            if (!CollectionUtils.isEmpty(newPartnerList) && newPartnerList.size() > 0) {
//                if (oldPartnerList.size() == newPartnerList.size()) {
//                    newPartnerList1 = newPartnerList.stream().map(ProjectCompanyRelevance::getUnitId).collect(Collectors.toList());
//                    if (oldPartnerList.containsAll(newPartnerList1)) {
//                        partnerFlag = true;
//                    }
//                }
//            }
//            // 资金方
//            if (!CollectionUtils.isEmpty(newFundList) && newFundList.size() > 0) {
//                if (oldFundList.size() == newFundList.size()) {
//                    newFundList1 = newFundList.stream().map(ProjectCompanyRelevance::getUnitId).collect(Collectors.toList());
//                    if (oldFundList.containsAll(newFundList1)) {
//                        fundFlag = true;
//                    }
//                }
//            }
            // 倒流方
            if (!CollectionUtils.isEmpty(newBackflowList) && newBackflowList.size() > 0) {
                if (oldBackflowList.size() == newBackflowList.size()) {
                    if (oldBackflowList.containsAll(newBackflowList)) {
                        backFlowFlag = true;
                    }
                }
            }
            // 技术服务方
            if (!CollectionUtils.isEmpty(newProviderList) && newProviderList.size() > 0) {
                if (oldProviderList.size() == newProviderList.size()) {
                    if (oldProviderList.containsAll(newProviderList)) {
                        providerFlag = true;
                    }
                }
            }

            // 如果五个条件都为true，则证明有重复数据
            if (custFlag && partnerFlag && fundFlag && backFlowFlag && providerFlag) {
                returnMap.put("isOk", "N");
                return returnMap;
            }
            oldCustList.clear();
            oldPartnerList.clear();
            oldFundList.clear();
            oldBackflowList.clear();
            oldProviderList.clear();

            newCustList1.clear();
            newPartnerList1.clear();
            newFundList1.clear();
        }
        return returnMap;
    }

    @Override
    public Map<String, Object> selectListBeforeParam(LoginUser loginUser, String selectType, String oaNotifyType) {
        Map<String, Object> paramMap = new HashMap<>();
        boolean present = loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "caiwuAdmin".equals(r.getRoleKey()) || "OA".equals(r.getRoleKey()) || "yewuAdmin".equals(r.getRoleKey()));
        //进行角色的判断，如果是上述角色，则没有公司限制，如果不是，则查找自己所属的公司
        if (present) {
            paramMap.put("companyIdList", null);
        } else {
            List<SysUserPost> userPostList = loginUser.getUser().getUserPostList();
            //筛选出用户所在的岗位
            List<Long> postList = userPostList.stream().map(SysUserPost::getPostId).collect(Collectors.toList());
            //根据岗位集合找到公司的集合
            //post找到了，找dept，然后找unit
            List<SysDept> deptList = sysDeptMapper.selectDeptListByPostIds(postList);
            List<Long> companyIdList = deptList.stream().map(SysDept::getUnitId).distinct().collect(Collectors.toList());
            if (companyIdList.size() > 0) {
                paramMap.put("companyIdList", companyIdList);
            } else {
                paramMap.put("companyIdList", null);
            }
        }
        //查待办通知表，筛选出来通知表中存在的的项目Id（oaApplyId）再根据方法传过来的oaNotifyType来确定具体筛选出来的结果
        if (!"0".equals(selectType)) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyType("1");
            topNotify.setDisposeUser(loginUser.getUserId());
            topNotify.setOaNotifyType(oaNotifyType);
            topNotify.setOaNotifyStep(selectType);
            List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
            List<Long> filterOaApplyId = topNotifies.stream().map(TopNotify::getOaApplyId).distinct().collect(Collectors.toList());
            if (filterOaApplyId.size() > 0) {
                paramMap.put("filterOaApplyId", filterOaApplyId);
            } else {
                paramMap.put("filterOaApplyId", null);
            }
        }
        return paramMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addNewEditInfo(OaProjectDeployBo oaProjectDeployBo, LoginUser loginUser) {
//        String oaApplyType = "4";
        Long oaApplyId = oaProjectDeployBo.getId();
        Date nowDate = DateUtils.getNowDate();
        //当前用户的姓名
        String nickName = loginUser.getUser().getNickName();
        //todo 项目名称配置在提交申请的时候是交给业务管理员申请，后续再添加用户角色到相关用户表
        //当前用户的用户id
//        Long currentUserId = loginUser.getUserId();
        //当前用户的角色相关信息
//        List<SysRole> roles = loginUser.getUser().getRoles();
//        boolean yewuFlag = roles.stream().anyMatch(t -> "yewu".equals(t.getRoleKey()));
//        boolean yewuAdminFlag = roles.stream().anyMatch(t -> "yewuAdmin".equals(t.getRoleKey()));
        //财务角色 ----> 会计或者出纳
//        boolean caiwuFlag = roles.stream().anyMatch(t -> "kuaiji".equals(t.getRoleKey()) || "chuna".equals(t.getRoleKey()));
//        boolean caiwuAdminFlag = roles.stream().anyMatch(t -> "caiwuAdmin".equals(t.getRoleKey()));
        String checkStatus = "2";
        //先进行添加规则的判断。不管是新增、修改还是删除，都要有功能表id主键（为什么？因为需要有表数据id去做后续的各种信息）
        String editType = oaProjectDeployBo.getEditType();
        //实体的存在，是为了对库里的数据进行操作，新增，修改，或者删除
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        //这个DTO对象的存在，是为了获取一个JSON字符串
        OaProjectDeployDto oaProjectDeployDto = new OaProjectDeployDto();

        //业务管理员发起的新增、修改、删除 ---> 相当于普通的新增、修改、删除，但是需要留痕
        boolean userFlag = loginUser.getUser().getRoles().stream().anyMatch(t -> "yewuAdmin".equals(t.getRoleKey()));
        if (userFlag) {
            //说明用户是业务管理员
            if ("0".equals(editType)) {
                //直接从前端接收到的对象进行落库
                //2024-04-18 wangzeyu 权限修改
                BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeploy);
                oaProjectDeploy.setAddNotApprove("0");
                //入库
                oaProjectDeployBo.setAddNotApprove("0");
                Long i = this.newInsertOrUpdateOaProjectDeploy(oaProjectDeployBo,loginUser);
                //入库后的自增主键
                oaApplyId = i;
                oaProjectDeployBo.setId(oaApplyId);
                BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeployDto);
                oaProjectDeployDto.setAddNotApprove("0");
                String jsonString = JSONObject.toJSONString(oaProjectDeployDto);
                //新增一条记录到oa_edit_approve_generality_records表中
                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
                oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
                oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
                oaEditApproveGeneralityRecords.setData(jsonString);
                oaEditApproveGeneralityRecords.setStatus("0");
                oaEditApproveGeneralityRecords.setCreateBy(nickName);
                oaEditApproveGeneralityRecords.setCreateTime(nowDate);
                oaEditApproveGeneralityRecords.setUpdateBy(nickName);
                oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
                int i1 = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录",1,null,null);
                if (i1 > 0) {
                    Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                    OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                    oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
                    oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaApplyRecordsNewId);
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                    oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
                    oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                    oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectDeployBo.getEditInfo());
                    oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
                    oaEditApproveGeneralityEditRecords.setCheckUserId(loginUser.getUserId());
                    oaEditApproveGeneralityEditRecords.setRejectFlag("0");
                    oaEditApproveGeneralityEditRecords.setConfirmFlag("1");
                    oaEditApproveGeneralityEditRecords.setResponsibilityConfirmFlag("1");
                    int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
                    sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录详情信息",1,null,null);
                }
            } else if ("1".equals(editType)) {
                //直接从前端接收到的对象进行修改
                //2024-04-18 权限管理修改 wangzeyu
                BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeploy);
                oaProjectDeploy.setAddNotApprove("0");
                //入库
                oaProjectDeployBo.setAddNotApprove("0");
                Long ii = this.newInsertOrUpdateOaProjectDeploy(oaProjectDeployBo,loginUser);
                //获取JSON字符串
                BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeployDto);
                oaProjectDeployDto.setAddNotApprove("0");
                String jsonString = JSONObject.toJSONString(oaProjectDeployDto);
                //新增一条记录到oa_edit_approve_generality_records表中
                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
                oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
                oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
                oaEditApproveGeneralityRecords.setData(jsonString);
                oaEditApproveGeneralityRecords.setStatus("0");
                oaEditApproveGeneralityRecords.setCreateBy(nickName);
                oaEditApproveGeneralityRecords.setCreateTime(nowDate);
                oaEditApproveGeneralityRecords.setUpdateBy(nickName);
                oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
                int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录",1,null,null);
                if (i > 0) {
                    Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                    OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                    oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
                    oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaProjectDeployBo.getOaProjectDeployBoOldDataId());
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                    oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
                    oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                    oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectDeployBo.getEditInfo());
                    oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
                    oaEditApproveGeneralityEditRecords.setCheckUserId(loginUser.getUserId());
                    oaEditApproveGeneralityEditRecords.setRejectFlag("0");
                    oaEditApproveGeneralityEditRecords.setConfirmFlag("1");
                    oaEditApproveGeneralityEditRecords.setResponsibilityConfirmFlag("1");
                    int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
                    sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录详情信息",1,null,null);
                }
            } else if ("2".equals(editType)) {
                //直接删除
                int i = topNotifyMapper.deleteTopNotifyByOaNotifyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                int i1 = oaEditApproveGeneralityEditRecordsMapper.deleteOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                int i2 = oaEditApproveGeneralityRecordsMapper.deleteOaEditApproveGeneralityRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                int i3 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                int i4 = oaProjectDeployMapper.deleteOaProjectDeployById(oaApplyId);
                int i5 = projectTypeRelevanceMapper.deleteByProjectId(oaApplyId);
                int i6 = projectCompanyRelevanceMapper.deleteByProjectId(oaApplyId);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"删除项目相关联信息",3,null,null);
            }
            return 1;
        }


        //下面的业务管理员集合是项目名称配置需要的
        //找所有的业务管理员
        List<SysUser> yewuAdminList = sysUserMapper.selectUserByRoleKey("yewuAdmin");
//        OaEditApproveGeneralityUser yewuAdminUser = new OaEditApproveGeneralityUser();
//        //oaApplyType为4代表事项目名称配置功能
//        yewuAdminUser.setOaApplyType(currentOaApplyType);
//        yewuAdminUser.setUserFlag("0");
//        yewuAdminUser.setStatus("0");
//        yewuAdminUser.setCreateBy(nickName);
//        yewuAdminUser.setCreateTime(nowDate);
//        yewuAdminUser.setUpdateBy(nickName);
//        yewuAdminUser.setUpdateTime(nowDate);
        //以下注释代码是项目名称配置不需要的
//        List<Long> salesmanList = oaProjectDeployBo.getSalesmanList();
//        List<Long> financialStaffList = oaProjectDeployBo.getFinancialStaffList();
//        if (yewuFlag || yewuAdminFlag) {
//            //如果操作者为业务，那么需要财务去审核
//            checkStatus = "1";
//            if (yewuFlag) {
//                //将用户id加入到业务集合中
//                financialStaffList.add(currentUserId);
//            }
//        } else if (caiwuFlag || caiwuAdminFlag){
//            checkStatus = "0";
//            //将用户id加入到业务集合中
//            if (caiwuFlag) {
//                salesmanList.add(currentUserId);
//            }
//        }
//        OaEditApproveGeneralityUser salesmanUser = new OaEditApproveGeneralityUser();
//        //oaApplyType为4代表事项目名称配置功能
//        salesmanUser.setOaApplyType(currentOaApplyType);
//        salesmanUser.setUserFlag("0");
//        salesmanUser.setStatus("0");
//        salesmanUser.setCreateBy(nickName);
//        salesmanUser.setCreateTime(nowDate);
//        salesmanUser.setUpdateBy(nickName);
//        salesmanUser.setUpdateTime(nowDate);
//        OaEditApproveGeneralityUser financialStaffUser = new OaEditApproveGeneralityUser();
//        //oaApplyType为4代表事项目名称配置功能
//        salesmanUser.setOaApplyType(currentOaApplyType);
//        financialStaffUser.setUserFlag("1");
//        financialStaffUser.setStatus("0");
//        financialStaffUser.setCreateBy(nickName);
//        financialStaffUser.setCreateTime(nowDate);
//        financialStaffUser.setUpdateBy(nickName);
//        financialStaffUser.setUpdateTime(nowDate);
        if ("0".equals(editType)) {
            //新增 --> 新增需要数据落库
            BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeploy);
            oaProjectDeploy.setAddNotApprove("9");
            oaProjectDeploy.setCreateTime(nowDate);
            //入库
            oaProjectDeployBo.setAddNotApprove("9");
            oaProjectDeployBo.setCreateTime(nowDate);
            Long i = this.newInsertOrUpdateOaProjectDeploy(oaProjectDeployBo,loginUser);
            //入库后的自增主键
            oaApplyId = i;
            oaProjectDeployBo.setId(oaApplyId);
            //以下注释代码是项目名称配置不需要的
            //id给到上面两个用户基础对象
//            salesmanUser.setOaApplyId(oaApplyId);
//            financialStaffUser.setOaApplyId(oaApplyId);
            //获取JSON字符串
            BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeployDto);
            oaProjectDeployDto.setAddNotApprove("9");
            String jsonString = JSONObject.toJSONString(oaProjectDeployDto);
            //新增一条记录到oa_edit_approve_generality_records表中
            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
            oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
            oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
            oaEditApproveGeneralityRecords.setData(jsonString);
            oaEditApproveGeneralityRecords.setStatus("0");
            oaEditApproveGeneralityRecords.setCreateBy(nickName);
            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
            int i1 = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录",1,null,null);
            //以下注释代码是项目名称配置不需要的
            //新增本项目负责的用户数据
//            for (Long userId:salesmanList) {
//                //用户为财务
//                salesmanUser.setUserId(userId);
//                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
//            }
//            for (Long userId:financialStaffList) {
//                //用户为业务
//                financialStaffUser.setUserId(userId);
//                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
//            }
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
            if (i1 > 0) {
                Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
                oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaApplyRecordsNewId);
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
                oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectDeployBo.getEditInfo());
                oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
                oaEditApproveGeneralityEditRecords.setConfirmFlag("0");
                oaEditApproveGeneralityEditRecords.setResponsibilityConfirmFlag("0");
                int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录详情信息",1,null,null);
            }
        } else if ("1".equals(editType)) {
            Long oaApplyRecordsOldIdForAddFirst = null;
            //做修改的时候，首先做一个判断，查一下历史数据有没有初始化入库，如果没有，则入一个新增的数据。如果有，则正常业务进行
            //查询所有的编辑记录
            List<OaEditApproveGeneralityEditRecordsVo> oaEditApproveGeneralityEditRecordsVos = oaEditApproveGeneralityEditRecordsMapper.selectAllEditRecordListByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
            if (oaEditApproveGeneralityEditRecordsVos.size() == 0) {
                //超管身份进行落库
                List<SysUser> userList = sysUserMapper.selectUserByRoleKey("admin");
                Long editUserIdOfAdmin = null;
                String adminNickName = null;
                if (userList.size() > 0) {
                    editUserIdOfAdmin = userList.get(0).getUserId();
                    adminNickName = userList.get(0).getNickName();
                }
                OaProjectDeploy oaProjectDeploy2 = oaProjectDeployMapper.selectOaProjectDeployById(oaApplyId);
                oaProjectDeploy2.setAddNotApprove("0");
                String jsonString = JSONObject.toJSONString(oaProjectDeploy2);
                //新增一条记录到oa_edit_approve_generality_records表中
                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
                oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
                oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
                oaEditApproveGeneralityRecords.setData(jsonString);
                oaEditApproveGeneralityRecords.setStatus("0");
                oaEditApproveGeneralityRecords.setCreateBy(adminNickName);
                oaEditApproveGeneralityRecords.setCreateTime(nowDate);
                oaEditApproveGeneralityRecords.setUpdateBy(adminNickName);
                oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
                int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录",1,null,null);
                //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
                if (i > 0) {
                    Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                    OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                    oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
                    oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaApplyRecordsNewId);
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                    oaEditApproveGeneralityEditRecords.setEditUserId(editUserIdOfAdmin);
                    oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                    oaEditApproveGeneralityEditRecords.setEditInfo("超级管理员初始化现存数据");
                    oaEditApproveGeneralityEditRecords.setCheckStatus("9");
                    oaEditApproveGeneralityEditRecords.setRejectFlag("0");
                    oaEditApproveGeneralityEditRecords.setConfirmFlag("1");
                    int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
                    sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录详情信息",1,null,null);
                    //新增的新 = 修改的老
                    oaApplyRecordsOldIdForAddFirst = oaApplyRecordsNewId;
                }
            }


            //修改 --> 修改原本有id，所以不用新增
//            Long id = oaProjectDeployBo.getId();
            //以下注释代码是项目名称配置不需要的
            //id给到上面两个用户基础对象
//            salesmanUser.setOaApplyId(oaApplyId);
//            financialStaffUser.setOaApplyId(oaApplyId);
            //获取JSON字符串
            BeanUtil.copyProperties(oaProjectDeployBo, oaProjectDeployDto);
            oaProjectDeployDto.setAddNotApprove("1");
            oaProjectDeployDto.setCreateTime(nowDate);
            String jsonString = JSONObject.toJSONString(oaProjectDeployDto);
            //新增一条记录到oa_edit_approve_generality_records表中
            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
            oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
            oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
            oaEditApproveGeneralityRecords.setData(jsonString);
            oaEditApproveGeneralityRecords.setStatus("0");
            oaEditApproveGeneralityRecords.setCreateBy(nickName);
            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
            int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录",1,null,null);
            //根据主表id删除所有人员 todo 可能不对？
//            int i1 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
            //以下注释代码是项目名称配置不需要的
            //新增本项目负责的用户数据
//            for (Long userId:salesmanList) {
//                //用户为财务
//                salesmanUser.setUserId(userId);
//                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
//            }
//            for (Long userId:financialStaffList) {
//                //用户为业务
//                financialStaffUser.setUserId(userId);
//                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
//            }
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
            if (i > 0) {
                Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
                oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
                if (oaProjectDeployBo.getOaProjectDeployBoOldDataId() == null) {
                    oaProjectDeployBo.setOaProjectDeployBoOldDataId(oaApplyRecordsOldIdForAddFirst);
                }
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaProjectDeployBo.getOaProjectDeployBoOldDataId());
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
                oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectDeployBo.getEditInfo());
                oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
                oaEditApproveGeneralityEditRecords.setConfirmFlag("0");
                oaEditApproveGeneralityEditRecords.setResponsibilityConfirmFlag("0");
                int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录详情信息",1,null,null);
            }
            //给业务表进行修改
            oaProjectDeploy.setId(oaApplyId);
            oaProjectDeploy.setAddNotApprove("1");
            int i3 = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"更新项目信息",2,null,null);
        } else if ("2".equals(editType)) {
            //删除 --> 删除原本有id，所以不用新增
//            Long id = oaProjectDeployBo.getId();
            //以下注释代码是项目名称配置不需要的
            //id给到上面两个用户基础对象
//            salesmanUser.setOaApplyId(oaApplyId);
//            financialStaffUser.setOaApplyId(oaApplyId);
            //根据主表id删除所有人员 todo 可能不对？
//            int i1 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
            //以下注释代码是项目名称配置不需要的
            //新增本项目负责的用户数据
//            for (Long userId:salesmanList) {
//                //用户为财务
//                salesmanUser.setUserId(userId);
//                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
//            }
//            for (Long userId:financialStaffList) {
//                //用户为业务
//                financialStaffUser.setUserId(userId);
//                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
//            }
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
            OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
            oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
            oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaProjectDeployBo.getOaProjectDeployBoOldDataId());
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(null);
            oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
            oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
            oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectDeployBo.getEditInfo());
            oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
            oaEditApproveGeneralityEditRecords.setConfirmFlag("0");
            oaEditApproveGeneralityEditRecords.setResponsibilityConfirmFlag("0");
            int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增审批记录详情信息",1,null,null);
            //给业务表进行修改
            oaProjectDeploy.setId(oaApplyId);
            oaProjectDeploy.setAddNotApprove("2");
            int i3 = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"更新项目信息",1,null,null);
        }
        //待办通知
        TopNotify topNotify = new TopNotify();
//        topNotify.setNotifyModule("记账凭证规则发生修改");
        topNotify.setNotifyType("1");
        topNotify.setUrl("/businessInformation/projectDeploy");
        //这里projectId不能为空，否则入库会报错
        topNotify.setProjectId(0L);
        //这里incomeId不能为空，否则入库会报错
        topNotify.setIncomeId(0L);
        topNotify.setViewFlag("0");
        topNotify.setCreateBy(nickName);
        topNotify.setCreateTime(nowDate);
        topNotify.setUpdateBy(nickName);
        topNotify.setUpdateTime(nowDate);
        //代表通知是项目名称配置的
        topNotify.setOaNotifyType(currentOaApplyType);
        //代表跳转URL的视图为待我审核
        topNotify.setOaNotifyStep("1");
        //OA功能的id
        topNotify.setOaApplyId(oaApplyId);
        if ("9".equals(oaProjectDeploy.getAddNotApprove())) {
            //新增
            topNotify.setNotifyModule("新增项目名称");
            topNotify.setNotifyMsg(nickName + "提交申请创建项目" + "[" + oaProjectDeployBo.getProjectName() + "]" + "，请审核");
        } else if ("1".equals(oaProjectDeploy.getAddNotApprove())) {
            //修改
            topNotify.setNotifyModule("修改项目名称");
            topNotify.setNotifyMsg(nickName + "提交" + "[" + oaProjectDeployBo.getProjectName() + "]" + "的项目名称修改，请审核");
        } else if ("2".equals(oaProjectDeploy.getAddNotApprove())) {
            //删除
            topNotify.setNotifyModule("删除项目名称");
            topNotify.setNotifyMsg(nickName + "提交申请删除" + "[" + oaProjectDeployBo.getProjectName() + "]" + "项目名称，请审核");
        }
        //项目名称配置在提交申请的时候是交给业务管理员申请，后续再添加用户角色到相关用户表
        for (SysUser su:yewuAdminList) {
            topNotify.setDisposeUser(su.getUserId());
            topNotifyMapper.insertTopNotify(topNotify);
        }
        sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"新增通知",1,null,null);
        //以下注释代码是项目名称配置不需要的
//        if (yewuFlag || yewuAdminFlag) {
//            //用户为业务或者是业务管理员，通知财务去待办
//            for (Long userId:salesmanList) {
//                //用户为财务
//                topNotify.setDisposeUser(userId);
//                topNotifyMapper.insertTopNotify(topNotify);
//            }
//        } else if (caiwuFlag || caiwuAdminFlag) {
//            //用户为财务或者是财务管理员，通知财务去待办
//            for (Long userId:financialStaffList) {
//                //用户为业务
//                topNotify.setDisposeUser(userId);
//                topNotifyMapper.insertTopNotify(topNotify);
//            }
//        }
        return 1;
    }

    @Override
    public OaEditApproveGeneralityEditRecordsVo selectOaEditApproveGeneralityEditRecordsDetailByOaApplyId(Long oaApplyId) {
        PageHelper.clearPage();
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId, "0");
        if (oaEditApproveGeneralityEditRecords == null) {
            PageHelper.clearPage();
            oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyIdAndOaNotifyStepThree(currentOaApplyType, oaApplyId, "0");
        }
        OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo = new OaEditApproveGeneralityEditRecordsVo();
        BeanUtil.copyProperties(oaEditApproveGeneralityEditRecords, oaEditApproveGeneralityEditRecordsVo);
        if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() == null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() != null) {
            //新增
            oaEditApproveGeneralityEditRecordsVo.setApplyType("0");
        } else if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() != null) {
            //修改
            oaEditApproveGeneralityEditRecordsVo.setApplyType("1");
        } else if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() == null) {
            //删除
            oaEditApproveGeneralityEditRecordsVo.setApplyType("2");
        }
        if ("0".equals(oaEditApproveGeneralityEditRecordsVo.getCheckStatus())) {
            oaEditApproveGeneralityEditRecordsVo.setIdentity("财务负责人");
        } else if ("1".equals(oaEditApproveGeneralityEditRecordsVo.getCheckStatus())) {
            oaEditApproveGeneralityEditRecordsVo.setIdentity("业务负责人");
        }
        //查询财务管理员和业务管理员
        List<SysUser> caiwuAdminList = sysUserMapper.selectUserByRoleKey("caiwuAdmin");
        List<SysUser> yewuAdminList = sysUserMapper.selectUserByRoleKey("yewuAdmin");
        List<SysUser> adminList = sysUserMapper.selectUserByRoleKey("admin");
        OaEditApproveGeneralityUser editUser = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyIdAndUserId(currentOaApplyType, oaApplyId, oaEditApproveGeneralityEditRecords.getEditUserId());
        if (editUser != null) {
            oaEditApproveGeneralityEditRecordsVo.setEditIdentity(editUser.getUserFlag());
        } else {
            List<SysUser> caiwuAdmin = caiwuAdminList.stream().filter(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
            if (caiwuAdmin.size() == 0) {
                List<SysUser> yewuAdmin = yewuAdminList.stream().filter(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
                if (yewuAdmin.size() == 0) {
                    boolean b = adminList.stream().anyMatch(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId()));
                    if (b) {
                        oaEditApproveGeneralityEditRecordsVo.setEditIdentity("9");
                    } else {
                        oaEditApproveGeneralityEditRecordsVo.setEditIdentity(null);
                    }
                } else {
                    oaEditApproveGeneralityEditRecordsVo.setEditIdentity("3");
                }
            } else {
                oaEditApproveGeneralityEditRecordsVo.setEditIdentity("2");
            }
        }
        OaEditApproveGeneralityUser checkUser = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyIdAndUserId(currentOaApplyType, oaApplyId, oaEditApproveGeneralityEditRecords.getCheckUserId());
        if (checkUser != null) {
            oaEditApproveGeneralityEditRecordsVo.setEditIdentity(checkUser.getUserFlag());
        } else {
            List<SysUser> caiwuAdmin = caiwuAdminList.stream().filter(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
            if (caiwuAdmin.size() == 0) {
                List<SysUser> yewuAdmin = yewuAdminList.stream().filter(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
                if (yewuAdmin.size() == 0) {
                    oaEditApproveGeneralityEditRecordsVo.setEditIdentity(null);
                } else {
                    oaEditApproveGeneralityEditRecordsVo.setEditIdentity("3");
                }
            } else {
                oaEditApproveGeneralityEditRecordsVo.setEditIdentity("2");
            }
        }
        return oaEditApproveGeneralityEditRecordsVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int checkOaEditApproveGenerality(OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo, LoginUser loginUser) {
        Long oaApplyId = oaEditApproveGeneralityEditRecordsVo.getOaApplyId();
        Date nowDate = DateUtils.getNowDate();
        //当前用户的姓名
        String nickName = loginUser.getUser().getNickName();
        //当前用户的用户id
        Long currentUserId = loginUser.getUserId();
        //提交人id（也就是编辑发起人）
        Long editUserId = oaEditApproveGeneralityEditRecordsVo.getEditUserId();
        //提交人对象
        SysUser editUser = sysUserMapper.selectUserById(editUserId);
        String rejectFlag = oaEditApproveGeneralityEditRecordsVo.getRejectFlag();
        String deleteFlag = StringUtils.EMPTY;
        if ("0".equals(rejectFlag)) {
            //通过之后，落实修改
            //落实修改有三种情况 新增 修改 删除 -----> 对应的调取之前的三个入库方法
            Long oaApplyRecordsOldId = oaEditApproveGeneralityEditRecordsVo.getOaApplyRecordsOldId();
            Long oaApplyRecordsNewId = oaEditApproveGeneralityEditRecordsVo.getOaApplyRecordsNewId();
            if (oaApplyRecordsOldId == null && oaApplyRecordsNewId != null) {
                //上面提交的时候已经新增完成了，直接修改状态即可
                OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
                oaProjectDeploy.setId(oaApplyId);
                //0 - 代表正常状态
                oaProjectDeploy.setAddNotApprove("0");
                int i = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"更新项目信息",2,null,null);
                deleteFlag = "0";
            } else if (oaApplyRecordsOldId != null && oaApplyRecordsNewId != null) {
                //修改
                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = oaEditApproveGeneralityRecordsMapper.selectOaEditApproveGeneralityRecordsById(oaApplyRecordsNewId);
                String data = oaEditApproveGeneralityRecords.getData();
                OaProjectDeployBo oaProjectDeployBo = JSON.toJavaObject(JSON.parseObject(data), OaProjectDeployBo.class);
//                OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
//                BeanUtil.copyProperties(oaProjectDeployDto, oaProjectDeploy);
                oaProjectDeployBo.setAddNotApprove("1");
                Long ii = this.newInsertOrUpdateOaProjectDeploy(oaProjectDeployBo,loginUser);
//                int i = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
                deleteFlag = "1";
            } else if (oaApplyRecordsOldId != null && oaApplyRecordsNewId == null) {
                //删除
//                String applyType = oaEditApproveGeneralityEditRecordsVo.getApplyType();
                OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
                oaProjectDeploy.setId(oaApplyId);
                oaProjectDeploy.setAddNotApprove("2");
                int i = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), FunctionNodeEnum.PROJECTNA.getCode(),"更新项目信息",1,null,null);
                deleteFlag = "2";
            }
        }
        //项目名称配置，在这一步进行人员的入库
        List<Long> salesmanList = oaEditApproveGeneralityEditRecordsVo.getSalesmanList().stream().filter(t -> !t.equals(editUserId)).collect(Collectors.toList());
        List<Long> financialStaffList = oaEditApproveGeneralityEditRecordsVo.getFinancialStaffList().stream().filter(t -> !t.equals(editUserId)).collect(Collectors.toList());
        //提交人加入业务负责人列表中
//        financialStaffList.add(editUserId);
        OaEditApproveGeneralityUser salesmanUser = new OaEditApproveGeneralityUser();
        //oaApplyType为4代表事项目名称配置功能
        salesmanUser.setOaApplyType(currentOaApplyType);
        salesmanUser.setOaApplyId(oaApplyId);
        salesmanUser.setUserFlag("0");
        salesmanUser.setStatus("0");
        salesmanUser.setCreateBy(nickName);
        salesmanUser.setCreateTime(nowDate);
        salesmanUser.setUpdateBy(nickName);
        salesmanUser.setUpdateTime(nowDate);
        OaEditApproveGeneralityUser financialStaffUser = new OaEditApproveGeneralityUser();
        //oaApplyType为4代表事项目名称配置功能
        financialStaffUser.setOaApplyType(currentOaApplyType);
        financialStaffUser.setOaApplyId(oaApplyId);
        financialStaffUser.setUserFlag("1");
        financialStaffUser.setStatus("0");
        financialStaffUser.setCreateBy(nickName);
        financialStaffUser.setCreateTime(nowDate);
        financialStaffUser.setUpdateBy(nickName);
        financialStaffUser.setUpdateTime(nowDate);
        //新增本项目负责的用户数据
        for (Long userId:salesmanList) {
            //用户为财务
            salesmanUser.setUserId(userId);
            int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
        }
        for (Long userId:financialStaffList) {
            //用户为业务
            financialStaffUser.setUserId(userId);
            int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
        }
        //提交人再加入业务负责人
//        financialStaffUser.setUserId(editUserId);
//        int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
        //至此，人员入库结束
        //更新oa_edit_approve_generality_edit_records表状态
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
        oaEditApproveGeneralityEditRecords.setId(oaEditApproveGeneralityEditRecordsVo.getId());
        oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
        oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
        oaEditApproveGeneralityEditRecords.setCheckUserId(currentUserId);
        oaEditApproveGeneralityEditRecords.setCheckTime(nowDate);
        oaEditApproveGeneralityEditRecords.setRejectFlag(rejectFlag);
        oaEditApproveGeneralityEditRecords.setCheckRejectInfo(oaEditApproveGeneralityEditRecordsVo.getCheckRejectInfo());
        //整个流程还没结束，流程依然是未知悉状态
        //把之前提交的待办状态改掉
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyType("1");
        topNotify.setOaNotifyType(currentOaApplyType);
        topNotify.setOaNotifyStep("1");
        topNotify.setOaApplyId(oaApplyId);
        List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
        //通过主表id找到当前项目的所有审批中的通知
        List<Long> collect = topNotifies.stream().map(TopNotify::getId).collect(Collectors.toList());
        //修改之前的待办
        if (collect.size() > 0) {
            int a = topNotifyMapper.updateTopNotifyTypeAndViewFlagByIds(collect, "0", "1", "0");
        }
        //新的待办
        OaProjectDeploy oaProjectDeploy = oaProjectDeployMapper.selectOaProjectDeployById(oaApplyId);
        TopNotify topNotify1 = new TopNotify();
//        topNotify1.setNotifyModule("项目名称配置发生修改");
        topNotify1.setNotifyType("1");
        topNotify1.setUrl("/businessInformation/projectDeploy");
        //这里projectId不能为空，否则入库会报错
        topNotify1.setProjectId(0L);
        //这里incomeId不能为空，否则入库会报错
        topNotify1.setIncomeId(0L);
        topNotify1.setViewFlag("0");
        topNotify1.setCreateBy(nickName);
        topNotify1.setCreateTime(nowDate);
        topNotify1.setUpdateBy(nickName);
        topNotify1.setUpdateTime(nowDate);
        //代表通知是项目名称配置的
        topNotify1.setOaNotifyType(currentOaApplyType);
        //代表跳转URL的视图为我的提交
        topNotify1.setOaNotifyStep("2");
        //OA功能的id
        topNotify1.setOaApplyId(oaApplyId);
        if ("0".equals(deleteFlag)) {
            //新增
            topNotify1.setNotifyModule("新增项目名称");
            if ("0".equals(rejectFlag)) {
                //通过  -->  通过后给提交人、业务责任人和财务责任人发待办
                topNotify1.setDisposeUser(editUserId);
                topNotify1.setNotifyMsg("您提交的新增[" + oaProjectDeploy.getProjectName() + "]项目名称已生效");
                int i = topNotifyMapper.insertTopNotify(topNotify1);
                if (i > 0) {
                    //代表跳转URL的视图为我负责的
                    topNotify1.setOaNotifyStep("3");
                    String editUserNickName = editUser.getNickName();
                    topNotify1.setNotifyMsg(editUserNickName + "提交新增[" + oaProjectDeploy.getProjectName() + "]项目名称，已生效");
                    for (Long userId:salesmanList) {
                        topNotify1.setDisposeUser(userId);
                        topNotifyMapper.insertTopNotify(topNotify1);
                    }
                    for (Long userId:financialStaffList) {
                        topNotify1.setDisposeUser(userId);
                        topNotifyMapper.insertTopNotify(topNotify1);
                    }
                }
            } else {
                //驳回  -->  驳回后给提交人发待办
                topNotify1.setDisposeUser(editUserId);
                topNotify1.setNotifyMsg("您提交的新增[" + oaProjectDeploy.getProjectName() + "]项目名称被驳回");
                int i = topNotifyMapper.insertTopNotify(topNotify1);
            }
        } else if ("1".equals(deleteFlag)) {
            //修改
            topNotify1.setNotifyModule("修改项目名称");
            if ("0".equals(rejectFlag)) {
                //通过  -->  通过后给提交人、业务责任人和财务责任人发待办
                topNotify1.setDisposeUser(editUserId);
                topNotify1.setNotifyMsg("您提交的[" + oaProjectDeploy.getProjectName() + "]项目名称修改已生效");
                int i = topNotifyMapper.insertTopNotify(topNotify1);
                if (i > 0) {
                    //代表跳转URL的视图为我负责的
                    topNotify1.setOaNotifyStep("3");
                    String editUserNickName = editUser.getNickName();
                    topNotify1.setNotifyMsg(editUserNickName + "提交修改[" + oaProjectDeploy.getProjectName() + "]项目名称，已生效");
                    for (Long userId:salesmanList) {
                        topNotify1.setDisposeUser(userId);
                        topNotifyMapper.insertTopNotify(topNotify1);
                    }
                    for (Long userId:financialStaffList) {
                        topNotify1.setDisposeUser(userId);
                        topNotifyMapper.insertTopNotify(topNotify1);
                    }
                }
            } else {
                //驳回  -->  驳回后给提交人发待办
                topNotify1.setDisposeUser(editUserId);
                topNotify1.setNotifyMsg("您提交的[" + oaProjectDeploy.getProjectName() + "]项目名称修改被驳回");
                int i = topNotifyMapper.insertTopNotify(topNotify1);
            }
        } else if ("2".equals(deleteFlag)) {
            //删除
            topNotify1.setNotifyModule("删除项目名称");
            if ("0".equals(rejectFlag)) {
                //通过  -->  通过后给提交人、业务责任人和财务责任人发待办
                topNotify1.setDisposeUser(editUserId);
                topNotify1.setNotifyMsg("您提交的删除[" + oaProjectDeploy.getProjectName() + "]项目名称已生效");
                int i = topNotifyMapper.insertTopNotify(topNotify1);
                if (i > 0) {
                    //代表跳转URL的视图为我负责的
                    topNotify1.setOaNotifyStep("3");
                    String editUserNickName = editUser.getNickName();
                    topNotify1.setNotifyMsg(editUserNickName + "提交删除[" + oaProjectDeploy.getProjectName() + "]项目名称，已生效");
                    for (Long userId:salesmanList) {
                        topNotify1.setDisposeUser(userId);
                        topNotifyMapper.insertTopNotify(topNotify1);
                    }
                    for (Long userId:financialStaffList) {
                        topNotify1.setDisposeUser(userId);
                        topNotifyMapper.insertTopNotify(topNotify1);
                    }
                }
            } else {
                //驳回  -->  驳回后给提交人发待办
                topNotify1.setDisposeUser(editUserId);
                topNotify1.setNotifyMsg("您提交的删除[" + oaProjectDeploy.getProjectName() + "]项目名称被驳回");
                int i = topNotifyMapper.insertTopNotify(topNotify1);
            }
        }
        return oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmOaEditApproveGeneralityEditRecords(OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo, LoginUser loginUser) {
        Long userId = loginUser.getUserId();
        Long oaApplyId = oaEditApproveGeneralityEditRecordsVo.getOaApplyId();
        //要进行做修改的步骤判断
        String oaNotifyStep = oaEditApproveGeneralityEditRecordsVo.getOaNotifyStep();
        PageHelper.clearPage();
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId, "0");
        if ("0".equals(oaNotifyStep) && oaEditApproveGeneralityEditRecords != null) {
            oaNotifyStep = "2";
        }
        if ("3".equals(oaNotifyStep) || oaEditApproveGeneralityEditRecords == null){
            PageHelper.clearPage();
            oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyIdAndOaNotifyStepThree(currentOaApplyType, oaApplyId, "0");
            oaNotifyStep = "3";
        }
        String confirmFlag = oaEditApproveGeneralityEditRecords.getConfirmFlag();
        String responsibilityConfirmFlag = oaEditApproveGeneralityEditRecords.getResponsibilityConfirmFlag();
//        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId, "0");

        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyType("1");
        topNotify.setOaNotifyType(currentOaApplyType);
        topNotify.setOaNotifyStep(oaNotifyStep);
        topNotify.setOaApplyId(oaApplyId);
        List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
        List<Long> collect = topNotifies.stream().map(TopNotify::getId).collect(Collectors.toList());
        //修改之前的待办
        if (collect.size() > 0) {
            int a = topNotifyMapper.updateTopNotifyTypeAndViewFlagByIds(collect, "0", "1", "0");
        }

        //判断是否是删除
        if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() == null) {
            //是否通过
            if ("0".equals(oaEditApproveGeneralityEditRecords.getRejectFlag())) {
                //进行步骤的判断
                if ("2".equals(oaNotifyStep)) {
                    //提交人已经知悉，判断责任人是否知悉
                    if ("1".equals(responsibilityConfirmFlag)) {
                        //责任人也知悉，删除
                        int i = topNotifyMapper.deleteTopNotifyByOaNotifyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                        int i1 = oaEditApproveGeneralityEditRecordsMapper.deleteOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                        int i2 = oaEditApproveGeneralityRecordsMapper.deleteOaEditApproveGeneralityRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                        int i3 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                        int i4 = oaProjectDeployMapper.deleteOaProjectDeployById(oaApplyId);
                        int i5 = projectTypeRelevanceMapper.deleteByProjectId(oaApplyId);
                        int i6 = projectCompanyRelevanceMapper.deleteByProjectId(oaApplyId);
                        return i4;
                    } else {
                        //提交人知悉，责任人不知悉，修改表
                        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = new OaEditApproveGeneralityEditRecords();
                        oaEditApproveGeneralityEditRecords1.setId(oaEditApproveGeneralityEditRecords.getId());
                        oaEditApproveGeneralityEditRecords1.setConfirmFlag("1");
                        int i = oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords1);
                        return  i;
                    }
                } else if ("3".equals(oaNotifyStep)) {
                    //责任人已知悉，判断提交人是否知悉
                    if ("1".equals(confirmFlag)) {
                        //提交人也知悉，删除
                        int i = topNotifyMapper.deleteTopNotifyByOaNotifyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                        int i1 = oaEditApproveGeneralityEditRecordsMapper.deleteOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                        int i2 = oaEditApproveGeneralityRecordsMapper.deleteOaEditApproveGeneralityRecordsByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                        int i3 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
                        int i4 = oaProjectDeployMapper.deleteOaProjectDeployById(oaApplyId);
                        int i5 = projectTypeRelevanceMapper.deleteByProjectId(oaApplyId);
                        int i6 = projectCompanyRelevanceMapper.deleteByProjectId(oaApplyId);
                        return i4;
                    } else {
                        //提交人不知悉，责任人知悉，修改表
                        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = new OaEditApproveGeneralityEditRecords();
                        oaEditApproveGeneralityEditRecords1.setId(oaEditApproveGeneralityEditRecords.getId());
                        oaEditApproveGeneralityEditRecords1.setResponsibilityConfirmFlag("1");
                        int i = oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords1);
                        return  i;
                    }
                }


            }
        }
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = new OaEditApproveGeneralityEditRecords();
        oaEditApproveGeneralityEditRecords1.setId(oaEditApproveGeneralityEditRecords.getId());
        if ("2".equals(oaNotifyStep)) {
            oaEditApproveGeneralityEditRecords1.setConfirmFlag("1");
        } else if ("3".equals(oaNotifyStep)) {
            oaEditApproveGeneralityEditRecords1.setResponsibilityConfirmFlag("1");
        }
        int i = oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords1);
        //状态已经改为了已知悉，改主表的状态
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setId(oaApplyId);
        oaProjectDeploy.setAddNotApprove("0");
        int i1 = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
        return i1;
    }

    @Override
    public Long selectOaProjectDeployListTotal(OaProjectDeploy oaProjectDeploy, List<Long> companyIdList) {
        return oaProjectDeployMapper.selectOaProjectDeployListTotal(oaProjectDeploy, companyIdList);
    }

    @Override
    public Long selectOaProjectDeployListTotalByOaProjectDeployAndCompanyIdListAndFilterOaApplyIdList(OaProjectDeploy oaProjectDeploy, List<Long> companyIdList, List<Long> filterOaApplyId) {
        return oaProjectDeployMapper.selectOaProjectDeployListTotalByOaProjectDeployAndCompanyIdListAndFilterOaApplyIdList(oaProjectDeploy, companyIdList, filterOaApplyId);
    }

    @Override
    public Map<String, Object> viewCount(OaProjectDeploy oaProjectDeploy, LoginUser loginUser) {
        //查全部的条数
        Map<String, Object> paramMap = this.selectListBeforeParam(loginUser, "0", "4");
        List<Long> companyIdList = (List<Long>) paramMap.get("companyIdList");
        Long allCount = oaProjectDeployMapper.selectOaProjectDeployListTotal(oaProjectDeploy, companyIdList);
        if (allCount == null) {
            allCount = 0L;
        }
        //查待我审核的条数
        Map<String, Object> paramMap1 = this.selectListBeforeParam(loginUser, "1", "4");
        List<Long> companyIdList1 = (List<Long>) paramMap1.get("companyIdList");
        List<Long> filterOaApplyId1 = (List<Long>) paramMap1.get("filterOaApplyId");
        Long myCheckCount = null;
        if (filterOaApplyId1 != null) {
            myCheckCount = oaProjectDeployMapper.selectOaProjectDeployListTotalByOaProjectDeployAndCompanyIdListAndFilterOaApplyIdList(oaProjectDeploy, companyIdList1, filterOaApplyId1);
        }
        if (myCheckCount == null) {
            myCheckCount = 0L;
        }
        //查我的提交的条数
        Map<String, Object> paramMap2 = this.selectListBeforeParam(loginUser, "2", "4");
        List<Long> companyIdList2 = (List<Long>) paramMap2.get("companyIdList");
        List<Long> filterOaApplyId2 = (List<Long>) paramMap2.get("filterOaApplyId");
        Long mySubmitCount = null;
        if (filterOaApplyId2 != null) {
            mySubmitCount = oaProjectDeployMapper.selectOaProjectDeployListTotalByOaProjectDeployAndCompanyIdListAndFilterOaApplyIdList(oaProjectDeploy, companyIdList2, filterOaApplyId2);
        }
        if (mySubmitCount == null) {
            mySubmitCount = 0L;
        }
        //查我负责的的条数
        Map<String, Object> paramMap3 = this.selectListBeforeParam(loginUser, "3", "4");
        List<Long> companyIdList3 = (List<Long>) paramMap3.get("companyIdList");
        List<Long> filterOaApplyId3 = (List<Long>) paramMap3.get("filterOaApplyId");
        Long myResponsibleCount = null;
        if (filterOaApplyId3 != null) {
            myResponsibleCount = oaProjectDeployMapper.selectOaProjectDeployListTotalByOaProjectDeployAndCompanyIdListAndFilterOaApplyIdList(oaProjectDeploy, companyIdList3, filterOaApplyId3);
        }
        if (myResponsibleCount == null) {
            myResponsibleCount = 0L;
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("allCount", allCount);
        returnMap.put("myCheckCount", myCheckCount);
        returnMap.put("mySubmitCount", mySubmitCount);
        returnMap.put("myResponsibleCount", myResponsibleCount);
        return returnMap;
    }

    @Override
    public int addDutyUser(OaProjectDeployBo oaProjectDeployBo, LoginUser loginUser) {
        int i = 0;
        List<Long> salesmanList = oaProjectDeployBo.getSalesmanList();
        List<Long> financialStaffList = oaProjectDeployBo.getFinancialStaffList();
        Long oaApplyId = oaProjectDeployBo.getId();
        Date nowDate = DateUtils.getNowDate();
        //当前用户的姓名
        String nickName = loginUser.getUser().getNickName();
        OaEditApproveGeneralityUser salesmanUser = new OaEditApproveGeneralityUser();
        //oaApplyType为4代表事项目名称配置功能
        salesmanUser.setOaApplyType(currentOaApplyType);
        salesmanUser.setOaApplyId(oaApplyId);
        salesmanUser.setUserFlag("0");
        salesmanUser.setStatus("0");
        salesmanUser.setCreateBy(nickName);
        salesmanUser.setCreateTime(nowDate);
        salesmanUser.setUpdateBy(nickName);
        salesmanUser.setUpdateTime(nowDate);
        OaEditApproveGeneralityUser financialStaffUser = new OaEditApproveGeneralityUser();
        //oaApplyType为4代表事项目名称配置功能
        financialStaffUser.setOaApplyType(currentOaApplyType);
        financialStaffUser.setOaApplyId(oaApplyId);
        financialStaffUser.setUserFlag("1");
        financialStaffUser.setStatus("0");
        financialStaffUser.setCreateBy(nickName);
        financialStaffUser.setCreateTime(nowDate);
        financialStaffUser.setUpdateBy(nickName);
        financialStaffUser.setUpdateTime(nowDate);
        //新增本项目负责的用户数据
        if(null!= salesmanList && salesmanList.size()>0){
            oaEditApproveGeneralityUserMapper.deleteDataByAppIdAndUserType(currentOaApplyType,oaApplyId,"0");
            for (Long userId:salesmanList) {
                //用户为财务
                salesmanUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
                i=i+i2;
            }
        }
       if(null!= financialStaffList && financialStaffList.size()>0){
           oaEditApproveGeneralityUserMapper.deleteDataByAppIdAndUserType(currentOaApplyType,oaApplyId,"1");
             for (Long userId:financialStaffList) {
               //用户为业务
               financialStaffUser.setUserId(userId);
               int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
               // add by niey  在【业务信息配置-项目名称】中当业务管理员修改了业务责任人，则在项目立项管理中，项目负责人也同步变更，他们是同一人
               xmglProjectService.updateBusinessUser(financialStaffUser, financialStaffList);
               i=i+i2;
             }
   }


        return i;
    }

    @Override
    public int deleteDutyUser(OaProjectDeploy oaProjectDeploy, LoginUser loginUser) {
        int i = oaEditApproveGeneralityUserMapper.deleteDataByAppIdAndUserIdAndUserType(currentOaApplyType,oaProjectDeploy.getId(),oaProjectDeploy.getUserid(),oaProjectDeploy.getUserFlag());
        //删除立项项目的项目负责人
        Long id = oaProjectDeploy.getId();
        //根据项目名称id查询对应的立项项目id
        XmglDeployProject xmglDeployProject = xmglProjectDeployServiceImpl.selectXmglProjectDeployByDeployId(id);
        xmglProjectUserMapper.updateXmglProjectUserByProjectId(xmglDeployProject.getProjectId());
        return i;
    }

    @Override
    public List<OaProjectDeploy> selectProjectByComPanyId(OaProjectDeploy oaProjectDeploy, LoginUser loginUser) {

        return oaProjectDeployMapper.selectProjectByCompanyId(oaProjectDeploy.getId());

    }

    @Override
    public Map<String, Object> queryCooperationUnit(OaProjectDeploy oaProjectDeploy, LoginUser loginUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        //担保公司 0
        Map<String,Object> custMap =  oaProjectDeployMapper.queryCooperationUnit(oaProjectDeploy.getId(),"0");
        returnMap.put("custNum",custMap.get("num"));
        //资产方 1
        Map<String,Object> partnerMap =oaProjectDeployMapper.queryCooperationUnit(oaProjectDeploy.getId(),"1");
        returnMap.put("partnerNum",partnerMap.get("num"));
        //资金方 2
        Map<String,Object> fundMap =oaProjectDeployMapper.queryCooperationUnit(oaProjectDeploy.getId(),"2");
        returnMap.put("fundNum",fundMap.get("num"));

        return returnMap;
    }

    @Override
    public List<OaProjectDeploy> getDataByTypelist(OaProjectDeploy oaProjectDeploy) {
        return oaProjectDeployMapper.getDataByTypelist(oaProjectDeploy.getTypeData(),oaProjectDeploy.getTypeId());
    }

    @Override
    public List<OaProjectDeploy> getSelectList(OaProjectDeploy oaProjectDeploy) {
        return oaProjectDeployMapper.selectOaProjectDeployList(oaProjectDeploy);
    }

    @Override
    public List<Map<String, Object>> getprojectTypeList(OaProjectDeploy oaProjectDeploy) {
        return oaProjectDeployMapper.getProjectTypeList(oaProjectDeploy.getId());
    }

    @Override
    public Map<String, Object> getDataIsReference(OaProjectDeploy oaProjectDeploy) {

        HashMap<String, Object> retunMap = new HashMap<>();
        int numByprojectId = cwProjectMapper.getNumByprojectId(oaProjectDeploy.getId().toString());
        List<String> arrayList = new ArrayList<>();
        if(numByprojectId>0){
            retunMap.put("isUse","true");
            arrayList.add("财务项目管理");
        }else {
            retunMap.put("isUse","false");
        }
        retunMap.put("model",arrayList);
//        HashMap<String, Object> cdProject = new HashMap<>();
//        cdProject.put("model","车抵贷绿本出入库");
//        cdProject.put("isUse","false");
//        HashMap<String, Object> xmlxProject = new HashMap<>();
//        xmlxProject.put("model","项目立项管理");
//        xmlxProject.put("isUse","false");
        return retunMap;
    }

    @Override
    public List<SysUser> getPostByCode(SysPost sysPost) {
        return sysUserMapper.getUserByPostName(sysPost.getPostName());
    }

    /**
     * 项目名称新增查重
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaProjectDeployBo selectProjectDeployInfoById(Long id)
    {
        OaProjectDeploy oaProjectDeploy = oaProjectDeployMapper.selectDataById(id);
        OaProjectDeployBo oaProjectDeployBo = new OaProjectDeployBo();
        BeanUtil.copyProperties(oaProjectDeploy, oaProjectDeployBo);
        //担保公司
//        List<ProjectCompanyRelevance> custList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("0", id);
//        oaProjectDeployBo.setCustList(custList);
//        //资产方
//        List<ProjectCompanyRelevance> partnerList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("1", id);
//        oaProjectDeployBo.setPartnerList(partnerList);
//        //资金方
//        List<ProjectCompanyRelevance> fundList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("2", id);
//        oaProjectDeployBo.setFundList(fundList);
        //其他
        List<ProjectCompanyRelevance> otherUnitList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("3", id);
        oaProjectDeployBo.setOtherUnitList(otherUnitList);
        return oaProjectDeployBo;
    }

    /**
     * 新增逻辑 项目信息 - 收付款信息
     *
     * @param oaProjectDeployReceiptAndPaymentInfoVo1 项目信息-收付款信息
     * @param loginUser 当前登录用户
     * @return oaProjectDeployId 项目id，通过这个id可以找到对应的收付款信息 和 receiptAndPaymentInfoIdList 项目信息 - 收付款信息的id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> insertReceiptAndPaymentInfo(OaProjectDeployReceiptAndPaymentInfoVo1 oaProjectDeployReceiptAndPaymentInfoVo1, LoginUser loginUser)
    {
        List<OaProjectDeployReceiptAndPaymentInfoVo> oaProjectDeployReceiptAndPaymentInfoVoList = oaProjectDeployReceiptAndPaymentInfoVo1.getOaProjectDeployReceiptAndPaymentInfoVoList();
        List<Long> receiptAndPaymentInfoIdList = new ArrayList<>();
        Long oaProjectDeployId = oaProjectDeployReceiptAndPaymentInfoVoList.get(0).getOaProjectDeployId();
        Date nowDate = DateUtils.getNowDate();
        for (OaProjectDeployReceiptAndPaymentInfoVo oaProjectDeployReceiptAndPaymentInfoVo : oaProjectDeployReceiptAndPaymentInfoVoList) {
            String receiptAndPaymentType = oaProjectDeployReceiptAndPaymentInfoVo.getReceiptAndPaymentType();
            Map<String, Object> oaProjectDeployReceiptAndPaymentInfo = oaProjectDeployReceiptAndPaymentInfoVo.getOaProjectDeployReceiptAndPaymentInfo();
            List<Map<String, Object>> orderByrRceiptAndPaymentType = (List<Map<String, Object>>) oaProjectDeployReceiptAndPaymentInfo.get("orderByrRceiptAndPaymentType");
            for (Map<String, Object> map : orderByrRceiptAndPaymentType) {
                String itemName = map.get("itemName").toString();
                Object remarkObj = map.get("remark");
                String remark = null;
                if (remarkObj != null) {
                    remark = remarkObj.toString();
                }
                List<Map<String, Object>> orderByItemName = (List<Map<String, Object>>) map.get("orderByItemName");
                for (Map<String, Object> stringObjectMap : orderByItemName) {
                    Integer serialNum = (Integer) stringObjectMap.get("serialNum");
                    List<Map<String, Object>> orderBySerialNum = (List<Map<String, Object>>) stringObjectMap.get("orderBySerialNum");
                    for (Map<String, Object> objectMap : orderBySerialNum) {
//                        String traderType = objectMap.get("traderType").toString();
//                        String accountName = objectMap.get("accountName").toString();
//                        String inputType = objectMap.get("inputType").toString();
//                        String accountNumber = objectMap.get("accountNumber").toString();
//                        String bankOfDeposit = objectMap.get("bankOfDeposit").toString();
                        Object traderTypeObj = objectMap.get("traderType");
                        String traderType = null;
                        if (traderTypeObj != null) {
                            traderType = objectMap.get("traderType").toString();
                        }
                        Object accountNameObj = objectMap.get("accountName");
                        String accountName = null;
                        if (accountNameObj != null) {
                            accountName = objectMap.get("accountName").toString();
                        }
                        Object inputTypeObj = objectMap.get("inputType");
                        String inputType = null;
                        if (inputTypeObj != null) {
                            inputType = objectMap.get("inputType").toString();
                        }
                        Object accountNumberObj = objectMap.get("accountNumber");
                        String accountNumber = null;
                        if (accountNumberObj != null) {
                            accountNumber = objectMap.get("accountNumber").toString();
                        }
                        Object bankOfDepositObj = objectMap.get("bankOfDeposit");
                        String bankOfDeposit = null;
                        if (bankOfDepositObj != null) {
                            bankOfDeposit = objectMap.get("bankOfDeposit").toString();
                        }
                        Object idObj = objectMap.get("id");
                        Long id = null;
                        if (idObj != null) {
                            id = Long.parseLong(idObj.toString());
                        }
                        Object oaTraderIdObj = objectMap.get("oaTraderId");
                        Long oaTraderId = null;
                        if (oaTraderIdObj != null) {
                            oaTraderId = Long.parseLong(oaTraderIdObj.toString());
                        }
                        if (!StringUtils.EMPTY.equals(inputType) && inputType != null) {
                            OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo1 = new OaProjectDeployReceiptAndPaymentInfo();
                            oaProjectDeployReceiptAndPaymentInfo1.setId(id);
                            oaProjectDeployReceiptAndPaymentInfo1.setOaProjectDeployId(oaProjectDeployId);
                            oaProjectDeployReceiptAndPaymentInfo1.setReceiptAndPaymentType(receiptAndPaymentType);
                            oaProjectDeployReceiptAndPaymentInfo1.setItemName(itemName);
                            oaProjectDeployReceiptAndPaymentInfo1.setRemark(remark);
                            oaProjectDeployReceiptAndPaymentInfo1.setSerialNum(serialNum);
                            oaProjectDeployReceiptAndPaymentInfo1.setInputType(inputType);
                            oaProjectDeployReceiptAndPaymentInfo1.setOaTraderId(oaTraderId);
                            if ("2".equals(inputType) && oaTraderId == null) {
                                oaProjectDeployReceiptAndPaymentInfo1.setTraderType(traderType);
                                oaProjectDeployReceiptAndPaymentInfo1.setAccountName(accountName);
                                oaProjectDeployReceiptAndPaymentInfo1.setAccountNumber(accountNumber);
                                oaProjectDeployReceiptAndPaymentInfo1.setBankOfDeposit(bankOfDeposit);
                            }
                            oaProjectDeployReceiptAndPaymentInfo1.setStatus("1");
                            oaProjectDeployReceiptAndPaymentInfo1.setCreateBy(loginUser.getUser().getNickName());
                            oaProjectDeployReceiptAndPaymentInfo1.setCreateId(loginUser.getUserId());
                            oaProjectDeployReceiptAndPaymentInfo1.setCreateTime(nowDate);
                            oaProjectDeployReceiptAndPaymentInfo1.setUpdateBy(loginUser.getUser().getNickName());
                            oaProjectDeployReceiptAndPaymentInfo1.setUpdateId(loginUser.getUserId());
                            oaProjectDeployReceiptAndPaymentInfo1.setUpdateTime(nowDate);
                            int a = oaProjectDeployMapper.insertReceiptAndPaymentInfo(oaProjectDeployReceiptAndPaymentInfo1);
                            Long id1 = oaProjectDeployReceiptAndPaymentInfo1.getId();
                            receiptAndPaymentInfoIdList.add(id1);
                        }
                    }
                }
            }
        }
        //把涉及到的oaProject表主要状态改了，改成项目审核中
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setId(oaProjectDeployId);
//        oaProjectDeploy.setCheckStatus("1");
        oaProjectDeploy.setTraderProcessFlag("1");
        OaProjectDeploy oaProjectDeploy1 = oaProjectDeployMapper.selectOaProjectDeployById(oaProjectDeployId);
        String deployProcessFlag = oaProjectDeploy1.getDeployProcessFlag();
        String traderProcessFlag = oaProjectDeploy1.getTraderProcessFlag();
        String feeProcessFlag = oaProjectDeploy1.getFeeProcessFlag();
        if ("0".equals(deployProcessFlag) || StringUtils.EMPTY.equals(deployProcessFlag) || deployProcessFlag == null) {
            //说明没有发起项目信息修改
            if ("0".equals(feeProcessFlag) || StringUtils.EMPTY.equals(feeProcessFlag) || feeProcessFlag == null) {
                //说明也没有发起收付款信息修改
                oaProjectDeploy.setCheckStatus("1");
            } else {
                //说明发起了收付款信息修改
                oaProjectDeploy.setCheckStatus("1");
            }
        } else if ("2".equals(deployProcessFlag)){
            //说明发起了项目信息修改
            oaProjectDeploy.setCheckStatus("1");
        } else if ("3".equals(deployProcessFlag)) {
            //说明发起了项目信息删除
            oaProjectDeploy.setCheckStatus("2");
        }
        oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
        sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJECTNAMEOATRADER.getCode(), FunctionNodeEnum.PROJECTNAMEOATRADER.getCode(),"新增项目信息-收付款信息",1,null,null);
        Map<String, String> map = new HashMap<>();
        map.put("oaProjectDeployId", oaProjectDeployId.toString());
        map.put("receiptAndPaymentInfoIdList", receiptAndPaymentInfoIdList.stream().sorted().map(String::valueOf).collect(Collectors.joining(",")));
        return map;
    }

    /**
     * 新增 项目信息 - 收付款信息 审批逻辑
     *
     * @param oaApplyId 项目信息id
     * @param applyIdOfSecondIds 项目信息-收付款信息 id集合（字符串）
     * @param checkStatus 审批是否通过 1-通过 2-驳回
     * @param loginUser 当前登录用户
     * @return oaProjectDeployId 项目id，通过这个id可以找到对应的收付款信息 和 receiptAndPaymentInfoIdList 项目信息 - 收付款信息的id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertReceiptAndPaymentInfoUpdateStatus(Long oaApplyId, String applyIdOfSecondIds, String checkStatus, LoginUser loginUser)
    {
        List<Long> collect = new ArrayList<>();
        if (applyIdOfSecondIds != null || StringUtils.EMPTY.equals(applyIdOfSecondIds)) {
            collect = Arrays.stream(applyIdOfSecondIds.split(",")).map(Long::parseLong).sorted(Collections.reverseOrder()).collect(Collectors.toList());
        }
        Date nowDate = DateUtils.getNowDate();
        int a = 0;
        if ("1".equals(checkStatus)) {
            //审核通过。
            for (Long aLong : collect) {
                OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo1 = new OaProjectDeployReceiptAndPaymentInfo();
                oaProjectDeployReceiptAndPaymentInfo1.setId(aLong);
                oaProjectDeployReceiptAndPaymentInfo1.setStatus("0");
                oaProjectDeployReceiptAndPaymentInfo1.setUpdateBy(loginUser.getUser().getNickName());
                oaProjectDeployReceiptAndPaymentInfo1.setUpdateId(loginUser.getUserId());
                oaProjectDeployReceiptAndPaymentInfo1.setUpdateTime(nowDate);
                oaProjectDeployMapper.updateReceiptAndPaymentInfo(oaProjectDeployReceiptAndPaymentInfo1);
            }
        } else if ("2".equals(checkStatus)) {
            //审核驳回。
            //删除之前入库的数据
            oaProjectDeployMapper.deleteReceiptAndPaymentInfoByIds(collect);
        }
        //判断是否是审核通过的，不管审核通不通过，oa项目表的状态都要改回去
        //修改oa项目表状态

        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setId(oaApplyId);
//        oaProjectDeploy.setCheckStatus("3");
        oaProjectDeploy.setTraderProcessFlag("0");
        OaProjectDeploy oaProjectDeploy1 = oaProjectDeployMapper.selectOaProjectDeployById(oaApplyId);
        String deployProcessFlag = oaProjectDeploy1.getDeployProcessFlag();
        String traderProcessFlag = oaProjectDeploy1.getTraderProcessFlag();
        String feeProcessFlag = oaProjectDeploy1.getFeeProcessFlag();
        if ("0".equals(deployProcessFlag) || StringUtils.EMPTY.equals(deployProcessFlag) || deployProcessFlag == null) {
            //说明没有发起项目信息修改
            if ("0".equals(feeProcessFlag) || StringUtils.EMPTY.equals(feeProcessFlag) || feeProcessFlag == null) {
                //说明也没有发起信息费信息修改
                oaProjectDeploy.setCheckStatus("3");
            } else {
                //说明发起了信息费信息修改
                oaProjectDeploy.setCheckStatus("1");
            }
        } else if ("2".equals(deployProcessFlag)){
            //说明发起了项目信息修改
            oaProjectDeploy.setCheckStatus("1");
        } else if ("3".equals(deployProcessFlag)) {
            //说明发起了项目信息删除
            oaProjectDeploy.setCheckStatus("2");
        }
        a = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
        return a;
    }

    /**
     * 修改 项目信息 - 收付款信息 审批逻辑
     *
     * @param oaApplyId 项目信息id
     * @param applyIdOfSecondIds 项目信息-收付款信息 id集合（字符串）
     * @param oaProjectDeployReceiptAndPaymentInfoVo1 项目信息-收付款信息表
     * @param checkStatus 审批是否通过 1-通过 2-驳回
     * @param loginUser 当前登录用户
     * @return oaProjectDeployId 项目id，通过这个id可以找到对应的收付款信息 和 receiptAndPaymentInfoIdList 项目信息 - 收付款信息的id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateReceiptAndPaymentInfoUpdateStatus(Long oaApplyId, String applyIdOfSecondIds, OaProjectDeployReceiptAndPaymentInfoVo1 oaProjectDeployReceiptAndPaymentInfoVo1, String checkStatus, LoginUser loginUser)
    {
//        List<Long> receiptAndPaymentInfoIdList = Arrays.stream(applyIdOfSecondIds.split(",")).map(Long::parseLong).sorted(Collections.reverseOrder()).collect(Collectors.toList());
        Date nowDate = DateUtils.getNowDate();
        int a = 0;
        if ("1".equals(checkStatus)) {
            //审核通过。
            //获取到之前的id（用于差集状态）
            List<Long> oldIdList = new ArrayList<>();
            List<Long> newIdList = new ArrayList<>();
            List<OaProjectDeployReceiptAndPaymentInfoVo> oaProjectDeployReceiptAndPaymentInfoVoList = oaProjectDeployReceiptAndPaymentInfoVo1.getOaProjectDeployReceiptAndPaymentInfoVoList();
            for (OaProjectDeployReceiptAndPaymentInfoVo oaProjectDeployReceiptAndPaymentInfoVo : oaProjectDeployReceiptAndPaymentInfoVoList) {
                List<Long> oldIdList1 = oaProjectDeployReceiptAndPaymentInfoVo.getOldIdList();
                oldIdList.addAll(oldIdList1);
//            List<OaProjectDeployReceiptAndPaymentInfo> insertOaProjectDeployReceiptAndPaymentInfoList = new ArrayList<>();
//            List<OaProjectDeployReceiptAndPaymentInfo> updateOaProjectDeployReceiptAndPaymentInfoList = new ArrayList<>();


                String receiptAndPaymentType = oaProjectDeployReceiptAndPaymentInfoVo.getReceiptAndPaymentType();
                Map<String, Object> oaProjectDeployReceiptAndPaymentInfo = oaProjectDeployReceiptAndPaymentInfoVo.getOaProjectDeployReceiptAndPaymentInfo();
                List<Map<String, Object>> orderByrRceiptAndPaymentType = (List<Map<String, Object>>) oaProjectDeployReceiptAndPaymentInfo.get("orderByrRceiptAndPaymentType");
                for (Map<String, Object> map : orderByrRceiptAndPaymentType) {
                    String itemName = map.get("itemName").toString();
                    Object remarkObj = map.get("remark");
                    String remark = null;
                    if (remarkObj != null) {
                        remark = remarkObj.toString();
                    }
                    List<Map<String, Object>> orderByItemName = (List<Map<String, Object>>) map.get("orderByItemName");
                    for (Map<String, Object> stringObjectMap : orderByItemName) {
                        Integer serialNum = (Integer) stringObjectMap.get("serialNum");
                        List<Map<String, Object>> orderBySerialNum = (List<Map<String, Object>>) stringObjectMap.get("orderBySerialNum");
                        for (Map<String, Object> objectMap : orderBySerialNum) {
                            Object traderTypeObj = objectMap.get("traderType");
                            String traderType = null;
                            if (traderTypeObj != null) {
                                traderType = objectMap.get("traderType").toString();
                            }
                            Object accountNameObj = objectMap.get("accountName");
                            String accountName = null;
                            if (accountNameObj != null) {
                                accountName = objectMap.get("accountName").toString();
                            }
                            Object inputTypeObj = objectMap.get("inputType");
                            String inputType = null;
                            if (inputTypeObj != null) {
                                inputType = objectMap.get("inputType").toString();
                            }
                            Object accountNumberObj = objectMap.get("accountNumber");
                            String accountNumber = null;
                            if (accountNumberObj != null) {
                                accountNumber = objectMap.get("accountNumber").toString();
                            }
                            Object bankOfDepositObj = objectMap.get("bankOfDeposit");
                            String bankOfDeposit = null;
                            if (bankOfDepositObj != null) {
                                bankOfDeposit = objectMap.get("bankOfDeposit").toString();
                            }
                            Object idObj = objectMap.get("id");
                            Long id = null;
                            if (idObj != null) {
                                id = Long.parseLong(idObj.toString());
                            }
                            Object oaTraderIdObj = objectMap.get("oaTraderId");
                            Long oaTraderId = null;
                            if (oaTraderIdObj != null) {
                                oaTraderId = Long.parseLong(oaTraderIdObj.toString());
                            }
                            if (!StringUtils.EMPTY.equals(inputType) && inputType != null) {
                                OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo1 = new OaProjectDeployReceiptAndPaymentInfo();
                                oaProjectDeployReceiptAndPaymentInfo1.setId(id);
                                oaProjectDeployReceiptAndPaymentInfo1.setOaProjectDeployId(oaApplyId);
                                oaProjectDeployReceiptAndPaymentInfo1.setReceiptAndPaymentType(receiptAndPaymentType);
                                oaProjectDeployReceiptAndPaymentInfo1.setItemName(itemName);
                                oaProjectDeployReceiptAndPaymentInfo1.setRemark(remark);
                                oaProjectDeployReceiptAndPaymentInfo1.setSerialNum(serialNum);
                                oaProjectDeployReceiptAndPaymentInfo1.setInputType(inputType);
                                oaProjectDeployReceiptAndPaymentInfo1.setOaTraderId(oaTraderId);
                                if ("2".equals(inputType) && oaTraderId == null) {
                                    oaProjectDeployReceiptAndPaymentInfo1.setTraderType(traderType);
                                    oaProjectDeployReceiptAndPaymentInfo1.setAccountName(accountName);
                                    oaProjectDeployReceiptAndPaymentInfo1.setAccountNumber(accountNumber);
                                    oaProjectDeployReceiptAndPaymentInfo1.setBankOfDeposit(bankOfDeposit);
                                }
                                oaProjectDeployReceiptAndPaymentInfo1.setStatus("0");
//                            oaProjectDeployReceiptAndPaymentInfo1.setCreateBy(loginUser.getUser().getNickName());
//                            oaProjectDeployReceiptAndPaymentInfo1.setCreateId(loginUser.getUserId());
//                            oaProjectDeployReceiptAndPaymentInfo1.setCreateTime(nowDate);
                                oaProjectDeployReceiptAndPaymentInfo1.setUpdateBy(loginUser.getUser().getNickName());
                                oaProjectDeployReceiptAndPaymentInfo1.setUpdateId(loginUser.getUserId());
                                oaProjectDeployReceiptAndPaymentInfo1.setUpdateTime(nowDate);
                                if (id != null) {
                                    //id不为空，存起来。与老的id求差集。
                                    newIdList.add(id);
                                    oaProjectDeployMapper.updateReceiptAndPaymentInfo(oaProjectDeployReceiptAndPaymentInfo1);
//                            updateOaProjectDeployReceiptAndPaymentInfoList.add(oaProjectDeployReceiptAndPaymentInfo1);
                                } else {
                                    oaProjectDeployReceiptAndPaymentInfo1.setCreateBy(loginUser.getUser().getNickName());
                                    oaProjectDeployReceiptAndPaymentInfo1.setCreateId(loginUser.getUserId());
                                    oaProjectDeployReceiptAndPaymentInfo1.setCreateTime(nowDate);
                                    oaProjectDeployMapper.insertReceiptAndPaymentInfo(oaProjectDeployReceiptAndPaymentInfo1);
//                            insertOaProjectDeployReceiptAndPaymentInfoList.add(oaProjectDeployReceiptAndPaymentInfo1);
                                }
                            }
                        }
                    }
                }
            }
            //处理数据库中的数据
            //首先求差集，老id比新id多的，得到的是要改状态为1的
            List<Long> collect = oldIdList.stream().filter(t -> !newIdList.contains(t)).collect(Collectors.toList());
            for (Long aLong : collect) {
                OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo1 = new OaProjectDeployReceiptAndPaymentInfo();
                oaProjectDeployReceiptAndPaymentInfo1.setId(aLong);
                oaProjectDeployReceiptAndPaymentInfo1.setStatus("1");
                oaProjectDeployMapper.updateReceiptAndPaymentInfo(oaProjectDeployReceiptAndPaymentInfo1);
            }
        } else if ("2".equals(checkStatus)) {
            //审核驳回。
            //维持之前的数据，把驳回的直接删除掉
//            oaProjectDeployMapper.deleteReceiptAndPaymentInfoByIds(receiptAndPaymentInfoIdList);
        }
//        sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJECTNAMEOATRADER.getCode(), FunctionNodeEnum.PROJECTNAMEOATRADER.getCode(),"新增项目信息-收付款信息",1,null,null);
        //判断是否是审核通过的，不管审核通不通过，oa项目表的状态都要改回去
        //修改oa项目表状态
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setId(oaApplyId);
//        oaProjectDeploy.setCheckStatus("3");
        oaProjectDeploy.setTraderProcessFlag("0");
        OaProjectDeploy oaProjectDeploy1 = oaProjectDeployMapper.selectOaProjectDeployById(oaApplyId);
        String deployProcessFlag = oaProjectDeploy1.getDeployProcessFlag();
        String traderProcessFlag = oaProjectDeploy1.getTraderProcessFlag();
        String feeProcessFlag = oaProjectDeploy1.getFeeProcessFlag();
        if ("0".equals(deployProcessFlag) || StringUtils.EMPTY.equals(deployProcessFlag) || deployProcessFlag == null) {
            //说明没有发起项目信息修改
            if ("0".equals(feeProcessFlag) || StringUtils.EMPTY.equals(feeProcessFlag) || feeProcessFlag == null) {
                //说明也没有发起信息费信息修改
                oaProjectDeploy.setCheckStatus("3");
            } else {
                //说明发起了信息费信息修改
                oaProjectDeploy.setCheckStatus("1");
            }
        } else if ("2".equals(deployProcessFlag)){
            //说明发起了项目信息修改
            oaProjectDeploy.setCheckStatus("1");
        } else if ("3".equals(deployProcessFlag)) {
            //说明发起了项目信息删除
            oaProjectDeploy.setCheckStatus("2");
        }
        a = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
        return a;
    }

    /**
     * 删除 项目信息 - 收付款信息 审批逻辑
     *
     * @param oaApplyId 项目信息id
     * @param applyIdOfSecondIds 项目信息-收付款信息 id集合（字符串）
     * @param checkStatus 审批是否通过 1-通过 2-驳回
     * @param loginUser 当前登录用户
     * @return oaProjectDeployId 项目id，通过这个id可以找到对应的收付款信息 和 receiptAndPaymentInfoIdList 项目信息 - 收付款信息的id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteReceiptAndPaymentInfoUpdateStatus(Long oaApplyId, String applyIdOfSecondIds, String checkStatus, LoginUser loginUser)
    {
        List<Long> collect = new ArrayList<>();
        if (applyIdOfSecondIds != null || StringUtils.EMPTY.equals(applyIdOfSecondIds)) {
            collect = Arrays.stream(applyIdOfSecondIds.split(",")).map(Long::parseLong).sorted(Collections.reverseOrder()).collect(Collectors.toList());
        }
        Date nowDate = DateUtils.getNowDate();
        int a = 0;
        if ("1".equals(checkStatus)) {
            //审核通过。
            //因为是删除的逻辑，所以审批通过之后，把数据改成
            for (Long aLong : collect) {
                OaProjectDeployReceiptAndPaymentInfo oaProjectDeployReceiptAndPaymentInfo1 = new OaProjectDeployReceiptAndPaymentInfo();
                oaProjectDeployReceiptAndPaymentInfo1.setId(aLong);
                oaProjectDeployReceiptAndPaymentInfo1.setStatus("1");
                oaProjectDeployReceiptAndPaymentInfo1.setUpdateBy(loginUser.getUser().getNickName());
                oaProjectDeployReceiptAndPaymentInfo1.setUpdateId(loginUser.getUserId());
                oaProjectDeployReceiptAndPaymentInfo1.setUpdateTime(nowDate);
                oaProjectDeployMapper.updateReceiptAndPaymentInfo(oaProjectDeployReceiptAndPaymentInfo1);
            }
        } else if ("2".equals(checkStatus)) {
            //审核驳回。
            //因为是删除的逻辑，所以审批驳回之后，相当于之前的数据没有变化，只是改变了项目信息表 和 审批记录表
        }
        //判断是否是审核通过的，不管审核通不通过，oa项目表的状态都要改回去
        //修改oa项目表状态
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setId(oaApplyId);
//        oaProjectDeploy.setCheckStatus("3");
        oaProjectDeploy.setTraderProcessFlag("0");
        OaProjectDeploy oaProjectDeploy1 = oaProjectDeployMapper.selectOaProjectDeployById(oaApplyId);
        String deployProcessFlag = oaProjectDeploy1.getDeployProcessFlag();
        String traderProcessFlag = oaProjectDeploy1.getTraderProcessFlag();
        String feeProcessFlag = oaProjectDeploy1.getFeeProcessFlag();
        if ("0".equals(deployProcessFlag) || StringUtils.EMPTY.equals(deployProcessFlag) || deployProcessFlag == null) {
            //说明没有发起项目信息修改
            if ("0".equals(feeProcessFlag) || StringUtils.EMPTY.equals(feeProcessFlag) || feeProcessFlag == null) {
                //说明也没有发起信息费信息修改
                oaProjectDeploy.setCheckStatus("3");
            } else {
                //说明发起了信息费信息修改
                oaProjectDeploy.setCheckStatus("1");
            }
        } else if ("2".equals(deployProcessFlag)){
            //说明发起了项目信息修改
            oaProjectDeploy.setCheckStatus("1");
        } else if ("3".equals(deployProcessFlag)) {
            //说明发起了项目信息删除
            oaProjectDeploy.setCheckStatus("2");
        }
        a = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
        return a;
    }

    /**
     * 新增逻辑 项目信息 - 信息费信息
     *
     * @param oaProjectDeployCwProjectFeeInfoVo 项目信息-信息费信息
     * @param loginUser 当前登录用户
     * @return oaProjectDeployId 项目id，通过这个id可以找到对应的收付款信息 和 receiptAndPaymentInfoIdList 项目信息 - 收付款信息的id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> insertCwProjectFeeInfo(OaProjectDeployCwProjectFeeInfoVo oaProjectDeployCwProjectFeeInfoVo, LoginUser loginUser)
    {
        List<OaProjectDeployCwProjectFeeInfo> oaProjectDeployCwProjectFeeInfoList = oaProjectDeployCwProjectFeeInfoVo.getOaProjectDeployCwProjectFeeInfoList();
        List<Long> cwProjectFeeInfoIdList = new ArrayList<>();
        Long oaProjectDeployId = oaProjectDeployCwProjectFeeInfoList.get(0).getOaProjectDeployId();
        Date nowDate = DateUtils.getNowDate();
        for (OaProjectDeployCwProjectFeeInfo oaProjectDeployCwProjectFeeInfo : oaProjectDeployCwProjectFeeInfoList) {
            oaProjectDeployCwProjectFeeInfo.setCreateBy(loginUser.getUser().getNickName());
            oaProjectDeployCwProjectFeeInfo.setCreateId(loginUser.getUserId());
            oaProjectDeployCwProjectFeeInfo.setCreateTime(nowDate);
            oaProjectDeployCwProjectFeeInfo.setUpdateBy(loginUser.getUser().getNickName());
            oaProjectDeployCwProjectFeeInfo.setUpdateId(loginUser.getUserId());
            oaProjectDeployCwProjectFeeInfo.setUpdateTime(nowDate);
            oaProjectDeployMapper.insertCwProjectFeeInfo(oaProjectDeployCwProjectFeeInfo);
            Long id = oaProjectDeployCwProjectFeeInfo.getId();
            cwProjectFeeInfoIdList.add(id);
        }
        //把涉及到的oaProject表主要状态改了，改成项目审核中
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setCwProjectFeeFlag(oaProjectDeployCwProjectFeeInfoVo.getCwProjectFeeFlag());
        oaProjectDeploy.setId(oaProjectDeployId);
//        oaProjectDeploy.setCheckStatus("1");
        oaProjectDeploy.setFeeProcessFlag("1");
        oaProjectDeploy.setCwProjectFeeFlag(oaProjectDeployCwProjectFeeInfoVo.getCwProjectFeeFlag());
        OaProjectDeploy oaProjectDeploy1 = oaProjectDeployMapper.selectOaProjectDeployById(oaProjectDeployId);
        String deployProcessFlag = oaProjectDeploy1.getDeployProcessFlag();
        String traderProcessFlag = oaProjectDeploy1.getTraderProcessFlag();
        String feeProcessFlag = oaProjectDeploy1.getFeeProcessFlag();
        if ("0".equals(deployProcessFlag) || StringUtils.EMPTY.equals(deployProcessFlag) || deployProcessFlag == null) {
            //说明没有发起项目信息修改
            if ("0".equals(traderProcessFlag) || StringUtils.EMPTY.equals(traderProcessFlag) || traderProcessFlag == null) {
                //说明也没有发起收付款信息修改
                oaProjectDeploy.setCheckStatus("1");
            } else {
                //说明发起了收付款信息修改
                oaProjectDeploy.setCheckStatus("1");
            }
        } else if ("2".equals(deployProcessFlag)){
            //说明发起了项目信息修改
            oaProjectDeploy.setCheckStatus("1");
        } else if ("3".equals(deployProcessFlag)) {
            //说明发起了项目信息删除
            oaProjectDeploy.setCheckStatus("2");
        }
        oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
        sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJECTNAMECWPROJ.getCode(), FunctionNodeEnum.PROJECTNAMECWPROJ.getCode(),"新增项目信息-收付款信息",1,null,null);
        Map<String, String> map = new HashMap<>();
        map.put("oaProjectDeployId", oaProjectDeployId.toString());
        map.put("cwProjectFeeInfoIdList", cwProjectFeeInfoIdList.stream().sorted().map(String::valueOf).collect(Collectors.joining(",")));
        return map;
    }

    /**
     * 新增 项目信息 - 信息费信息 审批逻辑
     *
     * @param oaApplyId 项目信息id
     * @param applyIdOfSecondIds 项目信息-信息费信息 id集合（字符串）
     * @param checkStatus 审批是否通过 1-通过 2-驳回
     * @param loginUser 当前登录用户
     * @return oaProjectDeployId 项目id，通过这个id可以找到对应的收付款信息 和 receiptAndPaymentInfoIdList 项目信息 - 收付款信息的id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertCwProjectFeeInfoUpdateStatus(Long oaApplyId, String applyIdOfSecondIds, String checkStatus, LoginUser loginUser)
    {
        List<Long> collect = new ArrayList<>();
        if (applyIdOfSecondIds != null || StringUtils.EMPTY.equals(applyIdOfSecondIds)) {
            collect = Arrays.stream(applyIdOfSecondIds.split(",")).map(Long::parseLong).sorted(Collections.reverseOrder()).collect(Collectors.toList());
        }
        Date nowDate = DateUtils.getNowDate();
        int a = 0;
        if ("1".equals(checkStatus)) {
            //审核通过。
            for (Long aLong : collect) {
                OaProjectDeployCwProjectFeeInfo oaProjectDeployCwProjectFeeInfo = new OaProjectDeployCwProjectFeeInfo();
                oaProjectDeployCwProjectFeeInfo.setId(aLong);
                oaProjectDeployCwProjectFeeInfo.setStatus("0");
                oaProjectDeployCwProjectFeeInfo.setUpdateBy(loginUser.getUser().getNickName());
                oaProjectDeployCwProjectFeeInfo.setUpdateId(loginUser.getUserId());
                oaProjectDeployCwProjectFeeInfo.setUpdateTime(nowDate);
                oaProjectDeployMapper.updateCwProjectFeeInfo(oaProjectDeployCwProjectFeeInfo);
            }
        } else if ("2".equals(checkStatus)) {
            //审核驳回。
            //删除之前入库的数据
            int i = oaProjectDeployMapper.deleteCwProjectFeeInfoByIds(collect);
            return i;
        }
        //判断是否是审核通过的，不管审核通不通过，oa项目表的状态都要改回去
        //修改oa项目表状态
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setId(oaApplyId);
//        oaProjectDeploy.setCheckStatus("3");
        oaProjectDeploy.setFeeProcessFlag("0");
        OaProjectDeploy oaProjectDeploy1 = oaProjectDeployMapper.selectOaProjectDeployById(oaApplyId);
        String deployProcessFlag = oaProjectDeploy1.getDeployProcessFlag();
        String traderProcessFlag = oaProjectDeploy1.getTraderProcessFlag();
        String feeProcessFlag = oaProjectDeploy1.getFeeProcessFlag();
        if ("0".equals(deployProcessFlag) || StringUtils.EMPTY.equals(deployProcessFlag) || deployProcessFlag == null) {
            //说明没有发起项目信息修改
            if ("0".equals(traderProcessFlag) || StringUtils.EMPTY.equals(traderProcessFlag) || traderProcessFlag == null) {
                //说明也没有发起收付款信息修改
                oaProjectDeploy.setCheckStatus("3");
            } else {
                //说明发起了收付款信息修改
                oaProjectDeploy.setCheckStatus("1");
            }
        } else if ("2".equals(deployProcessFlag)){
            //说明发起了项目信息修改
            oaProjectDeploy.setCheckStatus("1");
        } else if ("3".equals(deployProcessFlag)) {
            //说明发起了项目信息删除
            oaProjectDeploy.setCheckStatus("2");
        }
        a = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
        return a;
    }

    /**
     * 修改 项目信息 - 信息费信息 审批逻辑
     *
     * @param oaApplyId 项目信息id
     * @param applyIdOfSecondIds 项目信息-信息费信息 id集合（字符串）
     * @param oaProjectDeployCwProjectFeeInfoVo 项目信息-收付款信息表
     * @param checkStatus 审批是否通过 1-通过 2-驳回
     * @param loginUser 当前登录用户
     * @return oaProjectDeployId 项目id，通过这个id可以找到对应的收付款信息 和 receiptAndPaymentInfoIdList 项目信息 - 收付款信息的id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateCwProjectFeeInfoUpdateStatus(Long oaApplyId, String applyIdOfSecondIds, OaProjectDeployCwProjectFeeInfoVo oaProjectDeployCwProjectFeeInfoVo, String checkStatus, LoginUser loginUser)
    {
//        List<Long> cwProjectFeeInfoIdList = Arrays.stream(applyIdOfSecondIds.split(",")).map(Long::parseLong).sorted(Collections.reverseOrder()).collect(Collectors.toList());
        Date nowDate = DateUtils.getNowDate();
        int a = 0;
        if ("1".equals(checkStatus)) {
            //审核通过。
            //获取到之前的id（用于差集状态）
            List<Long> oldIdList = oaProjectDeployCwProjectFeeInfoVo.getOldIdList();
            List<Long> newIdList = new ArrayList<>();
            List<OaProjectDeployCwProjectFeeInfo> oaProjectDeployCwProjectFeeInfoList = oaProjectDeployCwProjectFeeInfoVo.getOaProjectDeployCwProjectFeeInfoList();
            for (OaProjectDeployCwProjectFeeInfo oaProjectDeployCwProjectFeeInfo : oaProjectDeployCwProjectFeeInfoList) {
                Long id = oaProjectDeployCwProjectFeeInfo.getId();
                oaProjectDeployCwProjectFeeInfo.setUpdateBy(loginUser.getUser().getNickName());
                oaProjectDeployCwProjectFeeInfo.setUpdateId(loginUser.getUserId());
                oaProjectDeployCwProjectFeeInfo.setUpdateTime(nowDate);
                if (id != null) {
                    newIdList.add(id);
                    if (oaProjectDeployCwProjectFeeInfo.getCompanyId() == null) {
                        oaProjectDeployMapper.updateCwProjectFeeInfo1(oaProjectDeployCwProjectFeeInfo);
                    } else {
                        oaProjectDeployMapper.updateCwProjectFeeInfo(oaProjectDeployCwProjectFeeInfo);
                    }
                } else {
                    oaProjectDeployCwProjectFeeInfo.setCreateBy(loginUser.getUser().getNickName());
                    oaProjectDeployCwProjectFeeInfo.setCreateId(loginUser.getUserId());
                    oaProjectDeployCwProjectFeeInfo.setCreateTime(nowDate);
                    oaProjectDeployMapper.insertCwProjectFeeInfo(oaProjectDeployCwProjectFeeInfo);
                }
            }
            //首先求差集，老id比新id多的，得到的是要改状态为1的
            List<Long> collect = oldIdList.stream().filter(t -> !newIdList.contains(t)).collect(Collectors.toList());
            for (Long aLong : collect) {
                OaProjectDeployCwProjectFeeInfo oaProjectDeployCwProjectFeeInfo = new OaProjectDeployCwProjectFeeInfo();
                oaProjectDeployCwProjectFeeInfo.setId(aLong);
                oaProjectDeployCwProjectFeeInfo.setStatus("1");
                oaProjectDeployMapper.updateCwProjectFeeInfo(oaProjectDeployCwProjectFeeInfo);
            }
        } else if ("2".equals(checkStatus)) {

        }
        //判断是否是审核通过的，不管审核通不通过，oa项目表的状态都要改回去
        //修改oa项目表状态
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setId(oaApplyId);
//        oaProjectDeploy.setCheckStatus("3");
        oaProjectDeploy.setFeeProcessFlag("0");
        oaProjectDeploy.setCwProjectFeeFlag(oaProjectDeployCwProjectFeeInfoVo.getCwProjectFeeFlag());
        OaProjectDeploy oaProjectDeploy1 = oaProjectDeployMapper.selectOaProjectDeployById(oaApplyId);
        String deployProcessFlag = oaProjectDeploy1.getDeployProcessFlag();
        String traderProcessFlag = oaProjectDeploy1.getTraderProcessFlag();
        String feeProcessFlag = oaProjectDeploy1.getFeeProcessFlag();
        if ("0".equals(deployProcessFlag) || StringUtils.EMPTY.equals(deployProcessFlag) || deployProcessFlag == null) {
            //说明没有发起项目信息修改
            if ("0".equals(traderProcessFlag) || StringUtils.EMPTY.equals(traderProcessFlag) || traderProcessFlag == null) {
                //说明也没有发起收付款信息修改
                oaProjectDeploy.setCheckStatus("3");
            } else {
                //说明发起了收付款信息修改
                oaProjectDeploy.setCheckStatus("1");
            }
        } else if ("2".equals(deployProcessFlag)){
            //说明发起了项目信息修改
            oaProjectDeploy.setCheckStatus("1");
        } else if ("3".equals(deployProcessFlag)) {
            //说明发起了项目信息删除
            oaProjectDeploy.setCheckStatus("2");
        }
        a = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
        return a;
    }

    /**
     * 删除 项目信息 - 信息费信息 审批逻辑
     *
     * @param oaApplyId 项目信息id
     * @param applyIdOfSecondIds 项目信息-信息费信息 id集合（字符串）
     * @param checkStatus 审批是否通过 1-通过 2-驳回
     * @param loginUser 当前登录用户
     * @return oaProjectDeployId 项目id，通过这个id可以找到对应的收付款信息 和 receiptAndPaymentInfoIdList 项目信息 - 收付款信息的id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteCwProjectFeeInfoUpdateStatus(Long oaApplyId, String applyIdOfSecondIds, String checkStatus, String cwProjectFeeFlag, LoginUser loginUser)
    {
        List<Long> collect = new ArrayList<>();
        if (applyIdOfSecondIds != null || StringUtils.EMPTY.equals(applyIdOfSecondIds)) {
            collect = Arrays.stream(applyIdOfSecondIds.split(",")).map(Long::parseLong).sorted(Collections.reverseOrder()).collect(Collectors.toList());
        }
        Date nowDate = DateUtils.getNowDate();
        int a = 0;
        if ("1".equals(checkStatus)) {
            //审核通过。
            //因为是删除的逻辑，所以审批通过之后，把数据改成
            for (Long aLong : collect) {
                OaProjectDeployCwProjectFeeInfo oaProjectDeployCwProjectFeeInfo = new OaProjectDeployCwProjectFeeInfo();
                oaProjectDeployCwProjectFeeInfo.setId(aLong);
                oaProjectDeployCwProjectFeeInfo.setStatus("1");
                oaProjectDeployCwProjectFeeInfo.setUpdateBy(loginUser.getUser().getNickName());
                oaProjectDeployCwProjectFeeInfo.setUpdateId(loginUser.getUserId());
                oaProjectDeployCwProjectFeeInfo.setUpdateTime(nowDate);
                oaProjectDeployMapper.updateCwProjectFeeInfo(oaProjectDeployCwProjectFeeInfo);
            }
        } else if ("2".equals(checkStatus)) {
            //审核驳回。
            //因为是删除的逻辑，所以审批驳回之后，相当于之前的数据没有变化，只是改变了项目信息表 和 审批记录表
        }
        //判断是否是审核通过的，不管审核通不通过，oa项目表的状态都要改回去
        //修改oa项目表状态
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setId(oaApplyId);
//        oaProjectDeploy.setCheckStatus("3");
        oaProjectDeploy.setFeeProcessFlag("0");
        oaProjectDeploy.setCwProjectFeeFlag(cwProjectFeeFlag);
        OaProjectDeploy oaProjectDeploy1 = oaProjectDeployMapper.selectOaProjectDeployById(oaApplyId);
        String deployProcessFlag = oaProjectDeploy1.getDeployProcessFlag();
        String traderProcessFlag = oaProjectDeploy1.getTraderProcessFlag();
        String feeProcessFlag = oaProjectDeploy1.getFeeProcessFlag();
        if ("0".equals(deployProcessFlag) || StringUtils.EMPTY.equals(deployProcessFlag) || deployProcessFlag == null) {
            //说明没有发起项目信息修改
            if ("0".equals(traderProcessFlag) || StringUtils.EMPTY.equals(traderProcessFlag) || traderProcessFlag == null) {
                //说明也没有发起收付款信息修改
                oaProjectDeploy.setCheckStatus("3");
            } else {
                //说明发起了收付款信息修改
                oaProjectDeploy.setCheckStatus("1");
            }
        } else if ("2".equals(deployProcessFlag)){
            //说明发起了项目信息修改
            oaProjectDeploy.setCheckStatus("1");
        } else if ("3".equals(deployProcessFlag)) {
            //说明发起了项目信息删除
            oaProjectDeploy.setCheckStatus("2");
        }
        a = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
        return a;
    }

    public Map<String, List<ProjectCompanyRelevance>> repliceMap(Map<String, List<ProjectCompanyRelevance>> tableList){
        Map<String, List<ProjectCompanyRelevance>> modifiedTableList = new HashMap<>();
        for (Map.Entry<String, List<ProjectCompanyRelevance>> entry : tableList.entrySet()) {
            String key = entry.getKey();
            List<ProjectCompanyRelevance> value = entry.getValue();
            // 根据 key 的值进行替换
            String newKey;
            switch (key) {
                case "custList":
                    newKey = "0List";
                    break;
                case "partnerList":
                    newKey = "1List";
                    break;
                case "fundList":
                    newKey = "2List";
                    break;
                case "otherList":
                    newKey = "3List";
                    break;
                default:
                    newKey = key; // 如果 key 不是上述值，保留原值
                    break;
            }
            // 将替换后的键值对放入新 Map
            modifiedTableList.put(newKey, value);
        }
        return modifiedTableList;
    }

    public Map<String, List<ProjectCompanyRelevance>> repliceTpyeMap(Map<String, List<ProjectCompanyRelevance>> tableList){
        Map<String, List<ProjectCompanyRelevance>> modifiedTableList = new HashMap<>();
        for (Map.Entry<String, List<ProjectCompanyRelevance>> entry : tableList.entrySet()) {
            String key = entry.getKey();
            List<ProjectCompanyRelevance> value = entry.getValue();
            // 根据 key 的值进行替换
            String newKey;
            switch (key) {
                case "0List":
                    newKey = "custList";
                    break;
                case "1List":
                    newKey = "partnerList";
                    break;
                case "2List":
                    newKey = "fundList";
                    break;
                case "3List":
                    newKey = "otherList";
                    break;
                default:
                    newKey = key; // 如果 key 不是上述值，保留原值
                    break;
            }
            List<ProjectCompanyRelevance> updatedList = value.stream().map(
               item -> { item.setUnitType(newKey.replace("List", ""));
                   return item;}
            ).collect (Collectors.toList()) ;
            // 将替换后的键值对放入新 Map
            modifiedTableList.put(newKey, updatedList);
        }
        return modifiedTableList;
    }
}
