package org.ruoyi.core.oasystem.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaMyUsualProcess;

import java.util.List;


/**
 * OA系统-发起流程-我的常用流程Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-29
 */
public interface OaMyUsualProcessMapper 
{
    /**
     * 查询OA系统-发起流程-我的常用流程
     * 
     * @param id OA系统-发起流程-我的常用流程主键
     * @return OA系统-发起流程-我的常用流程
     */
    public OaMyUsualProcess selectOaMyUsualProcessById(Long id);

    /**
     * 查询OA系统-发起流程-我的常用流程列表
     * 
     * @param oaMyUsualProcess OA系统-发起流程-我的常用流程
     * @return OA系统-发起流程-我的常用流程集合
     */
    public List<OaMyUsualProcess> selectOaMyUsualProcessList(OaMyUsualProcess oaMyUsualProcess);

    /**
     * 新增OA系统-发起流程-我的常用流程
     * 
     * @param oaMyUsualProcess OA系统-发起流程-我的常用流程
     * @return 结果
     */
    public int insertOaMyUsualProcess(OaMyUsualProcess oaMyUsualProcess);

    /**
     * 修改OA系统-发起流程-我的常用流程
     * 
     * @param oaMyUsualProcess OA系统-发起流程-我的常用流程
     * @return 结果
     */
    public int updateOaMyUsualProcess(OaMyUsualProcess oaMyUsualProcess);

    /**
     * 删除OA系统-发起流程-我的常用流程
     * 
     * @param id OA系统-发起流程-我的常用流程主键
     * @return 结果
     */
    public int deleteOaMyUsualProcessById(Long id);

    /**
     * 批量删除OA系统-发起流程-我的常用流程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaMyUsualProcessByIds(Long[] ids);

    int batchInsert(@Param("myUseulList") List<OaMyUsualProcess> myUseulList);

    //通过我的常用流程id和用户id找到这条数据，并返回id用于删除前置（防止越权删除）
    Long selectOaMyUsualProcessByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);
}
