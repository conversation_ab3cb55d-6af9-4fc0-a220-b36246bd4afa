package org.ruoyi.core.oasystem.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.domain.dto.FlowUserAndNotificationDto;
import org.ruoyi.core.oasystem.domain.vo.FlowPrintHistoryVo;
import org.ruoyi.core.oasystem.domain.vo.MyUseulTemplVo;
import org.ruoyi.core.oasystem.domain.vo.ProcessTemplateVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * OA系统-流程模板Service接口
 *
 * <AUTHOR>
 * @date 2023-07-25
 */
public interface IOaProcessTemplateUserService
{
    public List<OaProcessTemplateUser> selectByTemplateId(Long templateId);

    public int batchAddOaProcessTemplateUser(List<OaProcessTemplateUser> oaProcessTemplateUserList);
}