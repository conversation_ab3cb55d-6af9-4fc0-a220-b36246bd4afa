package org.ruoyi.core.oasystem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.oasystem.domain.OaProjectNameRule;
import org.ruoyi.core.oasystem.domain.vo.OaProjectNameRuleVo;
import org.ruoyi.core.oasystem.service.IOaProjectNameRuleService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 特殊产品分类配置Controller
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/project/name/rule")
public class OaProjectNameRuleController extends BaseController
{
    @Autowired
    private IOaProjectNameRuleService oaProjectNameRuleService;

    /**
     * 查询特殊产品分类配置列表
     */
    //@PreAuthorize("@ss.hasPermi('system:rule:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaProjectNameRuleVo oaProjectNameRule)
    {
        startPage();
        List<OaProjectNameRuleVo> list = oaProjectNameRuleService.selectOaProjectNameRuleList(oaProjectNameRule);
        return getDataTable(list);
    }

    /**
     * 导出特殊产品分类配置列表
     */
    //@PreAuthorize("@ss.hasPermi('system:rule:export')")
    @Log(title = "特殊产品分类配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaProjectNameRuleVo oaProjectNameRule)
    {
        List<OaProjectNameRuleVo> list = oaProjectNameRuleService.selectOaProjectNameRuleList(oaProjectNameRule);
        ExcelUtil<OaProjectNameRuleVo> util = new ExcelUtil<OaProjectNameRuleVo>(OaProjectNameRuleVo.class);
        util.exportExcel(response, list, "特殊产品分类配置数据");
    }

    /**
     * 获取特殊产品分类配置详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:rule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaProjectNameRuleService.selectOaProjectNameRuleById(id));
    }

    /**
     * 获取特殊产品分类配置详细信息--在新建项目中
     */
    @PostMapping(value = "/getRule")
    public AjaxResult getOaProjectNameRule(@RequestBody OaProjectNameRuleVo oaProjectNameRule)
    {
        return AjaxResult.success(oaProjectNameRuleService.getOaProjectNameRule(oaProjectNameRule));
    }

    /**
     * 新增特殊产品分类配置
     */
    //@PreAuthorize("@ss.hasPermi('system:rule:add')")
    @Log(title = "特殊产品分类配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaProjectNameRuleVo oaProjectNameRule)
    {
        return toAjax(oaProjectNameRuleService.insertOaProjectNameRule(oaProjectNameRule));
    }

    /**
     * 修改特殊产品分类配置
     */
    //@PreAuthorize("@ss.hasPermi('system:rule:edit')")
    @Log(title = "特殊产品分类配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaProjectNameRuleVo oaProjectNameRule)
    {
        return toAjax(oaProjectNameRuleService.updateOaProjectNameRule(oaProjectNameRule));
    }

    /**
     * 删除特殊产品分类配置
     */
    //@PreAuthorize("@ss.hasPermi('system:rule:remove')")
    @Log(title = "特殊产品分类配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(oaProjectNameRuleService.deleteOaProjectNameRuleById(id));
    }
}
