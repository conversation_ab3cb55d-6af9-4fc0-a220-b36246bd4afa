package org.ruoyi.core.oasystem.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 记账凭证规则 - 审核
 *
 * @Description
 * <AUTHOR>
 * @Date 2023/12/19 14:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CheckOaVoucherRulesMainBo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 审核类型 0-通过 1-驳回 */
    private String checkType;

    /** 驳回原因*/
    private String checkRejectInfo;
}