package org.ruoyi.core.oasystem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 oa_voucher_rules_vice
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public class OaVoucherRulesVice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 规则主表id */
    @Excel(name = "规则主表id")
    private Long rulesMainId;

    /** 摘要 */
    @Excel(name = "摘要")
    private String abstractJson;


    private String isEnableDynamicForm;


    /** 凭证规则类型（0付款方账套，1收款方账套） */
    @Excel(name = "凭证规则类型", readConverterExp = "0=付款方账套，1收款方账套")
    private String rulerType;

    /** 字 */
    @Excel(name = "字")
    private String finanicalWord;

    /** 收款方记账金额字段 */
    @Excel(name = "收款方记账金额字段")
    private String collAccountingField;

    /** 收款方记账金额字段名称 */
    @Excel(name = "收款方记账金额字段名称")
    private String collAccountingFieldName;

    /** 规则名 */
    @Excel(name = "规则名")
    private String name;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setRulesMainId(Long rulesMainId)
    {
        this.rulesMainId = rulesMainId;
    }

    public Long getRulesMainId()
    {
        return rulesMainId;
    }
    public void setAbstractJson(String abstractJson)
    {
        this.abstractJson = abstractJson;
    }

    public String getAbstractJson()
    {
        return abstractJson;
    }
    public void setRulerType(String rulerType)
    {
        this.rulerType = rulerType;
    }

    public String getRulerType()
    {
        return rulerType;
    }
    public void setFinanicalWord(String finanicalWord)
    {
        this.finanicalWord = finanicalWord;
    }

    public String getFinanicalWord()
    {
        return finanicalWord;
    }
    public void setCollAccountingField(String collAccountingField)
    {
        this.collAccountingField = collAccountingField;
    }

    public String getCollAccountingField()
    {
        return collAccountingField;
    }
    public void setCollAccountingFieldName(String collAccountingFieldName)
    {
        this.collAccountingFieldName = collAccountingFieldName;
    }

    public String getCollAccountingFieldName()
    {
        return collAccountingFieldName;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public String getIsEnableDynamicForm() {
        return isEnableDynamicForm;
    }

    public void setIsEnableDynamicForm(String isEnableDynamicForm) {
        this.isEnableDynamicForm = isEnableDynamicForm;
    }

    @Override
    public String toString() {
        return "OaVoucherRulesVice{" +
                "id=" + id +
                ", rulesMainId=" + rulesMainId +
                ", abstractJson='" + abstractJson + '\'' +
                ", isEnableDynamicForm='" + isEnableDynamicForm + '\'' +
                ", rulerType='" + rulerType + '\'' +
                ", finanicalWord='" + finanicalWord + '\'' +
                ", collAccountingField='" + collAccountingField + '\'' +
                ", collAccountingFieldName='" + collAccountingFieldName + '\'' +
                ", name='" + name + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}