package org.ruoyi.core.oasystem.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.ProjectChannelUser;
import org.ruoyi.core.oasystem.domain.ProjectCompanyRelevance;

/**
 * 项目渠道方用户关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-27
 */
public interface ProjectChannelUserMapper 
{
    /**
     * 查询项目渠道方用户关联
     * 
     * @param id 项目渠道方用户关联主键
     * @return 项目渠道方用户关联
     */
    public ProjectChannelUser selectProjectChannelUserById(Long id);

    /**
     * 查询项目渠道方用户关联列表
     * 
     * @param projectChannelUser 项目渠道方用户关联
     * @return 项目渠道方用户关联集合
     */
    public List<ProjectChannelUser> selectProjectChannelUserList(ProjectChannelUser projectChannelUser);

    /**
     * 新增项目渠道方用户关联
     * 
     * @param projectChannelUser 项目渠道方用户关联
     * @return 结果
     */
    public int insertProjectChannelUser(ProjectChannelUser projectChannelUser);

    /**
     * 修改项目渠道方用户关联
     * 
     * @param projectChannelUser 项目渠道方用户关联
     * @return 结果
     */
    public int updateProjectChannelUser(ProjectChannelUser projectChannelUser);

    /**
     * 删除项目渠道方用户关联
     * 
     * @param id 项目渠道方用户关联主键
     * @return 结果
     */
    public int deleteProjectChannelUserById(Long id);

    /**
     * 批量删除项目渠道方用户关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectChannelUserByIds(Long[] ids);


    /**
     * 批量插入
     *
     * @param list 列表
     * @return int
     */
    int batchInsert(@Param("list") List<ProjectChannelUser> list);

    List<Map<String, Object>> selectProjectuserList(@Param("projectId") Long projectId);

    List<ProjectChannelUser> queryDataByProjectId(@Param("projectId") Long projectId);

    int deleteDataByProjectId(@Param("projectId") Long projectId);
}
