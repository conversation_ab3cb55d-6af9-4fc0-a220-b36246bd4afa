package org.ruoyi.core.license.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.ruoyi.core.license.domain.ZzLicenseNotifySend;

import java.util.List;

/**
 * 证照授权Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
@Mapper
public interface ZzLicenseNotifySendMapper
{

    /**
     * 查询证照消息发送列表
     *
     * @param zzLicenseNotifySend 证照消息发送
     * @return 证照消息发送集合
     */
    public List<ZzLicenseNotifySend> selectZzLicenseNotifySendList(ZzLicenseNotifySend zzLicenseNotifySend);

    /**
     * 查询证照消息发送
     *
     * @param licenseId 证照消息发送主键
     * @return 证照消息发送
     */
    public ZzLicenseNotifySend selectZzLicenseNotifySendByLicenseId(String licenseId);

    /**
     * 新增证照消息发送
     *
     * @param zzLicenseNotifySend 证照消息发送
     * @return 结果
     */
    public int insertZzLicenseNotifySend(ZzLicenseNotifySend zzLicenseNotifySend);

    /**
     * 修改证照消息发送
     *
     * @param zzLicenseNotifySend 证照消息发送
     * @return 结果
     */
    public int updateZzLicenseNotifySend(ZzLicenseNotifySend zzLicenseNotifySend);

    /**
     * 根据证照id查询是否发送系统代办
     * @param licenseId
     * @return
     */
    ZzLicenseNotifySend selectZzLicenseSendNotifyByLicenseId(String licenseId);

    /**
     * 查询是否存在未发送或发送的代办
     * @param sendVo
     * @return
     */
    List<ZzLicenseNotifySend> selectLicenseNotifyListToSend(ZzLicenseNotifySend sendVo);
}
