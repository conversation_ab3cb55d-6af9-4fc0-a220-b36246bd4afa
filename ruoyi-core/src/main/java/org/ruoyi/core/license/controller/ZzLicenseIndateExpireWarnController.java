package org.ruoyi.core.license.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.license.domain.ZzLicenseIndateExpireWarn;
import org.ruoyi.core.license.service.IZzLicenseIndateExpireWarnService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 证照有效期预警Controller
 * 
 * <AUTHOR>
 * @date 2024-02-02
 */
@RestController
@RequestMapping("/licenseIndateExpireWarn/expireWarn")
public class ZzLicenseIndateExpireWarnController extends BaseController
{
    @Autowired
    private IZzLicenseIndateExpireWarnService zzLicenseIndateExpireWarnService;

    /**
     * 查询证照有效期预警列表
     */
    @PreAuthorize("@ss.hasPermi('licenseIndateExpireWarn:expireWarn:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZzLicenseIndateExpireWarn zzLicenseIndateExpireWarn)
    {
        startPage();
        List<ZzLicenseIndateExpireWarn> list = zzLicenseIndateExpireWarnService.selectZzLicenseIndateExpireWarnList(zzLicenseIndateExpireWarn);
        return getDataTable(list);
    }

    /**
     * 导出证照有效期预警列表
     */
    @PreAuthorize("@ss.hasPermi('licenseIndateExpireWarn:expireWarn:export')")
    @Log(title = "证照有效期预警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZzLicenseIndateExpireWarn zzLicenseIndateExpireWarn)
    {
        List<ZzLicenseIndateExpireWarn> list = zzLicenseIndateExpireWarnService.selectZzLicenseIndateExpireWarnList(zzLicenseIndateExpireWarn);
        ExcelUtil<ZzLicenseIndateExpireWarn> util = new ExcelUtil<ZzLicenseIndateExpireWarn>(ZzLicenseIndateExpireWarn.class);
        util.exportExcel(response, list, "证照有效期预警数据");
    }

    /**
     * 获取证照有效期预警详细信息
     */
    @PreAuthorize("@ss.hasPermi('licenseIndateExpireWarn:expireWarn:query')")
    @GetMapping(value = "/{licenseId}")
    public AjaxResult getInfo(@PathVariable("licenseId") String licenseId)
    {
        return AjaxResult.success(zzLicenseIndateExpireWarnService.selectZzLicenseIndateExpireWarnByLicenseId(licenseId));
    }

    /**
     * 开启/关闭证照有效期预警
     */
    @Log(title = "开启/关闭证照有效期预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZzLicenseIndateExpireWarn zzLicenseIndateExpireWarn)
    {
        return toAjax(zzLicenseIndateExpireWarnService.insertZzLicenseIndateExpireWarn(zzLicenseIndateExpireWarn));
    }

    /**
     * 修改证照有效期预警
     */
    @Log(title = "证照有效期预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZzLicenseIndateExpireWarn zzLicenseIndateExpireWarn)
    {
        return toAjax(zzLicenseIndateExpireWarnService.updateZzLicenseIndateExpireWarn(zzLicenseIndateExpireWarn));
    }

    /**
     * 删除证照有效期预警
     */
    @PreAuthorize("@ss.hasPermi('licenseIndateExpireWarn:expireWarn:remove')")
    @Log(title = "证照有效期预警", businessType = BusinessType.DELETE)
	@DeleteMapping("/{licenseIds}")
    public AjaxResult remove(@PathVariable String[] licenseIds)
    {
        return toAjax(zzLicenseIndateExpireWarnService.deleteZzLicenseIndateExpireWarnByLicenseIds(licenseIds));
    }

    /**
     * 证照有效期预警状态回显
     * @return
     */
    @GetMapping("/statusEcho")
    public AjaxResult statusEcho(){
        return AjaxResult.success(zzLicenseIndateExpireWarnService.selectLicenseWarnStatusEcho());
    }

}
