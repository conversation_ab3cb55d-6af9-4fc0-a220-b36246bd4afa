package org.ruoyi.core.license.service.impl;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.personnel.domain.PersonnelFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.license.mapper.ZzLicenseFilesMapper;
import org.ruoyi.core.license.domain.ZzLicenseFiles;
import org.ruoyi.core.license.service.IZzLicenseFilesService;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 证照附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
@Service
public class ZzLicenseFilesServiceImpl implements IZzLicenseFilesService 
{
    @Autowired
    private ZzLicenseFilesMapper zzLicenseFilesMapper;

    /**
     * 查询证照附件
     * 
     * @param id 证照附件主键
     * @return 证照附件
     */
    @Override
    public ZzLicenseFiles selectZzLicenseFilesById(Long id)
    {
        return zzLicenseFilesMapper.selectZzLicenseFilesById(id);
    }

    /**
     * 查询证照附件列表
     * 
     * @param zzLicenseFiles 证照附件
     * @return 证照附件
     */
    @Override
    public List<ZzLicenseFiles> selectZzLicenseFilesList(ZzLicenseFiles zzLicenseFiles)
    {
        return zzLicenseFilesMapper.selectZzLicenseFilesList(zzLicenseFiles);
    }

    /**
     * 新增证照附件
     * 
     * @param zzLicenseFiles 证照附件
     * @return 结果
     */
    @Override
    public int insertZzLicenseFiles(ZzLicenseFiles zzLicenseFiles)
    {
        zzLicenseFiles.setCreateTime(DateUtils.getNowDate());
        zzLicenseFiles.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        return zzLicenseFilesMapper.insertZzLicenseFiles(zzLicenseFiles);
    }

    /**
     * 修改证照附件
     * 
     * @param zzLicenseFiles 证照附件
     * @return 结果
     */
    @Override
    public int updateZzLicenseFiles(ZzLicenseFiles zzLicenseFiles)
    {
        return zzLicenseFilesMapper.updateZzLicenseFiles(zzLicenseFiles);
    }

    /**
     * 批量删除证照附件
     * 
     * @param ids 需要删除的证照附件主键
     * @return 结果
     */
    @Override
    public int deleteZzLicenseFilesByIds(Long[] ids)
    {
        return zzLicenseFilesMapper.deleteZzLicenseFilesByIds(ids);
    }

    /**
     * 删除证照附件信息
     * 
     * @param id 证照附件主键
     * @return 结果
     */
    @Override
    public int deleteZzLicenseFilesById(Long id)
    {
        return zzLicenseFilesMapper.deleteZzLicenseFilesById(id);
    }

    @Override
    public void insertZzLicenseFileList(List<ZzLicenseFiles> licenseFilesList) {
        zzLicenseFilesMapper.insertZzLicenseFileList(licenseFilesList);
    }

    @Override
    public List<ZzLicenseFiles> selectZzLicenseFilesByLicenseId(String licenseId) {
        return zzLicenseFilesMapper.selectZzLicenseFilesByLicenseId(licenseId);
    }

    /**
     * 附件上传
     * @param file
     * @return
     */
    @Override
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.LICENSE_SYSTEM, file);
            ZzLicenseFiles zzLicenseFiles = new ZzLicenseFiles();
            zzLicenseFiles.setFilePath(url);
            zzLicenseFiles.setFileName(name);
            zzLicenseFiles.setCreateBy(getUsername());
            zzLicenseFiles.setCreateTime(DateUtils.getNowDate());
            //插入附件表
            zzLicenseFilesMapper.insertZzLicenseFiles(zzLicenseFiles);
            zzLicenseFiles.setFileId(zzLicenseFiles.getId());
            return AjaxResult.success(zzLicenseFiles);
        } catch (Exception e) {
            return AjaxResult.error();
        }
    }
}
