package org.ruoyi.core.license.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.ruoyi.core.license.domain.vo.ZzLicenseProcessVo;

/**
 * 证照收回对象 zz_pending_detail
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
public class ZzPendingDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 流程id */
    @Excel(name = "流程id")
    private String businessId;

    /** 证照id */
    private String licenseId;

    /** 证照名称 */
    private String licenseName;

    /** 证照是否签领状态 */
    private String licenseStatus;

    /** 证照是否签领状态字典值 */
    private String licenseStatusLabel;

    /** 主题 */
    @Excel(name = "主题")
    private String themes;

    /** 所属公司(借用人) */
    @Excel(name = "所属公司(借用人)")
    private Long pertainCompanyId;

    /** 所属公司名称 */
    private String pertainCompanyName;

    /** 借用人 */
    @Excel(name = "借用人")
    private String borrowPerson;

    /** 签领收回状态(1待签领;2审核中;3已签领;4不签领;5:已收回;6:已废弃) */
    @Excel(name = "签领收回状态(1待签领;2审核中;3已签领;4不签领;5:已收回;6:已废弃)")
    private String signStatus;

    /** 签领收回状态字典值 */
    private String signStatusLabel;

    /** 证照状态(1未到期，2即将到期，3已到期) */
    @Excel(name = "证照状态(1未到期，2即将到期，3已到期)")
    private String backTimeStatus;

    /** 证照状态字典值 */
    private String backTimeStatusLabel;

    /** 借用开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "借用开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date borrowStartTime;

    /** 借用结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "借用结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date borrowEndTime;

    /** 不签领原因 */
    @Excel(name = "不签领原因")
    private String reason;

    /** 是否发送企业微信通知(1是 2否) */
    private String sendNotify;

    /** 签领时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签领时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date isSignTime;

    /** 表单json字符串 */
    private String formJosnString;

    /** 发起流程时的表单id */
    private String formId;

    /** 页签类型(领用/收回) */
    private String tableType;

    /** 待处理证照id集合-接参 */
    private List<String> licenseIdList;

    /** 流程对应的证照信息 */
    private List<ZzLicenseProcessVo> processList;

    /** 保管人 */
    private String custodyPerson;

    /** 版本 */
    private String version;

    /** 发证日期 */
    private String issuingTime;

    /** 发证单位 */
    private String issuingUnit;

    /** 保管人(sql查询返参) */
    private String custodyNameParam;

    /** 证照保管人(借用明细) */
    private String custodyName;

    /** 按钮类型(BC保存/TJ提交) */
    private String buttonType;

    private Integer pageSize;

    private Integer pageNum;

    /** 是否签领(0已签领 1不签领) */
    private String isSign;

    /** 是否签领字典(0已签领 1不签领) */
    private String IsSignLabel;

    /** 节点标识 */
    private String jieDianFlag;


    public String getJieDianFlag() {
        return jieDianFlag;
    }

    public void setJieDianFlag(String jieDianFlag) {
        this.jieDianFlag = jieDianFlag;
    }

    public String getIsSign() {
        return isSign;
    }

    public void setIsSign(String isSign) {
        this.isSign = isSign;
    }

    public String getIsSignLabel() {
        return IsSignLabel;
    }

    public void setIsSignLabel(String isSignLabel) {
        IsSignLabel = isSignLabel;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public String getButtonType() {
        return buttonType;
    }

    public void setButtonType(String buttonType) {
        this.buttonType = buttonType;
    }

    public String getCustodyName() {
        return custodyName;
    }

    public void setCustodyName(String custodyName) {
        this.custodyName = custodyName;
    }

    public String getCustodyNameParam() {
        return custodyNameParam;
    }

    public void setCustodyNameParam(String custodyNameParam) {
        this.custodyNameParam = custodyNameParam;
    }

    public String getIssuingUnit() {
        return issuingUnit;
    }

    public void setIssuingUnit(String issuingUnit) {
        this.issuingUnit = issuingUnit;
    }

    public String getIssuingTime() {
        return issuingTime;
    }

    public void setIssuingTime(String issuingTime) {
        this.issuingTime = issuingTime;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCustodyPerson() {
        return custodyPerson;
    }

    public void setCustodyPerson(String custodyPerson) {
        this.custodyPerson = custodyPerson;
    }

    public List<ZzLicenseProcessVo> getProcessList() {
        return processList;
    }

    public void setProcessList(List<ZzLicenseProcessVo> processList) {
        this.processList = processList;
    }

    public List<String> getLicenseIdList() {
        return licenseIdList;
    }

    public void setLicenseIdList(List<String> licenseIdList) {
        this.licenseIdList = licenseIdList;
    }

    public String getTableType() {
        return tableType;
    }

    public void setTableType(String tableType) {
        this.tableType = tableType;
    }

    public String getFormJosnString() {
        return formJosnString;
    }

    public void setFormJosnString(String formJosnString) {
        this.formJosnString = formJosnString;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public String getPertainCompanyName() {
        return pertainCompanyName;
    }

    public void setPertainCompanyName(String pertainCompanyName) {
        this.pertainCompanyName = pertainCompanyName;
    }

    public String getSignStatusLabel() {
        return signStatusLabel;
    }

    public void setSignStatusLabel(String signStatusLabel) {
        this.signStatusLabel = signStatusLabel;
    }

    public String getBackTimeStatusLabel() {
        return backTimeStatusLabel;
    }

    public void setBackTimeStatusLabel(String backTimeStatusLabel) {
        this.backTimeStatusLabel = backTimeStatusLabel;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setBusinessId(String businessId) 
    {
        this.businessId = businessId;
    }

    public String getBusinessId() 
    {
        return businessId;
    }
    public void setLicenseId(String licenseId) 
    {
        this.licenseId = licenseId;
    }

    public String getLicenseId() 
    {
        return licenseId;
    }
    public void setThemes(String themes) 
    {
        this.themes = themes;
    }

    public String getThemes() 
    {
        return themes;
    }
    public void setPertainCompanyId(Long pertainCompanyId)
    {
        this.pertainCompanyId = pertainCompanyId;
    }

    public Long getPertainCompanyId()
    {
        return pertainCompanyId;
    }
    public void setBorrowPerson(String borrowPerson) 
    {
        this.borrowPerson = borrowPerson;
    }

    public String getBorrowPerson() 
    {
        return borrowPerson;
    }
    public void setSignStatus(String signStatus) 
    {
        this.signStatus = signStatus;
    }

    public String getSignStatus() 
    {
        return signStatus;
    }
    public void setBackTimeStatus(String backTimeStatus) 
    {
        this.backTimeStatus = backTimeStatus;
    }

    public String getBackTimeStatus() 
    {
        return backTimeStatus;
    }
    public void setBorrowStartTime(Date borrowStartTime) 
    {
        this.borrowStartTime = borrowStartTime;
    }

    public Date getBorrowStartTime() 
    {
        return borrowStartTime;
    }
    public void setBorrowEndTime(Date borrowEndTime) 
    {
        this.borrowEndTime = borrowEndTime;
    }

    public Date getBorrowEndTime() 
    {
        return borrowEndTime;
    }
    public void setReason(String reason) 
    {
        this.reason = reason;
    }

    public String getReason() 
    {
        return reason;
    }
    public void setIsSignTime(Date isSignTime) 
    {
        this.isSignTime = isSignTime;
    }

    public Date getIsSignTime() 
    {
        return isSignTime;
    }

    public String getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(String licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public String getLicenseStatusLabel() {
        return licenseStatusLabel;
    }

    public void setLicenseStatusLabel(String licenseStatusLabel) {
        this.licenseStatusLabel = licenseStatusLabel;
    }

    public String getSendNotify() {
        return sendNotify;
    }

    public void setSendNotify(String sendNotify) {
        this.sendNotify = sendNotify;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("businessId", getBusinessId())
            .append("licenseId", getLicenseId())
            .append("themes", getThemes())
            .append("pertainCompanyId", getPertainCompanyId())
            .append("borrowPerson", getBorrowPerson())
            .append("signStatus", getSignStatus())
            .append("backTimeStatus", getBackTimeStatus())
            .append("borrowStartTime", getBorrowStartTime())
            .append("borrowEndTime", getBorrowEndTime())
            .append("reason", getReason())
            .append("isSignTime", getIsSignTime())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
