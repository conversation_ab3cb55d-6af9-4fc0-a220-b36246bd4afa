package org.ruoyi.core.license.service;

import org.ruoyi.core.license.domain.ZzLicenseIndateExpireWarn;

import java.util.List;

public interface IZzLicenseIndateExpireWarnService {
    /**
     * 查询证照有效期预警
     *
     * @param licenseId 证照有效期预警主键
     * @return 证照有效期预警
     */
    public ZzLicenseIndateExpireWarn selectZzLicenseIndateExpireWarnByLicenseId(String licenseId);

    /**
     * 查询证照有效期预警列表
     *
     * @param zzLicenseIndateExpireWarn 证照有效期预警
     * @return 证照有效期预警集合
     */
    public List<ZzLicenseIndateExpireWarn> selectZzLicenseIndateExpireWarnList(ZzLicenseIndateExpireWarn zzLicenseIndateExpireWarn);

    /**
     * 新开启/关闭证照有效期预警
     *
     * @param zzLicenseIndateExpireWarn 证照有效期预警
     * @return 结果
     */
    public int insertZzLicenseIndateExpireWarn(ZzLicenseIndateExpireWarn zzLicenseIndateExpireWarn);

    /**
     * 修改证照有效期预警
     *
     * @param zzLicenseIndateExpireWarn 证照有效期预警
     * @return 结果
     */
    public int updateZzLicenseIndateExpireWarn(ZzLicenseIndateExpireWarn zzLicenseIndateExpireWarn);

    /**
     * 批量删除证照有效期预警
     *
     * @param licenseIds 需要删除的证照有效期预警主键集合
     * @return 结果
     */
    public int deleteZzLicenseIndateExpireWarnByLicenseIds(String[] licenseIds);

    /**
     * 删除证照有效期预警信息
     *
     * @param licenseId 证照有效期预警主键
     * @return 结果
     */
    public int deleteZzLicenseIndateExpireWarnByLicenseId(String licenseId);

    /**
     * 证照有效期预警状态回显
     * @return
     */
    ZzLicenseIndateExpireWarn selectLicenseWarnStatusEcho();

    /**
     * 新增证照的同时增加有效期到期预警
     * @param indateExpireWarn
     */
    void insertLicenseIndateExpireWarn(ZzLicenseIndateExpireWarn indateExpireWarn);
}