package org.ruoyi.core.license.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.collections.CollectionUtils;
import org.ruoyi.core.license.constant.ZzConstant;
import org.ruoyi.core.license.domain.ZzLicenseMain;
import org.ruoyi.core.license.domain.ZzLicenseProcess;
import org.ruoyi.core.license.domain.vo.ZzLicenseProcessVo;
import org.ruoyi.core.license.service.IZzLicenseMainService;
import org.ruoyi.core.license.service.IZzLicenseProcessService;
import org.ruoyi.core.oasystem.domain.OaFormField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.license.mapper.ZzPendingDetailMapper;
import org.ruoyi.core.license.domain.ZzPendingDetail;
import org.ruoyi.core.license.service.IZzPendingDetailService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 证照收回Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
@Service
public class ZzPendingDetailServiceImpl implements IZzPendingDetailService {
    @Autowired
    private ZzPendingDetailMapper zzPendingDetailMapper;

    @Resource
    private IZzLicenseMainService zzLicenseMainService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private IZzLicenseProcessService zzLicenseProcessService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询待处理证照详情
     *
     * @param id 证照收回主键
     * @return 证照收回
     */
    @Override
    public ZzPendingDetail selectZzPendingDetailById(Long id) {
        return zzPendingDetailMapper.selectZzPendingDetailById(id);
    }

    /**
     * 查询待处理证照列表
     *
     * @param zzPendingDetail 证照收回
     * @return 证照收回
     */
    @Override
    public List<ZzPendingDetail> selectZzPendingDetailList(ZzPendingDetail zzPendingDetail) {
        List<ZzPendingDetail> zzPendingDetails = new ArrayList<>();
        // 领用列表
        if (zzPendingDetail.getTableType().equals(ZzConstant.TABLE_TYPE_LY)) {
            if (zzPendingDetail.getPageNum() != null && zzPendingDetail.getPageSize() != null){
                PageHelper.startPage(zzPendingDetail.getPageNum(),zzPendingDetail.getPageSize());
                zzPendingDetails = zzPendingDetailMapper.selectZzPendingDetailList(zzPendingDetail);
            }
        }
        // 收回列表
        if (zzPendingDetail.getTableType().equals(ZzConstant.TABLE_TYPE_SH)) {
            if (zzPendingDetail.getPageNum() != null && zzPendingDetail.getPageSize() != null){
                PageHelper.startPage(zzPendingDetail.getPageNum(),zzPendingDetail.getPageSize());
                zzPendingDetails = zzPendingDetailMapper.selectZzPendingDetailSHList(zzPendingDetail);
            }
        }
        // 组装数据
        if (CollectionUtils.isNotEmpty(zzPendingDetails)) {
            normalDictType(zzPendingDetails, zzPendingDetail.getTableType());
        }
        return zzPendingDetails;
    }

    /**
     * 新增待处理证照
     *
     * @param zzPendingDetail 证照收回
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertZzPendingDetail(ZzPendingDetail zzPendingDetail) throws ParseException {
        String businessId = zzPendingDetail.getBusinessId();
        //如果流程id不为空，删除旧数据
        if (businessId != null && !businessId.equals("")){
            zzPendingDetailMapper.deleteZzPendingDetailByBusinessId(businessId);
        }
        String formJosnString = zzPendingDetail.getFormJosnString();
        ZzLicenseMain zzLicenseMain = new ZzLicenseMain();
        int i = 0;
        if (formJosnString == null || formJosnString.equals("")) {
            throw new ServiceException("未查询到对应的流程表单，请联系系统管理员！");
        }
        String formId = zzPendingDetail.getFormId();
        if (formId == null || formId.equals("")) {
            throw new ServiceException("未查询到对应的表单，请联系系统管理员！");
        }
        // 根据form表单id查询表单json
        List<OaFormField> oaFormFieldList = zzPendingDetailMapper.selectFormFieldByFormId(formId);
        if (CollectionUtils.isNotEmpty(oaFormFieldList)) {
            List<String> keyList = new ArrayList<>();
            oaFormFieldList.forEach(info -> {
                if (info.getFieldName().contains(ZzConstant.JIEYUE_DATE) || info.getFieldName().contains(ZzConstant.JIEYONG_DATE)) {
                    String key = info.getFieldKey();
                    keyList.add(key);
                }
            });
            this.splitJsonString(zzPendingDetail, keyList);
        }
        zzPendingDetail.setBorrowPerson(SecurityUtils.getLoginUser().getUser().getUserName());
        zzPendingDetail.setPertainCompanyId(SecurityUtils.getLoginUser().getUser().getUnit().getUnitId());
        zzPendingDetail.setBackTimeStatus(ZzConstant.STRING_1);
        zzPendingDetail.setSignStatus(ZzConstant.STRING_2);
        zzPendingDetail.setCreateTime(DateUtils.getNowDate());
        zzPendingDetail.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        i = zzPendingDetailMapper.insertZzPendingDetail(zzPendingDetail);
        //流程为保存草稿状态时，不修改证照状态
        if (zzPendingDetail.getButtonType().equals(ZzConstant.BUTTON_TYPE_BC)){
            return i;
        }
        List<String> licenseIdList = zzPendingDetail.getLicenseIdList();
        if (CollectionUtils.isNotEmpty(licenseIdList)) {
            // 更新证照状态为外借申请中
            zzLicenseMain.setIsSign(ZzConstant.STRING_6);
            zzLicenseMain.setLicenseIdList(licenseIdList);
            zzLicenseMainService.updateZzLicenseMain(zzLicenseMain);
        }
        return i;
    }

    /**
     * 修改待处理证照
     * (1待签领;2审核中;3已签领;4不签领;5:已收回)
     *
     * @param zzPendingDetail 证照收回
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateZzPendingDetail(ZzPendingDetail zzPendingDetail) {
        zzPendingDetail.setUpdateTime(DateUtils.getNowDate());
        zzPendingDetail.setUpdateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        if (zzPendingDetail.getSignStatus() != null && !zzPendingDetail.getSignStatus().equals("")) {
            //查询当前流程中所有要外借的证照
            List<ZzLicenseProcessVo> zzLicenseProcessVos = zzLicenseProcessService.selectZzLicenseProcessListByBusinessId(zzPendingDetail.getBusinessId());
            //新权限2.0-签领节点审批通过后，将待审核证照状态改为待签领
            if (zzPendingDetail.getSignStatus().equals(ZzConstant.STRING_11)) {
                if ( null != zzPendingDetail.getJieDianFlag() && zzPendingDetail.getJieDianFlag().equals("QLL")){
                    zzPendingDetail.setSignStatus("99");
                }else {
                    zzPendingDetail.setSignStatus("1");
                }
            }
            // 不签领或已收回或已废弃时，更新流程为已完结
            if (zzPendingDetail.getSignStatus().equals(ZzConstant.STRING_4) || zzPendingDetail.getSignStatus().equals(ZzConstant.STRING_5)) {
                ZzLicenseProcess process = new ZzLicenseProcess();
                process.setProcessId(zzPendingDetail.getBusinessId());
                process.setProcessStatus(ZzConstant.STRING_1);
                zzLicenseProcessService.updateZzLicenseProcess(process);
                // 不签领，更新证照为在库状态
                if (CollectionUtils.isNotEmpty(zzLicenseProcessVos) && zzPendingDetail.getSignStatus().equals(ZzConstant.STRING_4)) {
                    List<String> mainIds = zzLicenseProcessVos.stream().map(ZzLicenseProcessVo::getLicenseId).collect(Collectors.toList());
                    ZzLicenseMain zzLicenseMain = new ZzLicenseMain();
                    zzLicenseMain.setLicenseIdList(mainIds);
                    zzLicenseMain.setIsSign(ZzConstant.STRING_4);
                    zzLicenseMain.setBusinessId(zzPendingDetail.getBusinessId());
                    zzLicenseMainService.updateZzLicenseMain(zzLicenseMain);
                }
                // 已收回，更新证照为在库状态
                if (CollectionUtils.isNotEmpty(zzLicenseProcessVos) && zzPendingDetail.getSignStatus().equals(ZzConstant.STRING_5)) {
                    List<String> mainIds = zzLicenseProcessVos.stream().map(ZzLicenseProcessVo::getLicenseId).collect(Collectors.toList());
                    ZzLicenseMain zzLicenseMain = new ZzLicenseMain();
                    zzLicenseMain.setLicenseIdList(mainIds);
                    zzLicenseMain.setIsSign(ZzConstant.STRING_5);
                    zzLicenseMainService.updateZzLicenseMain(zzLicenseMain);
                }
            }
            //签领证照时，更改证照是否在库状态为‘已外借’
            if (zzPendingDetail.getSignStatus().equals(ZzConstant.STRING_3)){
                //获取本次借用流程中所有的证照id集合
                List<String> mainIds = zzLicenseProcessVos.stream().map(ZzLicenseProcessVo::getLicenseId).collect(Collectors.toList());
                //获取本次借用流程中，要签领的证照
                List<String> licenseIdList = zzPendingDetail.getLicenseIdList();
                mainIds.removeAll(licenseIdList);
                mainIds.removeAll(Collections.singleton(null));
                //此时mainIds集合中只包含本次借用流程中未勾选确认签领的证照id，然后将这些证照状态改为在库
                if (CollectionUtils.isNotEmpty(mainIds)) {
                    ZzLicenseMain zzLicenseMain1 = new ZzLicenseMain();
                    zzLicenseMain1.setLicenseIdList(mainIds);
                    zzLicenseMain1.setIsSign(ZzConstant.STRING_4);
                    zzLicenseMain1.setBusinessId(zzPendingDetail.getBusinessId());
                    zzLicenseMainService.updateZzLicenseMain(zzLicenseMain1);
                    //修改流程表中该证照为未借阅状态
                    for (String licenseId : mainIds) {
                        ZzLicenseProcess zzLicenseProcess = new ZzLicenseProcess();
                        zzLicenseProcess.setProcessId(zzPendingDetail.getBusinessId());
                        zzLicenseProcess.setLicenseId(licenseId);
                        zzLicenseProcess.setIsSign("1");
                        zzLicenseProcessService.updateLicenseStatus(zzLicenseProcess);
                    }
                }
                //将确认签领的证照改为已外借状态
                ZzLicenseMain zzLicenseMain = new ZzLicenseMain();
                zzLicenseMain.setLicenseIdList(licenseIdList);
                zzLicenseMain.setIsSign(ZzConstant.STRING_2);
                zzLicenseMain.setBusinessId(zzPendingDetail.getBusinessId());
                zzLicenseMainService.updateZzLicenseMain(zzLicenseMain);
            }
            //流程废弃时，更新证照待签领页签中的状态为‘已废弃’
            if (zzPendingDetail.getSignStatus().equals(ZzConstant.STRING_6)){
                List<String> mainIds = zzLicenseProcessVos.stream().map(ZzLicenseProcessVo::getLicenseId).collect(Collectors.toList());
                List<String> licenseSysCodes = zzLicenseProcessVos.stream().map(ZzLicenseProcessVo::getLicenseSystemCode).collect(Collectors.toList());
                //根据证照系统编号更新本次外借流程中所有的证照状态为‘在库’
                if (CollectionUtils.isNotEmpty(licenseSysCodes)){
                    zzLicenseMainService.updateLicenseStatusBySysCode(licenseSysCodes);
                }
                //流程废弃时，更新流程表中该流程涉及到的证照为不签领状态
                ZzLicenseMain licenseMain = new ZzLicenseMain();
                licenseMain.setLicenseIdList(mainIds);
                licenseMain.setIsSign(ZzConstant.STRING_4);
                licenseMain.setBusinessId(zzPendingDetail.getBusinessId());
                zzLicenseMainService.updateZzLicenseMain(licenseMain);
                //流程废弃时，更新证照状态为在库
                ZzLicenseMain zzLicenseMain = new ZzLicenseMain();
                zzLicenseMain.setLicenseIdList(mainIds);
                zzLicenseMain.setIsSign(ZzConstant.STRING_1);
                zzLicenseMainService.updateZzLicenseMain(zzLicenseMain);
                //更新流程表中该流程为‘已完结’状态
                ZzLicenseProcess process = new ZzLicenseProcess();
                process.setProcessId(zzPendingDetail.getBusinessId());
                process.setProcessStatus(ZzConstant.STRING_1);
                process.setIsSign(ZzConstant.STRING_1);
                zzLicenseProcessService.updateZzLicenseProcess(process);
            }
        }
        //更新待处理证照的签领状态
        if (null != zzPendingDetail.getSignStatus() && zzPendingDetail.getSignStatus().equals("99")){
            zzPendingDetail.setSignStatus("3");
        }
        return zzPendingDetailMapper.updateZzPendingDetail(zzPendingDetail);
    }

    /**
     * 批量删除证照收回
     *
     * @param ids 需要删除的证照收回主键
     * @return 结果
     */
    @Override
    public int deleteZzPendingDetailByIds(Long[] ids) {
        return zzPendingDetailMapper.deleteZzPendingDetailByIds(ids);
    }

    /**
     * 删除证照收回信息
     *
     * @param id 证照收回主键
     * @return 结果
     */
    @Override
    public int deleteZzPendingDetailById(Long id) {
        return zzPendingDetailMapper.deleteZzPendingDetailById(id);
    }

    /**
     * 拆分json字符串，获取借用时间
     *
     * @param zzPendingDetail
     * @param fieldKeyList
     * @return
     * @throws ParseException
     */
    public void splitJsonString(ZzPendingDetail zzPendingDetail, List<String> fieldKeyList) throws ParseException {
        // 根据key查询json字符串
        String data = zzPendingDetail.getFormJosnString();
        String beginTime = "";
        String endTime = "";
        Date borrowStartTime = null;
        Date borrowEndTime = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < fieldKeyList.size(); i++) {
            int j = data.indexOf(fieldKeyList.get(i));
            int mm = j + fieldKeyList.get(i).length() + 3;
            String substring = data.substring(mm);
            int j1 = substring.indexOf("\"");
            if (i == ZzConstant.INTEGER_0) {
                beginTime = substring.substring(0, j1);
                //复制流程重新发起时，不修改借用日期会报错，此处直接重新拼接年月日时分秒
                if (!beginTime.contains(":")){
                    beginTime = beginTime + " 00:00:00";
                }
                borrowStartTime = sdf.parse(beginTime);

            }
            if (i == ZzConstant.INTEGER_1) {
                endTime = substring.substring(0, j1);
                //复制流程重新发起时，不修改借用日期会报错，此处直接重新拼接年月日时分秒
                if (!endTime.contains(":")){
                    endTime = endTime + " 00:00:00";
                }
                borrowEndTime = sdf.parse(endTime);

            }
        }
        zzPendingDetail.setBorrowStartTime(borrowStartTime);
        zzPendingDetail.setBorrowEndTime(borrowEndTime);
    }

    /**
     * 查询数据字典，组装数据
     *
     * @param zzPendingDetails
     */
    public void normalDictType(List<ZzPendingDetail> zzPendingDetails, String tableType) {
        if (CollectionUtils.isEmpty(zzPendingDetails)) {
            return;
        }
        List<SysDictData> zzqlList = dictDataService.selectDictLabelByType(ZzConstant.ZZ_LICENSE_SIGN_STATUS);
        List<SysDictData> zzztList = dictDataService.selectDictLabelByType(ZzConstant.ZZ_LICENSE_TIME_STATUS);
        zzPendingDetails.forEach(d -> {
            // 签领收回状态
            String zzql = "";
            // 证照过期状态
            String zzzt = "";

            // 如果是收回页签，3已签领的就是未收回的
            if (tableType != null && !tableType.equals("") && tableType.equals(ZzConstant.TABLE_TYPE_SH)) {
                if (d.getSignStatus().equals(ZzConstant.STRING_3))
                    d.setSignStatusLabel("未收回");
            } else {
                // 签领收回状态
                if (d.getSignStatus() != null && !CollectionUtils.isEmpty(zzqlList)) {
                    Optional<SysDictData> zzqlDescO = zzqlList.stream()
                            .filter(bean -> d.getSignStatus().toString().equals(bean.getDictValue()))
                            .findFirst();
                    zzql = zzqlDescO.isPresent() ? zzqlDescO.get().getDictLabel() : null;
                    d.setSignStatusLabel(zzql);
                }
            }
            // 证照过期状态
            if (d.getBackTimeStatus() != null && !CollectionUtils.isEmpty(zzztList)) {
                Optional<SysDictData> zzztDescO = zzztList.stream()
                        .filter(bean -> d.getBackTimeStatus().toString().equals(bean.getDictValue()))
                        .findFirst();
                zzzt = zzztDescO.isPresent() ? zzztDescO.get().getDictLabel() : null;
                d.setBackTimeStatusLabel(zzzt);
            }
        });
    }

    /**
     * 待处理证照页面-查看流程页面的证照详情
     *
     * @param flowId 流程id
     * @return
     */
    @Override
    public List<ZzPendingDetail> selectPendLicenseById(String flowId) {
        List<ZzPendingDetail> zzPendingDetailList = zzPendingDetailMapper.queryPendingLicenseById(flowId);
        zzPendingDetailList.forEach( b->{
        //证照保管人
        String custodyName = b.getCustodyNameParam();
        if (custodyName != null && !custodyName.equals("") ){
            String[] splitUserName = custodyName.split(",");
            if (splitUserName.length > 0){
                String userNickName = "";
                //根据用户账号获取用户姓名
                for (String userName : splitUserName) {
                    SysUser sysUser = sysUserService.selectUserByUserName(userName);
                    if (!Objects.isNull(sysUser)) {
                        userNickName = userNickName + sysUser.getNickName() + ",";
                    }
                }
                if (StringUtils.isNotEmpty(userNickName)) {
                    String nickName = userNickName.substring(0, userNickName.lastIndexOf(","));
                    b.setCustodyName(nickName);
                }
            }
        }
    });
        return zzPendingDetailList;
    }
}
