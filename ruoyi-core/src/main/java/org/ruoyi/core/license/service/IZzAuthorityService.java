package org.ruoyi.core.license.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.license.domain.ZzAuthority;

/**
 * 证照授权Service接口
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
public interface IZzAuthorityService
{
    /**
     * 查询证照授权
     *
     * @param billId 证照授权主键
     * @return 证照授权
     */
    public ZzAuthority selectZzAuthorityByBillId(Long billId);

    /**
     * 查询证照授权列表
     *
     * @param zzAuthority 证照授权
     * @return 证照授权集合
     */
    public List<ZzAuthority> selectZzAuthorityList(ZzAuthority zzAuthority);

    /**
     * 新增证照授权
     *
     * @param zzAuthority 证照授权
     * @return 结果
     */
    public int insertZzAuthority(List<ZzAuthority> zzAuthority);

    /**
     * 修改证照授权
     *
     * @param zzAuthority 证照授权
     * @return 结果
     */
    public int updateZzAuthority(ZzAuthority zzAuthority);

    /**
     * 批量删除证照授权
     *
     * @param billIds 需要删除的证照授权主键集合
     * @return 结果
     */
    public int deleteZzAuthorityByBillIds(Long[] billIds);

    /**
     * 删除证照授权信息
     *
     * @param billId 证照授权主键
     * @return 结果
     */
    public int deleteZzAuthorityByBillId(Long billId);

    /**
     * 修改目录授权
     * @param authorityList
     * @return
     */
    int accreditAuthority(List<ZzAuthority> authorityList);

    /**
     * 根据目录id查询证照授权集合
     * @param billId
     * @return
     */
    public List<ZzAuthority> selectZzAuthorityListByBillId(Long billId);

}