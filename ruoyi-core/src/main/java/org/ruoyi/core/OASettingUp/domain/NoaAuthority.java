package org.ruoyi.core.OASettingUp.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * authority对象 noa_authority
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
@Data
public class NoaAuthority extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 流程ID */
    @Excel(name = "流程ID")
    private String flowId;

    /**
     * 流程配置ID
     */
    private String workFlowId;

    /**
     * 流程提醒配置ID
     */
    private String remindId;

    /** 节点ID */
    @Excel(name = "节点ID")
    private String nodeId;

    /** 权限类型(0:部门 1:岗位 2:人员) */
    @Excel(name = "权限类型(0:部门 1:岗位 2:人员)")
    private Long authorityType;

    /** 关联权限id */
    @Excel(name = "关联权限id")
    private Long authorityId;

    /**
     * 权限配置名称
     */
    private String authorityName;
}
