package org.ruoyi.core.OASettingUp.service;

import org.ruoyi.core.OASettingUp.domain.NoaFlow;
import org.ruoyi.core.OASettingUp.domain.NoaNeedRemind;
import org.ruoyi.core.OASettingUp.domain.NoaRemindPerson;
import org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind;
import org.ruoyi.core.cwproject.domain.TopNotify;

import java.util.Date;
import java.util.List;

/**
 * OASettingUpService接口
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
public interface INoaWorkflowRemindService
{
    /**
     * 查询OASettingUp列表
     *
     * @param noaWorkflowRemind OASettingUp
     * @return OASettingUp集合
     */
    public List<NoaWorkflowRemind> selectNoaWorkflowRemindList(NoaWorkflowRemind noaWorkflowRemind);

    /**
     * 新增流程提醒配置
     *
     * @param noaWorkflowRemind OASettingUp
     * @return 结果
     */
    public int insertNoaWorkflowRemind(NoaWorkflowRemind noaWorkflowRemind);

    /**
     * 修改OASettingUp
     *
     * @param noaWorkflowRemind OASettingUp
     * @return 结果
     */
    public int updateNoaWorkflowRemind(NoaWorkflowRemind noaWorkflowRemind);

    /**
     * 批量删除OASettingUp
     *
     * @param flowIds 需要删除的OASettingUp主键集合
     * @return 结果
     */
    public int deleteNoaWorkflowRemindByFlowIds(String[] flowIds);

    /**
     * 删除OASettingUp信息
     *
     * @param flowId OASettingUp主键
     * @return 结果
     */
    public int deleteNoaWorkflowRemindByFlowId(String[] ids);

    /**
     * 获取流程配置详细信息
     * @param noaWorkflowRemind
     * @return
     */
    NoaWorkflowRemind selectNoaWorkflowRemindByFlowIdAndNodeId(NoaWorkflowRemind noaWorkflowRemind);

    List<NoaWorkflowRemind> selectWorkFlowByBusinessIdAndNodeId(String businessKey, String stepId);

    int insertNeedRemind(NoaNeedRemind bill);

    List<NoaRemindPerson> selectAuthority(String flowId, String nodeId, String id);

    int insertRemindPerson(List<NoaRemindPerson> personDate,String busId);

    List<NoaWorkflowRemind> selectRemindByUser(String username);

    NoaFlow selectFormulaByNotify(TopNotify topNotify,String userName,String state);

    int setRemindState(String remindId);
}
