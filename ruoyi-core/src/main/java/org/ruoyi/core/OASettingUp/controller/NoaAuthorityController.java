package org.ruoyi.core.OASettingUp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.OASettingUp.domain.NoaAuthority;
import org.ruoyi.core.OASettingUp.service.INoaAuthorityService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流程提醒权限Controller
 *
 * <AUTHOR>
 * @date 2023-12-26
 *
 * 流程提醒配置，根据提醒人配置的内容，过滤提醒当前登录人哪些信息
 */
@RestController
@RequestMapping("/noa/authority")
public class NoaAuthorityController extends BaseController
{
    @Autowired
    private INoaAuthorityService noaAuthorityService;

    /**
     * 查询authority列表
     */
    //@PreAuthorize("@ss.hasPermi('authority:authority:list')")
    @GetMapping("/list")
    public TableDataInfo list(NoaAuthority noaAuthority)
    {
        startPage();
        List<NoaAuthority> list = noaAuthorityService.selectNoaAuthorityList(noaAuthority);
        return getDataTable(list);
    }

    /**
     * 导出authority列表
     */
    //@PreAuthorize("@ss.hasPermi('authority:authority:export')")
    @Log(title = "authority", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NoaAuthority noaAuthority)
    {
        List<NoaAuthority> list = noaAuthorityService.selectNoaAuthorityList(noaAuthority);
        ExcelUtil<NoaAuthority> util = new ExcelUtil<NoaAuthority>(NoaAuthority.class);
        util.exportExcel(response, list, "authority数据");
    }

    /**
     * 新增authority
     */
    //@PreAuthorize("@ss.hasPermi('authority:authority:add')")
    @Log(title = "authority", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NoaAuthority noaAuthority)
    {
        return toAjax(noaAuthorityService.insertNoaAuthority(noaAuthority));
    }

    /**
     * 修改authority
     */
    //@PreAuthorize("@ss.hasPermi('authority:authority:edit')")
    @Log(title = "authority", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NoaAuthority noaAuthority)
    {
        return toAjax(noaAuthorityService.updateNoaAuthority(noaAuthority));
    }

    /**
     * 删除authority
     */
    //@PreAuthorize("@ss.hasPermi('authority:authority:remove')")
    @Log(title = "authority", businessType = BusinessType.DELETE)
	@DeleteMapping("/{flowIds}")
    public AjaxResult remove(@PathVariable String[] flowIds)
    {
        return toAjax(noaAuthorityService.deleteNoaAuthorityByFlowIds(flowIds));
    }

    /**
     * 获取所有流程及节点详情
     * @return  NoaFlow
     */
    @GetMapping(value = "/getFlow")
    public AjaxResult getFlow(String flowName)
    {
        return AjaxResult.success(noaAuthorityService.getFlow(flowName));
    }

    /**
     * 根据流程全量Id获取流程节点信息
     * @return  NoaFlow
     */
    @GetMapping(value = "/getNodeToFlow")
    public AjaxResult getNodeToFlow(String flowFullId)
    {
        return AjaxResult.success(noaAuthorityService.getNodeToFlow(flowFullId));
    }

}
