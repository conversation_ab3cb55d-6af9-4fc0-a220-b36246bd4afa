package org.ruoyi.core.OASettingUp.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 流程提醒配置-人员提醒状态对象 noa_remind_person
 *
 * <AUTHOR>
 * @date 2024-01-16
 */
@Data
public class NoaRemindPerson extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 提醒业务id */
    private String remindId;

    /** 被提醒人 */
    @Excel(name = "被提醒人")
    private String remindPerson;

    /** 状态：1  已确认，0 未确认 */
    @Excel(name = "状态：1  已确认，0 未确认")
    private String state;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date notarizeTime;

}
