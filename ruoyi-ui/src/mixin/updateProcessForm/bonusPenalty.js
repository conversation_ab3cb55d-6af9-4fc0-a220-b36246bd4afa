import BonusPenalty from "@/views/oaWork/updateProcessForm/components/bonusPenalty";
import {
  getProcessIdPunishment,
  processRewardPunishment,
  punishmentList
} from "@/api/checkWork/bonusPenalty";
export default {
  components: {
    BonusPenalty,
  },
  data() {
    return {
      tableCheckWorkBonusPenalty: {},
      tableCheckWorkBonusPenaltyList:[]
    };
  },
  methods: {
    async initCheckWorkBonusPenalty() {
      if (this.$route.query.checkWorkBonusPenalty) {
        this.tableCheckWorkBonusPenalty = JSON.parse(
          sessionStorage.getItem("oa-checkWorkBonusPenalty")
        );
      }
      if (this.$route.query.checkWorkBonusPenaltyList) {
        this.tableCheckWorkBonusPenaltyList = JSON.parse(
          sessionStorage.getItem("oa-checkWorkBonusPenalty")
        );
      }
      if (this.$route.query.oid && this.oaModuleType == "rewardsPunishment") {
        const id = this.$route.query.businessId || this.$route.query.oid;
        const { data } = await getProcessIdPunishment({ processId: id });
        if (data) this.tableCheckWorkBonusPenalty = data;
      }
      if (this.$route.query.oid && this.oaModuleType == "rewardsPunishmentList") {
        const id = this.$route.query.businessId || this.$route.query.oid;
        const { rows } = await punishmentList({ processId: id });
        if (rows) this.tableCheckWorkBonusPenaltyList = rows;
      }
    },
    async checkWorkBonusPenaltyProcessTo(response, isSave) {
      if (
        (sessionStorage.getItem("oa-checkWorkBonusPenalty") &&
          Object.keys(this.tableCheckWorkBonusPenalty).length > 0 &&
          this.$route.query.checkWorkBonusPenalty) ||
        (this.$route.query.oid &&
          Object.keys(this.tableCheckWorkBonusPenalty).length > 0)
      ) {
        const ids = [this.tableCheckWorkBonusPenalty.id];
        const params = {
          processId: response.data,
          ids,
        };
        if (isSave) {
          processRewardPunishment({...params,status:1}).then(async (res) => {
            if (res.code == 200) {
              sessionStorage.removeItem("oa-checkWorkBonusPenalty");
            }
          });
        } else {
          processRewardPunishment({...params,status:2}).then(async (res) => {
            if (res.code == 200) {
              sessionStorage.removeItem("oa-checkWorkBonusPenalty");
            }
          });
        }
      }
      if (
        (sessionStorage.getItem("oa-checkWorkBonusPenalty") &&
          Object.keys(this.tableCheckWorkBonusPenaltyList).length > 0 &&
          this.$route.query.checkWorkBonusPenaltyList) ||
        (this.$route.query.oid &&
          Object.keys(this.tableCheckWorkBonusPenaltyList).length > 0)
      ) {
        const ids = this.tableCheckWorkBonusPenaltyList.map(item=>item.id);
        const params = {
          processId: response.data,
          ids,
        };
        if (isSave) {
          processRewardPunishment({...params,status:1}).then(async (res) => {
            if (res.code == 200) {
              sessionStorage.removeItem("oa-checkWorkBonusPenalty");
            }
          });
        } else {
          processRewardPunishment({...params,status:2}).then(async (res) => {
            if (res.code == 200) {
              sessionStorage.removeItem("oa-checkWorkBonusPenalty");
            }
          });
        }
      }
    },
    checkWorkBonusPenaltyDraft(processId) {
      if (this.oaModuleType == "rewardsPunishment") {
        const ids = [this.tableCheckWorkBonusPenalty.id];
        const params = {
          processId,
          ids,
        };
        processRewardPunishment(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-checkWorkBonusPenalty");
          }
        });
      }
      if (this.oaModuleType == "rewardsPunishmentList") {
        const ids = this.tableCheckWorkBonusPenaltyList.map(item=>item.id);
        const params = {
          processId,
          ids,
        };
        processRewardPunishment(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-checkWorkBonusPenalty");
          }
        });
      }
    },
  },
};
