import GoErrand from "@/views/oaWork/updateProcessForm/components/goErrand";
import {
  getProcessIdBusinessTrip,
  processBusinessTrip,
} from "@/api/checkWork/goErrand";
export default {
  components: {
    GoErrand,
  },
  data() {
    return {
      tableCheckWorkGoErrand: {},
    };
  },
  methods: {
    async initCheckWorkGoErrand() {
      if (this.$route.query.checkWorkGoErrand) {
        this.tableCheckWorkGoErrand = JSON.parse(
          sessionStorage.getItem("oa-checkWorkGoErrand")
        );
      }
      if (this.$route.query.oid && this.oaModuleType == "businessTrip") {
        const id = this.$route.query.businessId || this.$route.query.oid;
        const { data } = await getProcessIdBusinessTrip({ processId: id });
        if (data) this.tableCheckWorkGoErrand = data;
      }
    },
    async checkWorkGoErrandProcessTo(response, isSave) {
      if (
        (sessionStorage.getItem("oa-checkWorkGoErrand") &&
          Object.keys(this.tableCheckWorkGoErrand).length > 0 &&
          this.$route.query.checkWorkGoErrand) ||
        (this.$route.query.oid &&
          Object.keys(this.tableCheckWorkGoErrand).length > 0)
      ) {
        const id = this.tableCheckWorkGoErrand.id;
        const params = {
          processId: response.data,
          id,
        };
        if (isSave) {
        } else {
          processBusinessTrip(params).then(async (res) => {
            if (res.code == 200) {
              sessionStorage.removeItem("oa-checkWorkGoErrand");
            }
          });
        }
      }
    },
    checkWorkGoErrandDraft(processId) {
      if (this.oaModuleType == "businessTrip") {
        const id = this.tableCheckWorkGoErrand.id;
        const params = {
          processId,
          id,
        };
        processBusinessTrip(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-checkWorkGoErrand");
          }
        });
      }
    },
  },
};
