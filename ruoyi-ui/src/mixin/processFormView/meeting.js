import { unpassMeeting, processIdMeeting } from "@/api/meeting/staging";
export default {
  data() {
    return {
      formMeeting: {},
    };
  },
  methods: {
    async initMeeting() {
      const id = this.$route.query.businessId || this.$route.query.oid;
      const initMeeting = ["hylc"];
      if (!initMeeting.includes(this.followData.oaModuleType)) return;
      const {data} = await processIdMeeting(id);
      this.formMeeting = data;
    },
    async unpassMeeting() {
      if (this.followData.oaModuleType == "hylc") {
        await unpassMeeting(this.formMeeting.id);
      }
    },
  },
};
