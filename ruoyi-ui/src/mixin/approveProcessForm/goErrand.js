import GoErrand from "@/views/oaWork/updateProcessForm/components/goErrand";
import {
  getProcessIdBusinessTrip,
  passBusinessTrip,
  unpassBusinessTrip,
} from "@/api/checkWork/goErrand";

export default {
  components: {
    GoErrand,
  },
  data() {
    return {};
  },
  methods: {
    async initCheckWorkGoErrand() {
      const id = this.$route.query.businessId || this.$route.query.oid;
      const { data } = await getProcessIdBusinessTrip({ processId: id });
      if (data) this.checkWork.checkWorkGoErrand = data;
    },

    passCheckWorkGoErrand() {
      const id = this.checkWork.checkWorkGoErrand.id;
      passBusinessTrip({ id });
    },

    reviewFailedCheckWorkGoErrand() {
      if (
        this.checkWork.checkWorkGoErrand &&
        this.checkWork.checkWorkGoErrand.id
      ) {
        unpassBusinessTrip({ id: this.checkWork.checkWorkGoErrand.id });
      }
    },
  },
};
