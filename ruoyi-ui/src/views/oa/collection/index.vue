<template>
  <div class="app-container">
    <div style="width: 100%; height: 30px">
      <span style="color: #9d9d9d; margin-left: 20px"
        >说明：配置OA流程中的收款人信息</span
      >
    </div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="收款人" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账号" prop="accountNumber">
        <el-input
          v-model="queryParams.accountNumber"
          placeholder="请输入账号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div style="width: 100%; height: 10px"></div>
    <el-row :gutter="10" class="mb8">
      <el-col
        v-if="changeEditType"
        :span="1.5"
        style="display: flex; align-items: center"
      >
        <span>视图：</span>
        <el-radio-group v-model="queryParams.selectType" @change="getList">
          <!-- <el-radio-button label="0" @click="querycompony()">公司付款人</el-radio-button>
          <el-radio-button label="1" @click="querypersonagelabel()">个人付款人</el-radio-button>-->
          <el-radio-button label="0">全部</el-radio-button>
          <el-radio-button label="1"
            >待我审核<span v-if="countData.myCheckCount > 0"
              >({{ countData.myCheckCount }})</span
            ></el-radio-button
          >
          <el-radio-button label="2"
            >我的提交<span v-if="countData.mySubmitCount > 0"
              >({{ countData.mySubmitCount }})</span
            ></el-radio-button
          >
        </el-radio-group>
      </el-col>
      <el-col :span="1.5" style="display: flex; align-items: center">
        <span>类型：</span>
        <el-radio-group v-model="queryParams.type" @change="changeType()">
          <!-- <el-radio-button label="0" @click="querycompony()">公司付款人</el-radio-button>
          <el-radio-button label="1" @click="querypersonagelabel()">个人付款人</el-radio-button>-->
          <el-radio-button label="2">全部</el-radio-button>
          <el-radio-button label="0">公司</el-radio-button>
          <el-radio-button label="1">个人</el-radio-button>
        </el-radio-group>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
          >新增收款人</el-button
        >
        <el-button type="primary" v-hasRole="['OA']" @click="editTypeChange"
          >编辑需审核（{{ changeEditType ? "已开启" : "已关闭" }}）</el-button
        >
      </el-col>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="traderList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="收款人" align="center" prop="userName" />
      <el-table-column label="开户行" align="center" prop="bankOfDeposit" />
      <el-table-column label="账号" align="center" prop="accountNumber" />
      <el-table-column label="简称" align="center" prop="abbreviation" />
      <el-table-column
        label="业务责任人"
        v-if="changeEditType"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="financialStaff"
      />
      <el-table-column
        label="财务责任人"
        v-if="changeEditType"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="salesman"
      />
      <el-table-column
        label="提交人"
        width="160"
        show-overflow-tooltip=""
        align="left"
        v-if="queryParams.selectType == 1&& changeEditType"
        prop="editUserNickName"
      />
      <el-table-column
        label="提交时间"
        width="160"
        show-overflow-tooltip=""
        align="left"
        v-if="queryParams.selectType == 1&& changeEditType"
        prop="editTime"
      />
      <el-table-column
        label="修改说明"
        width="160"
        v-if="queryParams.selectType == 1"
        show-overflow-tooltip=""
        align="left"
        prop="editInfo"
      />
      <el-table-column
        label="审核人"
        width="160"
        v-if="queryParams.selectType == 2&& changeEditType"
        show-overflow-tooltip=""
        align="left"
        prop="checkUserNickName"
      />
      <el-table-column
        label="审核时间"
        v-if="queryParams.selectType == 2 && changeEditType"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="checkTime"
      />

      <el-table-column
        label="审核状态"
        v-if="changeEditType"

        width="120"
        show-overflow-tooltip=""
        align="left"
        prop="remark"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.rejectFlag !== null && scope.row.confirmFlag == 0"
            >{{ scope.row.rejectFlag == 0 ? "已通过" : "已驳回" }}</span
          >
          <span
            v-if="scope.row.rejectFlag === null && scope.row.confirmFlag == 0"
            >{{
              scope.row.checkStatus == 0 ? "待业务审核" : "待财务审核"
            }}</span
          >
          <span v-if="scope.row.confirmFlag == 1">-</span>
        </template>
      </el-table-column>
      <el-table-column label="关联收款方账套" align="center" prop="isAccount">
        <template slot-scope="scope">
          <span v-if="scope.row.isAccount == 'Y'">是</span>
          <span v-if="scope.row.isAccount == 'N'">否</span>
        </template>
      </el-table-column>
      <el-table-column label="启用状态" align="center" prop="isEnable">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isEnable"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="Y"
            inactive-value="N"
            @change="updateinEnable(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="最后修改时间"
        align="center"
        prop="endUpdateTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.endUpdateTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        v-if="changeEditType"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            style="color: #ff9900"
            type="text"
            v-if="
              scope.row.rejectFlag === null &&
              scope.row.checkStatus !== null &&
              scope.row.showCheckFlag == 1
            "
            @click="getDetail(scope.row)"
            >审核</el-button
          >
          <el-button
            style="color: #ff9900"
            type="text"
            v-if="scope.row.confirmFlag == 0 && scope.row.rejectFlag !== null"
            @click="rulesConfirm(scope.row)"
            >已知悉</el-button
          >
          <el-button
            size="mini"
            type="text"
            v-if="scope.row.confirmFlag == 1"
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            size="mini"
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="editRcored(scope.row)"
            >编辑记录</el-button
          >

          <el-button
            size="mini"
            type="text"
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            v-show="
              scope.row.isEnable == 'N' &&
              ((scope.row.rejectFlag !== null && scope.row.confirmFlag == 0) ||
                scope.row.confirmFlag == 1)
            "
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        v-else
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            size="mini"
            type="text"
            @click="editRcored(scope.row)"
            >编辑记录</el-button
          >
          <el-button
            size="mini"
            type="text"
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            v-show="scope.row.isEnable == 'N'"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog title="修改记录" :visible.sync="dinamicListdeilog" width="30%">
      <el-table :data="dynamicDataList">
        <el-table-column
          property="operationTime"
          label="时间"
        ></el-table-column>
        <el-table-column
          property="operationContent"
          label="操作"
        ></el-table-column>
        <el-table-column property="operationBr" label="用户"></el-table-column>
      </el-table>
      <span slot="footer">
        <el-button @click="dinamicListdeilog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="addOrUpdate"
      :visible.sync="updateOrAddDeilog"
      width="30%"
    >
      <span>{{ this.addOrUpdateText }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cencelclose()">取 消</el-button>
        <el-button type="primary" @click="checkrepeatdata()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="收款人类型">
          <el-radio-group @change="updaterules()" v-model="form.type">
            <el-radio label="0">公司</el-radio>
            <el-radio label="1">个人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="收款人" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="开户行" prop="bankOfDeposit">
          <el-input v-model="form.bankOfDeposit" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="账号" prop="accountNumber">
          <el-input v-model="form.accountNumber" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="简称" prop="abbreviation">
          <el-input v-model="form.abbreviation" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属公司" prop="companyNo">
          <el-select
            :disabled="title == '编辑付款人'"
            v-model="form.companyNo"
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option
              v-for="item in projects"
              :key="item.companyId"
              :label="item.name"
              :value="item.companyId"
            ></el-option>
          </el-select>
          <p style="color: #999; line-height: 18px">
            所属公司属性，用于判断该付款人的权限<br />业务和财务角色，只能看到或编辑自己所属公司的付款人信息<br />不同公司下允许存在相同的付款人
          </p>
        </el-form-item>
        <el-row>
          <el-form-item label="关联收款方账套" prop="isAccount">
            <el-switch
              v-model="form.isAccount"
              active-value="Y"
              inactive-value="N"
            ></el-switch>
          </el-form-item>

          <el-form-item
            v-if="form.isAccount == 'Y'"
            label="关联账套"
            prop="accountId"
          >
            <el-select
              v-model="form.accountId"
              filterable
              size="mini"
              style="width: 300px"
              placeholder="请选择项目类型"
            >
              <el-option
                v-for="item in accountSetList"
                :key="item.id"
                :label="item.companyName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <span style="color: #9d9d9d; margin-left: 80px"
          >当记账凭证规则要求对收款方也生成凭证时，</span
        >
        <br />
        <span style="color: #9d9d9d; margin-left: 80px"
          >将在关联的账套下生成第二个记账凭证</span
        >
        <el-form-item label="启用状态" prop="isEnable">
          <el-switch v-model="form.isEnable" active-value="Y" inactive-value="N"
            >启用</el-switch
          >
        </el-form-item>
        <div v-if="changeEditType">
          <el-divider></el-divider>
          <p style="padding-left: 80px">管理员已开启编辑需审核功能</p>
          <p style="padding-left: 80px">
            如果付款人信息发生修改，必须由财务责任人、业务责任人审核确认后才能生效
            <span style="color: #409eff">规则说明</span>
            <el-tooltip placement="top">
              <div slot="content">
                管理员已开启编辑需审核功能 <br />
                所有编辑操作必须设置了财务、业务责任人后才能提交 <br />
                财务责任人提交编辑修改后，需要业务责任人审核后，本次编辑才能生效
                <br />
                业务责任人提交编辑修改后，需要财务责任人审核后，本次编辑才能生效
                <br />
                审核可以被驳回，驳回后本次编辑无效 <br />
                审核中的规则，将锁定无法被编辑 <br />
                审核通过后新规则即时生效 <br />
                可指定任任何属于财务角色的人员，作为财务责任人。如果您是财务角色，但还不是财务责任人，您提交修改后将自动成为本规则的财务责任人
                <br />
                可指定任任何属于财务角色的人员，作为业务责任人。如果您是业务角色，但还不是业务责任人，您提交修改后将自动成为本规则的业务责任人
                <br />
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </p>
          <div class="item">
            <span><i>*</i>财务负责人</span>
            <el-select
              v-model="form.salesmanList"
              size="mini"
              placeholder="请选择"
              filterable
              multiple=""
            >
              <el-option
                v-for="item in userList.caiwu"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <p
              v-if="
                caiwuType &&
                !form.salesmanList.includes(userId) &&
                !roleList.includes('caiwuAdmin')
              "
              style="margin-left: 110px; margin-bottom: 0"
            >
              当前您不是本付款人信息的财务责任人
            </p>
            <p
              v-if="
                caiwuType &&
                !form.salesmanList.includes(userId) &&
                !roleList.includes('caiwuAdmin')
              "
              style="margin-left: 110px; margin-bottom: 0"
            >
              提交修改后，您将自动成为该付款人信息的财务责任人
            </p>
          </div>
          <div class="item">
            <span><i>*</i>业务负责人</span>
            <el-select
              v-model="form.financialStaffList"
              size="mini"
              placeholder="请选择"
              filterable
              multiple=""
            >
              <el-option
                v-for="item in userList.yewu"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <p
              v-if="
                yewuType &&
                !form.financialStaffList.includes(userId) &&
                !roleList.includes('yewuAdmin')
              "
              style="margin-bottom: 0; margin-left: 110px"
            >
              当前您不是本付款人信息的业务责任人
            </p>
            <p
              v-if="
                yewuType &&
                !form.financialStaffList.includes(userId) &&
                !roleList.includes('yewuAdmin')
              "
              style="margin-bottom: 0; margin-left: 110px"
            >
              提交修改后，您将自动成为该付款人信息的业务责任人
            </p>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm"
          v-if="!changeEditType || (changeEditType && form.id == null)"
          >确 定</el-button
        >
        <el-button
          type="primary"
          @click="submitForm"
          v-if="changeEditType && form.id != null"
          >下一步</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="设置"
      :show-close="false"
      :visible.sync="editDialogType"
      width="600px"
    >
      <p>
        编辑需审核：<el-switch v-model="changeEditType2"> </el-switch
        >{{ changeEditType2 ? "已开启" : "已关闭" }}
      </p>
      <p>
        开启[编辑需审核]功能后，如果付款人信息发生修改，必须由所属的财务、业务责任人审核后才能生效
      </p>
      <p>如果财务责任人进行了修改，则需要业务责任人审核确认</p>
      <p>如果业务责任人进行了修改，则需要财务责任人审核确认</p>
      <p>如果本条付款人尚未设置财务、业务责任人，则需先设置才能进行修改</p>
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="
            editDialogType = false;
            changeEditType2 = false;
          "
          >取 消</el-button
        >
        <el-button
          type="primary"
          :disabled="!changeEditType2"
          @click="submitChangeEdit"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <editData
      :oldData="oldForm"
      :newData="form"
      v-if="editDataType"
      :accountSetList="accountSetList"
      @close="editDataType = false"
      @submit="submitEdit"
      :personData="personData"
    />
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="editSubmitType"
      width="500px"
      :show-close="false"
    >
      <p style="font-weight: bold; text-align: center">编辑申请已提交!</p>
      <p style="text-align: center">
        以下人员将在OA系统待办中收到待审核通知，审核通过后编辑内容立即生效。请及时沟通以尽快完成审核
      </p>
      <p style="text-align: center">
        <span style="font-weight: bold">{{ personData.zrr }}</span
        >：{{ personData.zrrList }}
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </span>
    </el-dialog>
    <examine
      v-if="examineType"
      :detailData="detailData"
      @close="examineType = false"
      @submit="submitExamine"
      :accountSetList="accountSetList"
    />
    <know
      v-if="knowType"
      :detailData="detailData"
      @close="knowType = false"
      @confirm="submitKnow"
    />
    <editRecord
      :changeEditType="changeEditType"
      :recordId="recordId"
      v-if="editRecordType"
      @close="editRecordType = false"
    />
  </div>
</template>

<script>
import editRecord from "./editRecord.vue";
import editData from "./editData.vue";
import examine from "./examine.vue";
import know from "./know.vue";
import {
  editExamine,
  checkEditExamine,
  getuser,
  traderDetail,
  traderCheck,
  traderConfirm,
  addNewEditInfoTrader,
  traderviewCount,
} from "@/api/oa/voucharRules";
import { allCompanyList } from "@/api/oa/processTemplate";

import {
  listTrader,
  getTrader,
  delTrader,
  addTrader,
  updateTrader,
  changeenableStatus,
  checkTrader,
} from "@/api/oa/trader";
import { getAllAccountSetsList } from "@/api/oa/voucharRules";
import {
  listDynamic,
  getDynamic,
  delDynamic,
  addDynamic,
  updateDynamic,
} from "@/api/oa/dynamic";
export default {
  components: {
    editData,
    examine,
    editRecord,
    know,
  },
  name: "Collection",
  data() {
    return {
      countData: null,

      editRecordType: false,
      recordId: null,
      knowType: false,
      examineType: false,
      detailData: null,
      editSubmitType: false,
      oldForm: null,
      editDataType: false,
      changeEditType: null,
      changeEditType2: false,
      editDialogType: false,
      //账套集合

      accountSetList: [],
      dynamicDataList: [],
      dinamicListdeilog: false,
      addOrUpdate: "",
      addOrUpdateText: "",
      updateOrAddDeilog: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      traderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        traderType: 1,
        type: 2,
        selectType: 0,
        userName: null,
        bankOfDeposit: null,
        accountNumber: null,
        abbreviation: null,
        financialStaffList: [],
        salesmanList: [],
        isEnable: null,
        endUpdateTime: null,
      },
      // 表单参数
      form: {
        financialStaffList: [],
        salesmanList: [],
      },
      // 表单校验
      rules: {},
      userList: {},
      yewuType: false,
      caiwuType: false,
      roleList: [],
      userId: "",
      personData: {},
      projects: [],
    };
  },
  created() {
    if (this.$route.query.oaNotifyStep) {
      this.queryParams.selectType = this.$route.query.oaNotifyStep;
    }
    allCompanyList().then((response) => {
      this.projects = response;
    });
    this.userId = Number(sessionStorage.getItem("userId"));
    if (sessionStorage.getItem("roleList")) {
      this.roleList = JSON.parse(sessionStorage.getItem("roleList"));
    }

    this.checkEditExamine();
    this.getList();
  },
  methods: {
    editRcored(v) {
      this.recordId = v.id;
      this.editRecordType = true;
    },
    submitKnow() {
      traderConfirm({ ...this.detailData, confirmFlag: 1 }).then((res) => {
        if (res.code == 200) {
          this.$message.success("本条通知已删除");
          this.knowType = false;
          this.getList();
        }
      });
    },
    rulesConfirm(v) {
      traderDetail({ oaApplyType: 2, oaApplyId: v.oaApplyId || v.id }).then(
        (res) => {
          if (res.code == 200) {
            this.detailData = res.data;
            this.knowType = true;
          }
        }
      );
    },
    getDetail(v) {
      traderDetail({ oaApplyType: 2, oaApplyId: v.oaApplyId || v.id }).then(
        (res) => {
          if (res.code == 200) {
            this.detailData = res.data;
            this.examineType = true;
          }
        }
      );
    },
    submitExamine(v, i) {
      //0通过1驳回
      if (v == 0) {
        this.$confirm("点击确定，本次修改将立即生效?", "审核通过", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            traderCheck({ ...this.detailData, rejectFlag: v }).then((res) => {
              if (res.code == 200) {
                this.examineType = false;
                this.$message.success("审核已通过");
                this.getList();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消",
            });
          });
      } else {
        traderCheck({
          ...this.detailData,
          rejectFlag: v,
          checkRejectInfo: i,
        }).then((res) => {
          if (res.code == 200) {
            this.examineType = false;
            this.$message.success("审核已驳回");
            this.getList();
          }
        });
      }
    },
    close() {
      this.editSubmitType = false;
      this.delSubmitType = false;
      this.editDataType = false;
      this.open = false;
      this.getList();
    },
    submitEdit(v) {
      addNewEditInfoTrader({ ...v }).then((res) => {
        if (res.code == 200) {
          this.editSubmitType = true;
        }
      });
    },
    checkEditExamine() {
      checkEditExamine({ projectType: 3 }).then((res) => {
        if (res.code == 200) {
          this.changeEditType = res.msg == 0 ? false : true;
          if (!this.changeEditType) {
            this.queryParams.selectType = 0;
          }
        }
      });
    },
    submitChangeEdit() {
      editExamine({ projectType: 3, isEnable: 1 }).then((res) => {
        if (res.code == 200) {
          this.$message.success("审核需编辑已开启");
          this.editDialogType = false;
          this.checkEditExamine();
          this.getList();
        }
      });
    },
    editTypeChange() {
      if (!this.changeEditType) {
        this.editDialogType = true;
      } else {
        this.$confirm(
          "关闭后，当前处于审核中的付款人信息编辑申请将会恢复为提交前的状态?",
          "关闭编辑需审核功能",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            editExamine({ projectType: 3, isEnable: 0 }).then((res) => {
              if (res.code == 200) {
                this.$message.success("审核需编辑已关闭");
                this.checkEditExamine();
                this.getList();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消",
            });
          });
      }
    },
    //修改校验规则
    updaterules() {
      const isNum = (rule, value, callback) => {
        const num = /^[0-9]*$/;
        if (!num.test(value)) {
          callback(new Error("只能输入数字，请检查"));
        } else {
          callback();
        }
      };
      if (this.form.type == 0) {
        this.rules = {
          companyNo: [
            { required: true, message: "请选择公司", trigger: "change" },
          ],
          userName: [
            { required: true, message: "请输入收款人", trigger: "blur" },
            {
              min: 1,
              max: 40,
              message: "长度在 1 到 40 个字符",
              trigger: "blur",
            },
          ],
          bankOfDeposit: [
            { required: true, message: "请输入开户行", trigger: "blur" },
            {
              min: 1,
              max: 40,
              message: "长度在 1 到 40 个字符",
              trigger: "blur",
            },
          ],
          accountNumber: [
            { required: true, message: "请输入账号", trigger: "blur" },
            { validator: isNum, trigger: "blur" },
          ],
          abbreviation: [
            { required: true, message: "请输入简称", trigger: "blur" },
            {
              min: 1,
              max: 40,
              message: "长度在 1 到 40 个字符",
              trigger: "blur",
            },
          ],
        };
      } else if (this.form.type == 1) {
        this.rules = {
          companyNo: [
            { required: true, message: "请选择公司", trigger: "change" },
          ],
          userName: [
            { required: true, message: "请输入收款人", trigger: "blur" },
            {
              min: 1,
              max: 40,
              message: "长度在 1 到 40 个字符",
              trigger: "blur",
            },
          ],

          accountNumber: [
            { required: true, message: "请输入账号", trigger: "blur" },
            { validator: isNum, trigger: "blur" },
          ],
        };
      }
    },
    changeType() {
      if (this.queryParams.type == 0) {
        this.querycompony();
      } else if (this.queryParams.type == 1) {
        this.querypersonage();
      } else {
        this.getList();
      }
    },
    //查询公司收款人
    querycompony() {
      this.queryParams.type = 0;
      this.getList();
    },
    //查询个人收款人
    querypersonage() {
      this.queryParams.type = 1;
      this.getList();
    },
    //修改启用状态
    updateinEnable(row) {
      let text = row.isEnable === "Y" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.userName + '"收款人吗？')
        .then(function () {
          console.log("到这了");
          return changeenableStatus(row.id, row.isEnable);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.isEnable = row.isEnable === "Y" ? "N" : "Y";
        });
    },
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      listTrader({
        ...this.queryParams,
        type: this.queryParams.type == 2 ? null : this.queryParams.type,
      }).then((response) => {
        this.traderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });

      getAllAccountSetsList().then((response) => {
        this.accountSetList = response;
      });
      traderviewCount({
        ...this.queryParams,
        type: this.queryParams.type == 2 ? null : this.queryParams.type,
      }).then((res) => {
        this.countData = res.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        traderType: "1",
        type: "0",
        userName: null,
        bankOfDeposit: null,
        accountNumber: null,
        abbreviation: null,
        isEnable: "Y",
        financialStaffList: [],
        salesmanList: [],
        isAccount: "N",
        accountId: null,
        endUpdateTime: null,
        companyNo: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      getuser().then((res) => {
        this.userList = res;
        let yewu = this.userList.yewu.find((item) => {
          return item.value == this.userId;
        });
        if (yewu) {
          this.yewuType = true;
          if (this.form.financialStaffList.includes(yewu.value)) {
            return;
          }
          this.form.financialStaffList.push(yewu.value);
          return;
        }
        let caiwu = this.userList.caiwu.find((item) => {
          return item.value == this.userId;
        });
        if (caiwu) {
          this.caiwuType = true;
          if (this.form.salesmanList.includes(caiwu.value)) {
            return;
          }
          this.form.salesmanList.push(caiwu.value);
        }
      });
      this.title = "新增收款人";
      this.open = true;
      this.updaterules();
    },
    updateDynamicList(row) {
      this.dinamicListdeilog = true;
      var dynamic = {
        oaTraderId: row.id,
      };
      listDynamic(dynamic).then((response) => {
        this.dynamicDataList = response.rows;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();

      const id = row.id || this.ids;
      this.updaterules();
      getTrader(id).then((response) => {
        this.oldForm = JSON.parse(JSON.stringify(response.data));
        this.form = response.data;
        getuser().then((res) => {
          this.userList = res;
          let yewu = this.userList.yewu.find((item) => {
            return item.value == this.userId;
          });
          if (yewu) {
            this.yewuType = true;
            if (this.form.financialStaffList.includes(yewu.value)) {
              return;
            }
            this.form.financialStaffList.push(yewu.value);
            return;
          }
          let caiwu = this.userList.caiwu.find((item) => {
            return item.value == this.userId;
          });
          if (caiwu) {
            this.caiwuType = true;
            if (this.form.salesmanList.includes(caiwu.value)) {
              return;
            }
            this.form.salesmanList.push(caiwu.value);
          }
        });
        this.open = true;
        this.title = "编辑收款人";
      });
    },
    /** 提交按钮 */
    submitForm() {
      if (this.changeEditType && this.form.salesmanList.length == 0) {
        this.$message.warning("请选择财务负责人");
        return;
      }
      if (this.changeEditType && this.form.financialStaffList.length == 0) {
        this.$message.warning("请选择业务负责人");
        return;
      }
      if (this.form.id != null && this.changeEditType) {
        this.personData = {
          tjsfr: this.yewuType
            ? "业务责任人"
            : this.caiwuType
            ? "财务责任人"
            : this.roleList.includes("caiwuAdmin")
            ? "财务管理员"
            : this.roleList.includes("yewuAdmin")
            ? "业务管理员"
            : "",
        };
        if (this.yewuType) {
          let list = [];
          this.userList.caiwu.forEach((item) => {
            this.form.salesmanList.forEach((i) => {
              if (item.value == i) {
                list.push(item);
              }
            });
          });
          this.personData.zrr = "财务责任人";
          this.personData.zrrList = list.map((item) => item.label);
        }
        if (this.caiwuType) {
          let list = [];
          this.userList.yewu.forEach((item) => {
            this.form.financialStaffList.forEach((i) => {
              if (item.value == i) {
                list.push(item);
              }
            });
          });
          this.personData.zrr = "业务责任人";
          this.personData.zrrList = list.map((item) => item.label);
        }
        this.editDataType = true;
        return;
      }
      if (this.form.id == null && this.changeEditType) {
        addNewEditInfoTrader({ ...this.form, editType: 0 }).then((response) => {
          this.$modal.msgSuccess("新增付款人成功");
          this.updateOrAddDeilog = false;
          this.open = false;
          this.getList();
        });
        return;
      }
      console.log("123");
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            this.addOrUpdate = "保存修改";
            this.addOrUpdateText = "是否确定保存对此收款人信息的修改？";
            this.updateOrAddDeilog = true;
          } else {
            this.updateOrAddDeilog = true;
            this.addOrUpdateText = "是否确定创建此收款人信息？";
            this.addOrUpdate = "新增收款人";
          }
        }
      });
    },
    submitData() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTrader(this.form).then((response) => {
              this.$modal.msgSuccess("保存成功");
              this.open = false;
              this.updateOrAddDeilog = false;
              this.getList();
            });
          } else {
            addTrader(this.form).then((response) => {
              this.$modal.msgSuccess("新增收款人成功");
              this.updateOrAddDeilog = false;
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    checkrepeatdata() {
      if (this.form.id == null) {
        checkTrader(this.form).then((response) => {
          if (response.isok == "Y") {
            this.submitData();
          } else if (response.isok == "N") {
            this.$message.error("系统已存在相同账号");
          }
        });
      } else {
        this.submitData();
      }
    },
    cencelclose() {
      this.updateOrAddDeilog = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除此收款人信息？")
        .then(function () {
          return delTrader(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/trader/export",
        {
          ...this.queryParams,
        },
        `trader_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="less" scoped>
.item {
  margin-bottom: 12px;
  /deep/ .el-input__inner {
    width: 250px !important;
  }
  span {
    display: inline-block;
    width: 100px;
    font-weight: bold;
    text-align: right;
    margin-right: 12px;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
</style>
