<template>
  <div>
    <el-dialog
      title="编辑记录"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <el-table border="" :data="tableData" style="width: 100%">
        <el-table-column prop="editTime" label="编辑日期" width="160" />
        <el-table-column prop="editUserNickName" label="编辑人员" />
        <el-table-column
          prop="date"
          label="编辑人身份"
          width="100"
          v-if="changeEditType"
        >
          <template slot-scope="scope">
            {{ identity(scope.row.editIdentity) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="changeEditType"
          prop="editInfo"
          label="修改说明"
          show-overflow-tooltip=""
          width="150"
        />
        <el-table-column
          prop="checkUserNickName"
          label="审核人"
          width="100"
          v-if="changeEditType"
        />
        <el-table-column
          prop="date"
          label="审核人身份"
          width="100"
          v-if="changeEditType"
        >
          <template slot-scope="scope">
            {{ identity(scope.row.checkIdentity) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="checkTime"
          label="审核时间"
          width="160"
          v-if="changeEditType"
        />
        <el-table-column
          prop="date"
          label="审核状态"
          width="100"
          v-if="changeEditType"
        >
          <template slot-scope="scope">
            {{ scope.row.checkStatus == 0 ? "待业务审核" : "待财务审核" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="checkRejectInfo"
          label="驳回原因"
          show-overflow-tooltip=""
          v-if="changeEditType"
          width="150"
        />
        <el-table-column prop="date" width="130" label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="see(scope.row)"
              >查看编辑内容</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
    <preview v-if="previewType" @close="previewType = false" :detail="detail" />
  </div>
</template>

<script>
import { traderDetail, selectEditRecord2 } from "@/api/oa/voucharRules";
import preview from "./preview.vue";
export default {
  components: {
    preview,
  },
  props: {
    recordId: Number || String,
    changeEditType: Boolean,
  },
  data() {
    return {
      dialogVisible: true,
      previewType: false,
      tableData: [],
      detail: null,
    };
  },
  mounted() {
    selectEditRecord2({ oaApplyType: 2, oaApplyId: this.recordId }).then(
      (res) => {
        if (res.code == 200) {
          this.tableData = res.data;
        }
      }
    );
  },
  methods: {
    identity(e) {
      if (e == 0) {
        return "财务负责人";
      } else if (e == 1) {
        return "业务负责人";
      } else if (e == 2) {
        return "财务管理员";
      } else if (e == 3) {
        return "业务管理员";
      } else if (e == 9) {
        return "超级管理员";
      }
    },
    see(v) {
      this.detail = v;
      this.previewType = true;
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
</style>