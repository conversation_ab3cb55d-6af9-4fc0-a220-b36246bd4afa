<template>
  <div class="app-container">
    <div style="margin-bottom: 10px">
      本功能用于法催-法律诉讼业务自动对账<br>
      用户导入对账单文件，自动与系统业务数据、回款情况统计数据做比对，展示数据差异
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="openUploadFile(1)"
          v-hasPermi="['lawcoll:check:list']"
        >非安徽中钧数据核对</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="openUploadFile(2)"
          v-hasPermi="['lawcoll:check:list']"
        >安徽中钧数据核对</el-button>
      </el-col>
    </el-row>
    <div style="color: #cccccc; margin-bottom: 8px">历史核对记录，共{{total}}条</div>
    <el-table v-loading="loading" :data="historyList" @selection-change="handleSelectionChange">
      <el-table-column type="index" label="序号" align="left" />
      <el-table-column label="核对日期" align="left" prop="checkDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.checkDate, '{y}年{m}月{d}日') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合作方" align="left" prop="cust" width="180">
        <template slot-scope="scope">
          <span>{{custs[scope.row.cust]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="前置费用" align="left" prop="preFee" width="100">
        <template slot-scope="scope">
          <span>{{ preFees[scope.row.preFee]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作人" align="left" prop="createBy" width="100"/>
      <el-table-column label="备注" align="left" prop="remark" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{scope.row.remark.length>20? scope.row.remark.substring(0,20) +"...":scope.row.remark }}</span>
          <el-button v-if="scope.row.remark.length>20" type="text" @click="showRemark(scope.row.remark)">更多</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="showDetail(scope.row)"
            v-hasPermi="['lawcoll:check:query']"
          >查看详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleExport(scope.row)"
            v-hasPermi="['lawcoll:check:export']"
          >导出文件</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['lawcoll:check:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 保存核对结果 -->
    <el-dialog :title="title" :visible.sync="isShowRemark" width="500px" append-to-body>
      <span>
        {{remark}}
      </span>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCheck, getCheck, delCheck, addCheck, updateCheck } from "@/api/lawcoll/check";
import moment from "moment";
export default {
  name: "LawCollCheck",
  data() {
    return {
      custs: {
        'ahzj': '安徽中钧',
        'hbjf': '湖北锦发',
        'guohao': '国浩律师事务所',
        'gdzm': '广东众民',
        'bjly': '北京绿韵'
      },
      preFees: {
        '0': '无',
        '1': '有',
        '2': '武汉/朝阳-无',
        '3': '武汉/朝阳-有',
        '-': '-',
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 法催对账历史表格数据
      historyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkDate: null,
        importType: "1",
      },
      // 表单参数
      form: {},
      isShowRemark: false,
      remark: ""
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询法催对账历史列表 */
    getList() {
      this.loading = true;
      listCheck(this.queryParams).then(response => {
        this.historyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        fundCode: null,
        batchNo: null,
        batchName: null,
        name: null,
        applyNo: null,
        loanTime: null,
        sRepayTotalAmt: null,
        cRepayTotalAmt: null,
        sRepayPrinAmt: null,
        cRepayPrinAmt: null,
        sOvdDays: null,
        cOvdDays: null,
        sServiceRate: null,
        cServiceRate: null,
        sServiceAmt: null,
        cServiceAmt: null,
        repaySrc: null,
        compensatoryDate: null,
        compensatoryTotalAmt: null,
        compensatoryPrinAmt: null,
        status: "0",
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 打开上传文件框 */
    openUploadFile(type) {
      if(type ==1){
        const obj = { name: "legalProceedingsUpload" };
        this.$tab.closeOpenPage(obj);
      }else{
        const obj = { name: "legalProceedingsAhUpload" };
        this.$tab.closeOpenPage(obj);
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCheck(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改法催对账历史";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCheck(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCheck(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除数据项？').then(function() {
        return delCheck(row.importIdentify);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport(row) {
      let queryParams = {
        importIdentify: row.importIdentify,
      }
      if(row.cust == 'ahzj'){
        this.download('lawcoll/check/exportAh', {
          ...queryParams
        }, `法律诉讼业务_安徽_${moment(new Date()).format("YYYYMMDDHHmmss")}.xlsx`)
      }else{
        this.download('lawcoll/check/exportLp', {
          ...queryParams
        }, `法律诉讼业务_非安徽_${moment(new Date()).format("YYYYMMDDHHmmss")}.xlsx`)
      }
    },
    showDetail(row){
      if(row.cust == 'ahzj'){
        const obj = {name: 'legalProceedingsAhDetail', params: {importIdentify: row.importIdentify, remark: row.remark}};
        this.$tab.closeOpenPage(obj);
      }else{
        const obj = {name: 'legalProceedingsDetail', params: {importIdentify: row.importIdentify, remark: row.remark, preFee: row.preFee}};
        this.$tab.closeOpenPage(obj);
      }
    },
    showRemark(remark){
      this.title = "备注";
      this.remark = remark;
      this.isShowRemark = true;
    },
    close(){
      this.isShowRemark = false;
    }
  }
};
</script>
