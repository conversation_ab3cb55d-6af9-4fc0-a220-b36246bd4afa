<template>
    <div>
        <el-dialog title="修改工单" :visible.sync="dialogVisible" width="950px" :before-close="handleClose">
            <el-form ref="form" :model="form" label-width="130px" :rules="rules">
                <!-- 工单主题 -->
                <el-form-item label="工单主题" prop="workOrderTitle">
                    <el-input v-model="form.workOrderTitle" placeholder="请输入" maxlength="20"></el-input>
                    <div style="color: #aaaaaa;">用简洁明确的语言概括需求主题，不超过20字，如“XX 系统功能优化需求”</div>
                </el-form-item>
                <div style="display: flex;">
                    <el-form-item label="工单类型" prop="workOrderType">
                        <el-select v-model="form.workOrderType" placeholder="请选择工单类型">
                            <el-option v-for="item in typeOptions" :key="item.dictValue" :label="item.dictLabel"
                                :value="item.dictValue"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="需求提出人部门" prop="requesterDepartmentName">
                        <el-input readonly v-model="form.requesterDepartmentName" placeholder="请选择部门"> </el-input>
                        <el-button type="text" style="margin-left: 5px;" @click="selectDep(0)">选择</el-button>
                    </el-form-item>
                </div>
                <el-form-item label="需求背景" prop="requirementBackground">
                    <el-input type="textarea" :rows="4" v-model="form.requirementBackground"
                        placeholder="详细阐述提出该需求的原因和背景，包括业务现状、存在的问题或期望达成的目标可结合数据、案例说明，增强需求的合理性" maxlength="5000">
                    </el-input>
                </el-form-item>
                <el-form-item label="需求目的" prop="requirementPurpose">
                    <el-input type="textarea" :rows="4" v-model="form.requirementPurpose"
                        placeholder="描述需求实现后预期达到的目的，如提高工作效率、降低成本等，便于评估需求价值" maxlength="5000">
                    </el-input>
                </el-form-item>
                <el-form-item label="需求描述" prop="requirementDescription">
                    <el-input type="textarea" :rows="4" v-model="form.requirementDescription"
                        placeholder="从功能、性能、业务流程等方面详细说明需求内容。功能需求需列举具体功能点;性能需求注明响应时间、吞吐量等指标;业务流程用流程图或文字清晰描述，确保需求无歧义"
                        maxlength="5000">
                    </el-input>
                </el-form-item>

                <div style="display: flex;">
                    <el-form-item label="期望完成时间">
                        <el-date-picker v-model="form.expectedCompletionDate" type="date" placeholder="选择日期时间"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="需求优先级" prop="requirementPriority">
                        <el-select v-model="form.requirementPriority" placeholder="请选择优先级">
                            <el-option v-for="item in xuqiuOptions" :key="item.dictValue" :label="item.dictLabel"
                                :value="item.dictValue"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <el-form-item label="需求备注">
                    <el-input type="textarea" :rows="4" v-model="form.requirementRemark"
                        placeholder="请输入其他需要补充说明，特殊备注的情况" maxlength="5000">
                    </el-input>
                </el-form-item>
                <el-divider></el-divider>
                <div style="display: flex;">
                    <el-form-item label="工单状态">
                        <el-select v-model="form.workOrderStatus" placeholder="请选择">
                            <el-option v-for="item in statusOptions" :key="item.dictValue" :label="item.dictLabel"
                                :value="item.dictValue"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="研发进度">
                        <el-select v-model="form.rndProgress" placeholder="请选择">
                            <el-option v-for="item in jinduOptions" :key="item.dictValue" :label="item.dictLabel"
                                :value="item.dictValue"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div style="display: flex;">
                    <el-form-item label="需求实现系统">
                        <el-select v-model="form.requirementImplementationSystem" placeholder="请选择">
                            <el-option v-for="item in systemOptions" :key="item.dictValue" :label="item.dictLabel"
                                :value="item.dictValue"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="系统功能模块">
                        <el-input v-model="form.systemFunctionModule" placeholder="请输入"> </el-input>
                    </el-form-item>
                </div>
                <el-form-item label="项目风险">
                    <el-select v-model="form.projectRisk" placeholder="请选择">
                        <el-option v-for="item in fengxianOptions" :key="item.dictValue" :label="item.dictLabel"
                            :value="item.dictValue"></el-option>
                    </el-select>
                </el-form-item>
                <div style="display: flex;" v-for="(item, index) in xqUsers" :key="index">
                    <el-form-item label="需求涉及部门">
                        <el-input readonly v-model="item.departmentName" placeholder="请选择部门"> </el-input>
                        <el-button type="text" style="margin-left: 5px;" @click="selectDep(1, index)">选择</el-button>
                    </el-form-item>
                    <el-form-item label="部门需求负责人">
                        <el-input readonly v-model="item.personnelName" placeholder="部门需求负责人"></el-input>
                        <el-button type="text" style="margin-left: 5px;" @click="selectUser(index)">选择</el-button>
                        <el-button type="text" icon="el-icon-plus" v-if="index == 0" @click="addDept">添加部门</el-button>
                        <el-button v-if="index > 0" type="text" icon="el-icon-delete"
                            @click="removeDept(index)"></el-button>
                    </el-form-item>
                </div>
                <el-form-item label="需求外部相关人">
                    <el-input type="textarea" :rows="4" v-model="form.externalStakeholderInfo"
                        placeholder="从功能、性能、业务流程等方面详细说明需求内容。功能需求需列举具体功能点;性能需求注明响应时间、吞吐量等指标;业务流程用流程图或文字清晰描述，确保需求无歧义"
                        maxlength="5000">
                    </el-input>
                </el-form-item>
                <el-divider></el-divider>
                <div style="position: relative;">
                    <span style="position: absolute;top: 5px;left: 38px;color: red;font-size: 16px;">*</span>
                    <el-form-item label="当前执行人">

                        <el-select multiple v-model="zxIds">
                            <el-option v-for="(item, index) in zxUsers" :key="index" :value="item.userId"
                                :label="item.nickName"></el-option>
                        </el-select>
                        <el-button type="text" style="margin-left: 5px;" @click="selectPostUser(0)">选择</el-button>
                    </el-form-item>
                </div>
                <div style="display: flex;">
                    <el-form-item label="产品经理">
                        <!-- <el-input readonly v-model="cpNames" placeholder="请选择"> </el-input> -->
                        <el-select multiple v-model="cpIds">
                            <el-option v-for="(item, index) in cpUsers" :key="index" :value="item.userId"
                                :label="item.nickName"></el-option>
                        </el-select>
                        <el-button type="text" style="margin-left: 5px;" @click="selectPostUser(1)">选择</el-button>
                    </el-form-item>
                    <el-form-item label="项目经理">
                        <!-- <el-input readonly v-model="xmNames" placeholder="请选择"> </el-input> -->
                        <el-select multiple v-model="xmIds">
                            <el-option v-for="(item, index) in xmUsers" :key="index" :value="item.userId"
                                :label="item.nickName"></el-option>
                        </el-select>
                        <el-button type="text" style="margin-left: 5px;" @click="selectPostUser(2)">选择</el-button>
                    </el-form-item>
                </div>
                <div style="display: flex;">
                    <el-form-item label="开发人员">
                        <!-- <el-input readonly v-model="kfNames" placeholder="请选择"> </el-input> -->
                        <el-select multiple v-model="kfIds">
                            <el-option v-for="(item, index) in kfUsers" :key="index" :value="item.userId"
                                :label="item.nickName"></el-option>
                        </el-select>
                        <el-button type="text" style="margin-left: 5px;" @click="selectPostUser(3)">选择</el-button>
                    </el-form-item>
                    <el-form-item label="测试人员">
                        <!-- <el-input readonly v-model="csNames" placeholder="请选择"> </el-input> -->
                        <el-select multiple v-model="csIds">
                            <el-option v-for="(item, index) in csUsers" :key="index" :value="item.userId"
                                :label="item.nickName"></el-option>
                        </el-select>
                        <el-button type="text" style="margin-left: 5px;" @click="selectPostUser(4)">选择</el-button>
                    </el-form-item>
                </div>
                <el-form-item label="需求排期">
                    <el-date-picker v-model="xqDate" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="设计排期">
                    <el-date-picker v-model="sjDate" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="开发排期">
                    <el-date-picker v-model="kfDate" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="测试排期">
                    <el-date-picker v-model="csDate" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="验收测试排期">
                    <el-date-picker v-model="ysDate" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="预计上线时间">
                    <el-date-picker v-model="form.expectedGoLiveDate" type="date" placeholder="选择日期时间"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <!-- 附件上传 -->
                <el-form-item label="需求附件">
                    <el-upload class="upload-demo" :auto-upload="false" action :on-preview="handlePreview"
                        :on-success="handleFileSuccess" :before-upload="beforeUpload" :before-remove="beforeRemove"
                        :on-change="handleChange" :on-remove="handleRemove" :on-progress="handleFileUploadProgress"
                        multiple :file-list="fileList">
                        <el-button size="mini"><i class="el-icon-upload2"></i>上传附件</el-button>
                    </el-upload>
                </el-form-item>

            </el-form>

            <!-- 操作按钮 -->
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>

                <el-button type="primary" @click="submitForm(2)">保存</el-button>
            </div>
            <el-image ref="previewImg" v-show="false" :src="photoUrl" :preview-src-list="imagePreviewUrls"></el-image>
        </el-dialog>
        <UserDepPostSelect :title="userDep.title" :multiple="userDep.multiple" :rowKey="userDep.rowKey"
            :multipleSelectionProp="userDep.multipleSelectionUserDep" v-model="userDep.open"
            @on-submit-success-user="userSuccess" @on-submit-success-dep="depSuccess" />
    </div>
</template>

<script>
import { formRequest } from '@/utils/request'
import { getToken } from "@/utils/auth";
import { getDicts } from "@/api/system/dict/data";
import { downloadByUrl } from "@/api/oa/processTemplate";

import { getFilesPathMapping } from "@/api/cdlb/files";
export default {
    props: {
        itemData: Object, // 编辑数据
    },
    data() {
        return {
            xqDate: [],
            sjDate: [],
            kfDate: [],
            csDate: [],
            ysDate: [],
            cpIds: [],
            xmIds: [],
            kfIds: [],
            csIds: [],
            zxIds: [],
            zxUsers: [],
            cpUsers: [],
            xmUsers: [],
            kfUsers: [],
            csUsers: [],
            xqUsers: [{ departmentId: '', departmentName: '', personnelId: '', personnelName: '', sourceFirstCategory: '', sourceSecondCategory: '' }],
            userDep: {
                title: '',
                multiple: false,
                rowKey: '',
                open: false,
                multipleSelectionUserDep: []
            },
            rules: {
                workOrderTitle: [{ required: true, message: '工单主题不能为空', trigger: 'blur' }],

                workOrderType: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
                requesterDepartmentName: [{ required: true, message: '请选择提出部门', trigger: 'change' }],
                requirementBackground: [{ required: true, message: '需求背景不能为空', trigger: 'blur' }],
                requirementPurpose: [{ required: true, message: '需求目的不能为空', trigger: 'blur' }],
                requirementDescription: [{ required: true, message: '需求描述不能为空', trigger: 'blur' }],
                requirementPriority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
            },
            imagePreviewUrls: [],
            photoUrl: "",
            fileList: [],
            upload: {
                headers: { Authorization: "Bearer " + getToken() },
                // 上传的地址
                url: process.env.VUE_APP_BASE_API + "/lyxSystem/dayCheck/lyxUploadFile",
                //当前步骤id
                stepId: null,
                businessId: null,
            },
            dialogVisible: true,
            form: {
                currentExecutorName: '',
                workOrderTitle: '',
                workOrderType: '',
                requesterDepartmentName: '',
                requesterDepartmentId: '',
                requirementBackground: '',
                requirementPurpose: '',
                requirementDescription: '',
                expectedCompletionDate: '',
                requirementPriority: '',
                requirementRemark: '',
                departmentPersonnelListJson: [],
                workOrderStatus: '',
                currentExecutor: '',
                currentExecutorName: '',
                requirementImplementationSystem: '',
                rndProgress: '',
                systemFunctionModule: '',
                projectRisk: '',
                externalStakeholderInfo: '',
                expectedGoLiveDate: ''
            },

            deptOptions: [], // 从接口获取的部门数据
            selectDetType: null,
            listIndex: null,
            typeOptions: [],
            xuqiuOptions: [],
            statusOptions: [],
            jinduOptions: [],
            systemOptions: [],
            fengxianOptions: []
        };
    },
    mounted() {
        this.getDicts()
        this.getDetail()
    },
    methods: {
        getDetail() {
            console.log(this.itemData);

            if (this.itemData) {
                this.form.workOrderTitle = this.itemData.workOrderTitle
                this.form.workOrderType = this.itemData.workOrderType
                this.form.requesterDepartmentName = this.itemData.requesterDepartmentName
                this.form.requesterDepartmentId = this.itemData.requesterDepartmentId
                this.form.requirementBackground = this.itemData.requirementBackground
                this.form.requirementPurpose = this.itemData.requirementPurpose
                this.form.requirementDescription = this.itemData.requirementDescription
                this.form.expectedCompletionDate = this.itemData.expectedCompletionDate
                this.form.requirementPriority = this.itemData.requirementPriority
                this.form.requirementRemark = this.itemData.requirementRemark
                this.form.workOrderStatus = this.itemData.workOrderStatus
                this.form.currentExecutor = this.itemData.currentExecutor
                this.form.currentExecutorName = this.itemData.currentExecutorName
                this.form.requirementImplementationSystem = this.itemData.requirementImplementationSystem
                this.form.rndProgress = this.itemData.rndProgress
                this.form.systemFunctionModule = this.itemData.systemFunctionModule
                this.form.projectRisk = this.itemData.projectRisk
                this.form.externalStakeholderInfo = this.itemData.externalStakeholderInfo
                this.form.expectedGoLiveDate = this.itemData.expectedGoLiveDate
                this.form.requirementSubmissionTimeStr = this.itemData.requirementSubmissionTime ? this.$format(this.itemData.requirementSubmissionTime, 'yyyy-MM-dd HH:mm:ss') : ''
                this.form.acceptanceTime = this.itemData.acceptanceTime
                this.form.requesterId = this.itemData.requesterId

                this.xqDate = this.itemData.requirementScheduleStartDate ? [this.itemData.requirementScheduleStartDate, this.itemData.requirementScheduleEndDate] : []
                this.sjDate = this.itemData.designScheduleStartDate ? [this.itemData.designScheduleStartDate, this.itemData.designScheduleEndDate] : []
                this.kfDate = this.itemData.developmentScheduleStartDate ? [this.itemData.developmentScheduleStartDate, this.itemData.developmentScheduleEndDate] : []
                this.csDate = this.itemData.testingScheduleStartDate ? [this.itemData.testingScheduleStartDate, this.itemData.testingScheduleEndDate] : []
                this.ysDate = this.itemData.acceptanceTestingScheduleStartDate ? [this.itemData.acceptanceTestingScheduleStartDate, this.itemData.acceptanceTestingScheduleEndDate] : []
                this.form.id = this.itemData.id
                if (this.itemData.fileList && this.itemData.fileList.length > 0) {
                    this.form.fileIds = this.itemData.fileList.map(item => { return item.id })
                }
                if (this.itemData.personnelList && this.itemData.personnelList.length > 0) {
                    this.itemData.personnelList.forEach(item => {
                        item.deptId = item.departmentId
                        item.deptName = item.departmentName
                        item.userId = item.personnelId
                        item.nickName = item.personnelName
                    })
                    this.zxUsers = this.itemData.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_ZHIXINGREN')
                    this.zxIds = this.zxUsers.map(item => { return item.userId })
                    this.cpUsers = this.itemData.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_CHANPINJINGLI')
                    this.cpIds = this.cpUsers.map(item => { return item.userId })
                    this.xmUsers = this.itemData.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_XIANGMUJINGLI')
                    this.xmIds = this.xmUsers.map(item => { return item.userId })
                    this.kfUsers = this.itemData.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_KAIFARENYUAN')
                    this.kfIds = this.kfUsers.map(item => { return item.userId })
                    this.csUsers = this.itemData.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_CESHIRENYUAN')
                    this.csIds = this.csUsers.map(item => { return item.userId })
                    this.xqUsers = this.itemData.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_BUMENXUQIUFUZEREN').length > 0 ? this.itemData.personnelList.filter(item => item.sourceSecondCategory == 'ORDER_RENYUAN_BUMENXUQIUFUZEREN') : [{ departmentId: '', departmentName: '', personnelId: '', personnelName: '', sourceFirstCategory: '', sourceSecondCategory: '' }]
                }
                this.fileList = this.itemData.fileList.map(item => { return { name: item.fileName, url: item.fileAddress, id: item.id } })

            }
        },
        selectPostUser(e) {
            this.selectPostIndex = e
            this.listIndex = null
            console.log(this.listIndex);
            if (e == 0) {
                this.userDep.multipleSelectionUserDep = this.zxUsers
            } else if (e == 1) {
                this.userDep.multipleSelectionUserDep = this.cpUsers
            }
            else if (e == 2) {
                this.userDep.multipleSelectionUserDep = this.xmUsers
            }
            else if (e == 3) {
                this.userDep.multipleSelectionUserDep = this.kfUsers
            }
            else if (e == 4) {
                this.userDep.multipleSelectionUserDep = this.csUsers
            }

            this.userDep.multiple = true
            this.userDep.title = 'user'
            this.userDep.rowKey = 'userId'
            this.userDep.open = true
        },
        getDicts() {
            Promise.all([getDicts("work_order_type"), getDicts("work_order_xuqiu_type"), getDicts("work_order_status_type"), getDicts("work_order_yanfa_jindu_type"), getDicts("work_order_shixian_system"), getDicts("work_order_project_fengxian")]).then((res) => {
                console.log(res);
                this.typeOptions = res[0].data
                this.xuqiuOptions = res[1].data
                this.statusOptions = res[2].data
                this.jinduOptions = res[3].data
                this.systemOptions = res[4].data
                this.fengxianOptions = res[5].data
            })
        },
        selectUser(index) {
            this.selectPostIndex = null
            this.userDep.multipleSelectionUserDep = []
            this.listIndex = index
            this.userDep.title = 'user'
            this.userDep.rowKey = 'userId'
            this.userDep.open = true
        },
        selectDep(e, index) {
            this.listIndex = index
            this.selectDetType = e
            this.userDep.title = 'dep'
            this.userDep.rowKey = 'deptId'
            this.userDep.open = true
        },
        userSuccess(v) {
            console.log(v);
            console.log(this.listIndex);

            if (this.listIndex !== null && this.listIndex !== undefined) {
                this.xqUsers[this.listIndex].personnelName = v[0].nickName
                this.xqUsers[this.listIndex].personnelId = !v[0].userId ? '' : v[0].userId + ''
            } else {
                switch (this.selectPostIndex) {
                    case 0:
                        this.zxIds = v.map(item => { return item.userId })
                        this.zxUsers = v
                        this.$refs.form.validateField('zxIds')
                        break;
                    case 1:
                        this.cpIds = v.map(item => { return item.userId })
                        this.cpUsers = v
                        break;
                    case 2:
                        this.xmIds = v.map(item => { return item.userId })
                        this.xmUsers = v
                        break;
                    case 3:
                        this.kfIds = v.map(item => { return item.userId })
                        this.kfUsers = v
                        break;
                    case 4:
                        this.csIds = v.map(item => { return item.userId })
                        this.csUsers = v
                        break;

                }
            }

        },
        depSuccess(v) {
            console.log(v);
            if (!this.selectDetType) {
                this.form.requesterDepartmentName = v[0].deptName
                this.form.requesterDepartmentId = v[0].deptId + ''
                this.$refs.form.validateField('requesterDepartmentName')
            } else {
                this.xqUsers[this.listIndex].departmentName = v[0].deptName
                this.xqUsers[this.listIndex].departmentId = v[0].deptId + ''
            }
        },
        addDept() {
            this.xqUsers.push(
                {
                    departmentId: '', departmentName: '', personnelId: '', personnelName: '', sourceFirstCategory: '', sourceSecondCategory: ''
                })
        },
        removeDept(index) {
            this.xqUsers.splice(index, 1)
        },
        submitForm(e) {
            // 提交逻辑
            if (this.zxIds.length == 0) {
                this.$message.error('当前执行人不能为空')
                return
            }
            this.$refs.form.validate((valid) => {
                if (valid) {
                    console.log(this.form);
                    this.form.files = this.fileList
                    if (this.zxUsers.length > 0) {

                        this.zxUsers = this.zxUsers.filter(item => this.zxIds.includes(item.userId))
                        this.zxUsers.forEach(item => {
                            this.form.departmentPersonnelListJson.push({
                                departmentId: !item.deptId ? '' : item.deptId + '', departmentName: item.deptChain, personnelId: item.userId + '', personnelName: item.nickName, sourceFirstCategory: 'ORDER_TABLE_ZHUBIAO', sourceSecondCategory: 'ORDER_RENYUAN_ZHIXINGREN'
                            })
                        })
                    }
                    if (this.cpUsers.length > 0) {
                        //取cpUsers和cpIds的交集
                        this.cpUsers = this.cpUsers.filter(item => this.cpIds.includes(item.userId))
                        this.cpUsers.forEach(item => {
                            this.form.departmentPersonnelListJson.push({
                                departmentId: !item.deptId ? '' : item.deptId + '', departmentName: item.deptChain, personnelId: item.userId + '', personnelName: item.nickName, sourceFirstCategory: 'ORDER_TABLE_ZHUBIAO', sourceSecondCategory: 'ORDER_RENYUAN_CHANPINJINGLI'
                            })
                        })
                    }
                    if (this.xmUsers.length > 0) {
                        //取xmUsers和xmIds的交集
                        this.xmUsers = this.xmUsers.filter(item => this.xmIds.includes(item.userId))
                        this.xmUsers.forEach(item => {
                            this.form.departmentPersonnelListJson.push({
                                departmentId: !item.deptId ? '' : item.deptId + '', departmentName: item.deptChain, personnelId: item.userId + '', personnelName: item.nickName, sourceFirstCategory: 'ORDER_TABLE_ZHUBIAO', sourceSecondCategory: 'ORDER_RENYUAN_XIANGMUJINGLI'
                            })
                        })
                    }
                    if (this.kfUsers.length > 0) {
                        //取kfUsers和kfIds的交集
                        this.kfUsers = this.kfUsers.filter(item => this.kfIds.includes(item.userId))
                        this.kfUsers.forEach(item => {
                            this.form.departmentPersonnelListJson.push({
                                departmentId: !item.deptId ? '' : item.deptId + '', departmentName: item.deptChain, personnelId: item.userId + '', personnelName: item.nickName, sourceFirstCategory: 'ORDER_TABLE_ZHUBIAO', sourceSecondCategory: 'ORDER_RENYUAN_KAIFARENYUAN'
                            })
                        })
                    }
                    if (this.csUsers.length > 0) {
                        //取csUsers和csIds的交集
                        this.csUsers = this.csUsers.filter(item => this.csIds.includes(item.userId))
                        console.log(this.csUsers, '--');

                        this.csUsers.forEach(item => {
                            this.form.departmentPersonnelListJson.push({
                                departmentId: !item.deptId ? '' : item.deptId + '', departmentName: item.deptChain, personnelId: item.userId + '', personnelName: item.nickName, sourceFirstCategory: 'ORDER_TABLE_ZHUBIAO', sourceSecondCategory: 'ORDER_RENYUAN_CESHIRENYUAN'
                            })
                        })
                    }
                    if (this.xqUsers.length > 0) {


                        this.xqUsers.forEach(item => {
                            this.form.departmentPersonnelListJson.push({
                                departmentId: item.departmentId, departmentName: item.departmentName, personnelId: item.personnelId, personnelName: item.personnelName, sourceFirstCategory: 'ORDER_TABLE_ZHUBIAO', sourceSecondCategory: 'ORDER_RENYUAN_BUMENXUQIUFUZEREN'
                            })
                        })
                    }
                    console.log(this.form.departmentPersonnelListJson);


                    if (this.xqDate && this.xqDate.length > 0) {
                        this.form.requirementScheduleStartDate = this.xqDate[0]
                        this.form.requirementScheduleEndDate = this.xqDate[1]
                    } else {
                        this.form.requirementScheduleStartDate = null
                        this.form.requirementScheduleEndDate = null
                    }
                    if (this.sjDate && this.sjDate.length > 0) {
                        this.form.designScheduleStartDate = this.sjDate[0]
                        this.form.designScheduleEndDate = this.sjDate[1]
                    } else {
                        this.form.designScheduleStartDate = null
                        this.form.designScheduleEndDate = null
                    }
                    if (this.kfDate && this.kfDate.length > 0) {
                        this.form.developmentScheduleStartDate = this.kfDate[0]
                        this.form.developmentScheduleEndDate = this.kfDate[1]
                    } else {
                        this.form.developmentScheduleStartDate = null
                        this.form.developmentScheduleEndDate = null
                    }
                    if (this.csDate && this.csDate.length > 0) {
                        this.form.testingScheduleStartDate = this.csDate[0]
                        this.form.testingScheduleEndDate = this.csDate[1]
                    } else {
                        this.form.testingScheduleStartDate = null
                        this.form.testingScheduleEndDate = null
                    }
                    if (this.ysDate && this.ysDate.length > 0) {
                        this.form.acceptanceTestingScheduleStartDate = this.ysDate[0]
                        this.form.acceptanceTestingScheduleEndDate = this.ysDate[1]
                    } else {
                        this.form.acceptanceTestingScheduleStartDate = null
                        this.form.acceptanceTestingScheduleEndDate = null
                    }
                    console.log(this.form);
                    //如果row.personnelList中的对象departmentId和personnelId都为空，那么删除这个对象
                    if (this.form.departmentPersonnelListJson.length > 0) {
                        this.form.departmentPersonnelListJson = this.form.departmentPersonnelListJson.filter(item => item.departmentId || item.personnelId)
                    }
                    let data = {
                        ...this.form,
                        departmentPersonnelListJson: JSON.stringify(this.form.departmentPersonnelListJson),
                        dynamicType: 'ORDER_DONGTAI_XIUGAIGONGDAN'
                    }


                    formRequest(`/system/workOrder/${this.itemData ? 'update' : 'create'}`, {
                        ...data

                    }).then(res => {
                        console.log(res);
                        this.$message.success('提交成功');
                        this.$emit('success', res.data)
                    })



                }
            });
        },
        saveDraft() {
            // 保存草稿逻辑
            this.$refs.form.validate((valid) => {
                if (valid) {

                }
            });
        },
        handleClose(id) {
            this.$emit('close');
        },

        handleChange(file, fileList) {

            this.fileList.push(file.raw)
            console.log(this.fileList);
        },
        handleRemove(file, fileList) {

            console.log(file);

            var findex = this.fileList
                .map((f) => f.uid)
                .indexOf(file.uid);
            if (findex > -1) {
                this.fileList.splice(findex, 1);
                if (this.form.fileIds) {
                    this.form.fileIds.splice(findex, 1);
                }
            }
        },
        beforeRemove(file, upFileList) {

        },
        beforeUpload(file, fileList) {


            //定义文件最大的限制，单位：MB
            var maxSize = 2048;
            //文件的大小
            var fileSize = file.size / 1024 / 1024;
            //进行文件的判断
            if (fileSize <= 0) {
                this.$message.error("上传文件大小不能为 0 MB");
                return false;
            } else if (fileSize < maxSize) {
                let promise = new Promise((resolve) => {
                    this.$nextTick(function () {
                        resolve(true);
                    });
                });
                return promise;
            } else {
                this.$message.error(`上传文件大小不能超过2G!`);
                return false;
            }
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) { },
        handleFileSuccess(response, file, fileList) {
            this.fileList = fileList;
            console.log(this.fileList);

            console.log(this.fileList);
        },
        handlePreview(file) {
            console.log(file, "===");

            if (file.name.endsWith(".pdf")) {
                //文件是pdf格式
                getFilesPathMapping().then((resp) => {
                    this.pdfUrl = resp.msg + (file.url || file.fileUrl);
                    window.open(this.pdfUrl);
                    return;
                });
                return;
            } else if (
                file.name.endsWith(".jpg") ||
                file.name.endsWith(".jpeg") ||
                file.name.endsWith(".png") ||
                file.name.endsWith(".gif")
            ) {
                //文件是图片格式
                getFilesPathMapping().then((resp) => {
                    this.photoUrl = resp.msg + (file.url || file.fileUrl);
                    console.log(this.photoUrl);
                    let array = new Set([]);
                    array.add(resp.msg + (file.url || file.fileUrl));
                    let from = Array.from(array);
                    this.imagePreviewUrls = from;
                    this.$refs.previewImg.showViewer = true;
                });
                // this.showImgViewer = true;
            } else {
                //文件下载
                this.handleDownload(file);
            }
        },

        handleDownload(file) {
            const url = file.url || file.fileUrl; //图片的https链接
            console.log(url);
            downloadByUrl({
                url: url,
            }).then((res) => {
                let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
                const link = document.createElement("a"); //创建一个隐藏的a标签
                link.target = "_blank";
                link.href = href; //设置下载的url
                link.download = file.name; //设置下载的文件名
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(href); // 释放掉blob对象
            });
        },
    }
};
</script>

<style scoped lang="less">
/deep/ .el-input,
.el-select {
    width: 220px !important;
}

.el-form-item {
    margin-bottom: 20px;
}

.dialog-footer {
    text-align: center;
    padding: 20px 0 0;
}
</style>