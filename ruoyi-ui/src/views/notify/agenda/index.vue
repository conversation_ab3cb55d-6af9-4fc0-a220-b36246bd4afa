<template>
  <div class="app-container">
    <div style="width: 100%; height: 50px"></div>
    <div>
      <span v-if="this.suma > 0" style="font-size: 14px"
        >您有{{ suma }}条待办需要处理</span
      >
    </div>
    <div style="width: 100%; height: 8px"></div>
    <el-table v-loading="loading" :data="notifyList">
      <template #empty>
        <div
          style="
            width: 100%;
            height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          "
        >
          <div style="color: #cccccc">暂无待办</div>
        </div>
      </template>

      <el-table-column
        label="事项类型"
        align="left"
        prop="notifyModule"
        width="300"
      />
      <el-table-column
        label="待办事项"
        align="left"
        prop="notifyMsg"
        width="400"
      />
      <!-- <el-table-column label="待办url" align="center" prop="url" /> -->
      <el-table-column label="时间" align="left" prop="createTime" width="300">
      </el-table-column>

      <el-table-column
        label="操作"
        align="left"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.buttonType != 1&&!(['5','7','bl'].includes(scope.row.oaNotifyType))"
            size="mini"
            type="text"
            @click="handleMessage(scope.row)"
            >去处理 ></el-button
          >
          <el-button
            v-if="scope.row.buttonType == 1"
            size="mini"
            type="text"
            @click="updateState(scope.row)"
            >已收到通知</el-button
          >
          <el-button
             v-if="['5','7','bl'].includes(scope.row.oaNotifyType)"
            size="mini"
            type="text"
            @click="reading(scope.row)"
            >阅读</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
     <DetailDialog
      :readUrlParams="readUrlParams"
      v-model="openRead"
      @onSubmit="getList"
    />
  </div>
</template>

<script>
import {
  listNotify,
  getNotify,
  delNotify,
  addNotify,
  updateNotify,
  updateState,
  updateNotifyStatus
} from "@/api/system/notify";
import DetailDialog from "./components/DetailDialog.vue";
import {  meetingNotify } from "@/api/meeting/notice";
import { getReadRelationList,updateNoticeCheckStatus } from "@/api/notice/homePage";

import {  updateBlNotify } from "@/api/badSystem/financialSettlement";
import moment from "moment";

export default {
  // name: "Notify",
   components: {
    DetailDialog
  },
  data() {
    return {
      suma: "",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 通知待办信息表格数据
      notifyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        notifyModule: null,
        notifyType: null,
        notifyMsg: null,
        url: null,
        viewFlag: null,
        status: null,
        createtime: null,
        updatetime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        notifyModule: [
          { required: true, message: "通知模块不能为空", trigger: "blur" },
        ],
        notifyType: [
          {
            required: true,
            message: "通知类型 0通知 1待办不能为空",
            trigger: "change",
          },
        ],
        notifyMsg: [
          { required: true, message: "通知内容不能为空", trigger: "blur" },
        ],
        url: [{ required: true, message: "待办url不能为空", trigger: "blur" }],
        viewFlag: [
          {
            required: true,
            message: "阅读状态：0未阅 1已阅不能为空",
            trigger: "blur",
          },
        ],
        createtime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" },
        ],
        updatetime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" },
        ],
      },
      openRead:false,
      readUrlParams:{}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询通知待办信息列表 */
    getList() {
      this.loading = true;
      listNotify(this.queryParams).then((response) => {
        this.notifyList = response.rows;
        this.total = response.total;
        this.suma = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    reading(row){
      if(['7','bl'].includes(row.oaNotifyType)){
        this.$alert(row.remindText,'通知内容', {
          cancelButtonText: '取消',
          confirmButtonText: '清除通知',
          showCancelButton: true,
          callback: async(action) => {
            console.log(action)
            if(action=="confirm"){
              if(row.oaNotifyType==7){
                await meetingNotify({viewFlag:1,id:row.id,viewTime:moment(new Date()).format("YYYY-MM-DD HH:mm")});
              }else if(row.oaNotifyType=='bl'){
                await updateBlNotify({viewFlag:1,id:row.id,});
              }
              this.getList();
            }
          }
        });
      }else{
        this.readUrlParams={...row};
        this.openRead=true;
      }
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        notifyModule: null,
        notifyType: null,
        notifyMsg: null,
        url: null,
        viewFlag: null,
        status: "0",
        createBy: null,
        createtime: null,
        updateBy: null,
        updatetime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加通知待办信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getNotify(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改通知待办信息";
      });
    },
    async handleMessage(row) {
      if(['月报评审','加班申请','请假申请','取消请假申请','取消加班申请'].includes(row.notifyModule)){
        this.$router.push({path:`/commonNotifyModule/${row.id}`,query:{row:JSON.stringify(row)}}).catch(err => { console.log(err) });
        return;
      }
      if(['会议参加提醒'].includes(row.notifyModule)){
        await meetingNotify({viewFlag:1,id:row.id,viewTime:moment(new Date()).format("YYYY-MM-DD HH:mm")});
        this.$router.push({
          path: row.url,
        });
        return;
      }
      if (row.url === "/zzCommonNotify/notify") {
        this.handleMessageCertificate(row);
        return;
      }
      if (row.id == -2) {
        sessionStorage.setItem("remindData", JSON.stringify(row));
        this.$router.push({
          path: "/oaWork/remind",
        });
        return;
      }
      if (row.oaNotifyType == 9) {
        updateNoticeCheckStatus({id:row.yurrId})
        sessionStorage.setItem("remindData", JSON.stringify(row));
        this.$router.push({
          path: "/oaWork/remind",
        });
        return;
      }
      if(row.oaNotifyType=='6'){
        // updateNotifyStatus({ id: row.id });
        this.getList();
        this.goProcess(row);
        return;
      }
      if (row.oaNotifyType !== null) {
        this.$router.push({
          path: row.url,
          query: {
            oaNotifyStep: row.oaNotifyStep,
          },
        });
        return;
      }
      this.$router.push({
        path: row.url,
        query: { productId: row.projectId, incomeId: row.incomeId },
      });
    },
    goProcess(row){
      this.$router.push({
        path: "/oaWork/approveProcessForm",
        query: {
          oid: row.processId,
          businessId: row.processId,
          formHome:true,
          formHomeId:row.id,
        },
      });
    },
    updateState(row) {
      let data = {
        id: "",
        notifyType: "",
        viewFlag: "",
      };
      data.id = row.id;
      data.notifyType = "0";
      data.viewFlag = "1";
      updateNotify(data).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.open = false;
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateNotify(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNotify(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除通知待办信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delNotify(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/notify/export",
        {
          ...this.queryParams,
        },
        `notify_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
