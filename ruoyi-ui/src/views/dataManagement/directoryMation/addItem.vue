<template>
  <div>
    <el-dialog
      :title="
        seeType
          ? '资料目录详情'
          : editData && editData.id
          ? '修改授信及贷后资料目录'
          : '新增授信及贷后资料目录'
      "
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <div class="flex">
        <div class="item">
          <span><i>*</i>目录名称</span>
          <el-input
            type="text"
            :disabled="seeType"
            v-model="params.catalogueName"
            placeholder="请输入目录名称"
            style="width: 220px"
          ></el-input>
        </div>
        <div class="item">
          <span>上级目录</span>
          <el-select
            :disabled="seeType"
            v-model="params.parentId"
            clearable
            @clear="delml"
            style="width: 220px"
            placeholder="请选择上级目录"
            ref="selectUpResId1"
          >
            <el-option
              hidden
              :value="params.parentId"
              :label="params.parentName"
            >
            </el-option>

            <el-tree
              :data="leftTreeList"
              :props="defaultProps"
              :expand-on-click-node="false"
              :check-on-click-node="true"
              @node-click="handleNodeClick"
            >
            </el-tree>
          </el-select>
        </div>
      </div>
      <div class="flex">
        <div class="item">
          <span>系统目录编号</span>
          <el-input
            type="text"
            v-model="params.catalogueSystemCode"
            disabled
            placeholder="保存后自动生成"
            style="width: 220px"
          ></el-input>
        </div>
        <div class="item">
          <span>目录编号</span>
          <el-input
            :disabled="seeType"
            v-model="params.catalogueCode"
            placeholder="请输入目录编号"
            style="width: 220px"
          ></el-input>
        </div>
      </div>
      <div class="flex">
        <div class="item">
          <span><i>*</i>显示排序</span>
          <el-input-number
            style="width: 220px"
            :disabled="seeType"
            :min="0"
            v-model="params.orderNum"
            type="number"
          ></el-input-number>
        </div>
          <div class="item">
          <span>是否为公共资料库</span>
           <div style="width: 220px;display:inline-block">
            <el-radio-group v-model="params.isPublic" :disabled="seeType">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="flex" v-show="params.isPublic != 1">
        <div class="item">
          <span>所属公司</span>
          <el-input
            type="text"
            disabled
            v-model="params.orgName"
            placeholder="选择所属部门后自动带出"
            style="width: 220px"
          ></el-input>
        </div>
        <div class="item">
          <span><i>*</i>所属部门</span>
          <el-select
            v-model="params.deptId"
            clearable
            :disabled="seeType"
            @clear="delzz"
            style="width: 220px"
            placeholder="请选择上级分类"
            ref="selectUpResId"
          >
            <el-option hidden :value="params.deptId" :label="params.deptName">
            </el-option>

            <el-tree
              :data="deTreeListInit"
              :props="defaultProps2"
              :expand-on-click-node="false"
              :check-on-click-node="true"
              @node-click="dehandleNodeClick"
            >
            </el-tree>
          </el-select>
        </div>
      </div>

      <!-- <div class="flex">
        <div class="item" style="position: relative">
          <span>人员权限</span>
          <el-select
            multiple
            collapse-tags
            :disabled="seeType"
            class="applySelect"
            type="text"
            v-model="params.auUserIds"
            placeholder="点击右侧按钮选择"
            style="width: 220px"
          >
            <el-option
              v-for="item in personList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            ></el-option
          ></el-select>
          <i
            @click.stop="PersonnelAuthorityType = true"
            style="position: absolute; right: 12px; top: 12px; cursor: pointer"
            class="el-icon-search"
          ></i>
        </div>
        <div class="item" style="position: relative">
          <span>岗位权限</span>
          <el-select
            multiple
            collapse-tags
            :disabled="seeType"
            type="text"
            class="applySelect"
            v-model="params.auPostIds"
            placeholder="点击右侧按钮选择"
            style="width: 220px"
          >
            <el-option
              v-for="item in postList"
              :key="item.postId"
              :label="item.postName"
              :value="item.postId"
            ></el-option
          ></el-select>
          <i
            @click.stop="PostAuthorityType = true"
            style="position: absolute; right: 12px; top: 12px; cursor: pointer"
            class="el-icon-search"
          ></i>
        </div>
      </div> -->
      <div class="flex">
        <div class="item" style="display: flex">
          <span style="flex-shrink: 0">备注</span>
          <el-input
            type="textarea"
            :disabled="seeType"
            :rows="2"
            style="width: 590px"
            placeholder="请输入内容"
            v-model="params.remake"
          >
          </el-input>
        </div>
      </div>
      <!-- <el-table
        v-if="seeType && isHomeDirectory"
        border
        :data="detailList"
        style="width: 100%"
      >
        <el-table-column align="center" width="120" prop="name" label="">
        </el-table-column>
        <el-table-column align="center" prop="auth" label="已授权内容">
        </el-table-column>
      </el-table> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="addItem" v-show="!seeType">确 定</el-button>
      </span>
    </el-dialog>
    <DepartmentAuthority
      :auDeptIds="params.auDeptIds"
      @confimrDe="confimrDe"
      v-if="DepartmentAuthorityType"
      @close="DepartmentAuthorityType = false"
    />
    <PostAuthority
      :auPostIds="params.auPostIds"
      @confirmPost="confirmPost"
      v-if="PostAuthorityType"
      @close="PostAuthorityType = false"
    />
    <PersonnelAuthority
      :auUserIds="params.auUserIds"
      @confrimPer="confrimPer"
      v-if="PersonnelAuthorityType"
      @close="PersonnelAuthorityType = fasle"
    />
  </div>
</template>

  <script>
  import {
  treeselect,

} from "@/api/directoryMation/directoryMationSupervise";
import {
  getUserAuthorizationList,
  informationCatalogueList,
  getPostAuthorizationList,
} from "@/api/directoryMation/directoryMation";
import DepartmentAuthority from "./DepartmentAuthority.vue";
import PersonnelAuthority from "./PersonnelAuthority.vue";

import PostAuthority from "./PostAuthority.vue";
import { selectCompanyInfo } from "@/api/directoryArchives/directoryArchives";
export default {
  props: {
    seeType: Boolean,
    leftTreeList: Array,
    deTreeList: Array,
    itemData: Object,
    editData: Object,
    isHomeDirectory: Boolean,
  },
  components: {
    DepartmentAuthority,
    PostAuthority,
    PersonnelAuthority,
  },
  data() {
    return {
      deTreeListInit: [],
      postList: [],
      personList: [],
      departList: [],
      PersonnelAuthorityType: false,
      PostAuthorityType: false,
      DepartmentAuthorityType: false,
      defaultProps: {
        children: "informationCatalogueVOList",
        label: "catalogueName",
      },
      defaultProps2: {
        children: "children",
        label: "label",
      },
      params: {
        catalogueName: "",
        parentId: "",
        orgId: "",
        catalogueSystemCode: "",
        isPublic: 0,
        orgName: "",
        deptId: "",
        deptName: "",
        catalogueCode: "",
        pertainArchivist: "所属档案库",
        orderNum: 0,
        remake: "",
        parentName: "",
        auDeptIds: [],
        auUserIds: [],
        auPostIds: [],
      },
      textarea: "",
      dialogVisible: true,
      detailList: [],
    };
  },
  mounted() {
    if (this.editData?.id) {
      console.log(this.editData);
      this.params = Object.assign(this.params, this.editData);
      this.params.isPublic=Number(this.params.isPublic)
    }
     if (this.editData?.huixianid&&this.editData.type=='add') {
      this.params.parentId = this.editData.huixianid;
      this.params.parentName = this.editData.catalogueName;
    }
    treeselect({AuthModuleEnumCode:"SXINFORMATION"}).then((res) => {
      if (res.code == 200) {
        if (this.params.auDeptIds && this.params.auDeptIds.length > 0) {
          this.departList = this.dg(res.data, this.params.auDeptIds, []);
          console.log(this.departList);
          if (this.seeType) {
            this.detailList.push({
              name: "部门授权",
              auth: JSON.stringify(this.departList.map((item) => item.label)),
            });
          }
        } else {
          this.detailList.push({
            name: "部门授权",
            auth: "",
          });
        }
      }
    });
    // getUserAuthorizationList().then((res) => {
    //   if (res.code == 200) {
    //     if (this.params.auUserIds && this.params.auUserIds.length > 0) {
    //       res.data.forEach((item) => {
    //         this.params.auUserIds.forEach((i) => {
    //           if (item.userId == i) {
    //             this.personList.push(item);
    //           }
    //         });
    //       });
    //       console.log(this.personList);
    //       if (this.seeType) {
    //         this.detailList.push({
    //           name: "人员授权",
    //           auth: JSON.stringify(
    //             this.personList.map((item) => item.nickName)
    //           ),
    //         });
    //       }
    //     } else {
    //       this.detailList.push({
    //         name: "人员授权",
    //         auth: "",
    //       });
    //     }
    //   }
    // });
    // getPostAuthorizationList().then((res) => {
    //   if (res.code == 200) {
    //     if (this.params.auPostIds && this.params.auPostIds.length > 0) {
    //       res.rows.forEach((item) => {
    //         this.params.auPostIds.forEach((i) => {
    //           if (item.postId == i) {
    //             this.postList.push(item);
    //           }
    //         });
    //       });
    //       console.log(this.postList);
    //       if (this.seeType) {
    //         this.detailList.push({
    //           name: "岗位授权",
    //           auth: JSON.stringify(this.postList.map((item) => item.postName)),
    //         });
    //       }
    //     } else {
    //       this.detailList.push({
    //         name: "岗位授权",
    //         auth: "",
    //       });
    //     }
    //   }
    // });
    this.init();
  },
  methods: {
    async init() {
      this.deTreeListInit = JSON.parse(JSON.stringify(this.deTreeList));
      if (this.editData?.orgId) {
        this.deTreeListInit = this.deTreeListInit.filter(
          (item) => item.id == this.editData.orgId
        );
      }
    },
    delml() {
      this.params.parentName = "";
      this.params.parentId = "";
    },
    delzz() {
      this.params.deptId = "";
      this.params.deptName = "";
      this.params.orgName = "";
    },
    dg(list, list2, arr) {
      console.log(list, list2, arr);
      list.forEach((item) => {
        if (item.children && item.children.length > 0) {
          this.dg(item.children, list2, arr);
        }
        list2.forEach((i) => {
          if (item.id == i) {
            arr.push(item);
          }
        });
      });
      return arr;
    },
    confirmPost(e) {
      this.PostAuthorityType = false;
      this.postList = e;
      this.params.auPostIds = [];
      this.postList.forEach((item) => {
        this.params.auPostIds.push(item.postId);
      });
    },
    confrimPer(e) {
      this.PersonnelAuthorityType = false;
      this.personList = e;
      this.params.auUserIds = [];
      this.personList.forEach((item) => {
        this.params.auUserIds.push(item.userId);
      });
    },
    confimrDe(e) {
      console.log(e);
      this.DepartmentAuthorityType = false;
      this.departList = e;
      this.params.auDeptIds = [];
      this.departList.forEach((item) => {
        this.params.auDeptIds.push(item.id);
      });
    },
    addItem() {
      if (!this.params.catalogueName) {
        this.$message.warning("请输入目录名称");
        return;
      }
      if (!this.params.deptId&& this.params.isPublic != 1) {
        this.$message.warning("请选择所属组织");
        return;
      }
      if (!this.params.parentId) {
        this.params.parentId = 0;
      }
      this.$emit("addItem", this.params);
    },
    updateDeTreeListInit(data) {
      this.deTreeListInit = JSON.parse(JSON.stringify(this.deTreeList));
      this.deTreeListInit = this.deTreeListInit.filter(
        (item) => item.id == data.orgId
      );
    },
    handleNodeClick(data) {
      console.log(data);
      this.params.parentName = data.catalogueName;
      this.params.parentId = data.id;
      this.$refs.selectUpResId1.blur();
      this.updateDeTreeListInit(data);
    },
    dehandleNodeClick(data) {
      console.log(data);
      this.params.deptName = data.label;
      this.params.deptId = data.id;
      selectCompanyInfo(this.params.deptId).then((res) => {
        this.params.orgId = res.data.deptId;
        this.params.orgName = res.data.deptName;
      });
      this.$refs.selectUpResId.blur();
    },
    // 选择器配置可以清空选项，用户点击清空按钮时触发
    handleClear() {
      // 将选择器的值置空
      this.upResName = "";
      this.saveForm.upResId = "";
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

  <style lang="less" scoped>
.flex {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  span {
    margin-right: 9px;
    display: inline-block;
    width: 120px;
    text-align: right;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
/deep/ .el-select .el-input__inner {
  height: 36px !important;
}
/deep/ .applySelect .el-icon-arrow-up:before {
  content: "";
}
</style>
