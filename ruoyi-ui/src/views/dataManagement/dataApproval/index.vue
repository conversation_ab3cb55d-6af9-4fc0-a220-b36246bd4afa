<template>
  <div>
    <div class="search">
      <div class="item">
        <span>信息检索</span><el-tooltip class="item" effect="dark" content="包含界面的所有展示数据的内容，不包含资料文件或视频中的文字" placement="top-start"><i class="el-icon-question relative right-1 cursor-pointer"></i> </el-tooltip>


        <el-input
          v-model="queryParams.informationRetrieval"
          clearable=""
          placeholder="请输入关键字"
          style="width: 200px"
          @input="getList"
        ></el-input>
      </div>
      <div class="item">
        <span>资料递交档案名称</span>
        <el-input
          clearable=""
          placeholder="请输入资料递交档案名称"
          style="width: 220px"
          v-model="queryParams.informationUserName"
        ></el-input>
      </div>
      <div class="item">
        <span>资料获取方</span>
        <el-input
          clearable=""
          placeholder="请输入资料获取方"
          style="width: 220px"
          v-model="queryParams.user"
        ></el-input>
      </div>
      <div class="item">
        <span>资料审核时间</span>
        <el-date-picker
          v-model="time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 320px"
        >
        </el-date-picker>
      </div>
      <div class="item">
        <span>所属项目</span>
        <el-input
          clearable=""
          placeholder="请输入所属项目"
          style="width: 220px"
          v-model="queryParams.projectName"
        ></el-input>
      </div>
      <el-button type="primary" @click="search">搜 索</el-button>
      <el-button @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="mb-5 ml-4">
      您当前正在使用<span style="color: rgba(217, 0, 27, 0.803921568627451)"
        >授信及贷后</span
      >资料下载审批
    </div>
    <div class="btn">
      <el-button icon="el-icon-refresh" @click="getList">刷新</el-button>
    </div>
    <div style="padding: 0 16px">
      <el-table
        :data="tableData"
        ref="table"
        style="width: 100%; border: 1px solid #e6e6e6; border-bottom: none"
      >
        <el-table-column width="80" align="center" label="序号">
          <template slot-scope="scope">
            {{ scope.row.xh }}
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="informationUserName"
          label="资料递交档案名称"
          min-width="320"
        >
          <template #default="{ row }">
            <span
              v-html="
                highlightKeyword(
                  row.informationUserName,
                  queryParams.informationRetrieval
                )
              "
            ></span> </template
        ></el-table-column>
        <el-table-column
          align="center"
          prop="user"
          label="资料获取方"
          width="200"
          ><template #default="{ row }">
            <span
              v-html="
                highlightKeyword(row.user, queryParams.informationRetrieval)
              "
            ></span> </template
        ></el-table-column>
        <el-table-column
          align="center"
          prop="auditTime"
          label="资料审核时间"
          width="200"
        >
          <template #default="{ row }">
            <span
              v-html="
                highlightKeyword(
                  row.auditTime,
                  queryParams.informationRetrieval
                )
              "
            ></span> </template
        ></el-table-column>
        <el-table-column
          align="center"
          prop="projectName"
          label="所属项目"
          width="150"
        >
          <template #default="{ row }">
            <span
              v-html="
                highlightKeyword(
                  row.projectName,
                  queryParams.informationRetrieval
                )
              "
            ></span> </template
        ></el-table-column>
        <el-table-column
          align="center"
          prop="sponsor"
          label="发起人"
          width="150"
        />
        <el-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="send(scope.row)"
              >发起资料用印流程</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <SelectItem
      :itemData="itemData"
      :usedCompanyId="usedCompanyId"
      v-if="selectItemType"
      @close="selectItemType = false"
    />
  </div>
</template>

<script>
import { usedList } from "@/api/directoryMation/directoryMation";
import SelectItem from "./SelectItem.vue";
import { highlightKeyword } from "@/utils/index.js";

export default {
  components: {
    SelectItem,
  },
  data() {
    return {
      time: [],
      selectItemType: false,
      tableData: [],
      total: 0,
      processId: null,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        informationRetrieval: "",
        informationUserName: "",
        projectName: undefined,
        user: "",
        startTime: "",
        endTime: "",
      },
      usedCompanyId: 0,
      highlightKeyword,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    send(v) {
      this.usedCompanyId = v.usedCompanyId || 0;
      this.itemData = v;
      sessionStorage.setItem("ThemeTypeYy", JSON.stringify({ theme: v.user }));
      this.selectItemType = true;
    },
    search() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    reset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        informationUserName: "",
        projectName: undefined,
        user: "",
        startTime: "",
        endTime: "",
      };
      this.time = [];
      this.getList();
    },
    getList() {
      if (this.time.length > 0) {
        this.queryParams.startTime = this.$format(this.time[0], "yyyy-MM-dd");
        this.queryParams.endTime = this.$format(this.time[1], "yyyy-MM-dd");
      }
      usedList({ ...this.queryParams }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.tableData.forEach((item, index) => {
            item.xh = (this.queryParams.pageNum - 1) * 10 + index + 1;
          });
          this.total = res.total;
        }
      });
    },
    handleSelectionChange(e) {},
  },
};
</script>

<style lang="less" scoped>
.btn {
  text-align: right;
  padding: 16px;
}
.solid {
  width: 100%;
  height: 10px;
  background: #f8f8f9;
}
.search {
  padding: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    margin-right: 16px;
  }
  span {
    margin-right: 9px;
  }
}
/deep/ .el-select .el-input__inner {
  height: 36px !important;
}
.el-button {
  height: 36px;
  margin-left: 16px;
}
</style>