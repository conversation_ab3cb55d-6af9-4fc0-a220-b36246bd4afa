<template>
  <div style="padding: 16px">
    <div style="color: #777">
      配置智慧平台各功能模块界面中，按公司类型搜索的下拉选择控件关联到哪些公司类型
    </div>
    <el-tabs
      v-model="activeName"
      type="card"
      @tab-click="handleClick"
      class="mt-4"
    >
      <el-tab-pane
        v-for="(item, index) in tabsList"
        :key="index"
        :label="item.info"
        :name="item.code"
      ></el-tab-pane>
    </el-tabs>
    <div>
      <span style="margin-right: 5px; font-weight: bold">下拉选择名称</span>
      <el-input
        style="width: 200px"
        placeholder="请输入"
        v-model="params.selectName"
      ></el-input>
      <span style="margin: 0 5px 0 20px; font-weight: bold">关联公司类型</span>
      <el-input style="width: 200px" placeholder="请输入"></el-input>
      <el-button
        v-hasPermi="['companyTypeSelection:search']"
        type="primary"
        style="margin-left: 20px"
        @click="getList"
        >搜 索</el-button
      >
      <el-button @click="reset">重 置</el-button>
    </div>
    <div style="margin-top: 16px">
      <el-button
        type="primary"
        size="mini"
        @click="add"
        v-hasPermi="['companyTypeSelection:addEdit']"
        >+ 新增下拉选择类型</el-button
      >
    </div>
    <el-table :data="tableData" style="width: 100%; margin-top: 16px">
      <el-table-column prop="selectName" label="下拉选择名称" />
      <el-table-column prop="selectCode" label="编码" />
      <el-table-column prop="date" label="关联公司类型">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.comtypes" :key="index">
            <span class="item_label">
              {{ item.dictLabel }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="内部/外部">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.comtypes" :key="index">
            <span class="item_label2">
              {{ item.auxiliaryField || "-" }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="说明" />
      <el-table-column prop="date" label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="detail(scope.row)"
            v-hasPermi="['companyTypeSelection:detail']"
            >查看详情</el-button
          >
          <el-button
            type="text"
            @click="edit(scope.row)"
            v-hasPermi="['companyTypeSelection:addEdit']"
            >编辑</el-button
          >
          <el-button
            v-hasPermi="['companyTypeSelection:delete']"
            type="text"
            @click="handleDelete(scope.row)"
            style="color:red"
            >删除</el-button
          >
          <el-button type="text" v-hasPermi="['companyTypeSelection:record']" @click="editRecord(scope.row)"
            >编辑记录</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="params.total > 0"
      :total="params.total"
      :page.sync="params.pageNum"
      :limit.sync="params.pageSize"
      @pagination="getList"
    />
    <AddData
      v-if="addType"
      @close="addType = false"
      @submit="submitAdd"
      :itemData="itemData"
    />
    <Record v-if="recordType" @close="recordType = false" :id="itemData.id" />
  </div>
</template>

<script>
import { getModuleType } from "@/api/businessInformation/authTemplate";
import {
  selectTypeAdd,
  selectTypeList,
  selectTypeEdit,
  selectTypeDetail,
  delSystemSelectType
} from "@/api/companyTypeSelection/index";
import AddData from "./components/AddData.vue";
import Record from "./components/Record.vue";
export default {
  components: {
    AddData,
    Record,
  },
  data() {
    return {
      recordType: false,
      tableData: [],
      addType: false,
      params: {
        selectName: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      activeName: "",
      tabsList: [],
      itemData: null,
    };
  },
  mounted() {
    this.getTabs();
  },
  methods: {
    detail(v) {},
    edit(v) {
      this.itemData = v;
      this.addType = true;
    },
    editRecord(v) {
      this.itemData = v;
      this.recordType = true;
    },
    submitAdd(v) {
      let comtypes = [];
      v.comtypes.forEach((item) => {
        comtypes.push({ comTypeId: item });
      });
      if (v.id) {
        selectTypeEdit({ ...v, modelCode: this.activeName, comtypes }).then(
          (res) => {
            this.$message.success("操作成功");
            this.addType = false;
            this.getList();
          }
        );
      } else {
        selectTypeAdd({ ...v, modelCode: this.activeName, comtypes }).then(
          (res) => {
            this.$message.success("操作成功");
            this.addType = false;
            this.getList();
          }
        );
      }
    },
    add() {
      this.itemData = null;
      this.addType = true;
    },
    reset() {
      this.params = {
        selectName: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
      this.getList();
    },
    getList() {
      selectTypeList({ ...this.params, modelCode: this.activeName }).then(
        (res) => {
          this.tableData = res.rows;
          this.params.total = res.total;
        }
      );
    },
    getTabs() {
      getModuleType("select").then((res) => {
        if (res.code == 200) {
          this.tabsList = res.data;
          this.activeName = this.tabsList[0].code;
          this.getList();
        }
      });
    },
    handleClick(e) {
      this.activeName = e.name;
      this.getList();
    },
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除下拉选择名称为"' + row.selectName + '"的数据项？')
        .then(function () {
          return delSystemSelectType(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="less" scoped>
.item_label {
  display: inline-block;
  border: 1px solid #cccccc;
  background: #f2f2f2;
  border-radius: 4px;
  padding: 2px 6px;
  margin-top: 2px;
}
.item_label2 {
  display: inline-block;

  padding: 2px 6px;
  margin-top: 2px;
}
</style>