<template>
  <div class="app-container">
    <div>请项目经理编辑本项目的收付款信息</div>
    <div>点击事项后的 [编辑] 按钮，为每个事项配置收付款人</div>
    <div>
      编辑项目收付款信息需要OA流程审核，编辑完成后点击 [下一步] 发起审核流程
    </div>
    <div style="margin: 12px 0">
      <el-button size="mini" @click="$router.push('/businessInformation/projectDeploy')">取消</el-button>
      <el-button size="mini" type="primary" @click="next" :disabled="nextType"
        >下一步</el-button
      >
    </div>
    <div style="font-weight: bold">常规业务收支款项（含助贷平台和资金方）</div>
    <el-table
      :data="tableData"
      style="width: 100%"
      border=""
      :cell-style="tableRowClassName"
    >
      <el-table-column align="left" width="140" prop="itemName" label="事项">
        <template slot-scope="scope">
          <div style="font-weight: bold">
            {{ namefilter(scope.row.itemName) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" width="200" prop="remark" label="备注" />
      <el-table-column
        align="center"
        width="50"
        prop=""
        label="序号"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            v-if="scope.row.orderByItemName[0].orderBySerialNum[0].accountName"
          >
            <div
              class="commodityDiscountAmount"
              :style="{
                height: item.orderBySerialNum.length * 42 + 'px',
                lineHeight: item.orderBySerialNum.length * 42 + 'px',
              }"
              v-for="(item, index) in scope.row.orderByItemName"
              :key="index"
            >
              {{ index + 1 }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="60"
        prop=""
        label="类型"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{
                v.traderType == 1
                  ? "收"
                  : v.traderType == 0
                  ? "付"
                  : v.traderType == 9
                  ? "收/付"
                  : ""
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        prop=""
        label="账户名称"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountName }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        prop=""
        label="账号"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountNumber }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        prop=""
        label="开户行"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.bankOfDeposit }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="60" prop="" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="edit(scope.row, 1, scope.$index)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div style="font-weight: bold; margin-top: 12px">技术服务方支出款项</div>
    <el-table
      :data="tableData2"
      style="width: 100%"
      border=""
      :cell-style="tableRowClassName"
    >
      <el-table-column align="left" width="140" prop="itemName" label="事项">
        <template slot-scope="scope">
          <div style="font-weight: bold">
            {{ namefilter(scope.row.itemName) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" width="200" prop="remark" label="备注" />
      <el-table-column
        align="center"
        width="50"
        prop=""
        label="序号"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.orderByItemName[0].orderBySerialNum[0].id">
            <div
              class="commodityDiscountAmount"
              :style="{
                height: item.orderBySerialNum.length * 42 + 'px',
                lineHeight: item.orderBySerialNum.length * 42 + 'px',
              }"
              v-for="(item, index) in scope.row.orderByItemName"
              :key="index"
            >
              {{ index + 1 }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="60"
        prop=""
        label="类型"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{
                v.traderType == 1
                  ? "收"
                  : v.traderType == 0
                  ? "付"
                  : v.traderType == 9
                  ? "收/付"
                  : ""
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        prop=""
        label="账户名称"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountName }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        prop=""
        label="账号"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.accountNumber }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        prop=""
        label="开户行"
        min-width="260"
        class-name="commodityDiscountAmount1"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            v-for="(item, index) in scope.row.orderByItemName"
            :key="index"
          >
            <div
              class="commodityDiscountAmount"
              v-for="(v, i) in item.orderBySerialNum"
              :key="i"
            >
              {{ v.bankOfDeposit }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="60" prop="" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="edit(scope.row, 2, scope.$index)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <EditDialog
      :itemData="itemData"
      :type="selectType"
      v-if="editType"
      :name="namefilter(itemData.itemName)"
      @close="editType = false"
      @submit="submitEdit"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="selectCompanyType = false"
      @submit="submitCompany"
    />
  </div>
</template>
  
  <script>
import { getDataByTemplName } from "@/api/oa/deploy";
import { getReceiptAndPaymentInfo, getItemInfo } from "@/api/oa/deploy";
import EditDialog from "./EditDialog.vue";
export default {
  name: "EditCollecPay",
  components: {
    EditDialog,
  },
  data() {
    return {
      nextType: true,
      selectCompanyType: false,
      itemData: null,
      editType: false,
      tableData: [{}],
      tableData2: [],
      nameList: [],
      selectType: null,
      selectIndex: null,
      oldData: null,
      newData: null,
      editList: [],
    };
  },
  mounted() {
    getItemInfo().then((res) => {
      this.nameList = res.data;
    });
    getReceiptAndPaymentInfo(this.$route.query.id).then((res) => {
      if (res.data) {
        this.oldData = JSON.parse(JSON.stringify(res.data));
        this.newData = res.data;
        this.tableData =
          res.data.oaProjectDeployReceiptAndPaymentInfoVoList[0].oaProjectDeployReceiptAndPaymentInfo.orderByrRceiptAndPaymentType;
        this.tableData2 =
          res.data.oaProjectDeployReceiptAndPaymentInfoVoList[1].oaProjectDeployReceiptAndPaymentInfo.orderByrRceiptAndPaymentType;
      }
    });
  },
  methods: {
    submitCompany(v) {
      getDataByTemplName({
        companyId: v,
        templateName: "业务信息配置-修改项目收付款信息申请",
        isEnableCompanyId: 1,
      }).then((res) => {
        this.selectCompanyType = false;
        let obj = {
          oldData: this.oldData,
          newData: this.newData,
          editList: this.editList,
          projectName: this.$route.query.name,
        };
        sessionStorage.setItem("editCollecPayData", JSON.stringify(obj));
        this.$router.push({
          path: "/oaWork/updateProcessForm",
          query: {
            templateId: res.templateId,
            classificationId: res.classificationId,
            companyId: res.companyId,
            editCollecPay: true,
          },
        });
      });
    },
    next() {
      this.selectCompanyType = true;
    },
    tableRowClassName({ row, rowIndex }) {
      // 根据 row.special 字段的值返回类名
      return row &&
        row.orderByItemName &&
        !row.orderByItemName[0].orderBySerialNum[0].accountName
        ? "background:#f8f8f9"
        : "background:#fff";
    },
    namefilter(v) {
      let data = this.nameList.find((item) => {
        return item.code == v;
      });
      if (data) {
        return data.info;
      }
    },
    edit(v, type, index) {
      console.log(v, type, index);
      this.selectType = type;
      this.selectIndex = index;
      this.itemData = v;
      this.editType = true;
    },
    submitEdit(v, list, oldRemark, newRemark) {
      var flag = false;
      this.editList.forEach((item, index) => {
        if (item.code == this.itemData.itemName && list.length > 0) {
          flag = true;
          this.editList[index] = {
            code: this.itemData.itemName,
            name: `${
              this.selectType == 1
                ? "常规业务收支款项（含助贷平台和资金方）"
                : "技术服务方支出款项"
            } -${this.namefilter(this.itemData.itemName)}`,
            list,
            oldRemark,
            newRemark,
          };
        }
      });
      if (flag) {
      } else {
        if (list.length > 0 || oldRemark != newRemark) {
          this.editList.push({
            code: this.itemData.itemName,
            name: `${
              this.selectType == 1
                ? "常规业务收支款项（含助贷平台和资金方）"
                : "技术服务方支出款项"
            } -${this.namefilter(this.itemData.itemName)}`,
            list,
            oldRemark,
            newRemark,
          });
        }
      }

      console.log(this.editList);

      let arr1 = [...this.tableData];
      let arr2 = [...this.tableData2];
      if (this.selectType == 1) {
        arr1[this.selectIndex] = v;
      } else {
        arr2[this.selectIndex] = v;
      }
      this.tableData = JSON.parse(JSON.stringify(arr1));
      this.tableData2 = JSON.parse(JSON.stringify(arr2));

      console.log(this.tableData);
      this.newData.oaProjectDeployReceiptAndPaymentInfoVoList[0].oaProjectDeployReceiptAndPaymentInfo.orderByrRceiptAndPaymentType =
        this.tableData;
      this.newData.oaProjectDeployReceiptAndPaymentInfoVoList[1].oaProjectDeployReceiptAndPaymentInfo.orderByrRceiptAndPaymentType =
        this.tableData2;
      this.editType = false;
      this.nextType = false;
    },
  },
};
</script>
  
  <style lang="less" scoped>
/deep/ .el-table__cell {
  padding: 0 !important;
}

/deep/ .commodityDiscountAmount1 .is-center .is-leaf .el-table__cell {
  line-height: 42px !important;
  height: 42px;
}

/deep/ .commodityDiscountAmount1 .cell {
  padding: 0 !important;
  padding-left: 10px !important;
  .commodityDiscountAmount {
    border-bottom: 1px solid #ebeef5;
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  div:last-child {
    border: none !important;
  }
}
.commodityDiscountAmount2 {
  border-bottom: 1px solid #ebeef5;
  line-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>