<template>
  <div>
    <p class="mb-0">
      本页面展示您在智慧财务系统拥有哪些公司（账套）的权限，它们来自于您的上级用户对您的授权
    </p>
    <p class="mb-0">您可以将自己已拥有的权限授权给自己的下级用户</p>

    <div class="mt-3">
      <el-input
        class="mr-3"
        v-model="queryParams.queryName"
        style="width: 200px"
        placeholder="请输入公司名称"
      ></el-input>
      <el-button icon="el-icon-search" type="primary" @click="getList()"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
    </div>
    <div class="mt-3">
      <!-- <span class="mr-6">
        <el-button
          size="mini"
          v-hasPermi="['currencyCompany:allAuth']"
          type="primary"
          @click="changeAuthType"
          >批量授权</el-button
        >
      </span> -->
      <el-switch class="mr-3" v-model="changeUserType" @change="getList">
      </el-switch
      >仅看直属下级
      <span v-if="roleData && roleData.userHasRole.includes('9')">
        <el-switch class="mr-3 ml-8" v-model="changeType" @change="getList">
        </el-switch
        >未对他人分配权限的公司
      </span>
    </div>
    <MyTable
      class="mt-3"
      :columns="dataColumns"
      :queryParams="queryParams"
      :source="dataList"
      :showIndex="true"
    >
      <template #generalAccountant="{ record }">
        <div
          v-if="
            record.authorizedFeatureDetailList[0].featureUserList.length > 0
          "
        >
          <div
            v-for="(item, i) in record.authorizedFeatureDetailList[0]
              .featureUserList"
            :key="i"
          >
            <span
              class="user"
              @click="getUserData(item.featureUserId, item)"
              :style="{
                background:
                  item.authorizedUserIsCurrentUserFlag != 1 ||
                  item.authorizedUserHaveAllPermissionFlag == 1
                    ? '#f2f2f2'
                    : '',
              }"
            >
              {{ item.featureUserId | user
              }}<span style="color: #cccccc">{{
                item.featureUserId | userStatus
              }}</span
              ><i
                @click.stop="
                  delUser(
                    record,
                    item,
                    record.authorizedFeatureDetailList[0].featureUserFlag
                  )
                "
                class="el-icon-close"
                v-if="
                  item.authorizedUserIsCurrentUserFlag == 1 &&
                  item.authorizedUserHaveAllPermissionFlag != 1
                "
              ></i>
            </span>
          </div>
        </div>
        <div v-else style="color: #ff0000">未分配普通会计</div>
      </template>
      <template #chargeAccountant="{ record }">
        <div
          v-if="
            record.authorizedFeatureDetailList[7].featureUserList.length > 0
          "
        >
          <div
            v-for="(item, i) in record.authorizedFeatureDetailList[7]
              .featureUserList"
            :key="i"
          >
            <span
              class="user"
              @click="getUserData(item.featureUserId, item)"
              :style="{
                background:
                  item.authorizedUserIsCurrentUserFlag != 1 ||
                  item.authorizedUserHaveAllPermissionFlag == 1
                    ? '#f2f2f2'
                    : '',
              }"
            >
              {{ item.featureUserId | user
              }}<span style="color: #cccccc">{{
                item.featureUserId | userStatus
              }}</span
              ><i
                @click.stop="
                  delUser(
                    record,
                    item,
                    record.authorizedFeatureDetailList[7].featureUserFlag
                  )
                "
                class="el-icon-close"
                v-if="
                  item.authorizedUserIsCurrentUserFlag == 1 &&
                  item.authorizedUserHaveAllPermissionFlag != 1
                "
              ></i>
            </span>
          </div>
        </div>
        <div v-else style="color: #ff0000">未分配主管会计</div>
      </template>
      <template #financialManager="{ record }">
        <div
          v-if="
            record.authorizedFeatureDetailList[9].featureUserList.length > 0
          "
        >
          <div
            v-for="(item, i) in record.authorizedFeatureDetailList[9]
              .featureUserList"
            :key="i"
          >
            <span
              class="user"
              @click="getUserData(item.featureUserId, item)"
              :style="{
                background:
                  item.authorizedUserIsCurrentUserFlag != 1 ||
                  item.authorizedUserHaveAllPermissionFlag == 1
                    ? '#f2f2f2'
                    : '',
              }"
            >
              {{ item.featureUserId | user
              }}<span style="color: #cccccc">{{
                item.featureUserId | userStatus
              }}</span
              ><i
                @click.stop="
                  delUser(
                    record,
                    item,
                    record.authorizedFeatureDetailList[9].featureUserFlag
                  )
                "
                class="el-icon-close"
                v-if="
                  item.authorizedUserIsCurrentUserFlag == 1 &&
                  item.authorizedUserHaveAllPermissionFlag != 1
                "
              ></i>
            </span>
          </div>
        </div>
        <div v-else style="color: #ff0000">未分配财务管理员</div>
      </template>
      <template #viewPermisson="{ record }">
        <div
          v-if="
            record.authorizedFeatureDetailList[8].featureUserList.length > 0
          "
        >
          <div
            v-for="(item, i) in record.authorizedFeatureDetailList[8]
              .featureUserList"
            :key="i"
          >
            <span
              class="user"
              @click="getUserData(item.featureUserId, item)"
              :style="{
                background:
                  item.authorizedUserIsCurrentUserFlag != 1 ||
                  item.authorizedUserHaveAllPermissionFlag == 1
                    ? '#f2f2f2'
                    : '',
              }"
            >
              {{ item.featureUserId | user
              }}<span style="color: #cccccc">{{
                item.featureUserId | userStatus
              }}</span
              ><i
                @click.stop="
                  delUser(
                    record,
                    item,
                    record.authorizedFeatureDetailList[8].featureUserFlag
                  )
                "
                class="el-icon-close"
                v-if="
                  item.authorizedUserIsCurrentUserFlag == 1 &&
                  item.authorizedUserHaveAllPermissionFlag != 1
                "
              ></i>
            </span>
          </div>
        </div>
        <div v-else style="color: #ff0000">未分配查看权限</div>
      </template>
      <template #opertion="{ record }">
        <el-dropdown v-hasPermi="['currencyCompany:addPerson']" trigger="click">
          <span class="el-dropdown-link"> +添加用户 </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-button
                v-if="
                  getPerminsson(1, record.id) || getPerminsson(9, record.id)
                "
                type="text"
                @click="addUser(record, 1)"
                >添加普通会计</el-button
              >
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button
                v-if="
                  getPerminsson(8, record.id) || getPerminsson(9, record.id)
                "
                type="text"
                @click="addUser(record, 8)"
                >添加主管会计</el-button
              >
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button
                v-if="getPerminsson(9, record.id)"
                type="text"
                @click="addUser(record, 9)"
                >添加财务管理员</el-button
              >
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button
                v-if="
                  getPerminsson(88, record.id) || getPerminsson(9, record.id)
                "
                type="text"
                @click="addUser(record, 88)"
                >添加查看权限</el-button
              >
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </MyTable>

    <pagination
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <AddingUsers
      v-if="addingUsersType"
      :subordinateList="subordinateList"
      @close="addingUsersType = false"
      @submit="submitAddUser"
    />
    <AuthMethod
      v-if="authMethodType"
      @close="authMethodType = false"
      @submitMeth="submitMeth"
    />
    <SelectAuthTime
      v-if="selectAuthTimeTpye"
      @close="selectAuthTimeTpye = false"
    />
    <UserDetail2
      :userId="userId"
      :authType="authTypeName"
      v-if="userDetailType"
      @close="userDetailType = false"
    />
  </div>
</template>
<script>
import {
  newAuthority,
  getUserListAll,
  subordinate,
  newAuthTemp,
  queryCancelUser,
  userRoleAuthority,
  cancelAuthorization,
  queryAllCancelUser,
  cancelAllAuthorization,
  addAuthorization,
} from "@/api/businessInformation/currencyCompany";
let that = "";
export default {
  data() {
    return {
      authTypeName: "",
      userId: "",
      userDetailType: false,
      roleData: null,
      changeUserType: false,
      subordinateList: [],
      userList: [],
      selectAuthTimeTpye: false,
      allSelectType: false,
      showCheckbox: false,
      //批量授权状态
      authType: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        queryType: 2,
        queryName: "",
        queryCode: "FINANCESYS",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      },
      dataList: [],
      dataColumns: [
        {
          label: "账套所属公司",
          prop: "responseName",
          width: "200",
        },
        {
          label: "普通会计",
          prop: "generalAccountant",
          key: "generalAccountant",
          width: "180",
        },
        {
          label: "主管会计",
          prop: "chargeAccountant",
          key: "chargeAccountant",
          width: "180",
        },
        {
          label: "财务管理员",
          prop: "financialManager",
          key: "financialManager",
          width: "180",
        },
        {
          label: "查看权限",
          prop: "viewPermisson",
          key: "viewPermisson",
          width: "180",
        },

        {
          label: "操作",
          key: "opertion",
          prop: "opertion",
          fixed: "right",
        },
      ],
      companyType: "1",
      changeType: false,
      authMethodType: false,
      addingUsersType: false,

      openSelect: false,
      selectList: [],
      selectItemData: null,
      addType: null,
    };
  },
  created() {
    that = this;

    this.init();
  },
  watch: {
    "$store.state.principalId": {
      handler(newval, oldval) {
        console.log(newval);
        this.init();
      },
      deep: true,
    },
  },
  filters: {
    user(e) {
      if (that.userList && that.userList.length > 0) {
        let data = that.userList.find((item) => {
          return item.userId == e;
        });
        return data.nickName || "";
      }
    },
    userName(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      return data.userName;
    },
    userStatus(e) {
      if (that.userList && that.userList.length > 0) {
        let data = that.userList.find((item) => {
          return item.userId == e;
        });
        if (data.status == 0) {
          return "";
        } else {
          return "（已停用）";
        }
      }
    },
  },
  methods: {
    getUserData(e, v) {
      if (v.authorizedUserHaveAllPermissionFlag == 1) {
        this.authTypeName = "所有";
      }
      if (
        !v.authorizedUserHaveAllPermissionFlag &&
        !v.authorizedUserHaveCompanyPermissionFlag
      ) {
        this.authTypeName = "项目";
      }
      if (v.authorizedUserHaveCompanyPermissionFlag == 1) {
        this.authTypeName = "公司";
      }
      console.log(e);
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      console.log(data);
      this.userId = data.userName;
      this.userDetailType = true;
    },
    getPerminsson(flag, companyId) {
      let data = this.roleData.userAuthorizedDetail.find((item) => {
        return item.userFlag == flag;
      });
      if (data && data.companyIdList.includes(companyId)) {
        return true;
      } else {
        return false;
      }
    },
    getNickName(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data) {
        return data.nickName;
      }
    },
    delUser(v, i, x) {
      console.log(v, i, x);
      let params = {
        id: v.id,
        authorizedType: 2,
        authorizedCode: "FINANCESYS",
        unAuthorizedUserId: i.featureUserId,
        authRoleCode: x,
      };
      queryCancelUser({
        ...params,
        principalId: this.$store.state.principalId,
      }).then((res) => {
        if (res.msg == 0) {
          this.$confirm(
            `是否确认取消对用户[${this.getNickName(
              i.featureUserId
            )}]的授权？点击确定后，取消授权将立即生效！`,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        } else if (res.msg > 0) {
          this.$confirm(
            `是否确认取消对用户[${this.getNickName(
              i.featureUserId
            )}]的授权？ [${this.getNickName(
              i.featureUserId
            )}]已经将本权限授权于其下级用户，取消后其下级用户也将失去此权限,点击确定后，取消授权将立即生效！ `,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        }
      });
    },
    cancelAuthorization(e) {
      cancelAuthorization({
        ...e,
        principalId: this.$store.state.principalId,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getList();
        }
      });
    },
    submitMeth(e) {
      this.selectAuthTimeTpye = true;
    },
    submitAddUser(e) {
      let data = {
        id: this.selectItemData.id,
        businessType: 2,
        businessCode: "FINANCESYS",
        authRoleCode: this.addType,
        authorizedUserIdList: e.map((item) => item.userId),
        ancestors: this.selectItemData.ancestors
          ? this.selectItemData.ancestors
          : "",
      };
      addAuthorization({
        ...data,
        principalId: this.$store.state.principalId,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success("添加成功");
          this.getList();
          this.addingUsersType = false;
        }
      });
    },
    allSelect() {},
    tableSelect(e) {
      console.log(e);
      this.selectList = [...e];
    },
    changeAuthType() {
      this.$emit("authType");
    },
    addUser(e, v) {
      this.selectItemData = e;
      this.addType = v;
      subordinate({ principalId: this.$store.state.principalId }).then(
        (res) => {
          if (res.code == 200) {
            if (
              res.data.subordinateList &&
              res.data.subordinateList.length > 0
            ) {
              this.subordinateList = res.data;
              this.addingUsersType = true;
            } else {
              this.$message.warning("您没有下级用户，无法添加用户给他人授权！");
            }
          }
        }
      );
    },
    init() {
      this.getUser();
      this.getList();
     
    },
    userRoleAuthority() {
      userRoleAuthority({
        businessType: 2,
        businessCode: "FINANCESYS",
        principalId: this.$store.state.principalId,
        inputIdList: this.dataList.length>0?this.dataList.map((item) => item.id).join(","):'',
      }).then((res) => {
        this.roleData = res.data;
      });
    },
    getList() {
      this.queryParams.subordinateFlag = this.changeUserType ? 1 : 0;
      this.queryParams.unassignedCompaniesFlag = this.changeType ? 1 : 0;
      newAuthority({
        ...this.queryParams,
        queryUserId: this.$store.state.principalId,
      }).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows;
          this.queryParams.total = res.total;
          this.userRoleAuthority();
        }
      });
    },
    getUser() {
      getUserListAll().then((res) => {
        if (res.code == 200) {
          this.userList = res.data;
        }
      });
    },
    submitDelet(e) {
      console.log(e);
    },
    reset() {
      this.changeType = false;
      this.changeUserType = false;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        queryType: 2,
        queryName: "",
        queryCode: "FINANCESYS",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      };
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
.user {
  display: inline-block;
  border: 1px solid #cccccc;
  padding: 0 10px;
  cursor: pointer;
  color: #409eff;
  border-radius: 4px;
  margin-right: 10px;
  margin-top: 3px;

  i {
    color: #666666;
    margin-left: 5px;
    cursor: pointer;
  }
}
.demonstration {
  display: block;
}
</style>
