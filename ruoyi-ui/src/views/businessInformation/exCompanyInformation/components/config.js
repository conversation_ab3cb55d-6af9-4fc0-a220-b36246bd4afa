export default {
  isInternalList:Object.freeze([
    {label: "全部",value:undefined},
    {label: "是",value:"1"},
    {label: "否",value:"0"},
  ]),
  isInternalObj:Object.freeze({
    "1":'是',
    "0":'否',
  }),
  isInternalListEdit:Object.freeze([
    {label: "是",value:"1"},
    {label: "否",value:"0"},
  ]),
  isInternalListEditObj:Object.freeze({
    1:"是",
    0:"否",
  }),
  columns:Object.freeze([
    { label: "公司名称", key: "companyName", minWidth: "220" },
    { label: "公司简称", prop: "companyShortName", minWidth: "120",isHSlot:true },
    { label: "公司类型", key: "companyTypeMappingList", minWidth: "200" },
    { label: "支持业务类型", key: "companyBusinessTypeMappingList", minWidth: "200" },
    { label: "合作项目", prop: "projectCount", minWidth: "120" },
    // { label: "是否内部公司", key:'isInside', minWidth: "150",isHSlot:true, },
    { label: "审核状态", key: "checkStatus", minWidth: "150" },
    { label: "启用状态", key: "status", minWidth: "150" },
    { label: "操作", key: "operate", fixed: "right", align: "left", minWidth: "200" },
  ]),
  columnsBasicInformation:Object.freeze([
    { label: "项目", prop: "projectName", },
    { label: "操作", key: "operate", fixed: "right", align: "left", Width: "100" },
  ]),
  columnsSummaryPermissions:Object.freeze([
    { label: "更新时间", prop: "operTime",minWidth:'120' },
    { label: "操作人", prop: "nickName", minWidth:'100' },
    { label: "操作类型", prop: "businessTypeString",minWidth:'200'  },
    { label: "操作", key: "operate",minWidth:'120'  },
  ]),
  unitTypeListColor: Object.freeze({
    '资金方': "#A9D1EF",
    '资产方': "rgb(240, 198, 100)",
    '担保公司': "rgb(143, 206, 140)",
    '其他': "#CCCCCC",
  }),
  unitTypeListBackColor: Object.freeze({
    '资金方': "#EDF5FC",
    '资产方': "rgb(253, 247, 232)",
    '担保公司': "rgb(240, 249, 240)",
    '其他': "#F2F2F2",
  }),
  tabsList: Object.freeze([
    { label: "基础信息", name: "BasicInformation" },
    { label: "更新记录", name: "SummaryPermissions" },
  ]),
  rules:{},
  rulesNoCode: Object.freeze({
    companyName: [
      { required: true, message: "请输入公司名称", trigger: "blur" },
    ],
    companyShortName: [
      { required: true, message: "请输入公司简称", trigger: "blur" },
    ],
    companyTypeMappingList: [
      { required: true, message: "请添加公司类型", trigger: "blur" },
    ],
    companyBusinessTypeMappingList: [
      { required: true, message: "请添加支持业务类型", trigger: "blur" },
    ],
    isInside: [
      { required: true, message: "请选择是否内部公司", trigger: "blur" },
    ],
  }),
  rulesCode: Object.freeze({
    companyName: [
      { required: true, message: "请输入公司名称", trigger: "blur" },
    ],
    companyCode: [
      { required: true, message: "请输入公司编码", trigger: "blur" },
    ],
    companyShortName: [
      { required: true, message: "请输入公司简称", trigger: "blur" },
    ],
    companyTypeMappingList: [
      { required: true, message: "请添加公司类型", trigger: "blur" },
    ],
    companyBusinessTypeMappingList: [
      { required: true, message: "请添加支持业务类型", trigger: "blur" },
    ],
    isInside: [
      { required: true, message: "请选择是否内部公司", trigger: "blur" },
    ],
  }),
  statusList:Object.freeze([
    { label: "正常", value: "0",raw:{listClass:'primary'} },
    { label: "停用", value: "1",raw:{listClass:'danger'}},
   
  ]),
  statusObj:Object.freeze({
    0:'正常',
    1:'停用',
  })
};
