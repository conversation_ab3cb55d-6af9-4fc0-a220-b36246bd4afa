<template>
  <div class="app-container">
    <div style="
        position: fixed;
        bottom: 0;
        left: 260px;
        width: 86%;
        text-align: center;
        height: 80px;
        background: #fff;
        z-index: 999;
        padding-top: 20px;
        border-top: 1px solid #f2f2f2;
      ">
      <el-button size="mini" @click="handleExit">关闭</el-button>
      <el-button v-if="currentStatus !== '4' && !officeSuppliesData && !officePurchaseData" size="mini" @click="handleSave">保存</el-button>
      <el-button size="mini" type="primary" @click="handleAdd" style="width: 100px">提交</el-button>
    </div>
    <div style="width: 100%; margin-left: 16px">
      <span style="font-size: 26px; font-weight: bold">
        {{ this.templateName }}
      </span>
      <br />
      <div v-if="templateType == 1">
        <span style="font-size: 13px; color: #cccccc">本流程将在结束后生成会计记账凭证，计入财务报表</span>
        <br />
        <span style="font-size: 13px; margin-top: 15px">
          <input type="checkbox" name="customFlag" v-model="customFlag" @change="confirmChange(customFlag)" />自定义凭证生成时间
        </span>
        <span style="margin-left: 10px; margin-top: 15px" v-if="customFlag === true">
          <el-date-picker v-model="voucherDate" size="small" style="width: 200px" value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"></el-date-picker>
        </span>
      </div>
    </div>

    <div style="
        width: 110%;
        height: 12px;
        background-color: #f8f8f9;
        margin-bottom: 24px;
        margin-top: 16px;
        margin-left: -2%;
      "></div>

    <div>
      <div style="margin-left: 13px; margin-bottom: 16px">
        <span style="font-weight: bold; margin-left: 35px">
          <span style="color: red">*</span>主题：
        </span>
        <el-tooltip class="item" effect="dark" :content="theme" placement="top-start" :open-delay="500"
          :enterable="false" :disabled="!theme">
          <el-input :disabled="Boolean(
            this.$route.query.themeTypeXz ||
            this.$route.query.themeTypeYy ||
            this.$route.query.themeTypeZzYy
          )
            " v-model="theme" maxlength="65" show-word-limit size="small" :placeholder="this.$route.query.themeTypeXz
              ? '根据资料获取方自动生成'
              : '请输入'
              " style="width: 650px"></el-input>
        </el-tooltip>
        <!--        <span style="font-weight: bold; margin-left: 35px">流程编号</span>-->
        <!--        {{processNumber}}-->
      </div>
      <div v-if="this.$route.query.themeTypeXz || this.$route.query.themeTypeZzYy"
        style="margin-left: 13px; margin-bottom: 16px">
        <span style="font-weight: bold; margin-left: -7px">
          <span style="color: red">*</span>资料获取方：
        </span>
        <el-autocomplete class="inline-input" v-model="zlhqf" :fetch-suggestions="queryZlhqf" size="small"
          placeholder="请输入资金方全称" style="width: 650px"></el-autocomplete>
        <!--        <span style="font-weight: bold; margin-left: 35px">流程编号</span>-->
        <!--        {{processNumber}}-->
      </div>
      <div style="margin-left: 11px; margin-bottom: 16px">
        <el-row>
          <el-col :span="4">
            <span style="font-weight: bold; margin-left: 35px">申请人：</span>
            <span v-if="businessId == null || businessId == ''">
              {{ user.nickName }}
            </span>
            <span v-if="businessId != null && businessId != ''">
              {{ createName }}
            </span>
            <span style="color: #cccccc">{{ flowStartStatus }}</span>
          </el-col>
          <el-col :span="8">
            <span style="font-weight: bold">公司：</span>
            <!--todo 公司待完善-->
            <!--            <span v-if="businessId == null || businessId == ''">{{user.nickName}}</span>-->
            <span v-if="businessId != null && businessId != ''">
              {{ companyName }}
            </span>
            <span v-if="businessId == null || businessId == ''">
              <span v-for="item in allCompanyList">
                <span v-if="item.id == companyId">{{ item.companyName }}</span>
              </span>
            </span>
          </el-col>
        </el-row>
      </div>

      <k-form-build ref="generateForm" :value="jsonData" :dynamicData="dynamicData" @change="changeKbuild" />
      <salaryData style="width: 750px" v-if="salaryData2" @seeImg="seeImg" :detail="salaryData2" />
      <el-button type="primary" style="float: right; margin: 10px 0" @click="batchDownloadSupervise"
        v-if="dataList.length > 0">
        批量下载附件</el-button>
      <el-table v-if="dataList.length > 0" :data="dataList" style="width: 100%"
        @selection-change="handleSelectionChangeDataList">
        <el-table-column type="selection" width="50" />
        <el-table-column prop="index" label="序号" width="50" />
        <el-table-column prop="informationCode" label="资料编号" width="180" />
        <el-table-column prop="informationName" label="资料名称" min-width="350">
          <template slot-scope="scope">
            <el-button v-if="scope.row.isTemporary != 0" @click="see(scope.row)" type="text">{{
              scope.row.informationName }}</el-button>
            <span v-else>{{ scope.row.informationName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="informationYear" label="资料年度" width="120" />
        <el-table-column prop="date" label="资料类型" width="120">
          <template slot-scope="scope">{{
            scope.row.isTemporary == 0 ? "临时文件" : "资料库"
          }}</template>
        </el-table-column>
        <el-table-column prop="informationSystemCode" label="系统资料编号" width="180" />
        <el-table-column prop="date" label="资料下载流程发起人" width="180">
          <template>{{ createName }}</template>
        </el-table-column>
        <el-table-column prop="createBy" label="资料创建人" width="180" />
        <el-table-column prop="uploadInfo" label="资料下载" v-if="!this.$route.query.themeTypeXz" fixed="right"
          show-overflow-tooltip :resizable="false" min-width="320">
          <template slot-scope="scope">
            {{ scope.row.fileName }}
            <span v-if="
              scope.row.fileName.endsWith('.pdf') ||
              scope.row.fileName.endsWith('.jpg') ||
              scope.row.fileName.endsWith('.png') ||
              scope.row.fileName.endsWith('.gif') ||
              scope.row.fileName.endsWith('.jpeg')
            ">
              <el-button type="text" @click="handlePreview(scope.row)">查看</el-button>
              <el-button type="text" @click="handleDownload(scope.row)">下载</el-button>
              <br />
            </span>
            <span v-else>
              <el-button type="text" @click="handleDownload(scope.row)">下载</el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 办公用品 -->
      <Supplies v-if="officeSuppliesData" :data="officeSuppliesData" @getText="getText" />
      <Purchase v-if="officePurchaseData" :data="officePurchaseData" @getText="getText" />

      <!-- 收付款人 -->
      <AddPayment v-if="formAddPayment || formDelPayment" :data="formAddPayment || formDelPayment" @getText="getText" />
      <EditPayment v-if="formEditPayment" :data="formEditPayment" @getText="getText" />
      <!-- 编辑项目收付款信息 -->
      <EditCollecPay v-if="formEditCollecPay" :data="formEditCollecPay.editList" :newData="formEditCollecPay.newData"
        :name="formEditCollecPay.projectName" @getText="getText" />
      <EditInfo v-if="formEditInformation" :data="formEditInformation" :name="formEditInformation.projectName"
        @getText="getText" />
      <!-- 监管资料 -->
      <Supervise v-if="dataListSupervise.length > 0" :tableData="dataListSupervise"
        :processType="processTypeSupervise" />
      <!-- 立项管理 -->
      <AddLxProject @getText="getText" v-if="formLxProAdd" :data="formLxProAdd" />
      <ClaimLxProject @getText="getText" v-if="formLxProClaim" :data="formLxProClaim" />
      <DelayLxProject @getText="getText" v-if="formLxProDelay" :data="formLxProDelay" />
      <TermLxProject @getText="getText" v-if="formLxProTerm" :data="formLxProTerm" />
      <EditLxProject @getText="getText" v-if="formLxProEdit" :data="formLxProEdit" />

      <!-- 公司信息 -->
      <CompanyAddForm @getText="getText" :data="formCompanyAdd || formCompanyDel"
        v-if="formCompanyAdd || formCompanyDel" />
      <CompanyEditForm @getText="getText" :data="formCompanyEdit" v-if="formCompanyEdit" />
      <!-- 产品信息 -->
      <ProductAddForm @getText="getText" :data="formProductAdd || formProductDel"
        v-if="formProductAdd || formProductDel" />
      <ProductEditForm @getText="getText" :data="formProductEdit" v-if="formProductEdit" />
      <!-- 项目名称 -->
      <ProEditForm @getText="getText" :data="formProEdit || formLxProjectNameEdit"
        v-if="formProEdit || formLxProjectNameEdit" />
      <ProDelForm @getText="getText" :data="formProDel" v-if="formProDel" />
      <!-- 人事模块 -->
      <PerEntryForm v-if="Object.keys(formPerEntry).length" :form="formPerEntry"></PerEntryForm>
      <PerBecomeForm v-if="Object.keys(formPerBecome).length" :form="formPerBecome" :disabled="false"></PerBecomeForm>
      <PerTranForm v-if="Object.keys(formPerTransfer).length" :form="formPerTransfer"></PerTranForm>
      <PerResign v-if="Object.keys(formPerResign).length" :form="formPerResign"></PerResign>
      <!-- 证照模块 -->
      <CerAllLicenses v-if="tableCerAllLienses.length" :tableData="tableCerAllLienses" :update="true"
        :tableConflict="tableConflict" @delAllLicenses="delAllLicenses"></CerAllLicenses>
      <!-- 请假申请 -->
      <Leave v-if="Object.keys(tableCheckWorkLeave).length" :tableData="tableCheckWorkLeave"></Leave>
      <!-- 加班申请 -->
      <OverTime v-if="Object.keys(tableCheckWorkOverTime).length" :tableData="tableCheckWorkOverTime"></OverTime>
      <!-- 奖惩申请 -->
      <BonusPenalty v-if="
        Object.keys(tableCheckWorkBonusPenalty).length ||
        (tableCheckWorkBonusPenaltyList &&
          tableCheckWorkBonusPenaltyList.length)
      " :tableData="tableCheckWorkBonusPenalty" :tableDataList="tableCheckWorkBonusPenaltyList"></BonusPenalty>
      <!-- 出差申请 -->
      <GoErrand v-if="Object.keys(tableCheckWorkGoErrand).length" :tableData="tableCheckWorkGoErrand"></GoErrand>
      <!-- 年度计划 -->
      <AnnualPlanReview v-if="tableAnnualPlanReview" :tableData="tableAnnualPlanReview"></AnnualPlanReview>
      <!-- 考核配置 -->
      <AssessmentConfiguration v-if="tableAssessmentConfiguration" :tableData="tableAssessmentConfiguration">
      </AssessmentConfiguration>
      <!-- 考核配置通过 -->
      <AssessmentConfigurationFinish v-if="tableAssessmentConfigurationFinish"
        :tableData="tableAssessmentConfigurationFinish"></AssessmentConfigurationFinish>
      <!-- 项目业绩录入 -->
      <ProjectPerformance v-if="tableProjectPerformance" :tableData="tableProjectPerformance"></ProjectPerformance>
      <!-- 项目业绩录入通过 -->
      <ProjectPerformanceFinish v-if="tableProjectPerformanceFinish" :tableData="tableProjectPerformanceFinish">
      </ProjectPerformanceFinish>
      <!-- 考核结果 -->
      <AssessmentResults v-if="tableAssessmentResults" :tableData="tableAssessmentResults"></AssessmentResults>
      <!-- 会议管理 -->
      <Meeting v-if="Object.keys(formMeeting).length" :formProp="formMeeting"></Meeting>
      <!-- 不良资产机构管理 -->
      <OrganizationalManagement v-if="organizationalManagementDetailId"
        :organizationalManagementDetailId="organizationalManagementDetailId"></OrganizationalManagement>
      <!-- 不良资产委外分案 -->
      <Outsourced v-if="outsourcedDetailId" :outsourcedDetailId="outsourcedDetailId"></Outsourced>
      <!-- 不良资产渠道业务对账单 -->
      <ChannelBusiness v-if="channelBusinessDetailId" :channelBusinessDetailId="channelBusinessDetailId">
      </ChannelBusiness>
      <!-- 不良资产财务结算单 -->
      <FinancialSettlement v-if="financialSettlementDetailId"
        :financialSettlementDetailId="financialSettlementDetailId">
      </FinancialSettlement>
    </div>

    <el-divider></el-divider>

    <div>
      <span style="font-size: 16px; font-weight: bold">流程属性</span>
      <br />

      <div>
        <div>
          <el-row>
            <el-col :span="2">
              <div style="
                  height: 50px;
                  background: #f2f2f2;
                  border: 1px solid #cccccc;
                ">
                <div style="margin-left: 6px; margin-top: 13px">紧急程度</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div style="height: 50px; border: 1px solid #cccccc">
                <el-radio-group v-model="radio" style="margin-left: 12px; margin-top: 16px">
                  <el-radio :label="1">正常</el-radio>
                  <el-radio :label="2" style="color: #ff9900">较急</el-radio>
                  <el-radio :label="3" style="color: #ff0000">紧急</el-radio>
                </el-radio-group>
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="2">
              <div style="
                  height: 120px;
                  background: #f2f2f2;
                  border: 1px solid #cccccc;
                ">
                <div style="margin-left: 6px; margin-top: 48px">
                  可阅览人
                  <el-tooltip class="item" effect="dark" content="阅览人可在【待我阅览】列表看到您的流程" placement="top">
                    <i class="el-icon-warning-outline"></i>
                  </el-tooltip>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div style="height: 120px; border: 1px solid #cccccc">
                <div style="margin-left: 12px; margin-top: 20px">
                  <div>
                    <span>默认用户：</span>
                    <span v-if="businessId != null && businessId != ''">
                      <!--                      <span v-if="userListTypeOne.length != 0"></span>-->
                      <span v-for="(item, index) in userList">
                        {{ item.nickName }}
                        <span v-if="userList.length - 1 != index">,</span>
                        <span v-if="userList.length - 1 != index" style="margin-left: 3px"></span>
                      </span>
                    </span>
                    <span v-if="businessId == null || businessId == ''">
                      <span v-for="(item, index) in (templateObj &&
                        templateObj.postList) ||
                        []">{{ item.postName }}</span>
                    </span>
                    <span v-if="businessId == null || businessId == ''">
                      <span v-for="(item, index) in templateObj &&
                        templateObj.userList">{{ item.nickName }}</span>
                    </span>
                  </div>
                  <div v-if="editFlag == 0" style="margin-top: 10px">
                    <span>自定义用户：</span>
                    <el-select class="select-none" v-model="selectObjList" multiple placeholder="点击右侧添加" v-if="
                      businessId != null &&
                      businessId != '' &&
                      !this.$route.query.isCopy
                    " style="width: 400px">
                      <el-option v-for="item in selectListNotObj" :label="item.nickName"
                        :value="item.userId"></el-option>
                    </el-select>

                    <el-select class="select-none" v-model="templateObj.selectList" multiple placeholder="点击右侧添加"
                      v-if="!businessId || this.$route.query.isCopy" style="width: 400px">
                      <el-option v-for="item in selectListNotObj" :label="item.nickName"
                        :value="item.userId"></el-option>
                    </el-select>
                    <el-button type="primary" size="mini" @click="handleAddIntoSelectListDialog"
                      style="margin-left: 16px">+
                      添加用户</el-button>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="2">
              <div style="
                  height: 50px;
                  background: #f2f2f2;
                  border: 1px solid #cccccc;
                ">
                <div style="margin-left: 6px; margin-top: 13px">
                  待办通知方式
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div style="height: 50px; border: 1px solid #cccccc">
                <div style="margin-left: 12px; margin-top: 10px">
                  <span>
                    <el-checkbox style="zoom: 120%" v-model="checked" />
                    <span>企业微信</span>
                  </span>
                  <span style="margin-left: 16px">
                    <el-checkbox style="zoom: 120%" v-model="checked2" disabled />
                    <span>邮件</span>
                  </span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <el-tab-pane label="流程图" name="first">
            <div style="position: relative; height: 100%; width: 70%">
              <iframe id="iframe" :src="modelerUrl" frameborder="0" width="100%" height="720px"
                scrolling="auto"></iframe>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 选择查阅人对话框 -->
    <el-dialog :title="title" :visible.sync="selectOpen" width="40%" append-to-body>
      <el-form @submit.native.prevent ref="form" :model="dialogUserQueryParams">
        <el-form-item label="姓名查询：" prop="nickName">
          <el-autocomplete v-model="dialogUserQueryParams.nickName" :fetch-suggestions="dialogUserQuerySearch"
            placeholder="请输入" show-word-limit @select="handleSelect" style="width: 20%"></el-autocomplete>
        </el-form-item>
      </el-form>
      <el-table :data="dialogUserList" border @row-click="handleCheckAllChange" style="width: 500px" height="600px">
        <el-table-column label prop="selectFlag" width="40px">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.selectFlag" @change="handleCheckAllChange(scope.row)"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="人员姓名" prop="nickName"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="dialogClose">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="openNodePostTitle" :visible.sync="openNodePost" width="40%">
      <div class="el-dialog-div">
        <div style="width: 30%; float: left">
          <div style="width: 100%; height: 100%; float: left">
            <el-tree :data="dialogPostList" :props="dialogPostList" :filter-node-method="filterNode" ref="tree"
              default-expand-all @node-click="postHandleNodeClick">
              <span class="custom-tree-node" slot-scope="{ node }">
                <span>
                  <!-- <i class="el-icon-house"></i> -->
                  <i class="el-icon-folder-opened"></i>
                </span>
                <span>{{ node.label }}</span>
              </span>
            </el-tree>
          </div>
        </div>

        <div style="width: 70%; float: left">
          <el-table :data="userDialogList" @row-click="newFlow">
            <el-table-column width="50" align="center">
              <template slot-scope="scope">
                <el-radio-group v-model="nextFlowApproveUserName">
                  <el-radio :label="scope.row.userName">
                    <span></span>
                  </el-radio>
                </el-radio-group>
              </template>
            </el-table-column>
            <el-table-column label="用户昵称" align="center" prop="nickName" />
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeOp">取 消</el-button>
        <el-button v-if="!isEditFlag" type="primary" @click="getUserName">确 定</el-button>
        <el-button v-if="isEditFlag" type="primary" @click="getUserNameForEdit">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="openNodeUserTitle" :visible.sync="openNodeUser" width="40%">
      <div class="el-dialog-div">
        <div>
          <el-table :data="dialogNextUserList" @row-click="newFlow">
            <el-table-column width="50" align="center">
              <template slot-scope="scope">
                <el-radio-group v-model="nextFlowApproveUserName">
                  <el-radio :label="scope.row.userName">
                    <span></span>
                  </el-radio>
                </el-radio-group>
              </template>
            </el-table-column>
            <el-table-column label="用户昵称" align="center" prop="nickName" />
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeNextUserOp">取 消</el-button>
        <el-button type="primary" @click="getNextUserName">确 定</el-button>
      </div>
    </el-dialog>
    <addItem :editData="editData" :seeType="seeType" v-if="addItemType" @close="addItemType = false" />
    <el-dialog :visible.sync="showImgViewer" title="请点击图片进行，放大缩小等操作" append-to-body width="550px">
      <div class="demo-image__preview">
        <el-image :src="photoUrl" :preview-src-list="imagePreviewUrls"></el-image>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button style="display: block; margin: 0 auto" @click="showImgViewer = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { updateInfoPur, receiveMainAdd, receiveMainEdit, supplyPurchase, supplyPurchaseEdit, } from "@/api/officeSupplies/officeSupplies";
import { addNewEditInfoTrader } from "@/api/oa/voucharRules";
import salaryData from "./salaryData";
import { getDicts, listData } from "@/api/system/dict/data";
import {
  commitFormalById,
  personnelFormal,
  updateFormal,
} from "@/api/personnel/becomeWorker";
import {
  commitResignation,
  personnelResignation,
} from "@/api/personnel/resignation";
import {
  commitOnboardings,
  personnelProcess,
  personnelOnboarding,
  personnelProcessId,
} from "@/api/personnel/entry";
import { personnelTransfer } from "@/api/personnel/transfer";

import {
  batchInformationDownloadProcess,
  batchInformationdirectUsedProcess,
  batchInformationProcess,
  getDataToFlow,
  batchInformationUseProcess,
  commitInformations,
  informationUserList,
  informationUser,
  initiateProcess,
} from "@/api/directoryMation/directoryMation";
import { startProd, deleteTemporarily } from "@/api/oa/deploy";
import {
  batchInformationDownloadProcessSupervise,
  batchInformationProcessSupervise,
  batchInformationUseProcessSupervise,
  commitInformationsSupervise,
} from "@/api/directoryMation/directoryMationSupervise";
import {
  listTemplate,
  getTemplate,
  delTemplate,
  addTemplate,
  updateTemplate,
  changeenableStatus,
  changeNotificationProcessEnable,
  changeEditFlagStatus,
  listUserForOaProcessTemplateFlow,
  getFormProcKey,
  flowAddUserListAndNotification,
  flowUserListInfoAndNotificationInfo,
  flowUpdateUserListAndNotification,
  downloadByUrl,
  allCompany,
  InfomationListSupervise,
  InfomationList,
} from "@/api/oa/processTemplate";
import {
  getDefinitionsByInstanceId,
  selectJieDian,
} from "@/api/flow/definition";
import {
  processLicenseProcess,
  pendingDetailPost,
  putLicense,
} from "@/api/certificate/allLicenses";
import {
  getUserDialogListByPostId,
  getUserProfile,
  listUser,
} from "@/api/system/user";
import {
  flowInfo,
  getFlowInfoByFlowFullId,
  saveData,
  startFlow,
  checkFeeFlow,
  updateFlowInfo,
} from "@/api/flow/flow";
import { inspectionTime } from "@/api/meeting/staging";
import { getDef } from "@/api/form/form";
import {
  listDeploy,
  lxProjectAdd,
  postponeAudit,
  terminateProject,
  relationAdd,
} from "@/api/oa/deploy";

import { selectListData } from "@/api/oa/data";
import { getOADictData } from "@/api/system/dict/type";
import {
  getPostList,
  getPostListByCode,
  getFeeSelectList,
  ziliaogongsi,
  ziliaoxiangmu,
} from "@/api/form/formdesign";
import addItem from "../../dataManagement/inputData/addItem.vue";
import CerAllLicenses from "./components/allLicenses/Table";
import PerEntryForm from "./components/entry/ElForm";
import PerBecomeForm from "./components/become/ElForm";
import PerTranForm from "./components/transfer/ElForm";
import PerResign from "./components/resign/ElForm";
import Leave from "./components/leave/Form";
import ProEditForm from "./components/editProject/ElForm";
import ProDelForm from "./components/delProject/ElForm";
import ProductAddForm from "./components/product/AddProduct";
import ProductEditForm from "./components/product/EditProduct";
import CompanyAddForm from "./components/company/AddCompany";
import CompanyEditForm from "./components/company/EditCompany";
import AddLxProject from "./components/lxProject/AddLxProject";
import ClaimLxProject from "./components/lxProject/ClaimLxProject";
import DelayLxProject from "./components/lxProject/DelayLxProject";
import TermLxProject from "./components/lxProject/TermLxProject";
import EditLxProject from "./components/lxProject/EditLxProject";
import EditCollecPay from "./components/collecPay/EditCollecPay";
import EditInfo from "./components/editInfo/EditInfo";
import Supervise from "./components/supervise/Table";
import Meeting from "./components/meeting/Form";
import AddPayment from "./components/payment/AddPayment";
import EditPayment from "./components/payment/EditPayment";
import OrganizationalManagement from "./components/organizationalManagement/Form";
import Outsourced from "./components/outsourced/Form";
import ChannelBusiness from "./components/channelBusiness/Form";
import FinancialSettlement from "./components/financialSettlement/Form";
import Supplies from "./components/office/supplies";
import Purchase from "./components/office/purchase";

import { getPendLicenseInfo } from "@/api/certificate/pendingProcessing";
import dataUpLoadApprovalSupervise from "@/mixin/updateProcessForm/dataUpLoadApprovalSupervise";

import {
  leaveProcessId,
  commitAskLeave,
  updateLeave,
} from "@/api/checkWork/leave";
import overTime from "@/mixin/updateProcessForm/overTime";
import bonusPenalty from "@/mixin/updateProcessForm/bonusPenalty";
import goErrand from "@/mixin/updateProcessForm/goErrand";
import annualPlanReview from "@/mixin/updateProcessForm/annualPlanReview";
import assessmentConfiguration from "@/mixin/updateProcessForm/assessmentConfiguration";
import projectPerformance from "@/mixin/updateProcessForm/projectPerformance";
import assessmentResults from "@/mixin/updateProcessForm/assessmentResults";
import meeting from "@/mixin/updateProcessForm/meeting";
import organizationalManagement from "@/mixin/updateProcessForm/organizationalManagement";
import outsourced from "@/mixin/updateProcessForm/outsourced";
import channelBusiness from "@/mixin/updateProcessForm/channelBusiness";
import financialSettlement from "@/mixin/updateProcessForm/financialSettlement";
import { clone } from "xe-utils";

export default {
  mixins: [
    overTime,
    annualPlanReview,
    assessmentConfiguration,
    projectPerformance,
    assessmentResults,
    dataUpLoadApprovalSupervise,
    meeting,
    organizationalManagement,
    outsourced,
    channelBusiness,
    financialSettlement,
    bonusPenalty,
    goErrand,
  ],
  components: {
    addItem,
    PerEntryForm,
    PerBecomeForm,
    PerTranForm,
    PerResign,
    ProEditForm,
    salaryData,
    CerAllLicenses,
    Leave,
    ProDelForm,
    ProductAddForm,
    ProductEditForm,
    CompanyAddForm,
    CompanyEditForm,
    AddLxProject,
    ClaimLxProject,
    DelayLxProject,
    TermLxProject,
    EditLxProject,
    Supervise,
    Meeting,
    EditCollecPay,
    EditInfo,
    AddPayment,
    EditPayment,
    OrganizationalManagement,
    Outsourced,
    ChannelBusiness,
    FinancialSettlement,
    Supplies,
    Purchase
  },
  name: "UpdateProcessForm",
  data() {
    return {
      buttonType: false,
      photoUrl: "",
      imagePreviewUrls: [],
      showImgViewer: false,
      editData: null,
      seeType: false,
      addItemType: false,
      //申请人状态
      flowStartStatus: "",
      subData: {},
      updateFormInfo: {},
      //下一流程的审批人员
      nextFlowApproveUserName: null,
      userDialogList: [],
      //所有岗位列表
      allPostList: [],
      //筛选到弹窗里的岗位列表
      dialogPostList: [],
      dialogNextUserList: [],
      //岗位弹窗控制
      openNodePost: false,
      openNodeUser: false,
      openNodePostTitle: "请选择下一流程的审批人员",
      openNodeUserTitle: "请选择下一流程的审批人员",

      //流程第二步节点
      secondNode: null,
      //流程第二步人员
      secondNodeForUser: null,
      //流程第二步岗位
      secondNodeForPost: null,

      allCompanyList: [],
      dynamicData: {
        projectList: [],
        oaDictData: [],
        InformationList: [],
        firstSubjectData: [],
        secondSubjectData: [],
        projectTypeData: [],
        feeCompanyData: [],
        informationCompany: [],
        informationProject: [],
      },
      //流程全称id
      flowFullId: null,
      //表单名称
      formName: null,
      //关联key
      procKey: null,
      //当前状态
      currentStatus: null,
      //是否编辑页面跳转
      isEditFlag: false,
      //是否审批通过后回到驳回节点
      specifiedNode: false,
      //下拉框（有businessId时使用）
      selectObjList: [],
      userList: [],
      userListTypeZero: [],
      userListTypeOne: [],
      companyName: null,
      //流程编号
      processNumber: null,
      //创建者
      createName: null,
      //bussinessId
      businessId: null,
      //oid
      oid: null,
      //流程图key
      refProcKey: null,
      //表单对象
      formObj: {},
      //表单id
      formId: null,
      newFromId: null,
      //用户相关信息
      user: {},
      roleGroup: {},
      postGroup: {},

      //页面展示模板默认用户
      defaultTemplateUser: [],
      //模板类型（最上面标题用）
      templateType: "0",
      //自定义凭证生成时间
      voucherDate: null,
      //是否自定义生存凭证时间
      customFlag: false,
      //通知方式复选框
      checked: false, //企业微信
      checked2: false, //邮件
      //下拉框里的对象
      selectListNotObj: [],
      //弹窗用户列表
      dialogUserList: [],
      //对话框内的用户昵称数组
      dialogUserNickNameList: [],
      //弹窗用户搜索条件
      dialogUserQueryParams: {
        nickName: "",
      },
      //dialog显示
      selectOpen: false,
      //主题
      theme: null,
      //待办方式 - 企业微信
      qiyeWeChat: false,
      //待办方式 - 邮件
      email: false,
      //紧急程度对象，默认是1-正常
      radio: 1,
      salaryDataType: false,
      //流程URL
      modelerUrl: "",
      //表单数据
      jsonData: {},
      editFlagBoolean: false,
      editFlag: null,
      //获得的对象
      templateObj: null,
      //模板启用状态
      isEnable: null,
      //模板名称
      templateName: null,
      //模板id
      templateId: null,
      //上级菜单id
      classificationId: null,
      //公司id
      companyId: null,
      //标签页
      activeName: "first",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      zlhqf: "",
      dataList: [],
      dataListSupervise: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      themeType: false,
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        dictName: null,
        dictType: null,
        isEnable: null,
        endUpdateTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      informationUserList: [],
      //证照模块变量
      tableCerAllLienses: [],
      tableConflict: [],
      //证照模块变量
      //人事模块需用的变量
      formPerEntry: {},
      formPerBecome: {},
      formPerTransfer: {},
      formPerResign: {},
      formProEdit: null,
      formProDel: null,
      formDelEdit: null,
      formEntryId: null,
      salaryData2: null,
      oaModuleType: "",
      formPerson: {},
      //请假申请变量
      tableCheckWorkLeave: {},
      projectText: "",
      formProductAdd: null,
      formProductEdit: null,
      formProductDel: null,
      formCompanyAdd: null,
      formCompanyEdit: null,
      formCompanyDel: null,
      formLxProAdd: null,
      formLxProDelay: null,
      formLxProTerm: null,
      formLxProEdit: null,
      formLxProClaim: null,
      formLxProjectNameEdit: null,
      processTypeSupervise: null,
      formEditCollecPay: null,
      projectNamesSupervise: "",
      companyNamesSupervise: "",
      formEditInformation: null,
      formAddPayment: null,
      formEditPayment: null,
      formDelPayment: null,
      officeSuppliesData: null,
      officePurchaseData: null,
    };
  },

  async created() {
    if (this.$route.query.oid) {
      const { data } = await flowInfo(this.$route.query.oid);
      this.oaModuleType = data?.flowInfo?.oaModuleType;
    }
    this.initEditCollecPay(); //编辑项目收付款信息
    this.initOffice(); //办公用品
    this.initPayment(); //收付款人
    this.initSupervise(); // 监管报送
    this.initLxProject(); //立项管理跳转执行
    this.initCertificate(); //证照模块跳转进来执行
    this.initCheckWork(); //请假模块跳转进来执行
    this.initProject(); //项目名称跳转执行
    this.initProduct(); //产品信息跳转执行
    this.initCompany(); //公司跳转执行
    this.initPerAppraisal(); //考核模块跳转进来执行
    this.initMeeting(); //会议模块跳转进来执行
    this.initBadSystem(); //不良资产跳转进来执行
    if (this.$route.query.themeTypeXz || this.$route.query.themeTypeZzYy) {
      informationUserList().then((res) => {
        if (res.code == 200) {
          this.informationUserList = res.rows;
          this.informationUserList.forEach((item) => {
            item.value = item.name;
          });
        }
      });
    }
    this.initPersonne(); //人事模块跳转进来执行
    if (this.$route.query.salaryType) {
      this.salaryData2 = JSON.parse(sessionStorage.getItem("salaryData"));
    }
    if (this.$route.query.oid) {
      getDataToFlow(this.$route.query.oid).then((res) => {
        if (res.code == 200) {
          this.salaryData2 = res.data;
          console.log(this.salaryData2);
        }
      });
    }

    //下载流程发起时带过来的资料数据
    if (
      this.$route.query.themeTypeXz ||
      this.$route.query.themeTypeLr ||
      this.$route.query.themeTypeYy ||
      this.$route.query.themeTypeZzYy
    ) {
      this.dataList = JSON.parse(sessionStorage.getItem("dataList"));
    }
    //监管资料
    if (
      this.$route.query.themeTypeXzSupervise ||
      this.$route.query.themeTypeLrSupervise ||
      this.$route.query.themeTypeYySupervise
    ) {
      this.dataListSupervise = JSON.parse(
        sessionStorage.getItem("dataListSupervise")
      );
      this.processTypeSupervise = this.$route.query.themeTypeLrSupervise
        ? 1
        : this.$route.query.themeTypeXzSupervise
          ? 2
          : 3;
    }
    //调用下拉选择器组件
    this.getProject();
    //获取跳转过来的模板id，上级菜单id，公司id
    this.templateId = this.$route.query.templateId;
    this.classificationId = this.$route.query.classificationId;
    this.companyId = this.$route.query.companyId;

    //从查看页面跳转过来
    this.oid = this.$route.query.oid;
    this.businessId = this.$route.query.businessId;
    this.currentStatus = this.$route.query.currentStatus;
    this.isEditFlag = this.$route.query.isEditFlag;
    if (typeof this.currentStatus == "undefined") {
      this.currentStatus = null;
    }
    if (
      typeof this.businessId == "undefined" ||
      this.businessId == "" ||
      this.businessId == null
    ) {
      if (this.classificationId == null && this.companyId == null) {
        this.currentStatus = "5";
      }
    }
    if (this.currentStatus == "5") {
      this.businessId = this.oid;
    }
    if (
      (this.businessId && this.currentStatus != "5") ||
      this.$route.query.isCopy
    ) {
      this.flowDetail();
      this.userListDetail();
    }
    this.templateDetail();
    if (this.currentStatus == "5") {
      this.flowDetail();
    }
    //获得用户信息
    this.getUser();
    //获取所有的公司
    this.getAllCompany();
  },
  watch: {
    zlhqf: {
      handler(newval, oldval) {
        if (this.$route.query.themeTypeXz) {
          const companyName = this.getCompanyName();
          this.theme = `提供授信及贷后资料:${companyName}——${newval}——${this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          )}`;
        } else if (this.$route.query.themeTypeZzYy) {
          const count = this.$route.query.count.toString().padStart(2, "0");
          const companyName = this.getCompanyName();
          this.theme = `提供授信及贷后资料:${companyName}公司——${newval}——${this.$format(
            new Date().getTime(),
            "yyyyMMdd"
          )}${count}`;
        }
      },
    },
    "dialogUserQueryParams.nickName": {
      handler(newval, oldval) {
        if (!newval) {
          this.handleAddIntoSelectListDialog();
        }
      },
      deep: true,
    },
  },
  methods: {
    async initSupervise() {
      //监管资料
      let e = this.oaModuleType;
      if (["JG1", "JGTS1", "JGFX1", "JGKJ1", "JG2", "JG3"].includes(e)) {
        //查询流程绑定的准入资料
        InfomationListSupervise({
          processId: this.$route.query.businessId || this.$route.query.oid,
        }).then((res) => {
          if (res.code == 200) {
            this.dataListSupervise = res.rows;
            if (this.dataListSupervise.length > 0) {
              this.processTypeSupervise = this.dataListSupervise[0].processType;
            }
          }
        });
      }
      if (e == 1 || e == 2 || e == 3 || e == "ZL4") {
        //查询流程绑定的准入资料
        InfomationList({
          processId: this.$route.query.businessId || this.$route.query.oid,
        }).then((res) => {
          if (res.code == 200) {
            this.dataList = res.rows;
            if (this.dataList.length > 0) {
              this.processType = this.dataList[0].processType;
            }
          }
        });
      }
    },
    initOffice() {
      if (this.$route.query.officeSuppliesType) {
        this.officeSuppliesData = JSON.parse(
          sessionStorage.getItem("officeSupplies")
        );
        this.theme = `${sessionStorage.getItem(
          "userNickName"
        )}-办公用品领用-${this.officeSuppliesData.offReceiveDetailList.map(
          (item) => item.supplyName || item.itemName
        )}`;
      }
      if (this.$route.query.officePurchaseType) {
        this.officePurchaseData = JSON.parse(
          sessionStorage.getItem("officePurchase")
        );
        this.theme = `${sessionStorage.getItem(
          "userNickName"
        )}-办公用品采购-${this.officePurchaseData.map(
          (item) => item.itemName
        )}`;
      }
    },
    initProduct() {
      if (this.$route.query.addProduct) {
        this.formProductAdd = JSON.parse(
          sessionStorage.getItem("addProductData")
        );
      }
      if (this.$route.query.editProduct) {
        this.formProductEdit = JSON.parse(
          sessionStorage.getItem("editProductData")
        );
      }
      if (this.$route.query.delProduct) {
        this.formProductDel = JSON.parse(
          sessionStorage.getItem("delProductData")
        );
      }
    },
    initCompany() {
      if (this.$route.query.addCompany) {
        this.formCompanyAdd = JSON.parse(
          sessionStorage.getItem("addCompanyData")
        );
      }
      if (this.$route.query.editCompany) {
        this.formCompanyEdit = JSON.parse(
          sessionStorage.getItem("editCompanyData")
        );
      }
      if (this.$route.query.delCompany) {
        this.formCompanyDel = JSON.parse(
          sessionStorage.getItem("delCompanyData")
        );
      }
    },
    initLxProject() {
      if (this.$route.query.addLxProject) {
        this.formLxProAdd = JSON.parse(sessionStorage.getItem("addLxProData"));
        console.log(this.formLxProAdd);
      }
      if (this.$route.query.delayLxProject) {
        this.formLxProDelay = JSON.parse(
          sessionStorage.getItem("delayLxProData")
        );
        console.log(this.formLxProDelay);
      }
      if (this.$route.query.termLxProject) {
        this.formLxProTerm = JSON.parse(
          sessionStorage.getItem("termLxProData")
        );
        console.log(this.formLxProTerm);
      }
      if (this.$route.query.editLxProject) {
        this.formLxProEdit = JSON.parse(
          sessionStorage.getItem("editLxProData")
        );
        console.log(this.formLxProjectNameEdit);
      }
      if (this.$route.query.claimLxProject) {
        this.formLxProClaim = JSON.parse(
          sessionStorage.getItem("claimLxProData")
        );
        console.log(this.formLxProjectNameEdit);
      }
      if (this.$route.query.editLxProjectName) {
        this.formLxProjectNameEdit = JSON.parse(
          sessionStorage.getItem("editLxProjectNameData")
        );
        console.log(this.formLxProjectNameEdit);
      }
    },
    initEditCollecPay() {
      if (this.$route.query.editCollecPay) {
        this.formEditCollecPay = JSON.parse(
          sessionStorage.getItem("editCollecPayData")
        );
        this.theme =
          "业务信息配置-修改项目收付款信息申请-" +
          this.formEditCollecPay.projectName;
      }
      if (this.$route.query.editInformation) {
        this.formEditInformation = JSON.parse(
          sessionStorage.getItem("editInformationData")
        );
        this.theme =
          "业务信息配置-修改项目信息费信息申请-" +
          this.formEditInformation.projectName;
      }
    },
    initPayment() {
      if (this.$route.query.addPayment) {
        this.formAddPayment = JSON.parse(
          sessionStorage.getItem("addPaymentData")
        );
        this.theme = "业务信息配置-新增收付款人申请";
      }
      if (this.$route.query.editPayment) {
        this.formEditPayment = JSON.parse(
          sessionStorage.getItem("editPaymentData")
        );
        this.theme = "业务信息配置-修改收付款人申请";
      }
      if (this.$route.query.delPayment) {
        this.formDelPayment = JSON.parse(
          sessionStorage.getItem("delPaymentData")
        );
        this.theme = "业务信息配置-删除收付款人申请";
      }
    },
    initProject() {
      if (this.$route.query.editProject) {
        this.formProEdit = JSON.parse(sessionStorage.getItem("editProData"));
        console.log(this.formProEdit);
      }
      if (this.$route.query.delProject) {
        this.formProDel = JSON.parse(sessionStorage.getItem("delProData"));
        console.log(this.formProDel);
      }
    },
    changeKbuild(value, key) {
      if (
        this.$route.query.themeTypeXzSupervise ||
        this.$route.query.themeTypeYySupervise ||
        this.$route.query.themeTypeLrSupervise
      ) {
        if (key == "cooperationProjectName") {
          this.projectNamesSupervise = this.dynamicData.informationProject
            .filter((item) => value.includes(item.value))
            .map((item) => item.label)
            .join("、");
        }
        if (key == "cooperationCompanyName") {
          this.companyNamesSupervise = this.dynamicData.informationCompany
            .filter((item) => value.includes(item.value))
            .map((item) => item.label)
            .join("、");
        }
        const count = this.$route.query.count.toString().padStart(2, "0");
        let time = this.$format(new Date().getTime(), "yyyyMMdd");
        this.theme = `提供监管报送${this.$route.query.themeTypeYySupervise ? "用印" : ""
          }资料:${this.companyNamesSupervise ? this.companyNamesSupervise + "——" : ""
          }${this.projectNamesSupervise ? this.projectNamesSupervise + "——" : ""
          }${time}${count}`;
      }
    },
    getText(e) {
      console.log(e);
      this.projectText = e;
    },
    getCompanyName() {
      let companyName;
      if (this.companyName) {
        companyName = this.companyName;
      } else {
        companyName = this.allCompanyList.filter(
          (item) => item.id == this.companyId
        )[0]?.companyName;
      }
      return companyName;
    },
    getPerName(data, name, unit = "") {
      const groupedByYear = data.reduce((acc, item) => {
        const { year } = item; // 提取年份
        const names = item[name]; // 动态获取名称

        if (!acc[year]) {
          acc[year] = [];
        }
        acc[year].push(`${names}${unit}`);
        return acc;
      }, {});
      return Object.entries(groupedByYear)
        .map(([year, companies]) => `${year}年${companies.join("/")}`)
        .join(",");
    },
    getTheme() {
      let userNickName = sessionStorage.getItem("userNickName");
      if (this.$route.query.addLxProject) {
        this.theme = `${userNickName}的新增${this.formLxProAdd.projectForm?.projectName}立项申请`;
      }
      if (this.$route.query.delayLxProject) {
        this.theme = `${userNickName}的延期${this.formLxProDelay.projectForm?.projectName}项目申请`;
      }
      if (this.$route.query.termLxProject) {
        this.theme = `${userNickName}的终止${this.formLxProTerm.projectForm?.projectName}项目申请`;
      }
      if (this.$route.query.editLxProject) {
        this.theme = `${userNickName}的修改${this.formLxProEdit.oldData.projectForm?.projectName}项目申请`;
      }
      if (this.$route.query.claimLxProject) {
        this.theme = `${userNickName}的认领${this.formLxProClaim.oldData.projectForm?.projectName}项目申请`;
      }
      if (this.$route.query.editLxProjectName) {
        this.theme = `${userNickName}的修改${this.formLxProjectNameEdit.oldData?.projectName}项目名称申请`;
      }
      if (this.$route.query.addProduct) {
        this.theme = `${userNickName}的新增${this.formProductAdd.productName}产品申请`;
      }
      if (this.$route.query.editProduct) {
        this.theme = `${userNickName}的修改${this.formProductEdit.oldData?.productName}产品申请`;
      }
      if (this.$route.query.delProduct) {
        this.theme = `${userNickName}的删除${this.formProductDel.productName}产品申请`;
      }
      if (this.$route.query.addCompany) {
        this.theme = `${userNickName}的新增${this.formCompanyAdd.companyName}公司申请`;
      }
      if (this.$route.query.editCompany) {
        this.theme = `${userNickName}的修改${this.formCompanyEdit.oldData?.companyName}公司申请`;
      }
      if (this.$route.query.delCompany) {
        this.theme = `${userNickName}的删除${this.formCompanyDel.companyName}公司申请`;
      }
      if (this.$route.query.editProject) {
        this.theme = `${userNickName}的修改${this.formProEdit.oldData?.projectName}项目申请`;
      }
      if (this.$route.query.delProject) {
        this.theme = `${userNickName}的删除${this.formProDel.projectName}项目申请`;
      }
      if (this.$route.query.themeTypeYy) {
        const companyName = this.getCompanyName();
        if (this.dataList.length) {
          this.theme = `提供授信及贷后用印资料：${companyName}—${this.dataList[0].user
            }—${this.$format(new Date().getTime(), "yyyy-MM-dd HH:mm:ss")}`;
        } else {
          const themeTypeYy = JSON.parse(sessionStorage.getItem("ThemeTypeYy"));
          this.theme = `提供授信及贷后用印资料：${companyName}—${themeTypeYy.theme
            }—${this.$format(new Date().getTime(), "yyyy-MM-dd HH:mm:ss")}`;
        }
      }
      if (this.$route.query.checkWorkLeave) {
        this.theme = `${this.tableCheckWorkLeave.nickName}的请假申请`;
      }
      if (this.$route.query.checkWorkOverTime) {
        this.theme = `${this.tableCheckWorkOverTime.nickName}的加班申请`;
      }
      if (this.$route.query.checkWorkBonusPenalty) {
        if (this.tableCheckWorkBonusPenalty.type == "1") {
          this.theme = `${this.tableCheckWorkBonusPenalty.nickName}的奖励申请`;
        } else if (this.tableCheckWorkBonusPenalty.type == "2") {
          this.theme = `${this.tableCheckWorkBonusPenalty.nickName}的惩罚申请`;
        }
      }
      if (this.$route.query.checkWorkBonusPenaltyList) {
        const types = this.tableCheckWorkBonusPenaltyList.map(
          (item) => item.type
        );
        if (types.includes("1") && types.includes("2")) {
          this.theme = `奖惩申请流程`;
        } else if (types.includes("1")) {
          this.theme = `奖励申请流程`;
        } else if (types.includes("2")) {
          this.theme = `惩罚申请流程`;
        }
      }
      if (this.$route.query.checkWorkGoErrand) {
        this.theme = `${this.tableCheckWorkGoErrand.applicantName}的出差申请`;
      }
      if (this.$route.query.perEntry) {
        this.theme = `${this.formPerEntry.name}的入职申请`;
      }
      if (this.$route.query.perBecome) {
        this.theme = `${this.formPerBecome.name}的转正申请`;
      }
      if (this.$route.query.perResign) {
        this.theme = `${this.formPerResign.name || this.formPerResign.showName
          }的离职申请`;
      }
      if (this.$route.query.perTransfer) {
        this.theme = `${this.formPerTransfer.name}的换岗申请`;
      }
      if (this.$route.query.themeTypeLr) {
        this.theme = `${this.dataList[0].orgName}授信及贷后资料录入`;
      }
      if (this.$route.query.themeTypeLrSupervise) {
        const count = this.$route.query.count.toString().padStart(2, "0");
        this.theme = `监管报送资料管理—录入资料—${this.$format(
          new Date().getTime(),
          "yyyyMMdd"
        )}${count}`;
      }
      if (this.$route.query.annualPlanReview) {
        this.theme =
          this.getPerName(this.tableAnnualPlanReview, "shortName") + "年度计划";
      }
      if (this.$route.query.assessConfigReviewTable) {
        this.theme =
          this.getPerName(
            this.tableAssessmentConfiguration,
            "companyShortName",
            "公司"
          ) + "考核配置";
      }
      if (this.$route.query.assessConfigReviewTableFinish) {
        this.theme = `${this.tableAssessmentConfigurationFinish.year}年${this.tableAssessmentConfigurationFinish.companyShortName}公司考核配置`;
      }
      if (this.$route.query.projectPerformanceReviewTable) {
        const obj = {
          1: "一",
          2: "二",
          3: "三",
          4: "四",
        };
        this.theme = `${this.tableProjectPerformance[0].year}年第${obj[this.tableProjectPerformance[0].quarter]
          }季度公司项目业绩汇总`;
      }
      if (this.$route.query.projectPerformanceReviewTableFinish) {
        this.theme = `${this.tableProjectPerformanceFinish.year}年${this.tableProjectPerformanceFinish.projectName}项目业绩录入`;
      }
      if (this.$route.query.assessmentResultsTable) {
        this.theme =
          this.getPerName(this.tableAssessmentResults, "companyShortName") +
          "考核结果";
      }
      if (this.$route.query.metting) {
        this.theme = this.formMeeting.meetingTheme;
      }
      if (this.$route.query.organizationalManagementDetailId) {
        this.theme = "机构审核";
      }
      if (this.$route.query.outsourcedDetailId) {
        this.theme = "委案分案审核";
      }
      if (this.$route.query.channelBusinessDetailId) {
        this.theme = "渠道业务对账单审核";
      }
      if (this.$route.query.financialSettlementDetailId) {
        this.theme = "财务结算单审核";
      }
    },
    async initCertificate() {
      this.initCerAllLienses();
    },
    delAllLicenses(row) {
      this.tableCerAllLienses = this.tableCerAllLienses.filter(
        (item) => item.id != row.id
      );
    },
    async initCerAllLienses() {
      if (this.$route.query.cerAllLicenses) {
        this.tableCerAllLienses = JSON.parse(
          sessionStorage.getItem("oa-allLicensesTable")
        );
      }
      if (this.$route.query.oid && this.oaModuleType == 10) {
        const id = this.$route.query.businessId || this.$route.query.oid;
        const { rows } = await getPendLicenseInfo(id);
        this.tableCerAllLienses = rows;
      }
    },
    async initCheckWork() {
      this.initCheckWorkLeave();
      this.initCheckWorkOverTime();
      this.initCheckWorkBonusPenalty();
      this.initCheckWorkGoErrand();
    },
    async initCheckWorkLeave() {
      if (this.$route.query.checkWorkLeave) {
        this.tableCheckWorkLeave = JSON.parse(
          sessionStorage.getItem("oa-checkWorkLeaveTable")
        );
      }
      if (this.$route.query.oid && this.oaModuleType == 11) {
        const id = this.$route.query.businessId || this.$route.query.oid;
        const { data } = await leaveProcessId(id);
        if (data) this.tableCheckWorkLeave = data;
      }
    },
    async initPerAppraisal() {
      this.initAnnualPlanReview();
      this.initAssessmentConfiguration();
      this.initAssessmentConfigurationFinish();
      this.initProjectPerformance();
      this.initProjectPerformanceFinish();
      this.initAssessmentResults();
    },
    async initBadSystem() {
      this.initOrganizationalManagement();
      this.initOutsourced();
      this.initChannelBusiness();
      this.initFinancialSettlement();
    },
    queryZlhqf(queryString, cb) {
      var restaurants = this.informationUserList;
      // if (restaurants.length !== 0) {
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
      // } else {
      //   cb([]);
      // }
    },
    seeImg(e) {
      this.handlePreview(e);
    },
    async initPersonne() {
      const id = this.$route.query.businessId || this.$route.query.oid;
      let data = null;
      if (id) {
        const dataRes = await personnelProcessId(id);
        data = dataRes.data;
      }
      this.formEntryId = data?.id;
      this.formPerson = { ...data };
      this.initPersonnelEntry(data);
      this.initPersonnelBecome(data);
      this.initPersonnelTransfer(data);
      this.initPersonnelResign(data);
    },
    initPersonnelEntry(data) {
      if (this.$route.query.perEntry) {
        this.formPerEntry = JSON.parse(sessionStorage.getItem("oa-entryForm"));
      }
      if (this.$route.query.oid && data.processType == 1) {
        personnelOnboarding(data.correlationId).then((res) => {
          if (res.code == 200) {
            this.formPerEntry = res.data;
          }
        });
      }
    },
    initPersonnelBecome(data) {
      if (this.$route.query.perBecome) {
        this.formPerBecome = JSON.parse(
          sessionStorage.getItem("oa-becomeForm")
        );
      }
      if (this.$route.query.oid && data.processType == 2) {
        personnelFormal(data.correlationId).then((res) => {
          if (res.code == 200) {
            this.formPerBecome = res.data;
          }
        });
      }
    },
    initPersonnelTransfer(data) {
      if (this.$route.query.perTransfer) {
        this.formPerTransfer = JSON.parse(
          sessionStorage.getItem("oa-transferForm")
        );
      }
      if (this.$route.query.oid && data.processType == 3) {
        personnelTransfer(data.correlationId).then((res) => {
          if (res.code == 200) {
            this.formPerTransfer = res.data;
          }
        });
      }
    },
    initPersonnelResign(data) {
      if (this.$route.query.perResign) {
        this.formPerResign = JSON.parse(
          sessionStorage.getItem("oa-resignForm")
        );
      }
      if (this.$route.query.oid && data.processType == 5) {
        personnelResignation(data.correlationId).then((res) => {
          if (res.code == 200) {
            this.formPerResign = res.data;
          }
        });
      }
    },
    see(v) {
      this.seeType = true;
      this.editData = { ...v };
      this.addItemType = true;
    },
    changeForm(e) {
      console.log(this.zlhqf);
      return;
      if (v == "select_Information") {
        this.dynamicData.InformationList.forEach((item) => {
          if (item.value == e) {
            console.log(item);
            this.theme = `${item.label}${this.$format(
              new Date().getTime(),
              "yyyy-MM-dd"
            )}提供资料`;
          }
        });
      }
    },
    //关闭下一个用户弹窗并打印用户名
    getNextUserName() {
      this.openNodeUser = false;
      this.goStartFlow(this.subData);
    },
    //关闭下一个用户弹窗
    closeNextUserOp() {
      this.openNodeUser = false;
    },
    //关闭岗位弹窗并打印用户名
    getUserName() {
      this.openNodePost = false;
      this.goStartFlow(this.subData);
    },
    getUserNameForEdit() {
      this.openNodePost = false;
      this.goUpdateFlowInfo(this.updateFormInfo);
    },
    //关闭岗位弹窗
    closeOp() {
      this.openNodePost = false;
    },
    //岗位单击事件
    postHandleNodeClick(data) {
      getUserDialogListByPostId({ postId: data.postId }).then((resp) => {
        this.userDialogList = resp.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    getAllCompany() {
      allCompany().then((response) => {
        this.allCompanyList = response;
        this.getTheme();
      });
    },
    getProject() {
      //获取项目名称
      listDeploy({ isEnable: "Y" }).then((response) => {
        this.dynamicData.projectList = response.rows.map((obj) => {
          return {
            value: obj.id,
            label: obj.projectName,
          };
        });
      });

      //获取oa字典配置
      selectListData().then((response) => {
        this.dynamicData.oaDictData = response.rows.map((obj) => {
          return {
            value: obj.dictTypeIdent,
            label: obj.dictTypeName,
          };
        });
      });
      getDicts("Information_type").then((res) => {
        this.dynamicData.InformationList = res.data.map((obj) => {
          return {
            value: obj.dictValue,
            label: obj.dictLabel,
          };
        });
        console.log(this.dynamicData.InformationList);
      });

      getOADictData().then((response) => {
        this.dynamicData.firstSubjectData = response.firstSubject.map((obj) => {
          return {
            value: obj.value,
            label: obj.label,
          };
        });
        this.dynamicData.secondSubjectData = response.secondSubject.map(
          (obj) => {
            return {
              value: obj.value,
              label: obj.label,
            };
          }
        );
        this.dynamicData.projectTypeData = response.projectTypeList.map(
          (obj) => {
            return {
              value: obj.value,
              label: obj.label,
            };
          }
        );
      });

      getFeeSelectList().then((response) => {
        this.dynamicData.feeCompanyData = response.feeList.map((obj) => {
          return {
            value: obj.id,
            label: obj.userName,
          };
        });
      });
      // if (
      //   this.$route.query.themeTypeYySupervise ||
      //   this.$route.query.themeTypeXzSupervise ||
      //   this.$route.query.themeTypeLrSupervise
      // ) {
      ziliaogongsi().then((response) => {
        this.dynamicData.informationCompany = response.informationCompany.map(
          (obj) => {
            return {
              value: obj.id,
              label: obj.companyName,
            };
          }
        );
      });
      ziliaoxiangmu().then((response) => {
        this.dynamicData.informationProject = response.informationProject.map(
          (obj) => {
            return {
              value: obj.id,
              label: obj.projectName,
            };
          }
        );
      });
      // }
    },
    //获取可阅览人和待办通知方式
    userListDetail() {
      flowUserListInfoAndNotificationInfo(this.businessId).then((response) => {
        this.userList = response.userList;
        this.userListTypeZero = this.userList.filter((t) => t.userType == 0);
        this.userListTypeOne = this.userList.filter((t) => t.userType == 1);
        for (let i = 0; i < response.notificationList.length; i++) {
          if (
            response.notificationList[i].notificationType == 0 &&
            response.notificationList[i].processEnable == 0
          ) {
            //通知方式为微信
            this.checked = true;
          }
          if (
            response.notificationList[i].notificationType == 1 &&
            response.notificationList[i].processEnable == 0
          ) {
            //通知方式为微信
            this.checked2 = true;
          }
        }
      });
    },
    //获取流程的信息
    flowDetail() {
      flowInfo(this.businessId || this.oid).then((response) => {
        if (response.code != 200) {
          this.$modal.msgError("出现错误数据");
        } else {
          //获取流程对应的表单
          // getDef(response.data.flowInfo.newFromId).then((response) => {
          //   this.jsonData = JSON.parse(response.data.defination);
          // });
          //赋值表单数据
          this.$nextTick(() => {
            console.log(response.data.flowInfo.data, "================");
            if (this.$route.query.isCopy) {
              for (const item in response.data.flowInfo.data) {
                console.log(item);
                if (item.includes("uploadFile")) {
                  response.data.flowInfo.data[item] = [];
                }
                if (item.includes("uploadImg")) {
                  response.data.flowInfo.data[item] = [];
                }
                if (item.includes("date")) {
                  response.data.flowInfo.data[item] = this.$format(
                    new Date().getTime(),
                    "yyyy-MM-dd"
                  );
                }
                if (item.includes("time")) {
                  response.data.flowInfo.data[item] = this.$format(
                    new Date().getTime(),
                    "HH:mm:ss"
                  );
                }
              }
              this.$nextTick(() => {
                setTimeout(() => {
                  this.$refs.generateForm.setData(response.data.flowInfo.data);
                }, 500);
              });
            } else {
              this.$refs.generateForm.setData(response.data.flowInfo.data);
            }
          });
          //是否驳回后回到驳回节点
          this.specifiedNode = response.data.currentNodeInfo.specifiedNode;
          //获取其他页面展示信息
          this.processNumber = response.data.flowInfo.processNumber;
          this.companyName = response.data.flowInfo.companyName;
          // this.jsonData = JSON.parse(response.data.flowInfo.dataJson);
          this.urgency = response.data.flowInfo.urgency;
          this.radio = parseInt(this.urgency);
          this.theme = response.data.flowInfo.theme;
          this.createName = response.data.flowInfo.createName;
          this.flowStartStatus = response.data.flowInfo.userStatus;
          // this.templateName = response.data.flowInfo.templateName;
          this.examineAndApproveRecord = response.data.examineAndApproveRecord;
          this.currentNodeName = response.data.currentNodeInfo.currentNode;
          this.currentCandidateUsers =
            response.data.currentNodeInfo.currentCandidateUsers;
          this.currentCandidateGroups =
            response.data.currentNodeInfo.currentCandidateGroups;
          this.approveAlreadyPass = response.data.approveAlreadyPass;

          this.formId = response.data.flowInfo.formId;

          this.formName = response.data.flowInfo.formName;
          this.procKey = response.data.flowInfo.procKey;
          if (this.$route.query.isCopy) {
            this.businessId = null;
          }
          //渲染流程图
          if (this.currentStatus == "5") {
            //根据表单id找流程key
            this.formDetail(this.formId);
            // //流程
            // var flowId = response.data.oaProcessTemplate.flowId;
            // var flowName = response.data.oaProcessTemplate.flowName;
            // localStorage.setItem(
            //   "VUE_APP_BASE_API",
            //   process.env.VUE_APP_BASE_API
            // );
            // //获取查看的图
            // this.modelerUrl =
            //   "/bpmnjs/processFormindex.html?type=editBpmn&deploymentFileUUID=" +
            //   flowId +
            //   "&deploymentName=" +
            //   encodeURI(flowName);
          } else {
            if (
              response.data.flowInfo.status != 5 &&
              !this.$route.query.isCopy
            ) {
              this.checkTheSchedule(response.data.flowInfo);
            }
          }
        }
      });
    },
    /** 进度查看 */
    checkTheSchedule(item) {
      getDefinitionsByInstanceId(item.instanceId).then((response) => {
        let data = response.data;
        // this.url = '/bpmnjs/index.html?type=lookBpmn&deploymentFileUUID='+data.deploymentID+'&deploymentName='+ encodeURI(data.resourceName);
        localStorage.setItem("VUE_APP_BASE_API", process.env.VUE_APP_BASE_API);
        console.log("8909");
        this.modelerUrl =
          "/bpmnjs/viewer.html?type=lookBpmn&instanceId=" +
          item.instanceId +
          "&deploymentFileUUID=" +
          data.deploymentID +
          "&deploymentName=" +
          encodeURI(data.resourceName);
        // this.modelVisible = true;
      });
    },
    //获得用户信息
    getUser() {
      getUserProfile().then((response) => {
        this.user = response.data;
        this.roleGroup = response.roleGroup;
        this.postGroup = response.postGroup;
      });
    },

    /** 新增按钮操作 */
    handleExit() {
      const visitedViews = this.$store.state.tagsView.visitedViews;
      this.$store.state.tagsView.visitedViews = visitedViews.filter((v) => {
        //返回上一页
        // this.$router.go(-1);
        return v.path !== this.$route.path;
      });
      this.$router.push("/oaWork/initiatingProcess");
      sessionStorage.removeItem("addProductData");
      sessionStorage.removeItem("editProductData");
      sessionStorage.removeItem("delProductData");
      sessionStorage.removeItem("addCompanyData");
      sessionStorage.removeItem("editCompanyData");
      sessionStorage.removeItem("delCompanyData");
      sessionStorage.removeItem("editProData");
      sessionStorage.removeItem("delProData");
      if (this.$route.query.addLxProject && this.formLxProAdd.uuId) {
        deleteTemporarily({ addUuid: this.formLxProAdd.uuId });
      }
    },
    newFlow(row) {
      this.nextFlowApproveUserName = row.userName;
    },
    //标签页选择
    handleClick(tab, event) { },
    addDefaultValue() {
      if (
        this.$route.query.themeTypeXzSupervise ||
        this.$route.query.themeTypeYySupervise ||
        this.$route.query.themeTypeLrSupervise
      ) {
        let cooperationProject = this.dataListSupervise.map(
          (item) => item.cooperationProject
        );
        cooperationProject = [...new Set(cooperationProject)].filter(
          (item) => item != null
        );
        let cooperationCompany = this.dataListSupervise.map(
          (item) => item.cooperationCompany
        );
        cooperationCompany = [...new Set(cooperationCompany)].filter(
          (item) => item != null
        );
        let cooperationProjectName = this.dataListSupervise.map(
          (item) => item.cooperationProjectName
        );
        this.projectNamesSupervise = [...new Set(cooperationProjectName)];
        let cooperationCompanyName = this.dataListSupervise.map(
          (item) => item.cooperationCompanyName
        );
        this.companyNamesSupervise = [...new Set(cooperationCompanyName)];

        this.jsonData.list.forEach((item) => {
          if (cooperationProject.length) {
            if (item.model == "cooperationProjectName") {
              item.options.defaultValue = cooperationProject;
            }
          }
          if (cooperationCompany.length) {
            if (item.model == "cooperationCompanyName") {
              item.options.defaultValue = cooperationCompany;
            }
          }
        });
        const count = this.$route.query.count.toString().padStart(2, "0");
        let time = this.$format(new Date().getTime(), "yyyyMMdd");
        this.theme = `提供监管报送${this.$route.query.themeTypeYySupervise ? "用印" : ""
          }资料:${this.companyNamesSupervise && this.companyNamesSupervise.length
            ? this.companyNamesSupervise + "——"
            : ""
          }${this.projectNamesSupervise && this.projectNamesSupervise.length
            ? this.projectNamesSupervise + "——"
            : ""
          }${time}${count}`;
      }
    },
    //
    templateDetail() {
      if (this.templateId == null) {
        this.$modal.msgError("出现错误数据");
      } else {
        getTemplate(this.templateId, this.currentStatus).then((response) => {
          console.log("查询模板相亲templateId====" + this.templateId);
          //获取到的各种信息在这里放着
          this.templateObj = response.data.oaProcessTemplate;
          this.templateName = response.data.oaProcessTemplate.templateName;
          this.templateType = response.data.oaProcessTemplate.templateType;
          this.isEnable = response.data.oaProcessTemplate.isEnable;
          this.editFlag = response.data.oaProcessTemplate.editFlag;

          //获取通知方式的显示
          for (
            let i = 0;
            i <
            response.data.oaProcessTemplate.oaProcessTemplateNotificationList
              .length;
            i++
          ) {
            if (
              response.data.oaProcessTemplate.oaProcessTemplateNotificationList[
                i
              ].notificationType == 0 &&
              response.data.oaProcessTemplate.oaProcessTemplateNotificationList[
                i
              ].processEnable == 0
            ) {
              //通知方式为微信
              this.checked = true;
            }
            if (
              response.data.oaProcessTemplate.oaProcessTemplateNotificationList[
                i
              ].notificationType == 1 &&
              response.data.oaProcessTemplate.oaProcessTemplateNotificationList[
                i
              ].processEnable == 0
            ) {
              //通知方式为微信
              this.checked2 = true;
            }
          }

          // //表单
          // if (!this.$route.query.isCopy) {
          //   this.jsonData = JSON.parse(response.data.defination);
          // }
          this.jsonData = JSON.parse(response.data.defination);
          this.addDefaultValue();
          //表单id  `
          this.formId = response.data.oaProcessTemplate.formId;

          this.flowFullId = response.data.oaProcessTemplate.flowFullId;

          //根据表单id找流程key
          this.formDetail(this.formId);
          //流程
          var flowId = response.data.oaProcessTemplate.flowId;

          this.newFromId = response.data.oaProcessTemplate.formId;
          console.log("最新模板表单Id" + this.newFromId);
          // if (response.data.flowInfo.newFromId) {
          //   console.log("入了新的");
          //   this.newFromId = response.data.flowInfo.newFromId;
          // } else {
          //   console.log("入了旧的的");
          //   this.newFromId = response.data.flowInfo.formId;
          //   console.log("入了旧的的" + this.newFromId);
          // }
          var flowName = response.data.oaProcessTemplate.flowName;
          localStorage.setItem(
            "VUE_APP_BASE_API",
            process.env.VUE_APP_BASE_API
          );
          console.log("123");
          //获取查看的图
          this.modelerUrl =
            "/bpmnjs/processFormindex.html?type=editBpmn&deploymentFileUUID=" +
            flowId +
            "&deploymentName=" +
            encodeURI(flowName);
        });
      }
    },
    //根据表单id找flow关联的key
    formDetail(formId) {
      getFormProcKey(formId).then((response) => {
        this.formObj = response.data;
        this.refProcKey = response.data.refProcKey;
      });
    },
    confirmChange(b) {
      let message = b ? "开启" : "关闭";
      this.$modal
        .confirm("确认是否" + message + "自定义生成凭证时间")
        .then()
        .catch(() => {
          this.customFlag = !b;
        });
    },
    //修改启用状态
    updateinEnable() {
      var enable = this.isEnable;
      var templateId = this.templateId;
      let text = this.isEnable === "Y" ? "启用" : "停用";
      this.$modal
        .confirm("确认要" + text + '"' + this.templateName + '"模板吗？')
        .then(function () {
          return changeenableStatus(templateId, enable);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(() => {
          this.isEnable = enable === "Y" ? "N" : "Y";
        });
      // this.isEnable = enable;
    },
    //修改通知方式里的对本流程启用状态
    updateNotificationProcessEnable(row) {
      let text = row.isEnable === "0" ? "启用" : "停用";
      let notification = row.notificationType === "0" ? "企业微信" : "邮件";
      this.$modal
        .confirm("确认要" + text + '"' + notification + '"通知吗？')
        .then(function () {
          return changeNotificationProcessEnable(row.id, row.processEnable);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.processEnable = row.processEnable === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.selectOpen = false;
      this.selectListNotObj = [];
      this.templateObj.selectList = [];
      this.selectObjList = [];
      // this.open = false;
      // this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        dictName: null,
        dictType: null,
        isEnable: null,
        endUpdateTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    async goStartFlow(subData) {
      if (this.$route.query.themeTypeLr) {
        let list = this.dataList.map((item) => item.id);
        commitInformations(list).then((res) => {
          if (res.code == 200) {
            this.startFlow(subData);
          }
        });
      } else if (this.$route.query.perEntry) {
        commitOnboardings(this.formPerEntry.id).then((res) => {
          if (res.code == 200) {
            this.startFlow(subData);
          }
        });
      } else if (this.$route.query.perResign) {
        commitResignation(this.formPerResign.id).then((res) => {
          if (res.code == 200) {
            this.startFlow(subData);
          }
        });
      } else if (this.$route.query.themeTypeLrSupervise) {
        let list = this.dataListSupervise.map((item) => item.id);
        commitInformationsSupervise(list).then((res) => {
          if (res.code == 200) {
            this.startFlow(subData);
          }
        });
      } else {
        this.startFlow(subData);
      }
    },
    certificateProcessTo(response, isSave = false) {
      this.certificateAllProcessTo(response, isSave);
    },
    async certificateAllProcessTo(response, isSave) {
      if (
        (sessionStorage.getItem("oa-allLicensesTable") &&
          this.tableCerAllLienses.length > 0 &&
          this.$route.query.cerAllLicenses) ||
        (this.$route.query.oid && this.tableCerAllLienses.length > 0)
      ) {
        const data = await this.$refs.generateForm.getData();
        let licenseIdList = this.tableCerAllLienses.map((item) => item.id);
        if (this.$route.query.oid && this.tableCerAllLienses.length > 0) {
          licenseIdList = this.tableCerAllLienses.map((item) => item.licenseId);
        }
        const params = {
          processId: response.data,
          licenseIdList,
        };
        processLicenseProcess(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-allLicensesTable");
            const params = {
              formId: this.formId,
              businessId: response.data,
              licenseIdList,
              themes: this.theme,
              formJosnString: JSON.stringify(data),
              buttonType: isSave ? "BC" : "TJ",
            };
            pendingDetailPost(params);
          }
        });
      }
    },
    async officeProcessTo(response) {
      if (this.$route.query.officeSuppliesType) {
        var data = null
        if (this.officeSuppliesData.id) {
          await receiveMainEdit(this.officeSuppliesData);
        } else {
          data = await receiveMainAdd(this.officeSuppliesData)
        }

        receiveMainEdit({
          ...this.officeSuppliesData,
          approvalStatus: 1,
          id: this.officeSuppliesData.id ? this.officeSuppliesData.id : data.data.id,
          processId: response.data,
          offReceivePurchaseDetail: {
            jsonData: JSON.stringify(this.officeSuppliesData),
          },
          companyId: this.$route.query.companyId,
        }).then((res) => {
          sessionStorage.removeItem("officeSupplies");
        });
      }
      if (this.$route.query.officePurchaseType) {
        if (this.officePurchaseData.length == 1) {
          var data2 = null
          if (this.officePurchaseData[0].id) {
            data2 = await supplyPurchaseEdit({
              ...this.officePurchaseData[0],
              status: 0,
              totalPayable: this.officePurchaseData[0].totalPrice
            })
          } else {
            data2 = await supplyPurchase({
              ...this.officePurchaseData[0],
              status: 0,
              totalPayable: this.officePurchaseData[0].totalPrice
            })
          }
          updateInfoPur({
            ids: [data2.data.id],
            status: 1,
            processId: response.data,
            offReceivePurchaseDetail: {
              jsonData: JSON.stringify({
                list: this.officePurchaseData, ids: [data2.data.id],
                status: 1,
                processId: response.data, companyId: this.$route.query.companyId
              }),
            },
            companyId: this.$route.query.companyId,
          }).then((res) => {
            sessionStorage.removeItem("officePurchase");
          });
        } else {
          updateInfoPur({
            ids: this.officePurchaseData.map((item) => item.id),
            status: 1,
            processId: response.data,
            offReceivePurchaseDetail: {
              jsonData: JSON.stringify({
                list: this.officePurchaseData, ids: this.officePurchaseData.map((item) => item.id),
                status: 1,
                processId: response.data, companyId: this.$route.query.companyId
              }),
            },
            companyId: this.$route.query.companyId,
          }).then((res) => {
            sessionStorage.removeItem("officePurchase");
          });
        }

      }
    },
    paymentProcessTo(response) {
      if (this.$route.query.addPayment) {
        addNewEditInfoTrader({
          ...this.formAddPayment,
          oaApplyRecordsOldData: null,
          oaApplyRecordsNewData: JSON.stringify(this.formAddPayment),
          processId: response.data,
          editInfo: this.projectText,
          editType: 0,
        }).then((res) => {
          sessionStorage.removeItem("addPaymentData");
        });
      }
      if (this.$route.query.editPayment) {
        addNewEditInfoTrader({
          ...this.formEditPayment.newData,
          oaApplyRecordsOldData: JSON.stringify(this.formEditPayment.oldData),
          oaApplyRecordsNewData: JSON.stringify(this.formEditPayment.newData),
          processId: response.data,
          editInfo: this.projectText,
          editType: 1,
        }).then((res) => {
          sessionStorage.removeItem("editPaymentData");
        });
      }
      if (this.$route.query.delPayment) {
        addNewEditInfoTrader({
          ...this.formDelPayment,
          oaApplyRecordsOldData: JSON.stringify(this.formDelPayment.oldData),
          oaApplyRecordsNewData: null,
          processId: response.data,
          editInfo: this.projectText,
          editType: 2,
        }).then((res) => {
          sessionStorage.removeItem("delPaymentData");
        });
      }
    },
    editCollecPayProcessTo(response) {
      if (this.$route.query.editCollecPay) {
        let data = {
          applyId:
            this.formEditCollecPay.newData
              .oaProjectDeployReceiptAndPaymentInfoVoList[0].oaProjectDeployId,
          applyType: 7,
          processId: response.data,
          operation: this.formEditCollecPay.newData.editFlag,
          oaApplyRecordsOldData: JSON.stringify(this.formEditCollecPay.oldData),
          oaApplyRecordsNewData: JSON.stringify(this.formEditCollecPay.newData),
          oaApplyRecordsThirdData: JSON.stringify({
            ...this.formEditCollecPay.editList,
            projectName: this.formEditCollecPay.projectName,
          }),
          editInfo: this.projectText,
        };
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("editCollecPayData");
          }
        });
        return;
      }
      if (this.$route.query.editInformation) {
        let data = {
          applyId: this.formEditInformation.newData.id,
          applyType: 8,
          processId: response.data,
          oaApplyRecordsThirdData: JSON.stringify(this.formEditInformation),
          operation: !this.formEditInformation.oldData.cwProjectFeeFlag ? 0 : 1,
          oaApplyRecordsOldData: JSON.stringify(
            this.formEditInformation.oldData
          ),
          oaApplyRecordsNewData: JSON.stringify(
            this.formEditInformation.newData
          ),
          editInfo: this.projectText,
        };
        if (
          this.formEditInformation.newData.oaProjectDeployCwProjectFeeInfoList
            .length == 0
        ) {
          data.operation = 2;
        }
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("editInformationData");
          }
        });
      }
    },
    companyProcessTo(response) {
      if (this.$route.query.addCompany) {
        let data = {
          applyType: 3,
          processId: response.data,
          operation: 0,
          oaApplyRecordsNewData: JSON.stringify(this.formCompanyAdd),
          editInfo: this.projectText,
        };
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("addCompanyData");
          }
        });
        return;
      }
      if (this.$route.query.editCompany) {
        let data = {
          applyId: this.formCompanyEdit.newData.id,
          applyType: 3,
          processId: response.data,
          operation: 1,
          oaApplyRecordsOldData: JSON.stringify(this.formCompanyEdit.oldData),
          oaApplyRecordsNewData: JSON.stringify(this.formCompanyEdit.newData),
          editInfo: this.projectText,
        };
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("editCompanyData");
          }
        });
        return;
      }
      if (this.$route.query.delCompany) {
        let data = {
          applyId: this.formCompanyDel.id,
          applyType: 3,
          processId: response.data,
          operation: 2,
          oaApplyRecordsNewData: JSON.stringify(this.formCompanyDel),
          editInfo: this.projectText,
        };
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("delCompanyData");
          }
        });
        return;
      }
    },
    productProcessTo(response) {
      if (this.$route.query.addProduct) {
        let data = {
          applyType: 2,
          processId: response.data,
          operation: 0,
          oaApplyRecordsNewData: JSON.stringify(this.formProductAdd),
          editInfo: this.projectText,
        };
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("addProductData");
          }
        });
        return;
      }
      if (this.$route.query.editProduct) {
        let data = {
          applyId: this.formProductEdit.newData.id,
          applyType: 2,
          processId: response.data,
          operation: 1,
          oaApplyRecordsOldData: JSON.stringify(this.formProductEdit.oldData),
          oaApplyRecordsNewData: JSON.stringify(this.formProductEdit.newData),
          editInfo: this.projectText,
        };
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("editProductData");
          }
        });
        return;
      }
      if (this.$route.query.delProduct) {
        let data = {
          applyId: this.formProductDel.id,
          applyType: 2,
          processId: response.data,
          operation: 2,
          oaApplyRecordsNewData: JSON.stringify(this.formProductDel),
          editInfo: this.projectText,
        };
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("delProductData");
          }
        });
        return;
      }
    },

    lxProjectProcessTo(response) {
      if (this.$route.query.addLxProject) {
        console.log(this.formLxProAdd, "-");
        let data = {
          projectForm: this.formLxProAdd.projectForm,
          userform: this.formLxProAdd.userform,
        };

        lxProjectAdd({ ...data }).then((res) => {
          let data = {
            flowId: response.data,
            projectId: res.data.projectId,
            flowClassify: 6,
            applicantId: sessionStorage.getItem("userId"),
            newJsonDate: JSON.stringify(this.formLxProAdd),
            remark: this.projectText,
          };
          let params = {
            companyIdList: [],
            deployIdList: [],
            addUuid: this.formLxProAdd.uuId,
          };
          console.log(this.formLxProAdd);
          if (this.formLxProAdd.addCompanyList.length > 0) {
            params.companyIdList = this.formLxProAdd.addCompanyList.map(
              (item) => item.id
            );
          }
          if (this.formLxProAdd.addProject && this.formLxProAdd.addProject.id) {
            params.deployIdList = [this.formLxProAdd.addProject.id];
          }
          data.addTemporarilyVo = params;
          relationAdd({ ...data });
          sessionStorage.removeItem("addLxProData");
        });
      }
      if (this.$route.query.delayLxProject) {
        let data = {
          flowId: response.data,
          projectId: this.formLxProDelay.projectForm.id,
          flowClassify: 8,
          applicantId: sessionStorage.getItem("userId"),
          newJsonDate: JSON.stringify(this.formLxProDelay),
          postponeDay: this.formLxProDelay.day,
          remark: this.projectText,
        };
        relationAdd({ ...data });
        sessionStorage.removeItem("delayLxProData");
      }
      if (this.$route.query.termLxProject) {
        let data = {
          flowId: response.data,
          projectId: this.formLxProTerm.projectForm.id,
          flowClassify: 9,
          applicantId: sessionStorage.getItem("userId"),
          newJsonDate: JSON.stringify(this.formLxProTerm),
          remark: this.projectText,
        };
        relationAdd({ ...data });
        sessionStorage.removeItem("tremLxProData");
      }
      if (this.$route.query.editLxProject) {
        console.log(this.formLxProEdit);

        let data = {
          flowId: response.data,
          projectId: this.formLxProEdit.newData.projectForm.id,
          flowClassify: 10,
          applicantId: sessionStorage.getItem("userId"),
          newJsonDate: JSON.stringify(this.formLxProEdit.newData),
          oldJsonDate: JSON.stringify(this.formLxProEdit.oldData),
          remark: this.projectText,
        };

        relationAdd({ ...data });
        sessionStorage.removeItem("editLxProData");
      }
      if (this.$route.query.claimLxProject) {
        console.log(this.formLxProClaim);

        let data = {
          flowId: response.data,
          projectId: this.formLxProClaim.projectForm.id,
          flowClassify: 7,
          applicantId: sessionStorage.getItem("userId"),
          newJsonDate: JSON.stringify(this.formLxProClaim),
          remark: this.projectText,
        };

        relationAdd({ ...data });
        sessionStorage.removeItem("claimLxProData");
      }
      if (this.$route.query.editLxProjectName) {
        let data = {
          flowId: response.data,
          projectId: this.formLxProjectNameEdit.detail.projectForm.id,
          flowClassify: 11,
          applicantId: sessionStorage.getItem("userId"),
          newJsonDate: JSON.stringify(this.formLxProjectNameEdit.newData),
          oldJsonDate: JSON.stringify(this.formLxProjectNameEdit.oldData),
          remark: this.projectText,
        };

        relationAdd({ ...data });
        let data2 = {
          applyId: this.formLxProjectNameEdit.newData.id,
          applyType: 1,
          processId: response.data,
          operation: 1,
          oaApplyRecordsOldData: JSON.stringify(
            this.formLxProjectNameEdit.oldData
          ),
          oaApplyRecordsNewData: JSON.stringify(
            this.formLxProjectNameEdit.newData
          ),
          editInfo: this.projectText || "-",
        };
        startProd({ ...data2 }).then((res) => {
          if (res.code == 200) {
          }
        });
        sessionStorage.removeItem("editLxProjectNameData");
      }
    },
    projectProcessTo(response) {
      if (this.$route.query.editProject) {
        let data = {
          applyId: this.formProEdit.newData.id,
          applyType: 1,
          processId: response.data,
          operation: 1,
          oaApplyRecordsOldData: JSON.stringify(this.formProEdit.oldData),
          oaApplyRecordsNewData: JSON.stringify(this.formProEdit.newData),
          editInfo: this.projectText,
        };
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("editProData");
          }
        });
        return;
      }
      if (this.$route.query.delProject) {
        let data = {
          applyId: this.formProDel.id,
          applyType: 1,
          processId: response.data,
          operation: 2,
          oaApplyRecordsNewData: JSON.stringify(this.formProDel),
          editInfo: this.projectText,
        };
        startProd({ ...data }).then((res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("delProData");
          }
        });
        return;
      }
    },
    perAppraisalProcessTo(response, isSave = false) {
      this.annualPlanReviewProcessTo(response, isSave);
      this.assessmentConfigurationProcessTo(response, isSave);
      this.assessmentConfigurationProcessToFinish(response, isSave);
      this.projectPerformanceProcessTo(response, isSave);
      this.projectPerformanceProcessToFinish(response, isSave);
      this.assessmentResultsProcessTo(response, isSave);
    },
    badSystemProcessTo(response, isSave = false) {
      this.organizationalManagementProcessTo(response, isSave);
      this.outsourcedProcessTo(response, isSave);
      this.channelBusinessProcessTo(response, isSave);
      this.financialSettlementProcessTo(response, isSave);
    },
    checkWorkProcessTo(response, isSave = false) {
      this.checkWorkLeaveProcessTo(response, isSave);
      this.checkWorkOverTimeProcessTo(response, isSave);
      this.checkWorkBonusPenaltyProcessTo(response, isSave);
      this.checkWorkGoErrandProcessTo(response, isSave);
    },
    async checkWorkLeaveProcessTo(response, isSave) {
      if (
        (sessionStorage.getItem("oa-checkWorkLeaveTable") &&
          Object.keys(this.tableCheckWorkLeave).length > 0 &&
          this.$route.query.checkWorkLeave) ||
        (this.$route.query.oid &&
          Object.keys(this.tableCheckWorkLeave).length > 0)
      ) {
        const id = this.tableCheckWorkLeave.id;
        const params = {
          processId: response.data,
          id,
        };
        if (isSave) {
          updateLeave(params).then(async (res) => {
            if (res.code == 200) {
              sessionStorage.removeItem("oa-checkWorkLeaveTable");
            }
          });
        } else {
          commitAskLeave(params).then(async (res) => {
            if (res.code == 200) {
              sessionStorage.removeItem("oa-checkWorkLeaveTable");
            }
          });
        }
      }
    },
    async superviseProcessTo(response) {
      if (
        sessionStorage.getItem("dataListSupervise") &&
        this.dataListSupervise.length > 0 &&
        this.$route.query.themeTypeXzSupervise
      ) {
        let list = this.dataListSupervise.map((item) => item.id);
        await batchInformationDownloadProcessSupervise({
          processId: response.data,
          infoIds: list,
        });
        sessionStorage.removeItem("dataListSupervise");
      }
      if (
        sessionStorage.getItem("dataListSupervise") &&
        this.dataListSupervise.length > 0 &&
        this.$route.query.themeTypeLrSupervise
      ) {
        let list = this.dataListSupervise.map((item) => item.id);
        await batchInformationProcessSupervise({
          processId: response.data,
          infoIds: list,
        });
        sessionStorage.removeItem("dataListSupervise");
      }
      if (
        sessionStorage.getItem("dataListSupervise") &&
        this.dataListSupervise.length > 0 &&
        this.$route.query.themeTypeYySupervise
      ) {
        let list = this.dataListSupervise.map((item) => item.id);
        await batchInformationUseProcessSupervise({
          processId: response.data,
          infoIds: list,
        });
        sessionStorage.removeItem("dataListSupervise");
      }
    },
    personnelProcessTo(response, isSave = false) {
      this.personnelEntryProcessTo(response, isSave);
      this.personnelBecomeProcessTo(response, isSave);
      this.personnelTransferProcessTo(response, isSave);
      this.personnelResignProcessTo(response, isSave);
    },
    async personnelEntryProcessTo(response, isSave) {
      if (
        (sessionStorage.getItem("oa-entryForm") &&
          Object.keys(this.formPerEntry).length > 0 &&
          this.$route.query.perEntry) ||
        (this.$route.query.oid && Object.keys(this.formPerEntry).length > 0)
      ) {
        const params = {
          processType: 1,
          correlationId: this.formPerEntry.id,
          processName: this.theme,
          processId: response.data,
          personnelName: this.formPerEntry.name,
          sponsor: this.user.nickName,
          processState: isSave ? 3 : 1,
          id: this.formEntryId,
        };
        personnelProcess(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-entryForm");
          }
        });
      }
    },
    async personnelBecomeProcessTo(response, isSave) {
      if (
        (sessionStorage.getItem("oa-becomeForm") &&
          Object.keys(this.formPerBecome).length > 0 &&
          this.$route.query.perBecome) ||
        (this.$route.query.oid && Object.keys(this.formPerBecome).length > 0)
      ) {
        await updateFormal(this.formPerBecome);
        commitFormalById(this.formPerBecome.id);
        const params = {
          processType: 2,
          correlationId: this.formPerBecome.id,
          processName: this.theme,
          processId: response.data,
          personnelName: this.formPerBecome.name,
          sponsor: this.user.nickName,
          processState: isSave ? 3 : 1,
          id: this.formEntryId,
        };
        personnelProcess(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-becomeForm");
          }
        });
      }
    },
    async personnelTransferProcessTo(response, isSave) {
      if (
        (sessionStorage.getItem("oa-transferForm") &&
          Object.keys(this.formPerTransfer).length > 0 &&
          this.$route.query.perTransfer) ||
        (this.$route.query.oid && Object.keys(this.formPerTransfer).length > 0)
      ) {
        const params = {
          processType: 3,
          correlationId: this.formPerTransfer.transferId,
          processName: this.theme,
          processId: response.data,
          personnelName: this.formPerTransfer.name,
          sponsor: this.user.nickName,
          processState: isSave ? 3 : 1,
          id: this.formEntryId,
          recordId: this.formPerTransfer.recordId,
        };
        personnelProcess(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-transferForm");
          }
        });
      }
    },
    async personnelResignProcessTo(response, isSave) {
      if (
        (sessionStorage.getItem("oa-resignForm") &&
          Object.keys(this.formPerResign).length > 0 &&
          this.$route.query.perResign) ||
        (this.$route.query.oid && Object.keys(this.formPerResign).length > 0)
      ) {
        const params = {
          processType: 5,
          correlationId: this.formPerResign.id,
          processName: this.theme,
          processId: response.data,
          personnelName: this.formPerResign.name || this.formPerResign.showName,
          sponsor: this.user.nickName,
          processState: isSave ? 3 : 1,
          id: this.formEntryId,
        };
        personnelProcess(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-resignForm");
          }
        });
      }
    },
    handlerFlowObj(flowObj) {
      if (Object.keys(this.tableCheckWorkLeave).length) {
        const data = JSON.parse(flowObj.data);
        data.days =
          Number(this.tableCheckWorkLeave.totalTime) > 1 ? true : false;
        flowObj.data = JSON.stringify(data);
      }
      if (Object.keys(this.tableCheckWorkGoErrand).length) {
        const data = JSON.parse(flowObj.data);
        data.days =
          Number(this.tableCheckWorkGoErrand.businessTripTimes) > 1 ? true : false;
        flowObj.data = JSON.stringify(data);
      }
    },
    isCertificateProcessTo(value) {
      if (typeof value.data == "string") return false;
      this.tableConflict = value.data || [];
      let content = ``;
      this.tableConflict.forEach((item) => {
        content += `<div>【${item.licenseName}】;</div>`;
      });
      content +=
        "<div>以上证照在您选择的借阅时间内已被借出，请调整借阅时间</div>";
      if (this.tableConflict.length) {
        this.$alert(content, "警告", {
          confirmButtonText: "确定",
          dangerouslyUseHTMLString: true,
        });
        return this.tableConflict.length;
      }
    },
    addCertific(value) {
      if (
        (sessionStorage.getItem("oa-allLicensesTable") &&
          this.tableCerAllLienses.length > 0 &&
          this.$route.query.cerAllLicenses) ||
        (this.$route.query.oid && this.tableCerAllLienses.length > 0)
      ) {
        let licenseIdList = this.tableCerAllLienses.map((item) => item.id);
        if (this.$route.query.oid && this.tableCerAllLienses.length > 0) {
          licenseIdList = this.tableCerAllLienses.map((item) => item.licenseId);
        }
        value.borrowLicIds = licenseIdList;
      }
    },
    async startFlow(subData) {
      var notificationList = subData.notificationList;
      var wechatNotification = subData.wechatNotification;
      var mailNotification = subData.mailNotification;
      var flowObj = subData.flowObj;
      console.log(this.nextFlowApproveUserName, "this.nextFlowApproveUserName");
      this.$set(
        flowObj,
        "nextFlowApproveUserName",
        this.nextFlowApproveUserName
      );
      // flowObj.nextFlowApproveUserName = this.nextFlowApproveUserName;
      if (this.secondNode != null) {
        flowObj.nodeId = this.secondNode.nodeId;
      }
      this.handlerFlowObj(flowObj);
      this.addCertific(flowObj); //证照流程增加参数
      let meetingpass = false;
      if (
        this.$route.query.metting ||
        (this.$route.query.oid && this.oaModuleType == "hycl")
      ) {
        try {
          let params = clone(this.formMeeting, true);
          delete params.organizationalDept;
          delete params.corganizationalDeptIds;
          delete params.attendUser;
          delete params.meetingReminderWay;
          const { code } = await inspectionTime(params);
          if (code != 200) meetingpass = true;
        } catch (e) {
          meetingpass = true;
        }
      }
      if (meetingpass) {
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.3)",
      });
      checkFeeFlow(flowObj).then((res) => {
        if (res.isok == "D") {
          this.$message.warning("当前申请金额大于信息费金额");
          loading.close();
        } else {
          startFlow(flowObj)
            .then((response) => {
              if (response.code == 200) {
                loading.close();
                //然后获取到发起的流程表单id
                // if (this.templateObj.selectList.length != 0) {
                var procFormDataId = response.data;
                if (
                  (this.$route.query.salaryType && this.salaryData2) ||
                  (this.$route.query.oid && this.salaryData2)
                ) {
                  initiateProcess({
                    processId: response.data,
                    id: this.salaryData2.dataId,
                    effectiveDate: this.salaryData2.effectiveDate,
                  }).then((res) => {
                    if (res.code == 200) {
                      sessionStorage.removeItem("salaryData");
                    }
                  });
                }
                if (
                  sessionStorage.getItem("dataList") &&
                  this.dataList.length > 0 &&
                  (this.$route.query.themeTypeXz ||
                    this.$route.query.themeTypeZzYy)
                ) {
                  informationUser({ name: this.zlhqf });
                  let list = this.dataList.map((item) => item.id);
                  if (this.$route.query.themeTypeXz) {
                    batchInformationDownloadProcess({
                      processId: response.data,
                      infoIds: list,
                    }).then((res) => {
                      if (res.code == 200) {
                        sessionStorage.removeItem("dataList");
                      }
                    });
                  }
                  if (this.$route.query.themeTypeZzYy) {
                    batchInformationdirectUsedProcess({
                      processId: response.data,
                      infoIds: list,
                    }).then((res) => {
                      if (res.code == 200) {
                        sessionStorage.removeItem("dataList");
                      }
                    });
                  }
                }
                if (
                  sessionStorage.getItem("dataList") &&
                  this.dataList.length > 0 &&
                  this.$route.query.themeTypeLr
                ) {
                  let list = this.dataList.map((item) => item.id);
                  batchInformationProcess({
                    processId: response.data,
                    infoIds: list,
                  }).then((res) => {
                    if (res.code == 200) {
                      sessionStorage.removeItem("dataList");
                    }
                  });
                }
                if (
                  sessionStorage.getItem("dataList") &&
                  this.dataList.length > 0 &&
                  this.$route.query.themeTypeYy
                ) {
                  let list = this.dataList.map((item) => item.id);
                  batchInformationUseProcess({
                    processId: response.data,
                    infoIds: list,
                  }).then((res) => {
                    if (res.code == 200) {
                      sessionStorage.removeItem("dataList");
                    }
                  });
                }
                //监管报送资料跳转执行
                this.superviseProcessTo(response);
                sessionStorage.removeItem("ThemeTypeYy");
                sessionStorage.removeItem("ThemeTypeYySupervise");
                //办公物品
                this.officeProcessTo(response);
                //收付款人
                this.paymentProcessTo(response);
                //编辑项目收付款信息跳转执行
                this.editCollecPayProcessTo(response);
                //公司信息跳转执行
                this.companyProcessTo(response);
                //产品信息跳转执行
                this.productProcessTo(response);
                //项目名称跳转执行
                this.projectProcessTo(response);
                //人员模块跳转执行
                this.personnelProcessTo(response);
                //立项管理跳转执行
                this.lxProjectProcessTo(response);
                //证照模块跳转执行
                if (this.isCertificateProcessTo(response)) return; //证照模块如果有在库的不允许提交
                this.certificateProcessTo(response);
                //考勤模块跳转执行
                this.checkWorkProcessTo(response);
                //考核模块跳转执行
                this.perAppraisalProcessTo(response);
                //会议模块跳转
                this.meetingProcessTo(response);
                //不良资产模块跳转
                this.badSystemProcessTo(response);
                var formUser = {
                  templateId: this.templateId,
                  procFormDataId: procFormDataId,
                  userIdList: this.templateObj.selectList,
                };
                //待办通知方式
                wechatNotification.procFormDataId = procFormDataId;
                mailNotification.procFormDataId = procFormDataId;
                if (this.checked == true) {
                  wechatNotification.processEnable = "0";
                }
                if (this.checked2 == true) {
                  mailNotification.processEnable = "0";
                }
                notificationList.push(wechatNotification);
                notificationList.push(mailNotification);
                //组装对象
                var userAndNotification = {
                  user: formUser,
                  notificationList: notificationList,
                };

                //调用新增发起流程的用户列表接口
                flowAddUserListAndNotification(userAndNotification).then(
                  (res) => {
                    this.$modal.msgSuccess("提交成功");
                    let route = {
                      fullPath: "/oaWork/updateProcessForm",
                      name: "UpdateProcessForm",
                      path: "/oaWork/updateProcessForm",
                    };
                    this.closeSelectedTag(route);

                    let route2 = {
                      fullPath: "/oaWork/myActivite",
                      name: "MyActivite",
                      path: "/oaWork/myActivite",
                    };
                    this.closeSelectedTag(route2);
                    setTimeout(() => {
                      this.$router.push({
                        path: "/oaWork/myActivite",
                        query: { tab1: "first", tab2: "first" },
                      });
                    }, 200);

                    //------跳转结束------
                  }
                );
                // }
              } else {
                loading.close();
                this.$modal.msgError("提交失败");
              }
            })
            .catch((err) => {
              loading.close();
            });
        }
      });
    },
    closeSelectedTag(view) {
      console.log(view);
      this.$tab.closePage(view).then(({ visitedViews }) => { });
    },
    /** 提交按钮操作 */
    async handleAdd() {
      if (this.buttonType) {
        this.$message.warning("请勿重复提交");
        return;
      }
      this.buttonType = true;
      setTimeout(() => {
        this.buttonType = false;
      }, 3000);
      if (
        (this.$route.query.editProject ||
          this.$route.query.delProject ||
          this.$route.query.addProduct ||
          this.$route.query.editProduct ||
          this.$route.query.delProduct ||
          this.$route.query.addCompany ||
          this.$route.query.editCompany ||
          this.$route.query.delCompany ||
          this.$route.query.addLxProject ||
          this.$route.query.claimLxProject ||
          this.$route.query.termLxProject ||
          this.$route.query.delayLxProject ||
          this.$route.query.editPayment ||
          this.$route.query.addPayment ||
          this.$route.query.delPayment) &&
        !this.projectText
      ) {
        this.$message.warning("请填写原因说明");
        return;
      }
      if (this.$route.query.themeTypeXz && !this.zlhqf) {
        this.$message.warning("请填写资料获取方");
        return;
      }
      if (this.customFlag && this.voucherDate == null) {
        this.$modal.msgError("自定义凭证生成时间不可为空");
        return;
      }

      if (this.theme == null || this.theme == "") {
        this.$modal.msgError("流程主题不能为空");
      } else {
        //待办通知方式集合
        var notificationList = [];
        var wechatNotification = {
          templateId: this.templateId,
          procFormDataId: null,
          notificationType: "0",
          processEnable: "1",
        };
        var mailNotification = {
          templateId: this.templateId,
          procFormDataId: null,
          notificationType: "1",
          processEnable: "1",
        };

        this.$refs.generateForm.getData().then((data) => {
          console.log("这是提交的表单id" + this.newFromId);
          // this.form.data = JSON.stringify(data);
          var flowObj = {
            theme: this.theme,
            companyId: this.companyId,
            classificationId: this.classificationId,
            templateId: this.templateId,
            //紧急程度
            urgency: this.radio,
            //状态 - 审批中状态
            status: "0",
            procKey: this.refProcKey,
            flowFullId: this.flowFullId,
            formId: this.newFromId,
            formName: this.formObj.name,
            data: JSON.stringify(data),
            customFlag: this.customFlag,
            voucherDate: this.voucherDate,
          };
          //todo 获取流程图的第二个节点信息
          getFlowInfoByFlowFullId({
            flowFullId: this.flowFullId,
            data: JSON.stringify(data),
          })
            .then((resp) => {
              if (resp.code == 200) {
                if (resp.hasOwnProperty("data")) {
                  this.secondNode = resp.data;
                }
              }
              if (!this.businessId || this.$route.query.isCopy) {
                this.subData = {
                  notificationList,
                  wechatNotification,
                  mailNotification,
                  flowObj,
                };
                if (this.secondNode != null) {
                  //todo future 筛选弹窗 ----> 用户
                  if (
                    this.secondNode.assignee ||
                    this.secondNode.candidateUsers.length != 0
                  ) {
                    this.secondNodeForUser = !this.secondNode.assignee
                      ? this.secondNode.candidateUsers
                      : this.secondNode.assignee.split(",");
                    if (this.secondNodeForUser.length == 1) {
                      this.nextFlowApproveUserName = this.secondNodeForUser[0];
                      console.log(this.subData, "this.subData");
                      this.goStartFlow(this.subData);
                    } else {
                      //根据用户名查找用户信息
                      listUser().then((response) => {
                        this.dialogNextUserList = response.rows.filter((t) => {
                          return (
                            this.secondNodeForUser.indexOf(t.userName) !== -1
                          );
                        });
                        this.openNodeUser = true;
                      });
                    }
                  } else {
                    //发起第二步有岗位，那么就弹窗，弹窗内容给岗位筛选过的人员
                    this.secondNodeForPost = this.secondNode.candidateGroups;
                    //如果岗位有一个
                    if (this.secondNodeForPost.length == 1) {
                      getPostListByCode({
                        postCode: this.secondNodeForPost[0],
                      }).then((response) => {
                        this.dialogPostList = response;
                        for (let i = 0; i < this.dialogPostList.length; i++) {
                          this.dialogPostList[i].label =
                            this.dialogPostList[i].postName;
                        }
                        getUserDialogListByPostId({
                          postId: this.dialogPostList[0].postId,
                        }).then((resp) => {
                          this.userDialogList = resp.data;
                          if (resp.data.length == 1) {
                            //该岗位下只有一个用户，则
                            this.nextFlowApproveUserName =
                              resp.data[0].userName;
                            this.goStartFlow(this.subData);
                          } else {
                            //不唯一，则弹框让用户选择
                            this.openNodePost = true;
                          }
                        });
                      });
                    } else {
                      //多岗位，让用户去选择一个岗位去选用户

                      //查找岗位的映射关系  --->  找所有的岗位信息
                      getPostListByCode({
                        postCode: this.secondNodeForPost[0],
                      }).then((response) => {
                        //筛选出存在的所有的岗位信息
                        this.dialogPostList = response;

                        for (let i = 0; i < this.dialogPostList.length; i++) {
                          this.dialogPostList[i].label =
                            this.dialogPostList[i].postName;
                        }
                        if (this.dialogPostList.length == 1) {
                          this.postHandleNodeClick(this.dialogPostList[0]);
                        }
                        this.openNodePost = true;
                      });
                    }
                  }
                } else {
                  //发起第二步没有岗位
                  this.goStartFlow(this.subData);
                }
              }

              if (
                this.businessId != null &&
                this.businessId != "" &&
                !this.$route.query.isCopy
              ) {
                //修改表单的信息、紧急程度、可阅览人和待办通知方式
                var formUser = {
                  templateId: this.templateId,
                  procFormDataId: this.businessId,
                  userIdList: this.selectObjList,
                };
                //待办通知方式
                wechatNotification.procFormDataId = this.businessId;
                mailNotification.procFormDataId = this.businessId;
                if (this.checked == true) {
                  wechatNotification.processEnable = "0";
                }
                if (this.checked2 == true) {
                  mailNotification.processEnable = "0";
                }
                notificationList.push(wechatNotification);
                notificationList.push(mailNotification);
                var formInfo = {
                  oid: this.oid,
                  businessId: this.businessId,
                  theme: this.theme,
                  //状态由被驳回或者草稿状态，改为审核中
                  status: "0",
                  data: JSON.stringify(data),
                  //紧急程度
                  urgency: this.radio,
                  //当前状态，草稿状态的进入页面会有当前状态参数
                  currentStatus: this.currentStatus,
                  formName: this.formName,
                  procKey: this.refProcKey,
                  flowFullId: this.flowFullId,
                };
                this.updateFormInfo = {
                  notificationList,
                  formInfo,
                  formUser,
                };
              }
              if (
                this.isEditFlag &&
                this.secondNode != null &&
                !this.specifiedNode
              ) {
                //todo future 筛选弹窗 ----> 用户
                if (
                  this.secondNode.assignee ||
                  this.secondNode.candidateUsers.length != 0
                ) {
                  this.secondNodeForUser = !this.secondNode.assignee
                    ? this.secondNode.candidateUsers
                    : this.secondNode.assignee.split(",");
                  if (this.secondNodeForUser.length == 1) {
                    this.nextFlowApproveUserName = this.secondNodeForUser[0];
                    this.goUpdateFlowInfo(this.updateFormInfo);
                  } else {
                    //根据用户名查找用户信息
                    listUser().then((response) => {
                      this.dialogNextUserList = response.rows.filter((t) => {
                        return (
                          this.secondNodeForUser.indexOf(t.userName) !== -1
                        );
                      });
                      this.openNodeUser = true;
                    });
                  }
                } else {
                  //发起第二步有岗位，那么就弹窗，弹窗内容给岗位筛选过的人员
                  this.secondNodeForPost = this.secondNode.candidateGroups;
                  //如果岗位有一个
                  if (this.secondNodeForPost.length == 1) {
                    getPostListByCode({
                      postCode: this.secondNodeForPost[0],
                    }).then((res) => {
                      this.dialogPostList = res;
                      for (let i = 0; i < this.dialogPostList.length; i++) {
                        this.dialogPostList[i].label =
                          this.dialogPostList[i].postName;
                      }
                      getUserDialogListByPostId({
                        postId: this.dialogPostList[0].postId,
                      }).then((resp) => {
                        this.userDialogList = resp.data;
                        if (resp.data.length == 1) {
                          //该岗位下只有一个用户，则
                          this.nextFlowApproveUserName = resp.data[0].userName;
                          this.goUpdateFlowInfo(this.updateFormInfo);
                        } else {
                          //不唯一，则弹框让用户选择
                          this.openNodePost = true;
                        }
                      });
                    });
                  } else {
                    //多岗位，让用户去选择一个岗位去选用户

                    //查找岗位的映射关系  --->  找所有的岗位信息
                    getPostListByCode({
                      postCode: this.secondNodeForPost[0],
                    }).then((res) => {
                      this.dialogPostList = res;
                      for (let i = 0; i < this.dialogPostList.length; i++) {
                        this.dialogPostList[i].label =
                          this.dialogPostList[i].postName;
                      }
                      if (this.dialogPostList.length == 1) {
                        this.postHandleNodeClick(this.dialogPostList[0]);
                      }
                      this.openNodePost = true;
                    });
                  }
                }
              } else {
                //发起第二步没有岗位
                this.goUpdateFlowInfo(this.updateFormInfo);
              }
            })
            .catch((e) => { });
        });
      }
    },
    //文件预览，图片和pdf
    handlePreview(file) {
      if (file.hasOwnProperty("fileName")) {
        file.name = file.fileName;
      }
      if (file.name.endsWith(".pdf")) {
        //文件是pdf格式
        getFilesPathMapping().then((resp) => {
          let pdfUrl = resp.msg + (file.url || file.fileUrl);
          window.open(pdfUrl);
          return;
          // 有时PDF文件地址会出现跨域的情况,这里最好处理一下
          this.pdfSrc = pdf.createLoadingTask(
            { url: this.pdfUrl },
            CMapReaderFactory
          );
          this.pdfSrc.promise.then((pdf) => {
            this.numPages = pdf.numPages;
          });
        });
        return;
        this.pdfPreviewDialog = true;
      } else if (
        file.name.endsWith(".jpg") ||
        file.name.endsWith(".jpeg") ||
        file.name.endsWith(".png") ||
        file.name.endsWith(".gif")
      ) {
        //文件是图片格式
        getFilesPathMapping().then((resp) => {
          this.photoUrl = resp.msg + (file.url || file.fileUrl);
          let array = new Set([]);
          array.add(resp.msg + (file.url || file.fileUrl));
          let from = Array.from(array);
          this.imagePreviewUrls = from;
        });
        this.showImgViewer = true;
      } else {
        //文件下载
        this.handleDownload(file);
      }
    },
    handleDownload(file) {
      if (file.hasOwnProperty("fileName")) {
        file.name = file.fileName;
      }
      const url = file.url || file.fileUrl; //图片的https链接
      downloadByUrl({
        url: url,
      }).then((res) => {
        let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
        const link = document.createElement("a"); //创建一个隐藏的a标签
        link.target = "_blank";
        link.href = href; //设置下载的url
        link.download = file.name; //设置下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(href); // 释放掉blob对象
      });
    },
    cerDraft() {
      if (this.oaModuleType == "10") {
        const licenseIdList = this.tableCerAllLienses.map(
          (item) => item.licenseId
        );
        const params = {
          isSign: "6",
          licenseIdList,
        };
        putLicense(params);
      }
    },
    checkWorkDraft(processId) {
      this.checkWorkLeavDraft(processId);
      this.checkWorkOverTimeDraft(processId);
      this.checkWorkBonusPenaltyDraft(processId);
      this.checkWorkGoErrandDraft(processId);
    },
    checkWorkLeavDraft(processId) {
      if (this.oaModuleType == "11") {
        const id = this.tableCheckWorkLeave.id;
        const params = {
          processId,
          id,
        };
        commitAskLeave(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-checkWorkLeaveTable");
          }
        });
      }
    },
    perAppraisalDraft(processId) {
      this.annualPlanReviewDraft(processId);
      this.assessmentConfigurationDraft(processId);
      this.assessmentConfigurationFinishDraft(processId);
      this.projectPerformanceDraft(processId);
      this.projectPerformanceFinishDraft(processId);
      this.assessmentResultsDraft(processId);
    },
    badSystemDraft(processId) {
      this.organizationalManagementDraft(processId);
      this.outsourcedProcessToDraft(processId);
      this.channelBusinessProcessToDraft(processId);
      this.financialSettlementProcessToDraft(processId);
    },
    //提交至接口进行修改
    goUpdateFlowInfo(updateFormInfo) {
      console.log("updateFormInfo为：", updateFormInfo);
      var formInfo = updateFormInfo.formInfo;
      var formUser = updateFormInfo.formUser;
      var notificationList = updateFormInfo.notificationList;
      formInfo.nextFlowApproveUserName = this.nextFlowApproveUserName;
      if (this.secondNode != null) {
        formInfo.nextNodeId = this.secondNode.nodeId;
      }
      this.handlerFlowObjSave(formInfo);
      updateFlowInfo(formInfo).then((resp) => {
        var userAndNotification = {
          user: formUser,
          notificationList: notificationList,
        };
        flowUpdateUserListAndNotification(userAndNotification).then((res) => {
          //证照保存后提交修改
          this.cerDraft();
          //入职保存后提交修改
          this.perDraft();
          //考勤保存后提交修改
          this.checkWorkDraft(formInfo.businessId);
          //考核管理保存后提交修改
          this.perAppraisalDraft(formInfo.businessId);
          //不良资产保存后提交修改
          this.badSystemDraft(formInfo.businessId);
          this.$modal.msgSuccess("修改成功");
          let route = {
            fullPath: "/oaWork/updateProcessForm",
            name: "UpdateProcessForm",
            path: "/oaWork/updateProcessForm",
          };
          this.closeSelectedTag(route);

          let route2 = {
            fullPath: "/oaWork/myActivite",
            name: "MyActivite",
            path: "/oaWork/myActivite",
          };
          this.closeSelectedTag(route2);
          this.$router.push({
            path: "/oaWork/myActivite",
            query: { tab1: "first", tab2: "first" },
          });

          //------跳转结束------
        });
        // this.close();
      });
    },
    perDraft() {
      if (this.currentStatus == "5" && this.oaModuleType == "4") {
        commitOnboardings(this.formPerEntry.id);
      } else if (this.currentStatus == "5" && this.oaModuleType == "5") {
        commitFormalById(this.formPerBecome.id);
      } else if (this.currentStatus == "5" && this.oaModuleType == "6") {
        commitResignation(this.formPerResign.id);
      }
      const data = {
        data: this.formPerson.processId,
      };
      this.personnelProcessTo(data, false);
    },
    handlerFlowObjSave(flowObj) {
      if (Object.keys(this.tableCheckWorkLeave).length) {
        const data = JSON.parse(flowObj.data);
        data.days =
          Number(this.tableCheckWorkLeave.totalTime) > 1 ? true : false;
        flowObj.data = JSON.stringify(data);
      }
      if (Object.keys(this.tableCheckWorkGoErrand).length) {
        const data = JSON.parse(flowObj.data);
        data.days =
          Number(this.tableCheckWorkGoErrand.businessTripTimes) > 1 ? true : false;
        flowObj.data = JSON.stringify(data);
      }
    },
    /** 保存按钮操作 */
    async handleSave() {
      if (this.theme == null || this.theme == "") {
        this.$modal.msgError("流程主题不能为空");
      } else {
        //待办通知方式集合
        var notificationList = [];
        var wechatNotification = {
          templateId: this.templateId,
          procFormDataId: null,
          notificationType: "0",
          processEnable: "1",
        };
        var mailNotification = {
          templateId: this.templateId,
          procFormDataId: null,
          notificationType: "1",
          processEnable: "1",
        };
        this.$refs.generateForm
          .getData()
          .then((data) => {
            var flowObj;
            // this.form.data = JSON.stringify(data);
            if (
              this.oid == null ||
              this.oid == "" ||
              this.$route.query.isCopy
            ) {
              flowObj = {
                theme: this.theme,
                companyId: this.companyId,
                classificationId: this.classificationId,
                templateId: this.templateId,
                //紧急程度
                urgency: this.radio,
                //状态 - 草稿状态
                status: "5",
                procKey: this.refProcKey,
                flowFullId: this.flowFullId,
                formId: this.newFromId,
                formName: this.formObj.name,
                data: JSON.stringify(data),
              };
            } else {
              flowObj = {
                oid: this.oid,
                theme: this.theme,
                companyId: this.companyId,
                classificationId: this.classificationId,
                templateId: this.templateId,
                //紧急程度
                urgency: this.radio,
                //状态 - 草稿状态
                status: "5",
                procKey: this.refProcKey,
                flowFullId: this.flowFullId,
                formId: this.newFromId,
                formName: this.formObj.name,
                data: JSON.stringify(data),
              };
            }
            this.handlerFlowObjSave(flowObj);
            this.addCertific(flowObj); //证照流程增加参数
            saveData(flowObj).then((response) => {
              if (response.code == 200) {
                if (this.isCertificateProcessTo(response)) return; //证照模块如果有在库的不允许提交
                if (
                  (this.$route.query.salaryType && this.salaryData2) ||
                  (this.$route.query.oid && this.salaryData2)
                ) {
                  initiateProcess({
                    processId: response.data,
                    id: this.salaryData2.dataId,
                    effectiveDate: this.salaryData2.effectiveDate,
                  }).then((res) => {
                    if (res.code == 200) {
                      sessionStorage.removeItem("salaryData");
                    }
                  });
                }
                //然后获取到发起的流程表单id
                // if (this.templateObj.selectList.length != 0) {
                var procFormDataId = response.data;
                var formUser = {
                  templateId: this.templateId,
                  procFormDataId: procFormDataId,
                  userIdList: this.templateObj.selectList,
                };
                //待办通知方式
                wechatNotification.procFormDataId = procFormDataId;
                mailNotification.procFormDataId = procFormDataId;
                if (this.checked == true) {
                  wechatNotification.processEnable = "0";
                }
                if (this.checked2 == true) {
                  mailNotification.processEnable = "0";
                }
                notificationList.push(wechatNotification);
                notificationList.push(mailNotification);
                //组装对象
                var userAndNotification = {
                  user: formUser,
                  notificationList: notificationList,
                };
                //调用新增发起流程的用户列表接口
                flowAddUserListAndNotification(userAndNotification).then(
                  (res) => {
                    this.$modal.msgSuccess("提交成功");
                    //成功后跳转我的流程
                    let route = {
                      fullPath: "/oaWork/updateProcessForm",
                      name: "UpdateProcessForm",
                      path: "/oaWork/updateProcessForm",
                    };
                    this.closeSelectedTag(route);
                    let route2 = {
                      fullPath: "/oaWork/myActivite",
                      name: "MyActivite",
                      path: "/oaWork/myActivite",
                    };
                    this.closeSelectedTag(route2);
                    //人事模块调用
                    this.savePerson(response);
                    this.saveCertific(response);
                    this.saveCheckWork(response); //考勤模块调用
                    this.savePerAppraisal(response); //考核模块调用
                    this.saveBadSystem(response); //不良资产模块调用
                    setTimeout(() => {
                      this.$router.push({
                        path: "/oaWork/myActivite",
                        query: { tab1: "first", tab2: "third" },
                      });
                    }, 200);

                    //------跳转结束------
                  }
                );
                // }
              } else {
                this.$modal.msgError("提交失败");
              }
              this.close();
            });
          })
          .catch((e) => { });
        if (this.$route.query.addLxProject && this.formLxProAdd.uuId) {
          deleteTemporarily({ addUuid: this.formLxProAdd.uuId });
        }
      }
    },
    savePerson(response) {
      this.personnelProcessTo(response, true);
    },
    saveCertific(response) {
      this.certificateProcessTo(response, true);
    },
    saveCheckWork(response) {
      this.checkWorkProcessTo(response, true);
    },
    savePerAppraisal(response) {
      this.perAppraisalProcessTo(response, true);
    },
    saveBadSystem(response) {
      this.badSystemProcessTo(response, true);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getTemplate(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【请填写功能名称】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTemplate(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTemplate(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除【请填写功能名称】编号为"' + ids + '"的数据项？')
        .then(function () {
          return delTemplate(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    //打开添加用户的对话框
    handleAddIntoSelectListDialog() {
      if (this.businessId == null || this.businessId == "") {
        if (
          this.templateObj.selectList.length == 0 &&
          this.selectListNotObj.length != 0
        ) {
          this.selectListNotObj = [];
        }
        //查询所有角色接口
        var userDateRange = [];
        var userQueryParams = {
          userName: undefined,
          phonenumber: undefined,
          custNo: undefined,
          status: undefined,
          deptId: undefined,
        };
        var template = {
          templateId: this.templateId,
        };
        listUserForOaProcessTemplateFlow(template).then((response) => {
          //进行两个list的对比，   补充原来数据checkBox的选中情况
          if (this.templateObj.selectList.length != 0) {
            var selectListObjNew = [];
            for (let i = 0; i < this.templateObj.selectList.length; i++) {
              var filter = this.selectListNotObj.filter(
                (t) => t.userId == this.templateObj.selectList[i]
              );
              selectListObjNew.push(filter[0]);
              //找用户集合中跟复选框一样的用户id给他的选择置成true
              var userId = filter[0].userId;
              for (let j = 0; j < response.rows.length; j++) {
                if (response.rows[j].userId == userId) {
                  response.rows[j].selectFlag = true;
                }
              }
            }

            // selectListObjNew.push(filter[0]);
            //复选框中的值改变，下拉框改变
            this.selectListNotObj = selectListObjNew;
          }

          this.dialogUserList = response.rows;
          this.dialogUserListBefore = response.rows;
          if (this.dialogUserNickNameList.length == 0) {
            for (let i = 0; i < this.dialogUserList.length; i++) {
              var a = {
                value: this.dialogUserList[i].nickName,
                userId: this.dialogUserList[i].userId,
              };
              this.dialogUserNickNameList.push(a);
            }
          }
        });
      }
      if (this.businessId != null && this.businessId != "") {
        if (
          this.selectObjList.length == 0 &&
          this.selectListNotObj.length != 0
        ) {
          this.selectListNotObj = [];
        }
        //查询所有角色接口
        var userDateRange = [];
        var userQueryParams = {
          userName: undefined,
          phonenumber: undefined,
          custNo: undefined,
          status: undefined,
          deptId: undefined,
        };
        var template = {
          templateId: this.templateId,
        };
        listUserForOaProcessTemplateFlow(template).then((response) => {
          if (this.selectObjList.length != 0) {
            var selectListObjNew = [];
            for (let i = 0; i < this.selectObjList.length; i++) {
              var filter = this.selectListNotObj.filter(
                (t) => t.userId == this.selectObjList[i]
              );
              selectListObjNew.push(filter[0]);
              //找用户集合中跟复选框一样的用户id给他的选择置成true
              var userId = filter[0].userId;
              for (let j = 0; j < response.rows.length; j++) {
                if (response.rows[j].userId == userId) {
                  response.rows[j].selectFlag = true;
                }
              }
            }

            // selectListObjNew.push(filter[0]);
            //复选框中的值改变，下拉框改变
            this.selectListNotObj = selectListObjNew;
          }

          this.dialogUserList = response.rows;
          this.dialogUserListBefore = response.rows;
          if (this.dialogUserNickNameList.length == 0) {
            for (let i = 0; i < this.dialogUserList.length; i++) {
              var a = {
                value: this.dialogUserList[i].nickName,
                userId: this.dialogUserList[i].userId,
              };
              this.dialogUserNickNameList.push(a);
            }
          }
        });
      }
      this.selectOpen = true;
    },
    handleSelect(item) {
      var filter = this.dialogUserList.filter((t) => t.userId == item.userId);
      this.dialogUserList = filter;
    },
    //对话框内搜索的两个方法
    dialogUserQuerySearch(queryString, cb) {
      var restaurants = this.dialogUserNickNameList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) !==
          -1
        );
      };
    },
    //复选框改变
    handleCheckAllChange(val) {
      val.selectFlag = !val.selectFlag;
      console.log(val.selectFlag, val);
      //选中的user对象
      if (val.selectFlag == true) {
        console.log(123);
        if (this.businessId == null || this.businessId == "") {
          this.selectListNotObj.push(val);
          this.templateObj.selectList.push(val.userId);
        }
        if (this.businessId != null && this.businessId != "") {
          this.selectListNotObj.push(val);
          this.selectObjList.push(val.userId);
        }
      } else {
        if (this.businessId == null || this.businessId == "") {
          var filter = this.selectListNotObj.filter(
            (t) => t.userId == val.userId
          );
          var filter1 = this.templateObj.selectList.filter(
            (t) => t == val.userId
          );
          var index = this.selectListNotObj.indexOf(filter[0]);
          var index1 = this.templateObj.selectList.indexOf(filter1[0]);
          if (index !== -1) {
            this.selectListNotObj.splice(index, 1);
            this.templateObj.selectList.splice(index1, 1);
          }
        }
        if (this.businessId != null && this.businessId != "") {
          var filter = this.selectListNotObj.filter(
            (t) => t.userId == val.userId
          );
          var filter1 = this.selectObjList.filter((t) => t == val.userId);
          var index = this.selectListNotObj.indexOf(filter[0]);
          var index1 = this.selectObjList.indexOf(filter1[0]);
          if (index !== -1) {
            this.selectListNotObj.splice(index, 1);
            this.selectObjList.splice(index1, 1);
          }
        }
      }
      console.log(this.selectListNotObj, this.templateObj.selectList);
    },
    dialogClose() {
      this.selectOpen = false;
    },
  },
};
</script>
<style lang="less" scoped>
/*为select框添加class*/
.select-none {
  /*pointer-events: none;*/
}

.el-select .el-input .el-select__caret {
  display: none;
}

/deep/ .el-row {
  margin-bottom: 0 !important;
}
</style>
