<template>
  <div>
    <div v-if="tableData && Object.keys(tableData).length">
      <el-descriptions :column="2" border>
        <el-descriptions-item
          ><template slot="label"> 人员姓名: </template>
          {{ tableData.nickName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 奖惩单号: </template>
          {{ tableData.rewardPunishmentCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 奖惩类型: </template>
          {{ $store.state.data.KV_MAP.bonus_penalty_type[tableData.type] }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 所属部门: </template>
          {{ tableData.rewardPunishmentCode }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="tableData.type == 1 && tableData.measure == 1"
        >
          <template slot="label"> 奖励金额(元): </template>
          {{ tableData.amount }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="tableData.type == 2 && tableData.measure == 1"
        >
          <template slot="label"> 工资扣减金额(元): </template>
          {{ tableData.amount }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="tableData.type == 1 && tableData.measure == 2"
        >
          <template slot="label"> 物品名称: </template>
          {{ tableData.itemName }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="tableData.type == 1 && tableData.measure == 2"
        >
          <template slot="label"> 物品数量: </template>
          {{ tableData.itemNum }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="1" border>
        <el-descriptions-item>
          <template slot="label"> 奖惩事由: </template>
          {{ tableData.reason }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 附件上传: </template>
          <div v-for="(item, index) in tableData.files" :key="index">
            {{ item.fileName }}
            <el-button type="text" @click="handleDownload(item)" class="ml-2"
              >下载</el-button
            >
            <el-button
              v-show="
                item.fileName.endsWith('.pdf') ||
                item.fileName.endsWith('.jpg') ||
                item.fileName.endsWith('.png') ||
                item.fileName.endsWith('.gif') ||
                item.fileName.endsWith('.jpeg')
              "
              type="text"
              @click="handlePreview(item)"
              >查看</el-button
            >
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 备注: </template>
          {{ tableData.remark }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div v-else>
      <el-table :data="tableDataList">
        <el-table-column
          label="人员姓名"
          align="center"
          width="130"
          fixed="left"
        >
          <template #default="{ row }">
            <el-button size="mini" type="text" @click="getUserData(row)">{{
              row.nickName
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="奖惩单号" align="center" width="130">
          <template #default="{ row }">
            <el-button size="mini" type="text">{{
              row.rewardPunishmentCode
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="所属部门"
          align="center"
          prop="deptChain"
          width="160"
        />
        <el-table-column label="奖惩类型" align="center" width="120">
          <template #default="{ row }">
            <div>
              {{ dict.label.bonus_penalty_type[row.type] }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="奖惩金额/物品" align="center" width="280">
          <template #default="{ row }">
            <div v-if="row.measure == 1">
              <div v-if="row.type == 1" style="color: #d9001b">
                +{{ row.amount }}元
              </div>
              <div v-else style="color: #bfbf00">-{{ row.amount }}元</div>
            </div>
            <div v-else>{{ row.itemName }}×{{ row.itemNum }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="奖惩事由"
          align="center"
          prop="reason"
          min-width="400"
        />
        <el-table-column label="创建人" align="center" width="130">
          <template #default="{ row }">
            <el-button
              size="mini"
              type="text"
              @click="getUserDataCreate(row)"
              >{{ row.createNickName }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="160"
        />

        <el-table-column label="审核状态" align="center" width="120">
          <template #default="{ row }">
            <div>
              {{ dict.label.check_work_approve_status[row.status] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          width="180px"
        >
          <template #default="{ row }">
            <el-button size="mini" type="text" @click="view(row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <DetailDialog v-model="detailDialog" :form="detailForm" />
      <UserDetail
        v-model="userDetailType"
        @close="userDetailType = false"
        :id="userId"
      />
    </div>
  </div>
</template>
<script>
import DetailDialog from "@/views/checkWork/bonusPenalty/components/DetailDialog.vue";
import privew from "@/mixin/privew";
export default {
  mixins: [privew],
  name: "BonusPenalty",
  components: { DetailDialog },
  dicts: ["bonus_penalty_type", "check_work_approve_status"],
  props: {
    tableData: {
      type: Object,
    },
    tableDataList: {
      type: Array,
    },
  },

  data() {
    return {
      detailForm: {},
      detailDialog: false,
      userId: "",
      userDetailType: false,
    };
  },
  created() {},
  methods: {
    view(row) {
      this.detailForm = row;
      this.detailDialog = true;
    },
    getUserData(data) {
      this.userId = data.userId;
      this.userDetailType = true;
    },
    getUserDataCreate(data) {
      this.userId = data.createUserId;
      this.userDetailType = true;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions-item__label {
  width: 200px;
}
</style>