<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="证照借用安排"
      :visible.sync="innerValue"
      width="90%"
      @close="handleClose"
      @opened="handleOpen"
    >
      <el-scrollbar>
        <div class="content">
          <div class="flex mb-5">
            <div class="mr-2 leading-7 font-bold">展示维度</div>
            <el-radio-group v-model="type" size="mini" @input="handleQuery">
              <el-radio-button label="图表"></el-radio-button>
              <el-radio-button label="列表"></el-radio-button>
            </el-radio-group>
          </div>
          <div class="relative">
            <el-form
              :model="queryParams"
              ref="queryForm"
              :inline="true"
              label-width="70px"
            >
              <el-form-item label="证照名称" prop="licenseName">
                <el-input
                  v-model.trim="queryParams.licenseName"
                  placeholder="请输入证照名称"
                  clearable
                  size="small"
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                  @clear="handleQuery"
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
            <div class="content_status">
              <div
                v-for="(item, index) in statusColorList"
                :key="index"
                class="item"
              >
                <div>
                  <div class="title" :style="{ background: item.value }"></div>
                  <div>{{ item.label }}</div>
                </div>
              </div>
            </div>
            <el-date-picker
              type="date"
              v-model="time"
              class="content_date"
              @change="changeDate"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </div>
          <div>
            <div v-show="type == '图表'">
              <FullCalendar
                class="calenderCon"
                :options="calendarOptions"
                ref="fullCalendar"
              >
              </FullCalendar>
            </div>
            <div v-show="type == '列表'">
              <MyTable :columns="columns" :source="configList">
                <template #custodyName="{ record }">
                  {{
                    record.licenseCustodyMiddleList &&
                    record.licenseCustodyMiddleList[0].custodyName
                  }}
                </template>
                <template #licenseName="{ record }">
                  <el-button size="mini" type="text" @click="see(record)">{{
                    record.licenseName
                  }}</el-button>
                </template>
                <template #loanServiceTime="{ record }">
                  {{ record.borrowStartTime }} ~ {{ record.borrowEndTime }}
                </template>
              </MyTable>
            </div>
          </div>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
      <addItem
        :editData="editData"
        :seeType="seeType"
        v-if="addItemType"
        :deTreeList="leftTreeList"
        @close="addItemType = false"
      />
    </el-dialog>
  </div>
</template>
<script>
import vModelMixin from "@/mixin/v-model";
import FullCalendar from "@fullcalendar/vue";
import resourceTimelinePlugin from "@fullcalendar/resource-timeline";
import interactionPlugin from "@fullcalendar/interaction";
import { getTreeList } from "@/api/certificate/directory";
import {
  getArrange,
  licenseMainLicenseeDetail,
} from "@/api/certificate/allLicenses";
import addItem from "@/views/certificate/allLicenses/components/addItem.vue";

export default {
  mixins: [vModelMixin],
  components: { FullCalendar, addItem },
  props: {
    tableData: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        licenseName: "",
      },
      time: "",
      total: 0,
      type: "图表",
      statusColorList: Object.freeze([
        { label: "审核中", value: "#F9905A" },
        { label: "未借阅", value: "#51B749" },
        { label: "借阅中", value: "#5484ED" },
        { label: "借阅完成", value: "#C280FF" },
      ]),
      statusColor: Object.freeze({
        审核中: "#F9905A",
        未借阅: "#51B749",
        借阅中: "#5484ED",
        借阅完成: "#C280FF",
      }),
      signStatusObj: Object.freeze({
        1: "未借阅",
        2: "审核中",
        3: "借阅中",
        5: "借阅完成",
      }),

      columns: Object.freeze([
        { label: "证照名称", key: "licenseName", width: 100 },
        { label: "证照保管人", key: "custodyName", width: 100 },
        { label: "证照版本", prop: "version", width: 50 },
        { label: "借阅时间", key: "loanServiceTime", width: 100 },
        { label: "本次借阅状态", prop: "lbSignStatus", width: 100 },
      ]),
      configList: [],
      calendarOptions: {
        locale: "zh-cn", //选择语言
        aspectRatio: 7,
        height: "auto",
        firstDay: 1,
        plugins: [
          resourceTimelinePlugin,
          interactionPlugin, // needed for dateClick
        ],
        headerToolbar: {
          left: "prev next today",
          center: "title",
          right: "resourceTimelineDay,resourceTimelineWeek",
        },

        buttonText: {
          // 设置按钮
          today: "今天",
          week: "周",
          day: "日",
        },
        titleFormat: {},
        schedulerLicenseKey: "GPL-My-Project-Is-Open-Source", //此配置是为了消除右下角的版权提示
        resourceAreaHeaderContent: "证照名称", // 纵轴的第一行 用来表示纵轴的名称
        resourceAreaWidth: "18%", //纵轴宽度
        slotDuration: "01:00:00", //时间间隔 默认半小时
        slotMinTime: "00:00:00", // 开始时间
        slotMaxTime: "24:00:00", // 结束时间
        handleWindowResize: true, //是否随浏览器窗口大小变化而自动变化
        initialView: "resourceTimelineDay",
        resources: [],
        events: [],
        // datesSet: this.getResourcesEvents,
        eventClick: this.eventClick, // 事件点击事件
      },
      editData: {},
      addItemType: false,
      seeType: false,
      leftTreeList: [],
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handleOpen() {
      this.init();
    },
    async init() {
      this.getResourcesEvents();
      this.getTreeList();
    },
    async getList() {
      const licenseIds = this.tableData.map(
        (item) => item.licenseId || item.id
      );
      const { rows, total } = await getArrange({
        ...this.queryParams,
        licenseIds,
        label: this.type == "图表" ? "TB" : "LB",
      });
      this.configList = rows;
      this.total = total;
      if (this.type == "图表") {
        this.handeCalendarOptions();
        this.addColor();
      }
    },
    getTreeList() {
      getTreeList().then((res) => {
        this.leftTreeList = res.data;
      });
    },
    async getResourcesEvents() {
      // const calendarApi = this.$refs.fullCalendar.getApi();
      // const calendar = calendarApi.view.calendar;
      // const time = calendar.currentData.viewTitle;
      await this.getList();
    },
    handeCalendarOptions() {
      this.calendarOptions.resources = this.configList.map((item) => {
        return {
          id: item.licenseId,
          title: item.licenseName,
        };
      });
      let events = [];
      this.configList.map((item, index) => {
        item.arrangeVoList?.map((item1, index1) => {
          events.push({
            id: `${index}-${index1}`,
            resourceId: item.licenseId,
            title: `${item1.borrowStartTime}至${item1.borrowEndTime}`,
            start: item1.borrowStartTime,
            end: item1.borrowEndTime,
            label: this.signStatusObj[item1.signStatus],
            borrowPersonNickName:item1.borrowPersonNickName,
            borrowPersonDept:item1.borrowPersonDept,
          });
        });
      });
       this.calendarOptions.events=events;
    },
    addColor() {
      this.calendarOptions.events.forEach((item) => {
        item.color = "transparent"; // 边框颜色
        item.backgroundColor = this.statusColor[item.label]; // 背景颜色
      });
    },
    changeDate(value) {
      const calendarApi = this.$refs.fullCalendar.getApi();
      const calendar = calendarApi.view.calendar;
      calendar.gotoDate(value);
      this.getResourcesEvents();
    },

    eventClick(eventInfo) {
      console.log(eventInfo,11)
      this.$alert(`<div> 借用时间: <strong> ${eventInfo.event._def.title}</strong> </div><div> 证照借用流程发起人: <strong> ${eventInfo.event._def.extendedProps.borrowPersonNickName} </strong></div><div> 发起人所属部门: <strong>${eventInfo.event._def.extendedProps.borrowPersonDept}</strong> </div>`, "", {
        confirmButtonText: "确定",
        dangerouslyUseHTMLString: true
      });
    },

    async handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    see(v) {
      licenseMainLicenseeDetail(v.licenseId).then((res) => {
        if (res.code == 200) {
          this.editData = Object.assign({ ...v }, res.data);
          this.addItemType = true;
          this.seeType = true;
        }
      });
    },

    handleClose() {},
    save() {
      this.innerValue = false;
      this.$emit("success");
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  min-height: 50vh;
  max-height: 80vh;
  padding-right: 20px;
  .content_status {
    position: absolute;
    right: 10%;
    display: flex;
    .item {
      margin-right: 20px;
      display: flex;
      font-size: 16px;
      font-weight: 600;
      position: relative;
      top: 5px;
      > div {
        display: flex;
        .title {
          width: 5px;
          height: 15px;
          margin-right: 5px;
          position: relative;
          top: 5px;
          border-radius: 5px;
        }
      }
    }
  }
  .content_date {
    position: absolute;
    left: 50%;
    top: 60px;
    transform: translateX(-50%);
    opacity: 0;
    width: 350px;
  }
  ::v-deep .fc-icon-chevron-left::before {
    content: "<";
    position: relative;
    top: -3px;
  }
  ::v-deep .fc-icon-chevron-right::before {
    content: ">";
    position: relative;
    top: -3px;
  }
  ::v-deep .fc-event-title-container {
    text-align: center;
  }

  ::v-deep .fc-event-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>