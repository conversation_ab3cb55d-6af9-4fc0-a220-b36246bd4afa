<template>
  <div>
    <div class="header-container">
      <div class="header">
        <div class="avatar">
          <router-link to="/user/profile">
            <el-avatar :size="50" :src="avatar"></el-avatar>
          </router-link>
        </div>

        <div class="info">
          <div>
            <router-link to="/user/profile">
              <div class="username">{{ this.user.nickName }}</div>
            </router-link>
          </div>
          <div class="dept">
            <div v-if="personnelArchives.deptName">
              {{ personnelArchives.companyShortName }}/{{ personnelArchives.deptName }}
            </div>
            <div>{{ this.mainPost }}</div>
          </div>
        </div>
      </div>
      <el-divider direction="vertical"></el-divider>
      <div class="button-container">
        <div class="btn-item" v-hasPermi="['home:organizational']">
          <router-link to="/organizational">
            <img :src="organizational" alt="" />
          </router-link>
          <span style="color: #363636">组织框架图</span>
        </div>
        <div class="btn-item" v-hasPermi="['home:organizationalAll']">
          <router-link to="/organizationalAll">
            <img :src="organizational" alt="" />
          </router-link>
          <span style="color: #363636">全公司组织架构</span>
        </div>
        <div class="btn-item">
          <router-link to="/oaWork/myActivite">
            <img :src="wdlc" alt="" />
          </router-link>
          <span style="color: #363636">我的流程</span>
        </div>
        <div class="btn-item" v-show="dataCwShow">
          <img style="cursor: pointer" :src="zhcwxt" alt="" @click="toCw" />

          <span style="color: #363636">智慧财务系统</span>
        </div>
        <div class="btn-item" v-show="dataPageShow">
          <router-link to="/DataSystem">
            <img :src="zhsjxt" alt="" />
          </router-link>
          <span style="color: #363636">智慧数据系统</span>
        </div>
      </div>
    </div>
    <div class="flex justify-between">
      <div class="content-container content-container-left">
        <el-card class="card-box">
          <el-tabs v-model="activeName" @tab-click="handleClick('left')">
            <el-tab-pane label="我的流程" name="1">
              <div class="tab-label" slot="label">
                <el-badge :value="badge.flowNum" :max="99" class="badge" type="warning">
                  <span>我的流程</span>
                </el-badge>
              </div>
            </el-tab-pane>
            <el-tab-pane label="待我审批" name="2">
              <div class="tab-label" slot="label">
                <el-badge :value="badge.approvalNum" :max="99" class="badge" type="warning">
                  <span>待我审批</span>
                </el-badge>
              </div>
            </el-tab-pane>
            <el-tab-pane label="预审批流程查询" name="4">
              <div class="tab-label" slot="label">
                <el-badge :value="yspData.wspNum" :max="99" class="badge b1" type="warning">
                  <span>预审批流程查询</span>
                </el-badge>
                <el-badge :value="yspData.wdbrspNum" :max="99" class="badge b2" type="warning">
                </el-badge>
              </div>
            </el-tab-pane>
          </el-tabs>
          <div class="list-container">
            <div class="list-item" @click="goProcessForm(item)" v-show="activeName == 1" v-for="item in list"
              :key="item.businessId">
              <div class="list-title">
                <el-button type="text" @click="goProcessForm(item)">{{
                  item.theme
                }}</el-button>
              </div>
              <div class="list-content">
                <div>{{ item.opcName }}>{{ item.templateName }}</div>
                <div class="status">
                  <span v-if="item.status == '0'" style="margin-left: 10px">{{
                    "审批中"
                  }}</span>
                  <span v-if="item.status == '1'" style="margin-left: 10px">{{
                    "审批通过"
                  }}</span>
                  <span v-if="item.status == '2'" style="margin-left: 10px">{{
                    "审批不通过"
                  }}</span>
                </div>
                <div>{{ item.createTime }}</div>
              </div>
            </div>

            <div class="list-item" v-show="activeName == 2" v-for="item in tastList" @click="goProcessForm1(item)"
              :key="item.businessId">
              <div class="list-title">
                <el-button type="text" @click="goProcessForm1(item)">{{
                  item.theme
                }}</el-button>
              </div>
              <div class="list-content">
                <div>{{ item.loginUser }}</div>
                <div style="margin-left: 40px">
                  {{ item.shortName }}>{{ item.opcName }}>{{ item.templateName }}
                </div>
                <div style="margin-left: 40px">{{ item.createdDate }}</div>
              </div>
            </div>
            <div v-show="activeName == 4">
              <el-table :data="tableData" style="width: 100%" @row-click="toTheme">
                <el-table-column label="流程主题" min-width="520" show-overflow-tooltip="">
                  <template slot-scope="scope">
                    <el-button style="margin-left: 0" type="text" @click="toTheme(scope.row)">{{ scope.row.theme
                      }}</el-button>
                  </template>
                </el-table-column>
                <el-table-column prop="companyName" label="所属公司" width="120" />
                <el-table-column prop="createName" label="流程发起人" width="100" />
                <el-table-column prop="createTime" label="发起时间" width="220">
                  <template slot-scope="scope">
                    {{ $format(scope.row.createTime, "yyyy-MM-dd HH:mm:ss") }}
                  </template>
                </el-table-column>
                <el-table-column prop="templateName" label="所属流程模板" />
                <el-table-column prop="nodeName" label="当前节点" width="150" />
                <el-table-column prop="handlers" label="当前处理人" width="150" />
                <el-table-column prop="userName" label="本人处理状态" width="150">
                  <template slot-scope="scope" slot="header">
                    <span>
                      本人处理状态
                      <el-tooltip class="item" effect="dark" placement="top-start">
                        <div slot="content">
                          未审核：流程已到您审核，请尽快处理<br />未到本人审核：流程还未流转到您审核<br />已审核：该条流程您已审核完毕
                        </div>
                        <i class="el-icon-warning-outline"></i>
                      </el-tooltip>
                    </span>
                  </template>
                  <template slot-scope="scope">
                    {{
                      scope.row.himCheckStatus == "-1"
                        ? "未到本人审核"
                        : scope.row.himCheckStatus == 0
                          ? "未审核"
                          : scope.row.himCheckStatus == 1
                            ? "已审核"
                            : ""
                    }}
                  </template>
                </el-table-column>

                <el-table-column prop="userName" label="流程状态" width="100">
                  <template slot-scope="scope">
                    {{ scope.row.status == 0 ? "未完结" : "已完结" }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" fixed="right" width="200">
                  <template slot-scope="scope">
                    <el-button v-if="
                      scope.row.himCheckStatus == 1 ||
                      scope.row.himCheckStatus == -1
                    " type="text" @click.stop="toTheme(scope.row)">查看流程</el-button>
                    <el-button type="text" v-if="scope.row.himCheckStatus == 0"
                      @click.stop="appToTheme(scope.row)">流程审批</el-button>
                    <el-button v-if="
                      scope.row.himCheckStatus == -1 ||
                      scope.row.himCheckStatus == 1
                    " type="text" @click.stop="toRushApp(scope.row)">催促审批</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div v-if="
              (activeName == 1 && list.length == 0) ||
              (activeName == 2 && tastList.length == 0)
            " style="text-align: center">
              <img :src="zwt" alt="" style="margin-top: 190px" />
            </div>
          </div>
          <div class="button-container">
            <el-button icon="el-icon-refresh" size="small" @click="refresh('left')">刷新</el-button>
            <el-button size="small" @click="spikTabs('left')">
              更多
              <i class="el-icon-arrow-right el-icon--right"></i>
            </el-button>
          </div>
        </el-card>
        <el-card class="card-box">
          <el-tabs @tab-click="handleClick('leftBottom')">
            <el-tab-pane label="通知公告">
              <div class="tab-label" slot="label">
                <el-badge :value="badge.noticeNum" :max="99" class="badge" type="warning">
                  <span>通知公告</span>
                </el-badge>
              </div>
            </el-tab-pane>

          </el-tabs>
          <div class="list-container">
            <div class="list-item" v-for="(item, index) in noticeList" @click="goNoticeList(item)" :key="index">
              <div class="list-title flex justify-between cursor-pointer">
                <div type="text"> <span v-show="item.isHeader == 1"
                    style="padding:3px;color:#FF9F9F ;font-size:14px;background:rgba(255, 237, 237, 1);border:1px solid rgba(255, 179, 195, 1);border-radius:20%">置顶</span>
                  {{
                    item.noticeName
                  }}<img v-show="item.isEmphasis == 1" :src="important" alt="" style="width:30px;height:30px" /> </div>
                <div style="width:20px;height:20px;border-radius:50%;flex-shrink: 0"
                  :style="{ background: item.readType == '0' ? 'red' : '#67c23a' }"></div>
              </div>
              <div class="list-content flex justify-between items-center	">
                <div>{{ item.noticeTypeName }}</div>
                <div>
                  <span>{{ item.createNickName }}</span>
                  <span style="margin-left: 40px">{{ item.createTime }}</span>
                </div>
              </div>
            </div>
            <div v-if="
              noticeList.length == 0
            " style="text-align: center">
              <img :src="zwt" alt="" style="margin-top: 190px" />
            </div>
          </div>
          <div class="button-container">
            <el-button icon="el-icon-refresh" size="small" @click="refresh('leftBottom')">刷新</el-button>
            <el-button size="small" @click="spikTabs('leftBottom')">
              更多
              <i class="el-icon-arrow-right el-icon--right"></i>
            </el-button>
          </div>
        </el-card>

      </div>
      <div class="content-container content-container-right">
        <el-card class="card-box">
          <el-tabs @tab-click="handleClick('right')">
            <el-tab-pane label="系统待办">
              <div class="tab-label" slot="label">
                <el-badge :value="badge.workNum" :max="99" class="badge" type="warning">
                  <span>系统待办</span>
                </el-badge>
              </div>
            </el-tab-pane>
          </el-tabs>
          <div class="list-container">
            <div class="list-item" @click="handleMessage(item)" v-for="(item, index) in notifyList" :key="index">
              <div class="list-title">
                {{ item.notifyMsg }}
                <el-button style="margin-left: 50px" type="text" v-if="!(['5', '7', 'bl','o'].includes(item.oaNotifyType))"
                  @click.stop="handleMessage(item)">去处理 ></el-button>
                <el-button v-if="['5', '7', 'bl'].includes(item.oaNotifyType)" style="margin-left: 50px" type="text"
                  @click.stop="reading(item)">阅读 </el-button>
                <el-button v-if="['o'].includes(item.oaNotifyType)" style="margin-left: 50px" type="text"
                  @click.stop="readingSuulies(item)">阅读 </el-button>
              </div>
              <div class="list-content">
                <div>{{ item.notifyModule }}</div>
                <div style="margin-left: 50px">{{ item.createTime }}</div>
              </div>
            </div>
            <div v-if="
              (notifyList.length == 0)
            " style="text-align: center">
              <img :src="zwt" alt="" style="margin-top: 190px" />
            </div>
          </div>
          <div class="button-container">
            <el-button icon="el-icon-refresh" size="small" @click="refresh('right')">刷新</el-button>
            <el-button size="small" @click="spikTabs('right')">
              更多
              <i class="el-icon-arrow-right el-icon--right"></i>
            </el-button>
          </div>
        </el-card>

      </div>
    </div>
    <div>
      <p style="
            font-size: 12px;
            text-align: center;
            color: #ccc;
            margin-top: 12px;
          ">
        智慧管理平台 V1.1 <span style="margin: 0 8px"></span> © 2025 聚汇融盛
      </p>
    </div>
    <DetailDialog :readUrlParams="readUrlParams" v-model="openRead" @onSubmit="listNotify" />
    <RushApp v-if="RushAppType" @close="RushAppType = false" @submit="submitRushApp" />
  </div>
</template>
<script>
import RushApp from "../preApprovalProcess/components/RushApp.vue";
import DetailDialog from "@/views/notify/agenda/components/DetailDialog.vue";

import wdlc from "@/assets/images/wdlc.png";
import zhcwxt from "@/assets/images/zhcwxt.png";
import zhsjxt from "@/assets/images/zhsjxt.png";
import zwt from "@/assets/images/zwt.jpg";
import organizational from "@/assets/images/organizational.png";
import important from "@/assets/images/important.gif";

import { yspcxList, addYspUrgent } from "@/api/oa/financeProcess";
import { mapGetters } from "vuex";
import { listTask, formDataShow, formDataSave } from "@/api/flow/task";
import { getUserProfile, getPageNumRule } from "@/api/system/user";
import { started, history } from "@/api/flow/flow";
import { getReadRelationList, updateNoticeCheckStatus } from "@/api/notice/homePage";
import { systemDataManageList } from "@/api/notice/dataSet";
import { meetingNotify } from "@/api/meeting/notice";
import { updateBlNotify } from "@/api/badSystem/financialSettlement";
import moment from "moment";

import {
  listNotify,
  updateNotifyStatus,
  getNotify,
  delNotify,
  addNotify,
  updateNotify,
} from "@/api/system/notify";
import { updateReadRelation, readDownloadHistory } from "@/api/notice/homePage";

import XEUtils from "xe-utils";

export default {
  components: {
    DetailDialog,
    RushApp,
  },
  data() {
    return {
      RushAppType: false,
      tableData: [],
      organizational,
      important,
      wdlc: "",
      zhcwxt: "",
      zhsjxt: "",
      zwt: "",
      dataPageShow: false,
      dataCwShow: false,
      // 总条数
      total: 0,
      // 请假表格数据
      formList: [],

      tastList: [],
      notifyList: [],
      noticeList: [],
      user: {},
      roleGroup: {},
      postGroup: {},
      mainPost: {},
      activeTab: "userinfo",
      queryParams: {
        pageNum: 1,
        pageSize: 5,
      },
      avatarUrl:
        "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
      activeName: "1",
      badge: {
        flowNum: 0,
        approvalNum: 0,
        workNum: 0,
        noticeNum: 0
      },
      list: [],
      openRead: false,
      readUrlParams: {},
      personnelArchives: {},
      businessId: "",
      yspData: {},
      statusOrder: ["0", "-1", "1"],
    };
  },
  computed: {
    ...mapGetters(["avatar"]),
  },
  created() {
    this.getUser();
    this.getList();
    this.getUserPagequanxian();
    this.wdlc = wdlc;
    this.zhcwxt = zhcwxt;
    this.zhsjxt = zhsjxt;
    this.zwt = zwt;
    console.log(this.zwt);
  },
  methods: {
    getPreProList() {
      yspcxList({}).then((res) => {
        const statusOrder = ["0", "-1", "1"]; // 定义优先级顺序

        let orderedArray = res.dataList.slice().sort((a, b) => {
          if (
            statusOrder.indexOf(a.himCheckStatus) <
            statusOrder.indexOf(b.himCheckStatus)
          ) {
            return -1;
          } else if (
            statusOrder.indexOf(a.himCheckStatus) >
            statusOrder.indexOf(b.himCheckStatus)
          ) {
            return 1;
          } else {
            return 0; // 如果两个项目的 state 相同，则维持原有位置关系
          }
        });
        res.dataList = orderedArray;
        this.tableData = res.dataList.slice(0, 5);
        this.yspData = {
          wdbrspNum: res.wdbrspNum,
          wspNum: res.wspNum,
        };
      });
    },
    submitRushApp(v) {
      let data = {
        urgentReviewRemarks: v,
        businessId: this.businessId,
      };
      addYspUrgent({ ...data }).then((res) => {
        if (res.code == 200) {
          this.$message.success("催促审批成功");
          this.RushAppType = false;
          this.getPreProList();
          this.businessId = "";
        }
      });
    },
    toRushApp(v) {
      this.businessId = v.businessId;
      this.RushAppType = true;
    },
    toTheme(v) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: v.businessId,
          businessId: v.businessId,
          financeProcess: "true",
        },
      });
    },
    appToTheme(v) {
      this.$router.push({
        path: "/oaWork/approveProcessForm",
        query: {
          businessId: v.businessId,
        },
      });
    },
    getUserPagequanxian() {
      getPageNumRule().then((response) => {
        this.dataPageShow = response.data;
        this.dataCwShow = response.caiwu;
      });
    },

    goProcessForm1(item) {
      this.$router.push({
        path: "/oaWork/approveProcessForm",
        query: { oid: item.oid, businessId: item.businessKey, taskId: item.id },
      });
    },
    goProcessForm(item) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: { oid: item.oid, businessId: item.businessId },
      });
    },
    async goNoticeList(row) {
      await updateReadRelation({ noticeId: row.noticeId });
      await readDownloadHistory({ noticeId: row.noticeId, rdType: 1, version: row.version });
      this.$router.push({
        path: `/noticeListDetail/${row.noticeId}`,
        query: {
          title: "通知公告详情",
        },
      });
    },
    spikTabs(type) {
      if (type == 'left') {
        if (this.activeName == 1) {
          this.$router.push({ path: "/oaWork/myActivite" });
        } else if (this.activeName == 2) {
          this.$router.push({
            path: "/oaWork/myActivite",
            query: { tab1: "second", tab2: "first" },
          });
        } else if (this.activeName == 4) {
          this.$router.push({ path: "/oaWork/preApprovalProcess" });
        }
      } else if (type == 'right') {
        this.$router.push({ path: "/notify/agenda" });
      } else if (type == 'leftBottom') {
        this.$router.push({ path: "/noticeList" });
      }
    },
    async refresh(type) {
      if (type == 'left') {
        await started(this.queryParams).then((response) => {
          this.formList = response.rows;
          this.list = this.formList;
          this.badge.flowNum = response.total;
          this.total = response.total;
        });
        await listTask(this.queryParams).then((response) => {
          this.tastList = response.rows;
          this.badge.approvalNum = response.total;
        });
      } else if (type == 'right') {
        this.listNotify();
      } else if (type == 'leftBottom') {
        this.getNotice();
      }
    },
    toCw() {
      console.log(process.env.NODE_ENV);
      if (process.env.NODE_ENV == "production") {
        window.open("http://oa.jhrs.top:11009");
      } else if (process.env.NODE_ENV == "testbeta") {
        window.open("http://123.57.217.52:21009");
      } else if (process.env.NODE_ENV == "testbeta2") {
        window.open("http://123.57.217.52:31009");
      } else if (process.env.NODE_ENV == "uat") {
        window.open("http://oatest.jhrs.top:21009");
      } else if (process.env.NODE_ENV == "uat2") {
        window.open("http://oatest.jhrs.top:31009");
      } else if (process.env.NODE_ENV == "uat3") {
        window.open("http://oatest.jhrs.top:41009");
      }
    },
    async getList() {
      this.loading = true;
      await started(this.queryParams).then((response) => {
        this.formList = response.rows;
        this.list = this.formList;
        this.badge.flowNum = response.total;
        this.total = response.total;
      });

      await listTask(this.queryParams).then((response) => {
        this.tastList = response.rows;
        this.badge.approvalNum = response.total;
      });
      this.listNotify();
      this.getPreProList();
      this.getNotice();
    },
    listNotify() {
      listNotify(this.queryParams).then((response) => {
        this.notifyList = response.rows;
        this.badge.workNum = response.total;
      });
    },
    async getNotice() {
      let data = await systemDataManageList();
      const flatData = XEUtils.toTreeArray(data.rows, {
        children: "fPiattaformas", // 指定子节点字段名
        clear: true
      });
      const { rows } = await getReadRelationList({});
      if (rows.length) {
        rows.forEach(item => {
          flatData.forEach(item1 => {
            if (item1.id == item.noticeType) {
              item.noticeTypeName = item1.dataName
            }
          })
        })
        this.noticeList = rows.slice(0, 5);
        let total = 0;
        rows.forEach(item => {
          if (item.readType == 0) {
            total++;
          }
        })
        this.badge.noticeNum = total;
      } else {
        this.noticeList = [];
        this.badge.noticeNum = 0;
      }

    },
    handleMessageCertificate(value) {
      this.$router.push({ path: '/certificate/allLicenses', query: { id: value.id, licenseId: value.licenseId } });
    },
    goProcess(row) {
      this.$router.push({
        path: "/oaWork/approveProcessForm",
        query: {
          oid: row.processId,
          businessId: row.processId,
          formHome: true,
          formHomeId: row.id,
          // taskId:row.id
        },
      });
    },
    async handleMessage(row) {
      if (['月报评审', '加班申请', '请假申请', '取消请假申请', '取消加班申请', '取消奖励/惩罚', '取消出差'].includes(row.notifyModule)) {
        this.$router.push({ path: `/commonNotifyModule/${row.id}`, query: { row: JSON.stringify(row) } }).catch(err => { console.log(err) });
        return;
      }
      if (['会议参加提醒'].includes(row.notifyModule)) {
        await meetingNotify({ viewFlag: 1, id: row.id, viewTime: moment(new Date()).format("YYYY-MM-DD HH:mm") });
        this.$router.push({
          path: row.url,
        });
        return;
      }
      if (['5', '7', 'bl'].includes(row.oaNotifyType)) {
        this.reading(row);
        return;
      }
      if (row.oaNotifyType == '6') {
        this.listNotify();
        this.goProcess(row);
        return;
      }
      if (row.url === "/zzCommonNotify/notify") {
        this.handleMessageCertificate(row);
        return;
      }
      if (row.id == -2) {
        sessionStorage.setItem("remindData", JSON.stringify(row));
        this.$router.push({
          path: "/oaWork/remind",
        });
        return;
      }
      if (row.oaNotifyType == 9) {
        updateNoticeCheckStatus({ id: row.yurrId })
        sessionStorage.setItem("remindData", JSON.stringify(row));
        this.$router.push({
          path: "/oaWork/remind",
          query: {
            homePage: true
          }
        });
        return;
      }
      if (row.oaNotifyType == 'o') {
        this.readingSuulies(row);
        return;
      }
      if (row.oaNotifyType !== null) {
        this.$router.push({
          path: row.url,
          query: {
            oaNotifyStep: row.oaNotifyStep,
          },
        });
        return;
      }
    
      this.$router.push({
        path: row.url,
        query: { productId: row.projectId, incomeId: row.incomeId },
      });
    },
    readingSuulies(item) {
      console.log(item);
      
      this.$alert(item.notifyMsg, `${item.notifyModule}`, {
        cancelButtonText: '取消',
        confirmButtonText: '清除通知',
        showCancelButton: true,
        callback: async (action) => {
          console.log(action)
          if (action == "confirm") {

            await updateNotify({ viewFlag: 1, id: item.id, viewTime: moment(new Date()).format("YYYY-MM-DD HH:mm") });

            this.listNotify();
          }
        },
      });
    },
    reading(item) {
      if (['7', 'bl'].includes(item.oaNotifyType)) {
        this.$alert(item.remindText, '通知内容', {
          cancelButtonText: '取消',
          confirmButtonText: '清除通知',
          showCancelButton: true,
          callback: async (action) => {
            console.log(action)
            if (action == "confirm") {
              if (item.oaNotifyType == 7) {
                await meetingNotify({ viewFlag: 1, id: item.id, viewTime: moment(new Date()).format("YYYY-MM-DD HH:mm") });
              } else if (item.oaNotifyType == 'bl') {
                await updateBlNotify({ viewFlag: 1, id: item.id, });
              }
              this.listNotify();
            }
          },
        });
      } else {
        this.readUrlParams = { ...item };
        this.openRead = true;
      }
    },
    getUser() {
      getUserProfile().then((response) => {
        this.user = response.data;
        this.roleGroup = response.roleGroup;
        this.postGroup = response.postGroup;
        this.mainPost = response.mainPost;
      });
    },
    handleClick(type) {
      if (type == 'left') {
        switch (this.activeName) {
          case "1":
            this.list = this.formList;
            break;
          case "2":
            break;
          default:
            break;
        }
        this.refresh('left')
      }
      if (type == 'right') {
        this.refresh('right')
      }
      if (type == 'leftBottom') {
        this.refresh('leftBottom')
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 50px;

  .header {
    display: flex;
    align-items: center;

    .info {
      margin-left: 20px;

      .username {
        color: #000;
        font-size: 16px;
        font-weight: bolder;
      }

      .dept {
        margin-top: 10px;
        color: #aaaaaa;
        font-size: 14px;
      }
    }
  }

  .button-container {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 80px;

      .icon {
        font-size: 30px;
      }

      img {
        width: 40px;
        margin-bottom: 6px;
      }
    }
  }
}

.content-container {
  background: #f7f7f7;

  .card-box {
    position: relative;
    height: 730px;
    overflow-y: auto;

    .tab-label {
      padding-top: 10px;
    }

    .list-container {
      display: flex;
      flex-direction: column;
      min-height: 500px;

      .list-item {
        display: flex;
        flex-direction: column;
        border-bottom: 1px solid #eeee;
        padding: 10px 0;

        .list-title {
          color: #333;
          font-size: 18px;
          font-weight: bolder;
        }

        .list-content {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #aaa;
          margin: 14px 0;

          .status {
            width: 52px;
            margin: 0 140px;
          }
        }
      }
    }

    .button-container {
      margin-top: 20px;
    }
  }
}

.content-container-left {
  padding: 20px 0 2px 20px;
  width: 60%;
}

.content-container-right {
  padding: 20px 20px 2px 20px;
  width: 40%;
}
</style>
<style lang="less" scoped>
/deep/ .b1 .el-badge__content.is-fixed {
  right: 35px !important;
}

/deep/ .b2 .el-badge__content--warning {
  background: #bfbf00 !important;
  right: 12px !important;
  top: -20px !important;
}

.el-divider--vertical {
  height: 5em !important;
  position: absolute !important;
  left: 280px !important;
}
</style>
