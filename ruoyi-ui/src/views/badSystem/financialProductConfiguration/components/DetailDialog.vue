<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      width="1050px"
      @close="handleClose"
      @open="handleOpen"
    >
      <div>
        <MyForm
          ref="form"
          v-model="myForm"
          :columns="formColumnsDialog"
          formType="form"
          :rules="rules"
          label-width="175px"
          :disabled="title == '查看产品'"
        >
          <template #version>
            <el-form-item label="版本号" prop="version">
              <el-input
                @input="myForm.version = myForm.version.replace(/[^0-9.]/g, '')"
                placeholder="请输入版本号"
                v-model.trim="myForm.version"
              >
              </el-input>
            </el-form-item>
          </template>
          <template #companyProductInterestList>
            <div class="text-base font-bold">计息规则</div>
            <el-button
              type="primary"
              class="my-2"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              >添加一行</el-button
            >
            <MyTable
              :columns="columnsDialog"
              :source="myForm.companyProductInterestList"
            >
              <template #interestType="{ record }">
                <el-select
                  v-model="record.interestType"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in interestTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </template>
              <template #annualCalculationDate="{ record }">
                <el-input
                  v-model.trim="record.annualCalculationDate"
                  @input="changeInput(record, 'annualCalculationDate', 1)"
                  placeholder="请输入年计算日"
                  ><template slot="append">天</template></el-input
                >
              </template>
              <template #annualizedInterestRate="{ record }">
                <el-input
                  v-model.trim="record.annualizedInterestRate"
                  @input="changeInput(record, 'annualizedInterestRate', 4)"
                  placeholder="请输入年利化率"
                  ><template slot="append">%</template></el-input
                >
              </template>
              <template #dailyInterestRate="{ record }">
                <el-input v-model.trim="record.dailyInterestRate" disabled
                  ><template slot="append">%</template></el-input
                >
              </template>
              <template #delete="{ record }">
                <el-button
                  @click="deletes(record)"
                  type="text"
                  style="color: red"
                  >删除</el-button
                >
              </template>
            </MyTable>
          </template>
        </MyForm>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <!--          <el-button-->
          <!--            type="primary"-->
          <!--            @click="onDraft"-->
          <!--            class="mr-3"-->
          <!--            v-show="title != '查看产品'"-->
          <!--            >存草稿</el-button-->
          <!--          >-->
          <el-button
            type="primary"
            @click="onSubmit"
            class="mr-3"
            v-show="title != '查看产品'"
            >提交</el-button
          >
          <el-button
            v-hasPermi="['badSystem:financialProductConfiguration:delete']"
            v-show="title == '查看产品'"
            type="primary"
            @click="handleDelete"
            class="mr-3"
            >删除</el-button
          >
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import {
  addCompanyProduct,
  updateCompanyProduct,
  delCompanyProduct,
} from "@/api/badSystem/financialProductConfiguration";

import { decimal, floatDivide } from "@/utils";
export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    dialogType: {
      type: String,
      required: true,
      default: "",
    },
  },
  data() {
    return {
      ...config,
      myForm: {
        enableStatus: "1",
        companyProductInterestList: [],
      },
      title: "",
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handleOpen() {
      this.myForm = XEUtils.clone(this.form, true);
      this.$set(
        this.myForm,
        "companyProductInterestList",
        this.myForm.companyProductInterestList || []
      );
      this.$set(this.myForm, "enableStatus", this.myForm.enableStatus || "1");
      this.title = { add: "新增产品", update: "修改产品", view: "查看产品" }[
        this.dialogType
      ];
      this.$nextTick(() => this.$refs["form"].clearValidate());
    },
    changeInput(value, itemValue, num) {
      value[itemValue] = decimal(value[itemValue], num);
      if (value.annualCalculationDate && value.annualizedInterestRate) {
        value.dailyInterestRate = floatDivide(
          value.annualizedInterestRate,
          value.annualCalculationDate
        ).toFixed(8);
      }
    },
    handleAdd() {
      this.myForm.companyProductInterestList.push({
        interestType: "",
        annualCalculationDate: "",
        annualizedInterestRate: "",
        dailyInterestRate: "",
      });
    },
    deletes(record) {
      this.myForm.companyProductInterestList =
        this.myForm.companyProductInterestList.filter(
          (item) => item.xh !== record.xh
        );
    },
    onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.myForm.id) {
            await updateCompanyProduct({ ...this.myForm, isDraft: 2 });
          } else {
            await addCompanyProduct({ ...this.myForm, isDraft: 2 });
          }
          this.$emit("on-submit-success", this.myForm);
          this.$modal.msgSuccess("操作成功");
          this.innerValue = false;
        }
      });
    },
    onDraft() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.myForm.id) {
            await updateCompanyProduct({ ...this.myForm, isDraft: 1 });
          } else {
            await addCompanyProduct({ ...this.myForm, isDraft: 1 });
          }
          this.$emit("on-submit-success", this.myForm);
          this.$modal.msgSuccess("操作成功");
          this.innerValue = false;
        }
      });
    },
    handleDelete() {
      this.$modal
        .confirm(
          '是否确认删除新产品名称为"' + this.myForm.productName + '"的数据项？'
        )
        .then(() => {
          return delCompanyProduct(this.myForm.id);
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.innerValue = false;
          this.$emit("on-submit-success");
        })
        .catch((e) => {
          console.log(e);
        });
    },
    handleClose() {},
  },
};
</script>
