export default {
  mechanismTypeObj:Object.freeze({
    1:'债权机构',
    2:'受托方',
    3:'委托方',
    4:'渠道',
  }),
  mechanismDisposalModeObj:Object.freeze({
    1:'催收/调解',
    2:'法诉',
    3:'保全',
  }),
  collaborationStatusObj:Object.freeze({
    1:'开启',
    2:'未开启',
  }),
  formColumns: [
    {
      label: "机构全称",
      prop: "mechanismName",
      type: "input",
      placeholder: "请输入机构全称",
    },
    {
      label: "机构简称",
      prop: "mechanismShortName",
      type: "input",
      placeholder: "机构简称",
    },
    {
      label: "机构类型",
      prop: "mechanismType",
      type: "select",
      filterable: true,
      options: [
        {value:'1',label:'债权机构'},
        {value:'2',label:'受托方'},
        {value:'3',label:'委托方'},
        {value:'4',label:'渠道'},
      ],
      placeholder: "请选择机构类型",
    },
    {
      label: "处置模式",
      prop: "mechanismDisposalMode",
      type: "select",
      filterable: true,
      options: [
        {value:'1',label:'催收/调解'},
        {value:'2',label:'法诉'},
        {value:'3',label:'保全'},
      ],
      placeholder: "请选择处置模式",
    },
  ],
  columns: [
    { label: "机构编号", prop: "mechanismCode", minWidth: "150px" },
    { label: "机构全称", key: "mechanismName", minWidth: "250px" },
    {
      label: "机构简称",
      prop: "mechanismShortName",
      minWidth: "150px",
    },
    { label: "机构类型", prop: "mechanismTypeLabel", minWidth: "150px" },
    { label: "处置模式", prop: "mechanismDisposalModeLabel", minWidth: "250px" },
    { label: "机构入驻日期", prop: "joinTime", minWidth: "150px" },
    { label: "合作状态", prop: "collaborationStatusLabel", minWidth: "100px" },
    { label: "操作", key: "operate", minWidth: "100" },
  ],
  UploadImporUrl:Object.freeze( {
    importUrl: "/mechanism/importDataCheck",
    downloadUrl: "/mechanism/importTemplate",
  }),
};
