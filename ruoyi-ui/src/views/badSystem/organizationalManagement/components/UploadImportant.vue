<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="550px"
      title="导入"
      @close="handleClose"
      @open="handleOpen"
    >
      <div class="content">
        <el-upload
          ref="upload"
          accept=".xlsx, .xls"
          action="#"
          :http-request="customUpload"
          :auto-upload="false"
          :file-list="upload.fileList"
          :on-change="changeFileList"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__tip" slot="tip">
            <div>
              上传前需要删除费数据工作表，机构表中不得包含与表头无关的内容。
            </div>
            <div>表头需包含机构全称、机构入驻日期、机构签约截止日期。</div>
          </div>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button
            type="success"
            @click="importTemplate"
            v-show="url.downloadUrl"
            >下载模板</el-button
          >
          <el-button type="primary" @click="onSure">确定</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
      <el-dialog :visible.sync="dialogVisible" width="30%" append-to-body>
        <div v-show="!duplicateList.length && !successList.length">
          导入数据为空
        </div>
       
        <div v-show="successList.length && !duplicateList.length">
          其中文件可导入
          <el-button type="text" @click="viewFile">{{
            successList.length
          }}</el-button>
          笔 是否继续导入
        </div>
        <div v-show="duplicateList.length">
          <div>
            部分识别成功，其中已识别<el-button type="text">{{
              successList.length
            }}</el-button
            >笔, 其中<el-button type="text">{{
              duplicateList.length
            }}</el-button
            >笔未识别，请重新导入
          </div>
          <el-button type="text" @click="viewFile">文件下载</el-button>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button
            v-show="duplicateList.length"
            @click="
              dialogVisible = false;
              innerValue = false;
            "
            >关闭</el-button
          >
          <el-button
            v-show="!duplicateList.length && successList.length"
            @click="dialogVisible = false"
            >取消</el-button
          >
          <el-button
            v-show="!duplicateList.length && successList.length"
            type="primary"
            @click="exprotSure"
            >导入</el-button
          >
        </span>
      </el-dialog>
    </el-dialog>
  </div>
</template>
  
<script>
import vModelMixin from "@/mixin/v-model";
import request from "@/utils/request";
import XLSX from "xlsx-js-style";
import config from "./config";
import XEUtils from "xe-utils";
import { importDataMechanism } from "@/api/badSystem/organizationalManagement";

export default {
  mixins: [vModelMixin],
  name: "UploadImportant",
  props: {
    url: {
      type: Object,
      required: true,
      default: {
        importUrl: "",
        downloadUrl: "",
      },
    },
  },
  watch: {},
  data() {
    return {
      ...config,
      dialogVisible: false,
      successList: [],
      duplicateList: [],
      upload: {
        fileList: [],
      },
    };
  },
  mounted() {},
  methods: {
    init() {
      this.handleOpen();
    },
    onSure() {
      this.$refs.upload.submit();
    },
    async uploadFile() {
      const formData = new FormData();
      this.upload.fileList.forEach((file) => {
        formData.append("file", file.raw);
      });

      const res = await request.post(this.url.importUrl, formData);
      if (res.code == 200) {
        return res.data;
      }
      return Promise.reject(new Error(res.msg));
    },
    customUpload() {
      this.uploadFile()
        .then((resp) => {
          this.successList = resp.successList;
          this.duplicateList = resp.duplicateList;
          this.handleListExport();
          this.dialogVisible = true;
        })
        .catch((e) => {
          this.$refs.upload.clearFiles();
        });
    },
    handleListExport() {
      this.successList.forEach((item, index) => {
        item.index = index + 1;
      });
      this.duplicateList.forEach((item, index) => {
        item.index = index + 1;
      });
    },
    changeFileList(file, fileList) {
      if (fileList.length > 0) {
        this.upload.fileList = [fileList[fileList.length - 1]];
      }
    },

    /** 下载模板操作 */
    importTemplate() {
      this.download(
        this.url.downloadUrl,
        {},
        `模板_${new Date().getTime()}.xlsx`
      );
    },
    async viewFile() {
      let title = [
        [
          "序号",
          "机构全称",
          "机构简称",
          "机构类型",
          "处置模式",
          "业务负责人",
          "机构入驻日期",
          "机构签约截至日期",
          "备注",
        ],
      ];
      let dataExeclSuccess = XEUtils.clone(title, true);
      let dataExeclError = XEUtils.clone(title, true);
      const valueArr = [
        "index",
        "mechanismName",
        "mechanismShortName",
        "mechanismType",
        "mechanismDisposalMode",
        "businessManager",
        "joinTime",
        "signEndTime",
        "remark",
      ];
      this.successList.forEach((item) => {
        let tempItem = [];
        valueArr.forEach((item1) => {
          tempItem.push(item[item1] || "");
        });
        dataExeclSuccess.push(tempItem);
      });
      // 创建工作表
      const ws = XLSX.utils.aoa_to_sheet(dataExeclSuccess);
      // 自动调整列宽
      this.autoAdjustColumnWidth(ws, dataExeclSuccess);
      // 创建工作簿并添加工作表
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "识别成功数据");
      if (this.duplicateList.length) {
        this.duplicateList.forEach((item) => {
          let tempItem = [];
          valueArr.forEach((item1) => {
            tempItem.push(item[item1] || "");
          });
          dataExeclError.push(tempItem);
        });
        const wsError = XLSX.utils.aoa_to_sheet(dataExeclError);
        this.autoAdjustColumnWidth(wsError, dataExeclError);
        XLSX.utils.book_append_sheet(wb, wsError, "未识别数据");
      }
      // 导出 Excel 文件
      XLSX.writeFile(wb, "机构导入.xlsx");
    },
    autoAdjustColumnWidth(ws, data) {
      // 计算列宽
      const colWidths = data[0].map((_, colIndex) =>
        Math.max(
          ...data.map(
            (row) =>
              (row[colIndex] ? row[colIndex].toString().length * 2 : 5) + 5 // 添加一点额外空间
          )
        )
      );
      ws["!cols"] = colWidths.map((width) => ({ wch: width }));
    },
    async exprotSure() {
      await importDataMechanism({ successList: this.successList });
      this.dialogVisible = false;
      this.innerValue = false;
      this.$message.success("导入成功");
      this.$emit("on-save-success");
    },
    async handleOpen() {},
    handleClose() {
      this.duplicateList = [];
      this.successList = [];
      this.$refs.upload.clearFiles();
    },
  },
};
</script>
  <style lang="less" scoped>
.content {
  max-height: 70vh;
  padding-right: 20px;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>