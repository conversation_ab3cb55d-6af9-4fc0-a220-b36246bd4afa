<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>

    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          v-hasPermi="['bad:organizationalManagement:add']"
          @click="handleAdd"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          >创建机构</el-button
        >
        <el-button
          v-hasPermi="['bad:organizationalManagement:export']"
          type="warning"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="openImport = true"
          >导入</el-button
        >
      </div>
    </div>
    <MyTable
      :columns="columns"
      :showIndex="true"
      :source="configList"
      :queryParams="queryParams"
    >
      <template #mechanismName="{ record }">
        <el-button
          v-if="checkPermi(['badSystem:organizationalManagement:view'])"
          type="text"
          @click="handleView(record)"
          >{{ record.mechanismName || "-" }}</el-button
        >
        <div v-else>{{ record.mechanismName || "-" }}</div>
      </template>

      <template #operate="{ record }">
        <el-button
          v-hasPermi="['bad:organizationalManagement:view']"
          type="text"
          @click="handleView(record)"
          >查看</el-button
        >
        <el-button
          v-hasPermi="['bad:organizationalManagement:update']"
          type="text"
          @click="handleUpdate(record)"
          >修改</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <UploadImportant
      v-model="openImport"
      @on-save-success="getList"
      :url="UploadImporUrl"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { getMechanismList } from "@/api/badSystem/organizationalManagement";
import config from "./components/config";
import UploadImportant from "./components/UploadImportant";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

export default {
  name: "OrganizationalManagement",
  components:{UploadImportant},
  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      configList: [],
      openImport: false,
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    checkPermi,
    init() {
      this.getList();
    },
    async getList() {
      const { rows, total } = await getMechanismList(this.queryParams);
      this.configList = this.handleConfigList(rows);
      this.total = total;
    },
    handleConfigList(row) {
      const newRow = XEUtils.clone(row, true);
      newRow.forEach((item) => {
        item.mechanismTypeLabel = item.mechanismType
          .map((item1) => this.mechanismTypeObj[item1])
          .join();
        item.mechanismDisposalModeLabel = item.mechanismDisposalMode
          .map((item1) => this.mechanismDisposalModeObj[item1])
          .join();
        item.collaborationStatusLabel =
          this.collaborationStatusObj[item.collaborationStatus];
      });
      return newRow;
    },
    handleAdd() {
      this.$router.push({
        path: `/badSystemOther/organizationalManagementDetail/add`,
        query: {
          title: `创建机构`,
        },
      });
    },

    handleUpdate(row) {
      this.$router.push({
        path: `/badSystemOther/organizationalManagementDetail/${row.id}`,
        query: {
          title: `修改机构${row.mechanismCode}`,
        },
      });
    },
    handleView(row) {
      this.$router.push({
        path: `/badSystemOther/organizationalManagementDetailView/${row.id}`,
        query: {
          title: `机构详情${row.mechanismCode}`,
        },
      });
    },
    handleQuery(value) {
      this.queryParams.pageNum = 1;
      this.getList();
    },
   
  },
};
</script>
<style lang="scss" scoped>
</style>
