<template>
  <div class="content">
    <div class="content_status">
      <div v-for="(item, index) in statusColorList" :key="index" class="item">
        <div>
          <div class="title" :style="{ background: item.value }"></div>
          <div>{{ item.label }}</div>
        </div>
      </div>
    </div>
    <el-date-picker
      v-model="time"
      type="date"
      class="content_date"
      @change="changeDate"
      value-format="yyyy-MM-dd"
    >
    </el-date-picker>
    <FullCalendar
      class="calenderCon"
      :options="calendarOptions"
      ref="fullCalendar"
    />
  </div>
</template>

<script>
import FullCalendar from "@fullcalendar/vue";
import resourceTimelinePlugin from "@fullcalendar/resource-timeline";
import interactionPlugin from "@fullcalendar/interaction";
import { meetingList } from "@/api/meeting/search";
import { meetingRoomList } from "@/api/meeting/management";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

import moment from "moment";
import "moment/locale/zh-cn";
import config from "./components/config";

export default {
  name: "Arrangement",
  components: {
    FullCalendar,
  },
  data() {
    return {
      detailItem: {},
      ...config,
      time: "",
      calendarOptions: {
        locale: "zh-cn", //选择语言
        aspectRatio: 7,
        height: "auto",
        firstDay: 1,
        plugins: [
          resourceTimelinePlugin,
          interactionPlugin, // needed for dateClick
        ],
        views: {
          // 日视图的自定义配置
          resourceTimelineDay: {
            titleFormat: {
              year: "numeric",
              month: "short",
              day: "numeric",
            },
            slotLabelFormat: {
              hour: "2-digit",
              minute: "2-digit",
              meridiem: false,
              omitZeroMinute: false,
            },
          },
          resourceTimelineWeek: {
            type: "resourceTimeline", // 资源时间线视图
            duration: { weeks: 1 }, // 每次显示1周
            slotDuration: { days: 1 }, // 每列代表一天
            slotLabelFormat: function (date) {
              moment.locale("zh-cn");
              return moment(date.date.marker).format("ddd(M/D)"); // 如果日期无效，返回空
            },
          },
        },
        headerToolbar: {
          left: "prev next today",
          center: "title",
          right: "resourceTimelineDay,resourceTimelineWeek",
        },
        buttonText: {
          // 设置按钮
          today: "今天",
          week: "周",
          day: "日",
        },

        titleFormat: {}, 
        schedulerLicenseKey: "GPL-My-Project-Is-Open-Source", //此配置是为了消除右下角的版权提示
        resourceAreaHeaderContent: "会议室", // 纵轴的第一行 用来表示纵轴的名称
        resourceAreaWidth: "10%", //纵轴宽度
        slotDuration: "02:00:00", //时间间隔 默认半小时
        slotMinTime: "00:00:00", // 开始时间
        slotMaxTime: "24:00:00", // 结束时间
        handleWindowResize: true, //是否随浏览器窗口大小变化而自动变化
        initialView: "resourceTimelineDay",
        resources: [],
        events: [],
        eventContent: function (info) {
          // 自定义事件显示内容
          let eventTitle = info.event.title; // 事件标题
          return { html: `<div class="event-title">${eventTitle}</div>` }; // 只显示事件标题
        },
        slotMinWidth: 200, // 每个时间槽的最小宽度
        eventMinWidth: 200, // 设置事件最小宽度
        eventClick: this.eventClick, // 事件点击事件
        dateClick: this.dateClick,
        datesSet: this.datesSet,
      },
    };
  },
  mounted() {},
  methods: {
    async getResourcesEvents(rows, roomData) {
      this.detailItem = rows;
      this.calendarOptions.resources = roomData.rows.map((item) => {
        return {
          id: item.id,
          title: item.meetingRoomName,
          roomId: item.id,
        };
      });

      this.calendarOptions.events = rows.map((item) => {
        return {
          id: item.id,
          resourceId: item.meetingRoom,
          title: `${item.hourMin} ${item.meetingTheme}`,
          start: item.meetingStartTime,
          end: item.meetingEndTime,
          label: this.statusLabel[item.meetingState],
        };
      });

      this.addColor();
    },
    addColor() {
      this.calendarOptions.events.forEach((item) => {
        item.color = "transparent"; // 边框颜色
        item.backgroundColor = this.statusColor[item.label]; // 背景颜色
      });
    },
    eventClick(eventInfo) {
      const item = this.detailItem.filter(
        (item) => item.id == eventInfo.event._def.publicId
      )[0];
      this.$alert(
        `<div>会议名称: ${item.meetingTheme}</div><div>时间: ${item.meetingStartTime} - ${item.meetingEndTime}</div><div>会议室: ${item.meetingRoomName}</div><div>主持人: ${item.organizationalUserName}</div><div>会议发起人: ${item.createByName}</div>`,
        "会议安排",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "查看详情",
          callback: (action) => {
            if (action == "confirm")
              this.$router.push({
                path: `/meetingOther/meetingDetails/${eventInfo.event._def.publicId}`,
              });
          },
        }
      );
    },
    dateClick(eventInfo) {
      if (eventInfo.resource._context.viewApi.type == "resourceTimelineWeek") {
        const inputDate = new Date(eventInfo.date);
        // 获取今天的日期
        const today = new Date();
        // 清除今天日期中的时分秒，确保只比较年月日
        today.setHours(0, 0, 0, 0);
        inputDate.setHours(0, 0, 0, 0);
        if (inputDate < today) return;
      } else {
        let inputDate = new Date(eventInfo.date);
        inputDate.setHours(inputDate.getHours() + 2);
        // 获取今天的日期
        const today = new Date();
        if (inputDate < today) return;
      }
      const params = eventInfo.resource._resource;
      let time = undefined;
      if(eventInfo.date<new Date()){
        time= this.roundUpToNextHalfHour();
      }else{
        time=this.$format(eventInfo.date, "yyyy-MM-dd HH:mm");
      }
      this.$alert(
        `<div>召开时间: ${time}</div><div>会议地点: ${params.title}</div>`,
        "会议室预约/会议安排",
        {
          confirmButtonText: "发起会议",
          cancelButtonText: "取消",
          dangerouslyUseHTMLString: true,
          showCancelButton:true,
          showConfirmButton:checkPermi(["arrangement:add"])
        }
      ).then(() => {
        this.$router.push({
          path: "/meetingOther/sponsor",
          query: {
            time,
            room: params.extendedProps.roomId,
          },
        });
      });
    },
    roundUpToNextHalfHour(time) {
      const date = time
        ? new Date(`${time} ${this.$format(new Date(), "HH:mm")}`)
        : new Date();
      const minutes = date.getMinutes();
      const remainder = minutes % 30;
      // 如果分钟数小于30分钟，则向上取整到30分钟
      // 如果分钟数大于等于30分钟，则向上取整到下一个小时的0分钟
      const minutesToAdd = remainder === 0 ? 0 : 30 - remainder;
      // 创建一个新的日期对象，避免修改原始日期
      const newDate = new Date(date.getTime());
      newDate.setMinutes(minutes + minutesToAdd);
      newDate.setSeconds(0); // 将秒数设置为0
      newDate.setMilliseconds(0); // 将毫秒设置为0
      return this.$format(newDate, "yyyy-MM-dd HH:mm");
    },
    async datesSet(eventInfo) {
      let params = {};
      if (eventInfo.view.type == "resourceTimelineDay") {
        params = {
          meetingStartTime: this.$format(eventInfo.start, "yyyy-MM-dd"),
        };
      } else if (eventInfo.view.type == "resourceTimelineWeek") {
        params = {
          startTime: this.$format(eventInfo.start, "yyyy-MM-dd"),
          endTime: this.$format(eventInfo.end, "yyyy-MM-dd"),
        };
      }
      params.meetingState = 6;
      const { rows } = await meetingList(params);
      const roomData = await meetingRoomList({ meetingRoomStatus: 0 });
      this.getResourcesEvents(rows, roomData);
    },
    changeDate(value) {
      const calendarApi = this.$refs.fullCalendar.getApi();
      const calendar = calendarApi.view.calendar;
      calendar.gotoDate(value);
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  padding: 20px;
  position: relative;
  .content_status {
    position: absolute;
    right: 10%;
    display: flex;
    .item {
      margin-right: 20px;
      display: flex;
      font-size: 16px;
      font-weight: 600;
      position: relative;
      top: 5px;
      > div {
        display: flex;
        .title {
          width: 5px;
          height: 15px;
          margin-right: 5px;
          position: relative;
          top: 5px;
          border-radius: 5px;
        }
      }
    }
  }
  .content_date {
    position: absolute;
    left: 50%;
    top: 20px;
    transform: translateX(-50%);
    opacity: 0;
    width: 350px;
  }
  ::v-deep .fc-icon-chevron-left::before {
    content: "<";
    position: relative;
    top: -3px;
  }
  ::v-deep .fc-icon-chevron-right::before {
    content: ">";
    position: relative;
    top: -3px;
  }
  ::v-deep .fc-event-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  ::v-deep .fc-timeline-body {
    cursor: pointer;
  }

  ::v-deep .fc-timeline-body td,
  ::v-deep .fc-scroller-harness td {
    height: 60px !important; /* 使用 !important 强制生效 */
  }
  ::v-deep .fc-scrollgrid-sync-table th {
    padding: 10px 0 !important;
  }
}
</style>

