<template>
  <div>
    <CommonDetail type="add" @submitForm="submitForm" class="w-2/3 m-auto"/>
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import { getMeetingFlow,inspectionTime } from "@/api/meeting/staging";
import CommonDetail from "@/views/meeting/components/commonDetail.vue";
export default {
  name: "Sponsor",
  components: { CommonDetail },
  data() {
    return {
      selectCompanyType: false,
      mettingParams: {},
      mettingWebResForm: {},
    };
  },
  watch: {},
  computed: {},
  mounted() {},
  methods: {
    async submitForm(value,webResForm) {
      this.mettingWebResForm=webResForm;
      const {code}= await inspectionTime(value);
      if(code==500){
        return
      }
      this.mettingParams = value;
      this.selectCompanyType = true;
    },
    submitCompany(e) {
      getMeetingFlow({ companyId: e }).then((res) => {
        if (res.code == 200) {
          const data = this.mettingWebResForm;
          sessionStorage.setItem(
            "oa-metting",
            JSON.stringify(data)
          );
          this.selectCompanyType = false;
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              metting: true,
            },
          });
        }
      });
    },
    closeCompany() {
      this.selectCompanyType = false;
    },
  },
};
</script>
<style lang="less" scoped>
</style>

