<template>
  <div class="content">
    <div class="content_left">
      <div class="first">
        <div
          v-for="(item, index) in operateList"
          :key="index"
          @click="operation(item)"
        >
          <i :class="[item.icon]" class="icon"></i>
          <div>{{ item.name }}</div>
        </div>
      </div>
      <div class="two">
        <div class="two_title">{{ month }}月会议统计</div>
        <div class="two_content">
          <div>即将参加会议(场)</div>
          <div class="nums">{{ meetingNum.soonMonthAttendNum }}</div>
        </div>
        <div class="two_content_has">
          <div class="two_content_title">
            <div>已参加会议(场)</div>
            <div class="nums">{{ meetingNum.alreadyMonthAttendNum }}</div>
          </div>
          <div
            style="
              height: 1px;
              background: rgba(218, 213, 213, 0.555);
              margin: 10px 0;
            "
          ></div>
          <div
            v-for="(item, index) in statusColorList"
            :key="index"
            class="two_content_title two_content_title_num"
          >
            <div>{{ item.label }}</div>
            <div class="nums">{{ meetingNum[item.num] }}</div>
          </div>
        </div>
      </div>
      <div class="two">
        <div class="two_title">本年会议</div>
        <div class="two_content">
          <div>已参与会议(场)</div>
          <div class="nums">{{ meetingNum.alreadyYearAttendNum }}</div>
        </div>
      </div>
    </div>
    <div class="content_right">
      <div class="content_status">
        <div v-for="(item, index) in statusColorList" :key="index" class="item">
          <div :style="{ background: item.value }" class="title"></div>
          <div>{{ item.label }}</div>
        </div>
      </div>
      <el-date-picker v-model="stagingTime" type="date" class="content_date">
      </el-date-picker>
      <el-tooltip
        class="content_search"
        effect="dark"
        content="刷新"
        placement="top"
      >
        <el-button
          size="mini"
          circle
          icon="el-icon-refresh"
          @click="refresh()"
        />
      </el-tooltip>
      <el-calendar v-model="stagingTime">
        <template #dateCell="{ data, date }">
          <!--  date   单元格代表的日期  data { type, isSelected, day}，type 表示该日期的所属月份，可选值有 prev-month，current-month，next-month；isSelected 标明该日期是否被选中；day 是格式化的日期，格式为 yyyy-MM-dd-->
          <div style="text-align: center">{{ data.day }}</div>
          <el-scrollbar @click.native="sponsor(data)" style="height: 70px">
            <div
              v-for="(item, index) in stagList"
              :key="index"
              @click.stop="sponsor(data, item)"
            >
              <div
                v-if="data.day == item.meetingStartDay"
                :style="{ background: statusColor[item.attendState] }"
                class="date_item"
              >
                {{ item.meetingTheme }}
              </div>
            </div>
            <div class="h-6"></div>
          </el-scrollbar>
        </template>
      </el-calendar>
    </div>
  </div>
</template>

<script>
import config from "./components/config";
import moment from "moment";
import { meetingMonthList } from "@/api/meeting/staging";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

export default {
  name: "Staging",
  components: {},
  data() {
    return {
      ...config,
      stagingTime: new Date(),
      stagList: [],
      meetingNum: {
        coming: "0",
        organized: "1",
        participated: "2",
        Participated: "3",
      },
    };
  },
  watch: {},
  computed: {
    month() {
      this.getStagList();
      return this.stagingTime.getMonth() + 1;
    },
  },
  mounted() {
    this.operateList = this.operateListInit.filter((item) =>
      checkPermi(item.permi)
    );
    this.init();
  },
  methods: {
    init() {
      this.getStagList();
    },
    refresh() {
      this.init();
    },
    async getStagList() {
      const params = {
        monthCondition: moment(this.stagingTime).format("YYYY-MM"),
        meetingState: 6,
      };
      const { data } = await meetingMonthList(params);
      this.meetingNum = { ...data };
      this.stagList = data.monthList;
    },

    operation(value) {
      const obj = {
        发起会议: this.sponsor,
        会议查询: this.meetingSerch,
        会议安排: this.meetingArrangement,
      };
      obj[value.name]();
    },
    async sponsor(data, item) {
      if (item) {
        this.$router.push({
          path: `/meetingOther/meetingDetails/${item.id}`,
        });
      } else {
        data = data || { day: moment(new Date()).format("YYYY-MM-DD") };
        const inputDate = new Date(data.day);
        // 获取今天的日期
        const today = new Date();
        // 清除今天日期中的时分秒，确保只比较年月日
        today.setHours(0, 0, 0, 0);
        inputDate.setHours(0, 0, 0, 0);
        if (inputDate < today) return;
        const sponsorTime = this.roundUpToNextHalfHour(data?.day);
        this.$alert(
          `<div>召开时间: ${sponsorTime}</div>`,
          "会议室预约/会议安排",
          {
            confirmButtonText: "发起会议",
            cancelButtonText: "取消",
            dangerouslyUseHTMLString: true,
            showCancelButton: true,
            showConfirmButton:checkPermi(["staging:add"])
          }
        ).then(() => {
          this.$router.push({
            path: "/meetingOther/sponsor",
            query: {
              time: sponsorTime,
            },
          });
        });
      }
    },
    meetingSerch() {
      this.$router.push({
        path: "/meeting/search",
      });
    },
    meetingArrangement() {
      this.$router.push({
        path: "/meeting/arrangement",
      });
    },
    roundUpToNextHalfHour(time) {
      const date = time
        ? new Date(`${time} ${this.$format(new Date(), "HH:mm")}`)
        : new Date();
      const minutes = date.getMinutes();
      const remainder = minutes % 30;
      // 如果分钟数小于30分钟，则向上取整到30分钟
      // 如果分钟数大于等于30分钟，则向上取整到下一个小时的0分钟
      const minutesToAdd = remainder === 0 ? 0 : 30 - remainder;
      // 创建一个新的日期对象，避免修改原始日期
      const newDate = new Date(date.getTime());
      newDate.setMinutes(minutes + minutesToAdd);
      newDate.setSeconds(0); // 将秒数设置为0
      newDate.setMilliseconds(0); // 将毫秒设置为0
      return this.$format(newDate, "yyyy-MM-dd HH:mm");
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  .content_left {
    width: 20%;
    padding: 0px 30px 0px 10px;
    font-size: 14px;
    .first {
      margin: 40px 0;
      width: 100%;
      font-weight: 600;
      display: flex;
      justify-content: space-around;
      border: 1px solid rgb(199, 194, 194);
      border-radius: 5px;
      padding: 10px 0;
      > div {
        display: flex;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        .icon {
          font-size: 30px;
          font-weight: 800;
          margin: auto;
        }
        .el-icon-bank-card {
          color: rgb(226, 162, 42);
        }
        .el-icon-search {
          color: rgb(134, 190, 134);
        }
        .el-icon-notebook-2 {
          color: rgb(112, 183, 211);
        }
      }
    }

    .two {
      margin-bottom: 50px;
      .two_title {
        font-weight: 600;
        margin-bottom: 10px;
      }
      .two_content {
        display: flex;
        justify-content: space-between;
        border: 1px solid rgb(199, 194, 194);
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 30px;
        .nums {
          color: rgb(0, 217, 255);
        }
        .two_content_title {
          width: 100%;
          display: flex;
          justify-content: space-between;
        }
      }
      .two_content_has {
        padding: 10px;
        border: 1px solid rgba(199, 194, 194, 0.753);
        border-radius: 5px;
        .two_content_title {
          width: 100%;
          display: flex;
          justify-content: space-between;
        }
        .two_content_title_num {
          font-size: 12px;
          margin: 10px 0;
        }
        .nums {
          color: rgb(0, 217, 255);
        }
      }
    }
  }
  .content_right {
    width: 80%;
    position: relative;
    .content_status {
      display: flex;
      position: absolute;
      left: 2%;
      top: 2%;
      .item {
        margin-right: 20px;
        display: flex;
        .title {
          width: 10px;
          height: 10px;
          margin-right: 5px;
          position: relative;
          top: 5px;
        }
      }
    }
    .content_search {
      position: absolute;
      top: 1.5%;
      right: 1%;
    }
    .content_date {
      position: absolute;
      top: 1%;
      left: 50%;
      transform: translateX(-50%);
      width: 150px;
    }
    .date_item {
      color: white;
      margin-bottom: 2px;
      padding-left: 2px;
      border-radius: 3px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    ::v-deep .el-calendar__title {
      opacity: 0;
    }
    ::v-deep .el-calendar-table__row {
      height: 100px;
    }
    ::v-deep .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    ::v-deep .el-button-group {
      position: relative;
      right: 50px;
    }
  }
}
</style>

