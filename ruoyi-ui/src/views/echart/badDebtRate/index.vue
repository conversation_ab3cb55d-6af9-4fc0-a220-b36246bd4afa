<template>
  <div style="height:2000px; " >

          <div style="height:20px;"></div>
          <div style="margin-left: 15px">
            <div style="color:#999999;margin-bottom:15px;font-size:13px;margin-left: 15px">
              本页面展示利润测算坏账参数设置中已配置的坏账率情况<br/>
              1.如果选择自动计算<br/>
              有追偿数据时：年化坏账率 = （月代偿 - 月追回）÷月初本金在贷余额（资金方口径）×12<br/>
              无追偿数据时：年化坏账率  = （每月代偿金额 ÷ 月初本金在贷余额）× A ×12<br/><br/>
              2.如果选择手动计算，年化坏账率为输入值<br/>
            </div>
                <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
                      <el-form-item label="系统" prop="platformNo">
                                <el-select v-model="platformNoParam" placeholder="请选择" filterable multiple size="medium"
                                          >
                                  <el-option
                                    v-for="dict in platformNoSelect"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                      </el-form-item>
                      <el-form-item label="合作方" prop="partnerNo">
                              <el-select v-model="partnerNoParam" placeholder="请选择" filterable multiple size="medium"
                                        >
                                <el-option
                                  v-for="dict in partnerNoSelect"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                      </el-form-item>
                    <el-form-item label="资金方" prop="fundNo">
                            <el-select v-model="fundNoParam" placeholder="请选择" filterable multiple size="medium"
                                      >
                              <el-option
                                v-for="dict in fundNoSelect"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                              />
                            </el-select>
                    </el-form-item>
                  <el-form-item label="担保公司" prop="custNo">
                    <el-select v-model="custNoParam" placeholder="请选择" filterable multiple size="medium"
                               >
                      <el-option
                        v-for="dict in custNoSelect"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="统计月份">
                          <el-date-picker
                            style="width: 205px"
                            v-model="queryParams.reconYear"
                            value-format="yyyy-MM"
                            type="month"
                             :picker-options="pickerOptions"
                            placeholder="选择月">
                          </el-date-picker>
                  </el-form-item>
                  <MoreSearch modelCode="ECHARTS" :params="queryParams" v-show="showMoreSearch" byId="companyCode"></MoreSearch>
                    <el-form-item>
                      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                      <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                      <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
                        >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
                      >
                    </el-form-item>
                </el-form>
            </div>
        <!-- </el-col>
    </el-row> -->
    <div style="height:30px;  "></div>
    <div style="height:30px;  text-align: center;font-weight:bold;">
          <span style="line-height:30px">年化坏账率</span>
    </div>

    <!-- <el-row type="flex" :gutter="20">
      <el-col :span="1"> </el-col>
      <el-col :span="23"> </el-col>
    </el-row> -->
    <div style="height:10px;"></div>

        <div style="height:1200px;">
<!--          <div style="width: 20px;height:1000px; float:left;"></div>-->
            <div style="width: 750px;height:1000px; float:left;margin-left: 110px">
                  <div v-loading="loading" id="charts3"  style="width: 850px;height:800px; float:right;">
                  </div>
            </div>
            <!-- <div style="width: 200px;height:1000px; float:left;">

            </div> -->
            <div  style="width: 500px;height:610px; float:right;margin-right: 120px">
          <!--  --><div style="width: 500px;height:50px; float:right"></div>
                 <div v-loading="loading" style="width: 500px; float:right">
                    <el-table
                      :data="tableData"
                      border
                      height="800"
                      :header-cell-style="{fontSize: '14px'}"
                      style="width: 100%;font-size: 14px">
                      <el-table-column
                        prop="partnerNo"
                        label="合作方"
                        width="150">
                          <template slot-scope="scope">
                              <dict-tag :options="dict.type.partner_no" :value="scope.row.partnerNo"/>
                            </template>
                      </el-table-column>
                      <el-table-column
                        prop="fundNo"
                        label="资金方"
                        width="230">
                         <template slot-scope="scope">
                            <dict-tag :options="dict.type.fund_no" :value="scope.row.fundNo"/>
                          </template>
                      </el-table-column>
                      <el-table-column
                        prop="dataa"
                        label="坏账率"
                        >
                        <template slot-scope="scope">
                          {{scope.row.dataa}}%
                        </template>
                      </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>


  </div>
</template>
<script>
import * as echarts from 'echarts';
import { getDicts} from "@/api/system/dict/data";
import {badDabtData,badDebtDataDate} from '@/api/system/echarts'
//  start 引用联级字典查询
import {getSysDictRefList,getSelectSysDictRefList} from '@/api/ref/ref'
import { clone } from "xe-utils";

//  end 引用联级字典查询
export default {
   name: 'BadDebtRate',
  dicts: ['sys_platform_code', 'product_no', 'fund_no', 'partner_no', 'platform_no', 'cust_no'],
  data() {
    return {
      //X轴数据
      fundxaxis:[],
      //柱状图数据
      fundpillarData:[],
      //饼图数据
      fundpieData:[],
      //表格数据
      tableData:[],
      loginUserRole:"",
      //  start 新增参数
      platformNoParam: '',
      custNoParam: '',
      partnerNoParam: '',
      fundNoParam: '',
      productNoParam: '',


      sysDictRefParam: {
        dictType: '',
        dictValue: '',
        pDictType: '',
        pDictValue: '',
        selectDictDatas:''
      },
      platformNoSelect: [],
      custNoSelect: [],
      fundNoSelect: [],
      partnerNoSelect: [],
      productNoSelect: [],
      // end 新增参数

      //外部系统
      externalsystems:[],
      //担保公司编码
      dbcompany:[],
      //合作方
      partnerdata:[],
      //资金方
      capitaldata:[],
      querydatatype:"cust_no",
      externalsystem:"platform_no",
      partnerscode:"partner_no",
      capitalcode:"fund_no",
      productcode:"product_no",

        // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
       // 表单参数
      form: {},
       // 查询参数
      queryParams: {
         pageNum: 1,
        pageSize: 10,
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        statisticalIndex: null,
        reconYear: null,
        dataJanuary: null,
        dataFebruary: null,
        dataMarch: null,
        dataApril: null,
        dataMay: null,
        dataJune: null,
        dataJuly: null,
        dataAugust: null,
        dataSeptember: null,
        dataOctober: null,
        dataNovember: null,
        dataDecember: null,
        yearTotal: null,
        status: null,
        params: {
         moduleTypeOfNewAuth: 'ECHARTS',
        }
      },
       pickerOptions: {
         disabledDate(time) {
           var threeDate = new Date();
            threeDate.setMonth(threeDate.getMonth() - 1);
            return time.getTime() > threeDate.getTime();
          },
          // disabledDate(time) {
          //   var threeDate = new Date();
          //   threeDate.setMonth(threeDate.getMonth() - 1);
          //   return  time.getMonth>threeDate.setMonth;
          // }

        },
        showMoreSearch:false
    };
  }, mounted(){

    //  start 页面刷新时对数据的处理

    // this.initSelect()
     this.initSelectData()
    //  end 页面刷新时对数据的处理

    this.getexternalsystem();
    this.getdbcompany();
    this.getpartner();
    this.getcapital();
     this.getList();
//  this.getLoginUserRole();
  },
  methods:{

     //wzy渲染下拉框
     initSelectData() {
      getSelectSysDictRefList({ unitType: 4, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
     getCustNoList(val) {
      const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
      this.queryParams.platformNo = this.platformNoParam.toString()
      if (val == null || val === '' || flag) {
        this.custNoSelect = null
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.custNo = null
        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.custNoParam = null
        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {

            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getPartnerNoList(val) {
      const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
      this.queryParams.custNo = this.custNoParam.toString()
      if (val == null || val === '' || flag) {
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getFundNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
      this.queryParams.partnerNo = this.partnerNoParam.toString()
      if (val == null || val === ''|| flag) {
        this.fundNoSelect = null
        this.productNoSelect = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
         this.sysDictRefParam.selectDictDatas =""

      if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
      else {
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
        this.sysDictRefParam.selectDictDatas =""
         if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getProductNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

      this.queryParams.fundNo = this.fundNoParam.toString()
      if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo
this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo

this.sysDictRefParam.selectDictDatas =""
        if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            // this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
      getProductNoValue(val) {
      const flag = this.lateByte(this.queryParams.productNo) > this.lateByte(val.toString())

      this.queryParams.productNo = this.productNoParam.toString()

       if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        this.productNoParam = null
        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue =''
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue = this.queryParams.productNo

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }

    },
     lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != '/r') {
          nStrLength++;
        }
      }
      return nStrLength;
    },
    //end




 /** 查询数据 */
   async getList() {

      this.loading = true;
      if(!this.queryParams.reconYear){
         await badDebtDataDate().then(response=>{
        this.queryParams.reconYear = response.date
      })

      }
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
      const params=clone(this.queryParams,true);
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
        badDabtData(params).then(response => {

          //echart数据
           this.fundxaxis = response.xaxisData;
           this.fundpillarData  = response.dataList;

           this.tableData = response.tableDataList;
            //柱状图
            this.partnerPillarEChart();
            //饼图
            // this.partnerpie();

          this.loading = false;
        }
      );
    },
 /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
         //  start 重置逻辑更新
      this.resetForm('queryForm')

      this.platformNoParam = ''
      this.custNoParam = ''
      this.partnerNoParam = ''
      this.fundNoParam = ''
      this.queryParams.moreSearch=undefined;
      this.handleQuery()
      this.initSelectData()
      //  end 重置逻辑更新
    },
     // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map(item => item.id)
    //   this.single = selection.length !== 1
    //   this.multiple = !selection.length
    // },
      //获取外部系统平台编码
    getexternalsystem(){
        getDicts(this.externalsystem).then(response =>{
            this.externalsystems = response.data;
        } );
    },
    //获取担保公司编码
    getdbcompany(){
        getDicts(this.querydatatype).then(response =>{
            this.dbcompany = response.data;
        } );
    },
    //获取合作方编码
    getpartner(){
        getDicts(this.partnerscode).then(response =>{
            this.partnerdata = response.data;
        } );
    },
    //获取资金方编码
    getcapital(){
        getDicts(this.capitalcode).then(response =>{
            this.capitaldata = response.data;
        } );
    },
     partnerPillarEChart(){
            var myChart = this.$echarts.init(document.getElementById('charts3'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50
                       },
                       grid: {
                              x: 240,
                              y: 100,
                              x2: 100,
                              y2: 120,
                              borderWidth: 1,
                            },
                        // title: {
                        //      left: 'left',
                        //     text: '各资金方在贷余额分布',
                        //     padding: [60, 0,0, 0],
                        //       textStyle: {
                        //       fontSize: 18,
                        //       color: '#333333',
                        //       fontWeight: "normal"
                        //       }
                        // },

              color: ['#F15A75'],
                         xAxis: {
                            type: 'value',
                            name: "单位（百分比）",
                        },
                        yAxis: {

                            type: 'category',
                            data: this.fundxaxis
                        },

                        series:  [
                          {
                          type:  'bar',
                          data : this.fundpillarData
                          }
                        ]
             };
              myChart.clear()
            myChart.setOption(option);

  },
     partnerpie(){
             var myChart = this.$echarts.init(document.getElementById('charts3'));
             var option={

                  toolbox: {
                  show: true,
                  feature: {
                        dataView: { readOnly: false },
                        restore: {},
                        saveAsImage: {},
                  }
                },

                tooltip: {
                  trigger: 'item',
                  formatter: "{b} : {c}({d}%)"
                },
                  legend: {
                          bottom: '150',

                    },
                 series:[
                    {

                        type: 'pie',
                        avoidLabelOverlap: true,
                        radius: '50%',
                        label: {
                          normal:{
                            show: true,
                            color: '#666666'
                            // position: 'center'
                          }
                         },
                         emphasis: {//选中的样式
                            borderColor: 'rgba(0,0,0,0)',
                            borderWidth: 1,
                            label: {
                                show: true,//选中时不显示数据标签,

                            },
                            labelLine: {
                                show: true,//选中时不显示数据标签引导线
                                // length: 50,
                                lineStyle: {
                                    width: 1,
                                     color: '#ff8000',
                                    type: 'solid',

                                }
                            }
                        },
                    center: ['50%', '40%'],
                      labelLine: {//设置延长线的长度
                          normal: {
                              length: 30,//设置延长线的长度
                              length2: 50,//设置第二段延长线的长度
                          }
                      },
                        data:this.fundpieData
                    }
                 ]

             };

              myChart.clear()
            myChart.setOption(option);
        },

  },

}
</script>
<style>
.el-row {
    margin-bottom: 20px;
  }
   .grid-content {
    /* border-radius: 10px;
    height: 50px;
    line-height: 14px; */
    color:#9D9D9D;
    /* font-weight:bold; */
    font-size:14px;
    text-align: center;
  }
  .grid-contentfont{
      color:#333333;
    font-weight:bold;
    font-size:14px;
  }
  .grid-contentcol{
    height: 20px;
    line-height: 30px;
    left: 30px;
  }
   .grid-col1 {
    border-radius: 4px;
    height: 36px;

  }
  .bg-purple {
    background: #9D9D9D;
  }
  #col-line {
      float: left;
      width: 1px;
      height: 60px;
      background: 	#E6E6E6;
    }

.span{
    color:#ff8000;
    font-weight:bold;
    font-size:28px;
  }
.spancol{
    color:#333333;
    font-weight:bold;
    font-size:20px;
    display: inline-block;
    padding-top:10px;

}
.spancol2{
    font-size:14px;
    color:#9D9D9D;
    display:block;
}
.echartspan{
  color: 	#007fff;
  font-size:12px;
  font-weight:bold;
  margin-left: 100px;
}
.balancediv{
  width: 100px;
  height:35px;
}
.item {
      margin: 4px;
    }

.spanfont {
  color: 	#333333;
  font-size:18px;
  font-weight:normal;
  font-family:"Microsoft YaHei";
}
</style>



