<template>
  <div class="app-container">
    <div style="color: #a1a3a6; font-size: 12px">
      FPD10+ 指标：项目上线至D-42日期间首逾10天(含)以上的借据的累积贷款本金总额
      / 截止D-42日之前累计放款总额
    </div>
    <div style="color: #a1a3a6; font-size: 12px">
      数据更新时间：动态保证金比例每周一上午9点更新，更新日为D日，不区分是否节假日，都需跑批数据进行更新
    </div>
    <div style="color: #a1a3a6; font-size: 12px">
      统计时间维度：项目上线至D-42日期间放款的借据
    </div>
    <div style="color: #a1a3a6; font-size: 12px">
      只看实际是否逾期，不看是否代偿，逾期后已还款用户不计入统计，只看D日统计时的用户逾期状态
    </div>
    <div style="color: #a1a3a6; margin-bottom: 15px; font-size: 12px">
      企业整体业务的累计放款总额，从经营这项业务开始累积
    </div>

    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="125px"
    >
      <el-form-item label="系统" prop="platformNo">
        <el-select
          v-model="platformNoParam"
          placeholder="请选择系统名称"
          filterable
          multiple
          size="small"
          
        >
          <el-option
            v-for="dict in platformNoSelect"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="担保公司" prop="custNo">
        <el-select
          v-model="custNoParam"
          placeholder="请选择担保公司"
          filterable
          multiple
          size="small"
          
        >
          <el-option
            v-for="dict in custNoSelect"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合作方" prop="partnerNo">
        <el-select
          v-model="partnerNoParam"
          placeholder="请选择合作方"
          filterable
          multiple
          size="small"
          
        >
          <el-option
            v-for="dict in partnerNoSelect"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="统计截至时间" prop="reconDate">
        <el-date-picker
          v-model="queryParams.reconDate"
          size="small"
          style="width: 208px"
          value-format="yyyy-MM"
          type="month"
          range-separator="-"
          placeholder="请选择月份"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          :disabled="
            !this.platformNoParam &&
            !this.custNoParam &&
            !this.partnerNoParam &&
            !this.queryParams.reconDate
          "
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div
      v-show="show1"
      style="
        width: 70vw;
        height: 50vh;
        display: flex;
        justify-content: center;
        align-items: center;
      "
    >
      <span style="color: #cccccc; font-size: 14px">{{ showDivFont }}</span>
    </div>
    <div v-show="!show1" id="main" style="width: 70vw; height: 50vh"></div>

    <div style="display: flex;width: 100%;overflow: auto;" v-if="distributionListRecoed.length > 0">
      <div style="width: 200px;flex-shrink: 0;">
        <div class="table_item"></div>
        <div class="table_item">FPD10+</div>
        <div class="table_item">逾期10+累计本金</div>
        <div class="table_item">平台累计放款</div>
      </div>
      <div
        style="width: 200px;flex-shrink: 0;"
        v-for="(item, index) in distributionListRecoed"
        :key="index"
      >
        <div class="table_item">{{ item.totalWeek }}</div>
        <div class="table_item">{{ item.fpd10Rate + "%" }}</div>
        <div class="table_item">
          {{ formaterMoney(item.totalFpd10PrintAmount) }}
        </div>
        <div class="table_item">{{ formaterMoney(item.totalAmount) }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  listDistribution,
  getDistribution,
  delDistribution,
  addDistribution,
  updateDistribution,
  dataMapping,
} from "@/api/data/distribution";
import { getSysDictRefList, getSelectSysDictRefList } from "@/api/ref/ref";
import { list } from "@/api/overdue/overdue";

export default {
  name: "Overdue",
  dicts: [
    "is_mapping",
    "platform_no",
    "fund_no",
    "product_no",
    "cust_no",
    "partner_no",
  ],
  data() {
    return {
      distributionListRecoed: [],
      /** 限制日期选择框，不允许选择超过当前日期 */
      pickerOptions: {
        disabledDate(time) {
          const FullYear = time.getFullYear();
          let myDate = new Date();
          // const Month = time.getMonth() + 1
          if (FullYear < 2000) {
            return true;
          } else {
            let t = myDate.getDate();
            // 如果想包含本月本月 - 8.64e7 * t 就不需要了，
            // 如果想之前的不能选择把 > 换成 <
            return time.getTime() > Date.now();
          }
          // return false
        },
      },
      // originData 为后端原始正常的数据, 此数据按正常表格展示 一行一行的数据
      // 保证数组里每一个对象中的字段顺序, 从上到下 一次对应显示表格中的从左到右
      originData: [
        {
          type: "选择题",
          num: "5题",
          average: "3分/题",
        },
        {
          type: "填空题",
          num: "5题",
          average: "3分/题",
        },
        {
          type: "选择题",
          num: "2题",
          average: "10分/题",
        },
      ],
      originTitle: [], // originTitle 该标题为 正常显示的标题, 数组中的顺序就是上面数据源对象中的字段标题对应的顺序
      transTitle: [], // transTitle 该标题为转化后的标题, 注意多一列,  因为原来的标题变成了竖着显示了, 所以多一列标题, 第一个为空即可
      transData: [],
      mymax: 25,
      //外部系统
      platforms: [],
      //合作方
      partners: [],

      custNoParam: "",
      custNoSelect: [],

      querydatatype: "cust_no",
      externalsystem: "platform_no",
      partnerscode: "partner_no",
      capitalcode: "fund_no",
      productcode: "product_no",

      platformNoParam: "",
      partnerNoParam: "",

      sysDictRefParam: {
        dictType: "",
        dictValue: "",
        pDictType: "",
        pDictValue: "",
        selectDictDatas: "",
      },
      platformNoSelect: [],
      partnerNoSelect: [],

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 外部系统平台余额分布表格数据
      distributionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        reconDate: null,
        productNo: null,
        loanMonth: null,
        reconMonth: null,
        loanBalanceAmount: null,
        loanRemainNumber: null,
        balanceDistributionType: null,
        mNumber: null,
        mBalanceAmount: null,
        isMapping: null,
        params: {
         moduleTypeOfNewAuth: 'ECHARTS',
        }
      },

      myoption: {},
      myCharts: null,
      totalFpd10PrintAmount: [],
      totalAmountMoey: [],
      // 表单参数
      form: {},
      showDivFont: "请选择数据筛选条件，点击[搜索]",
      show1: true,
      queryButton: false,
    };
  },
  created() {
    // this.getList();
    this.initSelectData();
  },
  mounted() {
    this.platformNoParam = "";
    this.partnerNoParam = "";
    this.custNoParam = "";
    this.createEchart();
    window.onresize = () => {
      this.myCharts.resize();
    };
  },
  methods: {
    getPartnerNoList(val) {
      const flag =
        this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString());
      this.queryParams.custNo = this.custNoParam.toString();
      if (val == null || val === "" || flag) {
        this.partnerNoSelect = null;
        this.fundNoSelect = null;
        this.productNoSelect = null;

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = "partner_no";
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = "cust_no";
        this.sysDictRefParam.pDictValue = this.queryParams.custNo;
        this.sysDictRefParam.selectDictDatas = "";

        if (
          this.queryParams.platformNo != null &&
          this.queryParams.platformNo != ""
        ) {
          this.sysDictRefParam.selectDictDatas = this.queryParams.platformNo;
        }
        if (this.queryParams.custNo != null && this.queryParams.custNo != "") {
          this.sysDictRefParam.selectDictDatas =
            this.sysDictRefParam.selectDictDatas +
            "_" +
            this.queryParams.custNo;
        }
        if (
          this.queryParams.partnerNo != null &&
          this.queryParams.partnerNo != ""
        ) {
          this.sysDictRefParam.selectDictDatas =
            this.sysDictRefParam.selectDictDatas +
            "_" +
            this.queryParams.partnerNo;
        }
        if (this.queryParams.fundNo != null && this.queryParams.fundNo != "") {
          this.sysDictRefParam.selectDictDatas =
            this.sysDictRefParam.selectDictDatas +
            "_" +
            this.queryParams.fundNo;
        }
        getSelectSysDictRefList(this.sysDictRefParam).then((response) => {
          this.platformNoSelect = response.data.platform_no;
          this.custNoSelect = response.data.cust_no;
          this.fundNoSelect = response.data.fund_no;
          this.partnerNoSelect = response.data.partner_no;
          this.productNoSelect = response.data.product_no;
        });
      } else {
        this.sysDictRefParam.dictType = "partner_no";
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = "cust_no";
        this.sysDictRefParam.pDictValue = this.queryParams.custNo;
        this.sysDictRefParam.selectDictDatas = "";

        if (
          this.queryParams.platformNo != null &&
          this.queryParams.platformNo != ""
        ) {
          this.sysDictRefParam.selectDictDatas = this.queryParams.platformNo;
        }
        if (this.queryParams.custNo != null && this.queryParams.custNo != "") {
          this.sysDictRefParam.selectDictDatas =
            this.sysDictRefParam.selectDictDatas +
            "_" +
            this.queryParams.custNo;
        }
        if (
          this.queryParams.partnerNo != null &&
          this.queryParams.partnerNo != ""
        ) {
          this.sysDictRefParam.selectDictDatas =
            this.sysDictRefParam.selectDictDatas +
            "_" +
            this.queryParams.partnerNo;
        }
        if (this.queryParams.fundNo != null && this.queryParams.fundNo != "") {
          this.sysDictRefParam.selectDictDatas =
            this.sysDictRefParam.selectDictDatas +
            "_" +
            this.queryParams.fundNo;
        }
        getSelectSysDictRefList(this.sysDictRefParam).then((response) => {
          this.platformNoSelect = response.data.platform_no;
          // this.custNoSelect= response.data.cust_no
          this.fundNoSelect = response.data.fund_no;
          this.partnerNoSelect = response.data.partner_no;
          this.productNoSelect = response.data.product_no;
        });
      }
    },

    changeTable() {
      // 数组按矩阵思路, 变成转置矩阵
      let matrixData = this.distributionList.map((row) => {
        let arr = [];
        for (let key in row) {
          arr.push(row[key]);
        }
        return arr;
      });
      // 加入标题拼接最终的数据
      this.transData = matrixData[0].map((col, i) => {
        return [
          this.originTitle[i],
          ...matrixData.map((row) => {
            return row[i];
          }),
        ];
      });
      this.transData[14].reverse();
      this.transData[11].reverse();
      this.transData[12].reverse();
      let sz1 = [...["FPD10+"], ...this.transData[14]];
      let sz2 = [...["逾期10+累计本金"], ...this.transData[11]];
      let sz3 = [...["平台累计放款"], ...this.transData[12]];
      sz1.forEach((item, i) => {
        if (i != 0) {
          if (item == null) {
            sz1[i] = "0.00%";
          } else {
            sz1[i] = item + "%";
          }
        }
      });
      sz2.forEach((item, i) => {
        if (i != 0) {
          // sz2[i]=this.formatEmployment(item,2,'.',',')
          sz2[i] = this.formaterMoney(Number(item));
        }
      });
      sz3.forEach((item, i) => {
        if (i != 0) {
          // sz3[i]=this.formatEmployment(item,2,'.',',')
          sz3[i] = this.formaterMoney(Number(item));
        }
      });
      this.transData = [];
      this.transData.push(sz1);
      this.transData.push(sz2);
      this.transData.push(sz3);
    },
    createEchart() {
      let that = this;
      this.myoption = {
        legend: {
          data: ["逾期10+累计本金", "FPD10+"],
          textStyle: {
            color: "#000000",
            fontSize: 14,
          },
          top: 0,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
          formatter: function (params) {
            let time = params[0].name;
            let lv = params[1].value + "%"; //FPD10+
            return `<div><b>${time}</b><p>FPD10+:${lv}</p><p>逾期10+累计本金:${
              that.totalFpd10PrintAmount[params[0].dataIndex]
            }</p><p>平台累计放款:${
              that.totalAmountMoey[params[0].dataIndex]
            }</p></div>`;
          },
        },
        xAxis: {
          type: "category",
          data: [], //X轴的数据，后端返
        },
        yAxis: [
          {
            type: "value",
            position: "right",
            splitLine: {
              //显示分割线
              show: false,
            },
          },
          {
            type: "value",
            position: "left",
            min: 0,
            max: that.mymax, //控制百分比的最大最小数值(这里获取到最大值进行赋值)
            axisLabel: {
              formatter: "{value}%", //使图变成百分比形式
            },
            splitLine: {
              //网格线显不显示
              show: true,
            },
          },
        ],
        series: [
          {
            data: [], //逾期10+累计本金数据（后端返）
            type: "bar",
            name: "逾期10+累计本金",
            showBackground: false,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            barWidth: 30,
            color: "rgb(237,125,49)",
          },
          {
            data: [], //FPD10+(后端返)
            type: "line",
            name: "FPD10+",
            yAxisIndex: 1,
            symbol: "circle", //拐点样式
            symbolSize: 18, //拐点大小
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 2, //折线宽度
                  color: "blue", //折线颜色
                },
                color: "blue", //拐点颜色
                borderColor: "skyblue", //拐点边框颜色
                borderWidth: 2, //拐点边框大小
              },
              emphasis: {
                color: "blue", //hover拐点颜色定义
              },
            },
            label: {
              show: true,
              position: "top",
              textStyle: {
                color: "#000",
              },
              formatter: (params) => {
                return `${params.value}%`;
              },
            },
          },
        ],
      };
      this.myCharts = this.$echarts.init(document.getElementById("main"));
      this.myCharts.setOption(this.myoption, true);
    },
    getSummaries01(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }

        if (index === 1) {
          sums[index] = this.formaterMoney(Number(this.incomeSum));
        } else if (index === 4) {
          sums[index] = this.formaterMoney(Number(this.feeAmtSum));
        } else if (index === 5) {
          sums[index] = this.formaterMoney(Number(this.feeAmt2Sum));
        } else if (index === 6) {
          sums[index] = this.formaterMoney(Number(this.grossProfitAmtSum));
        } else if (index === 7) {
          sums[index] = this.formaterMoney(Number(this.grossProfitAmt2Sum));
        } else if (index === 8) {
          sums[index] = this.formaterMoney(Number(this.feeAlreadySum));
        } else if (index === 9) {
          sums[index] = this.formaterMoney(Number(this.feeNoAlreadySum));
        } else if (index === 13) {
          sums[index] = this.formaterMoney(Number(this.payAmtSum));
        } else if (index === 14) {
          sums[index] = this.formaterMoney(Number(this.differenceAmtSum));
        }
      });
      return sums;
    },

    // 数值三位以，隔开
    format_number(n) {
      var b = parseInt(n).toString();
      var len = b.length;
      if (len <= 3) {
        return b;
      }
      var r = len % 3;
      return r > 0
        ? b.slice(0, r) + "," + b.slice(r, len).match(/\d{3}/g).join(",")
        : b.slice(r, len).match(/\d{3}/g).join(",");
    },

    //金额格式化
    formatEmployment(number, decimals, dec_point, thousands_sep) {
      number = (number + "").replace(/[^0-9+-Ee.]/g, "");
      var n = !isFinite(+number) ? 0 : +number,
        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
        sep = typeof thousands_sep === "undefined" ? "," : thousands_sep,
        dec = typeof dec_point === "undefined" ? "." : dec_point,
        s = "",
        toFixedFix = function (n, prec) {
          var k = Math.pow(10, prec);
          return "" + Math.floor(n * k) / k;
        };
      s = (prec ? toFixedFix(n, prec) : "" + Math.floor(n)).split(".");
      var re = /(-?\d+)(\d{3})/;
      while (re.test(s[0])) {
        s[0] = s[0].replace(re, "$1" + sep + "$2");
      }

      if ((s[1] || "").length < prec) {
        s[1] = s[1] || "";
        s[1] += new Array(prec - s[1].length + 1).join("0");
      }
      return s.join(dec);
    },
    /** 格式化金额 */
    formaterMoney(data) {
      if (data === "0.00") return data;
      if (data === 0) return "0.00";
      if (!data) return "-";
      if (data === "-") return "-";
      if (data === "您的业务流程有误，请再次核对！") return "数据错误！";
      // 将数据分割，保留两位小数
      data = data.toFixed(2);
      // 获取整数部分
      const intPart = Math.trunc(data);
      // 整数部分处理，增加,
      const intPartFormat = intPart
        .toString()
        .replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
      // 预定义小数部分
      let floatPart = ".00";
      // 将数据分割为小数部分和整数部分
      const newArr = data.toString().split(".");
      if (newArr.length === 2) {
        // 有小数部分
        floatPart = newArr[1].toString(); // 取得小数部分
        if (1 / intPart < 0 && intPart === 0) {
          return "-" + intPartFormat + "." + floatPart;
        }
        return intPartFormat + "." + floatPart;
      }
      if (1 / intPart < 0 && intPart === 0) {
        return "-" + intPartFormat + "." + floatPart;
      }
      return intPartFormat + floatPart;
    },
    //获取外部系统平台编码
    getexternalsystem() {
      getDicts(this.externalsystem).then((response) => {
        this.platforms = response.data;
      });
    },
    //获取合作方编码
    getpartner() {
      getDicts(this.partnerscode).then((response) => {
        this.partners = response.data;
      });
    },

    //wzy渲染下拉框
    initSelectData() {
      getSelectSysDictRefList({ unitType: 4,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
    getCustNoList(val) {
      const flag =
        this.lateByte(this.queryParams.platformNo) >
        this.lateByte(val.toString());
      this.queryParams.platformNo = this.platformNoParam.toString();
      if (val == null || val === "" || flag) {
        this.partnerNoSelect = null;

        this.sysDictRefParam.dictType = "cust_no";
        this.sysDictRefParam.pDictType = "platform_no";
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo;
        this.sysDictRefParam.selectDictDatas = "";
        if (
          this.queryParams.platformNo != null &&
          this.queryParams.platformNo != ""
        ) {
          this.sysDictRefParam.selectDictDatas = this.queryParams.platformNo;
        }

        getSelectSysDictRefList(this.sysDictRefParam).then((response) => {
          // this.platformNoSelect= response.data.platform_no;
          this.partnerNoSelect = response.data.partner_no;
          this.custNoSelect = response.data.cust_no;
        });
      } else {
        this.sysDictRefParam.dictType = "cust_no";
        this.sysDictRefParam.pDictType = "platform_no";
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo;
        this.sysDictRefParam.selectDictDatas = "";
        if (
          this.queryParams.platformNo != null &&
          this.queryParams.platformNo != ""
        ) {
          this.sysDictRefParam.selectDictDatas = this.queryParams.platformNo;
        }

        getSelectSysDictRefList(this.sysDictRefParam).then((response) => {
          // this.platformNoSelect= response.data.platform_no;
          this.partnerNoSelect = response.data.partner_no;
          this.custNoSelect = response.data.cust_no;
        });
      }
    },
    getFundNoList(val) {
      const flag =
        this.lateByte(this.queryParams.partnerNo) >
        this.lateByte(val.toString());
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      if (val == null || val === "" || flag) {
        this.sysDictRefParam.dictType = "fund_no";
        this.sysDictRefParam.pDictType = "partner_no";
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo;
        this.sysDictRefParam.selectDictDatas = "";

        if (
          this.queryParams.partnerNo != null &&
          this.queryParams.partnerNo != ""
        ) {
          this.sysDictRefParam.selectDictDatas =
            "_" + this.queryParams.partnerNo;
        }
        if (this.queryParams.custNo != null && this.queryParams.custNo != "") {
          this.sysDictRefParam.selectDictDatas =
            this.sysDictRefParam.selectDictDatas +
            "_" +
            this.queryParams.custNo;
        }
        // if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
        //   this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        // }
        getSelectSysDictRefList(this.sysDictRefParam).then((response) => {
          this.platformNoSelect = response.data.platform_no;
          // this.partnerNoSelect= response.data.partner_no
          this.custNoSelect = response.data.cust_no;
        });
      } else {
        this.sysDictRefParam.dictType = "fund_no";
        this.sysDictRefParam.pDictType = "partner_no";
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo;
        this.sysDictRefParam.selectDictDatas = "";

        if (
          this.queryParams.partnerNo != null &&
          this.queryParams.partnerNo != ""
        ) {
          this.sysDictRefParam.selectDictDatas =
            "_" + this.queryParams.partnerNo;
        }
        if (this.queryParams.custNo != null && this.queryParams.custNo != "") {
          this.sysDictRefParam.selectDictDatas =
            this.sysDictRefParam.selectDictDatas +
            "_" +
            this.queryParams.custNo;
        }

        getSelectSysDictRefList(this.sysDictRefParam).then((response) => {
          this.platformNoSelect = response.data.platform_no;
          this.custNoSelect = response.data.cust_no;
          // this.partnerNoSelect= response.data.partner_no
        });
      }
    },

    /** 查询外部系统平台余额分布列表 */
    getList() {
      this.loading = true;
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      // this.queryParams.fundNo = this.fundNoParam.toString();
      // this.queryParams.productNo = this.productNoParam.toString();
      list(this.queryParams).then((response) => {
        if (response.rows.length > 0) {
          this.showDivFont = "";
          this.show1 = false;
          this.distributionList = response.rows;
          let arr = [];
          this.distributionList.map((item) => {
            arr.unshift(item);
          });
          this.distributionListRecoed = [...arr];
          let week = [];
          let totalAmount = [];
          let FPD10lv = [];
          let totalFpd10PrintAmount = [];
          this.distributionList.forEach((item) => {
            week.push(item.totalWeek);
            totalAmount.push(item.totalAmount);
            FPD10lv.push(Number(item.fpd10Rate).toFixed(2));
            totalFpd10PrintAmount.push(item.totalFpd10PrintAmount);
          });
          this.totalFpd10PrintAmount = totalFpd10PrintAmount.reverse();
          this.totalAmountMoey = totalAmount.reverse();
          this.myoption.xAxis.data = week.reverse(); //x轴
          let weeks = JSON.parse(JSON.stringify(week));
          weeks.forEach((item, i) => {
            weeks[i] = item;
          });
          weeks.unshift("");
          this.transTitle = weeks;
          this.myoption.series[0].data = totalFpd10PrintAmount; //逾期10+累计本金
          this.myoption.series[1].data = FPD10lv.reverse(); //FPD10+
          let newarr = JSON.parse(JSON.stringify(FPD10lv));
          let max = newarr.sort();
          this.mymax = Math.floor(max[FPD10lv.length - 1]) + 1;
          this.myoption.yAxis[1].max = Math.floor(max[FPD10lv.length - 1]) + 1;
          if (this.myCharts) {
            this.myCharts.clear();
          }
          this.myCharts.setOption(this.myoption, true);
          this.total = response.total;
          this.loading = false;
          this.changeTable();
        } else {
          this.showDivFont = "由于该产品无追偿数据，无法统计首逾10天本金";
          this.show1 = true;
          this.distributionListRecoed = []
        }
      });
    },
    mappingData() {
      this.loading = true;
      dataMapping().then((response) => {
        this.handleQuery();
        this.loading = false;
      });
    },
    bindParamByPlatformNo(val) {
      this.sysDictRefParam.dictType = "platform_no";
      this.sysDictRefParam.dictValue = val;
      this.sysDictRefParam.pDictType = "TOP";
      this.sysDictRefParam.pDictValue = "TOP";
    },
    getPlatformNoList() {
      getSysDictRefList(this.sysDictRefParam).then((response) => {
        this.platformNoSelect = response.data;
      });
    },

    lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != "/r") {
          nStrLength++;
        }
      }
      return nStrLength;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        loanMonth: null,
        reconMonth: null,
        loanBalanceAmount: null,
        loanRemainNumber: null,
        balanceDistributionType: null,
        mNumber: null,
        mBalanceAmount: null,
        isMapping: null,
        remark: null,
        createTime: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.platformNoParam = "";
      this.partnerNoParam = "";
      this.custNoParam = "";
      this.handleQuery();
      // this.initSelect()
      this.initSelectData();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加外部系统平台余额分布";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDistribution(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改外部系统平台余额分布";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDistribution(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDistribution(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除外部系统平台余额分布编号为"' + ids + '"的数据项？'
        )
        .then(function () {
          return delDistribution(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/guaData/export",
        {
          ...this.queryParams,
        },
        `担保数据统计_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style>
.table_item {
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border: 1px solid #dfe6ec;
  box-sizing: border-box;
}
.total_box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #eee;
  background-color: #eee;
  font-size: 14px;
  color: #606266;
}
.total_box_left > span,
.total_box_right > span {
  padding: 10px;
  display: inline-block;
}
.total_box_left > span:first-child {
  width: 180px;
  border-right: 1px solid #eee;
}
.total_box_left > span:last-child {
  width: 180px;
  border-right: 1px solid #eee;
}
</style>
