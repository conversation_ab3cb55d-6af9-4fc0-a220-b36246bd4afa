<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-row
        ><span style="font-size: 15px; color: #97a8be">
          配置每个渠道的客服坐席人员</span
        >
      </el-row>
      <br />
      <el-form-item label="客服姓名" prop="accountName">
        <el-input
          v-model="queryParams.accountName"
          placeholder="请输入客服姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>


    <div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="按渠道" name="first">
          <div style="display: flex">
            <div style="width: 300px; flex-shrink: 0">
              <div v-for="(item, index) in companyChannelTreeList" :key="index">
                <div style="background: #f8f8f8; padding: 7px; font-size: 16px">
                  <i
                    class="el-icon-minus"
                    style="cursor: pointer"
                    @click="changeType(index)"
                    v-if="item.showType"
                  ></i
                  ><i
                    v-else
                    @click="changeType(index)"
                    class="el-icon-plus"
                    style="cursor: pointer"
                  ></i>
                  {{ item.companyName }}
                </div>
                <div
                  v-show="item.showType"
                  style="
                    border-bottom: 1px solid #f4f4f4;
                    cursor: pointer;
                    color: #26a1ff;
                    padding: 7px;
                    display: flex;
                    justify-content: space-between;
                    font-size: 14px;
                  "
                  @click="changeChannelId(item, v)"
                  :class="queryParams.channelId == v.channelId ? 'active' : ''"
                  v-for="(v, i) in item.childList"
                  :key="i"
                >
                  <span>{{ v.channelName }}</span>
                  <span>{{ v.count > 0 ? v.count : "-" }}</span>
                </div>
              </div>
            </div>
            <div style="flex: 1; padding: 16px">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['kf:account:add']"
                >添加客服
              </el-button>
              <div style="color: #2e363d; font-size: 14px" >
                <br>
                {{selectData.companyName}} > {{selectData.appName}} <span style="margin-left: 30px">共{{selectData.count}}人</span>
              </div>
              <el-table
                v-loading="loading"
                :data="accountList"
                style="margin-top: 12px"
              >
                <el-table-column
                  label="客服姓名"
                  width="200"
                  align="center"
                  prop="accountName"
                >
                  <template slot-scope="scope">
                    <span
                      v-if="scope.row.accountName"
                      style="
                        display: inline-block;
                        border: 1px solid #cccccc;
                        border-radius: 4px;
                        padding: 2px 8px;
                      "
                      >{{ scope.row.accountName }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column label="状态" align="center" prop="status">
                  <template slot-scope="scope">
                    <dict-tag
                      :options="dict.type.kf_status_type"
                      :value="scope.row.status"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="操作"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      @click="handleUpdateStatus(scope.row)"
                      v-hasPermi="['kf:account:updateStatus']"
                      ><span v-if="scope.row.status === '0'">停用</span
                      ><span v-if="scope.row.status === '1'">启用</span>
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDelete(scope.row)"
                      v-hasPermi="['kf:account:remove']"
                      >删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="按客服" name="second">
          <el-table v-loading="loading" :data="this.accountOtherList">
            <el-table-column
              label="客服姓名"
              width="450px"
              align="center"
              prop="accountName"
            >
              <template slot-scope="scope">
                <span
                  v-if="scope.row.accountName"
                  style="
                    display: inline-block;
                    border: 1px solid #cccccc;
                    border-radius: 4px;
                    padding: 2px 8px;
                  "
                >
                  {{ scope.row.accountName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="所属渠道" align="left" prop="childList">
              <template slot-scope="scope">
                <div v-for="item in scope.row.childList" :key="item.accountId">
                  <span>
                    {{ item.companyName }}
                    <span v-html="'\u00a0\u00a0\u00a0\u00a0'" />
                    &rarr;
                    <span v-html="'\u00a0\u00a0\u00a0\u00a0'" />
                    {{ item.channelName }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status">
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.kf_status_type"
                  :value="scope.row.status"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleUpdateStatus(scope.row)"
                  v-hasPermi="['kf:account:updateStatus']"
                  ><span v-if="scope.row.status === '0'">停用</span
                  ><span v-if="scope.row.status === '1'">启用</span>
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['kf:account:remove']"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 添加或修改客服人员对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="公司名称" prop="companyName">
         <span style="font-size: 15px; font-weight: bold">  ：{{this.selectData.companyName}}</span>
        </el-form-item>
        <el-form-item label="渠道信息" prop="channelIdList">
          <el-select
            v-model="form.channelIdList"
            placeholder="请选择渠道信息"
            multiple
            @change="changeChannelSelect"
          >
            <el-option
              v-for="dict in this.channelList"
              :key="dict.id"
              :label="dict.channelName"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号信息" prop="channelIdList">
          <el-select
            v-model="form.userNameList"
            placeholder="请选择账号信息"
            multiple
            @change="changeAccountSelect"
          >
            <el-option
              v-for="dict in this.userList"
              :key="dict.accountId"
              :label="dict.nickName"
              :value="dict.userName"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  accountList,
  addAccount,
  channelList,
  delAccount,
  getAccount,
  handleUpdateStatus,
  listByAccount,
  listByChannel,
} from "@/api/kf/account";
import { tree } from "@/api/kf/channel";

export default {
  name: "Account",
  dicts: ["kf_status_type"],
  data() {
    return {
      activeName: "first",
      // 公司渠道树状图
      companyChannelTreeList: [],
      // 列表展示开关
      ymCoverOpenFlag: true,
      // 渠道集合
      channelList: [],
      // 用户集合
      userList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 默认类型
      detailType: false,

      // div高度
      divHeight: "",
      // 添加工单类型
      addOrderType: false,
      // 客服人员渠道表格数据
      accountList: [],
      // 客服人员按客服表格数据
      accountOtherList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        channelIdList: [],
        pageNum: 1,
        pageSize: 10,
        accountCode: "",
        accountName: "",
        channelId: 2,
        status: null,
      },
      // 表单参数
      form: {},
      queryUnSelectParam: [],
      addForm: {},
      selectData: {
        companyName: "",
        companyCode:'',
        appName: "",
        count: "",
      },
      // 表单校验
      rules: {
        accountCode: [
          { required: true, message: "账号编码不能为空", trigger: "blur" },
        ],
        accountName: [
          { required: true, message: "账号姓名不能为空", trigger: "blur" },
        ],
        channelId: [
          { required: true, message: "渠道ID不能为空", trigger: "blur" },
        ],
        channelIdList: [
          { required: true, message: "渠道集合不能为空", trigger: "blur" },
        ],
        userNameList: [
          { required: true, message: "用户集合不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" },
        ],
        createBy: [
          { required: true, message: "创建人不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.tree();
  },
  methods: {
    changeChannelId(item, v) {
      this.queryParams.channelId = v.channelId;
      this.selectData.companyCode = item.companyCode
      this.selectData.companyName = item.companyName;
      this.selectData.appName = v.channelName;
      this.selectData.count = v.count;
      this.getList();
    },
    changeType(index) {
      let arr = JSON.parse(JSON.stringify(this.companyChannelTreeList));
      arr[index].showType = !arr[index].showType;
      this.companyChannelTreeList = [...arr];
    },
    kfButton() {
      this.ymCoverOpenFlag = false;
    },
    // 渠道按钮
    qdButton() {
      this.ymCoverOpenFlag = true;
    },
    // 树状图
    tree() {
      tree().then((resp) => {
        this.companyChannelTreeList = resp;
        if (this.companyChannelTreeList.length > 0) {
          this.companyChannelTreeList.forEach((item) => {
            item.showType = false;
          });
          this.companyChannelTreeList[0].showType = true;
          this.queryParams.channelId =
            this.companyChannelTreeList[0].childList[0].channelId;
          this.selectData.companyName =
            this.companyChannelTreeList[0].companyName;
          this.selectData.companyCode =
            this.companyChannelTreeList[0].companyCode;
          this.selectData.appName =
            this.companyChannelTreeList[0].childList[0].channelName;
          this.selectData.count =
            this.companyChannelTreeList[0].childList[0].count;
          this.getList();
        }
      });
    },

    // 查询用户可用信息
    changeChannelSelect(data) {
      this.form.accountCode = "";
      this.userList = [];
      this.queryUnSelectParam = data;
      if (data == undefined || data == null || data.length <= 0) {
        this.userList = [];
      } else {
        accountList(this.queryUnSelectParam).then((resp) => {
          this.userList = resp.data;
        });
      }
    },

    // 用户
    changeAccountSelect() {
    },

    // 集合全部渠道
    listChannelAll() {
      const str = this.selectData.companyCode;
      channelList(str).then((resp) => {
        this.channelList = resp.data;
      });
    },

    listByAccount() {
      listByAccount(this.queryParams.accountName).then((resp) => {
        this.accountOtherList = resp.rows;
      });
    },
    /** 查询客服人员列表 */
    getList() {
      this.loading = true;
      this.listByAccount();
      listByChannel(this.queryParams)
        .then((response) => {
          this.accountList = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch((reason) => {
          this.accountList = [];
          this.total = 0;
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountCode: null,
        accountName: null,
        channelId: null,
        status: null,
        createTime: null,
        createBy: null,
        lastUpdateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.listChannelAll();
      this.reset();
      this.open = true;
      this.title = "添加客服人员";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.listChannelAll();
      this.reset();
      const id = row.id || this.ids;
      getAccount(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改客服人员";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.status = 0;
          addAccount(this.form).then((response) => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.tree();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const data = {
        accountCode: row.accountCode,
        channelId: row.channelId === undefined ? "" : row.channelId,
      };
      this.$modal
        .confirm("是否确认删除此客服人员数据项？")
        .then(function () {
          return delAccount(data);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 通过用户编码更新状态*/
    handleUpdateStatus(row) {
      const data = {
        status: row.status === "0" ? "1" : "0",
        accountCode: row.accountCode,
      };
      var statusHz = row.status === "1" ? "停用" : "正常";
      var updateInfo = "";
      if (statusHz === "停用") {
        updateInfo =
          "启用后，将恢复该客服人员在停用前的信息，可继续认领处理工单";
      } else {
        updateInfo =
          "停用后，该客服人员在所有渠道都变为停用状态，将不能认领处理工单";
      }

      this.$modal
        .confirmHh(updateInfo + "<br/><br/>" + "客服名称: " + row.accountName)
        .then(function () {
          return handleUpdateStatus(data);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("更新状态完成");
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="less" scoped>
.active {
  color: #3892ff !important;
  font-weight: bold;
}
</style>
