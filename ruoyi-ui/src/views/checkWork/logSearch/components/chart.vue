<template>
  <div class="content">
    <div style="width: 80%; position: relative">
      <el-date-picker
        v-model="time"
        type="date"
        class="content_date z-20"
        @change="changeDate"
        value-format="yyyy-MM-dd"
      >
      </el-date-picker>
      <el-checkbox
        v-model="showData"
        class="absolute left-2 top-12"
        @change="showChange"
        >仅显示有日程数据的日期</el-checkbox
      >
      <el-button
        type="primary"
        plain
        class="absolute top-12 z-20"
        style="right: 0%"
        size="mini"
        @click="changList"
        >切换列表展示</el-button
      >
      <FullCalendar
        class="calenderCon w-full"
        :options="calendarOptions"
        ref="fullCalendar"
        v-loading="loading"
      />
    </div>
    <div class="flex-1 ml-3">
      <el-card>
        <el-form :model="queryParamsEchart" :inline="true" label-width="68px">
          <el-form-item label="员工姓名" prop="nickName">
            <el-input
              v-model="queryParamsEchart.nickName"
              autocomplete="new-password"
              placeholder="请输入员工姓名"
              clearable
              size="small"
              style="width: 180px"
              @keyup.enter.native="handleQuery"
              @clear="handleQuery"
              @focus.stop
            ></el-input>
          </el-form-item>
          <el-form-item label="分组搜索" prop="id">
            <el-select
              v-model="queryParamsEchart.id"
              autocomplete="new-password"
              placeholder="请输入/选择分组名称"
              clearable
              filterable
              size="small"
              style="width: 180px"
              @keyup.enter.native="handleQueryChange"
              @clear="handleQueryChange"
              @change="handleQueryChange"
              @focus.stop
            >
              <el-option
                v-for="item in groupList"
                :key="item.id"
                :label="item.groupName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <MyTable
          ref="mytable"
          :columns="columnsEchart"
          :source="configListEchart"
          showCheckbox
          rowKey="userId"
          @selection-change="selectionChange"
        >
          <template #name="{ record }">
            <div class="text-xs mb-1">{{ record.nickName }}</div>
            <div class="text-xs">{{ record.deptChain }}</div>
          </template>
        </MyTable>
        <pagination
          v-show="total > 0"
          :total="total"
          small
          layout="prev, pager, next"
          @pagination="getGroupUser"
          :page.sync="queryParamsEchart.pageNum"
          :limit.sync="queryParamsEchart.pageSize"
        />
      </el-card>
    </div>
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
      >
        <el-backtop
          target=".content"
          :bottom="5"
          :visibility-height="20"
          style="right: 50%; width: 150px"
        >
          <div
            style="
              height: 100%;
              width: 100%;
              background-color: #fff;
              text-align: center;
              line-height: 40px;
              color: #1989fa;
              font-size: 14px;
            "
          >
            返回顶部
          </div>
        </el-backtop>
      </div>
    </InBody>
  </div>
</template>
  
  <script>
import FullCalendar from "@fullcalendar/vue";
import resourceTimelinePlugin from "@fullcalendar/resource-timeline";
import interactionPlugin from "@fullcalendar/interaction";
import { getDayLogListOfLeader, getGroupUser } from "@/api/checkWork/log";
import { clone } from "xe-utils";

import moment from "moment";
import "moment/locale/zh-cn";

export default {
  name: "Chart",
  components: {
    FullCalendar,
  },
  props: {
    queryParams: {
      type: Object,
      default: () => ({}),
    },
    groupList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      configListEchart: [],
      columnsEchart: [{ label: "", key: "name" }],
      queryParamsEchart: {
        id: undefined,
        nickName: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      multipleSelection: [],
      isFirst: true, //首次进入
      logList: [],
      time: "",
      showData: true,
      loading: true,
      calendarOptions: {
        locale: "zh-cn", //选择语言
        aspectRatio: 7,
        height: "68vh",
        firstDay: 1,
        plugins: [
          resourceTimelinePlugin,
          interactionPlugin, // needed for dateClick
        ],
        views: {
          resourceTimelineMonth: {
            type: "resourceTimeline", // 资源时间线视图
            duration: { months: 1 }, // 设置视图为1个月
            slotDuration: { days: 1 }, // 每列代表一天
            slotLabelFormat: function (date) {
              // 格式化日期标签
              moment.locale("zh-cn");
              return moment(date.date.marker).format("ddd(M/D)"); // 格式：星期几 (月/日)
            },
          },
          resourceTimelineWeek: {
            type: "resourceTimeline", // 资源时间线视图
            duration: { weeks: 1 }, // 每次显示1周
            slotDuration: { days: 1 }, // 每列代表一天
            slotLabelFormat: function (date) {
              moment.locale("zh-cn");
              return moment(date.date.marker).format("ddd(M/D)"); // 如果日期无效，返回空
            },
          },
        },
        headerToolbar: {
          left: "prev next today",
          center: "title",
          right: "resourceTimelineMonth,resourceTimelineWeek",
        },
        buttonText: {
          // 设置按钮
          today: "今天",
          month: "月",
          week: "周",
        },
        titleFormat: {},
        schedulerLicenseKey: "GPL-My-Project-Is-Open-Source", //此配置是为了消除右下角的版权提示
        resourceAreaHeaderContent: "姓名", // 纵轴的第一行 用来表示纵轴的名称
        resourceAreaWidth: "10%", //纵轴宽度
        handleWindowResize: true, //是否随浏览器窗口大小变化而自动变化
        initialView: "resourceTimelineMonth",
        resources: [],
        events: [],
        eventContent: function (info) {
          // 自定义事件显示内容
          let eventTitle = info.event.title; // 事件标题
          return {
            html: `<div class="single-line-event">${eventTitle}</div>`,
          };
        },
        slotMinWidth: 200, // 每个时间槽的最小宽度
        eventMinWidth: 200, // 设置事件最小宽度
        eventClick: this.eventClick, // 事件点击事件
        dateClick: this.dateClick,
        datesSet: this.datesSet,
      },
    };
  },
  async mounted() {
    await this.getGroupUser();
    this.$refs.mytable.toggleAllSelection(true);
    this.multipleSelection = clone(this.configListEchart, true);
    this.$nextTick(async () => {
      await this.getResourcesEvents();
      this.isFirst = false;
    });
  },
  methods: {
    async getGroupUser() {
      const { rows, total } = await getGroupUser(this.queryParamsEchart);
      this.configListEchart = rows;
      this.total = total;
    },
    selectionChange(selection) {
      this.multipleSelection = selection;
      this.getResourcesEvents();
    },
    async handleQueryChange() {
      this.multipleSelection = [];
      this.$refs.mytable.clearSelection();
      await this.getGroupUser();
      this.$refs.mytable.toggleAllSelection(true);
      this.multipleSelection = clone(this.configListEchart, true);
      this.$nextTick(async () => {
        await this.getResourcesEvents();
      });
    },
    handleQuery() {
      this.getGroupUser();
      this.getResourcesEvents();
    },
    async getResourcesEvents() {
      const calendarApi = this.$refs.fullCalendar.getApi();
      const view = calendarApi.view;
      const logDateStart = moment(view.activeStart).format("YYYY-MM-DD");
      const logDateEnd = moment(view.activeEnd)
        .subtract(1, "day")
        .format("YYYY-MM-DD");
      const { rows } = await getDayLogListOfLeader({
        nickName: this.queryParams.nickName,
        deptName: this.queryParams.deptName,
        logContent: this.queryParams.logContent,
        logDateStart,
        logDateEnd,
        groupUserIdList: this.multipleSelection
          .map((item) => item.userId)
          ?.join(","),
      });
      this.logList = rows;
      this.calendarOptions.resources = this.multipleSelection.map((item) => {
        return { id: item.userId, title: item.nickName };
      });
      this.calendarOptions.events = rows.reduce((events, item) => {
        if (item.dayLogList && item.dayLogList.length > 0) {
          item.dayLogList.forEach((dayLog) => {
            if (
              dayLog.dataFormat == 2 &&
              dayLog.logContentList &&
              dayLog.logContentList.length > 0
            ) {
              dayLog.logContentList.forEach((logContentItem) => {
                if (logContentItem.logContent) {
                  events.push({
                    id: `${item.id}-${logContentItem.id}`,
                    resourceId: item.userId,
                    title:
                      dayLog.isAllDay == 1
                        ? `全天 ${logContentItem.logContent}`
                        : `${
                            dayLog.startTime +
                            "-" +
                            dayLog.endTime +
                            logContentItem.logContent
                          }`,
                    start: item.logDate,
                    end: item.logDate,
                  });
                }
              });
            } else {
              if (dayLog.logContent) {
                events.push({
                  id: `${item.id}-${dayLog.id}`,
                  resourceId: item.userId,
                  title:
                    dayLog.isAllDay == 1
                      ? `全天 ${dayLog.logContent}`
                      : `${
                          dayLog.startTime +
                          "-" +
                          dayLog.endTime +
                          dayLog.logContent
                        }`,
                  start: item.logDate,
                  end: item.logDate,
                });
              }
            }
          });
        }
        return events;
      }, []);
      this.showChange();
      this.loading = false;
      // this.addColor();
    },
    changList() {
      this.$emit("changList");
    },
    showChange() {
      const calendarApi = this.$refs.fullCalendar.getApi();
      const view = calendarApi.view;
      const start = moment(view.activeStart);
      const end = moment(view.activeEnd).subtract(1, "day");
      // 获取所有日期
      const dates = [];
      while (start.isSameOrBefore(end)) {
        dates.push(start.format("YYYY-MM-DD"));
        start.add(1, "day");
      }
      // 检查每一天是否有数据
      dates.forEach((date) => {
        const hasData = this.calendarOptions.events.some((event) =>
          moment(event.start).isSame(date, "day")
        );
        // 获取对应日期的单元格和标签
        const cell = document.querySelector(
          `.fc-timeline-body td[data-date="${date}"]`
        );
        const label = document.querySelector(
          `.fc-timeline-header th[data-date="${date}"]`
        );
        if (cell && label) {
          const shouldHide = !hasData && this.showData;
          cell.style.display = shouldHide ? "none" : "table-cell";
          label.style.display = shouldHide ? "none" : "table-cell";
        }
      });
      const scrollEl = document.querySelector(
        ".fc-scrollgrid  tr th:last-child .fc-scroller"
      );
      if (scrollEl) {
        scrollEl.scrollTo(0, 0);
      }
      setTimeout(() => {
        const newOptions = { ...this.calendarOptions };
        this.calendarOptions = newOptions;
      }, 0);
    },
    addColor() {
      this.calendarOptions.events.forEach((item) => {
        item.color = "transparent"; // 边框颜色
        item.backgroundColor = "rgb(193, 156, 83)"; // 背景颜色
      });
    },
    eventClick(eventInfo) {
      const row = this.logList.filter(
        (item) => item.id == eventInfo.event._def.publicId.split("-")[0]
      );
      this.$emit("onDetaill", row[0]);
    },
    dateClick(eventInfo) {},
    async datesSet(info) {
      if (this.isFirst) return;
      this.getResourcesEvents();
    },
    changeDate(value) {
      const calendarApi = this.$refs.fullCalendar.getApi();
      const calendar = calendarApi.view.calendar;
      calendar.gotoDate(value);
    },
  },
};
</script>
  <style lang="less" scoped>
.content {
  padding: 20px;
  position: relative;
  display: flex;
  overflow-y: auto;
  height: 70vh;
  .content_date {
    position: absolute;
    left: 45%;
    top: 20px;
    transform: translateX(-50%);
    opacity: 0;
    width: 350px;
  }
  ::v-deep .fc-header-toolbar {
    position: relative;
    padding-bottom: 30px;
  }
  ::v-deep .fc-icon-chevron-left::before {
    content: "<";
    position: relative;
    top: -3px;
  }
  ::v-deep .fc-icon-chevron-right::before {
    content: ">";
    position: relative;
    top: -3px;
  }
  ::v-deep .fc-event-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  ::v-deep .fc-timeline-body {
    cursor: pointer;
  }
  ::v-deep .fc-timeline-body td,
  ::v-deep .fc-scroller-harness td {
    height: 60px !important; /* 使用 !important 强制生效 */
  }
  ::v-deep .fc-scrollgrid-sync-table {
    border-bottom: 1px solid #ddd;
  }
  ::v-deep .fc-scrollgrid-section-header th:last-child {
    border-bottom: 1px solid #ddd;
  }
  ::v-deep .fc-scrollgrid-sync-table th {
    padding: 10px 0 !important;
  }
  ::v-deep .fc-day-today {
    background-color: rgba(174, 216, 255, 0.1);
  }
  ::v-deep .fc .fc-scrollgrid-section-sticky > * {
    position: relative;
  }
  ::v-deep .single-line-event {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  /* 增强月和周视图按钮的对比度 */
  ::v-deep .fc-button-primary:not(.fc-button-active) {
    background-color: #fafafa !important;
    color: #909399 !important;
    border-color: #ebeef5 !important;
    font-weight: normal !important;
  }
  ::v-deep .fc-button-group .fc-button:hover:not(.fc-button-active) {
    background-color: #f0f2f5 !important;
    color: #606266 !important;
  }

  /* 为全选按钮添加文字 */
  ::v-deep .el-table .el-table__header-wrapper .el-checkbox {
    position: relative;
  }

  ::v-deep .el-table .el-table__header-wrapper .el-checkbox::after {
    content: "全选";
    position: absolute;
    left: 20px;
    top: 4px;
    line-height: 18px;
    font-size: 12px;
    white-space: nowrap;
    color: #1890ff;
  }
}
</style>
  
  