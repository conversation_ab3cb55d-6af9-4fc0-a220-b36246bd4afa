<template>
  <div class="p-6 w-2/3 m-auto pb-10">
    <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
    <MyForm
      v-model="form"
      :columns="formColumnsDetail"
      formType="false"
      labelWidth="140px"
      ref="form"
      :rules="rules"
    >
      <template #businessTripSlaveList>
        <div style="padding-left: 50px"><el-divider></el-divider></div>
        <div
          v-for="(item, index) in form.businessTripSlaveList"
          :key="index"
          class="relative"
        >
          <div class="flex justify-between">
            <el-form-item
              label="出发地点"
              :rules="{
                required: true,
                message: '请输入出发地点',
                trigger: 'blur',
              }"
              :prop="'businessTripSlaveList.' + index + '.setOut'"
              style="margin-bottom: 20px; width: 45%"
            >
              {{ item.setOut }}
            </el-form-item>
            <el-form-item
              label="到达地点"
              :rules="{
                required: true,
                message: '请输入到达地点',
                trigger: 'blur',
              }"
              :prop="'businessTripSlaveList.' + index + '.reach'"
              style="margin-bottom: 20px; width: 45%"
            >
              {{ item.reach }}
            </el-form-item>
          </div>
          <el-form-item label="出发时间" style="margin-bottom: 20px" required>
            <div>
              {{ `${item.startTime} ${item.startTimePeriod}` }} 至
              {{ `${item.endTime} ${item.endTimePeriod}` }}
            </div>
          </el-form-item>
          <div class="flex justify-between">
            <el-form-item
              style="width: 45%"
              label="出差时长(天)"
              :rules="[
                {
                  required: true,
                  message: '时长不能为空',
                  trigger: 'change',
                },
              ]"
              :prop="'businessTripSlaveList.' + index + '.times'"
            >
              {{ item.times }}
            </el-form-item>
            <el-form-item label="交通工具" style="width: 45%">
              {{ item.vehicle }}
            </el-form-item>
          </div>
          <div style="padding-left: 50px"><el-divider></el-divider></div>
        </div>
      </template>
      <template #fileIds>
        <el-form-item
          prop="fileIds"
          label="附件上传:"
          style="margin-bottom: 20px"
        >
          <div v-for="(item, index) in form.files" :key="index">
            {{ item.fileName }}
            <el-button type="text" @click="handleDownload(item)" class="ml-2"
              >下载</el-button
            >
            <el-button
              v-show="
                item.fileName.endsWith('.pdf') ||
                item.fileName.endsWith('.jpg') ||
                item.fileName.endsWith('.png') ||
                item.fileName.endsWith('.gif') ||
                item.fileName.endsWith('.jpeg')
              "
              type="text"
              @click="handlePreview(item)"
              >查看</el-button
            >
          </div>
        </el-form-item>
      </template>
    </MyForm>
    <InBody>
      <div
        class="flex justify-center fixed bottom-0 bg-white z-10 w-full pb-2"
        style="left: 130px"
      >
        <el-button @click="cancel">关闭</el-button>
      </div>
    </InBody>
  </div>
</template>
    
    <script>
import { businessTripId } from "@/api/checkWork/goErrand";
import { clone } from "xe-utils";
import privew from "@/mixin/privew";
import config from "./config";
export default {
  mixins: [privew],
  name: "GoErrandView",
  components: {},
  props: {},
  data() {
    return {
      ...config,
      form: {},
      rules: Object.freeze({
        businessTripReason: [
          { required: true, message: "出差事由不能为空", trigger: "blur" },
        ],
      }),
    };
  },
  watch: {},
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getInitForm();
    },
    handlerInitForm() {
      this.form.companionsList=this.form.companionsList.map(item => item.nickName)?.join(',');
    },
    async getInitForm() {
      const { data } = await businessTripId(this.$route.query.id);
      this.form = clone(data, true);
      this.handlerInitForm();
    },

    cancel() {
      const obj = { path: this.$route.path };
      this.$tab.closePage(obj);
      this.$router.go(-1);
    },
  },
};
</script>

    
    