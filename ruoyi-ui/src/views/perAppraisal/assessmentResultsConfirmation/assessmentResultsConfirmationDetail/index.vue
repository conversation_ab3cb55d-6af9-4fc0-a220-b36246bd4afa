<template>
  <div
    class="p-5 px-10 page-component__scroll overflow-y-auto"
    style="height: calc(100vh - 100px)"
  >
    <div style="color: #aaaaaa">
      说明：该界面展示本公司所有计划指标、考核配置、项目业绩，以及考核结果
    </div>
    <div
      style="font-size: 25px"
      class="flex justify-between font-bold leading-10"
    >
      <div>{{ proForm.year }}</div>
      <div style="font-size: 28px">{{ proForm.companyName }}</div>
      <div>{{ quarterObj[proForm.quarter] }}</div>
    </div>
    <div v-for="(item, index) in showList" :key="index">
      <el-divider></el-divider>
      <div class="flex">
        <div @click="changeShow(item)" class="flex cursor-pointer">
          <div class="font-bold text-base">{{ item.title }}:</div>
          <i
            class="el-icon-caret-bottom relative top-1 mr-10"
            v-if="item.show"
          ></i>
          <i class="el-icon-caret-top relative top-1 mr-10" v-else></i>
        </div>
      </div>
      <div :ref="item.ref" class="pt-2 overflow-hidden h-0">
        <div v-show="item.ref == 'plan'" class="pt-5">
          <div class="flex mb-4">
            <div
              v-for="(item, index) in planHeadForm"
              :key="index"
              class="flex flex-1 mx-5 leading-9"
            >
              <div class="font-bold">{{ item.title }}</div>
              <el-input
                v-model="planForm.headForm[item.value]"
                disabled
                class="mx-1"
                style="width: 200px"
              ></el-input>
              <div>{{ item.unit }}</div>
            </div>
          </div>
          <div class="pb-2">
            <div class="font-bold my-1">部门计划指标</div>
            <MyTable
              class="w-3/4"
              size="mini"
              :columns="columnsPlanDept"
              :source="planForm.deptTable"
              border
              show-summary
              :cell-style="colunmStyle"
              :summary-method="getSummariesDept"
            />
            <pagination
              v-show="pagePlanDept.total > 0"
              :total="pagePlanDept.total"
              :page.sync="pagePlanDept.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :limit.sync="pagePlanDept.pageSize"
              @pagination="getListPlanDeptPage"
            />
            <div class="font-bold mb-1 mt-8">用户计划指标</div>
            <MyTable
              class="w-3/4"
              size="mini"
              :columns="columnsPlanUser"
              :source="planForm.userTable"
              border
              show-summary
              :cell-style="colunmStyle"
              :summary-method="getSummariesUser"
            />
            <pagination
              v-show="pagePlanUser.total > 0"
              :total="pagePlanUser.total"
              :page.sync="pagePlanUser.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :limit.sync="pagePlanUser.pageSize"
              @pagination="getListPlanUserPage"
            />
          </div>
        </div>
        <div v-show="item.ref == 'config'" class="pt-5">
          <div class="pb-2">
            <MyTable
              class="w-2/3"
              :columns="columnsConfigDept"
              :source="configForm.deptTable"
            />
            <pagination
              v-show="pageConfigDept.total > 0"
              :total="pageConfigDept.total"
              :page.sync="pageConfigDept.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :limit.sync="pageConfigDept.pageSize"
              @pagination="getListConfigDeptPage"
            />
          </div>
          <div class="pb-2">
            <MyTable
              class="w-2/3 mt-2"
              :columns="columnsConfigUser"
              :source="configForm.userTable"
            />
            <pagination
              v-show="pageConfigUser.total > 0"
              :total="pageConfigUser.total"
              :page.sync="pageConfigUser.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :limit.sync="pageConfigUser.pageSize"
              @pagination="getListConfigUserPage"
            />
          </div>
        </div>
        <div v-show="item.ref == 'project'" class="pt-5 pb-2">
          <MyTable :columns="columnsProject" :source="projectForm.table">
            <template #operate="{ record }">
              <el-button type="text" @click="goViewProject(record)"
                >查看详情</el-button
              >
              <el-button type="text" @click="recordsProject(record)"
                >查看修改记录</el-button
              >
            </template>
          </MyTable>
          <pagination
            v-show="pageProject.total > 0"
            :total="pageProject.total"
            :page.sync="pageProject.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :limit.sync="pageProject.pageSize"
            @pagination="getListProject"
          />
          <RecordsDialog
            v-model="projectForm.projectFormOpen"
            :recordForm="projectForm.form"
          />
        </div>
        <div v-show="item.ref == 'results'" class="pt-5 pb-4">
          <div class="flex mt-8">
            <div class="font-bold mr-2 leading-7">展示维度</div>
            <el-radio-group
              @input="getListResults"
              v-model="pageResults.type"
              size="mini"
            >
              <el-radio-button
                v-for="(item, index) in dimensionList"
                :key="index"
                :label="item.value"
                >{{ item.label }}</el-radio-button
              >
            </el-radio-group>
          </div>
          <right-toolbar
            @queryTable="getListResults"
            @handleClose="handleClose"
            :showSearchProp="false"
            :columns="columnsResultsChange"
            :propsTransfer="propsTransfer"
          ></right-toolbar>
          <MyTable
            :columns="columnsResults"
            :source="resultsForm.table"
            class="my-3"
          >
            <template #quarter="{ record }">
              <div>{{ quarterObj[record.quarter] }}</div>
            </template>
            <template #totalIndex="{ record }">
              <div
                :style="{
                  color: record.totalIndex ? '' : 'rgb(245, 108, 108)',
                }"
              >
                {{ record.totalIndex || "请补充年度计划" }}
              </div>
            </template>
            <template #distributionIndex="{ record }">
              <div
                :style="{
                  color: record.distributionIndex ? '' : 'rgb(245, 108, 108)',
                }"
              >
                {{ record.distributionIndex || "请补充年度计划" }}
              </div>
            </template>
            <template #extensionIndex="{ record }">
              <div
                :style="{
                  color: record.extensionIndex ? '' : 'rgb(245, 108, 108)',
                }"
              >
                {{ record.extensionIndex || "请补充年度计划" }}
              </div>
            </template>
            <template #extensionBank="{ record }">
              <div
                :style="{
                  color: record.extensionBank ? '' : 'rgb(245, 108, 108)',
                }"
              >
                {{ record.extensionBank || "未在年度计划中配置" }}
              </div>
            </template>
            <template #achievementWagesProportion="{ record }">
              <div
                :style="{
                  color: record.achievementWagesProportion
                    ? ''
                    : 'rgb(245, 108, 108)',
                }"
              >
                {{ record.achievementWagesProportion || "请补充考核配置" }}
              </div>
            </template>
            <template #distributionProportion="{ record }">
              <div
                :style="{
                  color: record.distributionProportion
                    ? ''
                    : 'rgb(245, 108, 108)',
                }"
              >
                {{ record.distributionProportion || "请补充考核配置" }}
              </div>
            </template>
            <template #extensionProportion="{ record }">
              <div
                :style="{
                  color: record.extensionProportion ? '' : 'rgb(245, 108, 108)',
                }"
              >
                {{ record.extensionProportion || "请补充考核配置" }}
              </div>
            </template>
            <template #completeExtensionBank="{ record }">
              <div
                :style="{
                  color: record.completeExtensionBank
                    ? ''
                    : 'rgb(245, 108, 108)',
                }"
              >
                {{ record.completeExtensionBank || "未补充" }}
              </div>
            </template>
            <template #distributionIndexDeviation="{ record }">
              <div
                :style="{
                  color: record.distributionIndexDeviationColor,
                }"
              >
                {{
                  record.distributionIndexDeviation != undefined
                    ? record.distributionIndexDeviation
                    : "-"
                }}
              </div>
            </template>
            <template #calibrationProjectSalaryExtensionProportion="{ record }">
              <div>
                {{
                   Math.max(record.calibrationProjectSalaryExtensionProportion||0 , record.extensionIndexDeviationProportionQ3||0)
                }}%
                <div
                  style="color: red"
                  v-show="
                    record.extensionIndexDeviationProportionQ3 >
                    record.calibrationProjectSalaryExtensionProportion
                    
                  "
                >
                  (包含前三季度补充)
                </div>
              </div>
            </template>
            <template #extensionIndexDeviation="{ record }">
              <div
                :style="{
                  color: record.extensionIndexDeviationColor,
                }"
              >
                {{
                  record.extensionIndexDeviation != undefined
                    ? record.extensionIndexDeviation
                    : "-"
                }}
              </div>
            </template>
            <template #calibrationDistributionIndex="{ record }">
              <div
                :style="{
                  color: record.calibrationDistributionIndexColor,
                }"
              >
                {{
                  record.calibrationDistributionIndex !== null &&
                  record.calibrationDistributionIndex !== undefined
                    ? record.calibrationDistributionIndex
                    : "-"
                }}
              </div>
            </template>
            <template #extensionBankDeviation="{ record }">
              <div
                :style="{
                  color: record.extensionBankDeviationColor,
                }"
              >
                {{ record.extensionBankDeviation }}
              </div>
            </template>
            <template #calibrationDistributionIndexState="{ record }">
              <div>
                {{
                  distributionStateObj[record.calibrationDistributionIndexState]
                }}
              </div>
            </template>
            <template #operate="{ record }">
              <el-dropdown
                @command="handleCommand($event, record)"
                class="ml-2"
              >
                <span class="el-dropdown-link mr-2" style="font-size: 14px">
                  >>更多
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="calibration"
                    :disabled="!record.isEdit"
                    >校准考核结果</el-dropdown-item
                  >
                  <el-dropdown-item
                    command="supplement"
                    :disabled="!record.isEdit"
                    >补充自拓银行完成情况</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </MyTable>
          <pagination
            v-show="pageResults.total > 0"
            :total="pageResults.total"
            :page.sync="pageResults.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :limit.sync="pageResults.pageSize"
            @pagination="handleConfigList"
          />
          <DetailDialog
            v-model="resultsForm.openSupplement"
            :currentForm="resultsForm.currentForm"
            :type="pageResults.type"
          />
        </div>
      </div>
    </div>
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
      >
        <el-backtop
          target=".page-component__scroll "
          :bottom="5"
          :right="280"
          :visibility-height="20"
        >
          <div
            style="
               {
                height: 100%;
                width: 100%;
                background-color: #fff;
                text-align: center;
                line-height: 40px;
                color: #1989fa;
                font-size: 14px;
              }
            "
          >
            返回顶部
          </div>
        </el-backtop>
        <el-button @click="onSure" type="primary">确认考核结果</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </InBody>
    <UpdateDetailDialog
      v-model="openSure"
      :form="form"
      @on-submit-success="openSureSucess"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import config from "./config";
import { getAnnualPlanVo } from "@/api/perAppraisal/annualPlan";
import {
  listResult,
  checkResultList,
  replaceColumnsInit,
  informationFlowControllerGetCheckResult,
} from "@/api/perAppraisal/results";
import UpdateDetailDialog from "../components/UpdateDetailDialog.vue";

import { listOfCheck } from "@/api/perAppraisal/projectPerformance";
import RecordsDialog from "@/views/perAppraisal/projectPerformance/components/RecordsDialog.vue";
import DetailDialog from "@/views/perAppraisal/assessmentResults/components/DetailDialog.vue";
import { floatAdd } from "@/utils";
import XEUtils from "xe-utils";

export default {
  name: "AssessmentResultsConfirmationDetail",
  components: { RecordsDialog, DetailDialog, UpdateDetailDialog },

  data() {
    return {
      ...config,
      openSure: false,
      selectCompanyType: false,
      form: [],
      id: this.$route.params.id,
      proForm: JSON.parse(this.$route.query.form),
      planForm: {
        headForm: {},
        deptTotal: {
          totalIndexAll: 0,
          distributionIndexAll: 0,
          extensionIndexAll: 0,
          extensionBankAll: 0,
        },
        deptTableAll: [],
        deptTable: [],
        userTotal: {
          totalIndexAll: 0,
          distributionIndexAll: 0,
          extensionIndexAll: 0,
          extensionBankAll: 0,
        },
        userTableAll: [],
        userTable: [],
      },
      pagePlanDept: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      pagePlanUser: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      configForm: {
        deptTable: [],
        userTable: [],
      },
      pageConfigDept: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      pageConfigUser: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      projectForm: {
        table: [],
        form: {},
        projectFormOpen: false,
      },
      pageProject: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      resultsForm: {
        table: [],
        tableAll: [],
        openSupplement: false,
        currentForm: {},
      },
      pageResults: {
        type: "1",
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {},

  created() {
    this.init();
  },
  methods: {
    init() {
      this.form = this.proForm.dilaogForm;
      this.getForm();
    },
    getForm() {
      this.showList = XEUtils.clone(this.showListInit, true);
      this.getPlan();
    },
    openSureSucess() {
      this.selectCompanyType = true;
    },
    submitCompany(e) {
      informationFlowControllerGetCheckResult({ companyId: e }).then(
        async (res) => {
          if (res.code == 200) {
            this.selectCompanyType = false;
            const data = this.form;
            sessionStorage.setItem(
              "oa-assessmentResultsTable",
              JSON.stringify(data)
            );
            this.$router.push({
              path: "/oaWork/updateProcessForm",
              query: {
                templateId: res.templateId,
                classificationId: res.classificationId,
                companyId: res.companyId,
                assessmentResultsTable: true,
              },
            });
          }
        }
      );
    },
    closeCompany() {
      this.selectCompanyType = false;
      this.getList();
    },
    async getPlan() {
      this.getPlanHeader();
      await this.getListPlanDept();
      await this.getListPlanUser();
      this.getConfig();
      this.getListProject();
      this.getListResults();
    },
    async getPlanHeader() {
      const { data } = await getAnnualPlanVo({ ...this.proForm, type: 1 });
      if (!data) return;
      data.TotalIndex = data[`q${this.proForm.quarter}TotalIndex`];
      data.DistributionIndex =
        data[`q${this.proForm.quarter}DistributionIndex`];
      data.ExtensionBank = data[`q${this.proForm.quarter}ExtensionBank`];
      data.ExtensionIndex = data[`q${this.proForm.quarter}ExtensionIndex`];
      this.planForm.headForm = data;
    },
    async getListPlanDept() {
      const { rows, total } = await listResult({
        ...this.proForm,
        years: this.proForm.year,
        quarters: this.proForm.quarter,
        type: 2,
        companyIds: this.proForm.companyId,
      });
      this.planForm.deptTableAll = rows.map((item) => {
        item = { ...item, ...item.checkResult };
        return item;
      });
      this.planForm.deptTableAll.forEach((item) => {
        this.planForm.deptTotal.totalIndexAll = floatAdd(
          this.planForm.deptTotal.totalIndexAll,
          item.totalIndex
        );
        this.planForm.deptTotal.distributionIndexAll = floatAdd(
          this.planForm.deptTotal.distributionIndexAll,
          item.distributionIndex
        );
        this.planForm.deptTotal.extensionIndexAll = floatAdd(
          this.planForm.deptTotal.extensionIndexAll,
          item.extensionIndex
        );
        this.planForm.deptTotal.extensionBankAll = floatAdd(
          this.planForm.deptTotal.extensionBankAll,
          item.extensionBank
        );
      });
      this.pagePlanDept.total = total;
      this.getListPlanDeptPage();
    },
    getListPlanDeptPage() {
      const start =
        (this.pagePlanDept.pageNum - 1) * this.pagePlanDept.pageSize;
      const end = start + this.pagePlanDept.pageSize;
      this.planForm.deptTable = this.planForm.deptTableAll.slice(start, end);
    },
    getSummariesDept(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计:";
        }
        if (index >= 1) {
          sums[index] = this.planForm.deptTotal.totalIndexAll;
        }
        if (index >= 2) {
          sums[index] = this.planForm.deptTotal.distributionIndexAll;
        }
        if (index >= 3) {
          sums[index] = this.planForm.deptTotal.extensionIndexAll;
        }
        if (index >= 4) {
          sums[index] = this.planForm.deptTotal.extensionBankAll;
        }
      });
      return sums;
    },
    async getListPlanUser() {
      const { rows, total } = await listResult({
        ...this.proForm,
        years: this.proForm.year,
        quarters: this.proForm.quarter,
        type: 3,
        companyIds: this.proForm.companyId,
      });
      this.planForm.userTableAll = rows.map((item) => {
        item = { ...item, ...item.checkResult };
        return item;
      });
      this.planForm.userTableAll.forEach((item) => {
        this.planForm.userTotal.totalIndexAll = floatAdd(
          this.planForm.userTotal.totalIndexAll,
          item.totalIndex
        );
        this.planForm.userTotal.distributionIndexAll = floatAdd(
          this.planForm.userTotal.distributionIndexAll,
          item.distributionIndex
        );
        this.planForm.userTotal.extensionIndexAll = floatAdd(
          this.planForm.userTotal.extensionIndexAll,
          item.extensionIndex
        );
        this.planForm.userTotal.extensionBankAll = floatAdd(
          this.planForm.userTotal.extensionBankAll,
          item.extensionBank
        );
      });
      this.pagePlanUser.total = total;
      this.getListPlanUserPage();
    },
    getListPlanUserPage() {
      const start =
        (this.pagePlanUser.pageNum - 1) * this.pagePlanUser.pageSize;
      const end = start + this.pagePlanUser.pageSize;
      this.planForm.userTable = this.planForm.userTableAll.slice(start, end);
    },
    getSummariesUser(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计:";
        }
        if (index >= 1) {
          sums[index] = this.planForm.userTotal.totalIndexAll;
        }
        if (index >= 2) {
          sums[index] = this.planForm.userTotal.distributionIndexAll;
        }
        if (index >= 3) {
          sums[index] = this.planForm.userTotal.extensionIndexAll;
        }
        if (index >= 4) {
          sums[index] = this.planForm.userTotal.extensionBankAll;
        }
      });
      return sums;
    },
    getConfig() {
      this.getListConfigDept();
      this.getListConfigUser();
    },
    async getListConfigDept() {
      this.getListConfigDeptPage();
      this.pageConfigDept.total = this.planForm.deptTableAll.length;
    },
    getListConfigDeptPage() {
      const start =
        (this.pageConfigDept.pageNum - 1) * this.pageConfigDept.pageSize;
      const end = start + this.pageConfigDept.pageSize;
      this.configForm.deptTable = this.planForm.deptTableAll.slice(start, end);
    },
    async getListConfigUser() {
      this.getListConfigUserPage();
      this.pageConfigUser.total = this.planForm.userTableAll.length;
    },
    getListConfigUserPage() {
      const start =
        (this.pageConfigUser.pageNum - 1) * this.pageConfigUser.pageSize;
      const end = start + this.pageConfigUser.pageSize;
      this.configForm.userTable = this.planForm.userTableAll.slice(start, end);
    },
    async getListProject() {
      this.getColumnsProject();
      const { rows, total } = await listOfCheck({
        ...this.pageProject,
        years: this.proForm.year,
        companyId: this.proForm.companyId,
        companyName: this.proForm.companyName,
      });
      this.projectForm.table = rows;
      this.pageProject.total = total;
    },

    getColumnsProject() {
      const quarterColumns = {
        1: this.columnsQ1Project,
        2: this.columnsQ2Project,
        3: this.columnsQ3Project,
        4: this.columnsQ4Project,
      };
      this.columnsProject = this.columnsInitProject.concat(
        quarterColumns[this.proForm.quarter],
        this.columnsOperateProject
      );
    },
    goViewProject(row) {
      const form = {
        year: row.year,
        projectId: row.projectId,
      };
      this.$router.push({
        path: `/perAppraisalOther/projectPerformance/${row.id}`,
        query: {
          title: "查看项目业绩详情",
          form: JSON.stringify(form),
        },
      });
    },
    recordsProject(record) {
      this.projectForm.form = record;
      this.projectForm.projectFormOpen = true;
    },
    async getListResults() {
      this.getColumnsResultsInit();
      this.pageResults.pageNum = 1;
      const { rows, total } = await listResult({
        type: this.pageResults.type,
        years: this.proForm.year,
        quarters: this.proForm.quarter,
        companyIds: this.proForm.companyId,
      });
      this.resultsForm.tableAll = rows;
      this.handleConfigList();
      this.pageResults.total = total;
    },
    handleConfigList() {
      let start = (this.pageResults.pageNum - 1) * this.pageResults.pageSize;
      if (start > this.resultsForm.tableAll.length) start = 0;
      const end = start + this.pageResults.pageSize;
      this.resultsForm.table = this.resultsForm.tableAll.slice(start, end);
      const toDecimal = [
        "totalIndex",
        "distributionIndex",
        "extensionIndex",
        "completeTotalIndex",
        "completeDistributionIndex",
        "calibrationDistributionIndex",
        "completeExtensionIndex",
        "calibrationExtensionIndex",
      ];
      const deviation = [
        "distributionIndexDeviation",
        "extensionIndexDeviation",
      ];
       const addUnit = [
        "achievementWagesProportion",
        "distributionProportion",
        "extensionProportion",
        "projectSalaryTotalProportion",
        "projectSalaryDistributionProportion",
        "calibrationProjectSalaryDistributionProportion",
        "bankSalaryExtensionProportion",
        "calibrationProjectSalaryBankProportion",
        "calibrationProjectSalaryTotalProportion",
        "projectSalaryExtensionProportion",
      ];
      this.resultsForm.table = this.resultsForm.table.map((item) => {
        item = Object.assign(item, item.checkResult);
        toDecimal.forEach((item1) => {
          item[item1] = item[item1] && Number(item[item1]).toFixed(6);
        });
         addUnit.forEach((item1) => {
          if (item[item1] != undefined && item[item1] != null) {
            item[item1] = item[item1] + "%";
          }
        });
        deviation.forEach((item1) => {
          if (item[item1]) {
            if (item[item1] > 0) {
              item[item1] = `+${Number(item[item1]).toFixed(6)}`;
              this.$set(item, item1 + "Color", "rgb(245, 108, 108)");
            } else {
              item[item1] = `${Number(item[item1]).toFixed(6)}`;
              this.$set(
                item,
                item1 + "Color",
                "rgba(191, 191, 0, 0.***************)"
              );
            }
          } else {
            item[item1] = item[item1] == 0 ? 0 : undefined;
            this.$set(item, item1 + "Color", "");
          }
        });
        if (item.extensionBankDeviation) {
          if (item.extensionBankDeviation > 0) {
            item.extensionBankDeviation = `+${item.extensionBankDeviation}`;
            this.$set(
              item,
              "extensionBankDeviationColor",
              "rgb(245, 108, 108)"
            );
          } else {
            this.$set(
              item,
              "extensionBankDeviationColor",
              "rgba(191, 191, 0, 0.***************)"
            );
          }
        } else {
          item.extensionBankDeviation =
            item.extensionBankDeviation == 0 ? 0 : undefined;
          this.$set(item, "extensionBankDeviationColor", "");
        }
        this.$set(
          item,
          "isEdit",
          Boolean(
            item.totalIndex &&
              item.distributionIndex &&
              item.extensionIndex &&
              item.totalIndex &&
              item.extensionBank &&
              item.achievementWagesProportion &&
              item.distributionProportion &&
              item.extensionProportion
          )
        );
        this.$set(
          item,
          "calibrationDistributionIndexColor",
          item.calibrationDistributionIndex &&
            item.calibrationDistributionIndex > 0
            ? "rgba(191, 191, 0, 0.***************)"
            : ""
        );
        if (
          item.calibrationExtensionIndexBankState &&
          item.calibrationExtensionIndexProjectState
        ) {
          let temp = "";
          if (
            item.calibrationExtensionIndexBankState.indexOf("已完成") != -1 &&
            item.calibrationExtensionIndexProjectState.indexOf("已完成") != -1
          ) {
            let partA =
              item.calibrationExtensionIndexProjectState.split("-")[1];
            let partB = item.calibrationExtensionIndexBankState.split("-")[1];
            let uniqueParts = new Set([partA, partB]);
            temp = `已完成-${Array.from(uniqueParts).join(",")}`;
          } else if (
            item.calibrationExtensionIndexBankState.indexOf("已完成") != -1 ||
            item.calibrationExtensionIndexProjectState.indexOf("已完成") != -1
          ) {
            if (
              item.calibrationExtensionIndexBankState.indexOf("已完成") != -1
            ) {
              temp = item.calibrationExtensionIndexBankState;
            }
            if (
              item.calibrationExtensionIndexProjectState.indexOf("已完成") != -1
            ) {
              temp = item.calibrationExtensionIndexProjectState;
            }
          } else {
            temp = "未完成";
          }
          this.$set(item, "calibrationExtensionIndexState", temp);
        } else {
          this.$set(
            item,
            "calibrationExtensionIndexState",
            item.calibrationExtensionIndexBankState ||
              item.calibrationExtensionIndexProjectState
          );
        }
        delete item.checkResult;
        return item;
      });
      this.changeShowResult(this.showList[3]);
    },
    async getColumnsResultsInit() {
      this.columnsResultsInit = {
        1: this.columnsResultsInitCompany,
        2: this.columnsResultsInitDept,
        3: this.columnsResultsInitUser,
      }[this.pageResults.type];
      //发送请求获取列 如果没有则用默认值
      const { rows } = await checkResultList();
      rows.forEach((item) => {
        this.columnsResultsAddInit.forEach((item1) => {
          if (item.columns == item1.columns) {
            this.$set(item, "label", item1.label);
          }
        });
      });
      this.columnsResultsChange = rows;
      this.columnsResults = this.columnsResultsInit.concat(
        this.getColumnsResultsChange(rows),
        this.columnsResultsOperate
      );
    },
    getColumnsResultsChange(rows) {
      const showColumns = rows
        .filter((item) => item.visible)
        .map((item) => item.columns);
      const columnsResultsAddInit = this.columnsResultsAddInit.filter(
        (item) => {
          if (this.pageResults.type == 1) {
            return !["deptNameBelong", "companyShortNameBelong"].includes(
              item.columns
            );
          } else if (this.pageResults.type == 2) {
            return !["deptNameBelong"].includes(item.columns);
          } else if (this.pageResults.type == 3) {
            return !["companyShortNameBelong"].includes(item.columns);
          }
        }
      );
      return columnsResultsAddInit.filter((item) => {
        return showColumns.includes(item.columns);
      });
    },
    async handleClose() {
      this.columnsResults = this.columnsResultsInit.concat(
        this.getColumnsResultsChange(this.columnsResultsChange),
        this.columnsResultsOperate
      );
      await replaceColumnsInit(this.columnsResultsChange);
    },
    handleCommand(command, record) {
      const obj = {
        calibration: this.calibration,
        supplement: this.supplement,
      };
      obj[command](record);
    },

    calibration(record) {
      const title = {
        1: "考核结果校准-公司",
        2: "考核结果校准-部门",
        3: "考核结果校准-用户",
      }[this.pageResults.type];
      const id = {
        1: record.companyId,
        2: record.deptId,
        3: record.userId,
      }[this.pageResults.type];
      const proForm = JSON.stringify({
        type: record.type,
        year: record.year,
        quarter: record.quarter,
        userId: record.userId,
        deptId: record.deptId,
        companyId: record.companyId,
      });
      this.$router.push({
        path: `/perAppraisalOther/assessmentResults/${id}`,
        query: {
          title,
          proForm,
        },
      });
    },
    supplement(record) {
      this.resultsForm.openSupplement = true;
      this.resultsForm.currentForm = { ...record };
    },

    onSure() {
      this.openSure = true;
    },
    cancel() {
      const obj = { path: "/PerAppraisalOther/assessmentResultsConfirmation" };
      this.$tab.closeOpenPage(obj);
    },
    changeShowResult(item) {
      this.$nextTick(() => {
        if (item.show) {
          const dom = this.$refs[item.ref][0];
          dom.style.transition = "none";
          dom.style.height = "auto";
          const height = dom.offsetHeight;
          dom.style.height = 0;
          dom.offsetHeight;
          dom.style.transition = ".5s";
          dom.style.height = height + "px";
          // setTimeout(() => {
          //   const doms = document.querySelector(".page-component__scroll");
          //   console.log(doms.scrollHeight, 1111);
          //   doms.scrollTo(0, doms.scrollHeight);
          // }, 450);
        }
      });
    },
    changeShow(item) {
      this.$nextTick(() => {
        item.show = !item.show;
        const dom = this.$refs[item.ref][0];
        if (item.show) {
          dom.style.transition = "none";
          dom.style.height = "auto";
          const height = dom.offsetHeight;
          dom.style.height = 0;
          dom.offsetHeight;
          dom.style.transition = ".5s";
          dom.style.height = height + "px";
        } else {
          dom.style.transition = ".5s";
          dom.style.height = 0;
        }
      });
    },
    colunmStyle({ row, column, rowIndex, columnIndex }) {
      //第一列背景色
      if (columnIndex == 1) {
        return "background:#f8f8f9";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.el-backtop {
  width: 100px;
}
::v-deep .pagination-container .el-pagination {
  left: 0;
}

::v-deep .el-table {
  .el-table__header-wrapper {
    table {
      thead {
        th {
          font-weight: bold;
          color: #333;
          // 换行
          .cell {
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}
</style>
