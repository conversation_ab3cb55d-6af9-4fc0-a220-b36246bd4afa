<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="警告"
      :visible.sync="innerValue"
      width="550px"
      @close="handleClose"
      @open="handleOpen"
    >
      <div>
        <div>
          <div v-for="(item, index) in myForm" :key="index">
            {{
              `【${item.companyShortName}】【${item.year}】年度【${
                quarterObj[item.quarter]
              }】`
            }}；
          </div>
          <div style="color: rgb(245, 108, 108)">
            审批通过后，对应公司的本季度考核结果将不可修改，考核配置的修改对已确认的考核结果不再生效，对应季度的项目业绩不可修改。
          </div>
        </div>
        <div class="text-center my-2">请输入登录密码，进行下一步操作</div>
        <div>
          <el-input
            class="block m-auto"
            style="width: 240px"
            placeholder="请输入登录密码"
            v-model.trim="password"
            show-password
            @keyup.enter.native="onSubmit"
          ></el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="mr-3">取消</el-button>
          <el-button
            @click="onSubmit"
            :disabled="!Boolean(password)"
            type="primary"
            class="ml-3"
            >提交</el-button
          >
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { JSEncrypt } from "jsencrypt";
import { getPublicCode } from "@/api/login";
import { checkPwd } from "@/api/perAppraisal/assessmentConfiguration";
import XEUtils from "xe-utils";

import vModelMixin from "@/mixin/v-model";
export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      myForm: [],
      password: "",
      quarterObj: Object.freeze({
        1: "一季度",
        2: "二季度",
        3: "三季度",
        4: "四季度",
      }),
    };
  },

  mounted() {},
  methods: {
    handleOpen() {
      this.myForm = XEUtils.clone(this.form, true);
    },
    async onSubmit() {
      if (!this.password) {
        this.$message.error("请输入密码");
        return;
      }
      const res = await getPublicCode();
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(res);
      //对字段进行加密
      const data = {};
      data.password = encrypt.encrypt(this.password);
      await checkPwd(data);
      this.innerValue = false;
      this.$emit("on-submit-success");
    },

    handleClose() {
      this.password = "";
    },
  },
};
</script>
