<template>
  <div class="p-5">
    <div class="mb-2 font-bold text-sm">开票详情：</div>
    <el-descriptions :column="1" border class="custom-descriptions">
      <el-descriptions-item label="开票申请编号">{{
        invoiceInfo.invoicingApplicationCode
      }}</el-descriptions-item>
      <el-descriptions-item label="开票申请时间">{{
        invoiceInfo.invoicingApplicationTime
      }}</el-descriptions-item>
      <el-descriptions-item label="渠道">{{
        channelObj[invoiceInfo.channel]
      }}</el-descriptions-item>
      <el-descriptions-item label="借款人">{{
        invoiceInfo.borrower
      }}</el-descriptions-item>
      <el-descriptions-item label="手机号">{{
        invoiceInfo.phoneNum
      }}</el-descriptions-item>
      <el-descriptions-item label="身份证号">{{
        invoiceInfo.idCard
      }}</el-descriptions-item>
      <el-descriptions-item label="开票金额（元）">{{
        invoiceInfo.invoicingAmountTotal
      }}</el-descriptions-item>
      <el-descriptions-item label="发票附件" v-if="$route.query.type == '2'">
        <el-button
          type="text"
          style="color: #409eff"
          @click="handleDownload(invoiceInfo)"
        >
          <i class="el-icon-document"></i>
          {{ invoiceInfo.fileName }}
        </el-button>
      </el-descriptions-item>
      <el-descriptions-item label="开票主体">{{
        invoiceInfo.mainBodyName
      }}</el-descriptions-item>
      <el-descriptions-item label="接收邮箱">{{
        invoiceInfo.receivingEmail
      }}</el-descriptions-item>
      <el-descriptions-item
        label="邮箱推送时间"
        v-if="$route.query.type == '2'"
        >{{ invoiceInfo.pushTime }}</el-descriptions-item
      >
    </el-descriptions>

    <div class="mb-2 mt-4 font-bold text-sm">开票明细：</div>
    <el-table :data="invoiceDetails" border style="width: 100%">
      <el-table-column
        prop="invoicingApplicationCode"
        label="借据申请编号"
        align="center"
      />
      <el-table-column
        prop="invoicingAmount"
        label="申请开票金额（元）"
        align="center"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <div class="text-center mt-4">
      <el-button @click="$router.back()">返回</el-button>
    </div>
  </div>
</template>

<script>
import { businessDetail } from "@/api/debtConversion/businessInvoicingApplication";
import privew from "@/mixin/privew";
import { clone } from "xe-utils";
import config from "./config";

export default {
  name: "InvoiceDetail",
  mixins: [privew],
  data() {
    return {
      ...config,
      loading: false,
      invoiceInfo: {
        invoicingApplicationCode: "",
        invoicingApplicationTime: "",
        channel: "",
        borrower: "",
        phoneNum: "",
        idCard: "",
        invoicingAmountTotal: "",
        mainBodyName: "",
        receivingEmail: "",
        pushTime: "",
      },
      invoiceDetails: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    async getDetail() {
      this.loading = true;
      await businessDetail(this.$route.query.id).then((res) => {
        this.invoiceInfo = res.data;
        this.invoiceDetails =
          res.data.channel == 2
            ? res.data.debtConversionVoList
            : res.data.invoicingApplicationVoList;
        this.invoiceDetails.forEach((item) => {
          if (res.data.channel == 2) {
            item.invoicingAmount = item.loanAmount;
            item.invoicingApplicationCode = item.debtConversionCode;
          } 
        });
        this.total = res.data.invoiceDetails
          ? res.data.invoiceDetails.length
          : 0;
        this.getList();
      });
      this.loading = false;
    },
    getList() {
      const { pageNum, pageSize } = this.queryParams;
      const allData = clone(this.invoiceDetails,true) || [];
      this.total = allData.length;
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      this.invoiceDetails = allData.slice(startIndex, endIndex);
    },
  },
};
</script>

<style scoped >
::v-deep .el-descriptions-item__label {
  width: 300px;
}
</style>


