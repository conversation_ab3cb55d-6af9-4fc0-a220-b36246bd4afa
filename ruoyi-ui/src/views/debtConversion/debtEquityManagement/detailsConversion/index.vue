<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>
    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          v-hasPermi="['debtEquityManagement:detailsConversion:export']"
          @click="handleExport"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          >批量导出</el-button
        >
      </div>
    </div>

    <MyTable
      ref="table"
      :columns="columns"
      :source="importList"
      showCheckbox
      @selection-change="handleSelectionChange"
    >
      <template #operation="{ record }">
        <el-button size="mini" type="text" @click="debtConversionNotice(record)"
          >债转通知书</el-button
        >
        <el-button
          size="mini"
          type="text"
          @click="handleDelete(record)"
          v-hasPermi="['debtEquityManagement:detailsConversion:delete']"
          >删除</el-button
        >
      </template>
    </MyTable>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { conversionList, delConversionList } from "@/api/debtConversion/detailsConversion";
import config from "./components/config";
import { clone } from "xe-utils";
export default {
  name: "DetailsConversion",
  data() {
    return {
      ...config,
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      importList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        borrower: undefined,
        idCard: undefined,
        phoneNum: undefined,
      },

      // 选中的行
      selectedRows: [],
      // 批量操作弹窗
      batchVisible: false,
      // 用户是否同意状态映射
      userAgreeMap: {
        Y: "是",
        N: "未知",
        "": "未知（默认）",
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    /** 查询导入列表 */
    getList() {
      this.loading = true;
      conversionList(this.queryParams).then((response) => {
        this.importList = response.rows;
        this.handleImportList();
        this.total = response.total;
        this.loading = false;
      });
    },
    handleImportList() {
      this.importList.forEach((item) => {
        item.pushChannel = this.pushChannelObj[item.pushChannel];
        item.isRead = this.stringObj[item.isRead];
        item.registerMiniProgram = this.stringObj[item.registerMiniProgram];
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm(
          '是否确认删除债权凭证编号为"' + row.debtConversionCode + '"的数据项？'
        )
        .then(() => {
          return delConversionList(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },

    handleExport() {
      this.download(
        "/debt/conversion/export",
        {
          ...this.queryParams,
          ids: this.selectedRows?.map((item) => item.id)?.join(","),
        },
        `债转通知明细.xlsx`
      );
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    debtConversionNotice(record) {
      const message = `
        <div style="text-align: center; margin-bottom: 20px;">
          <h3>债转通知书</h3>
        </div>
        <div style="text-indent: 2em; line-height: 2;">
          <p style="margin-bottom: 10px;">【<span style="color: red;">${record.custFullName}</span>】</p>
          <p style="margin-bottom: 10px;">尊敬的客户名字：<span style="color: red;">${record.borrower}</span></p>
          <p style="line-height: 2;">您于<span style="color: red;">${record.loanTime}</span>向<span style="color: red;">${record.fundName}</span>贷款<span style="color: red;">${record.loanAmount}元</span>，因您未按约履行还款义务，我司已于<span style="color: red;">${record.guaranteeTime}</span>向<span style="color: red;">${record.fundName}</span>承担了担保责任，依法取得对您的担保追偿权。现我司已将对您的上述担保追偿权转让至<span style="color: red;">${record.debtRecipientName}</span>,请您自${record.noticeLaunchTime}起向<span style="color: red;">${record.debtRecipientName}</span>履行还款义务，特此通知。</p>
        </div>
       
      `;
      this.$alert(message, "", {
        dangerouslyUseHTMLString: true,
        confirmButtonText	:'关闭'
      });
    },
  },
};
</script> 