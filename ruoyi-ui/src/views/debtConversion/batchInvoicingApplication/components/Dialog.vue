<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="1050px"
      @open="handleOpen"
    >
      <div class="pr-5">
        <MyTable :columns="dialogColumns" :source="tableData"> </MyTable>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      <span slot="footer">
        <div class="flex justify-end">
          <el-button @click="innerValue = false">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
  
  <script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import { applicationList } from "@/api/debtConversion/batchInvoicingApplication";

export default {
  mixins: [vModelMixin],
  name: "Dialog",
  props: {
    form: {
      type: Object,
      required: true,
      default: () => {
        return {};
      },
    },
  },
  computed: {},
  data() {
    return {
      ...config,
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  mounted() {},
  methods: {
    handleOpen() {
      this.getList();
    },
    getList() {
      this.loading = true;
      applicationList(this.queryParams).then((response) => {
        this.tableData = response.rows;
        this.total = response.total;
      });
    },
  },
};
</script>
  <style lang="less" scoped>
</style>