<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      width="1050px"
      @close="handleClose"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div class="content">
          <el-form ref="form" :model="form" label-width="130px" :rules="rules">
            <div class="flex">
              <el-form-item label="员工姓名" class="form-item" prop="name">
                <el-input
                  v-model.trim="form.name"
                  placeholder="请输入员工姓名"
                  :disabled="disabled"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="入职申请编号" class="form-item">
                <el-input v-model="form.onboardingCode" disabled></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                label="入职公司"
                class="form-item"
                prop="onboardingCompany"
              >
                <el-select
                  v-model="form.onboardingCompany"
                  placeholder="请选择入职公司"
                  :disabled="disabled"
                  filterable 
                  style="width: 280px"
                  @change="changeOnboardingCompany"
                >
                  <el-option
                    v-for="dict in unitListEnableList"
                    :key="dict.unitId"
                    :label="dict.unitName"
                    :value="dict.unitId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="入职部门"
                class="form-item"
                prop="onboardingDept"
              >
                <treeselect
                  style="width: 200px"
                  v-model="form.onboardingDept"
                   noOptionsText="暂无数据"
                  :options="deptOptionsFilter"
                  :normalizer="normalizer"
                  :show-count="true"
                  placeholder="选择入职部门"
                  :disabled="disabled"
                />
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                label="入职岗位"
                class="form-item"
                prop="onboardingPost"
              >
                <el-select
                  v-model="form.onboardingPost"
                  placeholder="入职岗位"
                  clearable
                  size="small"
                  :disabled="disabled"
                  filterable
                >
                  <el-option
                    v-for="dict in onboardingPostList"
                    :key="dict.postId"
                    :label="dict.postName"
                    :value="dict.postId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="入职职级" class="form-item">
                <el-select
                  v-model="form.onboardingRank"
                  placeholder="请选择入职职级"
                  :disabled="disabled"
                >
                  <el-option
                    v-for="dict in dict.type.entry_rank"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="身份证号码" class="form-item" prop="idCard">
                <el-input
                  v-model.trim="form.idCard"
                  placeholder="请输入身份证号码"
                  :disabled="disabled"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="出生日期" class="form-item" disabled>
                <el-input v-model="form.birthday" disabled></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="手机号" class="form-item" prop="phoneNum">
                <el-input
                  v-model.trim="form.phoneNum"
                  placeholder="请输入手机号"
                  :disabled="disabled"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="性别" class="form-item" prop="sex">
                <el-select
                  v-model="form.sex"
                  placeholder="请选择性别"
                  :disabled="disabled"
                >
                  <el-option
                    v-for="dict in dict.type.sys_user_sex"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="籍贯" class="form-item">
                <el-input
                  v-model.trim="form.hometown"
                  placeholder="请输入籍贯"
                  :disabled="disabled"
                  clearable
                  maxlength="50"
                ></el-input>
              </el-form-item>
              <el-form-item label="现住址" class="form-item" prop="currentAddress">
                <el-input
                  v-model.trim="form.currentAddress"
                  placeholder="请输入现住址"
                  :disabled="disabled"
                  clearable
                  maxlength="100"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                label="入职日期"
                class="form-item"
                prop="onboardingTime"
              >
                <el-date-picker
                  v-model="form.onboardingTime"
                  align="right"
                  type="date"
                  placeholder="请选择入职日期"
                  :picker-options="pickerOptions"
                  :disabled="disabled"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                label="办公场地"
                class="form-item"
                prop="officeSpace"
              >
                <el-input
                  v-model.trim="form.officeSpace"
                  placeholder="请输入办公场地"
                  :disabled="disabled"
                  clearable
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                label="参加工作时间"
                class="form-item"
                prop="workTime"
              >
                <el-date-picker
                  v-model="form.workTime"
                  align="right"
                  type="date"
                  placeholder="请选择参加工作时间"
                  :picker-options="pickerOptions"
                  :disabled="disabled"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                label="人员类别"
                class="form-item"
                prop="personnelType"
              >
                <el-select
                  v-model="form.personnelType"
                  placeholder="请选择人员类别"
                  :disabled="disabled"
                >
                  <el-option
                    v-for="dict in dict.type.personnel_category"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                label="试用期期限(月)"
                class="form-item"
                prop="probationMonths"
              >
                <el-input
                  v-model.trim="form.probationMonths"
                  placeholder="请输入试用期期限"
                  :disabled="disabled"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                label="预计转正日期"
                class="form-item"
                prop="formalTime"
              >
                <el-date-picker
                  v-model="form.formalTime"
                  align="right"
                  type="date"
                  placeholder="请选择转正日期"
                  :picker-options="pickerOptions"
                  :disabled="disabled"
                  value-format="yyyy-MM-dd"
                  @change="changeFormalTime"
                >
                </el-date-picker>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="转正薪酬(元)" class="form-item">
                <el-input
                  v-model.trim="form.formalSalary"
                  placeholder="请输入转正薪酬"
                  :disabled="disabled"
                  clearable
                  @input="changeFormalSalary"
                  @blur="blurFormalSalary"
                ></el-input>
              </el-form-item>
              <el-form-item label="试用期薪酬(元)" class="form-item">
                <el-input
                  v-model.trim="form.probationSalary"
                  placeholder="请输入试用期薪酬"
                  :disabled="disabled"
                  clearable
                  @input="changeProbationSalary"
                  @blur="blurProbationSalary"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="直接上级" class="form-item"  prop="leaderName">
                <el-input
                  @click.native="selectName(false)"
                  :disabled="disabled"
                  placeholder="请选择直接上级"
                  v-model="form.leaderName"
                  suffix-icon="el-icon-search"
                >
                </el-input>
              </el-form-item>
              <el-form-item
                label="政治面貌"
                class="form-item"
                prop="politicalLandscape"
              >
                <el-select
                  v-model="form.politicalLandscape"
                  placeholder="请选择政治面貌"
                  :disabled="disabled"
                >
                  <el-option
                    v-for="dict in dict.type.political_outlook"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="所在公司上级" class="form-item"  prop="localLeaderName">
                <el-input
                  @click.native="selectName(true)"
                  :disabled="disabled"
                  placeholder="请选择所在公司上级"
                  v-model="form.localLeaderName"
                  suffix-icon="el-icon-search"
                >
                </el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                label="系统登录名"
                class="form-item"
                prop="sysName"
              >
                <el-input
                  v-model.trim="form.sysName"
                  placeholder="请输入系统登录名"
                  :disabled="disabled"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                label="系统初始密码"
                class="form-item"
                prop="initialPassword"
              >
                <el-input
                  v-model.trim="form.initialPassword"
                  :disabled="disabled"
                  clearable
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="开户行" class="form-item" prop="openingBank">
                <el-input
                  v-model.trim="form.openingBank"
                  placeholder="请输入开户行"
                  :disabled="disabled"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                label="工资账号"
                class="form-item"
                prop="salaryAccount"
              >
                <el-input
                  v-model.trim="form.salaryAccount"
                  :disabled="disabled"
                  placeholder="请输入工资账号"
                  clearable
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="门禁卡申领" class="form-item">
                <el-select
                  v-model="form.accessCard"
                  placeholder="审批过程中选择"
                  disabled
                >
                  <el-option
                    v-for="dict in accessCardList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="办公用品领用" class="form-item">
                <el-input
                  v-model.trim="form.officeSupplies"
                  disabled
                  placeholder="审批过程中填写"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="文件柜领用" class="form-item">
                <el-input
                  v-model.trim="form.fileCabinet"
                  placeholder="审批过程中填写"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="文件柜编号" class="form-item">
                <el-input
                  v-model.trim="form.fileCabinetnum"
                  disabled
                  placeholder="审批过程中填写"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="名片印制" class="form-item">
                <el-input
                  v-model.trim="form.cardPrinting"
                  placeholder="审批过程中填写"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="企业邮箱" class="form-item">
                <el-input
                  v-model.trim="form.enterpriseEmail"
                  disabled
                  placeholder="审批过程中填写"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item label="阿里云账号" class="form-item">
                <el-input
                  v-model.trim="form.aliAccount"
                  placeholder="审批过程中填写"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="考勤机录入" class="form-item">
                <el-select
                  v-model="form.attendanceEntry"
                  disabled
                  placeholder="请选择考勤机录入"
                >
                  <el-option
                    v-for="dict in attendanceEntryList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-one">
              <el-form-item label="附件上传" prop="fileList">
                <el-upload
                  :disabled="disabled"
                  :headers="upload.headers"
                  :action="upload.url"
                  :on-success="handleFileSuccess"
                  :on-remove="handleRemove"
                  multiple
                  :file-list="fileList"
                  :on-preview="openFile"
                >
                  <el-button :disabled="disabled"
                    ><i class="el-icon-upload2"></i>上传附件</el-button
                  >
                </el-upload>
              </el-form-item>
            </div>
            <div class="flex-one">
              <el-form-item label="工作职责">
                <el-input
                  type="textarea"
                  v-model="form.duty"
                  placeholder="请输入工作职责"
                  maxlength="600"
                  :disabled="disabled"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex-one">
              <el-form-item label="备注">
                <el-input
                  type="textarea"
                  v-model="form.remark"
                  placeholder="请输入备注"
                  maxlength="600"
                  :disabled="disabled"
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button
            type="primary"
            @click="onSave"
            v-if="title != '查看人员入职'"
            >保存</el-button
          >
          <el-button
            type="primary"
            @click="onSubmit"
            v-if="title != '查看人员入职'"
            >提交</el-button
          >
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
    <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
    <ViewDialog v-model="openView" @on-save-success="successView" />
  </div>
</template>

<script>
import {
  getBirthdatByIdNo,
  commonAddMouth,
  decimal,
  returnFloat,
  compareDate,
  getTreeName,
} from "@/utils";
import { addOnboarding, updateOnboarding } from "@/api/personnel/entry";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import Treeselect from "@riophae/vue-treeselect";
import { getUnitListEnable } from "@/api/system/unit";
import { treeselect } from "@/api/system/dept";
import privew from "@/mixin/privew";
import ViewDialog from "./ViewDialog.vue";
export default {
  mixins: [vModelMixin, privew],
  components: { Treeselect, ViewDialog },
  dicts: [
    "entry_rank",
    "sys_user_sex",
    "personnel_category",
    "political_outlook",
  ],

  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    deptOptions: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
    onboardingPostList: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
    disabled: {
      type: Boolean,
    },
    title: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      ...config,
      unitListEnableList: [],
      fileList: [],
      fileListIds: [],
      deptOptionsFilter: [],
      isFirst: false,
      openView: false,
      onboardingDeptName: "",
      onboardingPostName: "",
      isLocal:false,
    };
  },
  watch: {
    "form.onboardingCompany": {
      handler: function (newValue, oldValue) {
        if (newValue) this.changeValue(newValue);
      },
      immediate: true,
    },
    "form.onboardingDept": {
      handler: function (newValue, oldValue) {
        this.onboardingDeptName = getTreeName(
          this.deptOptionsFilter,
          newValue
        ).label;
      },
    },
    "form.onboardingPost": {
      handler: function (newValue, oldValue) {
        this.onboardingPostName = this.onboardingPostList.filter(
          (item) => item.postId == newValue
        )[0].postName;
      },
    },
    "form.idCard"(val) {
      if (!val) return;
      if (
        (val.length == 18 || val.length === 15) &&
        this.regular.regularCard.test(val)
      ) {
        this.form.birthday = getBirthdatByIdNo(val);
      } else {
        this.form.birthday = "";
      }
    },
    "form.onboardingTime"(val) {
      if (!this.isFirst) return;
      if (val && this.form.probationMonths) {
        this.$set(
          this.form,
          "formalTime",
          commonAddMouth(val, this.form.probationMonths)
        );
      } else {
        this.$set(this.form, "formalTime", "");
      }
    },
    "form.probationMonths"(val) {
      if (!this.isFirst) return;
      if (val && this.form.onboardingTime) {
        this.$set(
          this.form,
          "formalTime",
          commonAddMouth(this.form.onboardingTime, val)
        );
      } else {
        this.$set(this.form, "formalTime", "");
      }
    },
    "form.files"(val) {
      if (this.fileList.length) return;
      if (val && val.length) {
        this.fileList = val.map((item) => {
          return {
            name: item.fileName,
            id: item.id,
            url: process.env.VUE_APP_BASE_API + item.fileUrl,
            downLoadUrl: item.fileUrl,
          };
        });
        this.getFileListIds(this.fileList);
        this.$set(this.form, "fileList", true);
      }
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.getUnitListEnable();
    },
    changeOnboardingCompany() {
      this.$set(this.form, "onboardingDept", null);
    },
    async changeValue(value) {
      const { data } = await treeselect({ unitId: value });
      this.deptOptionsFilter = [...data];
    },
    async getUnitListEnable() {
      const { data } = await getUnitListEnable();
      this.unitListEnableList = data;
    },
    handleOpen() {
      this.$nextTick(() => {
        this.$refs["form"].clearValidate();
        this.isFirst = true;
      });
    },
    successView(value) {
      if(!this.isLocal){
        this.$set(this.form, "directSuperior", value[0].userId);
        this.$set(this.form, "leaderName", value[0].nickName);
      }else{
        this.$set(this.form, "localLeader", value[0].userId);
        this.$set(this.form, "localLeaderName", value[0].nickName);
      }
    },
    selectName(isLocal) {
      this.isLocal=isLocal;
      this.openView = true;
    },
    changeFormalTime() {
      this.$set(this.form, "probationMonths", "");
    },
    changeFormalSalary(val) {
      this.form.formalSalary = decimal(val, 2);
    },
    blurFormalSalary() {
      this.form.formalSalary = returnFloat(this.form.formalSalary);
      this.$set(
        this.form,
        "probationSalary",
        (this.form.formalSalary * 0.8).toFixed(2)
      );
    },
    changeProbationSalary(val) {
      this.$set(this.form, "probationSalary", decimal(val, 2));
    },
    blurProbationSalary() {
      this.$set(
        this.form,
        "probationSalary",
        returnFloat(this.form.probationSalary)
      );
    },
    workTimeChange(value) {
      if (!(value && this.form.onboardingTime)) return;
      if (compareDate(this.form.onboardingTime, value)) {
        this.$message.error("参加工作时间不得晚于入职日期");
        this.$set(this.form, "workTime", "");
      }
    },
    onboardingChange(value) {
      if (!(value && this.form.workTime)) return;
      if (compareDate(value, this.form.workTime)) {
        this.$message.error("参加工作时间不得晚于入职日期");
        this.$set(this.form, "onboardingTime", "");
      }
    },
    handleFileSuccess(response, file, fileList) {
      this.fileList = fileList;
      this.getFileListIds(fileList);
    },
    handleRemove(file, fileList) {
      this.getFileListIds(fileList);
    },
    openFile(value) {
      this.handlePreview(value);
    },
    getFileListIds(value) {
      this.fileListIds = value.map((item) => item.id || item.response.data.id);
      this.$set(this.form, "fileList", this.fileListIds.length ? true : "");
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const showList = this.fileList.map((item) => {
            return {
              fileName: item.response?.data.fileName||item.name,
              fileUrl: item.response?.data.fileUrl||item.downLoadUrl,
              id: item.response?.data.id||item.id,
            };
          });
          this.$emit("on-submit-success", {
            ...this.form,
            fileIds: this.fileListIds,
            files: showList,
            onboardingDeptName: this.onboardingDeptName,
            onboardingPostName: this.onboardingPostName,
          });
        }
      });
    },
    onSave() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const params = {
            ...this.form,
            fileIds: this.fileListIds,
          };
          if (this.title == "修改人员入职") {
            await updateOnboarding(params);
            this.$modal.msgSuccess("修改成功");
          } else {
            await addOnboarding(params);
            this.$modal.msgSuccess("新增成功");
          }
          this.innerValue = false;
          this.$emit("on-save-success");
        }
      });
    },
    handleClose() {
      this.$refs.form.resetFields();
      this.fileList = [];
      this.fileListIds = [];
      this.isFirst = false;
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  max-height: 70vh;
  padding-right: 20px;
  .flex {
    display: flex;
    justify-content: space-between;

    .form-item {
      width: 48%;
    }
  }
  .flex-one {
    width: 70%;
    display: flex;

    ::v-deep .el-form-item {
      width: 100%;
    }
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>
