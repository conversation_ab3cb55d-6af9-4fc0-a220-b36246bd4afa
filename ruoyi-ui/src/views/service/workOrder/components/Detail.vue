<template>
  <div>
    <el-drawer
      :visible.sync="drawer"
      direction="rtl"
      :show-close="false"
      size="1000px"
      :before-close="handleClose"
    >
      <div class="drawer" v-if="data">
        <div class="left">
          <el-button size="mini" @click="handleClose">返回</el-button>
          <el-button
            @click="rlwork"
            v-hasPermi="['kf:workstatus:update']"
            v-if="data.workOrderStatus == 'READY' && !disabled"
            size="mini"
            style="background: #ff9900; color: #fff; border: none"
            >认领工单
          </el-button>
          <div style="font-weight: bold; margin-top: 16px">工单信息：</div>
          <el-table
            :show-header="false"
            :data="tableData"
            border
            style="width: 98%; margin: 16px 0"
          >
            <el-table-column width="140" prop="name" align="right" />
            <el-table-column prop="value" align="left">
              <template slot-scope="scope">
                <span v-if="scope.row.name == '公司'">
                  <dict-tag
                    :options="dict.type.kf_company_list"
                    :value="scope.row.value"
                  />
                </span>
                <span v-else-if="scope.row.name == '工单来源'">
                  <dict-tag
                    :options="dict.type.kf_source_type"
                    :value="scope.row.value"
                  />
                </span>
                <span
                  v-else-if="
                    scope.row.name == '已等待时长' &&
                    data.workOrderStatus == 'COMPLETE'
                  "
                >
                  --
                </span>
                <span v-else-if="scope.row.name == '渠道'">
                  <div v-for="item in channelList" :key="item.value">
                    <span v-if="item.value == scope.row.value">{{
                      item.label
                    }}</span>
                  </div>
                </span>
                <span v-else-if="scope.row.name == '工单状态'">
                  <dict-tag
                    :options="dict.type.kf_work_order_status"
                    :value="scope.row.value"
                  />
                </span>
                <span v-else-if="scope.row.name == '附件'">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="allDown"
                    v-if="fileList.length > 0"
                    v-hasPermi="['kf:work:downloadFileZip']"
                    >全部下载</el-button
                  >
                  <div v-for="item in fileList" :key="item.id">
                    <el-button
                      v-hasPermi="['kf:work:downloadFileZip']"
                      type="text"
                      style="margin-left: 10px"
                      @click="downLoad(item)"
                      >{{ item.fileName }}</el-button
                    >
                  </div>
                </span>
                <span v-else>{{ scope.row.value }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div style="font-weight: bold; margin: 16px 0">工作记录：</div>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in recordList"
              reverse="false"
              :key="index"
              placement="bottom"
              :timestamp="activity.time"
            >
              <span
                ><span style="font-weight: bold">{{
                  activity.userName + "："
                }}</span>
                <span style="margin-left: 8px">{{
                  activity.optionType == 1
                    ? "提交工单"
                    : activity.optionType == 2
                    ? "认领工单"
                    : activity.optionType == 4
                    ? `发布工作记录：${activity.infoText}`
                    : activity.optionType == 3
                    ? "放弃认领，工单已变为 [待认领] 状态"
                    : activity.optionType == 5
                    ? "选择不处理，工单已变为 [不处理] 状态"
                    : activity.optionType == 6
                    ? "完成工单"
                    : `添加备注：${activity.infoText}`
                }}</span>
              </span>
              <el-button
                v-if="
                  activity.optionType == 4 &&
                  data.workOrderStatus == 'LOAD' &&
                  !disabled &&
                  activity.accountName == loginUserName
                "
                v-hasPermi="['kf:workremark:delete']"
                @click="deletedynamic(activity)"
                style="margin-left: 8px"
                type="text"
                >删除
              </el-button>
            </el-timeline-item>
          </el-timeline>
          <div
            v-if="
              loginUserName == data.claimUserId &&
              data.workOrderStatus == 'LOAD' &&
              !disabled
            "
          >
            <div style="display: flex; align-items: center">
              <el-button
                v-hasPermi="['kf:workremark:insert']"
                type="primary"
                size="mini"
                @click="addRecord"
                >发布工作记录
              </el-button>
              <div style="color: #ccc; font-size: 14px; margin-left: 9px">
                记录工单处理工作中的重要信息
              </div>
            </div>
            <div style="font-size: 14px; margin-top: 16px">
              您已认领此工单<br />[已完成]
              ：联系并答复客户后，点击此按钮以完成此工单<br />[放弃认领]
              ：不再作为此工单的客服，工单将可以被其他客服认领<br />[不处理]
              ：将无效信息的工单移至不处理列表中
            </div>
            <div
              v-hasPermi="['kf:workstatus:update']"
              style="display: flex; margin-top: 20px"
            >
              <el-button type="primary" size="mini" @click="update(6)"
                >已完成
              </el-button>
              <el-button
                size="mini"
                @click="update(3)"
                style="color: #1890ff; border-color: #1890ff"
                >放弃认领
              </el-button>
              <el-button
                @click="update(5)"
                size="mini"
                style="color: #1890ff; border-color: #1890ff"
                >不处理
              </el-button>
            </div>
          </div>
        </div>
        <div class="right">
          <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="客户信息" name="first">
              <p>客户姓名：{{ data.userName }}</p>
              <p>
                客户手机号码：{{
                  queryData ? queryData.userPhone : data.userPhone
                }}
              </p>
              <p>
                客户身份证号码：{{
                  queryData ? queryData.userIdCardNum : data.userIdCardNum
                }}
              </p>
              <p>
                <span
                  style="font-size: 13px; color: #1890ff; cursor: pointer"
                  v-if="data.claimUserId === loginUserName && !disabled"
                  @click="changeXiuGaiCustomer()"
                  v-html="
                    '修改客户信息' + '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'
                  "
                />
                <span
                  style="font-size: 13px; color: #1890ff; cursor: pointer"
                  v-html="'编辑记录 (' + (!data.kcount ? 0 : data.kcount) + ')'"
                  @click="changeBianJiJiLu()"
                />
              </p>
              <el-divider></el-divider>
              <div>
                客户备注
                <el-tooltip class="item" effect="dark" placement="top-start">
                  <div slot="content">
                    客户备注用户描述此客户的特征，方便下次接待此客户时针对性提供服务<br />任何客服人员接待此客户时，都可看到客户备注信息
                  </div>
                  <span class="relative bottom-0.5">①</span>
                </el-tooltip>
              </div>
              <el-table
                v-if="remarkList.length > 0"
                border
                :data="remarkList"
                style="width: 100%; margin-top: 16px"
              >
                <el-table-column prop="time" label="备注时间" />
                <el-table-column prop="userName" label="录入人" />
                <el-table-column prop="infoText" label="备注" />
                <el-table-column prop="date" label="操作">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      v-hasPermi="['kf:workremark:delete']"
                      v-if="
                        scope.row.accountName == loginUserName &&
                        !disabled &&
                        data.workOrderStatus == 'LOAD'
                      "
                      @click="delRemark(scope.row)"
                      >删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div v-else style="color: #ccc; margin-top: 12px">
                暂无客户备注信息
              </div>
              <el-button
                v-hasPermi="['kf:workremark:insert']"
                type="text"
                @click="addRemark"
                v-if="!disabled && data.workOrderStatus == 'LOAD'"
                >+添加备注
              </el-button>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <el-dialog
        :visible.sync="dialogVisible"
        width="600px"
        append-to-body
        :show-close="false"
        :before-close="handleClose2"
      >
        <div style="margin-bottom: 16px">
          {{ addType == 1 ? "添加客户备注" : "发布工作记录" }}：
        </div>
        <el-input
          type="textarea"
          :placeholder="
            addType == 1
              ? '请输入对此客户的描述信息'
              : '请输入此工单办理工作中的重要信息'
          "
          :rows="6"
          v-model="textarea"
        ></el-input>
        <span slot="footer" class="dialog-footer">
          <el-button size="mini" @click="dialogVisible = false"
            >取 消</el-button
          >
          <el-button size="mini" type="primary" @click="submit"
            >提 交</el-button
          >
        </span>
      </el-dialog>
    </el-drawer>

    <el-dialog
      :title="'编辑记录'"
      :visible.sync="openBianJiJiLuFlag"
      width="900px"
      append-to-body
    >
      {{ "共" + this.bianJiJiLuListData.count + "条" }}
      <el-table v-loading="loading" :data="bianJiJiLuListData.list">
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="266px"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="提交人" align="center" prop="createBy" />

        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-query"
              @click="handleJiLuDetail(scope.row)"
              v-hasPermi="['custom:record:id']"
              >查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      :title="'查看编辑记录详情'"
      :visible.sync="opneChaKanJiLuDetail"
      width="900px"
      append-to-body
    >
      {{ "修改时间 : " + this.chaKanJiLuDetailData.data.createTime + " " }}
      {{ " 提交人 : " + this.chaKanJiLuDetailData.data.createBy }}
      <el-table v-loading="loading" :data="chaKanJiLuDetailData.list">
        <el-table-column label="" align="center" prop="title" />
        <el-table-column label="修改前" align="center" prop="before">
          <template slot-scope="scope">
            <div
              :style="{ color: scope.row.status === 1 ? '#cccccc' : '#363636' }"
            >
              {{ scope.row.before }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="修改后" align="center" prop="after">
          <template slot-scope="scope">
            <div
              :style="{ color: scope.row.status === 1 ? '#cccccc' : '#363636' }"
            >
              {{ scope.row.after }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 添加或修改客服-公司管理对话框 -->
    <el-dialog
      :title="'修改客户信息'"
      :visible.sync="changeXiuGaiCustomerFlag"
      width="500px"
      append-to-body
    >
      <div v-html="'对当前工单的客户信息做修改   '"></div>
      <br />
      <br />
      <el-form
        ref="changeXiuGaiCustomerParam"
        :model="changeXiuGaiCustomerParam"
        label-width="120px"
      >
        <el-form-item label="姓名" prop="userName">
          <el-input
            v-model="changeXiuGaiCustomerParam.userName"
            placeholder="请输入姓名"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="userPhone">
          <el-input
            v-model="changeXiuGaiCustomerParam.userPhone"
            placeholder="请输入手机号"
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item label="身份证号码" prop="userIdCardNum">
          <el-input
            v-model="changeXiuGaiCustomerParam.userIdCardNum"
            placeholder="请输入身份证号码"
            style="width: 300px"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryRemark,
  insertWorkRemark,
  deleteWorkRemark,
  updateWork,
  getById,
  queryInfo,
  recordId,
  updateCount,
  updateInfo,
  getFileListInfo,
  downloadFileZip,
} from "@/api/service/index";
import { channelDict } from "@/api/kf/channel";

export default {
  dicts: [
    "kf_company_list",
    "kf_channel_type",
    "kf_work_order_status",
    "kf_source_type",
  ],
  props: {
    id: Number,
    disabled: Boolean,
    isShow: Boolean,
  },
  data() {
    return {
      fileList: [],
      // 打开编辑记录标志
      openBianJiJiLuFlag: false,
      // 编辑记录数据集合
      bianJiJiLuListData: {},

      // 查看编辑记录详情
      opneChaKanJiLuDetail: false,
      // 查看编辑记录详情 集合
      chaKanJiLuDetailData: {
        data: {},
        list: [
          {
            title: "手机号",
            before: "",
            after: "",
            status: 0,
          },
          {
            title: "姓名",
            before: "",
            after: "",
            status: 0,
          },
          {
            title: "身份证号码",
            before: "",
            after: "",
            status: 0,
          },
        ],
      },

      // 修改客户信息新增参数
      changeXiuGaiCustomerParam: {
        id: "",
        workOrderNum: "",
        userName: "",
        userPhone: "",
        userIdCardNum: "",
      },
      // 修改客户信息新增标志
      changeXiuGaiCustomerFlag: false,
      data: null,
      dialogVisible: false,
      activeName: "first",
      drawer: true,
      tableData: [],
      recordList: [],
      remarkList: [],
      textarea: "",
      addType: null,
      loginUserName: "",
      queryData: null,
      channelList: [],
      // 遮罩层
      loading: true,
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        userPhone: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
        ],
        userIdCardNum: [
          { required: true, message: "身份证号码不能为空", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {
    this.getDetail();
  },
  methods: {
    allDown() {
      downloadFileZip({
        workNum: this.data.workOrderNum,

        downloadAllFlag: true,
      }).then((res) => {
        let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
        const link = document.createElement("a"); //创建一个隐藏的a标签
        link.target = "_blank";
        link.href = href; //设置下载的url
        link.download = "客服附件_" + new Date().getTime() + ".zip"; //设置下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(href); // 释放掉blob对象
      });
    },
    downLoad(v) {
      downloadFileZip({ workNum: this.data.workOrderNum, fileId: v.id }).then(
        (res) => {
          let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
          const link = document.createElement("a"); //创建一个隐藏的a标签
          link.target = "_blank";
          link.href = href; //设置下载的url
          link.download = "客服附件_" + new Date().getTime() + ".zip"; //设置下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(href); // 释放掉blob对象
        }
      );
    },
    // 修改客户信息
    changeXiuGaiCustomer() {
      this.changeXiuGaiCustomerFlag = true;
      this.changeXiuGaiCustomerParam.userName = this.queryData.userName
      this.changeXiuGaiCustomerParam.userPhone = this.queryData.userPhone
      this.changeXiuGaiCustomerParam.userIdCardNum = this.queryData.userIdCardNum
    },
    // 点击编辑记录
    changeBianJiJiLu() {
      this.openBianJiJiLuFlag = true;
      const queryData = { orderNum: this.data.workOrderNum };
      updateCount(queryData).then((resp) => {
        this.bianJiJiLuListData = resp.data;
      });
      this.loading = false;
    },
    // 查看记录详情数据
    async handleJiLuDetail(row) {
      this.initDetail();
      this.opneChaKanJiLuDetail = true;
      this.chaKanJiLuDetailData.data = row;
      const queryData = { id: row.id, orderNum: this.data.workOrderNum };
      await recordId(queryData).then((resp) => {
        if (resp.data !== undefined || resp.data) {
          if (resp.data.length === 1) {
            const dataOne = resp.data[0];
            this.chaKanJiLuDetailData.list.forEach((item) => {
              if (item.title === "手机号") {
                item.after = dataOne.userPhone;
                item.before = "-";
              }
              if (item.title === "姓名") {
                item.after = dataOne.userName;
                item.before = "-";
              }
              if (item.title === "身份证号码") {
                item.after = dataOne.userIdCardNum;
                item.before = "-";
              }
            });
          } else if (resp.data.length === 2) {
            const dataOne = resp.data[0];
            const dataTwo = resp.data[1];
            this.chaKanJiLuDetailData.list.forEach((item) => {
              if (item.title === "手机号") {
                item.before = dataTwo.userPhone;
                item.after = dataOne.userPhone;
                if (item.before && item.after && item.after === item.before) {
                  item.status = 1;
                }
              }
              if (item.title === "姓名") {
                item.before = dataTwo.userName;
                item.after = dataOne.userName;
                if (item.before && item.after && item.after === item.before) {
                  item.status = 1;
                }
              }
              if (item.title === "身份证号码") {
                item.before = dataTwo.userIdCardNum;
                item.after = dataOne.userIdCardNum;
                if (item.before && item.after && item.after === item.before) {
                  item.status = 1;
                }
              }
            });
          }
        }
      });
    },

    // 初始化详情数据
    initDetail() {
      this.chaKanJiLuDetailData.list = [
        {
          title: "手机号",
          before: "",
          after: "",
          status: 0,
        },
        {
          title: "姓名",
          before: "",
          after: "",
          status: 0,
        },
        {
          title: "身份证号码",
          before: "",
          after: "",
          status: 0,
        },
      ];
    },

    // 提交表单
    submitForm() {
      this.changeXiuGaiCustomerParam.id = this.data.id;
      this.changeXiuGaiCustomerParam.workOrderNum = this.data.workOrderNum;
      if (this.data.orderSourceNum === "KF_SOURCE_KF") {
        if (
          !this.changeXiuGaiCustomerParam.userName &&
          !this.changeXiuGaiCustomerParam.userPhone
        ) {
          this.$message.warning("请输入手机号或姓名");
          return;
        }
        if (
          this.changeXiuGaiCustomerParam.userPhone &&
          !this.validatePhoneNumber(this.changeXiuGaiCustomerParam.userPhone)
        ) {
          this.$message.warning("手机号不合法");
          return;
        }
        if (
          this.changeXiuGaiCustomerParam.userIdCardNum &&
          !this.validateIDCard(this.changeXiuGaiCustomerParam.userIdCardNum)
        ) {
          this.$message.warning("身份证不合法");
          return;
        }
      } else if (this.data.orderSourceNum === "KF_SOURCE_KH") {
        if (!this.changeXiuGaiCustomerParam.userName) {
          this.$message.warning("请输入姓名");
          return;
        }
        if (!this.changeXiuGaiCustomerParam.userPhone) {
          this.$message.warning("请输入手机号");
          return;
        }
        if (
          this.changeXiuGaiCustomerParam.userPhone &&
          !this.validatePhoneNumber(this.changeXiuGaiCustomerParam.userPhone)
        ) {
          this.$message.warning("手机号不合法");
          return;
        }
        if (
          this.changeXiuGaiCustomerParam.userIdCardNum &&
          !this.validateIDCard(this.changeXiuGaiCustomerParam.userIdCardNum)
        ) {
          this.$message.warning("身份证不合法");
          return;
        }
      } else {
        this.$message.warning("当前工单来源未知。");
      }

      updateInfo(this.changeXiuGaiCustomerParam)
        .then((resp) => {
          this.$message.success("修改成功");
          this.changeXiuGaiCustomerFlag = false;
          this.changeBianJiJiLu();
          this.cancel();
          if (this.loginUserName == this.data.claimUserId && !this.isShow) {
            queryInfo({ id: this.id, workOrderNum: this.data.workOrderNum }).then(
              (res) => {
                this.queryData = res.data;
              }
            );
            return;
          }
          if (this.isShow) {
            queryInfo({
              id: this.id,
              workOrderNum: this.data.workOrderNum,
              isShow: this.isShow,
            }).then((res) => {
              this.queryData = res.data;
            });
          }
        })
        .catch();
    },
    // 取消按钮
    cancel() {
      this.changeXiuGaiCustomerFlag = false;
      // 修改客户信息新增参数
      this.changeXiuGaiCustomerParam = {
        id: "",
        workOrderNum: "",
        userName: "",
        userPhone: "",
        userIdCardNum: "",
      };
    },

    getDict(companyNum) {
      channelDict(companyNum).then((res) => {
        this.channelList = res.data;
      });
    },
    // 验证手机号码合法性
    validatePhoneNumber(phone) {
      const phonePattern = /^\d{11}$/; // 正则表达式，匹配11位数字
      return phonePattern.test(phone);
    },
    // 验证身份证号码合法性
    validateIDCard(idCard) {
      const idCardPattern =
        /^\d{6}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/; // 正则表达式，匹配18位身份证号

      // 简单的长度和格式验证
      if (!idCardPattern.test(idCard)) {
        return false;
      }

      // 校验码验证（可选，但推荐实现以进行更严格的验证）
      const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const checkCodes = [
        "1",
        "0",
        "X",
        "9",
        "8",
        "7",
        "6",
        "5",
        "4",
        "3",
        "2",
      ];
      let sum = 0;
      for (let i = 0; i < 17; i++) {
        sum += parseInt(idCard.charAt(i), 10) * weights[i];
      }
      const mod = sum % 11;
      const checkCode = checkCodes[mod];

      // 比较计算出的校验码与提供的校验码（不区分大小写）
      return checkCode.toUpperCase() === idCard.charAt(17).toUpperCase();
    },

    update(v) {
      this.$confirm(
        v == 6
          ? "是否已完成此工单的工作？请确认已与客户取得联系，客户问题已得到答复"
          : v == 3
          ? "是否放弃认领此工单？工单将变为 [待认领] 状态，等待其他客服人员认领"
          : "是否确定不处理此工单？工单将移至 [不处理] 列表",
        v == 6 ? "已完成" : v == 3 ? "放弃认领" : "不处理",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          let params = {
            id: this.data.id,
            workOrderNum: this.data.workOrderNum,
            workOptionType: v,
          };
          updateWork({ ...params }).then((res) => {
            this.$message.success(
              v == 6
                ? "工单已完成！"
                : v == 3
                ? "已放弃认领工单"
                : "工单已移至不处理"
            );
            this.getDetail();
          });
        })
        .catch(() => {});
    },
    getDetail() {
      getById({ orderId: this.id + "" }).then((res) => {
        this.data = res.data;
        this.queryRemark();
        let timer = this.calculateDifference(
          this.$format(new Date().getTime(), "yyyy-MM-dd HH:mm:ss"),
          this.data.createTime
        );

        this.tableData = [];
        this.tableData.push(
          {
            name: "工单编号",
            value: this.data.workOrderNum,
          },
          {
            name: "提交时间",
            value: this.data.createTime,
          },
          {
            name: "已等待时长",
            value: `${timer.diffInHours}h${timer.remainingMinutes}m`,
          },
          {
            name: "公司",
            value: this.data.companyNum,
          },
          {
            name: "工单来源",
            value: this.data.orderSourceNum,
          },
          {
            name: "渠道",
            value: this.data.channelNum,
          },
          {
            name: "工单内容",
            value: this.data.workOrderText,
          },
          {
            name: "工单状态",
            value: this.data.workOrderStatus,
          },
          {
            name: "客服",
            value: this.data.claimUserName,
          },
          {
            name: "附件",
            value: "",
          }
        );
        const queryData = { companyCode: res.data.companyNum };
        this.getDict(queryData);
        getFileListInfo({ workNum: this.data.workOrderNum }).then((res) => {
          this.fileList = res.data || [];
        });
      });
    },
    rlwork() {
      this.$confirm(
        "是否确认认领此工单？认领后您将负责该工单的处理，直至完成",
        "认领工单",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          let params = {
            id: this.data.id,
            workOrderNum: this.data.workOrderNum,
            workOptionType: 2,
          };
          updateWork({ ...params }).then((res) => {
            this.$message.success("认领成功");
            this.getDetail();
          });
        })
        .catch(() => {});
    },
    deletedynamic(v) {
      this.$confirm("是否确定删除此条记录？", "删除工作记录", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            id: v.id,
            workOrderNum: this.data.workOrderNum,
            orderType: "RECORD",
            accountName: this.loginUserName,
          };
          deleteWorkRemark({ ...params }).then((res) => {
            this.$message.success("删除成功!");
            this.queryRemark();
          });
        })
        .catch(() => {});
    },
    delRemark(v) {
      this.$confirm("是否确定删除此条备注信息？", "删除备注", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            id: v.id,
            workOrderNum: this.data.workOrderNum,
            orderType: "REMARK",
            accountName: this.loginUserName,
          };
          deleteWorkRemark({ ...params }).then((res) => {
            this.$message.success("删除备注成功!");
            this.queryRemark();
          });
        })
        .catch(() => {});
    },
    handleClose2() {
      this.dialogVisible = false;
      this.textarea = "";
    },

    addRemark() {
      this.addType = 1;
      this.dialogVisible = true;
    },
    addRecord() {
      this.addType = 2;
      this.dialogVisible = true;
    },
    submit() {
      if (!this.textarea) {
        this.$message.warning("请填写内容");
        return;
      }
      let params = {
        id: this.data.id,
        workOrderNum: this.data.workOrderNum,
        workOptionType: this.addType == 1 ? 7 : 4,
        infoText: this.textarea,
      };
      insertWorkRemark({ ...params }).then((res) => {
        this.$message.success(
          this.addType == 1 ? "添加客户备注成功!" : "发布工作记录成功!"
        );
        this.dialogVisible = false;
        this.textarea = "";
        this.queryRemark();
      });
    },
    queryRemark() {
      let params = {
        workOrderNum: this.data.workOrderNum,
      };
      queryRemark({ ...params, orderType: "RECORD" }).then((res) => {
        this.loginUserName = res.data.loginUserName;
        this.recordList = res.data.workList;
        this.remarkList = res.data.remarkList;
        if (this.loginUserName == this.data.claimUserId && !this.isShow) {
          queryInfo({ id: this.id, workOrderNum: this.data.workOrderNum }).then(
            (res) => {
              this.queryData = res.data;
            }
          );
          return;
        }
        if (this.isShow) {
          queryInfo({
            id: this.id,
            workOrderNum: this.data.workOrderNum,
            isShow: this.isShow,
          }).then((res) => {
            this.queryData = res.data;
          });
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
    parseTime(timeString) {
      const [dateStr, timeStr] = timeString.split(" ");
      const [year, month, day] = dateStr.split("-").map(Number);
      const [hours, minutes, seconds] = timeStr.split(":").map(Number);
      return new Date(Date.UTC(year, month - 1, day, hours, minutes, seconds));
    },
    calculateDifference(a, b) {
      const date1 = this.parseTime(a);
      const date2 = this.parseTime(b);

      const diffInMilliseconds = Math.abs(date2 - date1); // 使用绝对值确保结果为正数
      const diffInSeconds = Math.floor(diffInMilliseconds / 1000);
      const diffInMinutes = Math.floor(diffInSeconds / 60);
      const diffInHours = Math.floor(diffInMinutes / 60);
      const remainingMinutes = diffInMinutes % 60;

      return { diffInHours, remainingMinutes };
    },
  },
};
</script>

<style lang="less" scoped>
.drawer {
  display: flex;
  height: 100%;
  padding: 12px;

  .left {
    width: 55%;
    flex-shrink: 0;
    border-right: 1px solid #cccccc;
  }

  .right {
    font-size: 14px;
    flex: 1;
    padding: 16px;
  }
}

/deep/ .el-timeline-item__tail {
  border-left: 2px solid #1890ff;
}

/deep/ .el-timeline-item__node {
  background-color: #fff;
  border: 2px solid #1890ff;
}

/deep/ .el-step__head.is-finish {
  color: #9ddc88;
  border-color: #9ddc88;
}

// /deep/ .el-step__head.is-process {
//   color: #9ddc88;
//   border-color: #9ddc88;
// }
/deep/ .el-step__title.is-finish {
  color: #8e598e;
}
</style>
