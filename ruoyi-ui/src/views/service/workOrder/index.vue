<template>
  <div style="color: #363636 !important">
    <div ref="myDiv" style="padding: 12px; padding-bottom: 0">
      <div class="top">
        <img src="@/assets/images/profile.jpg" alt="" />
        <span>{{ nickName }}</span>
      </div>
      <el-form
        :model="params"
        ref="queryForm"
        :inline="true"
        label-width="100px"
      >
        <el-form-item label="客户姓名" prop="userName">
          <el-input
            v-model="params.userName"
            size="small"
            clearable=""
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="userPhone">
          <el-input
            v-model="params.userPhone"
            size="small"
            clearable=""
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="身份证号码" prop="userIdCardNum">
          <el-input
            v-model="params.userIdCardNum"
            size="small"
            clearable=""
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="workOrderText">
          <el-input
            v-model="params.workOrderText"
            size="small"
            clearable=""
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="提交时间" prop="time">
          <el-date-picker
            v-model="params.time"
            size="small"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="渠道" prop="channelNumList">
          <el-select
            style="width: 200px"
            size="small"
            placeholder="全部"
            filterable=""
            clearable=""
            multiple=""
            collapse-tags=""
            v-model="params.channelNumList"
          >
            <el-option
              v-for="dict in channelList"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="activeName == 'COMPLETE' || activeName == 'ABANDON'"
          label="完成时间"
          prop="time2"
        >
          <el-date-picker
            v-model="params.time2"
            size="small"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            v-hasPermi="['kf:work:list']"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <el-divider style="margin-top: 0"></el-divider>
    <div
      class="content"
      v-if="divHeight"
      :style="{ height: windowHeight - divHeight - 85 + 'px' }"
    >
      <div class="left">
        <div class="title"><span>公司</span><span>未完成</span></div>
        <div
          class="left_item"
          @click="itemClick(item)"
          :class="active == item.key ? 'active' : ''"
          v-for="item in companyList"
          :key="item.key"
        >
          <dict-tag :options="dict.type.kf_company_list" :value="item.key" />
          <span class="value" v-show="item.value">{{ item.value }}</span>
        </div>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" v-if="companyList.length>0" @click="addOrderType = true"
          >+添加工单</el-button
        >
        <el-tabs
          style="margin-top: 16px"
          v-model="activeName"
          type="card"
          @tab-click="handleClick"
        >
          <el-tab-pane label="待我处理" name="DWHANDLER">
            <template slot="label">
              <el-badge
                v-if="DWHANDLERTotal > 0"
                :value="DWHANDLERTotal"
                type="warning"
                :max="99"
              >
                <span>待我处理</span>
              </el-badge>
            </template>
          </el-tab-pane>
          <el-tab-pane label="待认领" name="READY">
            <template slot="label">
              <el-badge
                v-if="READYTotal > 0"
                :value="READYTotal"
                type="warning"
                :max="99"
              >
                <span>待认领</span>
              </el-badge>
            </template>
          </el-tab-pane>
          <el-tab-pane label="未完成" name="NOTFINISH">
            <template slot="label">
              <el-badge
                v-if="NOTFINISHTotal > 0"
                :value="NOTFINISHTotal"
                type="warning"
                :max="99"
              >
                <span>未完成</span>
              </el-badge>
            </template>
          </el-tab-pane>
          <el-tab-pane label="已完成" name="COMPLETE"></el-tab-pane>
          <el-tab-pane label="不处理" name="ABANDON"></el-tab-pane>
        </el-tabs>
        <el-table ref="table" :data="tableData" style="width: 100%">
          <el-table-column prop="createTime" label="提交时间" width="220" />
          <el-table-column prop="channelNum" width="140" label="渠道">
            <template slot-scope="scope">
              <div v-for="item in channelList" :key="item.id">
                <span v-if="item.value === scope.row.channelNum">
                  {{item.label }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="userName" label="客户姓名" width="120" />
          <el-table-column prop="userPhone" label="客户手机号码" width="140" />
          <el-table-column
            prop="userIdCardNum"
            label="客户身份证号码"
            width="200"
          />
          <el-table-column prop="workOrderText" label="工单内容">
            <template slot-scope="scope">
              <span v-if="scope.row.workOrderText.length <= 20">{{
                scope.row.workOrderText
              }}</span>
              <span v-else
                >{{ truncateString(scope.row.workOrderText) }}
                <el-button type="text" @click="getMore(scope.row.workOrderText)"
                  >更多</el-button
                ></span
              >
            </template>
          </el-table-column>
          <el-table-column prop="claimUserName" width="200" label="客服">
            <template slot-scope="scope">
              <span
                v-if="scope.row.claimUserName"
                style="
                  display: inline-block;
                  border: 1px solid #cccccc;
                  border-radius: 4px;
                  padding: 2px 8px;
                "
                >{{ scope.row.claimUserName }}</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="date" label="状态" width="120">
            <template slot-scope="scope">
              <div style="display: flex; align-items: center">
                <i
                  class="status"
                  :style="{
                    background:
                      scope.row.workOrderStatus == 'READY'
                        ? '#ffc23e'
                        : scope.row.workOrderStatus == 'LOAD'
                        ? '#95f204'
                        : scope.row.workOrderStatus == 'COMPLETE'
                        ? '#f2f2f2'
                        : '#cccccc',
                  }"
                ></i>
                <dict-tag
                  :options="dict.type.kf_work_order_status"
                  :value="scope.row.workOrderStatus"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="activeName == 'COMPLETE' || activeName == 'ABANDON'"
            prop="lastUpdateTime"
            label="完成时间"
            width="220"
          />
          <el-table-column prop="date" label="操作" width="140">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                @click="open(scope.row)"
                v-if="
                  (scope.row.workOrderStatus == 'LOAD' &&
                    scope.row.claimUserId == loginUserName) ||
                  scope.row.workOrderStatus == 'DWHANDLER'
                "
                >处理</el-button
              >
              <el-button
                @click="open(scope.row)"
                v-else-if="scope.row.workOrderStatus == 'READY'"
                size="mini"
                style="background: #ff9900; color: #fff; border: none"
                >认领</el-button
              >
              <el-button type="text" @click="open(scope.row)" v-else
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="params.pageNum"
          :limit.sync="params.pageSize"
          :page-sizes="[20, 50, 100]"
          @pagination="getList"
        />
      </div>
    </div>
    <AddOrder v-if="addOrderType" :company="active" @close="close" />
    <Detail :id="workId" v-if="detailType" @close="close" />
    <wordsDialog
      :words="words"
      v-if="wordsDialogType"
      @close="wordsDialogType = false"
    />
  </div>
</template>

<script>
import Cookie from "js-cookie";
import { kfompanyList, workList } from "@/api/service/index";
import AddOrder from "./components/AddOrder.vue";
import Detail from "./components/Detail.vue";
import wordsDialog from "../components/wordsDialog.vue";
import { getDicts } from "@/api/system/dict/data";
import {channelDict} from "@/api/kf/channel";
export default {
  components: {
    AddOrder,
    Detail,
    wordsDialog,
  },
  dicts: ["kf_company_list", "kf_channel_type", "kf_work_order_status"],
  data() {
    return {
      wordsDialogType: false,
      total: 0,
      nickName: "",
      detailType: false,
      loginUserName: "",
      tableData: [],
      activeName: "DWHANDLER",
      addOrderType: false,
      active: "",
      companyList: [],
      windowHeight: "",
      divHeight: "",
      params: {
        userName: "",
        pageNum: 1,
        pageSize: 20,
        userPhone: "",
        userIdCardNum: "",
        workOrderStatus: "",
        companyNum: "",
        workOrderText: "",
        startTime: "",
        endTime: "",
        wcStartTime: "",
        wcEndTime: "",
        channelNumList: [],
        time: [],
        time2: [],
      },
      workId: null,
      words: "",
      DWHANDLERTotal: 0,
      NOTFINISHTotal: 0,
      READYTotal: 0,
      channelList: [],
    };
  },
  mounted() {
    this.nickName = Cookie.get("nickName");
    this.kfompanyList();
    this.updateWindowHeight();
    window.addEventListener("resize", this.updateWindowHeight);
    this.getDict();
  },
  methods: {
    getDict() {
      const dataCompany = {companyCode: this.params.companyNum}
      channelDict(dataCompany).then(resp=>{
          this.channelList = resp.data
        }
      )
      // getDicts("kf_channel_type").then((res) => {
      //   this.channelList = res.data;
      // });
    },
    close() {
      this.detailType = false;
      this.addOrderType = false;
      kfompanyList().then((res) => {
        let arr1 = Object.keys(res.data);
        let arr2 = Object.values(res.data);
        this.companyList = [];
        arr1.forEach((item, index) => {
          arr2.forEach((v, i) => {
            if (index == i) {
              this.companyList.push({
                key: item,
                value: v,
              });
            }
          });
        });
        this.getItemNum();
        this.getList();
      });
    },
    getMore(v) {
      this.words = v;
      this.wordsDialogType = true;
    },
    truncateString(str, maxLength = 20) {
      if (str.length > maxLength) {
        return str.substring(0, maxLength) + "...";
      } else {
        return str;
      }
    },
    open(v) {
      this.workId = v.id;
      this.detailType = true;
    },
    handleClick() {
      if(this.companyList.length>0){
        this.getList();
      }
      
    },
    itemClick(v) {
      this.active = v.key;
      this.params.companyNum = v.key;
      this.getList();
      this.getItemNum();
      this.getDict()
      this.params.channelNumList = []
    },
    async kfompanyList() {
      kfompanyList().then((res) => {
        let arr1 = Object.keys(res.data);
        let arr2 = Object.values(res.data);
        this.companyList = [];
        arr1.forEach((item, index) => {
          arr2.forEach((v, i) => {
            if (index == i) {
              this.companyList.push({
                key: item,
                value: v,
              });
            }
          });
        });
        this.active = this.companyList[0].key;
        this.params.companyNum = this.companyList[0].key;
        this.getItemNum();
        this.getList();
      });
    },
    updateWindowHeight() {
      // 更新窗口高度
      this.windowHeight = window.innerHeight;
      if (this.$refs && this.$refs.myDiv) {
        this.divHeight = this.$refs.myDiv.offsetHeight;
      }
      if (this.$refs.table){this.$refs.table.doLayout();}
    },
    handleQuery() {
      if(this.companyList.length>0){
        this.getList();
      }
     
    },
    getItemNum() {
      workList({
        companyNum: this.params.companyNum,
        workOrderStatus: "DWHANDLER",
      }).then((res) => {
        this.DWHANDLERTotal = res.data.total;
      });
      workList({
        companyNum: this.params.companyNum,
        workOrderStatus: "NOTFINISH",
      }).then((res) => {
        this.NOTFINISHTotal = res.data.total;
      });
      workList({
        companyNum: this.params.companyNum,
        workOrderStatus: "READY",
      }).then((res) => {
        this.READYTotal = res.data.total;
      });
    },
    getList() {
      this.detailType = false;
      this.addOrderType = false;
      if (this.activeName != "COMPLETE" && this.activeName != "ABANDON") {
        this.params.time2 = [];
        this.params.wcStartTime = "";
        this.params.wcEndTime = "";
      }
      if (this.params.time && this.params.time.length > 0) {
        this.params.startTime = this.$format(
          this.params.time[0],
          "yyyy-MM-dd HH:mm:ss"
        );
        this.params.endTime = this.$format(
          this.params.time[1],
          "yyyy-MM-dd 23:59:59"
        );
      } else {
        this.params.startTime = "";
        this.params.endTime = "";
      }
      if (this.params.time2 && this.params.time2.length > 0) {
        this.params.wcStartTime = this.$format(
          this.params.time2[0],
          "yyyy-MM-dd HH:mm:ss"
        );
        this.params.wcEndTime = this.$format(
          this.params.time2[1],
          "yyyy-MM-dd 23:59:59"
        );
      } else {
        this.params.wcStartTime = "";
        this.params.wcEndTime = "";
      }
      this.params.workOrderStatus = this.activeName;
      workList({ ...this.params }).then((res) => {
        this.loginUserName = res.data.loginUserName;
        this.tableData = res.data.rows;
        this.total = res.data.total;
      });
      this.getDict();
    },
    resetQuery() {
      this.resetForm("queryForm");
      if(this.companyList.length>0){
        this.handleQuery();
      }
   
    },
  },
};
</script>

<style lang="less" scoped>
.top {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 12px;
  }
  span {
    font-weight: bold;
    font-size: 20px;
  }
}
.el-divider--horizontal {
  margin: 0 !important;
}
.content {
  display: flex;
  height: 100vh;
  .left {
    width: 240px;
    flex-shrink: 0;
    border-right: 1px solid #d7d7d7;

    .title {
      display: flex;
      justify-content: space-between;
      background: #f8f8f9;
      padding: 6px 16px;
      font-weight: bold;
    }
    .left_item {
      cursor: pointer;
      color: #1890ff;
      padding: 8px 16px;
      border-bottom: 0.5px solid #f2f2f2;
      display: flex;
      justify-content: space-between;
      .value {
        display: inline-block;
        background: #ff9900;
        color: #fff;
        padding: 2px 10px;
        border-radius: 10px;
        font-weight: normal !important;
      }
    }
  }
  .right {
    flex: 1;
    padding: 12px;
    overflow: auto;
  }
}
.active {
  background: #e3f1ff;
  font-weight: 600;
}
.status {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: red;
  margin-right: 5px;
}
/deep/ .el-tabs__item {
  height: 52px !important;
  padding: 8px 20px !important;
}
</style>
