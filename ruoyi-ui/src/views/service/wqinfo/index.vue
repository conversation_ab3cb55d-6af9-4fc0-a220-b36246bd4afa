<template>
  <div style="color: #363636 !important">
    <div ref="myDiv" style="padding: 12px; padding-bottom: 0">
      <div class="top">
        <img src="@/assets/images/profile.jpg" alt="" />
        <span>张三</span>
      </div>
      <el-form
        :model="params"
        ref="queryForm"
        :inline="true"
        label-width="100px"
      >
        <el-form-item label="手机号码" prop="userPhone">
          <el-input
            v-model="params.userPhone"
            size="small"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="身份证号码" prop="userIdCardNum">
          <el-input
            v-model="params.userIdCardNum"
            size="small"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="workOrderText">
          <el-input
            v-model="params.workOrderText"
            size="small"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="提交时间" prop="time">
          <el-date-picker
            v-model="params.time"
            size="small"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="渠道" prop="channelNum">
          <el-select
            style="width: 200px"
            size="small"
            placeholder="全部"
            v-model="params.channelNum"
          >
            <el-option
              v-for="dict in dict.type.kf_channel_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="workOrderStatus">
          <el-select
            style="width: 200px"
            size="small"
            placeholder="全部"
            v-model="params.workOrderStatus"
          >
            <el-option
              v-for="dict in dict.type.kf_work_order_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            v-hasPermi="['kf:work:list']"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <el-divider style="margin-top: 0"></el-divider>
    <div
      class="content"
      v-if="divHeight"
      :style="{ height: windowHeight - divHeight - 85 + 'px' }"
    >
      <div class="left">
        <div class="title"><span>公司</span></div>
        <div
          class="left_item"
          @click="itemClick(item)"
          :class="active == item.key ? 'active' : ''"
          v-for="item in companyList"
          :key="item.key"
        >
          <dict-tag :options="dict.type.kf_company_list" :value="item.key" />
        </div>
      </div>
      <div class="right">
        <el-table ref="table" :data="tableData" style="width: 100%">
          <el-table-column prop="createTime" label="提交时间" width="220" />
          <el-table-column prop="channelNum" width="140" label="渠道">
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.kf_channel_type"
                :value="scope.row.channelNum"
              />
            </template>
          </el-table-column>
          <el-table-column prop="userPhone" label="客户手机号码" width="140" />
          <el-table-column
            prop="userIdCardNum"
            label="客户身份证号码"
            width="200"
          />
          <el-table-column
            prop="workOrderText"
            label="工单内容"
            show-overflow-tooltip=""
          />
          <el-table-column prop="claimUserName" width="200" label="客服">
            <template slot-scope="scope">
              <span
                v-if="scope.row.claimUserName"
                style="
                  display: inline-block;
                  border: 1px solid #cccccc;
                  border-radius: 4px;
                  padding: 2px 8px;
                "
                >{{ scope.row.claimUserName }}</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="date" label="状态" width="200">
            <template slot-scope="scope">
              <div style="display: flex; align-items: center">
                <i
                  class="status"
                  :style="{
                    background:
                      scope.row.workOrderStatus == 'READY'
                        ? '#ffc23e'
                        : scope.row.workOrderStatus == 'LOAD'
                        ? '#95f204'
                        : scope.row.workOrderStatus == 'COMPLETE'
                        ? '#f2f2f2'
                        : '#cccccc',
                  }"
                ></i>
                <dict-tag
                  :options="dict.type.kf_work_order_status"
                  :value="scope.row.workOrderStatus"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="lastUpdateTime" label="完成时间" width="220" />
          <el-table-column prop="date" label="操作" width="200">
            <template slot-scope="scope">
              <el-button @click="open(scope.row)" type="text">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <AddOrder v-if="addOrderType" :company="active" @close="getList" />
    <Detail :disabled="true" :id="workId" v-if="detailType" @close="getList" />
  </div>
</template>
  
  <script>
import { kfompanyList, workList } from "@/api/service/index";
import AddOrder from "../workOrder/components/AddOrder.vue";
import Detail from "../workOrder/components/Detail.vue";
export default {
  components: {
    AddOrder,
    Detail,
  },
  dicts: ["kf_company_list", "kf_channel_type", "kf_work_order_status"],
  data() {
    return {
      detailType: false,
      loginUserName: "",
      tableData: [],

      addOrderType: false,
      active: "",
      companyList: [],
      windowHeight: "",
      divHeight: "",
      params: {
        userPhone: "",
        userIdCardNum: "",
        workOrderStatus: "",
        companyNum: "",
        workOrderText: "",
        startTime: "",
        endTime: "",
        channelNum: "",
        time: [],
      },
      workId: null,
    };
  },
  mounted() {
    this.kfompanyList();
    this.updateWindowHeight();
    window.addEventListener("resize", this.updateWindowHeight);
  },
  methods: {
    open(v) {
      this.workId = v.id;
      this.detailType = true;
    },
    handleClick() {
      this.getList();
    },
    itemClick(v) {
      this.active = v.key;
      this.params.companyNum = v.key;
      this.getList();
    },
    kfompanyList() {
      kfompanyList().then((res) => {
        let arr1 = Object.keys(res.data);
        let arr2 = Object.values(res.data);
        console.log(arr1, arr2);
        this.companyList = [];
        arr1.forEach((item, index) => {
          arr2.forEach((v, i) => {
            if (index == i) {
              this.companyList.push({
                key: item,
                value: v,
              });
            }
          });
        });
        console.log(this.companyList);
        this.active = this.companyList[0].key;
        this.params.companyNum = this.companyList[0].key;
        this.getList();
      });
    },
    updateWindowHeight() {
      // 更新窗口高度
      this.windowHeight = window.innerHeight;
      if (this.$refs && this.$refs.myDiv) {
        this.divHeight = this.$refs.myDiv.offsetHeight;
      }
      this.$refs.table.doLayout();
    },
    handleQuery() {
      this.getList();
    },
    getList() {
      this.detailType = false;
      this.addOrderType = false;
      if (this.params.time && this.params.time.length > 0) {
        this.params.startTime = this.$format(
          this.params.time[0],
          "yyyy-MM-dd HH:mm:ss"
        );
        this.params.endTime = this.$format(
          this.params.time[1],
          "yyyy-MM-dd 23:59:59"
        );
      } else {
        this.params.startTime = "";
        this.params.endTime = "";
      }

      workList({ ...this.params }).then((res) => {
        this.loginUserName = res.data.loginUserName;
        this.tableData = res.data.list;
      });
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
  
  <style lang="less" scoped>
.top {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 12px;
  }
  span {
    font-weight: bold;
    font-size: 20px;
  }
}
.el-divider--horizontal {
  margin: 0 !important;
}
.content {
  display: flex;
  height: 100vh;
  .left {
    width: 240px;
    flex-shrink: 0;
    border-right: 1px solid #d7d7d7;

    .title {
      display: flex;
      justify-content: space-between;
      background: #f8f8f9;
      padding: 6px 16px;
      font-weight: bold;
    }
    .left_item {
      cursor: pointer;
      color: #1890ff;
      padding: 8px 16px;
      border-bottom: 0.5px solid #f2f2f2;
      display: flex;
      justify-content: space-between;
      .value {
        display: inline-block;
        background: #ff9900;
        color: #fff;
        padding: 2px 10px;
        border-radius: 10px;
        font-weight: normal !important;
      }
    }
  }
  .right {
    flex: 1;
    padding: 12px;
    overflow: auto;
  }
}
.active {
  background: #f5fafe;
  font-weight: 600;
}
.status {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: red;
  margin-right: 5px;
}
</style>