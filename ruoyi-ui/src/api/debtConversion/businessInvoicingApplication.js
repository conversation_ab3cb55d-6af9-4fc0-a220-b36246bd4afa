import request from '@/utils/request'
// 查询用户列表
export function businessList(params) {
    return request({
        url: '/invoicing/business/list',
        method: 'get',
        params 
    })
}

export function businessDetail(id) {
    return request({
        url: `/invoicing/business/${id}`,
        method: 'get',
    })
}
export function deleteBusiness(id) {
    return request({
        url: `/invoicing/business/${id}`,
        method: 'delete',
    })
}

export function importDataCheck(data) {
    return request({
      url: "/invoicing/business/importDataCheck",
      method: "post",
      data,
    });
  }
  export function importData(data) {
    return request({
      url: "/invoicing/business/importData",
      method: "post",
      data,
    });
  }
  