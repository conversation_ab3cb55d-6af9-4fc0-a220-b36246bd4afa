import request from "@/utils/request";

export function newAuthority(params) {
    return request({
        url: "/system/newAuthority",
        method: "get",
        params,
    });
}
export function newAuthTemp(data) {
    return request({
        url: "/system/template/saveAuthByTemp",
        method: "post",
        data,
    });
}
export function getUserListAll() {
    return request({
        url: '/system/user/getUserListAll',
        method: 'get'
    })
}
export function subordinate(params) {
    return request({
        url: '/personnel/archives/subordinate',
        method: 'get',
        params
    })
}
export function queryCancelUser(params) {
    return request({
        url: '/system/newAuthority/queryCancelUser',
        method: 'get',
        params
    })
}
export function queryCompanyList(params) {
    return request({
        url: '/system/newAuthority/queryCompanyList',
        method: 'get',
        params
    })
}
export function listForAuthority(params) {
    return request({
        url: '/system/company/listForAuthority',
        method: 'get',
        params
    })
}
export function treeSelectFroNewAuthority(params) {
    return request({
        url: '/oasystem/classification/treeSelectFroNewAuthority',
        method: 'get',
        params
    })
}
export function queryAllCancelUser(params) {
    return request({
        url: '/system/newAuthority/queryAllCancelUser',
        method: 'get',
        params
    })
}
export function userRoleAuthority(params) {
    return request({
        url: '/system/newAuthority/userRoleAuthority',
        method: 'get',
        params
    })
}
export function cancelAuthorization(data) {
    return request({
        url: '/system/newAuthority/cancelAuthorization',
        method: 'post',
        data
    })
}
export function cancelAllAuthorization(data) {
    return request({
        url: '/system/newAuthority/cancelAllAuthorization',
        method: 'post',
        data
    })
}
export function addAuthorization(data) {
    return request({
        url: '/system/newAuthority/addAuthorization',
        method: 'post',
        data
    })
}
export function readRelationUserRoleAuthority(params) {
  return request({
    url: '/tgReadRelation/readRelation/giveUserNoticeAuth',
    method: 'get',
    params
  })
}

//处理同步数据的问题
export function copyAuthByUserUserNameAndSourceAuthAndTargetAuth(data) {
  return request({
    url: '/system/newAuthority/copyAuthByUserUserNameAndSourceAuthAndTargetAuth',
    method: 'post',
    data
  })
}

