import request from '@/utils/request'

// 查询渠道列表
export function listChannel(query) {
  return request({
    url: '/kf/channel/list',
    method: 'get',
    params: query
  })
}

// 查询渠道列表所有
export function listAll(query) {
  return request({
    url: '/kf/channel/listAll',
    method: 'get',
    params: query
  })
}

// 查询渠道详细
export function getChannel(id) {
  return request({
    url: '/kf/channel/' + id,
    method: 'get'
  })
}

// 新增渠道
export function addChannel(data) {
  return request({
    url: '/kf/channel',
    method: 'post',
    data: data
  })
}

// 修改渠道
export function updateChannel(data) {
  return request({
    url: '/kf/channel',
    method: 'put',
    data: data
  })
}

// 删除渠道
export function delChannel(id) {
  return request({
    url: '/kf/channel/' + id,
    method: 'delete'
  })
}

// 渠道管理字典
export function channelDict(data) {
  return request({
    url: '/kf/channel/dict',
    method: 'post',
    data: data
  })
}
// 按照渠道查询树形结构
export function tree() {
  return request({
    url: '/kf/channel/tree',
    method: 'post'
  })
}
