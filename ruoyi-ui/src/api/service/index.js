import request from '@/utils/request'
export function kfompanyList(data) {
    return request({
        url: '/kf/incomplete/companyList',
        method: 'post',
        data
    })
}
export function insert(data) {
    return request({
        url: '/kf/word/order/insert',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        method: 'post',
        data
    })
}
export function workList(data) {
    return request({
        url: '/kf/work/list',
        method: 'post',
        data
    })
}
export function queryRemark(data) {
    return request({
        url: '/kf/workremark/query',
        method: 'post',
        data
    })
}
export function insertWorkRemark(data) {
    return request({
        url: '/kf/workremark/insert',
        method: 'post',
        data
    })
}
export function deleteWorkRemark(data) {
    return request({
        url: '/kf/workremark/delete',
        method: 'post',
        data
    })
}
export function updateWork(data) {
    return request({
        url: '/kf/workstatus/update',
        method: 'post',
        data
    })
}
export function getById(data) {
    return request({
        url: '/kf/work/getById',
        method: 'post',
        data
    })
}
export function queryInfo(data) {
    return request({
        url: '/kf/custominfo/query',
        method: 'post',
        data
    })
}
export function updateCount(data) {
    return request({
        url: '/custom/update/count',
        method: 'post',
        data
    })
}
export function recordId(data) {
    return request({
        url: '/custom/record/id',
        method: 'post',
        data
    })
}


export function updateInfo(data) {
    return request({
        url: '/kf/work/update',
        method: 'post',
        data
    })
}
export function downloadFileZip(data) {
    return request({
        url: '/kf/work/downloadFileZip',
        responseType: 'blob',
        method: 'post',
        data
    })
}
export function getFileListInfo(data) {
    return request({
        url: '/kf/work/getFileListInfo',
        method: 'post',
        data
    })
}

