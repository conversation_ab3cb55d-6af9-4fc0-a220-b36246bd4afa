package com.stylefeng.guns.modular.system.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.stylefeng.guns.core.datascope.DataScope;
import com.stylefeng.guns.modular.applicant.model.Applicant;
import com.stylefeng.guns.modular.system.dao.UserMapper;
import com.stylefeng.guns.modular.system.model.User;
import com.stylefeng.guns.modular.system.service.IUserService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 管理员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-02-22
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Override
    public int setStatus(Integer userId, int status) {
        return this.baseMapper.setStatus(userId, status);
    }

    @Override
    public int changePwd(Integer userId, String pwd) {
        return this.baseMapper.changePwd(userId, pwd);
    }

    @Override
    public List<Map<String, Object>> selectUsers(DataScope dataScope, String name, String beginTime, String endTime, Integer deptid) {
        return this.baseMapper.selectUsers(dataScope, name, beginTime, endTime, deptid);
    }

    @Override
    public int setRoles(Integer userId, String roleIds) {
        return this.baseMapper.setRoles(userId, roleIds);
    }

    @Override
    public User getByAccount(String account) {
        return this.baseMapper.getByAccount(account);
    }

	@Override
	public boolean isEVPI(Integer id) {
		boolean info = false;
		try{
			List<Applicant> userInfo = this.baseMapper.isEVPI(id);
			if(userInfo.size()!=0){
				info = true;
			}
		}catch (Exception e) {
			// TODO: handle exception
		}
		return info;
	}

    @Override
    public int selectEmail(Map<String, Object> map) {
        return this.baseMapper.selectEmail(map);
    }

    @Override
    public int selectPhone(Map<String, Object> map) {
        return this.baseMapper.selectPhone(map);
    }

    @Override
    public int editPassword(Map<String, Object> map) {
        return this.baseMapper.editPassword(map);
    }

    @Override
    public String selectSalt(Map<String, Object> map) { return this.baseMapper.selectSalt(map); }

    @Override
    public User selectSaltEmail(Map<String, Object> map) {return this.baseMapper.selectSaltEmail(map);  }
}
