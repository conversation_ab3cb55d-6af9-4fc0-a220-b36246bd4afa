package com.stylefeng.guns.modular.huanonginsurance.service.impl;


import com.stylefeng.guns.core.common.constant.Const;
import com.stylefeng.guns.core.common.exception.BizExceptionEnum;
import com.stylefeng.guns.core.exception.GunsException;
import com.stylefeng.guns.core.util.XStreamUtil;
import com.stylefeng.guns.modular.collectAppliAndPro.dao.AppliAndProMapper;
import com.stylefeng.guns.modular.customerservice.dao.OrderQueryMapper;
import com.stylefeng.guns.modular.huanonginsurance.service.HuaNongService;
import com.stylefeng.guns.modular.index.dao.AllInfoDtoMapper;
import com.stylefeng.guns.modular.index.model.*;
import com.stylefeng.guns.modular.inquiryPolicy.dao.ProjectMapper;
import com.stylefeng.guns.modular.insurer.dao.InsurerChildMapper;
import com.stylefeng.guns.modular.insurerinterface.sunshine.Encryption;
import com.stylefeng.guns.modular.insurerinterface.sunshine.HttpPostClient;
import com.stylefeng.guns.modular.xionganserviceplatform.SendInfoToXiongan;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 投保接口
 *
 * <AUTHOR>
 * @Date 2019-7-5
 */
@Service
public class HuaNongServiceImpl implements HuaNongService {

    @Resource
    private AllInfoDtoMapper allInfoDtoMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private InsurerChildMapper insurerChildMapper;
    @Resource
    private AppliAndProMapper appliAndProMapper;
    @Resource
    private OrderQueryMapper orderQueryMapper;
    @Override
    public Map<String, String> sendInsureInformationToHuaNong(String allInfoId, String source) {
        TransPolicyRequest transPolicyRequest = selectTransPolicyRequestById(allInfoId,source);
        String allInfoDtoXml = XStreamUtil.beanToXmlWithTagU(transPolicyRequest);
        System.out.println(allInfoDtoXml);
        Map<String, String> map = new HashMap<>();
        String resultXML =null;
        try{
            String HUANONG_URL = appliAndProMapper.getNotifyUrl(31);
            String HUANONG_KEY = appliAndProMapper.getNotifyUrl(7);
            String decode = HttpPostClient.sendPostHttp(HUANONG_URL, allInfoDtoXml,"UTF-8",HUANONG_KEY);
             resultXML = Encryption.authcodeDecode(decode,HUANONG_KEY);
            System.out.println(resultXML);
            TransPolicyResponse xmlToBean = XStreamUtil.xmlToBean(resultXML, TransPolicyResponse.class);
            return callBackAppliNo(allInfoId, source, xmlToBean);
        }catch (Exception e){
            //e.printStackTrace();
            map.put("applicationNo","");
            map.put("code","1029");
            map.put("respMsg",resultXML);
            return map;
        }
    }
    /**
     * 投保保险
     * @param allInfoId
     * @param source
     * @return
     */
    @Override
    public TransPolicyRequest selectTransPolicyRequestById(String allInfoId,String source) {
        String orderNumber = orderQueryMapper.selectOrderNumber(allInfoId,source);
        TransPolicyRequest transPolicyRequest = new TransPolicyRequest();
        transPolicyRequest.setSendSeq(orderNumber);
        AllInfoDto allInfoDto = allInfoDtoMapper.getAllInfoDtoById(allInfoId,source);
        Project project = allInfoDto.getProject();
        Map<String,Integer> param = insurerChildMapper.selectRateByAllInfoId(allInfoId,source);
        Integer areas = insurerChildMapper.selectRateByArea(String.valueOf(param.get("insuranceCompanyArea")));
        List<Map<String,Object>> rates = insurerChildMapper.selectRateByProjectCity(param.get("insuranceCompanyId"),areas,Const.CONST_ONE);
        if(rates.size() == 0){
            rates = insurerChildMapper.selectRateByProjectCounty(param.get("insuranceCompanyId"),areas,Const.CONST_ONE);
            if(rates.size() == 0){
                rates = insurerChildMapper.selectRateByProjectProvince(param.get("insuranceCompanyId"),areas,Const.CONST_ONE);
            }
        }
        System.out.println("查找时费率："+rates.get(0).get("rate").toString());
        BigDecimal divide = new BigDecimal(rates.get(0).get("rate").toString()).divide(new BigDecimal(100));
        ItemKind itemKind = allInfoDto.getItemKind().get(0);
        itemKind.setBenchMarkRate(divide.toString());
        transPolicyRequest.setSendName("GRXHKYTS");
        transPolicyRequest.setSendPwd("grxhkyts");
        transPolicyRequest.setSysFlag("GRXHKYTS");
        transPolicyRequest.setTransCode("SV000710");
        allInfoDto.setManageCom(String.valueOf(param.get("insuranceCompanyArea")));
        allInfoDto.setMainRiskCode("2227");
        itemKind.setKindCode("002");
        itemKind.setKindName("投标履约保证保险条款");
        itemKind.setItemName("投标履约");
        itemKind.setRiskcodesub("2227");
        //2019款删除字段默认传值
        //project.setGrade("1");
        //project.setProType("1");
        project.setPlannedTime("1");
        project.setTcp(new BigDecimal("1"));
        List<AllInfoDto> list = new ArrayList<>();
        list.add(allInfoDto);
        transPolicyRequest.setAllInfoDto(list);
        return transPolicyRequest;
    }
    private Map<String, String> callBackAppliNo(String allInfoId, String source,TransPolicyResponse xmlToBean) {
        Map<String, String> map =new HashMap<>();
            AllInfoDto allInfoDto = xmlToBean.getAllInfoDto().get(0);
            String result =allInfoDto.getOperateSuccess();
            if ("true".equals(result)){
                //通过
                String policyNo = allInfoDto.getPolicyNo();
                projectMapper.insertNoPayUrl(allInfoId,policyNo,allInfoDto.getImportSn(),source);
                if (Const.XIONGAN_TOKEN.equals(source)){
                    HashMap<String, Object> request = new HashMap<>();
                    request.put("id",allInfoId);
                    request.put("applicationNo",policyNo);
                    request.put("state",4);
                    SendInfoToXiongan.sendInfo(request);
                }
                map.put("applicationNo",policyNo);

            }else{
                //未通过
                projectMapper.updateStateById(3,allInfoId,source);
                map.put("applicationNo","");
                map.put("code","1029");
                map.put("respMsg",allInfoDto.getOperateMessage());
            }
        return map;
    }
}
