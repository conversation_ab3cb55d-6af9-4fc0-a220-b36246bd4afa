package com.stylefeng.guns.modular.xybserviceplatform.controller;

import com.cloopen.rest.sdk.CCPRestSmsSDK;
import com.stylefeng.guns.config.properties.GunsProperties;
import com.stylefeng.guns.core.common.constant.Const;
import com.stylefeng.guns.core.common.exception.BizExceptionEnum;
import com.stylefeng.guns.core.exception.GunsException;
import com.stylefeng.guns.core.support.HttpKit;
import com.stylefeng.guns.core.util.AcceptFileTypeUtil;
import com.stylefeng.guns.core.util.ToolUtil;
import com.stylefeng.guns.modular.insurerinterface.sunshine.Encryption;
import com.stylefeng.guns.modular.sms.SmsSendController;
import com.stylefeng.guns.modular.wukongSDK.constant.ConfigConstant;
import com.stylefeng.guns.modular.xybserviceplatform.service.FrontEndService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.util.*;

/**
 * 开发小信易保前端页面功能
 */
@RestController
@RequestMapping("/frontEnd")
public class FrontEndController {

    @Autowired
    FrontEndService frontEndService;
    @Autowired
    private GunsProperties gunsProperties;

    public static String getSysYear() {
        Calendar date = Calendar.getInstance();
        String year = String.valueOf(date.get(Calendar.YEAR));
        return year;
    }

    /**
     * 保函查验
     * @return
     */
    @RequestMapping("/guaranteeCheck/{insurancePolicyNo}/{unitName}")
    @ResponseBody
    public Map<String,Object> guaranteeCheck(@PathVariable String insurancePolicyNo,@PathVariable String unitName){
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> objectMap = frontEndService.guaranteeCheck(insurancePolicyNo, unitName);
        if (objectMap!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",objectMap);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }

    /**
     * 可靠的数据支持
     * @return
     */
    @RequestMapping("/xaDataSupport")
    @ResponseBody
    public Map<String,Object> cangzhouDataSupport(){
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> objectMap = frontEndService.cangzhouDataSupport();
        if (objectMap!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",objectMap);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }
    /**
     * 可靠的数据支持
     * @return
     */
    @RequestMapping("/dataSupport/{serviceType}/{area}")
    @ResponseBody
    public Map<String,Object> dataSupport(@PathVariable String serviceType,@PathVariable String area){
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> objectMap = frontEndService.dataSupport(serviceType,area);
        if (objectMap!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",objectMap);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }
    /**
     * 服务排名
     * @return
     */
    @RequestMapping("/serviceRank/{serviceType}/{area}")
    @ResponseBody
    public Map<String,Object> serviceRank(@PathVariable String serviceType,@PathVariable String area){
        Map<String, Object> map = new HashMap<>();
        List<Map<String,Object>> mapList = frontEndService.serviceRank(serviceType,area);
        if (mapList!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",mapList);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }
    /**
     * 产品（带机构信息）交易量
     * @return
     */
    @RequestMapping("/productTradingVolume")
    @ResponseBody
    public Map<String,Object> productTradingVolume(){
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> objectMap = frontEndService.productTradingVolume();
        if (objectMap!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",objectMap);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }

    /**
     * 近一年保函数据统计
     * @return
     */
    @RequestMapping("/totalGuarantees/{state}")
    @ResponseBody
    public Map<String,Object> totalGuarantees(@PathVariable String state){
        Map<String, Object> map = new HashMap<>();
        LinkedHashMap<String, Object> Object = frontEndService.totalGuarantees(getSysYear(), state);
        if (Object!=null){
            ArrayList<Integer> list = new ArrayList<>();
            for (Map.Entry<String, Object> m : Object.entrySet()) {
                list.add(Integer.parseInt(m.getValue().toString()));
            }
            map.put("code", Const.CONST_ZERO);
            map.put("data",list);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }

    /**
     * 保函总数-索赔状态
     * @return
     */
    @RequestMapping("/totalGuaranteesStatus")
    @ResponseBody
    public Map<String,Object> totalGuaranteesStatus(){
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> objectMap = frontEndService.totalGuaranteesStatus();
        if (objectMap!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",objectMap);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }

    /**
     * 履约异常公示统计
     * @return
     */
    @RequestMapping("/abnormalPerformance")
    @ResponseBody
    public Map<String,Object> abnormalPerformance(){
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> objectMap = frontEndService.abnormalPerformance();
        if (objectMap!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",objectMap);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }
    /**
     * 履约异常公示列表
     * @return
     */
    @RequestMapping("/abnormalPerformanceData")
    @ResponseBody
    public Map<String,Object> abnormalPerformanceData(int page){
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> mapList = frontEndService.abnormalPerformanceData(page);
        if (mapList!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",mapList);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }
    /**
     * 保单履约查询结果
     * @return
     */
    @RequestMapping("/policyPerformanceInquiry/{insurancePolicyNo}")
    @ResponseBody
    public Map<String,Object> policyPerformanceInquiry(@PathVariable String insurancePolicyNo){
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> objectMap = frontEndService.policyPerformanceInquiry(insurancePolicyNo);
        if (objectMap!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",objectMap);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }
    /**
     * 各方履约情况
     * @return
     */
    @RequestMapping("/performanceOfAllParties")
    @ResponseBody
    public Map<String,Object> performanceOfAllParties(@RequestBody String name){
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> objectMap = frontEndService.performanceOfAllParties(name);
        if (objectMap!=null){
            map.put("code", Const.CONST_ZERO);
            map.put("data",objectMap);
            return map;
        }else {
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }
    /**
     * 赔付异常报案
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, path = "/reportOfAbnormalCompensation")
    @ResponseBody
    public Map<String,Object> reportOfAbnormalCompensation(@RequestParam Map<String, String> CompensationData){
        return frontEndService.reportOfAbnormalCompensation(CompensationData);
    }
    @RequestMapping(method = RequestMethod.POST, path = "/upload")
    @ResponseBody
    public String upload(@RequestPart("file") MultipartFile picture) {
        String extensions = AcceptFileTypeUtil.getAcceptExtensions("document");
        // 验证待上传的文件类型是否符合要求
        // 原文件名
        String originalFilename = picture.getOriginalFilename();
        // 文件扩展名
        String suffix = ToolUtil.getFileSuffix(originalFilename);
        // 转化为小写后再验证
        if(!extensions.toLowerCase().contains(suffix.toLowerCase())){
            throw new GunsException(BizExceptionEnum.FILE_ACCEPUT_ERROR);
        }
        String OriginalFilename = picture.getOriginalFilename();
        String fileSavePath = gunsProperties.getFileUploadPath() + "report" + File.separator;
        String realurl = fileSavePath + OriginalFilename;
        File dir = new File(fileSavePath);
        if(!dir.exists()){
            dir.mkdirs();
        }
        Long size = dir.length() / 1024;
        if(size>5L){
            return "文件过大";
        }
        try {
            picture.transferTo(new File(realurl));
            String virtualPath = File.separator+ "report" + File.separator+OriginalFilename;
            System.out.println("上传文件成功："+realurl);
            return virtualPath;
        } catch (Exception e) {
            throw new GunsException(BizExceptionEnum.UPLOAD_ERROR);
        }
    }
    @RequestMapping(value="/clickGet/{phone}")
    @ResponseBody
    public Map<String,Object> sendSms(@PathVariable String phone) {
        CCPRestSmsSDK restAPI = new CCPRestSmsSDK();
        restAPI.init(ConfigConstant.sms_send_url, ConfigConstant.sms_port);
        restAPI.setAccount(ConfigConstant.sms_acount_sid, ConfigConstant.sms_account_token);
        restAPI.setAppId(ConfigConstant.sms_app_id);
        String code = SmsSendController.getNonce_str();
        HashMap<String, Object> result = restAPI.sendTemplateSMS(phone,"341490" ,new String[]{code,"5"});
        Map<String, Object> map = new HashMap<>();
        if("000000".equals(result.get("statusCode"))){
            System.out.println(phone+"-----qq-----"+code);
            HttpSession session = HttpKit.getRequest().getSession();
            session.setAttribute(phone,code);
            map.put("code",Const.CONST_ZERO);
            map.put("respMsg","success");
            return map;
        }else{
            System.out.println("错误码=" + result.get("statusCode") +" 错误信息= "+result.get("statusMsg"));
            map.put("code",Const.CONST_ZONE);
            map.put("respMsg","fail");
            return map;
        }
    }

}
