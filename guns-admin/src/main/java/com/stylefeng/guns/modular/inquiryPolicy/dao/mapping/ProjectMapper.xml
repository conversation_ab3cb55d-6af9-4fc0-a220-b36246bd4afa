<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.inquiryPolicy.dao.ProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.inquiryPolicy.model.Project">
        <id column="id" property="id" />
        <result column="agency" property="agency" />
        <result column="proName" property="proName" />
        <result column="bids" property="bids" />
        <result column="fileNumber" property="fileNumber" />
        <result column="proType" property="proType" />
        <result column="bidDate" property="bidDate" />
        <result column="bidTermValidity" property="bidTermValidity" />
        <result column="plannedTime" property="plannedTime" />
        <result column="proProvince" property="proProvince" />
        <result column="proCity" property="proCity" />
        <result column="proCunty" property="proCounty" />
        <result column="proAdd" property="proAdd" />
        <result column="insuranceStartTime" property="insuranceStartTime" />
        <result column="insuranceEndTime" property="insuranceEndTime" />
        <result column="insuranceAmount" property="insuranceAmount" />
        <result column="premium" property="premium" />
        <result column="tcp" property="tcp" />
        <result column="jurisdiction" property="jurisdiction" />
        <result column="dispute" property="dispute" />
        <result column="sendInfo" property="sendInfo" />
        <result column="invoice" property="invoice" />
        <result column="state" property="state" />
        <result column="isBasicPay" property="isBasicPay" />
        <result column="tenderDocument" property="tenderDocument" />
        <result column="projectContacts" property="projectContacts" />
        <result column="projectcontactsTel" property="projectcontactsTel" />
        <result column="projectEmail" property="projectEmail" />
        <result column="fpdm" property="fpdm" />
        <result column="fphm" property="fphm" />
        <result column="projectType" property="projectType" />
        <result column="officeType" property="officeType" />
        <result column="saleArea" property="saleArea" />
        <result column="electricPower" property="electricPower" />
        <result column="kbdate" property="kbDate" />
        <result column="xzCode" property="xzCode" />
        <result column="noticeUrl" property="noticeUrl" />
        <result column="noticePublishDate" property="noticePublishDate" />
        <result column="guaranteeProtocol" property="guaranteeProtocol" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, agency, proName, bids, fileNumber, proType, bidDate, bidTermValidity, plannedTime, proProvince, proCity, proCounty, proAdd, insuranceStartTime, insuranceEndTime, insuranceAmount, premium, tcp, jurisdiction, dispute,
        sendInfo, invoice, `state`, isBasicPay, tenderDocument, projectContacts, projectcontactsTel, projectEmail, fpdm, fphm, projectType, officeType, saleArea, electricPower, kbdate, xzCode, noticeUrl, noticePublishDate, guaranteeProtocol
    </sql>
   <!-- 后台保单查询-->
    <select id="getLists" resultType="map" parameterType="com.baomidou.mybatisplus.plugins.Page">
        select
         sp.id ,
         sp.proName,
         sp.bidDate,
         sp.bidTermValidity,
         sp.plannedTime,
         sp.insuranceEndTime,
         sp.insuranceStartTime,
         sp.insuranceAmount,
         sp.premium,
         sb.beneficiaryName,
         sai.state,
         sp.isBasicPay,
         sp.tenderDocument,
         sp.projectContacts,
         sp.projectcontactsTel,
         sp.projectEmail,
         sai.orderNumber,
         sai.serviceType,
        sai.createTime
         from
         ins_all_info sai  left join  ins_project  sp on sp.id = sai.projectId
          left join  ins_beneficiary sb on sai.beneficiary = sb.id
        where 1 = 1
          <if test="0">
              and sai.state in (1,5)
          </if>
        <if test="state !=null and state != ''and state!=0">
            and sai.state =#{state}
        </if>
                order by sai.createTime DESC
    </select>
    <!--前台页面查询信息-->
    <select id="getList" resultType="map" parameterType="com.baomidou.mybatisplus.plugins.Page">
        select
        su.name as bzrname,
        sp.id ,
        sp.proName,
        sp.bidDate,
        sp.bidTermValidity,
        sp.plannedTime,
        sp.insuranceEndTime,
        sp.insuranceStartTime,
        sp.insuranceAmount,
        sp.premium,
        sb.beneficiaryName,
        sp.isBasicPay,
        sp.tenderDocument,
        sp.projectContacts,
        sp.projectcontactsTel,
        sp.projectEmail,
        sai.state,
        sai.orderNumber,
        sai.serviceType,
        sai.id as allInfoId,
        sai.productId,
        sai.orderNumber,
        sai.createTime,
        sai.isRead,
        (select serverName from sys_server_type sst where sst.id = sai.serviceType) serviceType
        from
        ins_all_info sai  left join  ins_project  sp on sp.id = sai.projectId
        left join  ins_beneficiary sb on sai.beneficiary = sb.id
        left join sys_user su on sai.insuranceCompanyId = su.id
        where 1 = 1
        <if test="state!=null and state.length>0">
           and sai.state in
            <foreach collection="state" item="state" index="index"
                     open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        <if test="userId != null and userId !=''">
            and  sai.userId=#{userId}
        </if>
                order by sai.createTime DESC
    </select>
    <!-- 查询未读保单数量  -->
    <select id="selectNotRead" resultType="map">
        SELECT
            count(case when state =1 then 1 else null end ) as p1,
            count(case when state =2 then 1 else null end ) as p2,
            count(case when state =3 then 1 else null end ) as p3,
            count(case when state in (4,5) then 1 else null end ) as p4,
            count(case when state in(6,7,8,9) then 1 else null end ) as p5
        FROM
            ins_all_info
        WHERE userId=#{userId} and source = '950f66e4b10c4703913c87a0c78b61ce'
    </select>

    <insert id="addProjectInfo" useGeneratedKeys="true" keyProperty="id">
    	insert into ins_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agency != null ">agency,</if>
            <if test="proName != null ">proName,</if>
            <if test="proCode != null ">proCode,</if>
            <if test="bids != null ">bids,</if>
            <if test="fileNumber != null ">fileNumber,</if>
            <if test="proType != null ">proType,</if>
            <if test="bidDate != null ">bidDate,</if>
            <if test="bidTermValidity != null ">bidTermValidity,</if>
            <if test="plannedTime != null ">plannedTime,</if>
            <if test="proProvince != null ">proProvince,</if>
            <if test="proCity != null ">proCity,</if>
            <if test="proCounty != null ">proCounty,</if>
            <if test="proAdd != null ">proAdd,</if>
            <if test="insuranceStartTime != null ">insuranceStartTime,</if>
            <if test="insuranceEndTime != null ">insuranceEndTime,</if>
            <if test="insuranceAmount != null ">insuranceAmount,</if>
            <if test="premium != null ">premium,</if>
            <if test="tcp != null ">tcp,</if>
            <if test="jurisdiction != null ">jurisdiction,</if>
            <if test="dispute != null ">dispute,</if>
            <if test="sendInfo != null ">sendInfo,</if>
            <if test="invoice != null ">invoice,</if>
            <if test="state != null ">`state`,</if>
            <if test="isBasicPay != null ">isBasicPay,</if>
            <if test="tenderDocument != null ">tenderDocument,</if>
            <if test="projectContacts != null ">projectContacts,</if>
            <if test="projectcontactsTel != null ">projectcontactsTel,</if>
            <if test="projectEmail != null ">projectEmail,</if>
            <if test="fpdm != null ">fpdm,</if>
            <if test="fphm != null ">fphm,</if>
            <if test="projectType != null ">projectType,</if>
            <if test="officeType != null ">officeType,</if>
            <if test="saleArea != null ">saleArea,</if>
            <if test="electricPower != null ">electricPower,</if>
            <if test="kbDate != null ">kbdate,</if>
            <if test="xzCode != null ">xzCode,</if>
            <if test="noticeUrl != null ">noticeUrl,</if>
            <if test="noticePublishDate != null ">noticePublishDate,</if>
            <if test="guaranteeProtocol != null ">guaranteeProtocol,</if>
            <if test="actRate != null ">actRate,</if>
            <if test="actAmount != null ">actAmount,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agency != null">#{agency},</if>
            <if test="proName != null">#{proName},</if>
            <if test="proCode != null">#{proCode},</if>
            <if test="bids != null">#{bids},</if>
            <if test="fileNumber != null">#{fileNumber},</if>
            <if test="proType != null">#{proType},</if>
            <if test="bidDate != null">#{bidDate},</if>
            <if test="bidTermValidity != null">#{bidTermValidity},</if>
            <if test="plannedTime != null">#{plannedTime},</if>
            <if test="proProvince != null">#{proProvince},</if>
            <if test="proCity != null">#{proCity},</if>
            <if test="proCounty != null">#{proCounty},</if>
            <if test="proAdd != null">#{proAdd},</if>
            <if test="insuranceStartTime != null">#{insuranceStartTime},</if>
            <if test="insuranceEndTime != null">#{insuranceEndTime},</if>
            <if test="insuranceAmount != null">#{insuranceAmount},</if>
            <if test="premium != null">#{premium},</if>
            <if test="tcp != null">#{tcp},</if>
            <if test="jurisdiction != null">#{jurisdiction},</if>
            <if test="dispute != null">#{dispute},</if>
            <if test="sendInfo != null">#{sendInfo},</if>
            <if test="invoice != null">#{invoice},</if>
            <if test="state != null">#{state},</if>
            <if test="isBasicPay != null">#{isBasicPay},</if>
            <if test="tenderDocument != null">#{tenderDocument},</if>
            <if test="projectContacts != null">#{projectContacts},</if>
            <if test="projectcontactsTel != null">#{projectcontactsTel},</if>
            <if test="projectEmail != null">#{projectEmail},</if>
            <if test="fpdm != null">#{fpdm},</if>
            <if test="fphm != null">#{fphm},</if>
            <if test="projectType != null">#{projectType},</if>
            <if test="officeType != null">#{officeType},</if>
            <if test="saleArea != null">#{saleArea},</if>
            <if test="electricPower != null">#{electricPower},</if>
            <if test="kbDate != null">#{kbDate},</if>
            <if test="xzCode != null">#{xzCode},</if>
            <if test="noticeUrl != null">#{noticeUrl},</if>
            <if test="noticePublishDate != null">#{noticePublishDate},</if>
            <if test="guaranteeProtocol != null">#{guaranteeProtocol},</if>
            <if test="actRate != null">#{actRate},</if>
            <if test="actAmount != null">#{actAmount},</if>
        </trim>
    </insert>
    <update id="upProjectInfo" >
    	update ins_project set agency=#{agency},proName=#{proName},bids=#{bids},fileNumber=#{fileNumber},proType=#{proType},bidDate=#{bidDate},
    	bidTermValidity=#{bidTermValidity},plannedTime=#{plannedTime},proProvince=#{proProvince},proCity=#{proCity},proCounty=#{proCounty},
    	proAdd=#{proAdd},tcp=#{tcp},insuranceEndTime=#{insuranceEndTime},insuranceAmount=#{insuranceAmount},
    	premium=#{premium},dispute=#{dispute},sendInfo=#{sendInfo},invoice=#{invoice},isBasicPay=#{isBasicPay},
    	tenderDocument=#{tenderDocument},projectContacts=#{projectContacts},projectcontactsTel=#{projectcontactsTel},projectEmail=#{projectEmail}
        where id=#{id}
    </update>

    <select id="selectDetailById" parameterType="java.lang.Integer" resultType="java.util.HashMap" >
    SELECT  iai.id,ia.unitName,ia.cardType,ia.cardNum,ia.unitPhone,ia.unitProvince,ia.unitCity,ia.unitCounty,ia.unitAdd,ia.contacts,ia.contactsTel,ia.email,ia.licenseFile,ia.grade,ia.gradeFile,
   ip.agency,ip.proName,ip.bids,ip.fileNumber,ip.proType,ip.bidDate,ip.bidTermValidity,ip.plannedTime,ip.proProvince,ip.proCity,ip.proCounty,ip.proAdd,ip.tcp,ip.insuranceStartTime,ip.insuranceEndTime,ip.insuranceAmount,ip.premium,ip.jurisdiction,ip.dispute,ip.sendInfo,ip.invoice,ip.isBasicPay,ip.tenderDocument,ip.projectContacts,ip.projectcontactsTel,ip.projectEmail,
   ib.beneficiaryName,ib.beneficiaryCardType,ib.beneficiaryCardNum,ib.cardTime,ib.beneficiaryProvince,ib.beneficiaryCity,ib.beneficiaryCounty,ib.beneficiaryAdd,ib.beneficiaryContacts,ib.beneficiaryContactPhone,ib.beneficiaryContactTel
       FROM ins_all_info iai LEFT JOIN ins_project ip  ON  ip.id = iai.projectId
       LEFT JOIN ins_applicant_history ia ON ia.id = iai.applicant
   LEFT JOIN ins_beneficiary ib ON ib.id = iai.beneficiary
       WHERE iai.projectId = #{allInfoId}
    </select>
  <!-- 修改审核状态-->
    <update id="updateStateById">
        update ins_all_info set state = #{state}
        <if test="state != 9">
          ,isRead = 0
        </if>
        where id = #{id} and source = #{source}
    </update>
    <update id="updateInvoiceById">
        update ins_all_info set invoiceFlag = #{state}
        where id = #{id} and source = #{source}
    </update>
    <update id="updateInvoiceInfo">
        update ins_all_info set invoiceFlag = 1
        where orderNumber = #{processId} and insurancePolicyNo = #{policyNo}
    </update>
    <!-- 添加投保单号和添加时间-->
    <update id="insertNo" >
       update ins_all_info set
       insuranceApplicationNo = #{insuranceApplicationNo},
       insuranceApplicationNoTime = now(),
       state = 4,
       isRead = 0
        where
        id = #{id}
        and source = #{source}
    </update>
    
    <update id="insertNoPayUrl" >
       update ins_all_info set
       insuranceApplicationNo = #{insuranceApplicationNo},
       insuranceApplicationNoTime = now(),
       pcPaymentLink = #{pcPaymentLink},
       state = 4,
       isRead = 0
        where
        id = #{id} and source = #{source}
    </update>
    <update id="insertSinosurePayUrl" >
       update ins_all_info set
       pcPaymentLink = #{pcPaymentLink},
       isRead = 0
        where
        orderNumber = #{orderNo}
    </update>

    <!-- 添加下载保函URL-->
    <update id="insertPolicyNo" >
       update ins_all_info set
       url = #{url},
       state = 7
        where
        id = #{id} and source = #{source}
    </update>
    
    <select id="getPayUrlById" resultType="string" parameterType="string">
    	select 
    		pcPaymentLink
    	from ins_all_info
    	where id = #{allInfoId} and source = #{source}
    </select>
    
    
    
   <!-- 查询保单号 投保单号等-->
    <select id="selectPolicyNo" resultType="map">
        select
         iai.insurancePolicyNo,
         iai.insurancePolicyNoTime,
         iai.insuranceApplicationNo,
         iai.insuranceApplicationNoTime,
         iai.insuranceCompanyId,
         iai.orderNumber,
         iai.createTime,
         iai.eGuaranteeUrl,
         iai.ePolicyUrl,
         iai.invoiceurl,
         ip.proName,
         ip.premium,
         ip.isBasicPay,
         iah.unitName,
         (select productName from sys_product where id = iai.productId)productName,
         iai.state
        from
          ins_all_info iai,
          ins_project ip,
          ins_applicant_history iah
        where
        iai.projectId = ip.id
        and
        iah.id = iai.applicant
        and
        iai.id = #{id}
    </select>
    <select id="selectPolicyNoIofo" resultType="map">
          select
          iai.id,
         iai.eGuaranteeUrl,
         iai.ePolicyUrl,
         iai.insurancePolicyNo
         from ins_all_info iai,
          ins_project ip
        where
        iai.projectId = ip.id
        and
        iai.insuranceApplicationNo = #{policyNo}
        or iai.insurancePolicyNo = #{policyNo}
    </select>
    <select id="selectApplicationNo" parameterType="string" resultType="map">
        select
         iai.id,
         iai.pcPaymentLink,
         iai.eGuaranteeUrl,
         iai.ePolicyUrl,
         iai.insurancePolicyNo,
         iai.orderNumber,
         ip.premium,
         iai.areaCode
         from ins_all_info iai,
          ins_project ip
        where
        iai.projectId = ip.id
        and
        iai.insuranceApplicationNo = #{insuranceApplicationNo}
    </select>
    <update id="updatePayConfirm">
        update ins_all_info set confirmPayment = #{confirmPayment}, genStatus = #{params.genStatus}
        where id = #{params.id} and source = #{params.source}
    </update>

    <update id="updateEndTime">
        update ins_project set insuranceEndTime = #{insuranceEndTime} where id = #{projectId}
    </update>
</mapper>
