<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.customerservice.dao.CSInsurerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.customerservice.model.CSInsurer">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="sysUserId" property="sysUserId" />
        <result column="contact" property="contact" />
        <result column="address_p" property="addressP" />
        <result column="address_c" property="addressC" />
        <result column="address_a" property="addressA" />
        <result column="detailedAddress" property="detailedAddress" />
        <result column="bankAddress" property="bankAddress" />
        <result column="bankName" property="bankName" />
        <result column="bankAccount" property="bankAccount" />
        <result column="passTime" property="passTime" />
        <result column="sysProductId" property="sysProductId" />
        <result column="rate" property="rate" />
        <result column="business" property="business" />
        <result column="insurancePolicy" property="insurancePolicy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, sysUserId, contact, address_p, address_c, address_a, detailedAddress, bankAddress, bankName, bankAccount, passTime, sysProductId, rate, business, insurancePolicy
    </sql>

    <!-- 重新定义的根据条件进行查询，逆序排序-->
    <select id="selectMiniList" parameterType="com.baomidou.mybatisplus.plugins.Page" resultType="map">
        SELECT
            ii.name , su.name as contactName,ii.contact ,sysProductId ,sp.productName,business , rate,businessName
        FROM
            ic_insurer ii
        LEFT JOIN sys_user su ON ii.sysUserId = su.id
        LEFT JOIN sys_product sp ON ii.sysProductId = sp.id
        where 1 = 1
        ${ew.sqlSegment}
    </select>

</mapper>
