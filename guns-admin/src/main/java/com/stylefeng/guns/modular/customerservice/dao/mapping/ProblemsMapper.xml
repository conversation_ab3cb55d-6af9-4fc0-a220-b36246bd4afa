<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.customerservice.dao.ProblemsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.customerservice.model.Problems">
        <id column="id" property="id" />
        <result column="workOrderId" property="workOrderId" />
        <result column="problemDescription" property="problemDescription" />
        <result column="handler" property="handler" />
        <result column="problemClassification" property="problemClassification" />
        <result column="priority" property="priority" />
        <result column="solutionStates" property="solutionStates" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, workOrderId, problemDescription, handler, problemClassification, priority, solutionStates
    </sql>

    <!-- 根据工单id查询该工单对应的所有的问题 -->
    <select id="selectByWorkOrderId" parameterType="java.lang.String" resultType="com.stylefeng.guns.modular.customerservice.model.Problems">
       SELECT
        *
       from
        cs_problems
        where
        workOrderId = #{workOrderId}
    </select>

    <!-- 批量插入问题集合 -->
    <insert id="insertProblemsArrayList" parameterType = "java.util.List">
        INSERT INTO cs_problems (workOrderId, problemDescription,handler,problemClassification,priority,solutionStates)
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.workOrderId}, #{item.problemDescription}, #{item.handler}, #{item.problemClassification},
              #{item.priority}, #{item.solutionStates})
        </foreach>
    </insert>

    <!-- 根据工单id删除问题集合 -->
    <delete id="deleteProblemsByWorkOrderId" parameterType="java.lang.String">
        delete
        from
          cs_problems
        where
          workOrderId = #{workOrderId}
    </delete>

</mapper>
