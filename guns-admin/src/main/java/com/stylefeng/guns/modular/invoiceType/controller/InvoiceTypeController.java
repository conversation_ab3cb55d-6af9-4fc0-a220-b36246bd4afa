package com.stylefeng.guns.modular.invoiceType.controller;

import com.stylefeng.guns.core.base.controller.BaseController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.beans.factory.annotation.Autowired;
import com.stylefeng.guns.core.log.LogObjectHolder;
import org.springframework.web.bind.annotation.RequestParam;
import com.stylefeng.guns.modular.invoiceType.model.InvoiceType;
import com.stylefeng.guns.modular.invoiceType.service.IInvoiceTypeService;

/**
 * 发票类型控制器
 *
 * <AUTHOR>
 * @Date 2018-09-21 17:29:37
 */
@Controller
@RequestMapping("/invoiceType")
public class InvoiceTypeController extends BaseController {

    private String PREFIX = "/invoiceType/invoiceType/";

    @Autowired
    private IInvoiceTypeService invoiceTypeService;

    /**
     * 跳转到发票类型首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "invoiceType.html";
    }

    /**
     * 跳转到添加发票类型
     */
    @RequestMapping("/invoiceType_add")
    public String invoiceTypeAdd() {
        return PREFIX + "invoiceType_add.html";
    }

    /**
     * 跳转到修改发票类型
     */
    @RequestMapping("/invoiceType_update/{invoiceTypeId}")
    public String invoiceTypeUpdate(@PathVariable Integer invoiceTypeId, Model model) {
        InvoiceType invoiceType = invoiceTypeService.selectById(invoiceTypeId);
        model.addAttribute("item",invoiceType);
        LogObjectHolder.me().set(invoiceType);
        return PREFIX + "invoiceType_edit.html";
    }

    /**
     * 获取发票类型列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public Object list(String condition) {
        return invoiceTypeService.selectList(null);
    }

    /**
     * 新增发票类型
     */
    @RequestMapping(value = "/add")
    @ResponseBody
    public Object add(InvoiceType invoiceType) {
        invoiceTypeService.insert(invoiceType);
        return SUCCESS_TIP;
    }

    /**
     * 删除发票类型
     */
    @RequestMapping(value = "/delete")
    @ResponseBody
    public Object delete(@RequestParam Integer invoiceTypeId) {
        invoiceTypeService.deleteById(invoiceTypeId);
        return SUCCESS_TIP;
    }

    /**
     * 修改发票类型
     */
    @RequestMapping(value = "/update")
    @ResponseBody
    public Object update(InvoiceType invoiceType) {
        invoiceTypeService.updateById(invoiceType);
        return SUCCESS_TIP;
    }

    /**
     * 发票类型详情
     */
    @RequestMapping(value = "/detail/{invoiceTypeId}")
    @ResponseBody
    public Object detail(@PathVariable("invoiceTypeId") Integer invoiceTypeId) {
        return invoiceTypeService.selectById(invoiceTypeId);
    }
}
