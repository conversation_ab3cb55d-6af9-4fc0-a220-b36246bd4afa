<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.system.dao.DeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.system.model.Dept">
        <id column="id" property="id"/>
        <result column="num" property="num"/>
        <result column="pid" property="pid"/>
        <result column="pids" property="pids"/>
        <result column="simplename" property="simplename"/>
        <result column="fullname" property="fullname"/>
        <result column="tips" property="tips"/>
        <result column="version" property="version"/>
    </resultMap>

    <select id="tree" resultType="com.stylefeng.guns.core.node.ZTreeNode">
		select id,pid as pId,simplename as name,
		(
		CASE
		WHEN (pId = 0 OR pId IS NULL) THEN
		'true'
		ELSE
		'false'
		END
		) as isOpen from sys_dept
	</select>
    <!-- clj 查询mimi树，只包含省市 -->
    <select id="miniTree" resultType="com.stylefeng.guns.core.node.ZTreeNode">
		select id,pid as pId,simplename as name,
		(
		CASE
		WHEN (pId = 0 OR pId IS NULL) THEN
		'true'
		ELSE
		'false'
		END
		) as isOpen from sys_dept
		where
		  level = 1 or level = 2
	</select>

    <select id="list" resultType="map">
        select * from sys_dept
        <if test="condition != null and condition != ''">
            where simplename like CONCAT('%',#{condition},'%') or fullname like CONCAT('%',#{condition},'%')
        </if>
        order by num ASC
    </select>
	<!-- 根据父级节点查询部门ID和名称 -->
	<select id="getDeptListByPid" parameterType="int" resultType="com.stylefeng.guns.modular.system.model.Dept">
        SELECT id,fullname from sys_dept where pid =#{pid}
		<if test="isBondingCompanyProvince != null and isBondingCompanyProvince != ''">
			AND id in (select address_p from bonding_bank_info where isDeleted = 0 group by address_p)
		</if>
		<if test="isBondingCompanyCity != null and isBondingCompanyCity != ''">
			AND id in (select address_c from bonding_bank_info where isDeleted = 0 and address_p = #{pid} group by address_c)
		</if>
    </select>
</mapper>
