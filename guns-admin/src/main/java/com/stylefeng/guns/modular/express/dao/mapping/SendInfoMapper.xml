<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.express.dao.SendInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.express.model.SendInfo">
        <id column="id" property="id" />
        <result column="consignee" property="consignee" />
        <result column="consigneeAdd" property="consigneeAdd" />
        <result column="consigneeTel" property="consigneeTel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, consignee, consigneeAdd, consigneeTel
    </sql>
    
    <insert id="addSendInfo" useGeneratedKeys="true" keyProperty="id">
      insert into pro_send_info (consignee, consigneeAdd, consigneeTel) value (#{consignee},#{consigneeAdd},#{consigneeTel})
    </insert>
    <update id="upSendInfo" >
      update pro_send_info set consignee=#{consignee},consigneeAdd=#{consigneeAdd},consigneeTel=#{consigneeTel} where id=#{id}
    </update>
    <select id="getSendInfo" resultType="com.stylefeng.guns.modular.express.model.SendInfo">
    	select id, consignee, consigneeAdd, consigneeTel
    	from pro_send_info
    	where
    	id = #{id}
    </select>

</mapper>
