package com.stylefeng.guns.modular.icinformation.service.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.stylefeng.guns.modular.icinformation.dao.InsurerClaimsMapper;
import com.stylefeng.guns.modular.icinformation.model.InsurerClaims;
import com.stylefeng.guns.modular.icinformation.service.IInsurerClaimsService;
import com.stylefeng.guns.modular.index.po.AllInfoPO;
import com.stylefeng.guns.modular.index.service.LnzjService;
import com.stylefeng.guns.modular.index.vo.LnzjClaimsApplyCallBackVO;
import com.stylefeng.guns.modular.insInfo.dao.AllInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 理赔实现类
 * </p>
 *
 * <AUTHOR>
 * @Date 2023-03-27 11:35:33
 */
@Service
public class InsurerClaimsServiceImpl extends ServiceImpl<InsurerClaimsMapper, InsurerClaims> implements IInsurerClaimsService {

    @Resource
    private InsurerClaimsMapper insurerClaimsMapper;
    @Resource
    private AllInfoMapper allInfoMapper;
    @Autowired
    private LnzjService lnzjService;

    /**
     * 获取理赔数据
     * @param page
     * @param id
     * @param insurancePolicyNo
     * @return
     */
    @Override
    public List<Map<String,Object>> getInsurerClaimsList(Page page, Integer id,String insurancePolicyNo) {

        List<Map<String, Object>> detailLists = insurerClaimsMapper.getInsurerClaimsList(page, id, insurancePolicyNo);
        return detailLists;

    }

    /**
     * 获取理赔详情
     * @param allInfoId
     * @return
     */
    @Override
    public InsurerClaims getClaimsDetail(String allInfoId) {
        return insurerClaimsMapper.selectByOrderNumber(allInfoId);
    }

    /**
     * 更新理赔状态
     * @param orderNumber
     * @param claimsStatus
     */
    @Override
    public void updateClaimsStatus(String orderNumber,  String insurancePolicyNo, String claimsStatus, String claimsRemark) {
        insurerClaimsMapper.updateClaimsStatus(orderNumber, insurancePolicyNo,claimsStatus, claimsRemark);
        //更新理赔状态
        allInfoMapper.updateClaimsStatus(orderNumber, insurancePolicyNo, claimsStatus);



        //理赔通知
        try {
            LnzjClaimsApplyCallBackVO claimVO = new LnzjClaimsApplyCallBackVO();
            claimVO.setOrderNumber(orderNumber);
            claimVO.setInsurancePolicyNo(insurancePolicyNo);
            //获取保函信息
            AllInfoPO allInfo = allInfoMapper.selectByOrderNumber(orderNumber);
            if(allInfo.getPlatformCode().equals("LNZJ")){
                lnzjService.claimsApplyCallBack(claimVO, "v1");
            }else{
                lnzjService.claimsApplyCallBack(claimVO, "v2");
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("理赔通知失败："+orderNumber);
        }
    }
}
