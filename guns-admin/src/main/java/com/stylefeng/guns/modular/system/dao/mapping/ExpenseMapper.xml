<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.system.dao.ExpenseMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.system.model.Expense">
		<id column="id" property="id" />
		<result column="money" property="money" />
		<result column="desc" property="desc" />
		<result column="createtime" property="createtime" />
		<result column="state" property="state" />
		<result column="userid" property="userid" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, money, desc, createtime, state, userid
    </sql>

</mapper>
