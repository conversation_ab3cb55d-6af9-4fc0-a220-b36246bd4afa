<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.insurer.dao.InsurerChildMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.insurer.model.InsurerChild">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="sysUserId" property="sysUserId" />
        <result column="contact" property="contact" />
        <result column="address_p" property="addressP" />
        <result column="address_c" property="addressC" />
        <result column="address_a" property="addressA" />
        <result column="detailedAddress" property="detailedAddress" />
        <result column="bankAddress" property="bankAddress" />
        <result column="bankName" property="bankName" />
        <result column="bankAccount" property="bankAccount" />
        <result column="passTime" property="passTime" />
        <result column="sysProductId" property="sysProductId" />
        <result column="rate" property="rate" />
        <result column="business" property="business" />
        <result column="insurancePolicy" property="insurancePolicy" />
        <result column="businessName" property="businessName" />
        <result column="pid" property="pid" />
        <result column="isDeleted" property="isDeleted" />
        <result column="createTime" property="createTime" />
        <result column="modifiedTime" property="modifiedTime" />
        <result column="createUser" property="createUser" />
        <result column="modifiedUser" property="modifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, sysUserId, contact, address_p, address_c, address_a, detailedAddress, bankAddress, bankName, bankAccount, passTime, sysProductId, rate, business, insurancePolicy, businessName, pid, isDeleted, createTime, modifiedTime, createUser, modifiedUser
    </sql>
    <select id="selectNameByArea" parameterType="int" resultType="map">
        select
         if(pid=0,simplename,pid) name,
         level
         from sys_dept
         WHERE id = #{area}
    </select>
    <!-- 根据关联表id查询保险地区费率的参数 -->
    <select id="selectRateByAllInfoId" resultType="map">
        select
            iai.insuranceCompanyId,
            iai.insuranceCompanyArea,
            iai.serviceType,
            ip.proProvince,
            ip.proCity
        from
        ins_all_info iai join
        ins_project ip on
        iai.projectId = ip.id
        where iai.id = #{allInfoId} and iai.source = #{source}
    </select>
    <!-- 根据省查询保险公司费率 -->
    <select id="selectRateByProjectProvince" resultType="map">
        SELECT
        ii.rate
        FROM
        ic_insurer ii
        where
        ii.createUser = #{pid}
        <if test="address_p !=null and address_p != ''">
            AND ii.address_p = #{address_p} AND (ii.address_c is null or ii.address_c = '')
        </if>
        <if test="serviceType !=null and serviceType != ''">
            AND ii.sysProductId = #{serviceType}
        </if>
    </select>
    <!-- 根据市查询保险公司费率 -->
    <select id="selectRateByProjectCity" resultType="map">
        SELECT
            ii.rate
        FROM
            ic_insurer ii
        where
            ii.createUser = #{pid}
        <if test="address_c !=null and address_c != ''">
            AND ii.address_c = #{address_c} AND (ii.address_a is null or ii.address_a = '')
        </if>
        <if test="serviceType !=null and serviceType != ''">
            AND ii.sysProductId = #{serviceType}
        </if>
    </select>
    <select id="selectRateByProjectCounty" resultType="map">
        SELECT
            ii.rate
        FROM
            ic_insurer ii
        where
            ii.createUser = #{pid}
        <if test="address_a !=null and address_a != ''">
            AND ii.address_a = #{address_a}
        </if>
        <if test="serviceType !=null and serviceType != ''">
            AND ii.sysProductId = #{serviceType}
        </if>
    </select>
    <select id="selectRate" resultType="map">
        SELECT ii.rate FROM ic_insurer ii where ii.createUser =#{pid} AND ii.address_c =
IFNULL( (SELECT ii.address_c FROM ic_insurer ii where ii.createUser =#{pid} AND ii.address_c = #{address_c}),'74')
    </select>

    <select id="selectRateByArea" parameterType="string" resultType="int">
        SELECT id from sys_dept WHERE simplename like  (SELECT NAME from base_area WHERE CODE = #{area})
    </select>
    <!-- 查询默认保险公司费率 -->
    <select id="selectDefaultRateByProjectAddress" resultType="map">
        SELECT
        ii.rate
        FROM
        ic_insurer ii
        where
        ii.sysUserId = #{pid}
    </select>
    <!--获取保险公司信息列表-->
    <select id="getList"  resultType="map">
         select id,name,sysUserId,contact,
        (SELECT simplename from sys_dept where id =address_p) addressP,
        (SELECT simplename from sys_dept where id =address_c) addressC,
        (SELECT simplename from sys_dept where id =address_a) addressA,
        detailedAddress,bankAddress,bankName,bankAccount,passTime,rate,business,businessName,
        (select productName from sys_product where id=sysProductId ) sysProductId
         from ic_insurer WHERE createUser = #{sysUserId}
    </select>
    <!-- 根据用户来查询保险公司 -->
    <select id="selectInsurerByAllInfoId" resultType="com.stylefeng.guns.modular.insurer.model.InsurerChild">
        select
        ii.name,
        ii.bankName,
        ii.bankAddress,
        ii.bankAccount
        from ic_insurer ii
        left join ins_all_info iai on iai.insuranceCompanyId = ii.sysUserId
        where iai.id = #{allInfoId} and iai.source = #{source}
    </select>
    <select id="getCompany" parameterType="Integer" resultType="map">
        select ia.unitName,su.outsource from ins_applicant ia,sys_user su where ia.uid = #{id} and su.id =#{id}
    </select>
    <select id="getOneCode" parameterType="string" resultType="int">
        SELECT count(0) from codepremiu WHERE  code = #{code}
    </select>
    <select id="getCode" parameterType="string" resultType="string">
        SELECT CODE from base_area WHERE NAME =
        (SELECT simplename from sys_dept WHERE id = #{area})
    </select>
    <insert id="insertpremiu" parameterType="string">
        insert into codepremiu (premiu,code)values (#{premiu},#{code})
    </insert>
    <update id="updatepremiu" parameterType="string">
        update codepremiu set premiu=#{premiu} where code=#{code}
    </update>

    <select id="getPremiuList"  resultType="map">
         select cp.premiu,cp.code,(select NAME from base_area where CODE = cp.code) name
         from codepremiu cp
    </select>
    <select id="getFinanceList" parameterType="string" resultType="map">
         SELECT (SELECT NAME from base_area WHERE `CODE` = iai.insuranceCompanyArea)area,
            (SELECT `name` from ic_insurer  WHERE sysUserId =iai.insuranceCompanyId) company,
        SUM(ip.insuranceAmount) amount,SUM(ip.premium) premium,COUNT(iai.insurancePolicyNo) count,iai.insuranceCompanyArea
            from ins_all_info iai
            join ins_project ip
            on iai.projectId = ip.id
            WHERE iai.state>=6 and iai.eGuaranteeUrl is not null
            <if test="startTime!=null and startTime!=''">
                and iai.insurancePolicyNoTime >= #{startTime}
            </if>
            <if test="endTime!=null and endTime!=''">
            and #{endTime}>= iai.insurancePolicyNoTime
            </if>
            GROUP BY iai.insuranceCompanyId,iai.insuranceCompanyArea
    </select>
    <select id="getFinanceListjb" parameterType="string" resultType="map">
        SELECT SUM(premium) jbmoney,iai.insuranceCompanyArea from ins_project ip join ins_all_info iai on iai.projectId = ip.id
        WHERE iai.state>=6 and iai.eGuaranteeUrl is not null
        <if test="startTime!=null and startTime!=''">
            and ip.insuranceEndTime>= #{startTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            and #{endTime}>= ip.insuranceEndTime
        </if>
        <if test="endTime==null or endTime==''">
            and now()>= ip.insuranceEndTime
        </if>
        GROUP BY iai.insuranceCompanyId,iai.insuranceCompanyArea
    </select>
    <select id="getFinanceListzb" parameterType="string" resultType="map">
        SELECT SUM(premium) zbmoney,iai.insuranceCompanyArea from ins_project ip join ins_all_info iai on iai.projectId = ip.id
        WHERE iai.state>=6 and iai.eGuaranteeUrl is not null
        <if test="endTime!=null and endTime!=''">
            and ip.insuranceEndTime>= #{endTime}
        </if>
        <if test="endTime==null or endTime==''">
            and ip.insuranceEndTime>= now()
        </if>
        GROUP BY iai.insuranceCompanyId,iai.insuranceCompanyArea
    </select>

    <select id="getConfigureCode"  resultType="map">
         select provinceCode id,(select simplename from sys_dept where id =provinceCode) name
         from ins_sign_city
         WHERE isDeleted = 0
    </select>
    <select id="getConfigure" parameterType="int"  resultType="map">
         select operator,manageCom,agentCode,inputOperator,agencyCode,agencyNo,channelType,businessNature
         from ins_sign_city
         WHERE provinceCode = #{id} and isDeleted = 0
    </select>
    <update id="updateConfigure" parameterType="com.stylefeng.guns.modular.insurerinterface.sunshine.model.Configure" >
         update ins_sign_city
          set operator=#{operator},manageCom=#{manageCom},agentCode=#{agentCode}
          ,inputOperator=#{inputOperator},agencyCode=#{agencyCode},agencyNo=#{agencyNo}
          ,channelType=#{channelType},businessNature=#{businessNature}
         WHERE provinceCode = #{address} and isDeleted = 0
    </update>
    <select id="getArea" parameterType="int"  resultType="map">
         select
         if(pid=0,tips,pid) companyArea,
		 version address_c
         from sys_dept
         WHERE id = #{areas}
    </select>
    <select id="selectInfoByAllInfoId" resultType="map">
        select
            orderNumber,
            imageFile
        from
        ins_all_info
        where id = #{allInfoId} and source = #{source}
    </select>
</mapper>
