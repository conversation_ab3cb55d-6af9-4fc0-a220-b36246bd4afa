<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.icinformation.dao.InsurerGiveupMapper">
    <select id="getInsurerGiveupList" parameterType="com.baomidou.mybatisplus.plugins.Page" resultType="java.util.HashMap">
        SELECT iai.platformCode, iai.source,iai.confirmPayment, iai.id,iai.state,iai.insuranceApplicationNo,(SELECT NAME from base_area WHERE CODE = iai.insuranceCompanyArea) insuranceCompanyArea,
        iai.eGuaranteeUrl,iai.invoiceFlag,iai.createTime,iai.invoiceurl, iai.giveupStatus, iai.orderNumber, iai.insurancePolicyNo,
        ib.beneficiaryName,ia.unitName,ia.cardNum,ia.contactsTel,ip.proName,DATE_FORMAT(ip.bidDate,'%Y-%m-%d')bidDate,
        ip.bidTermValidity,ip.plannedTime, DATE_FORMAT(ip.insuranceStartTime,'%Y-%m-%d') insuranceStartTime,ip.projectContacts,ip.projectcontactsTel,
        DATE_FORMAT(ip.insuranceEndTime,'%Y-%m-%d') insuranceEndTime,ip.insuranceAmount,ip.premium,iai.source,ii.name
        from ins_all_info iai  JOIN ins_project ip on  ip.id = iai.projectId
        join ins_applicant_history ia on ia.id = iai.applicant
        join ins_beneficiary ib on ib.id = iai.beneficiary
        join ic_insurer ii on ii.sysUserId = iai.insuranceCompanyId
        where iai.giveupStatus is not null
        <if test="id ==0">
            and iai.userId = 2
        </if>
        <if test="id != null and id != ''">
            and iai.insuranceCompanyId = #{id}
        </if>
        <if test="orderNumber != null and orderNumber != ''">
            and iai.orderNumber = #{orderNumber}
        </if>
        order by iai.createTime desc
    </select>
</mapper>
