package com.stylefeng.guns.modular.beneficiary.service.impl;

import com.stylefeng.guns.modular.beneficiary.model.Beneficiary;
import com.stylefeng.guns.modular.beneficiary.dao.BeneficiaryMapper;
import com.stylefeng.guns.modular.beneficiary.service.IBeneficiaryService;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 受益人信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-10-07
 */
@Service
public class BeneficiaryServiceImpl extends ServiceImpl<BeneficiaryMapper, Beneficiary> implements IBeneficiaryService {
	@Resource
	private BeneficiaryMapper beneficiaryMapper;

	@Override
	public Integer addBeneficiaryInfo(Beneficiary beneficiary) {
		return beneficiaryMapper.addBeneficiaryInfo(beneficiary);
	}

	@Override
	public Integer upBeneficiaryInfo(Beneficiary beneficiary) {
		return beneficiaryMapper.upBeneficiaryInfo(beneficiary);
	}

}
