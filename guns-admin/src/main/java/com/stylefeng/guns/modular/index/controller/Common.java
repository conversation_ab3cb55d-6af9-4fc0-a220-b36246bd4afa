package com.stylefeng.guns.modular.index.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;

import com.stylefeng.guns.modular.product.model.Product;
import com.stylefeng.guns.modular.product.service.IProductService;

public class Common {
	
	@Autowired
    private static IProductService productService;

	
	public static void GetProduct(Model model){
		
		//获取产品
        List<Product> product = productService.getProductList();
        model.addAttribute("product", product);
	}
}
