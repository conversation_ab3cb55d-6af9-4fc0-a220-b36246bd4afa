<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.icinformation.dao.InsurerBackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.icinformation.model.InsurerBack">
        <id column="id" property="id" />
        <result column="HSTSEQNUM" property="hstseqnum" />
        <result column="IAcctNo" property="iAcctNo" />
        <result column="ApplyName" property="applyName" />
        <result column="InAmount" property="inAmount" />
        <result column="BackSeqNum" property="backSeqNum" />
        <result column="backReason" property="backReason" />
        <result column="backStatus" property="backStatus" />
        <result column="createTime" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, HSTSEQNUM, IAcctNo, ApplyName, InAmount, BackSeqNum, backReason, createTime
    </sql>

    <select id="getInsurerBackList" parameterType="com.baomidou.mybatisplus.plugins.Page" resultType="java.util.HashMap">
       SELECT iai.id, iai.platformCode, iai.source, iai.orderNumber, iai.insurancePolicyNo, ib.backStatus, ib.backReason
       from ins_all_info iai
       join ins_back ib on iai.orderNumber = ib.HSTSEQNUM and iai.insurancePolicyNo = BackSeqNum
       <where>
            <if test="id ==0">
                and iai.userId = 2
            </if>
            <if test="id != null and id != ''">
                and iai.insuranceCompanyId = #{id}
            </if>
            <if test="insurancePolicyNo != null and insurancePolicyNo != ''">
                and iai.insurancePolicyNo = #{insurancePolicyNo}
            </if>
       </where>
       order by iai.createTime desc
    </select>

    <select id="selectByOrderNumber" resultMap="BaseResultMap" parameterType="java.lang.String">
        select ib.HSTSEQNUM, ib.IAcctNo, ib.ApplyName, ib.InAmount, ib.BackSeqNum, ib.backReason, ib.createTime, ib.backStatus
        from ins_all_info iai
        join ins_back ib on iai.orderNumber = ib.HSTSEQNUM and iai.insurancePolicyNo = ib.BackSeqNum
        where ib.HSTSEQNUM = #{orderNumber}
    </select>

    <insert id="addBackInfo" useGeneratedKeys="true" keyProperty="id">
        insert into ins_back
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hstseqnum != null">HSTSEQNUM,</if>
            <if test="iAcctNo != null">IAcctNo,</if>
            <if test="applyName != null">ApplyName,</if>
            <if test="inAmount != null">InAmount,</if>
            <if test="backSeqNum != null">BackSeqNum,</if>
            <if test="backReason != null">backReason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="backStatus != null">backStatus,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hstseqnum != null">#{hstseqnum},</if>
            <if test="iAcctNo != null">#{iAcctNo},</if>
            <if test="applyName != null">#{applyName},</if>
            <if test="inAmount != null">#{inAmount},</if>
            <if test="backSeqNum != null">#{backSeqNum},</if>
            <if test="backReason != null">#{backReason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="backStatus != null">#{backStatus},</if>
        </trim>
    </insert>

    <!--退保申请-->
    <update id="updateBackStatus">
        update ins_back set backStatus = #{backStatus}
        where HSTSEQNUM = #{orderNumber} and BackSeqNum = #{insurancePolicyNo}
    </update>
</mapper>
