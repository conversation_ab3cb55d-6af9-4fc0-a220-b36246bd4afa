package com.stylefeng.guns.modular.invoice.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.stylefeng.guns.core.base.controller.BaseController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import com.stylefeng.guns.core.log.LogObjectHolder;
import org.springframework.web.bind.annotation.RequestParam;

import com.stylefeng.guns.modular.applicant.model.ApplicantHistory;
import com.stylefeng.guns.modular.applicant.service.IApplicantService;
import com.stylefeng.guns.modular.inquiryPolicy.model.Project;
import com.stylefeng.guns.modular.inquiryPolicy.service.IProjectService;
import com.stylefeng.guns.modular.insInfo.model.AllInfo;
import com.stylefeng.guns.modular.insInfo.service.IAllInfoService;
import com.stylefeng.guns.modular.invoice.model.InvoiceInfo;
import com.stylefeng.guns.modular.invoice.service.IInvoiceInfoService;

/**
 * 发票信息控制器
 *
 * <AUTHOR>
 * @Date 2018-10-09 14:48:26
 */
@Controller
@RequestMapping("/invoiceInfo")
public class InvoiceInfoController extends BaseController {

    private String PREFIX = "/invoice/invoiceInfo/";

    @Autowired
    private IInvoiceInfoService invoiceInfoService;
    @Autowired
    private IAllInfoService allInfoService;
    @Autowired
    private IProjectService projectService;
    @Autowired
    private IApplicantService applicantService;

    /**
     * 跳转到发票信息首页
     */
    @RequestMapping("")
    public String index() {
        return PREFIX + "invoiceInfo.html";
    }

    /**
     * 跳转到添加发票信息
     */
    @RequestMapping("/invoiceInfo_add")
    public String invoiceInfoAdd() {
        return PREFIX + "invoiceInfo_add.html";
    }

    /**
     * 跳转到修改发票信息
     */
    @RequestMapping("/invoiceInfo_update/{invoiceInfoId}")
    public String invoiceInfoUpdate(@PathVariable Integer invoiceInfoId, Model model) {
        InvoiceInfo invoiceInfo = invoiceInfoService.selectById(invoiceInfoId);
        model.addAttribute("item",invoiceInfo);
        LogObjectHolder.me().set(invoiceInfo);
        return PREFIX + "invoiceInfo_edit.html";
    }

    /**
     * 获取发票信息列表
     */
    @RequestMapping(value = "/list")
    @ResponseBody
    public Object list(String condition) {
        return invoiceInfoService.selectList(null);
    }

    /**
     * 新增发票信息
     */
    @RequestMapping(value = "/add")
    @ResponseBody
    public Object add(InvoiceInfo invoiceInfo) {
        invoiceInfoService.insert(invoiceInfo);
        return SUCCESS_TIP;
    }

    /**
     * 删除发票信息
     */
    @RequestMapping(value = "/delete")
    @ResponseBody
    public Object delete(@RequestParam Integer invoiceInfoId) {
        invoiceInfoService.deleteById(invoiceInfoId);
        return SUCCESS_TIP;
    }
    
    /**
     * 保险公司获取发票接口
     */
    @RequestMapping(value = "/getInvoiceByPolicyNum",method = RequestMethod.GET)
    @ResponseBody
    public Object getInvoiceByPolicyNum (HttpServletRequest request, HttpServletResponse response){
    	String insurancePolicyNo = request.getParameter("insurancePolicyNo");
    	Wrapper<AllInfo> ao = new EntityWrapper<AllInfo>();
    	ao.eq("insurancePolicyNo",insurancePolicyNo);
    	AllInfo a = allInfoService.selectOne(ao);
    	
    	Integer projectId = a.getProjectId();//项目ID
    	String applicant = a.getApplicant();//公司ID
    	
    	Project p = projectService.selectById(projectId);
    	ApplicantHistory ah = applicantService.selectApplicantHistory(Integer.valueOf(applicant));
    	
    	String projectContacts = p.getProjectContacts();
    	String projectcontactsTel = p.getProjectcontactsTel();
    	String projectEmail = p.getProjectEmail();
    	
    	String unitName = ah.getUnitName();
    	String cardNum = ah.getCardNum();
    	String unitAdd = ah.getUnitAdd();
    	
    	Map<String,Object> map = new HashMap<String,Object>();
    	map.put("projectContacts", projectContacts);
    	map.put("projectcontactsTel", projectcontactsTel);
    	map.put("projectEmail", projectEmail);
    	map.put("unitName", unitName);
    	map.put("cardNum", cardNum);
    	map.put("unitAdd", unitAdd);
    	
    	String  param= JSON.toJSONString(map);
    	System.out.println(param);
		return param;
    	
    }

    /**
     * 修改发票信息
     */
    @RequestMapping(value = "/update")
    @ResponseBody
    public Object update(InvoiceInfo invoiceInfo) {
        invoiceInfoService.updateById(invoiceInfo);
        return SUCCESS_TIP;
    }

    /**
     * 发票信息详情
     */
    @RequestMapping(value = "/detail/{invoiceInfoId}")
    @ResponseBody
    public Object detail(@PathVariable("invoiceInfoId") Integer invoiceInfoId) {
        return invoiceInfoService.selectById(invoiceInfoId);
    }
}
