<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.insurancedemand.dao.InsuranceDemandMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.insurancedemand.model.InsuranceDemand">
        <id column="id" property="id" />
        <result column="projectid" property="projectid" />
        <result column="demandCode" property="demandCode" />
        <result column="isDeleted" property="isDeleted" />
        <result column="createTime" property="createTime" />
        <result column="modifiedTime" property="modifiedTime" />
        <result column="createUser" property="createUser" />
        <result column="modifiedUser" property="modifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, projectid, demandCode, isDeleted, createTime, modifiedTime, createUser, modifiedUser
    </sql>



</mapper>
