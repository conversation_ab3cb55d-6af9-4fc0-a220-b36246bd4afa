package com.stylefeng.guns.modular.index.service.lnzj.v1;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stylefeng.guns.modular.icinformation.dao.InsurerBackMapper;
import com.stylefeng.guns.modular.icinformation.model.InsurerBack;
import com.stylefeng.guns.modular.index.controller.schedule.service.EmailNotifyTaskService;
import com.stylefeng.guns.modular.index.vo.LnzjInvoiceApplyVO;
import com.stylefeng.guns.modular.inquiryPolicy.dao.ProjectMapper;
import com.stylefeng.guns.modular.invoice.model.InvoiceInfo;
import com.stylefeng.guns.modular.index.util.lnzj.ParamSortUtil;
import com.stylefeng.guns.modular.index.util.lnzj.SmUtils;
import com.stylefeng.guns.modular.invoice.dao.InvoiceInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * @Authoer: huoruidong
 * @Description: 发票申请
 * @Date: 2023/3/16 14:29
 **/
@Service
public class InvoiceApplyService {

    @Autowired
    private InvoiceInfoMapper invoiceInfoMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Autowired
    private InsurerBackMapper insurerBackMapper;
    @Autowired
    private EmailNotifyTaskService emailNotifyTaskService;

    @Value("${lnzj.appsecret}")
    public String appSecret ;

    public JSONObject invoiceApply(LnzjInvoiceApplyVO lnzjInvoiceApplyVO, String version) throws Exception {
        System.out.println("发票申请请求参数："+ JSON.toJSONString(lnzjInvoiceApplyVO));
        JSONObject resJson = new JSONObject();
        //签名校验
        boolean verifyResult = verify(lnzjInvoiceApplyVO);
        if(!verifyResult){
            resJson.put("code", "0");
            resJson.put("message", "验签失败");
            return resJson;
        }
        InvoiceInfo existInvoiceInfo = invoiceInfoMapper.selectByOrderAndPolicyNo(lnzjInvoiceApplyVO.getApplyno(), lnzjInvoiceApplyVO.getBaohanno());
        if(null != existInvoiceInfo){
            resJson.put("code", "0");
            resJson.put("message", "该保函号已申请开票，请勿重复申请");
            return resJson;
        }

        InsurerBack insurerBack = insurerBackMapper.selectByOrderNumber(lnzjInvoiceApplyVO.getApplyno());
        if(null != insurerBack){
            resJson.put("code", "0");
            resJson.put("message", "已退保");
            return resJson;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        InvoiceInfo invoiceInfo = new InvoiceInfo();
        invoiceInfo.setCreateTime(sdf.parse(lnzjInvoiceApplyVO.getTimestamp()));//请求时间
        invoiceInfo.setOrderNumber(lnzjInvoiceApplyVO.getApplyno());//业务流水号
        invoiceInfo.setInsurancePolicyNo(lnzjInvoiceApplyVO.getBaohanno());//保函编号
        invoiceInfo.setCompanyName(lnzjInvoiceApplyVO.getBiddername());//投标企业名称
        invoiceInfo.setDutyParagraph(lnzjInvoiceApplyVO.getBiddercode());//统一社会信用代码
        invoiceInfo.setAmount(new BigDecimal(lnzjInvoiceApplyVO.getAmount()));//开票金额
        invoiceInfo.setInvoiceType(Integer.parseInt(lnzjInvoiceApplyVO.getType()));//发票类型
        invoiceInfo.setCompanyRegAdd(lnzjInvoiceApplyVO.getAddress());//公司地址
        invoiceInfo.setCompanyPhone(lnzjInvoiceApplyVO.getPhone());//公司电话
        invoiceInfo.setBankName(lnzjInvoiceApplyVO.getBank());//开户行
        invoiceInfo.setBankCard(lnzjInvoiceApplyVO.getAccount());//账号
        invoiceInfo.setInvoiceEmail(lnzjInvoiceApplyVO.getEmail());//接收电子发票的email
        invoiceInfo.setInvoicePhone(lnzjInvoiceApplyVO.getMobile());//联系电话
        invoiceInfoMapper.addInvoice(invoiceInfo);

        //申请开票 更新发票状态
        projectMapper.updateInvoiceInfo(lnzjInvoiceApplyVO.getApplyno(), lnzjInvoiceApplyVO.getBaohanno());

        resJson.put("code", "1");
        resJson.put("message", "申请成功");

        //邮件通知
        emailNotifyTaskService.emailNotifyTask("invoiceApply");
        return resJson;
    }

    /**
     * 拼装参数
     * @param vo
     * @return
     */
    private Map<String, String> sealRequest(LnzjInvoiceApplyVO vo){
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("appkey", vo.getAppkey());
        dataMap.put("timestamp", vo.getTimestamp());//请求时间
        dataMap.put("applyno", vo.getApplyno());//业务流水号
        dataMap.put("baohanno", vo.getBaohanno());//保函编号
        dataMap.put("biddername", vo.getBiddername());//投标企业名称
        dataMap.put("biddercode", vo.getBiddercode());//统一社会信用代码
        dataMap.put("amount", vo.getAmount());//开票金额
        dataMap.put("type", vo.getType());//发票类型
        dataMap.put("address", vo.getAddress());//公司地址
        dataMap.put("phone", vo.getPhone());//公司电话
        dataMap.put("bank", vo.getBank());//开户行
        dataMap.put("account", vo.getAccount());//账号
        dataMap.put("email", vo.getEmail());//接收电子发票的email
        dataMap.put("mobile", vo.getMobile());//联系电话
        return dataMap;
    }

    /**
     * 验签
     * @param lnzjInvoiceApplyVO
     * @return
     */
    private boolean verify(LnzjInvoiceApplyVO lnzjInvoiceApplyVO) throws Exception {
        Map<String, String> dataMap = sealRequest(lnzjInvoiceApplyVO);
        StringBuilder srcData = ParamSortUtil.asciiSortByKey(dataMap, "sign", "&", false, true);
        srcData.append("appsecret="+appSecret);
        String sign = SmUtils.createSign(srcData.toString());
        return sign.equals(lnzjInvoiceApplyVO.getSign());
    }
}
