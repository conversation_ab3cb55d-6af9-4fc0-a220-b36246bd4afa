package com.stylefeng.guns.modular.index.service.lnzj.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stylefeng.guns.core.common.constant.Const;
import com.stylefeng.guns.core.util.UUIDUtils;
import com.stylefeng.guns.modular.applicant.dao.ApplicantMapper;
import com.stylefeng.guns.modular.applicant.model.ApplicantHistory;
import com.stylefeng.guns.modular.beneficiary.dao.BeneficiaryMapper;
import com.stylefeng.guns.modular.beneficiary.model.Beneficiary;
import com.stylefeng.guns.modular.icinformation.service.IInsurerService;
import com.stylefeng.guns.modular.index.controller.schedule.service.EmailNotifyTaskService;
import com.stylefeng.guns.modular.index.util.lnzj.ParamSortUtil;
import com.stylefeng.guns.modular.index.util.lnzj.SmUtils;
import com.stylefeng.guns.modular.index.vo.LnzjTbsqVO;
import com.stylefeng.guns.modular.inquiryPolicy.dao.ProjectMapper;
import com.stylefeng.guns.modular.inquiryPolicy.model.Project;
import com.stylefeng.guns.modular.insInfo.dao.AllInfoMapper;
import com.stylefeng.guns.modular.insInfo.model.AllInfo;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Authoer: huoruidong
 * @Description: 投保申请
 * @Date: 2023/3/16 14:29
 **/
@Service
public class ToubApplyV2Service {

    @Resource
    private ApplicantMapper applicantMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private AllInfoMapper allInfoMapper;
    @Resource
    private IInsurerService insurerService;
    @Resource
    private BeneficiaryMapper beneficiaryMapper;
    @Autowired
    private EmailNotifyTaskService emailNotifyTaskService;


    @Value("${lnzjv2.appsecret}")
    public String appSecret ;
    @Value("${lnzjv2.pfxFilePath}")
    public String pfxFilePath ;//密钥文件地址
    @Value("${lnzjv2.pfxPasswd}")
    public String pfxPasswd ;//pfx密码
    @Value("${lnzjv2.redirectUrl}")
    public String redirectUrl ;//服务器地址


    public JSONObject touBaoApply(LnzjTbsqVO lnzjTbsqVO, String version) throws Exception {
        System.out.println("投保申请请求参数："+ JSON.toJSONString(lnzjTbsqVO));
        JSONObject resJson = new JSONObject();

        //签名校验
        boolean verifyResult = verify(lnzjTbsqVO);
        if(!verifyResult){
            resJson.put("code", "0");
            resJson.put("message", "验签失败");
            return resJson;
        }
        AllInfo allInfor = allInfoMapper.selectByOrderAndPolicyNo(lnzjTbsqVO.getApplyno(), null);
        if(null != allInfor){
            resJson.put("code", "0");
            resJson.put("message", "已申请投保，请勿重复申请");
            return resJson;
        }

        //解密参数
        decryptParams(lnzjTbsqVO);
        System.out.println("投保申请解密参数："+ JSON.toJSONString(lnzjTbsqVO));

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //项目信息
        Project project = new Project();
        project.setFileNumber(lnzjTbsqVO.getBiaoduanno());//标段编号
        project.setBids(lnzjTbsqVO.getBiaoduanno());//标段编号
        project.setInsuranceAmount(new BigDecimal(lnzjTbsqVO.getBzjamount()));//保证金金额
        BigDecimal cost = new BigDecimal(lnzjTbsqVO.getBzjamount()).multiply(new BigDecimal(lnzjTbsqVO.getRate()));//保险金额 * 0.007
        if(cost.compareTo(new BigDecimal("500"))<0){
            cost = new BigDecimal("500");
        }
        project.setPremium(cost);//保费
        project.setProName(lnzjTbsqVO.getBiaoduanname());//标段名称
        project.setProCode(lnzjTbsqVO.getZbrorgnum());//招标人统一社会信用代码
        project.setProAdd(lnzjTbsqVO.getZbraddress());//招标人地址
        project.setBidTermValidity(Integer.parseInt(lnzjTbsqVO.getTbyxq()));//投标有效期
        project.setKbDate(format.parse(lnzjTbsqVO.getKaibiaodate()));//开标时间
        project.setBidDate(format.parse(lnzjTbsqVO.getKaibiaodate()));//投标时间、开标时间

        //行政区划代码分割
        String province = lnzjTbsqVO.getXiaqucode().substring(0, 2);
        String city = lnzjTbsqVO.getXiaqucode().substring(2, 4);
        String county = lnzjTbsqVO.getXiaqucode().substring(4, 6);

        project.setProProvince(Integer.parseInt(province));
        project.setProCity(Integer.parseInt(city));
        project.setProCounty(Integer.parseInt(county));
        project.setXzCode(lnzjTbsqVO.getXiaqucode());//招标项目行政区域代码
        project.setNoticeUrl(lnzjTbsqVO.getTendernoticeurl());//招标公告地址
        project.setGuaranteeProtocol(lnzjTbsqVO.getGuaranteeprotocourl());//机构或者银行担保协议
        project.setNoticePublishDate(format.parse(lnzjTbsqVO.getNoticepublishdate()));//招标公告发布时间
        project.setActRate(lnzjTbsqVO.getRate());//费率
        project.setActAmount(new BigDecimal(lnzjTbsqVO.getAmount()));//实际支付费用
        projectMapper.addProjectInfo(project);
        Integer proId = project.getId();

        //保单的投保人信息
        ApplicantHistory applicantHistory = new ApplicantHistory();
        applicantHistory.setUnitName(lnzjTbsqVO.getBiddername());//投标企业名称
        applicantHistory.setUnitCode(lnzjTbsqVO.getBiddercode());//统一社会信用代码
        applicantHistory.setUnitUserName(lnzjTbsqVO.getBidderfrname());//投标企业法人姓名
        applicantHistory.setCardNum(lnzjTbsqVO.getBidderfrid());//投标企业法人身份证
        applicantHistory.setUnitEmail(lnzjTbsqVO.getBiddermail());//投标企业邮箱
        applicantHistory.setContactsTel(lnzjTbsqVO.getBiddercontacttel());//投标企业联系电话
        applicantHistory.setUnitAdd(lnzjTbsqVO.getBidderaddress());//投标企业地址
        applicantHistory.setBasicUserName(lnzjTbsqVO.getBasicaccountname());//基本户户名
        applicantHistory.setBankAccount(lnzjTbsqVO.getBasicaccountno());//基本户账号
        applicantHistory.setBasicAccountBank(lnzjTbsqVO.getBasicaccountbankname());//基本户开户行
        applicantHistory.setPayLimitation(lnzjTbsqVO.getPayLimitation());//赔付时效
        applicantHistory.setCaPubKey(lnzjTbsqVO.getBidderpublickey());//投标单位CA公钥
        applicantHistory.setIdcardZurl(lnzjTbsqVO.getBidderfrzmurl());//购函单位法人身份证正面url
        applicantHistory.setIdcardBurl(lnzjTbsqVO.getBidderfrbmurl());//购函单位法人身份证背面url
        applicantHistory.setLicenseFile(lnzjTbsqVO.getBiddercodeurl());//营业执照url
        applicantHistory.setContacts(lnzjTbsqVO.getManagername());//经办人
        applicantHistory.setContactCardZUrl(lnzjTbsqVO.getManageridcardzurl());//经办人身份证号正面
        applicantHistory.setContactCardBUrl(lnzjTbsqVO.getManageridcardfurl());//经办人身份证号反面
        applicantHistory.setContactIdCard(lnzjTbsqVO.getManageridcard());//经办人身份证号
        applicantMapper.addApplicantHistory(applicantHistory);
        String appId = applicantHistory.getId();

        Beneficiary beneficiary = new Beneficiary();
        beneficiary.setBeneficiaryName(lnzjTbsqVO.getZbr());//招标人
        beneficiary.setBeneficiaryCardNum(lnzjTbsqVO.getZbrorgnum());//统一社会信用代码
        beneficiary.setBeneficiaryAdd(lnzjTbsqVO.getZbraddress());//招标人地址
        beneficiaryMapper.addBeneficiaryInfo(beneficiary);
        Integer beId = beneficiary.getId();

        //保函所有信息关联
        AllInfo allInfo = new AllInfo();
        allInfo.setId(UUIDUtils.getUUID());
        allInfo.setCreateTime(format.parse(lnzjTbsqVO.getTimestamp()));//请求时间
        allInfo.setOrderNumber(lnzjTbsqVO.getApplyno());//业务流水号
        allInfo.setApplicant(appId);//添加申请人
        allInfo.setProjectId(proId);//添加项目信息ID
        allInfo.setBeneficiary(beId);//添加受益人信息ID
        allInfo.setProductId(Const.CONST_FOUR);
        allInfo.setState(Const.CONST_TWO);//设置默认状态
        allInfo.setServiceType(Const.CONST_ONE);//服务类型
        allInfo.setInsuranceCompanyId(50058);//保险公司id
        //获取投保单号
        String applicationNo = insurerService.getApplicationNo();
        allInfo.setInsuranceApplicationNo(applicationNo);//投保单号
        allInfo.setInsuranceApplicationNoTime(new Date());//投保单号生成时间
        if(version.equals("v1")){
            allInfo.setPlatformCode("LNZJ");
            allInfo.setSource(Const.LNZJ_TOKEN);//保函来源
        }else{
            allInfo.setPlatformCode("LNZJV2");
            allInfo.setSource(Const.LNZJV2_TOKEN);//保函来源
        }
        allInfoMapper.addAllInfo(allInfo);

        //响应成功
        resJson.put("code", "1");
        resJson.put("message", "success");
        JSONObject bodyJson = new JSONObject();
        bodyJson.put("redirecturl", redirectUrl+"/lnzjapi/v2/touBsuccess/"+lnzjTbsqVO.getApplyno());
        resJson.put("data", bodyJson);

        //邮件通知
        emailNotifyTaskService.emailNotifyTask("guaranteeApply");
        return resJson;
    }

    /**
     * 解密参数
     * @param lnzjTbsqVO
     */
    private void decryptParams(LnzjTbsqVO lnzjTbsqVO) throws Exception {

        //私钥证书
        BCECPrivateKey privateCert = SmUtils.getPrivateCert(pfxFilePath, pfxPasswd);
        //解密参数
        lnzjTbsqVO.setBiddername(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBiddername()));//投标企业名称
        lnzjTbsqVO.setBiddercode(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBiddercode()));//统一社会信用代码
        lnzjTbsqVO.setBidderfrname(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBidderfrname()));//投标企业法人姓名
        lnzjTbsqVO.setBidderfrid(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBidderfrid()));//投标企业法人身份证
        lnzjTbsqVO.setRate(lnzjTbsqVO.getRate());//费率
        if(StringUtils.isNotBlank(lnzjTbsqVO.getAmount())){
            lnzjTbsqVO.setAmount(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getAmount()));//实际支付费用
        }
        lnzjTbsqVO.setBiddermail(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBiddermail()));//投标企业邮箱
        lnzjTbsqVO.setBiddercontacttel(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBiddercontacttel()));//投标企业联系电话
        lnzjTbsqVO.setBidderaddress(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBidderaddress()));//投标企业地址
        lnzjTbsqVO.setBiaoduanno(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBiaoduanno()));//标段编号
        lnzjTbsqVO.setBiaoduanname(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBiaoduanname()));//标段名称
        lnzjTbsqVO.setZbr(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getZbr()));//招标人
        lnzjTbsqVO.setZbrorgnum(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getZbrorgnum()));//招标人统一社会信用代码
        lnzjTbsqVO.setZbraddress(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getZbraddress()));//招标人地址
        lnzjTbsqVO.setKaibiaodate(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getKaibiaodate()));//开标时间
        if(StringUtils.isNotBlank(lnzjTbsqVO.getBasicaccountname())){
            lnzjTbsqVO.setBasicaccountname(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBasicaccountname()));//基本户户名
        }
        if(StringUtils.isNotBlank(lnzjTbsqVO.getBasicaccountno())){
            lnzjTbsqVO.setBasicaccountno(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBasicaccountno()));//基本户账号
        }
        if(StringUtils.isNotBlank(lnzjTbsqVO.getBasicaccountbankname())){
            lnzjTbsqVO.setBasicaccountbankname(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBasicaccountbankname()));//基本户开户行
        }
        if(StringUtils.isNotBlank(lnzjTbsqVO.getBidderfrzmurl())){
            lnzjTbsqVO.setBidderfrzmurl(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBidderfrzmurl()));//购函单位法人身份证正面url
        }
        if(StringUtils.isNotBlank(lnzjTbsqVO.getBidderfrbmurl())){
            lnzjTbsqVO.setBidderfrbmurl(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBidderfrbmurl()));//购函单位法人身份证背面url
        }
        if(StringUtils.isNotBlank(lnzjTbsqVO.getBiddercodeurl())){
            lnzjTbsqVO.setBiddercodeurl(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getBiddercodeurl()));//营业执照 url
        }
        lnzjTbsqVO.setManagername(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getManagername()));//经办人
        if(StringUtils.isNotBlank(lnzjTbsqVO.getManageridcardzurl())){
            lnzjTbsqVO.setManageridcardzurl(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getManageridcardzurl()));//经办人身份证号正面
        }
        if(StringUtils.isNotBlank(lnzjTbsqVO.getManageridcardfurl())){
            lnzjTbsqVO.setManageridcardfurl(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getManageridcardfurl()));//经办人身份证号反面
        }
        lnzjTbsqVO.setManageridcard(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getManageridcard()));//经办人身份证号
        if(StringUtils.isNotBlank(lnzjTbsqVO.getTendernoticeurl())){
            lnzjTbsqVO.setTendernoticeurl(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getTendernoticeurl()));//招标公告地址
        }
        if(StringUtils.isNotBlank(lnzjTbsqVO.getGuaranteeprotocourl())){
            lnzjTbsqVO.setGuaranteeprotocourl(SmUtils.sm2Decode(privateCert, lnzjTbsqVO.getGuaranteeprotocourl()));//机构或者银行担保协议
        }
    }

    /**
     * 参数拼装
     * @param vo
     * @return
     */
    private Map<String, String> sealRequest(LnzjTbsqVO vo){
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("appkey", vo.getAppkey());
        dataMap.put("timestamp", vo.getTimestamp());//请求时间
        dataMap.put("applyno", vo.getApplyno());//业务流水号
        dataMap.put("biddername", vo.getBiddername());//投标企业名称
        dataMap.put("biddercode", vo.getBiddercode());//统一社会信用代码
        dataMap.put("bidderfrname", vo.getBidderfrname());//投标企业法人姓名
        dataMap.put("bidderfrid", vo.getBidderfrid());//投标企业法人身份证
        dataMap.put("biddermail", vo.getBiddermail());//投标企业邮箱
        dataMap.put("biddercontacttel", vo.getBiddercontacttel());//投标企业联系电话
        dataMap.put("bidderaddress", vo.getBidderaddress());//投标企业地址
        dataMap.put("biaoduanno", vo.getBiaoduanno());//标段编号
        dataMap.put("biaoduanname", vo.getBiaoduanname());//标段名称
        dataMap.put("bzjamount", vo.getBzjamount());//保证金金额
        dataMap.put("zbr", vo.getZbr());//招标人
        dataMap.put("zbrorgnum", vo.getZbrorgnum());//招标人统一社会信用代码
        dataMap.put("zbraddress", vo.getZbraddress());//招标人地址
        dataMap.put("tbyxq", vo.getTbyxq());//投标有效期
        dataMap.put("basicaccountname", vo.getBasicaccountname());//基本户户名
        dataMap.put("basicaccountno", vo.getBasicaccountno());//基本户账号
        dataMap.put("basicaccountbankname", vo.getBasicaccountbankname());//基本户开户行
        dataMap.put("kaibiaodate", vo.getKaibiaodate());//开标时间
        dataMap.put("xiaqucode", vo.getXiaqucode());//招标项目行政区域代码
        dataMap.put("payLimitation", vo.getPayLimitation());//赔付时效
        dataMap.put("bidderpublickey", vo.getBidderpublickey());//投标单位CA公钥
        dataMap.put("bidderfrzmurl", vo.getBidderfrzmurl());//购函单位法人身份证正面url
        dataMap.put("bidderfrbmurl", vo.getBidderfrbmurl());//购函单位法人身份证背面url
        dataMap.put("biddercodeurl", vo.getBiddercodeurl());//营业执照 url
        dataMap.put("managername", vo.getManagername());//经办人
        dataMap.put("manageridcardzurl", vo.getManageridcardzurl());//经办人身份证号正面
        dataMap.put("manageridcardfurl", vo.getManageridcardfurl());//经办人身份证号反面
        dataMap.put("manageridcard", vo.getManageridcard());//经办人身份证号
        dataMap.put("tendernoticeurl", vo.getTendernoticeurl());//招标公告地址
        dataMap.put("guaranteeprotocourl", vo.getGuaranteeprotocourl());//机构或者银行担保协议
        dataMap.put("noticepublishdate", vo.getNoticepublishdate());//招标公告发布时间
        dataMap.put("rate", vo.getRate());//费率
        dataMap.put("amount", vo.getAmount());//实际支付费用
        return dataMap;
    }

    /**
     * 验签
     * @param lnzjTbsqVO
     * @return
     */
    private boolean verify(LnzjTbsqVO lnzjTbsqVO) throws Exception {
        Map<String, String> dataMap = sealRequest(lnzjTbsqVO);
        StringBuilder srcData = ParamSortUtil.asciiSortByKey(dataMap, "sign", "&", false, true);
        srcData.append("appsecret="+appSecret);
        String sign = SmUtils.createSign(srcData.toString());
        return sign.equals(lnzjTbsqVO.getSign());
    }

}
