<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.collectAppliAndPro.dao.AppliAndProMapper">

<update id="updateUrl" parameterType="string" >
    update ins_all_info set url = #{imgFile} where id = #{id}
</update>
<select id="getAppliNo" parameterType="string" resultType="map">
    select iai.insuranceApplicationNo,iai.insurancePolicyNo,ip.bidDate,ip.insuranceEndTime,
     (select notifyUrl from notifyurl where id = 1) notifyUrl
    from ins_all_info iai
    join ins_project ip on iai.projectId=ip.id
    where iai.id =#{id}
</select>
    <update id="updNotifyUrl" parameterType="string" >
        update notifyurl set notifyUrl = #{notifyUrl} where id = 1
    </update>
    <update id="updbhcsNotifyUrl" parameterType="string" >
        update notifyurl set notifyUrl = #{notifyUrl} where id = 99
    </update>
    <select id="getNotifyUrl" parameterType="int" resultType="string">
    select notifyUrl from notifyurl where id = #{id}
    </select>
    <select id="getnotifyUrl" parameterType="string" resultType="string">
    select notifyUrl from ins_all_info where insurancePolicyNo = #{policyNo}
    </select>
    <select id="getnotifyurl" parameterType="string" resultType="string">
    select notifyUrl from ins_all_info where orderNumber = #{orderNumber}
    </select>
    <select id="getId" parameterType="string" resultType="map">
    select count(id) id,insuranceApplicationNo from ins_all_info
     where id =#{id} and source = #{token}
     <if test="orderNumber!=null and orderNumber!=''">
        and orderNumber = #{orderNumber}
     </if>
    </select>
    <select id="getInsId" parameterType="int" resultType="int">
    select id from ic_insurer where sysUserId = #{id}
    </select>
    <update id="updateId" parameterType="string" >
        update ins_all_info
         set id = concat(id,#{updateId}) ,
          orderNumber = concat(orderNumber,#{updateId})
         where id = #{id} and source = #{source}
    </update>
    <select id="getPremiu" parameterType="string"  resultType="BigDecimal">
        select premiu from codepremiu where code = #{code}
    </select>
    <select id="getPremius" parameterType="string"  resultType="BigDecimal">
        select premiu from codepremiu where code = #{code} and platform = #{source}
    </select>
    <select id="getOrgtap" parameterType="string"  resultType="int">
        select sysUserId from ic_insurer where orgtap = #{orgtap}
    </select>
    <select id="getAppliNoByState"   resultType="map">
        select insuranceApplicationNo  applicationNo
        from ins_all_info
        where state=4
        and insuranceApplicationNo is not null
        and insurancePolicyNo is null
        and eGuaranteeUrl is null
        and pcPaymentLink is not null
        and insuranceApplicationNoTime >=(NOW() - interval 10 MINUTE)
    </select>
    <select id="getInfo" parameterType="string" resultType="map">
        select
         iai.eGuaranteeUrl,
         iai.ePolicyUrl,
         iai.insuranceApplicationNo applicationNo,
         iai.insurancePolicyNo policyNo,
         iai.source
         from ins_all_info iai
        where
             iai.insuranceApplicationNo = #{applicationNo}
            or iai.insurancePolicyNo = #{applicationNo}
    </select>
    <update id="updateTag" parameterType="string">
        update ins_all_info set tag = #{tag} where insurancePolicyNo = #{policyNo} and tag = 0
    </update>
    <update id="updateTab" >
        update ins_all_info set tab = #{tab} where insuranceApplicationNo = #{applicationNo}
    </update>
    <select id="getTag" parameterType="Integer" resultType="map">
        select
         iai.eGuaranteeUrl,
         iai.insuranceApplicationNo applicationNo,
         iai.tab,
         iai.insurancePolicyNo policyNo,
        iai.source
         from ins_all_info iai
         where tag =0
        <if test="tab !=0">
            and iai.tab =#{tab}
        </if>
        and insurancePolicyNoTime >=(NOW() - interval 21 MINUTE)

    </select>
    <select id="getType" parameterType="string" resultType="int">
        select
        serviceType
         from ins_all_info
         where
         insurancePolicyNo = #{policyNo}
    </select>
</mapper>
