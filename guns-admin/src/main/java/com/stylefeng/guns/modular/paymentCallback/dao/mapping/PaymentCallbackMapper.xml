<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.paymentCallback.dao.PaymentCallbackMapper">

    <!-- 通用查询映射结果 -->

    <!-- 根据投保单号批量更新保单号 wz20181031  暂时不启用-->
    <update id="updateParam" parameterType="com.stylefeng.guns.modular.paymentCallback.model.PaymentCallbackDto" >
            update ins_all_info
            <trim prefix="set" suffixOverrides=",">
                <trim prefix="insurancePolicyNo =case" suffix="end,">
                    <foreach collection="paymentCallbackDto.tbresultList" item="item" index="index">
                        <if test="item.policyNo !=null and item.policyNo != ''">
                            when insuranceApplicationNo=#{item.proposalNo} then #{item.policyNo}
                        </if>
                    </foreach>
                </trim>
                <trim prefix="insurancePolicyNoTime =case" suffix="end,">
                    <foreach collection="paymentCallbackDto.tbresultList" item="item" index="index">
                        <if test="item.policyNo !=null and item.policyNo != ''">
                            when insuranceApplicationNo=#{item.proposalNo} then now()
                        </if>
                    </foreach>
                </trim>
                <trim prefix="state =case" suffix="end,">
                    <foreach collection="paymentCallbackDto.tbresultList" item="item" index="index">
                        <if test="item.policyNo !=null and item.policyNo != ''">
                            when insuranceApplicationNo=#{item.proposalNo} then 6
                        </if>
                    </foreach>
                </trim>
            </trim>
        where insuranceApplicationNo in (
        <foreach collection="paymentCallbackDto.tbresultList" item="item" index="index" open="" close="" separator=",">
            #{item.proposalNo}
        </foreach>
        )
    </update>
    <!--暂时不启用-->
    <insert id="insertParam" parameterType="com.stylefeng.guns.modular.paymentCallback.model.PaymentCallbackDto" >
        INSERT INTO ins_payment_history (jobName,sysflag,transCode,policyNo,importSN,premium,amountUp,premiumUp,isSuccess,proposalNo)
        values <foreach collection="paymentCallbackDto.tbresultList" item="tbresultList" separator="," >
                (#{paymentCallbackDto.jobName},#{paymentCallbackDto.xmlHead.sysflag},#{paymentCallbackDto.xmlHead.transCode},#{tbresultList.policyNo},#{tbresultList.importSN},#{tbresultList.premium},#{tbresultList.amountUp},#{tbresultList.premiumUp},#{tbresultList.isSuccess},#{tbresultList.proposalNo})
                </foreach>
    </insert>
    <insert id="insert" parameterType="com.stylefeng.guns.modular.paymentCallback.model.PaymentCallbackDto" >
        INSERT INTO ins_payment_history
        (result,applicationNo,mesaage,policyNo)
        values  (#{result},#{applicationNo},#{mesaage},#{policyNo})
    </insert>
    <update id="update" parameterType="com.stylefeng.guns.modular.paymentCallback.model.PaymentCallbackDto" >
        update
            ins_all_info
        set
            insurancePolicyNo = #{policyNo},
            state = 6,
            insurancePolicyNoTime = now()
        where
            insuranceApplicationNo = #{applicationNo}
    </update>
    <update id="updateeGuaranteeUrl" parameterType="com.stylefeng.guns.modular.paymentCallback.model.PaymentCallbackDto" >
        update
            ins_all_info
        set
            eGuaranteeUrl = #{eGuaranteeUrl}
        where
            insuranceApplicationNo = #{applicationNo}
    </update>
    <update id="updateGuaranteeUrl" parameterType="string" >
        update
            ins_all_info
        set
            eGuaranteeUrl = #{eGuaranteeUrl}
        where
            insurancePolicyNo = #{policyNo}
    </update>

    <update id="updateePolicyUrl" parameterType="com.stylefeng.guns.modular.paymentCallback.model.PaymentCallbackDto" >
        update
            ins_all_info
        set
            ePolicyUrl = #{ePolicyUrl}
        where
            insuranceApplicationNo = #{applicationNo}
    </update>
<select id="selectByApplicationNo" parameterType="java.lang.String" resultType="java.util.HashMap" >
     select
      *
     from  ins_all_info
     where
     insuranceApplicationNo = #{applicationNo}
</select>
<select id="getallInfoId" parameterType="java.lang.String" resultType="map" >
     select
      iai.id,iai.orderNumber,ip.bidDate,ip.insuranceEndTime,iai.source
     from  ins_all_info iai
     join ins_project ip on iai.projectId=ip.id
     where
     iai.insuranceApplicationNo = #{applicationNo}
</select>

    <select id="getSource" parameterType="string" resultType="string">
select source from ins_all_info where insuranceApplicationNo = #{applicationNo}
    </select>
    <update id="savePayCallBackInfo" parameterType="com.stylefeng.guns.modular.paymentCallback.model.Request" >
        update
            ins_all_info
        set
            insurancePolicyNo = #{policyNo},
            state = 6,
            ePolicyUrl = #{epolicyUrl},
            eGuaranteeUrl = #{eguranteeUrl},
            insurancePolicyNoTime = now()
        where
            orderNumber = #{serialNo}
    </update>
    <update id="saveorderCallBackInfo" parameterType="java.lang.String" >
        update
            ins_all_info
        set
            insuranceApplicationNo = #{proposalNo},
            insurancePolicyNo = #{policyNo},
            state = 6,
            ePolicyUrl = #{elecPolicyUrl},
            eGuaranteeUrl = #{elecLetterUrl},
            insurancePolicyNoTime = now()
        where
        orderNumber = #{orderNo}
    </update>
    <update id="saveccicPayCallBackInfo" parameterType="java.lang.String" >
        update
            ins_all_info
        set
            insurancePolicyNo = #{policyNo},
            state = 6,
            ePolicyUrl = #{elecPolicyUrl},
            eGuaranteeUrl = #{elecLetterUrl},
            insurancePolicyNoTime = now()
        where
            insuranceApplicationNo = #{proposalNo}
    </update>
    <update id="saveYonganPayCallBackInfo" parameterType="java.lang.String" >
        update
            ins_all_info
        set
            insurancePolicyNo = #{policyNo},
            state = 6,
            eGuaranteeUrl = #{downLoadUrl},
            insurancePolicyNoTime = now()
        where
            insuranceApplicationNo = #{proposalNo}
    </update>
    <select id="selectById" parameterType="string" resultType="string" >
        select insuranceApplicationNo from ins_all_info where orderNumber = #{id}
    </select>
    <select id="selectByorderNumber" parameterType="string" resultType="string" >
        select id from ins_all_info where orderNumber = #{orderNumber}
    </select>
</mapper>
