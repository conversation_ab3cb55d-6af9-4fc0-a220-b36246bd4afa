package com.stylefeng.guns.modular.index.service.zszh.v1;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stylefeng.guns.core.util.HttpUtils;
import com.stylefeng.guns.modular.index.vo.zszhVo.ZszhBaohanCallBack;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;

public class ZszhBaoHanCallBackApplyService {

    @Value("${zszh.appsecret}")
    public String appSecret ;
    @Value("${zszh.notifyUrl}")
    public String notifyUrl ;//回调地址
    @Value("${zszh.environment}")
    public String environment ;

    public JSONObject paySuccessService(ZszhBaohanCallBack zszhBaohanCallBack) throws Exception {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("appKey", zszhBaohanCallBack.getPlatformCode());//平台的标识
        dataMap.put("applyNo", zszhBaohanCallBack.getOrderNumber());//订单编号
        dataMap.put("guaranteeNo", zszhBaohanCallBack.getOrderNumber());//保函单号
        dataMap.put("fileType", zszhBaohanCallBack.getFileType());//文件类型
        dataMap.put("fileUrl", zszhBaohanCallBack.getFileUrl());//保函下载地址
        dataMap.put("fileUrl2", zszhBaohanCallBack.getFileUrl2());//保单下载地址
        dataMap.put("effectiveDate", zszhBaohanCallBack.getEffectiveDate());//保函起期
        dataMap.put("expiryDate", zszhBaohanCallBack.getExpiryDate());//保函止期
        dataMap.put("payAccountName", zszhBaohanCallBack.getPayAccountName());//企业基本户账户
        dataMap.put("payAccountNumber", zszhBaohanCallBack.getPayAccountNumber());//支付保费银行账户号码

        Map<String, String> headMap = new HashMap<>();
        headMap.put("Content-Type", "application/json");

        HttpResponse response = HttpUtils.doPost(notifyUrl,getPath(), null, headMap, null, JSON.toJSONString(dataMap));
        String resStr = EntityUtils.toString(response.getEntity());
        JSONObject responseJson = JSON.parseObject(resStr);
        System.out.println("保函结果通知响应数据："+zszhBaohanCallBack.getOrderNumber()+"::"+responseJson);

        return responseJson;
    }

    private String getPath(){
        String path = "/payment/notify";//测试地址
        if(environment.equals("prd")){
            path = "/payment/notify";
        }
        return path;
    }
}
