package com.stylefeng.guns.modular.index.model;

import com.stylefeng.guns.core.util.Signature;
import com.stylefeng.guns.core.util.UUIDUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 请求天安支付参数
 */
public class RequestHead {
    private String cooperation = "yibaohan";
    private String sign ;
    private String tradeNo = cooperation+ UUIDUtils.getUUID().substring(0,6);
    private String tradeDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format( new Date());
    private String nonce = Signature.create_nonce_str();
    private String timestamp = Signature.create_timestamp();

    public String getCooperation() {
        return cooperation;
    }

    public void setCooperation(String cooperation) {
        this.cooperation = cooperation;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(String tradeDate) {
        this.tradeDate = tradeDate;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "{" +
                "cooperation='" + cooperation + '\'' +
                ", sign='" + sign + '\'' +
                ", tradeNo='" + tradeNo + '\'' +
                ", tradeDate='" + tradeDate + '\'' +
                ", nonce='" + nonce + '\'' +
                ", timestamp='" + timestamp + '\'' +
                '}';
    }
}
