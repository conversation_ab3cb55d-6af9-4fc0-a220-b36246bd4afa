<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.invoice.dao.InvoiceInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.invoice.model.InvoiceInfo">
        <id column="id" property="id" />
        <result column="orderNumber" property="orderNumber" />
        <result column="insurancePolicyNo" property="insurancePolicyNo" />
        <result column="companyName" property="companyName" />
        <result column="dutyParagraph" property="dutyParagraph" />
        <result column="companyRegAdd" property="companyRegAdd" />
        <result column="companyPhone" property="companyPhone" />
        <result column="openingBankAdd" property="openingBankAdd" />
        <result column="bankName" property="bankName" />
        <result column="bankCard" property="bankCard" />
        <result column="amount" property="amount" />
        <result column="taxCert" property="taxCert" />
        <result column="taxpayer" property="taxpayer" />
        <result column="bankOpenCert" property="bankOpenCert" />
        <result column="invoiceType" property="invoiceType" />
        <result column="invoiceLinkman" property="invoiceLinkman" />
        <result column="invoiceEmail" property="invoiceEmail" />
        <result column="invoicePhone" property="invoicePhone" />
        <result column="createTime" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, orderNumber, insurancePolicyNo, companyName, dutyParagraph, companyRegAdd, companyPhone, openingBankAdd, bankName, bankCard, amount, taxCert, taxpayer, bankOpenCert, invoiceType, invoiceLinkman, invoiceEmail, invoicePhone, createTime
    </sql>
    
    <insert id="addInvoice" useGeneratedKeys="true" keyProperty="id">
      insert into pro_invoice_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNumber != null">orderNumber,</if>
            <if test="insurancePolicyNo != null">insurancePolicyNo,</if>
            <if test="companyName != null">companyName,</if>
            <if test="dutyParagraph != null">dutyParagraph,</if>
            <if test="companyRegAdd != null">companyRegAdd,</if>
            <if test="companyPhone != null">companyPhone,</if>
            <if test="openingBankAdd != null">openingBankAdd,</if>
            <if test="bankName != null">bankName,</if>
            <if test="bankCard != null">bankCard,</if>
            <if test="amount != null">amount,</if>
            <if test="taxCert != null">taxCert,</if>
            <if test="taxpayer != null">taxpayer,</if>
            <if test="bankOpenCert != null">bankOpenCert,</if>
            <if test="invoiceType != null">invoiceType,</if>
            <if test="invoiceLinkman != null">invoiceLinkman,</if>
            <if test="invoiceEmail != null">invoiceEmail,</if>
            <if test="invoicePhone != null">invoicePhone,</if>
            <if test="createTime != null">createTime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="insurancePolicyNo != null">#{insurancePolicyNo},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="dutyParagraph != null">#{dutyParagraph},</if>
            <if test="companyRegAdd != null">#{companyRegAdd},</if>
            <if test="companyPhone != null">#{companyPhone},</if>
            <if test="openingBankAdd != null">#{openingBankAdd},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="bankCard != null">#{bankCard},</if>
            <if test="amount != null">#{amount},</if>
            <if test="taxCert != null">#{taxCert},</if>
            <if test="taxpayer != null">#{taxpayer},</if>
            <if test="bankOpenCert != null">#{bankOpenCert},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="invoiceLinkman != null">#{invoiceLinkman},</if>
            <if test="invoiceEmail != null">#{invoiceEmail},</if>
            <if test="invoicePhone != null">#{invoicePhone},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>

    </insert>

    <update id="upInvoice">
        update  pro_invoice_info
        set companyName=#{companyName}, dutyParagraph=#{dutyParagraph}, companyRegAdd=#{companyRegAdd},companyPhone=#{companyPhone},
        openingBankAdd=#{openingBankAdd}, bankName=#{bankName}, bankCard=#{bankCard}, taxCert=#{taxCert}, taxpayer=#{taxpayer},
        bankOpenCert=#{bankOpenCert}, invoiceType=#{invoiceType}
        where id=#{id}
    </update>

    <select id="getInvoiceInfo" resultType="com.stylefeng.guns.modular.invoice.model.InvoiceInfo">
    	select id, companyName, dutyParagraph, companyRegAdd, companyPhone, openingBankAdd, bankName, bankCard, taxCert, taxpayer, bankOpenCert, invoiceType
    	from pro_invoice_info
    	where
    	id = #{id}
    </select>
    <update id="updateInvoice" parameterType="string" >
        update ins_all_info
        set invoiceurl = #{url}
        where    insurancePolicyNo = #{insurancePolicyNo}
    </update>
    <select id="getInvoice" parameterType="string" resultType="string" >
        select invoiceurl from ins_all_info where insurancePolicyNo = #{insurancePolicyNo}
    </select>
    
    <update id="updinvoice" parameterType="string" >
		update ins_all_info set invoiceurl = #{downLoadUrl} where  insurancePolicyNo = #{policyNo}
	</update>

    <select id="selectByOrderAndPolicyNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from pro_invoice_info
        where orderNumber = #{orderNumber}
        and insurancePolicyNo = #{insurancePolicyNo}
    </select>
    
</mapper>
