<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.insInfo.dao.AllInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.insInfo.model.AllInfo">
        <id column="id" property="id" />
        <result column="platformCode" property="platformCode" />
        <result column="applicant" property="applicant" />
        <result column="beneficiary" property="beneficiary" />
        <result column="projectId" property="projectId" />
        <result column="insuranceCompanyId" property="insuranceCompanyId" />
        <result column="insuranceCompanyArea" property="insuranceCompanyArea" />
        <result column="productId" property="productId" />
        <result column="state" property="state" />
        <result column="genStatus" property="genStatus" />
        <result column="userId" property="userId" />
        <result column="serviceType" property="serviceType" />
        <result column="orderNumber" property="orderNumber" />
        <result column="insuranceApplicationNo" property="insuranceApplicationNo" />
        <result column="insuranceApplicationNoTime" property="insuranceApplicationNoTime" />
        <result column="insurancePolicyNo" property="insurancePolicyNo" />
        <result column="insurancePolicyNoTime" property="insurancePolicyNoTime" />
        <result column="createTime" property="createTime" />
        <result column="eGuaranteeUrl" property="eGuaranteeUrl" />
        <result column="ePolicyUrl" property="ePolicyUrl" />
        <result column="url" property="url" />
        <result column="isRead" property="isRead" />
        <result column="source" property="source" />
        <result column="pcPaymentLink" property="pcPaymentLink" />
        <result column="notifyUrl" property="notifyUrl" />
        <result column="invoiceFlag" property="invoiceFlag" />
        <result column="invoiceurl" property="invoiceurl" />
        <result column="areaCode" property="areaCode" />
		<result column="tag" property="tag" />
		<result column="tab" property="tab" />
		<result column="imageFile" property="imageFile" />
		<result column="wtUrl" property="wtUrl" />
		<result column="confirmPayment" property="confirmPayment" />
		<result column="giveupStatus" property="giveupStatus" />
		<result column="isRetryApply" property="isRetryApply" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, platformCode, applicant, beneficiary, projectId, insuranceCompanyId, insuranceCompanyArea, productId, `state`, genStatus, userId, serviceType, orderNumber,
        insuranceApplicationNo, insuranceApplicationNoTime, insurancePolicyNo, insurancePolicyNoTime, createTime, eGuaranteeUrl, ePolicyUrl, url, isRead,
        `source`, pcPaymentLink, notifyUrl, invoiceFlag, invoiceurl, areaCode, tag, tab, imageFile, wtUrl, confirmPayment, giveupStatus, isRetryApply
    </sql>

    <insert id="addAllInfo" useGeneratedKeys="true" keyProperty="id">
    	insert into ins_all_info
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="platformCode != null">platformCode,</if>
			<if test="applicant != null">applicant,</if>
			<if test="beneficiary != null">beneficiary,</if>
			<if test="projectId != null">projectId,</if>
			<if test="insuranceCompanyId != null">insuranceCompanyId,</if>
			<if test="insuranceCompanyArea != null">insuranceCompanyArea,</if>
			<if test="productId != null">productId,</if>
			<if test="state != null">`state`,</if>
			<if test="genStatus != null">genStatus,</if>
			<if test="userId != null">userId,</if>
			<if test="serviceType != null">serviceType,</if>
			<if test="orderNumber != null">orderNumber,</if>
			<if test="insuranceApplicationNo != null">insuranceApplicationNo,</if>
			<if test="insuranceApplicationNoTime != null">insuranceApplicationNoTime,</if>
			<if test="insurancePolicyNo != null">insurancePolicyNo,</if>
			<if test="insurancePolicyNoTime != null">insurancePolicyNoTime,</if>
			<if test="createTime != null">createTime,</if>
			<if test="eGuaranteeUrl != null">eGuaranteeUrl,</if>
			<if test="ePolicyUrl != null">ePolicyUrl,</if>
			<if test="url != null">url,</if>
			<if test="isRead != null">isRead,</if>
			<if test="source != null">`source`,</if>
			<if test="pcPaymentLink != null">pcPaymentLink,</if>
			<if test="notifyUrl != null">notifyUrl,</if>
			<if test="invoiceFlag != null">invoiceFlag,</if>
			<if test="invoiceurl != null">invoiceurl,</if>
			<if test="areaCode != null">areaCode,</if>
			<if test="tag != null">tag,</if>
			<if test="tab != null">tab,</if>
			<if test="imageFile != null">imageFile,</if>
			<if test="wtUrl != null">wtUrl,</if>
			<if test="confirmPayment != null">confirmPayment,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="platformCode != null">#{platformCode},</if>
			<if test="applicant != null">#{applicant},</if>
			<if test="beneficiary != null">#{beneficiary},</if>
			<if test="projectId != null">#{projectId},</if>
			<if test="insuranceCompanyId != null">#{insuranceCompanyId},</if>
			<if test="insuranceCompanyArea != null">#{insuranceCompanyArea},</if>
			<if test="productId != null">#{productId},</if>
			<if test="state != null">#{state},</if>
			<if test="genStatus != null">#{genStatus},</if>
			<if test="userId != null">#{userId},</if>
			<if test="serviceType != null">#{serviceType},</if>
			<if test="orderNumber != null">#{orderNumber},</if>
			<if test="insuranceApplicationNo != null">#{insuranceApplicationNo},</if>
			<if test="insuranceApplicationNoTime != null">#{insuranceApplicationNoTime},</if>
			<if test="insurancePolicyNo != null">#{insurancePolicyNo},</if>
			<if test="insurancePolicyNoTime != null">#{insurancePolicyNoTime},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="eGuaranteeUrl != null">#{eGuaranteeUrl},</if>
			<if test="ePolicyUrl != null">#{ePolicyUrl},</if>
			<if test="url != null">#{url},</if>
			<if test="isRead != null">#{isRead},</if>
			<if test="source != null">#{source},</if>
			<if test="pcPaymentLink != null">#{pcPaymentLink},</if>
			<if test="notifyUrl != null">#{notifyUrl},</if>
			<if test="invoiceFlag != null">#{invoiceFlag},</if>
			<if test="invoiceurl != null">#{invoiceurl},</if>
			<if test="areaCode != null">#{areaCode},</if>
			<if test="tag != null">#{tag},</if>
			<if test="tab != null">#{tab},</if>
			<if test="imageFile != null">#{imageFile},</if>
			<if test="wtUrl != null">#{wtUrl},</if>
			<if test="confirmPayment != null">#{confirmPayment},</if>
		</trim>
    </insert>
    <update id="upAllInfo" >
    	update ins_all_info set  insuranceCompanyId=#{insuranceCompanyId},serviceType=#{serviceType},imageFile=#{imageFile},isRead = 0
    	where id=#{id}
		<if test="source!=null and source!=''">
			and source=#{source}
		</if>
    </update>

    <select id="getAllInfoById" resultType="java.util.HashMap">
		SELECT
		  iai.id,
		  iai.orderNumber,
		  iai.insurancePolicyNo,
		  iai.serviceType,
		  iai.state,
		  iai.applicant,
		  iai.beneficiary,
		  iai.insuranceCompanyId,
		  iai.projectId,
		  iai.productId,
		  iai.source,
		  iai.imageFile,
		  (select outId from sys_user where id = iai.userId) outId,
		  ia.uid,
		  ia.unitName,
		  ia.cardType,
		  ia.cardNum,
		  ia.unitPhone,
		  (SELECT simplename FROM sys_dept WHERE id=ia.unitProvince)unitProvince,
		  (SELECT simplename FROM sys_dept WHERE id=ia.unitCity) unitCity,
		  (SELECT simplename FROM sys_dept WHERE id=ia.unitCounty) unitCounty,
		  ia.unitAdd,
		  ia.contacts,
		  ia.contactsTel,
		  ia.email,
		  ia.licenseFile,
		  ia.grade,
		  ia.gradeFile,
		  ia.credit,
		  ia.isForeignCompany,
		  ia.addressP,
		  ia.addressC,
		  ia.getBankLists,
		  (SELECT simplename FROM sys_dept WHERE id=ia.addressP) addressPname,
		  (SELECT simplename FROM sys_dept WHERE id=ia.addressC) addressCname,
		  (SELECT name FROM sys_dict WHERE code = ia.getBankLists AND pid = ( SELECT id FROM sys_dict WHERE CODE = 'sys_bank' )) getBankListsname,
		  ia.userName,
		  ib.beneficiaryName,
		  ib.beneficiaryCardType,
		  ib.beneficiaryCardNum,
		  ib.cardTime,
		  ib.beneficiaryProvince bproid,
		  ib.beneficiaryCity bcityid,
		  ib.beneficiaryCounty bcountid,
		  (SELECT simplename FROM sys_dept WHERE id=ib.beneficiaryProvince) beneficiaryProvince,
		  (SELECT simplename FROM sys_dept WHERE id=ib.beneficiaryCity) beneficiaryCity,
		  (SELECT simplename FROM sys_dept WHERE id=ib.beneficiaryCounty) beneficiaryCounty,
		  ib.beneficiaryAdd,
		  ib.beneficiaryContacts,
		  ib.beneficiaryContactPhone,
		  ib.beneficiaryContactTel,
		  ip.agency,
		  ip.proName,
		  ip.bids,
		  ip.fileNumber,
		  ip.proType,
		  DATE_FORMAT(ip.bidDate,'%Y-%m-%d')bidDate,
		  ip.bidTermValidity,
		  ip.plannedTime,
		  ip.proProvince ppid,
		  ip.proCity pcityid,
		  ip.proCounty pcountyid,
		  (SELECT simplename FROM sys_dept WHERE id=ip.proProvince) proProvince,
		  (SELECT simplename FROM sys_dept WHERE id=ip.proCity) proCity,
		  (SELECT simplename FROM sys_dept WHERE id=ip.proCounty) proCounty,
		  ip.proAdd,
		  ip.tcp,
		  ip.insuranceStartTime,
		  ip.insuranceEndTime,
		  ip.insuranceAmount,
		  ip.premium,
		  ip.jurisdiction,
		  ip.dispute,
		  ip.sendInfo,
		  ip.invoice ,
		  ip.isBasicPay,
		  ip.tenderDocument,
		  ip.projectContacts,
		  ip.projectcontactsTel,
		  ip.projectEmail
		FROM
		  ins_all_info iai,
		  ins_applicant_history ia,
		  ins_beneficiary ib,
		  ins_project ip
		WHERE iai.id = #{id}
		  AND ia.id = iai.applicant
		  AND ib.id = iai.beneficiary
		  AND ip.id = iai.projectId
    </select>
	<select id="getstate" parameterType="string" resultType="int">
		select state from ins_all_info where id=#{id}
	</select>
	<select id="getPayCallbackInfoById" resultType="java.util.HashMap">
		SELECT
		  iai.orderNumber processId, iai.insurancePolicyNo, iai.platformCode,
		  ia.unitName companyName, ia.cardNum companySocialNo, ia.unitCode, ia.basicAccountBank payBank, ia.bankAccount payAccount,
		  ip.premium cost
		FROM
		  ins_all_info iai,
		  ins_applicant_history ia,
		  ins_project ip
		WHERE iai.id = #{id}
		AND ia.id = iai.applicant
		AND ip.id = iai.projectId
    </select>

	<select id="getPayCallbackInfoByIdOnlyYC" resultType="java.util.HashMap">
		SELECT
			iai.orderNumber processId,
			ia.unitName companyName,
			ia.cardNum companySocialNo,
			ia.basicAccountBank payBank,
			ia.bankAccount payAccount,
			ip.premium cost
		FROM
			ins_all_info iai,
			ins_applicant_history ia,
			ins_project ip
		WHERE iai.id = #{id}
		  AND ia.id = iai.applicant
		  AND ip.id = iai.projectId
	</select>

	<update id="paycallbackstate" parameterType="java.lang.String" >
        update
            ins_all_info
        set
            state = 6
        where
            orderNumber = #{orderNumber}
    </update>
	<select id="getCount" parameterType="string" resultType="int">
		select count(id) id from ins_all_info
		where orderNumber =#{orderNumber} and insuranceApplicationNo is not null
	</select>
	<select id="getcbInfo" parameterType="string" resultType="java.util.Map">
		select id,insurancePolicyNo,eGuaranteeUrl
		from ins_all_info
		where orderNumber =#{processId} and insuranceApplicationNo =#{applyPolicyNo} and state>5
	</select>
	<select id="getinvoiceInfo" parameterType="string" resultType="java.util.Map">
		select id,applicant,invoiceurl,invoiceFlag
		from ins_all_info
		where orderNumber =#{processId} and insurancePolicyNo =#{policyNo} and state>5
	</select>
	<select id="getNoById" parameterType="java.lang.String" resultType="com.stylefeng.guns.modular.insInfo.model.AllInfo" >
		select insuranceApplicationNo,insurancePolicyNo,state,eGuaranteeUrl,ePolicyUrl  from ins_all_info where id = #{allInfoId} and source=#{source}
	</select>
	<select id="getServiceType" parameterType="string" resultType="int" >
		select serviceType from ins_all_info WHERE insurancePolicyNo = #{insurancePolicyNo}
	</select>
	<select id="getInvoice" parameterType="java.lang.String" resultType="java.util.Map" >
	select iah.cardNum,iah.unitName,iai.insuranceCompanyArea,ip.projectEmail,ip.proAdd,ip.premium,iai.insuranceCompanyArea
	from ins_all_info iai
	join ins_applicant_history iah on iai.applicant = 	iah.id
	join ins_project ip on iai.projectId = ip.id
	where insurancePolicyNo = #{insurancePolicyNo}
</select>
	<select id="getManageCom" resultType="map">
		select manageCom,mainRiskCode,certifyCode,sysFlag,transCode from ins_sign_city where provinceCode =#{provinceCode} and type = #{serviceType} and isDeleted = 0
	</select>
	<update id="insertPICCPayUrl" >
       update ins_all_info set
       insuranceApplicationNo = #{insuranceApplicationNo},
       insuranceApplicationNoTime = now(),
       pcPaymentLink = #{pcPaymentLink},
       state = 4,
       isRead = 0
        where
        orderNumber = #{orderNumber}
    </update>

	<update id="updateGenStatus">
		update ins_all_info set genStatus = 4 where orderNumber = #{orderNumber}
	</update>

	<!--根据流水号查询保函信息-->
	<select id="selectByOrderNumber" parameterType="java.lang.String" resultType="com.stylefeng.guns.modular.index.po.AllInfoPO">
		select iai.platformCode, iai.applicant, iai.orderNumber, iai.insurancePolicyNo, iai.insurancePolicyNoTime, iai.eGuaranteeUrl, iai.state, iai.giveupStatus, iai.invoiceFlag, iai.invoiceurl,
		       ip.insuranceAmount, ip.insuranceStartTime, ip.insuranceEndTime, ip.premium, ip.actRate, ip.actAmount,
		       iah.unitName, iah.caPubKey, iah.unitCode,
		       ib.beneficiaryBankAccount, ib.beneficiaryBankCode, ib.beneficiaryBankOpenBank, ib.beneficiaryContacts, ib.beneficiaryContactPhone, ib.beneficiaryCardNum
		from ins_all_info iai
		inner join ins_project ip on iai.projectId = ip.id
		inner join ins_applicant_history iah on iai.applicant = iah.id
		inner join ins_beneficiary ib on iai.beneficiary = ib.id
		where orderNumber = #{orderNumber}
	</select>

	<!--根据流水号查询保函信息-->
	<select id="selectByAllInfoId" parameterType="java.lang.String" resultType="com.stylefeng.guns.modular.index.po.AllInfoPO">
		select iai.platformCode, iai.applicant, iai.projectId, iai.orderNumber, iai.insurancePolicyNo, iai.insurancePolicyNoTime, iai.eGuaranteeUrl, iai.state, iai.giveupStatus, iai.invoiceFlag, iai.invoiceurl,
			   ip.insuranceAmount, ip.insuranceStartTime, ip.insuranceEndTime, ip.premium, ip.bidTermValidity, ip.actRate, ip.actAmount,
			   iah.unitName, iah.caPubKey, iah.unitCode, iah.bankAccount, iah.basicAccountBank, iah.basicUserName,
			   ib.beneficiaryBankAccount, ib.beneficiaryBankCode, ib.beneficiaryBankOpenBank, ib.beneficiaryContacts, ib.beneficiaryContactPhone, ib.beneficiaryCardNum
		from ins_all_info iai
		inner join ins_project ip on iai.projectId = ip.id
		inner join ins_applicant_history iah on iai.applicant = iah.id
		inner join ins_beneficiary ib on iai.beneficiary = ib.id
		where iai.id = #{id}
	</select>

	<!--放弃申请-->
	<update id="updateGiveupStatus">
		update ins_all_info set giveupStatus = #{giveupStatus}
        where orderNumber = #{orderNumber}
	</update>


	<select id="selectGiveupApply" resultMap="BaseResultMap">
		select <include refid="Base_Column_List"/>
		from ins_all_info where giveupStatus = '0'
	</select>

	<select id="selectRetryApply" resultMap="BaseResultMap">
		select <include refid="Base_Column_List"/>
        from ins_all_info where isRetryApply = '0'
	</select>

	<!--重新申请-->
	<update id="retryApply">
		update ins_all_info set isRetryApply = #{isRetryApply}
		where orderNumber = #{orderNumber}
	</update>

	<select id="selectByOrderAndPolicyNo" parameterType="string" resultMap="BaseResultMap">
		select <include refid="Base_Column_List"/>
		from ins_all_info
		where orderNumber =#{orderNumber}
		<if test="insurancePolicyNo != null">
			and insurancePolicyNo =#{insurancePolicyNo}
		</if>
	</select>

	<!--是否存在退保-->
	<select id="notExistBack" parameterType="string" resultMap="BaseResultMap">
		select iai.*
		from ins_all_info iai
		left join ins_back ib on iai.orderNumber = ib.HSTSEQNUM and iai.insurancePolicyNo = ib.BackSeqNum
		where iai.orderNumber =#{orderNumber}
		and iai.insurancePolicyNo =#{insurancePolicyNo}
		and ib.HSTSEQNUM is null
	</select>

	<!--更新退保状态-->
	<update id="updateBackStatus">
		update ins_all_info set backStatus = #{backStatus} where orderNumber =#{orderNumber}
		<if test="insurancePolicyNo != null">
			and insurancePolicyNo =#{insurancePolicyNo}
		</if>
	</update>

	<!--更新理赔状态-->
	<update id="updateClaimsStatus">
		update ins_all_info set claimsStatus = #{claimsStatus} where orderNumber =#{orderNumber}
		<if test="insurancePolicyNo != null">
			and insurancePolicyNo =#{insurancePolicyNo}
		</if>
	</update>

	<select id="isGiveupByOrderNumber" parameterType="String" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List"/>
		from ins_all_info
		where orderNumber = #{orderNumber}
		and giveupStatus is not null
	</select>

	<update id="updateState" parameterType="com.stylefeng.guns.modular.insInfo.model.AllInfo">
		update ins_all_info
		set state = #{state}
		where orderNumber = #{orderNumber}
	</update>
</mapper>
