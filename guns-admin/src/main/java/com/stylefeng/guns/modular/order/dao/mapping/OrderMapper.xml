<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.order.dao.OrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.order.model.Order">
        <id column="id" property="id" />
        <result column="user" property="user" />
        <result column="place" property="place" />
        <result column="goods" property="goods" />
        <result column="createtime" property="createtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user, place, goods, createtime
    </sql>

</mapper>
