package com.stylefeng.guns.modular.insurerinterface.sunshine.service.impl;

import com.stylefeng.guns.modular.collectAppliAndPro.dao.AppliAndProMapper;
import com.stylefeng.guns.modular.insurerinterface.sunshine.Encryption;
import com.stylefeng.guns.modular.insurerinterface.sunshine.service.IbusInvoice;
import com.stylefeng.guns.modular.invoice.dao.InvoiceInfoMapper;
import net.sf.json.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

@Service
public class BusInvoiceImpl implements IbusInvoice {
    protected final Log logger = LogFactory.getLog(getClass());
    @Resource
    private InvoiceInfoMapper invoiceInfoMapper;
    @Resource
    private AppliAndProMapper appliAndProMapper;
    /**
     * 将InputStream转换成byte数组
     * @param in InputStream
     * @return byte[]
     * @throws IOException
     */
    public static byte[] InputStreamTOByte(InputStream in) throws IOException{
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int count = -1;
        while((count = in.read(data,0,1024)) != -1)
            outStream.write(data, 0, count);

        data = null;
        return outStream.toByteArray();
    }

    /**
     * 阳光开电子发票接口通用
     * @param insurancePolicyNo
     * @return
     */
    @Override
    public String serviceCommInvoice(Map map, Map manageCom, String insurancePolicyNo)  {
        String URL = invoiceInfoMapper.getInvoice(insurancePolicyNo);
        String sunShineInvoiceurl = appliAndProMapper.getNotifyUrl(6);
        if (StringUtils.isNotEmpty(URL)){
            return sunShineInvoiceurl+URL;
        }else {
            try{
                String file = "{\n" +
                        "    \"platFormFlag\":\"GRXHKYTS\",\n" +
                        "    \"sysFlag\":\"GRXHKYTS\",\n" +
                        "\t\"transCode\":\"GRXHKYTS\",\n" +
                        "    \"policyList\":[\n" +
                        "        {\n" +
                        "            \"taxPaerType\":\"2\",\n" +
                        "            \"taxPayerNo\":\""+map.get("cardNum")+"\",\n" +
                        "            \"invoicePersonName\":\""+map.get("unitName")+"\",\n" +
                        "            \"policyNo\":\""+insurancePolicyNo+"\",\n" +
                        "            \"invoicePersonIdNo\":\""+map.get("cardNum")+"\",\n" +
                        "            \"invoiceTitle\":\""+map.get("unitName")+"\",\n" +
                        "            \"email\":\""+map.get("projectEmail")+"\",\n" +
                        "\t\t\t\"holderemail\":\"0\",\n" +
                        "\t\t\t\"manageCom\":\""+manageCom.get("manageCom")+"\",\n" +
                        "\t\t\t\"mainRiskCode\":\""+manageCom.get("mainRiskCode")+"\",\n" +
                        "\t\t\t\"premium\":\""+String.valueOf(map.get("premium"))+"\",\n" +
                        "\t\t\t\"certifyCode\":\""+manageCom.get("certifyCode")+"\"\n" +
                        "        }\n" +
                        "    ]\n" +
                        "}\n";
                System.out.println(file);
                String serverName = "GRXHKYTS"; //渠道标识
                String requestMethod = "MAKEINVOICE"; //请求方法名，生成发票写死：MAKEINVOICE
                String policyNo = Encryption.authcodeEncode(insurancePolicyNo, "GRXHKYTSCXBXWFVCH5LK4N1UO76BJE20ZRA3S8");
                String sign = Encryption.authcodeEncode("GRXHKYTSgrxhkyts123"+insurancePolicyNo, "GRXHKYTSCXBXWFVCH5LK4N1UO76BJE20ZRA3S8");
                String urls = sunShineInvoiceurl+"?serverName="+serverName+"&requestMethod="+requestMethod+"&sign="+sign+"&policyNo="+policyNo;
                urls = urls.replaceAll("\n", "");
                sign = sign.replaceAll("\n", "");
                //System.out.println("urls ： " + urls);
                String reStr = httpSendReceiver(urls.trim(),file);//报文位置
                System.out.println("返回报文："+reStr);
                if (StringUtils.isNotEmpty(reStr)){
                    String remark = JSONArray.fromObject(reStr).getJSONObject(0).getString("remark");
                    String status = JSONArray.fromObject(reStr).getJSONObject(0).getString("status");
                    if ("success".equals(status)){
                        requestMethod = "QUERYINVOICE"; //请求方法名，查询发票写死：QUERYINVOICE
                        String param = "?serverName="+serverName+"&requestMethod="+requestMethod+"&sign="+sign+"&policyNo="+policyNo;
                        invoiceInfoMapper.updateInvoice(insurancePolicyNo,param);
                        return sunShineInvoiceurl+param;
                    }else{
                        return remark;
                    }
                }
                return reStr;
            }catch (Exception e){
                e.printStackTrace();
                return "系统异常";
            }
        }
    }
    /**
     * 人保开电子发票接口通用
     * @param insurancePolicyNo
     * @return
     */
    @Override
    public String serviceRBInvoice(Map map, Map manageCom, String insurancePolicyNo)  {
        String URL = invoiceInfoMapper.getInvoice(insurancePolicyNo);
        if (StringUtils.isNotEmpty(URL)){
            return URL;
        }else {
            try{
                String file = "{\n" +
                        "    \"platFormFlag\":\"GRXHRBSQ\",\n" +
                        "    \"sysFlag\":\"GRXHRBSQ\"\n" +
                        "    \"policyList\":\n" +
                        "        {\n" +
                        "            \"taxPaerType\":\"2\",\n" +
                        "            \"taxPayerNo\":\""+map.get("cardNum")+"\",\n" +
                        "            \"invoicetype\":\"1\",\n" +
                        "            \"invoicePersonName\":\""+map.get("unitName")+"\",\n" +
                        "            \"policyNo\":\""+insurancePolicyNo+"\",\n" +
                        "            \"invoicePersonIdNo\":\""+map.get("cardNum")+"\",\n" +
                        "            \"invoicePersonPhone\":\"\",\n" +
                        "            \"invoiceTitle\":\""+map.get("unitName")+"\",\n" +
                        "            \"email\":\"\",\n" +
                        "\t\"premium\":\""+map.get("premium")+"\",\n" +
                        "\t\"taxbank\":\"\",\n" +
                        "\t\"taxbankaccount\":\"\"\n" +
                        "\t\t\t\n" +
                        "        }\n" +
                        "}";
                System.out.println(file);
                String rbInvoiceurl = appliAndProMapper.getNotifyUrl(35);
                String reStr = httpSendReceiver(rbInvoiceurl.trim(),file);//报文位置
                System.out.println("返回报文："+reStr);
                if (StringUtils.isNotEmpty(reStr)){
                    String remark = JSONArray.fromObject(reStr).getJSONObject(0).getString("remark");
                    String status = JSONArray.fromObject(reStr).getJSONObject(0).getString("status");
                    if ("success".equals(status)){
                        String shortLink = JSONArray.fromObject(reStr).getJSONObject(0).getString("shortLink");
                        invoiceInfoMapper.updateInvoice(insurancePolicyNo,shortLink);
                        return shortLink;
                    }else{
                        return remark;
                    }
                }
                return reStr;
            }catch (Exception e){
                e.printStackTrace();
                return "系统异常";
            }
        }
    }
    /**此函数是平台主动发起的http请求,并接收的响应
     * @param url
     * @param
     * @return
     * @throws Exception
     */
    public String httpSendReceiver(String url,String xml) throws Exception {
        String iXMLData = null;
        try {
            logger.info("SendtoHttp->tcpSendReceiver()->后台调http");
            // read file data
//			BufferedReader br = new BufferedReader(new InputStreamReader(
//					new FileInputStream(fileName)));
//			StringBuffer sbf = new StringBuffer();
            String line = "";
//			@SuppressWarnings("unused")
//			int i = 0;
//			for (line = br.readLine(); line != null; line = br.readLine()) {
//				sbf.append(line);
//				i++;
//			}
            // 把报文写出
//			String xml = sbf.toString();

            //System.out.println("发送报文为：-------------------------->"+xml);
            // 建立一个HttpURLConnection
            URL urll = new URL(url.trim());
            HttpURLConnection httpConnection = (HttpURLConnection) urll
                    .openConnection();
            httpConnection.setRequestMethod("POST");
            httpConnection.setDoOutput(true);
            httpConnection.setDoInput(true);
            httpConnection.setAllowUserInteraction(true);
            httpConnection.setRequestProperty("content-type", "text/html");
            httpConnection.connect();
            OutputStream outputStream = httpConnection.getOutputStream();
            outputStream.write(xml.getBytes());
            outputStream.flush();
            // outputStream.close();

            int code = httpConnection.getResponseCode();
            InputStream inputStream = null;
            if (code != HttpURLConnection.HTTP_OK) {
                inputStream = httpConnection.getErrorStream();
            } else {
                inputStream = httpConnection.getInputStream();
            }

            // 接收相应报文，反馈出去

            BufferedInputStream input = null; // 输入流,用于接收请求的数据
            @SuppressWarnings("unused")
            byte[] buffer = new byte[1024]; // 数据缓冲区

            StringBuffer sb = new StringBuffer();
            input = new BufferedInputStream(inputStream);
            BufferedReader br = new BufferedReader(new InputStreamReader(input));
            line = "";
            try {
                for (line = br.readLine(); line != null; line = br.readLine()) {
                    sb.append(line);
                }
            } catch (Exception e) {
                logger.error("短险错误信息: ", e);
            } finally {
                if (input != null) {
                    try {
                        input.close();
                    } catch (Exception f) {
                        logger.error("短险错误信息: ", f);
                    }
                }
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (Exception f) {
                        logger.error("短险错误信息: ", f);
                    }
                }
            }
            sb.append("\n");
            iXMLData = sb.toString();
            httpConnection.disconnect();
        } catch (Exception e) {
            logger.error("-----------连接后台servlet：---" + url
                    + "时,出现连接异常----------");
            logger.error("短险错误信息: ", e);
        }
        return iXMLData;
    }
}
