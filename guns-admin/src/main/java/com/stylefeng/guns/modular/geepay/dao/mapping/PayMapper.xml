<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stylefeng.guns.modular.geepay.dao.PayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.stylefeng.guns.modular.geepay.model.Pay">
        <id column="id" property="id" />
        <result column="HSTSEQNUM" property="hstseqnum" />
        <result column="IAcctNo" property="iAcctNo" />
        <result column="ApplyName" property="applyName" />
        <result column="ApplyPhone" property="applyPhone" />
        <result column="ApplyIdCard" property="applyIdCard" />
        <result column="BenefitPerson" property="benefitPerson" />
        <result column="InAmount" property="inAmount" />
        <result column="InAcctNo" property="inAcctNo" />
        <result column="InName" property="inName" />
        <result column="BankNo" property="bankNo" />
        <result column="BankName" property="bankName" />
        <result column="BackSeqNum" property="backSeqNum" />
        <result column="Reason" property="reason" />
        <result column="FileList" property="fileList" />
        <result column="CreateTime" property="createTime" />
        <result column="claimsStatus" property="claimsStatus" />
        <result column="claimsRemark" property="claimsRemark" />
        <result column="claimsCompanyName" property="claimsCompanyName" />
        <result column="claimsPhone" property="claimsPhone" />
        <result column="claimsAmount" property="claimsAmount" />
        <result column="claimsFileList" property="claimsFileList" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, HSTSEQNUM, IAcctNo, ApplyName, ApplyPhone, ApplyIdCard, BenefitPerson, InAmount, InAcctNo, InName, BankNo, BankName, BackSeqNum, Reason, FileList, CreateTime,
        claimsStatus, claimsRemark, claimsCompanyName, claimsPhone, claimsAmount, claimsFileList
    </sql>

    <select id="selectByOrderAndPolicyNo" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List"/>
        from ins_pay
        where HSTSEQNUM = #{orderNumber} and BackSeqNum = #{insurancePolicyNo}
    </select>

    <insert id="addPayInfo">
        insert into ins_pay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hstseqnum != null">HSTSEQNUM,</if>
            <if test="iAcctNo != null">IAcctNo,</if>
            <if test="applyName != null">ApplyName,</if>
            <if test="applyPhone != null">ApplyPhone,</if>
            <if test="applyIdCard != null">ApplyIdCard,</if>
            <if test="benefitPerson != null">BenefitPerson,</if>
            <if test="inAmount != null">InAmount,</if>
            <if test="inAcctNo != null">InAcctNo,</if>
            <if test="inName != null">InName,</if>
            <if test="bankNo != null">BankNo,</if>
            <if test="bankName != null">BankName,</if>
            <if test="backSeqNum != null">BackSeqNum,</if>
            <if test="reason != null">Reason,</if>
            <if test="fileList != null">FileList,</if>
            <if test="createTime != null">CreateTime,</if>
            <if test="claimsStatus != null">claimsStatus,</if>
            <if test="claimsRemark != null">claimsRemark,</if>
            <if test="claimsCompanyName != null">claimsCompanyName,</if>
            <if test="claimsPhone != null">claimsPhone,</if>
            <if test="claimsAmount != null">claimsAmount,</if>
            <if test="claimsFileList != null">claimsFileList,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hstseqnum != null">#{hstseqnum},</if>
            <if test="iAcctNo != null">#{iAcctNo},</if>
            <if test="applyName != null">#{applyName},</if>
            <if test="applyPhone != null">#{applyPhone},</if>
            <if test="applyIdCard != null">#{applyIdCard},</if>
            <if test="benefitPerson != null">#{benefitPerson},</if>
            <if test="inAmount != null">#{inAmount},</if>
            <if test="inAcctNo != null">#{inAcctNo},</if>
            <if test="inName != null">#{inName},</if>
            <if test="bankNo != null">#{bankNo},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="backSeqNum != null">#{backSeqNum},</if>
            <if test="reason != null">#{reason},</if>
            <if test="fileList != null">#{fileList},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="claimsStatus != null">#{claimsStatus},</if>
            <if test="claimsRemark != null">#{claimsRemark},</if>
            <if test="claimsCompanyName != null">#{claimsCompanyName},</if>
            <if test="claimsPhone != null">#{claimsPhone},</if>
            <if test="claimsAmount != null">#{claimsAmount},</if>
            <if test="claimsFileList != null">#{claimsFileList},</if>
        </trim>
    </insert>
</mapper>
