package com.stylefeng.guns.core.common.exception;

/**
 * 验证码错误异常
 *
 * <AUTHOR>
 * @date 2017-05-05 23:52
 * slm修改，实现验证码验证错误后，登录页保留原用户名和密码信息
 */
public class InvalidKaptchaException extends RuntimeException {
    private String password;
    public InvalidKaptchaException(String password){
        super();
        this.password = password;
    }

    public String getPassword() {
        return password;
    }
}
