@layout("/common/_container.html"){
<div class="row">
    <div class="col-sm-12">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>发票类型管理</h5>
            </div>
            <div class="ibox-content">
                <div class="row row-lg">
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-3">
                                <#NameCon id="condition" name="名称" />
                            </div>
                            <div class="col-sm-3">
                                <#button name="搜索" icon="fa-search" clickFun="InvoiceType.search()"/>
                            </div>
                        </div>
                        <div class="hidden-xs" id="InvoiceTypeTableToolbar" role="group">
                            @if(shiro.hasPermission("/invoiceType/add")){
                                <#button name="添加" icon="fa-plus" clickFun="InvoiceType.openAddInvoiceType()"/>
                            @}
                            @if(shiro.hasPermission("/invoiceType/update")){
                                <#button name="修改" icon="fa-edit" clickFun="InvoiceType.openInvoiceTypeDetail()" space="true"/>
                            @}
                            @if(shiro.hasPermission("/invoiceType/delete")){
                                <#button name="删除" icon="fa-remove" clickFun="InvoiceType.delete()" space="true"/>
                            @}
                        </div>
                        <#table id="InvoiceTypeTable"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="${ctxPath}/static/modular/invoiceType/invoiceType/invoiceType.js"></script>
@}
