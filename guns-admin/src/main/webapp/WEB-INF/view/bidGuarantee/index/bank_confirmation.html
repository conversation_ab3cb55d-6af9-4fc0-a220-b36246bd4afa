@layout("common/_head.html"){
<div class="form_step container">
    <div class="form_step_mini clearfix">
        <dl class="form_state complete_cur">
            <dt><b class="coverline left"></b><span>1</span></dt>
            <dd>填写表单</dd>
        </dl>
        <dl class="form_state cur">
            <dt><span>2</span></dt>
            <dd>确认表单</dd>
        </dl>
        <dl class="form_state">
            <dt><span>3</span></dt>
            <dd>支付保费</dd>
        </dl>
        <dl class="form_state">
            <dt><span>4</span></dt>
            <dd>投保成功</dd>
        </dl>
        <dl class="form_state">
            <dt><span>5</span><b class="coverline right"></b></dt>
            <dd>下载保函</dd>
        </dl>
    </div>
</div>
<div class="order_detail container">
    <h2 class="form_title order_detail_title">项目信息表</h2>
    <div class="item_info">
        <h3 class="form_part_minititle "><span>企业基本信息</span></h3>

        <div class="order_detail_part clearfix">
            <form class="fillin_form" role="form" action="${ctxPath}/insurance/examine" method="post">
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>公司名称：</label>
                    <input readonly="true" value="${allInfo.unitName}" placeholder="北京金润企政信息科技有限公司" type="text">
                </div>
                <!--<div class="item_info_line clearfix">
                <label><b class="red">*</b>外国企业：</label>
                @if(allInfo.isForeignCompany ==1){
                <input class="policy_form_send" type="radio" value="1" name="isForeignCompany" placeholder='no' checked/><label class="policy_form_yes">是</label>
                <input class="policy_form_send" type="radio" value="2" name="isForeignCompany" placeholder='yes'  disabled="disabled"/><label class="policy_form_no">否</label><br>
                @}else{
                <input class="policy_form_send" type="radio" value="1" name="isForeignCompany" placeholder='yes' disabled="disabled"/><label class="policy_form_yes">是</label>
                <input class="policy_form_send" type="radio" value="2" name="isForeignCompany" placeholder='no' checked/><label class="policy_form_no">否</label><br>
                @}
            </div>-->
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>证件类型：</label>
                    @for(cardType in cardType){
                    @if(allInfo.cardType == cardType.id){
                    <input readonly="true" value="${cardType.cardTypeName}">
                    @}
                    @}
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>证件号码：</label>
                    <input readonly="true" value="${allInfo.cardNum}">
                </div>
                <!--<div class="item_info_line clearfix">
                <label><b class="red">*</b>联系电话</label>
                <input readonly="true" value="${allInfo.unitPhone}">
                </div>-->
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>注册地址(住所)：</label>
                    <input readonly="true" value="${allInfo.unitProvince}" class="shortInput">
                    <input readonly="true" value="${allInfo.unitCity}" class="shortInput">
                    <input readonly="true" value="${allInfo.unitCounty}" class="shortInput">
                    <input readonly="true" value="${allInfo.unitAdd}" class="longInput">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>联系人：</label>
                    <input readonly="true" value="${allInfo.contacts}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>联系方式：</label>
                    <input readonly="true" value="${allInfo.contactsTel}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>邮箱：</label>
                    <input readonly="true" value="${allInfo.email}">
                </div>
                <div class="item_info_line item_info_line_wrap clearfix">
                    <label><b class="red">*</b>证件上传：</label>
                    <div class="item_info_img">
                        <img src="${allInfo.licenseFile}" alt="">
                    </div>
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>资质等级：</label>
                    @for(gradeType in gradeType){
                    @if(allInfo.grade == gradeType.id){
                    <input readonly="true" value="${gradeType.gradeTypeName}">
                    @}
                    @}
                </div>
                <div class="item_info_line item_info_line_wrap clearfix">
                    <label>证件上传：</label>
                    <div class="item_info_img">
                        <img src="${allInfo.gradeFile}">
                    </div>
                </div>
                <h3 class="form_part_minititle order_title_mt"><span>开具地</span></h3>
                <div class="item_info_line item_info_line_wrap  clearfix">
                    <label><b class="red">*</b>银行保函开具地：</label>
                    <input class="shortInput" readonly="true" value="${allInfo.addressPname}">
                    <input class="shortInput" readonly="true" value="${allInfo.addressCname}">
                    <input class="shortInput" readonly="true" value="${allInfo.getBankListsname}">
                    <input type="hidden"  id="userName" value="">
                    <div id="userNameshow"><span class="important">友情提示：本保函由<span id="useText">${allInfo.userName}</span>担保公司提供担保。</span></div>
                </div>
                <h3 class="form_part_minititle order_title_mt"><span>项目负责人</span></h3>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>项目联系人：</label>
                    <input type="text" readonly="true" value="${allInfo.projectContacts}" placeholder="项目联系人"/>
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>项目联系方式：</label>
                    <input type="text" readonly="true" value="${allInfo.projectcontactsTel}" placeholder="项目联系方式"/>
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>项目邮箱：</label>
                    <input type="text" readonly="true" value="${allInfo.projectEmail}"  placeholder="项目邮箱" required/>
                </div>
                <h3 class="form_part_minititle order_title_mt"><span>受益人信息(招标人信息)</span></h3>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>受益人名称：</label>
                    <input readonly="true" value="${allInfo.beneficiaryName}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>证件类型：</label>
                    @for(beneficiary in cardType){
                    @if(allInfo.beneficiaryCardType == beneficiary.id){
                    <input readonly="true" value="${beneficiary.cardTypeName}">
                    @}
                    @}
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>证件号码：</label>
                    <input readonly="true" value="${allInfo.beneficiaryCardNum}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>证件有效期</label>
                    <input type="date" readonly="true" value="${allInfo.cardTime}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>联系人：</label>
                    <input readonly="true" value="${allInfo.beneficiaryContacts}">
                </div>
                <!--<div class="item_info_line clearfix">
                    <label><b class="red">*</b>联系方式：</label>
                    <input readonly="true" value="${allInfo.beneficiaryContactPhone}">
                </div>-->
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>手机号码：</label>
                    <input readonly="true" value="${allInfo.beneficiaryContactTel}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>联系地址：</label>
                    <input class="shortInput" readonly="true" value="${allInfo.beneficiaryProvince}">
                    <input class="shortInput" readonly="true" value="${allInfo.beneficiaryCity}">
                    <input class="shortInput" readonly="true" value="${allInfo.beneficiaryCounty}">
                    <input class="longInput" readonly="true" value="${allInfo.beneficiaryAdd}">
                </div>
                <h3 class="form_part_minititle order_title_mt"><span>投保项目基本信息</span></h3>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>项目名称：</label>
                    <input readonly="true" value="${allInfo.proName}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>项目标段：</label>
                    <input readonly="true" value="${allInfo.bids}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>招标文件编号：</label>
                    <input readonly="true" value="${allInfo.fileNumber}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>工程类型：</label>
                    @for(projectType in projectType){
                    @if(allInfo.proType == projectType.id){
                    <input readonly="true" value="${projectType.projectTypeName}">
                    @}
                    @}
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>投标截止日期：</label>
                    <input readonly="true" value="${allInfo.bidDate}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>投标有效期：</label>
                    <input readonly="true" value="${allInfo.bidTermValidity}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>计划工期(天)：</label>
                    <input readonly="true" value="${allInfo.plannedTime}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>招标控制价(元)：</label>
                    <input readonly="true" value="${allInfo.tcp}">
                </div>
                <!--<div class="item_info_line clearfix">
                    <label><b class="red">*</b>投标开始时间：</label>
                    <input readonly="true" value="${allInfo.insuranceStartTime}" >
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>投标结束时间：</label>
                    <input readonly="true" value="${allInfo.insuranceEndTime}" >
                </div>-->
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>保证金金额(元)：</label>
                    <input readonly="true" value="${allInfo.insuranceAmount}" >
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>保费(元)：</label>
                    <input readonly="true" value="${allInfo.premium}" >
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>投标代理机构：</label>
                    <input readonly="true" value="${allInfo.agency}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>司法管辖：</label>
                    <input readonly="true" value="${allInfo.jurisdiction}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>争议处理：</label>
                    @for(disputeType in disputeType){
                    @if(allInfo.dispute == disputeType.id){
                    <input readonly="true" value="${disputeType.disputeTypeName}">
                    @}
                    @}
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>招标文件是否要求基本账户付费：</label>
                    @if(allInfo.isBasicPay == 2){
                    <input class="fillin_form_send" type="radio" value="2" name="456" placeholder='yes'checked/><label class="fillin_form_yes">是</label>
                    <input class="fillin_form_send" type="radio" value="0" name="456" placeholder='no' disabled="disabled"/><label class="fillin_form_no">否</label><br>
                    @}else{
                    <input class="fillin_form_send" type="radio" value="2" name="456" placeholder='yes'disabled="disabled"/><label class="fillin_form_yes">是</label>
                    <input class="fillin_form_send" type="radio" value="0" name="456" placeholder='no' checked/><label class="fillin_form_no">否</label><br>
                    @}
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>项目地址：</label>
                    <input class="shortInput" readonly="true" value="${allInfo.proProvince}">
                    <input class="shortInput" readonly="true" value="${allInfo.proCity}">
                    <input class="shortInput" readonly="true" value="${allInfo.proCounty}">
                    <input class="longInput" readonly="true" value="${allInfo.proAdd}">
                </div>
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>工程招标文件：</label>
                    <input readonly="true" value="${allInfo.tenderDocument}">
                </div>
                @if(allInfo.invoice != 0){
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>发票：</label>
                    <input class="fillin_form_send bill" value="2" type="radio" name="invoice" placeholder='yes' checked/><label class="fillin_form_yes">是</label>
                    <input class="fillin_form_send bill" type="radio" value="0" name="invoice" placeholder='no' disabled="disabled"/><label class="fillin_form_no">否</label>
                </div>
                <div id="bill_inf2" class="clearfix left container">
                    <div class="item_info_line clearfix">
                        <label><b class="red">*</b>发票类型：</label>
                        @for(invoiceType in invoiceType){
                        @if(invoiceInfobo.invoiceType == invoiceType.id){
                        <input readonly="true" value="${invoiceType.invoiceType}" style="width:152px" />
                        @}
                        @}
                        <input class="fillin_form_send" type="radio" name="invoiceType" value="1" placeholder='no' checked/><label class="fillin_form_no2" chenked>纸质发票</label>
                    </div>
                    <div class="item_info_line clearfix">
                        <label><b class="red">*</b>单位名称：</label>
                        <input readonly="true" value="${invoiceInfobo.companyName}">
                    </div>
                    <div class="item_info_line clearfix">
                        <label><b class="red">*</b>纳税人识别号：</label>
                        <input readonly="true" value="${invoiceInfobo.dutyParagraph}">
                    </div>
                    <!--<div class="item_info_line clearfix">
                        <label><b class="red">*</b>公司注册地址：</label>
                        <input readonly="true" value="${invoiceInfobo.companyRegAdd}">
                    </div>-->
                    @if(invoiceInfobo.id == 2){
                    <div id="box2">
                        <div class="item_info_line clearfix">
                            <label><b class="red">*</b>开户行名称：</label>
                            <input readonly="true" value="${invoiceInfobo.bankName}">
                        </div>
                        <div class="item_info_line clearfix">
                            <label><b class="red">*</b>公司注册电话：</label>
                            <input readonly="true" value="${invoiceInfobo.companyPhone}">
                        </div>
                        <div class="item_info_line clearfix">
                            <label><b class="red">*</b>开户行账号：</label>
                            <input readonly="true" value="${invoiceInfobo.bankCard}">
                        </div>
                        <div class="item_info_line clearfix">
                            <label><b class="red">*</b>开户行地址：</label>
                            <input readonly="true" value="${invoiceInfobo.openingBankAdd}">
                        </div>
                        <div class="item_info_line clearfix">
                            <label><b class="red">*</b>上传信息采集表：</label>
                            <div class="input_file input_file_left">
                                <input class="file" type='file' onchange=''/>
                            </div>
                            <label class="file_label input_file_left">1.照片必须支持png、jpg格式<br>2.必须看清证件号码，且证件号码不能被遮挡<br>3.请在上传已盖章信息采集表前下载发票信息进行盖章：</label>
                        </div>
                        <div class="input_file_wrap form-group_2" style="position: relative;">
                            <div style="position:absolute;left: 13.75rem;top:0.75rem;width:11.5rem;height:8.5rem;" id="taxCertPreId">
                                <div><img width="20px" height="20px" src="${invoiceInfobo.taxCert}"></div>
                            </div>
                            <label class="fillin_form_label input_file_left"> 税务登记证</label>
                            <div style="position: relative; background-image: none;" class="input_file input_file_left">
                            </div>
                            <label class="file_label input_file_left">1.照片必须支持png、jpg格式<br>2.必须看清证件号码，且证件号码不能被遮挡</label>
                        </div>
                        <div class="input_file_wrap form-group_2" style="position: relative;">
                            <div style="position:absolute;left: 13.75rem;top:0.75rem;width:11.5rem;height:8.5rem;" id="taxpayerPreId">
                                <div><img width="20px" height="20px" src="${invoiceInfobo.taxpayer}"></div>
                            </div>
                            <label class="fillin_form_label input_file_left"> 一般纳税人证明</label>
                            <div class="input_file input_file_left" style="background: none;position: relative;">
                            </div>
                            <label class="file_label input_file_left">1.照片必须支持png、jpg格式<br>2.必须看清证件号码，且证件号码不能被遮挡</label>
                        </div>
                        <div class="input_file_wrap form-group_2" style="position: relative;">
                            <div style="position:absolute;left: 13.75rem;top:0.75rem;width:11.5rem;height:8.5rem;" id="bankOpenCertPreId">
                                <div><img width="20px" height="20px" src="${invoiceInfobo.bankOpenCert}"></div>
                            </div>
                            <label class="fillin_form_label input_file_left">银行开户许可证明</label>
                            <div class="input_file input_file_left" style="background: none;position: relative;">
                            </div>
                            <label class="file_label input_file_left">1.照片必须支持png、jpg格式<br>2.必须看清证件号码，且证件号码不能被遮挡</label>
                        </div>
                    </div>
                    @}
                </div>
                @}
                @if(allInfo.sendInfo != 0){
                <div class="item_info_line clearfix">
                    <label><b class="red">*</b>寄送信息：</label>
                    <input class="fillin_form_send send" value="2" type="radio" placeholder='yes' checked/><label class="fillin_form_yes">是</label>
                    <input class="fillin_form_send send" type="radio" name="sendInfo" value="0" placeholder='no' disabled="disabled"/><label class="fillin_form_no">否</label>
                </div>
                <div id="send_inf2">
                    <div class="item_info_line clearfix">
                        <label><b class="red">*</b>收件人名称：</label>
                        <input readonly="true" value="${sendInfobo.consignee}">
                    </div>
                    <div class="item_info_line clearfix">
                        <label><b class="red">*</b>收件人联系方式：</label>
                        <input readonly="true" value="${sendInfobo.consigneeTel}">
                    </div>
                    <div class="item_info_line clearfix">
                        <label><b class="red">*</b>收件人地址：</label>
                        <input readonly="true" value="${sendInfobo.consigneeAdd}">
                    </div>
                </div>
                @}
                <h3 class="form_part_minititle order_title_mt"><span>投保须知</span></h3>
                <div class="statement left container">
                    <div class="statement_title">
                        <h2>保险公司提示与投保人声明</h2>
                    </div>
                    <div class="statement_content">
                        <h2>保险公司提示</h2>
                        <p>1、本投保单为保险合同的组成部分。请投保人在填写投保单之前仔细阅读保险条款（包括主险和附加险），尤其是加黑突出标注的、免除保险人责任的条款内容，并听取保险公司业务人员对条款的说明以及对免除保险人责任条款的明确说明，如有不明白或有疑义的，请及时询问保险公司业务人员。</p>
                        <p>2、保险公司对您提供的资料承担保密义务，保险公司会采取恰当的物理、电子、管理技术手段保护投保人提供的资料，同时，采取适当的安全措施保护投保人提供的资料免于未经授权的访问、使用或泄露。</p>
                    </div>
                    <div class="statement_content">
                        <h2>投保人声明</h2>
                        <p>1、投保企业基本信息，真实有效，所填信息准确无误；投保项目信息，已完全按招标文件中的要求填写，所填信息准确无误；本投保人已经收悉并仔细阅读招标文件中有关支付方式的要求，并在保单支付时严格按照要求进行支付。</p>
                        <p>2、本投保人已经收悉并仔细阅读保险条款，尤其是加黑突出标注的、免除保险人责任的条款内容。保险公司业务人员已就本合同所涉及的所有免除保险人责任条款的概念、内容及其法律后果向本投保人做出了通俗的、本投保人能够理解的解释和明确说明，本投保人对其真实含义和法律后果完全理解，没有异议予以确认并申请投保。</p>
                        <p>3、基于为本投保人及被保险人提供更优质服务和产品的目的，除法律另有规定外，本投保人授权阳光保险可以从相关单位、组织和个人就有关保险事宜查询、索取与本投保人。</p>
                    </div>
                </div>
                <div class="agree left container">
                    <input onclick="check()" id="vehicle" type="checkbox" name="vehicle" placeholder="Car"/>
                    <label class="agree_label">我同意以上提示声明和<a id="agree_label">《保险条款》</a></label>
                    <input id="agree_clause" name="agree_clause" type="hidden" value="">
                </div>
                <div class="submit_box container left">
                    <button class="form_cancel" type="button" id="updInfoBtn">返回修改</button>
                    <button type="submit" id="btn_right"  disabled="disabled" class="form_sure">确认提交</button>
                </div>
                <input type="hidden" id="allInfoId" name="allInfoId" value="${allInfo.id}"/>
            </form>
            <form id="updInfo" action="${ctxPath}/insurance/updInfoPage" method="GET">
                <input type="hidden" id="allInfoId" name="allInfoId" value="${allInfo.id}"/>
            </form>
        </div>
    </div>
</div>
<input type="hidden" id="ai" value="${allInfo.invoice}">
<input type="hidden" id="as" value="${allInfo.sendInfo}">

<input id="invoiceId" name="invoiceId" type="hidden" value="${invoiceInfobo.invoiceType}">

<script src="${ctxPath}/static/modular/index/insurance/confirmation.js"></script>
<script src="${ctxPath}/static/modular/index/insurance/updInfo.js"></script>
<script src="${ctxPath}/static/modular/index/agree_clause.js" charset="utf-8"></script>
<script>
    function check(){
//判断checkbox有没有被选中
        if(document.getElementById("vehicle").checked==true)
        {
            document.getElementById("btn_right").disabled="";//给BUTTON按钮的disabled属性赋值
            document.getElementById("btn_right").style="background-color: #448aca;";
        }else
        {
            document.getElementById("btn_right").disabled="disabled";
            document.getElementById("btn_right").style="background-color: #c1ccda;";
        }
    }

    var invoiceId=$("#invoiceId").val();
    if(invoiceId!=2){
        $("#box2").hide();
    }else{
        $("#box2").show();
    }
</script>
@}
