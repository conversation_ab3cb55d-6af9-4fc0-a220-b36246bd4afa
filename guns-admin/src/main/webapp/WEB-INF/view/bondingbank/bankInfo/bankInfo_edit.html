@layout("/common/_container.html"){
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="form-horizontal">

            <div class="row">
                <div class="col-sm-12 b-r">
                    <#dictSelector id="sysBank" name ="sysBank" code="sys_bank" label="银行"  underline="true" value="${item.sysBank}" />
                    <#dictSelector id="sysProduct" name ="sysProduct" code="sys_product" label="险种设置"  underline="true" value="${item.sysProduct}" />
                    <#select id="addressP" name="省" underline="true" >
                        <option value="${item.addressP}">${address.address_p}</option>
                    </#select>
                    <#select id="addressC" name="市" underline="true" >
                        <option value="${item.addressC}">${address.address_c}</option>
                    </#select>
                    <#input id="rate" name="费率(%)" underline="true" value="${item.rate}" />
                </div>
            </div>

            <div class="row btn-group-m-t">
                <div class="col-sm-10">
                    <#button btnCss="info" name="提交" id="ensure" icon="fa-check" clickFun="BankInfoInfoDlg.editSubmit()"/>
                    <#button btnCss="danger" name="取消" id="cancel" icon="fa-eraser" clickFun="BankInfoInfoDlg.close()"/>
                </div>
            </div>
        </div>

    </div>
</div>
<script src="${ctxPath}/static/modular/bondingbank/bankInfo/bankInfo_info.js"></script>
<script>
    $("#sysBank").css("width","100%");
    $("#sysProduct").css("width","100%");
    $(".hr-line-dashed").css("margin","0");
    $(".btn-group-m-t .col-sm-10").css("text-align","center");
</script>
@}
