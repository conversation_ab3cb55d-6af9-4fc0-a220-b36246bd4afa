@layout("/common/_container.html"){

<link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/policy/fillin.css">
<script src="${ctxPath}/static/modular/icinformation/insurer/insureInformation_info.js"></script>

<div class="ibox float-e-margins">
    <div class="ibox-content">
        <!--<div class="logo left clearfix" style="margin-bottom: 50px">
            <img class="left" src="${ctxPath}/static/index/images/logo.png" alt="聚汇融盛">
        </div>-->
        <div id="tipInfo" style="display: inline; " class="fillin_text">
            <span style="font-weight: bold; font-size: 25px; color: #c10802; margin-left: 30px" >
                ${content}
            </span>
            <div class="row btn-group-m-t">
                <div class="col-sm-11" style="text-align: center">
                    <#button id="nextOp" clickFun="showPayInfo(0)" btnCss="info" name="倒计时开始" icon=""/>
                </div>
            </div>
        </div>
        <div id="insurerInfoForm" style="display: none" class="form-horizontal">
            <div class="case">
                <div class="column">
                    <div class="margin75">
                        <div class="fillin_title">收款账户信息</div>
                    </div>
                    <div class="fillin_form_box margin75">
                        <form class="fillin_form">
                            <label class="fillin_form_label">收款人：</label>
                            <h6 style="width: 25%">${username}</h6>
                            <label class="fillin_form_label">收款账号：</label>
                            <h6 style="width: 25%">${account}</h6>
                            <br>
                            <label class="fillin_form_label">开户行：</label>
                            <h6 style="width: 25%">${bankName}</h6>
                            <label class="fillin_form_label">费用（元）：</label>
                            <h6 style="width: 25%">${cost}</h6>
                        </form>
                    </div>
                    <div class="row btn-group-m-t">
                        <div class="col-sm-11" style="text-align: center">
                            <#button id="preOp" clickFun="showPayInfo(1)" btnCss="info" name="上一步" icon=""/>
                            <a href="${hrefUrl}" target="_blank">
                                <#button id="showDetail" btnCss="info" name="查看保函发放详情" icon=""/>
                            </a>
                        </div>
                    </div>
                    <div class="fillin_text">
                        <span style="font-weight: bold; font-size: 15px; color: #c10802; margin-left: 30px" >${content}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(function () {
        $("#nextOp").css({"width": "150px", "height": "50px", "font-size":"18px", "font-weight": "bold"}).attr ("disabled","true");//按钮样式
        $("#preOp, #showDetail").css({"width": "160px", "height": "40px", "font-size":"16px", "font-weight": "bold"});//按钮样式
        //$("#nextOp").attr ("disabled","true");//置灰
        var count = 6;
        var time = setInterval(function(){
            count--;
            //重新写入
            $("#nextOp").text("倒计时"+count+"S");
            if(count == 0){
                $("#nextOp").removeAttr ("disabled");//正常
                $("#nextOp").text("下一步");
                clearInterval(time);
            }
        },1000)
    });

    function showPayInfo(type){
        if(type == "0"){
            $("#tipInfo").css({"display": "none"});//隐藏
            $("#insurerInfoForm").css({"display": "inline"});//显示
        }else{
            $("#tipInfo").css({"display": "inline"});//显示
            $("#insurerInfoForm").css({"display": "none"});//隐藏
        }
    }
</script>
@}

