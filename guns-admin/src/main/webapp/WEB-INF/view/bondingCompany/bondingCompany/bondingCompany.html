@layout("/common/_container.html"){
<div class="row">
    <div class="col-sm-12">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>担保公司管理</h5>
            </div>
            <div class="ibox-content">
                <div class="row row-lg">
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-3">
                                <#NameCon id="condition" name="担保公司" />
                            </div>
                            <div class="col-sm-3">
                                <#button name="搜索" icon="fa-search" clickFun="BondingCompany.search()"/>
                            </div>
                        </div>
                        <div class="hidden-xs" id="BondingCompanyTableToolbar" role="group">
                            @if(shiro.hasPermission("/bondingCompany/add")){
                                <#button name="添加" icon="fa-plus" clickFun="BondingCompany.openAddBondingCompany()"/>
                            @}
                            @if(shiro.hasPermission("/bondingCompany/update")){
                                <#button name="修改" icon="fa-edit" clickFun="BondingCompany.openBondingCompanyDetail()" space="true"/>
                            @}
                            @if(shiro.hasPermission("/bondingCompany/delete")){
                                <#button name="删除" icon="fa-remove" clickFun="BondingCompany.delete()" space="true"/>
                            @}
                        </div>
                        <#table id="BondingCompanyTable"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="${ctxPath}/static/modular/bondingCompany/bondingCompany/bondingCompany.js"></script>
@}
