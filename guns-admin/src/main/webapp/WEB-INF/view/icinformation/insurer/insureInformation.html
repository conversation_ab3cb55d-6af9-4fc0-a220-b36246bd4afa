@layout("/common/_container.html"){
<div class="row">
    <div class="col-sm-12">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>保函申请列表</h5>
            </div>
            <div class="ibox-content">
                <div class="row row-lg">
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-2">
                                <#NameCon id="condition" name="项目名称" />
                            </div>
                            <div class="col-sm-2">
                                <#NameCon id="applicant" name="投标人名称" />
                            </div>
                            <div class="col-sm-2">
                                <#NameCon id="applicationNo" name="申请编号" />
                            </div>
                            <div class="col-sm-2">
                                <#NameCon id="orderNumber" name="保函编号" />
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" style="width:99px;padding-top:6px;text-align: center;margin-right: -16px;height: 34px;border: 1px solid #e7eaec;font-size: 14px;font-weight: normal">审核状态</label>
                                    <div class="col-sm-9">
                                        <select class="form-control" id="state" name="state" style="font-size: 14px">
                                            <option value="">请选择</option>
                                            <option value="4,5">未支付</option>
                                            <option value="6">已支付</option>
                                            <option value="2">审核中</option>
                                            <option value="3">审核失败</option>
                                            <option value="7">保单生成</option>
                                            <option value="8">保单查看</option>
                                            <option value="9">保单下载</option>
                                        </select>
                                    </div>
                                </div>


                            </div>

                            <div class="col-sm-2">
                                <#button btnCss="success" name="搜索" icon="fa-search" clickFun="Insurer.search()"/>
                            </div>
                        </div>
                        <!--<div class="hidden-xs" id="InsurerTableToolbar" role="group">-->

                            <!--<button type="button" class="btn btn-primary " onclick="Insurer.InsurerDetails()" id="">-->
                                <!--<i class=""></i>&nbsp;查看详情-->
                            <!--</button>-->
                        <!--</div>-->
                        <#table id="InsurerTable"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="${ctxPath}/static/modular/icinformation/insurer/insureInformation.js"></script>
@}
