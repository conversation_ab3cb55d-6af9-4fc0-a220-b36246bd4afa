@layout("/common/_container.html"){
<div class="row">
    <div class="col-sm-12">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>理赔申请列表</h5>
            </div>
            <div class="ibox-content">
                <div class="row row-lg">
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-3">
                                <#NameCon id="insurancePolicyNo" name="保函编号" />
                            </div>


                            <div class="col-sm-2">
                                <#button btnCss="success" name="搜索" icon="fa-search" clickFun="InsurerClaims.search()"/>
                            </div>
                        </div>
                        <!--<div class="hidden-xs" id="InsurerTableToolbar" role="group">-->

                            <!--<button type="button" class="btn btn-primary " onclick="Insurer.InsurerDetails()" id="">-->
                                <!--<i class=""></i>&nbsp;查看详情-->
                            <!--</button>-->
                        <!--</div>-->
                        <#table id="InsurerClaimsTable"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="${ctxPath}/static/modular/icinformation/insurer/claimsInformation.js"></script>
@}
