@layout("common/_head.html"){
<div class="column padding_top1">
    <div class="margin75">
        <div class="fillin_state_wrap">
            <div class="fill_state">
                <div class="state">
                    <div class="state_full"></div>
                    <div class="state_center1"><i class="iconfont icon-duigou"></i></div>
                    <div class="state_right1"></div>
                </div>
                <div class="state_text">填写表单</div>
            </div>
            <div class="fill_state">
                <div class="state">
                    <div class="state_left1"></div>
                    <div class="state_center1"><i class="iconfont icon-duigou"></i></div>
                    <div class="state_right1"></div>
                </div>
                <div class="state_text">确认保单</div>
            </div>
            <div class="fill_state">
                <div class="state">
                    <div class="state_left1"></div>
                    <div class="state_center">3</div>
                    <div class="state_right"></div>
                </div>
                <div class="state_text">支付保费</div>
            </div>
            <div class="fill_state">
                <div class="state">
                    <div class="state_left"></div>
                    <div class="state_center">4</div>
                    <div class="state_right"></div>
                </div>
                <div class="state_text">投保成功</div>
            </div>
            <div class="fill_state">
                <div class="state">
                    <div class="state_left"></div>
                    <div class="state_center">5</div>
                    <div class="state_full"></div>
                </div>
                <div class="state_text">下载保函</div>
            </div>
        </div>
    </div>
    <div class="fillin_form_box margin75 margin_top1">
        <form class="fillin_form" id="shenhe" role="form" action="${ctxPath}/insurance/payment" method="get">
            <div class="column padding_top1">
                <div class="wait">
                    <input id="reg_agree" name="reg_agree" type="hidden" value="">
                    <input type="hidden" id="allInfoIds" name="allInfoId" value="${allInfo.id}"/>
                    <input type="hidden" name="orderNumber" id = "orderNumber" value="${map.orderNumber}"/>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="${ctxPath}/static/js/jquery.min.js?v=2023"></script>
<script src="${ctxPath}/static/js/jquery-migrate.min.js?v=2023"></script>
<script src="${ctxPath}/static/modular/index/confir.js" charset="utf-8"></script>
<script src="${ctxPath}/static/js/plugins/layer/layer.js" charset="utf-8"></script>


@}
