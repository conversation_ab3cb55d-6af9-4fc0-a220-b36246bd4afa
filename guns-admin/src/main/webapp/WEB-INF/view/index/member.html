@layout("common/_head.html"){
<div class="column padding_top1">
	<div class="width75 border">
		<div class="policy_title">企业基本信息</div>

		<div class="policy_form_box width75">
			<form id="policy_form" class="policy_form" role="form" action="${ctxPath}/postUserInfo" method="post">
				@if(result==0){
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>公司名称</label>
					<input  name="unitName" placeholder="请输入公司名称"><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>证件类型</label>
					<select style="width:30rem;" name="cardType">
						@for(cardType in cardType){
						<option value="${cardType.id}">${cardType.cardTypeName}</option>
						@}
					</select><br>
					<!--<label class="policy_form_label"><span class="important">*</span>外国企业</label>
					<input class="policy_form_send" type="radio" value="1" name="isForeignCompany" placeholder='yes'/><label class="policy_form_yes">是</label>
					<input class="policy_form_send" type="radio" value="2" name="isForeignCompany" placeholder='no' checked/><label class="policy_form_no">否</label><br>
					 -->
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>证件号码</label>
					<input name="cardNum" placeholder="请输入证件号码"><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important"></span>基本户开户行</label>
					<input name="basicAccountBank" placeholder="请输入基本户开户行"><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important"></span>银行帐号</label>
					<input name="bankAccount" placeholder="请输入银行帐号"><br>
				</div>
				<!--<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important"></span>证件号码有效期</label>
					<input  type="date" name="dateofvalidity"><br>
				</div>-->
				<!--label class="policy_form_label"><span class="important">*</span>联系电话</label>
				<input name="unitPhone" placeholder="请输入联系电话"><br-->
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>注册地址(住所)</label>
					<select name="unitProvince" id="unitProvince">
					</select>
					<select name="unitCity" id="unitCity">
					</select>
					<select name="unitCounty" id="unitCounty">
					</select>
					<input type="text" name="unitAdd" class="add_input" placeholder="详细地址"/><br>
					<input type="hidden" id="result" value="0">
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>联系人</label>
					<input type="text" name="contacts" placeholder="联系人"/><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>联系方式</label>
					<input type="text" name="contactsTel" placeholder="联系电话"/><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>邮箱</label>
					<input type="text" name="email" required/><br>
				</div>
				<div class="input_file_wrap" style="position: relative;">
					<div class="loadimg_wrap" id="licenseFilePreId">
						<div class="loadimg"><img src="${ctxPath}/static/img/file.png"></div>
					</div>
					<label class="policy_form_label input_file_left"><span class="important">*</span>证件上传</label>
					<div style="position: relative; background-image: none;" class="input_file input_file_left">
						<!--<input id="fileName" class="file" type='file' />
						<input type="hidden" id="pathy" name="licenseFile">-->
						<div  style="position:absolute;left:0;top:0;opacity: 0;width:12.5rem;height:8.75rem;" class="head-scu-btn upload-btn webuploader-container" id="licenseFileBtnId">
							<div style="background-color:#448aca;opacity: 0;width:12.5rem;height:8.75rem;" class="webuploader-pick"><i class="fa fa-upload"></i>&nbsp;上传</div>
							<div id="rt_rt_1cq2is0q32e8ppurfe1u7j1rqm1" style="opacity: 0;position: absolute; top: 0px; left: 0px;width:100%;height:8.75rem; overflow: hidden; bottom: auto; right: auto;">
								<input type="file" name="file" class="webuploader-element-invisible" accept="image/gif,image/jpg,image/jpeg,image/bmp,image/png">
								<label style="opacity: 0; width: 100%; height: 100%; display: block; cursor: pointer; background-color:#448aca;"></label>
							</div>
						</div>
						<input type="hidden" id="licenseFile" name="licenseFile">
					</div>
					<label class="file_label input_file_left">示例</label>
					<label class="file_label_img input_file_left"><img src="${ctxPath}/static/index/images/license.png"></label>
					<label class="file_label input_file_left">1.照片仅支持png、jpg格式<br>2.必须看清证件号码，且证件号码不能被遮挡<br>3.扫描件或复印件必须加盖公章</label>
				</div><br>
				<label class="policy_form_label"><span class="important">*</span>资质等级</label>
				<select name="grade" class="grade">
					@for(gradeType in gradeType){
					<option value="${gradeType.id}">${gradeType.gradeTypeName}</option>
					@}
				</select><br>
				<div class="input_file_wrap" style="position: relative;">
					<div class="loadimg_wrap" id="gradeFilePreId">
						<div class="loadimg"><img src="${ctxPath}/static/img/file.png"></div>
					</div>
					<label class="policy_form_label input_file_left">证件上传</label>
					<div style="position: relative; background-image: none;" class="input_file input_file_left">
						<div  style="position:absolute;left:0;top:0;opacity: 0;width:12.5rem;height:8.75rem;" class="head-scu-btn upload-btn webuploader-container" id="gradeFileBtnId">
							<div style="background-color:#448aca;opacity: 0;width:12.5rem;height:8.75rem;" class="webuploader-pick"><i class="fa fa-upload"></i>&nbsp;上传</div>
							<div id="rt_rt_1cq2is0q32e8ppurfe1u7j1rqmm" style="opacity: 0;position: absolute; top: 0px; left: 0px;width:100%;height:8.75rem; overflow: hidden; bottom: auto; right: auto;">
								<input type="file" name="file" class="webuploader-element-invisible" accept="image/gif,image/jpg,image/jpeg,image/bmp,image/png">
								<label style="opacity: 0; width: 100%; height: 100%; display: block; cursor: pointer; background-color:#448aca;"></label>
							</div>
						</div>
						<input type="hidden" id="gradeFile" name="gradeFile">
					</div>
					<label class="file_label input_file_left">示例</label>
					<label class="file_label_img input_file_left"><img src="${ctxPath}/static/index/images/zizhi.png"></label>
					<label class="file_label input_file_left">1.照片仅支持png、jpg格式<br>2.必须看清证件号码，且证件号码不能被遮挡<br>3.扫描件或复印件必须加盖公章</label>
				</div><br>
				@}else{
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>公司名称</label>
					<input  name="unitName" value="${applicant.unitName}"><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>证件类型</label>
					<select style="width:30rem;" name="cardType" id="cardType">
						@for(cardType in cardType){
						@if(applicant.cardType==cardType.id){
						<option value="${cardType.id}" selected>${cardType.cardTypeName}</option>
						@}else{
						<option value="${cardType.id}">${cardType.cardTypeName}</option>
						@}
						@}
					</select><br>
					<!--
					<label class="policy_form_label"><span class="important">*</span>外国企业</label>
					@if(applicant.isForeignCompany ==1){
					<input class="policy_form_send" type="radio" value="1" name="isForeignCompany" placeholder='no' checked/><label class="policy_form_yes">是</label>
					<input class="policy_form_send" type="radio" value="2" name="isForeignCompany" placeholder='yes' /><label class="policy_form_no">否</label><br>
					@}else{
					<input class="policy_form_send" type="radio" value="1" name="isForeignCompany" placeholder='yes'/><label class="policy_form_yes">是</label>
					<input class="policy_form_send" type="radio" value="2" name="isForeignCompany" placeholder='no' checked/><label class="policy_form_no">否</label><br>
					@}
					-->
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>证件号码</label>
					<input readonly="readonly" name="cardNum" value="${applicant.cardNum}"><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important"></span>基本户开户行</label>
					<input name="basicAccountBank" value="${applicant.basicAccountBank}"><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important"></span>银行帐号</label>
					<input name="bankAccount" value="${applicant.bankAccount}"><br>
				</div>
				<!--<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important"></span>证件号码有效期</label>
					<input type="date" name="dateofvalidity" value="${applicant.dateofvalidity}"><br>
				</div>-->
				<!--label class="policy_form_label"><span class="important">*</span>联系电话</label>
				<input name="unitPhone" value="${applicant.unitPhone}"><br-->
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>注册地址(住所)</label>
					<select name="unitProvince" id="unitProvince">

					</select>
					<select name="unitCity" id="unitCity">

					</select>
					<select name="unitCounty" id="unitCounty">

					</select>
					<input type="text" name="unitAdd" class="add_input" value="${applicant.unitAdd}"/><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>联系人</label>
					<input type="text" name="contacts" value="${applicant.contacts}"/><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>联系方式</label>
					<input type="text" name="contactsTel" value="${applicant.contactsTel}"/><br>
				</div>
				<div class="form-group form-group_2">
					<label class="policy_form_label"><span class="important">*</span>邮箱</label>
					<input type="text" name="email" value="${applicant.email}" required/><br>
				</div>
				<div class="input_file_wrap" style="position: relative;">
					<div class="loadimg_wrap" id="licenseFilePreId">
						<div class="loadimg"><img src="${applicant.licenseFile}"></div>
					</div>
					<label class="policy_form_label input_file_left"><span class="important">*</span>证件上传</label>
					<div style="position: relative; background-image: none;" class="input_file input_file_left">
						<!--<input id="fileName" class="file" type='file' />
						<input type="hidden" id="pathy" name="licenseFile">-->
						<div  style="position:absolute;left:0;top:0;opacity: 0;width:12.5rem;height:8.75rem;" class="head-scu-btn upload-btn webuploader-container" id="licenseFileBtnId">
							<div style="background-color:#448aca;opacity: 0;width:12.5rem;height:8.75rem;" class="webuploader-pick"><i class="fa fa-upload"></i>&nbsp;上传</div>
							<div id="rt_rt_1cq2is0q32e8ppurfe1u7j1rqm1" style="opacity: 0;position: absolute; top: 0px; left: 0px;width:100%;height:8.75rem; overflow: hidden; bottom: auto; right: auto;">
								<input type="file" name="file" class="webuploader-element-invisible" accept="image/gif,image/jpg,image/jpeg,image/bmp,image/png">
								<label style="opacity: 0; width: 100%; height: 100%; display: block; cursor: pointer; background-color:#448aca;"></label>
							</div>
						</div>
						<input type="hidden" id="licenseFile" value="${applicant.licenseFile}" name="licenseFile">
					</div>
					<label class="file_label input_file_left">示例</label>
					<label class="file_label_img input_file_left"><img src="${ctxPath}/static/index/images/license.png"></label>
					<label class="file_label input_file_left">1.照片仅支持png、jpg格式<br>2.必须看清证件号码，且证件号码不能被遮挡<br>3.扫描件或复印件必须加盖公章</label>
				</div><br>
				<label class="policy_form_label"><span class="important">*</span>资质等级</label>
				<select id="grade" name="grade" class="grade">
					@for(gradeType in gradeType){
					@if(applicant.grade==gradeType.id){
					<option value="${gradeType.id}" selected>${gradeType.gradeTypeName}</option>
					@}else{
					<option value="${gradeType.id}">${gradeType.gradeTypeName}</option>
					@}
					@}
				</select><br>
				<div class="input_file_wrap" style="position: relative;">
					<div class="loadimg_wrap" id="gradeFilePreId">
						<div class="loadimg"><img src="${applicant.gradeFile}"></div>
					</div>
					<label class="policy_form_label input_file_left">证件上传</label>
					<div style="position: relative; background-image: none;" class="input_file input_file_left">
						<div  style="position:absolute;left:0;top:0;opacity: 0;width:12.5rem;height:8.75rem;" class="head-scu-btn upload-btn webuploader-container" id="gradeFileBtnId">
							<div style="background-color:#448aca;opacity: 0;width:12.5rem;height:8.75rem;" class="webuploader-pick"><i class="fa fa-upload"></i>&nbsp;上传</div>
							<div id="rt_rt_1cq2is0q32e8ppurfe1u7j1rqmm" style="opacity: 0;position: absolute; top: 0px; left: 0px;width:100%;height:8.75rem; overflow: hidden; bottom: auto; right: auto;">
								<input type="file" name="file" class="webuploader-element-invisible" accept="image/gif,image/jpg,image/jpeg,image/bmp,image/png">
								<label style="opacity: 0; width: 100%; height: 100%; display: block; cursor: pointer; background-color:#448aca;"></label>
							</div>
						</div>
						<input type="hidden" id="gradeFile" value="${applicant.gradeFile}" name="gradeFile">
					</div>
					<label class="file_label input_file_left">示例</label>
					<label class="file_label_img input_file_left"><img src="${ctxPath}/static/index/images/zizhi.png"></label>
					<label class="file_label input_file_left">1.照片仅支持png、jpg格式<br>2.必须看清证件号码，且证件号码不能被遮挡<br>3.扫描件或复印件必须加盖公章</label>
				</div><br>
				<input type="hidden" id="ins_pro" value="${applicant.unitProvince}">
				<input type="hidden" id="ins_city" value="${applicant.unitCity}">
				<input type="hidden" id="ins_county" value="${applicant.unitCounty}">
				<input type="hidden" id="result" value="${result}">


				@}
				<div class="btn_wrap">
					<button class="btn_left">取消</button>
					<button type="submit" class="btn_right">确认</button>
				</div>
			</form>
		</div>
	</div>
</div>
<link href="${ctxPath}/static/css/font-awesome.css?v=4.4.0" rel="stylesheet">
<link href="${ctxPath}/static/css/plugins/webuploader/webuploader.css" rel="stylesheet">
<script src="${ctxPath}/static/modular/index/common/area.js" charset="utf-8"></script>
<script src="${ctxPath}/static/modular/index/common/upload-member.js" charset="utf-8"></script>
<script src="${ctxPath}/static/js/plugins/webuploader/webuploader.min.js"></script>
<script src="${ctxPath}/static/js/plugins/validate/bootstrapValidator.min.js" charset="utf-8"></script>
<script src="${ctxPath}/static/frontend/policy_formfields.js" charset="utf-8"></script>
@if(has(duplicate)){
	<script type="text/javascript">
        $(function () {
            Feng.alert("${duplicate}");
        });
	</script>
@}
@}

