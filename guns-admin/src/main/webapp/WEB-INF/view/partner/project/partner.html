@layout("/common/_container.html"){
<div class="row">
    <div class="col-sm-12">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>合作方提供信息管理</h5>
            </div>
            <div class="ibox-content">
                <div class="row row-lg">
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-2">
                                <#TimeCon id="beginTime" name="开始时间" isTime="false" pattern="YYYY-MM-DD" />
                            </div>
                            <div class="col-sm-2">
                                <#TimeCon id="endTime" name="结束时间" isTime="false" pattern="YYYY-MM-DD" />
                            </div>
                            <div class="col-sm-2">
                                <#button name="搜索" icon="fa-search" clickFun="Partner.search()"/>
                                <#button name="重置" icon="fa-refresh" clickFun="Partner.refresh()"/>
                            </div>
                            <div class="row">
                                <div class="col-sm-2" style="position: absolute;top:5rem;left:1rem;overflow: hidden;width:100%" >
                                    <div style="float: left;">项目总数：<span id="totalNum"></span></div>
                                    <div style="margin-left:6rem;float: left;">保费总额：<span id="totalPremium"></span></div>
                                </div>
                            </div>
                        <#table id="PartnerTable"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="${ctxPath}/static/modular/partner/project/partner.js"></script>
<script>
    laydate.render({
        elem: '#beginTime'
    });
    laydate.render({
        elem: '#endTime'
    });
</script>
@}
