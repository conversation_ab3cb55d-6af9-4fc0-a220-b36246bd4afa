@layout("/common/_container.html"){
<div class="col-sm-4  col-sm-offset-4">
    <div class="ibox float-e-margins">
        <div class="ibox-title">
            <h5>修改密码</h5>
        </div>
        <div class="ibox-content">
            <div class="row row-lg">
                <div class="col-sm-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content" style="border:none !important; ">
                            <div class="form-horizontal">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <input id="account" type="hidden" value="${account}"/>
                                        <#input id="oldPwd" name="原密码" underline="true" type="password"/>
                                        <#input id="newPwd" name="新密码" underline="true" type="password"/>
                                        <#input id="rePwd" name="新密码验证" type="password"/>
                                        <h4 style="color: red;text-align: center;" id="tipsmsg"></h4>
                                    </div>
                                </div>
                                <div class="row btn-group-m-t">
                                    <div class="col-sm-10">
                                        <#button btnCss="info" name="提交" id="ensure" icon="fa-check" clickFun="UserInfoDlg.chPwd()"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="${ctxPath}/static/js/jquery.md5.min.js?v=2023"></script>
<script src="${ctxPath}/static/modular/system/user/user_info.js"></script>
@}
