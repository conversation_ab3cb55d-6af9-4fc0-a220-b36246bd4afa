@layout("/common/_container.html"){
<div class="ibox float-e-margins">
	<div class="ibox-content">
		<div class="form-horizontal" id="userInfoForm">

			<input type="hidden" id="id" value="${user.id}">
			<input type="hidden" id="sexValue" value="${user.sex}">

			<div class="row">
				<div class="col-sm-6 b-r">
					<#input id="account" name="账户" underline="true" value="${user.account}"/>

					<#select id="sex" name="性别" underline="true">
						<option value="1">男</option>
						<option value="2">女</option>
					</#select>

					<#input id="roleid" name="角色" underline="true" value="${roleName}" disabled="disabled"/>

					<#input id="email" name="邮箱" type="email" value="${user.email}"/>
				</div>
				<div class="col-sm-6">
					<div id="driverInfoContent">
						<#input id="name" name="姓名" underline="true" value="${user.name}"/>

						<#input id="birthday" name="出生日期" underline="true" type="text"
						value="${user.birthday}"/>

						<#input id="citySel" name="部门" underline="true" readonly="readonly" hidden="deptid" hiddenValue="${user.deptid}" value="${deptName}"
						clickFun="UserInfoDlg.showDeptSelectTree(); return false;"
						style="background-color: #ffffff !important;"/>

						<#input id="phone" name="电话" value="${user.phone}"/>
					</div>
				</div>
			</div>

			<!-- 这是部门选择的下拉框 -->
			<div id="menuContent" class="menuContent"
				 style="display: none; position: absolute; z-index: 200;">
				<ul id="treeDemo" class="ztree tree-box" style="width: 249px !important;"></ul>
			</div>

			<div class="row btn-group-m-t">
				<div class="col-sm-10">
					<#button btnCss="info" name="提交" id="ensure" icon="fa-check" clickFun="UserInfoDlg.editSubmit()"/>
					<#button btnCss="danger" name="取消" id="cancel" icon="fa-eraser" clickFun="UserInfoDlg.close()"/>
				</div>
			</div>
		</div>

	</div>
</div>
<script src="${ctxPath}/static/modular/system/user/user_info.js"></script>
<script>
    laydate.render({
        elem: '#birthday'
    });
</script>
@}
