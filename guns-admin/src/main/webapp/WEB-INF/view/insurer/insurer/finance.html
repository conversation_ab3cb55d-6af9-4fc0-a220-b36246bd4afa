@layout("/common/_container.html"){
<div class="row">
    <div class="col-sm-12">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>财务对账表</h5>
            </div>
            <div class="ibox-content">
                <div class="row row-lg">
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-2">
                                <#TimeCon id="startTime" name="开始时间" isTime="false" pattern="YYYY-MM-DD" />
                            </div>
                            <div class="col-sm-2">
                                <#TimeCon id="endTime" name="结束时间" isTime="false" pattern="YYYY-MM-DD" />
                            </div>
                            <div class="col-sm-3">
                                <#button btnCss="success" name="搜索" icon="fa-search" clickFun="Insurer.search()"/>
                            </div>
                        </div>

                        <#table id="InsurerTable"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="${ctxPath}/static/modular/insurer/insurer/finance.js"></script>
<script src="${ctxPath}/static/js/plugins/laydate/laydate.js"></script>
<script>
    laydate.render({
        elem: '#startTime'
    });
    laydate.render({
        elem: '#endTime'
    });
</script>
@}
