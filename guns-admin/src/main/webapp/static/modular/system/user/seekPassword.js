$(function(){
    //获取URL中的hostname和post
    var url1=window.location.host;
    //获取URL中的协议部分
    var url=window.location.protocol;
    $("#btn").click(function(){
        var This = $(this);
        var email=$("#email").val();
        var reg= /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        var codes=$("#codes").val();
        if(email=="") {
            $("#helpBlock2").html("*请输入邮箱");
            $("#helpBlock2").css({"color":"red"});
        }else if(!reg.test(email)){
            $("#helpBlock2").html("*请输入正确的邮箱地址");
            $("#helpBlock2").css({"color":"red"});
        }else{
            $("#helpBlock2").html("");
            var data={
                url1:url1,
                url:url,
                email:email,
                codes:codes
            };
            $.ajax({
                type:'post',
                url:'/send/sendEmail',
                data:data,
                cache:false,
                dataType:'json',
                success:function(data){
                    if(data.statusCode=="001100"){
                        $("#helpBlock0").html("*请输入正确的验证码");
                        $("#helpBlock0").css({"color":"red"});
                    }else if(data.statusCode=="002200"){
                        $("#helpBlock2").html("*该邮箱没有注册过的企业");
                        $("#helpBlock0").html("");
                        $("#helpBlock2").css({"color":"red"});
                    }else if(data.statusCode=="000000"){
                        $("#helpBlock0").html("");
                        $("#helpBlock2").html("");
                        layer.alert("邮件发送成功，请注意查收",function(){
                            window.location.href="/index";
                        });
                    }
                }
            })
        }
    })
})