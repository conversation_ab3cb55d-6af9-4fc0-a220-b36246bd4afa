$(function () {
    /*各种tab切换*/
    $(".info_detail").hide().first().show();
    $(".common_tab a").each(function (index,e) {
        $(e).click(function () {
            $(this).addClass("cur").siblings().removeClass("cur");
            $(".info_detail").eq(index).show().siblings(".info_detail").hide();
        });
    });

    $(".product_nav_detail").hide().first().show();
    $(".product_nav li").each(function (index,e) {
        $(e).click(function () {
            $(this).addClass("cur").siblings().removeClass("cur");
            $(".product_nav_detail").eq(index).show().siblings(".product_nav_detail").hide();
        });
    });

    /*弹窗关闭*/
    $(".popup_close").click(function () {
        $(this).parent(".form_popup").hide();
        $(".black_bg").hide();
    });
    /*移动端导航下拉*/
    $(".nav_icon").click(function () {
        $("nav").toggle();
    });
    $(".user_icon").click(function () {
        $(".top_right_list").toggle();
    });
    $(".form_popup").hide();
    $(".black_bg").hide();

    /*表单页面步骤适配*/
    $(".form_state dt span").height($(".form_state dt span").width()).css("line-height",$(".form_state dt span").width()+"px");
    $(".coverline").height($(".coverline").width()).css("line-height",$(".coverline").width()+"px");



    /*客服悬窗滑动控制*/
    $(".slidehide").hide();
    $(".slideshow").click(function () {
        $('#diyoumask').stop().fadeIn();
        $('#contactusdiyou').stop().animate({right:'0'},750);
        $(".slidehide").show();
        $(this).hide();
    });
    $(".slidehide").click(function () {
        $('#contactusdiyou').stop().animate({right:'-12rem'},750);
        $('#diyoumask').stop().fadeOut();
        $(".slideshow").show();
        $(this).hide();
    });

    /*checkbox*/
    $(".opacity_checkbox").click(function () {
        $(this).siblings(".checkboxBg").toggleClass("checked");
    });
    $(".opacity_checkbox:checked").siblings(".checkboxBg").addClass("checked");

    /*form*/
    if($("#no_bill1:checked")){
        $("#bill_inf1").hide();
    }else{
        $("#bill_inf1").show();
    }
    if($("#no_send1:checked")){
        $("#send_inf1").hide();
    }else{
        $("#send_inf1").show();
    }
    $("#bill_inf").hide();
    $("#bill").click(function () {
        $("#bill_inf").show();
    });
    $("#no_bill").click(function () {
        $("#bill_inf").hide();
        $("#companyName").val("");
        $("#dutyParagraph").val("");
        $("#companyRegAdd").val("");
    });
    $("#send_inf").hide();
    $("#send").click(function () {
        $("#send_inf").show();
    });
    $("#no_send").click(function () {
        $("#send_inf").hide();
        $("#send_inf2").hide();
        $("#consignee").val("");
        $("#consigneeTel").val("");
        $("#consigneeAdd").val("");
    });

    $("#bill1").click(function () {
        $("#bill_inf1").show();
    });
    $("#no_bill1").click(function () {
        $("#bill_inf1").hide();
        $("#companyName").val("");
        $("#dutyParagraph").val("");
        $("#companyRegAdd").val("");
    });
    $("#send1").click(function () {
        $("#send_inf1").show();
    });
    $("#no_send1").click(function () {
        $("#send_inf1").hide();
        $("#consignee").val("");
        $("#consigneeTel").val("");
        $("#consigneeAdd").val("");
    });

    $("#bill2").click(function () {
        $("#bill_inf2").show();
    });
    $("#no_bill2").click(function () {
        $("#bill_inf2").hide();
        $("#companyName").val("");
        $("#dutyParagraph").val("");
        $("#companyRegAdd").val("");
    });
    $("#send2").click(function () {
        $("#send_inf2").show();
    });
    $("#no_send2").click(function () {
        $("#send_inf2").hide();
        $("#consignee").val("");
        $("#consigneeTel").val("");
        $("#consigneeAdd").val("");
    });

    var divh = $(".new_index").height();
    var winh = $(window).height();
    /*console.log(divh,winh)*/
    if(divh<winh-120){
        $(".new_footer").addClass("botfooter")
    }
})

