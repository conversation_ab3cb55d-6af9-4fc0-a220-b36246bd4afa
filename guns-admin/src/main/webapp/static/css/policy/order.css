/*�û�����*/
/*order*/
.margin_top{
	margin-top:0.75rem;
}
.order_top{
	overflow:hidden;
	background-color:#fff;
}
.order_title{
	font-weight:bold;
	line-height:3;
	float:left;
	padding-left:1.5rem
}
.order_search{
	width:26.5rem;
	float:right;
	padding:0.5rem 0;
	overflow:hidden;
}
.order_search input{
	width:22.5rem;
	height:2rem;
	line-height:2rem;
	font-size:0.75rem;
	color:#999;
}
.order_content_box{
	
}
.content_title{
	display:flex;
	padding:0.5rem;
	background-color:#fff;
}
.content_title div{
	width:8%;
	text-align:center;
	line-height:2;
}
.current{
	border-bottom:2px #008ae9 solid;
	color:#008ae9;
}
.cnt_content div{
}
.order_wrap{}
.order_table{
	background-color:#fff;
	padding:0.75rem;
}
.table_head{
	font-size:0.75rem;
	line-height:3rem;
	font-weight:bold;
	display:flex;
	width:100%;
	text-align:left;
}
.column1{
	width:18.5rem;
	padding-left:0.75rem;
}
.column2{
	width:15.5rem;
}
.column3{
	width:10.5rem;
}
.column4{
	width:9rem;
}
.column5{
	width:11.5rem;
}
.column6{
	width:5.5rem;
}
.column7{
	width:6.5rem;
}
.column8{
	width:7rem;
}
.table_body{
	font-size:0.75rem;
	line-height:5.5rem;
	border:1px #d5d5d5 solid;
	display:flex;
	width:100%;
	text-align:left;
}
.cnt_content .table_inf{
	font-size:0.75rem;
	line-height:2.5rem;
	display:flex;
	width:100%;
	text-align:left;
	background-color:#f1f1f1;
	border-left:1px #d5d5d5 solid;
	border-right:1px #d5d5d5 solid;
	border-bottom:1px #d5d5d5 solid;
	margin-bottom:0.75rem;
}
.inf1{
	width:16.5rem;
	padding-left:0.75rem;
}
.inf2{
	width:50.5rem;
}
.inf3{
	width:7rem;
}
.red_btn{
	border:none;
	background-color:#e60012;
	width:4rem;
	height:2rem;
	line-height:2rem;
	color:#fff;
	margin-top:0.5rem;
	border-radius:0.25rem;
}
.blue_btn{
	border:none;
	background-color:#298cef;
	width:4rem;
	height:2rem;
	line-height:2rem;
	color:#fff;
	margin-top:0.5rem;
	border-radius:0.25rem;
}
