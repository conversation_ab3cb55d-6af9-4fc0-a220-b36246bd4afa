09:05:42.925 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
09:05:42.925 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 68129 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
09:05:42.928 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
09:05:45.051 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
09:05:45.052 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:05:45.052 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
09:05:45.099 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:05:47.655 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
09:05:48.205 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
09:05:49.912 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
09:05:49.912 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
09:05:50.344 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
09:05:50.346 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
09:05:50.349 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
09:05:50.359 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@702fc2e2, ORIGINAL=[Ljava.lang.String;@63ad6d23, PIC_CLICK=[Ljava.lang.String;@665351aa]
09:05:50.359 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
09:05:50.862 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
09:05:50.862 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$e780dd8c (priority:10000)
09:05:50.863 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$e780dd8c (priority:10000)
09:05:51.032 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$e780dd8c (priority:10000)
09:05:51.073 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
09:05:51.075 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
09:05:51.076 [Thread-28] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
09:05:51.076 [Thread-27] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
09:05:51.076 [Thread-29] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
09:05:51.264 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:05:51.272 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:05:51.273 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:05:51.277 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1747789551265'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:05:51.277 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
09:05:51.277 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:05:51.278 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@c45787d
09:05:52.694 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1747789551265 shutting down.
09:05:52.694 [restartedMain] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1747789551265 paused.
09:05:52.694 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1747789551265 shutdown complete.
09:05:52.695 [restartedMain] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
09:05:52.699 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
09:05:52.699 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
09:05:52.699 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
09:05:52.699 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
09:05:52.705 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
09:05:52.709 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
09:05:52.831 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:07:20.441 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 68200 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
09:07:20.443 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
09:07:20.443 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
09:07:22.818 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
09:07:22.819 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:07:22.819 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
09:07:22.887 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:07:25.494 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
09:07:26.028 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
09:07:27.450 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
09:07:27.451 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
09:07:28.064 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
09:07:28.068 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
09:07:28.071 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
09:07:28.087 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@1993b563, ORIGINAL=[Ljava.lang.String;@29c60ddc, PIC_CLICK=[Ljava.lang.String;@3eefb105]
09:07:28.088 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
09:07:28.676 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
09:07:28.677 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$beea17f5 (priority:10000)
09:07:28.677 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$beea17f5 (priority:10000)
09:07:28.885 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$beea17f5 (priority:10000)
09:07:28.914 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
09:07:28.915 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
09:07:28.915 [Thread-27] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
09:07:28.916 [Thread-29] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
09:07:28.916 [Thread-28] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
09:07:29.108 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:07:29.116 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:07:29.116 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:07:29.120 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1747789649109'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:07:29.121 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
09:07:29.121 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:07:29.122 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@59986914
09:07:30.868 [restartedMain] INFO  o.r.c.s.i.DSecretKeyServiceImpl - [cacheSecretData,59] - 初始化秘钥到 Redis 成功
09:07:33.223 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
09:07:33.642 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 13.438 seconds (JVM running for 14.419)
09:07:33.939 [RMI TCP Connection(6)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:07:34.643 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1747789649109 started.
09:07:35.933 [restartedMain] INFO  c.r.RuoYiApplication - [main,29] - 数据平台 RuoYiApplication 服务启动成功
09:07:50.673 [schedule-pool-1] INFO  sys-user - [run,56] - [127.0.0.1]内网IP[admin][Success][登录成功]
09:10:34.675 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1747789649109 paused.
09:10:34.739 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1747789649109 shutting down.
09:10:34.740 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1747789649109 paused.
09:10:34.744 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1747789649109 shutdown complete.
09:10:34.745 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
09:10:34.796 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
09:10:34.796 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
09:10:34.797 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
09:10:34.797 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
09:10:34.859 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
09:10:34.880 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
