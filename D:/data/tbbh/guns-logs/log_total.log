===2025-04-02 16:48:16.712 INFO  com.stylefeng.guns.GunsApplication Line:55  - Starting GunsApplication on macdeMacBook-Pro.local with PID 85566 (/Users/<USER>/IdeaProjects/dzbh/guns-admin/target/classes started by mac in /Users/<USER>/IdeaProjects/dzbh)
===2025-04-02 16:48:16.715 INFO  com.stylefeng.guns.GunsApplication Line:655 - The following profiles are active: wzy
===2025-04-02 16:48:16.767 INFO  org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor Line:225 - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
===2025-04-02 16:48:16.768 INFO  org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor Line:225 - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
===2025-04-02 16:48:17.266 WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext Line:558 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.stylefeng.guns.GunsApplication]; nested exception is org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'toubApplyService' for bean class [com.stylefeng.guns.modular.index.service.zszh.v1.ToubApplyService] conflicts with existing, non-compatible bean definition of same name and class [com.stylefeng.guns.modular.index.service.lnzj.v1.ToubApplyService]
===2025-04-02 16:48:17.270 INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener Line:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
===2025-04-02 16:48:17.275 ERROR org.springframework.boot.SpringApplication Line:826 - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.stylefeng.guns.GunsApplication]; nested exception is org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'toubApplyService' for bean class [com.stylefeng.guns.modular.index.service.zszh.v1.ToubApplyService] conflicts with existing, non-compatible bean definition of same name and class [com.stylefeng.guns.modular.index.service.lnzj.v1.ToubApplyService]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:184)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:319)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:236)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:275)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:95)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:706)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:532)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.stylefeng.guns.GunsApplication.main(GunsApplication.java:22)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'toubApplyService' for bean class [com.stylefeng.guns.modular.index.service.zszh.v1.ToubApplyService] conflicts with existing, non-compatible bean definition of same name and class [com.stylefeng.guns.modular.index.service.lnzj.v1.ToubApplyService]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:349)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:287)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:132)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:290)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:245)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:202)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:170)
	... 18 common frames omitted
===2025-04-02 16:57:16.313 INFO  com.stylefeng.guns.GunsApplication Line:55  - Starting GunsApplication on macdeMacBook-Pro.local with PID 86395 (/Users/<USER>/IdeaProjects/dzbh/guns-admin/target/classes started by mac in /Users/<USER>/IdeaProjects/dzbh)
===2025-04-02 16:57:16.314 INFO  com.stylefeng.guns.GunsApplication Line:655 - The following profiles are active: zw
===2025-04-02 16:57:16.353 INFO  org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor Line:225 - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
===2025-04-02 16:57:16.353 INFO  org.springframework.boot.devtools.env.DevToolsPropertyDefaultsPostProcessor Line:225 - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
===2025-04-02 16:57:16.590 WARN  org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext Line:558 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.stylefeng.guns.GunsApplication]; nested exception is org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'toubApplyService' for bean class [com.stylefeng.guns.modular.index.service.zszh.v1.ToubApplyService] conflicts with existing, non-compatible bean definition of same name and class [com.stylefeng.guns.modular.index.service.lnzj.v1.ToubApplyService]
===2025-04-02 16:57:16.593 INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener Line:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
===2025-04-02 16:57:16.599 ERROR org.springframework.boot.SpringApplication Line:826 - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.stylefeng.guns.GunsApplication]; nested exception is org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'toubApplyService' for bean class [com.stylefeng.guns.modular.index.service.zszh.v1.ToubApplyService] conflicts with existing, non-compatible bean definition of same name and class [com.stylefeng.guns.modular.index.service.lnzj.v1.ToubApplyService]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:184)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:319)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:236)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:275)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:95)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:706)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:532)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.stylefeng.guns.GunsApplication.main(GunsApplication.java:22)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'toubApplyService' for bean class [com.stylefeng.guns.modular.index.service.zszh.v1.ToubApplyService] conflicts with existing, non-compatible bean definition of same name and class [com.stylefeng.guns.modular.index.service.lnzj.v1.ToubApplyService]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:349)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:287)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:132)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:290)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:245)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:202)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:170)
	... 18 common frames omitted
