ALTER TABLE top_notify
ADD COLUMN `oa_notify_type` char(1) DEFAULT NULL COMMENT 'OA相关的通知，0记账凭证规则，1付款人配置，2收款人配置，3项目与流程关联，4项目名称配置' AFTER `apply_id`,
ADD COLUMN `oa_notify_step` char(1) DEFAULT NULL COMMENT 'OA相关的通知，具体某一个跳转的步骤，1待我审核，2我的提交，3我负责的' AFTER `oa_notify_type`,
ADD COLUMN `oa_apply_id` bigint(20) DEFAULT NULL COMMENT 'OA相关的申请id' AFTER `oa_notify_step`;

-- OA系统配置编辑后审批表 oa_system_disposition_edit_review
CREATE TABLE `oa_system_edit_review` (
  `id` BIGINT(11) NOT NULL AUTO_INCREMENT,
  `project_type` char(2) NOT NULL COMMENT '项目类型，0记账凭证规则，1项目名称配置，2付款人配置，3收款人配置，4项目与名称关联',
  `is_enable` char(1) NOT NULL DEFAULT '0' COMMENT '是否启用，0否，1是',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态，0正常，1禁用',
  `create_by` varchar(32) NOT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(32) NOT NULL COMMENT '更新者',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='OA系统配置编辑后审批表';

-- 所有OA四期需求的五个功能点初始化为关闭状态
INSERT INTO `oa_system_edit_review` (`project_type`, `is_enable`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('0', '0', '0', 'admin', '2023-12-11 11:10:05', 'admin', '2024-03-11 11:10:05');
INSERT INTO `oa_system_edit_review` (`project_type`, `is_enable`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1', '0', '0', 'admin', '2023-12-11 11:10:05', 'admin', '2024-03-11 11:10:05');
INSERT INTO `oa_system_edit_review` (`project_type`, `is_enable`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('2', '0', '0', 'admin', '2023-12-11 11:10:05', 'admin', '2024-03-11 11:10:05');
INSERT INTO `oa_system_edit_review` (`project_type`, `is_enable`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('3', '0', '0', 'admin', '2023-12-11 11:10:05', 'admin', '2024-03-11 11:10:05');
INSERT INTO `oa_system_edit_review` (`project_type`, `is_enable`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('4', '0', '0', 'admin', '2023-12-11 11:10:05', 'admin', '2024-03-11 11:10:05');

-- 记账凭证规则用户表
CREATE TABLE `oa_voucher_rules_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oa_voucher_rules_main_id` int(60) NOT NULL COMMENT '记账凭证主表id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `user_flag` char(1) NOT NULL COMMENT '用户标识，0财务，1业务',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态，0正常，1禁用',
  `create_by` varchar(32) NOT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(32) NOT NULL COMMENT '更新者',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='记账凭证规则用户表';

-- 记账凭证规则记录表
CREATE TABLE `oa_voucher_rules_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oa_voucher_rules_main_id` int(60) NOT NULL COMMENT '记账凭证主表id',
  `data` json NOT NULL COMMENT '保存的数据',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态，0正常，1禁用',
  `create_by` varchar(32) NOT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(32) NOT NULL COMMENT '更新者',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='记账凭证规则记录表';

-- 记账凭证规则编辑审批记录表
CREATE TABLE `oa_voucher_rules_edit_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oa_voucher_rules_main_id` int(60) NOT NULL COMMENT '记账凭证主表id',
  `oa_voucher_rules_records_old_id` bigint(20) DEFAULT NULL COMMENT '记录表修改前id',
  `oa_voucher_rules_records_new_id` bigint(20) DEFAULT NULL COMMENT '记录表修改后id',
  `edit_user_id` bigint(20) NOT NULL COMMENT '编辑人员（存用户id）',
  `edit_time` datetime NOT NULL COMMENT '编辑时间',
  `edit_info` varchar(200) DEFAULT NULL COMMENT '修改说明',
  `check_user_id` bigint(20) COMMENT '审核人（存用户id）',
  `check_time` datetime COMMENT '审批时间',
  `check_status` char(1) NOT NULL COMMENT '审核状态，0待业务审核，1待财务审核',
  `reject_flag` char(1) DEFAULT NULL COMMENT '驳回标识，0通过，1驳回',
  `check_reject_info` varchar(200) COMMENT '驳回原因，当驳回标识为1时，本字段不能为空',
  `confirm_flag` char(1) DEFAULT NULL COMMENT '是否知悉，0未知悉，1已知悉',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='记账凭证规则编辑审批记录表';

-- oa_project_deploy表新增公司字段
ALTER TABLE oa_project_deploy ADD COLUMN `company_no` bigint(20) DEFAULT NULL COMMENT '公司编码' AFTER id;
-- 存量数据默认都是中保
UPDATE oa_project_deploy SET company_no=1;

-- OA编辑审批功能通用用户表
CREATE TABLE `oa_edit_approve_generality_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oa_apply_type` char(1) NOT NULL COMMENT '代表的相关功能。1付款人配置，2收款人配置，3项目与流程关联，4项目名称配置',
  `oa_apply_id` int(60) NOT NULL COMMENT 'OA功能相关的申请id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `user_flag` char(1) NOT NULL COMMENT '用户标识，0财务，1业务',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态，0正常，1禁用',
  `create_by` varchar(32) NOT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(32) NOT NULL COMMENT '更新者',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='OA编辑审批功能通用用户表';

-- OA编辑审批功能通用记录表
CREATE TABLE `oa_edit_approve_generality_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
	`oa_apply_type` char(1) NOT NULL COMMENT '代表的相关功能。1付款人配置，2收款人配置，3项目与流程关联，4项目名称配置',
  `oa_apply_id` int(60) NOT NULL COMMENT 'OA功能相关的申请id',
  `data` json NOT NULL COMMENT '保存的数据',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态，0正常，1禁用',
  `create_by` varchar(32) NOT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(32) NOT NULL COMMENT '更新者',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='OA编辑审批功能通用记录表';

-- OA编辑审批功能通用编辑审批记录表
CREATE TABLE `oa_edit_approve_generality_edit_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oa_apply_type` char(1) NOT NULL COMMENT '代表的相关功能。1付款人配置，2收款人配置，3项目与流程关联，4项目名称配置',
  `oa_apply_id` int(60) NOT NULL COMMENT 'OA功能相关的申请id',
  `oa_apply_records_old_id` bigint(20) DEFAULT NULL COMMENT '记录表修改前id',
  `oa_apply_records_new_id` bigint(20) DEFAULT NULL COMMENT '记录表修改后id',
  `edit_user_id` bigint(20) NOT NULL COMMENT '编辑人员（存用户id）',
  `edit_time` datetime NOT NULL COMMENT '编辑时间',
  `edit_info` varchar(200) DEFAULT NULL COMMENT '修改说明',
  `check_user_id` bigint(20) COMMENT '审核人（存用户id）',
  `check_time` datetime COMMENT '审批时间',
  `check_status` char(1) NOT NULL COMMENT '审核状态，0待业务审核，1待财务审核，2待业务管理员审核',
  `reject_flag` char(1) DEFAULT NULL COMMENT '驳回标识，0通过，1驳回',
  `check_reject_info` varchar(200) COMMENT '驳回原因，当驳回标识为1时，本字段不能为空',
  `confirm_flag` char(1) DEFAULT NULL COMMENT '是否知悉，0未知悉，1已知悉',
  `responsibility_confirm_flag` char(1) DEFAULT NULL COMMENT '责任人是否知悉，0未知悉，1已知悉（项目名称配置用）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='OA编辑审批功能通用编辑审批记录表';

ALTER TABLE oa_project_deploy ADD COLUMN `add_not_approve` char(1) DEFAULT '0' COMMENT '新增未审批标识。当新增未审批过的为9，新增审批过的为0' AFTER update_time;

-- oa_trader表新增公司字段
ALTER TABLE oa_trader ADD COLUMN `company_no` bigint(20) DEFAULT NULL COMMENT '公司编码' AFTER id;
-- 存量数据默认都是中保
UPDATE oa_trader SET company_no=1;

-- oa_trader表新增是否可以编辑字段
ALTER TABLE oa_trader ADD COLUMN `add_not_approve` char(1) DEFAULT '0' COMMENT '编辑后提交之后，展示是否可以编辑的标识，0可以编辑，1不可编辑' AFTER update_time;



-- 对工作流现有的所有流程进行参数的补充
INSERT INTO act_ru_variable 
SELECT
	(SELECT
	UUID()) AS ID_,
	1 AS REV_,
	'string' AS VAR_TYPE_,
	'initiatorTheme' AS NAME_,
	EXECUTION_ID_,
	PROC_INST_ID_,
	a.ID_ AS TASK_ID_,
	NULL AS BYTEARRAY_ID_,
	NULL AS DOUBLE_,
	NULL AS LONG_,
	b.theme AS TEXT_,
	NULL AS TEXT2_
FROM
	act_ru_task a
	INNER JOIN proc_form_data b ON a.PROC_INST_ID_ = b.instance_id;
	
INSERT INTO act_ru_variable 
SELECT
	(SELECT
	UUID()) AS ID_,
	1 AS REV_,
	'long' AS VAR_TYPE_,
	'templateId' AS NAME_,
	EXECUTION_ID_,
	PROC_INST_ID_,
	a.ID_ AS TASK_ID_,
	NULL AS BYTEARRAY_ID_,
	NULL AS DOUBLE_,
	b.template_id AS LONG_,
	b.template_id AS TEXT_,
	NULL AS TEXT2_
FROM
	act_ru_task a
	INNER JOIN proc_form_data b ON a.PROC_INST_ID_ = b.instance_id;

INSERT INTO act_ru_variable 
SELECT
	(SELECT
	UUID()) AS ID_,
	1 AS REV_,
	'long' AS VAR_TYPE_,
	'initiatorUserId' AS NAME_,
	EXECUTION_ID_,
	PROC_INST_ID_,
	a.ID_ AS TASK_ID_,
	NULL AS BYTEARRAY_ID_,
	NULL AS DOUBLE_,
	b.user_id AS LONG_,
	b.user_id AS TEXT_,
	NULL AS TEXT2_
FROM
	act_ru_task a
	INNER JOIN proc_form_data b ON a.PROC_INST_ID_ = b.instance_id;



INSERT INTO act_ru_variable 
SELECT
	(SELECT
	UUID()) AS ID_,
	1 AS REV_,
	'string' AS VAR_TYPE_,
	'templateType' AS NAME_,
	EXECUTION_ID_,
	PROC_INST_ID_,
	a.ID_ AS TASK_ID_,
	NULL AS BYTEARRAY_ID_,
	NULL AS DOUBLE_,
	NULL AS LONG_,
	c.template_type AS TEXT_,
	NULL AS TEXT2_
FROM
	act_ru_task a
	INNER JOIN proc_form_data b ON a.PROC_INST_ID_ = b.instance_id
	LEFT JOIN oa_process_template c ON b.template_id=c.id;