/*
初始化建表语句 - start
*/
CREATE TABLE `sts_income_forecast_loan_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_no` char(2) NOT NULL DEFAULT '0' COMMENT '预留产品代码字段：0-富邦',
  `loan_month` varchar(12) NOT NULL DEFAULT '1999-01' COMMENT '放款月份',
  `loan_amt` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '放款金额',
  `product_type` char(1) NOT NULL DEFAULT '0' COMMENT '0-无，1-随借随还，2-等本等息，3-先息后本',
  `phase` int(3) NOT NULL DEFAULT 0 COMMENT '期数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='收入预测报表-放款信息表';



CREATE TABLE `sts_income_forecast_repayment_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_no` char(2) NOT NULL DEFAULT '0' COMMENT '预留产品代码字段：0-富邦',
  `loan_month` varchar(12) NOT NULL DEFAULT '1999-01' COMMENT '放款月份',
  `product_type` char(1) NOT NULL DEFAULT '0' COMMENT '0-无，1-随借随还，2-等本等息，3-先息后本',
  `phase` int(3) NOT NULL DEFAULT '0' COMMENT '期数',
  `repayment_month` varchar(12) NOT NULL DEFAULT '1999-01' COMMENT '还款月份',
  `repayment_print_amount` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '还款本金',
  `repayment_int_amount` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '还款利息',
  `repayment_oint_amt` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '还款罚息',
  `repayment_fl_amt` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '还款复利',
  `adv_define_amt` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '提前还款违约金',
  `deduct_amt` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '活动抵扣金额',
  `reduce_amt` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '红线减免金额',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='收入预测报表-还款信息表';



CREATE TABLE `sts_income_forecast_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_no` char(2) NOT NULL DEFAULT '0' COMMENT '预留产品代码字段：0-富邦',
  `loan_month` varchar(12) NOT NULL DEFAULT '1999-01' COMMENT '放款月份',
  `repayment_month` varchar(12) NOT NULL DEFAULT '1999-01' COMMENT '还款月份',
  `jt_fr_amt` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '借条分润',
  `cost_of_capital` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '资金成本',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='收入预测报表-导入还款表信息';



CREATE TABLE `sts_income_forecast_info_compensatory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_no` char(2) NOT NULL DEFAULT '0' COMMENT '预留产品代码字段：0-富邦',
  `loan_month` varchar(12) NOT NULL DEFAULT '1999-01' COMMENT '放款月份',
  `compensatory_month` varchar(12) NOT NULL DEFAULT '1999-01' COMMENT '代偿月份',
  `compensate_total_amt` decimal(17,2) NOT NULL DEFAULT '0.00' COMMENT '代偿总金额',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='收入预测报表-收入预测信息表';
/*
初始化建表语句 - end
*/

/*
初始化菜单语句 - start
*/
SELECT @yybbscParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='运营报表生成');

INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('收入预测报表', @yybbscParendId, 3, 'forecast', 'yybbsc/forecastList/index', NULL, 1, 0, 'C', '0', '0', NULL, 'example', 'admin', '2023-01-18 14:33:38', '', NULL, '');

SELECT @srycbbParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='收入预测报表');

INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('收入预测报表列表查询', @srycbbParendId, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'yybbsc:forecast:list', '#', 'admin', '2023-02-12 18:23:59', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('导入对账单', @srycbbParendId, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'yybbsc:forecast:importStaOfAcc', '#', 'admin', '2023-01-28 14:53:07', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('导入还款表', @srycbbParendId, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'yybbsc:forecast:importRepay', '#', 'admin', '2023-01-28 14:53:39', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('导入代偿明细表', @srycbbParendId, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'yybbsc:forecast:importCompensatory', '#', 'admin', '2023-01-28 14:54:15', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('导出收入预测报表', @srycbbParendId, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'yybbsc:forecast:export', '#', 'admin', '2023-01-28 14:54:47', 'admin', '2023-01-30 09:17:22', '');
/*
初始化菜单语句 - end
*/