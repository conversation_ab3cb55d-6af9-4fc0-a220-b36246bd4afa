package com.ruoyi.quartz.wukongsdk.run;

import java.io.File;
import java.lang.reflect.Array;

import com.ruoyi.quartz.wukongsdk.constant.ConfigConstant;
import com.ruoyi.quartz.wukongsdk.core.*;
import com.ruoyi.quartz.wukongsdk.exception.DefineException;
import com.ruoyi.quartz.wukongsdk.util.DeclareMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.timevale.esign.sdk.tech.bean.OrganizeBean;
import com.timevale.esign.sdk.tech.bean.PersonBean;
import com.timevale.esign.sdk.tech.bean.PosBean;
import com.timevale.esign.sdk.tech.bean.SignPDFFileBean;
import com.timevale.esign.sdk.tech.bean.SignPDFStreamBean;
import com.timevale.esign.sdk.tech.bean.result.FileDigestSignResult;
import com.timevale.esign.sdk.tech.bean.seal.OrganizeTemplateType;
import com.timevale.esign.sdk.tech.bean.seal.PersonTemplateType;
import com.timevale.esign.sdk.tech.bean.seal.SealColor;
import com.timevale.esign.sdk.tech.impl.constants.LegalAreaType;
import com.timevale.esign.sdk.tech.impl.constants.OrganRegType;
import com.timevale.esign.sdk.tech.impl.constants.SignType;
import com.timevale.esign.sdk.tech.v3.client.ServiceClient;


/**
 * desciption 运行流程
 *
 * @since 1.7
 */
public class RunProcess {

    private static final Logger LOGGER = LoggerFactory.getLogger(RunProcess.class);
    private static AccountHelper accountHelper;
    private static SealHelper sealHelper;
    private static SignHelper signHelper;
    private static VerifyPDFHelper verifyPDFHelper;
    //private static MobileCodeHelper mobileCodeHelper;
    //private static PdfTemplateHelper pdfTemplateHelper;
    
    /**
     * 场景模拟
     */
    private static int scene = 0;
    
    static {
    	try {
			if(true) {
				//1、注册客户端，全局使用，只需注册一次
	            ClientHelper.registClient();
			}
	            
            //2、获取已初始化的客户端，以便后续正常调用SDK提供的各种服务，全局使用，只需获取一次
            ServiceClient serviceClient = ClientHelper.getServiceClient(ConfigConstant.PROJECT_ID);
            
            //3、实例化辅助类
            accountHelper = new AccountHelper(serviceClient);
            sealHelper = new SealHelper(serviceClient);
            signHelper = new SignHelper(serviceClient);
            verifyPDFHelper = new VerifyPDFHelper(serviceClient);
            //mobileCodeHelper = new MobileCodeHelper(serviceClient);
            //pdfTemplateHelper = new PdfTemplateHelper(serviceClient);
		} catch (DefineException e) {
			e.getE().printStackTrace();
		}
    }
    
    //--------------------------------公有方法 start-------------------------------------

    public static void main(String[] args){

        //提示申明
        DeclareMsg.showImportantMessage();
        
        try {
        	/**创建账户以及印章，可根据实际场景所需进行创建*/
			// 个人客户账户AccountId,可将该AccountId保存到贵司数据库以便日后直接使用,只创建一次即可
			//String personAccountId = addPersonalAcct();

			// 个人客户印章SealData,可将该SealData保存到贵司数据库以便日后直接使用,只创建一次即可
			//String personSealData = addPersonTemplateSeal(personAccountId);

			// 企业客户账户AccountId,可将该AccountId保存到贵司数据库以便日后直接使用,只创建一次即可
			//String organizeAccountId = addOrganizeAcct();

			// 企业客户印章SealData,可将该SealData保存到贵司数据库以便日后直接使用,只创建一次即可
			//String organizeSealData = addOrganizeTemplateSeal(organizeAccountId);
			
			//3、常用场景签署流程演示(只作为场景演示示例，仅供参考)：
			switch (scene) {
			    case 0:
			        LOGGER.info("====>场景演示：接口调用方（平台方）与其个人客户和企业客户进行合同签署（PDF文件本地路径方式）<=====");
			        //doSign_PlatformWithPersonAndOrganize(personAccountId,personSealData,organizeAccountId,organizeSealData);
			        doSign_Platform("C:\\Users\\<USER>\\Desktop\\pdfTemplate.pdf","C:\\Users\\<USER>\\Desktop\\orgpdf.pdf");
					//doSign_entrustFilePlatform("C:\\Users\\<USER>\\Desktop\\wthtTemplate.pdf","C:\\Users\\<USER>\\Desktop\\orgpdf.pdf","C:\\Users\\<USER>\\Desktop\\ptpdf.pdf","河北雄安信易保科技有限公司","91133100MA0E724C7M");
			        break;
			    case 1:
			        LOGGER.info("====>场景演示：个人客户和企业客户进行合同签署（PDF文件字节流方式）<=====");
			        //doSign_PersonWithOrganize(personAccountId,personSealData,organizeAccountId,organizeSealData);
			        break;
			    default:
			        LOGGER.info("====>请选择应用场景<=====");
			        break;
			}
		} catch (DefineException e) {
			e.getE().printStackTrace();
		}


    }
	/**
	 * @description 接口调用方(平台方)进行合同签署(本地文件路径方式)
	 * @param srcPdfPath
	 * 			{@link String} 待签署PDF文件路径
	 * @param platformSignedPdfPath
	 * 			{@link String} 签署后PDF文件路径
	 * @throws DefineException
	 */
    public static void doSign_Platform(String srcPdfPath,String platformSignedPdfPath) throws DefineException{
		// 待签署PDF文件路径
		// 文档名称,此文档名称用于在e签宝服务端记录签署日志时用,非签署后PDF文件中的文件名.若为空则取待签署PDF文件中的文件名称
		String signLogFileName = "";
		// 文档编辑密码,如果待签署PDF文件设置了编辑密码时需要填写编辑密码,否则请传入null
		String ownerPWD = null;
		// 接口调用方(平台方)签署后PDF文件路径
		// 接口调用方(平台方)签署盖章
		doSign_OrganizeByPath(ConfigConstant.ORG_ID, ConfigConstant.ORG_SealData, srcPdfPath,platformSignedPdfPath, "刘牧之");
	}
	/**
	 * @description 接口调用方(平台方)和企业进行合同签署(本地文件路径方式)
	 * @param srcPdfPath
	 * 			{@link String} 待签署PDF文件路径
	 * @param platformSignedPdfPath
	 * 			{@link String} 签署后PDF文件路径
	 * @throws DefineException
	 */
    public static void doSign_entrustFilePlatform(String srcPdfPath,String organizeSignedEntrustPath,String platformSignedPdfPath,String sqr,String cardNum) throws DefineException{
		// 待签署PDF文件路径

		// 接口调用方(企业)签署后PDF文件路径
		String ORG_ID = addOrganizeAcct(sqr, cardNum);
		String ORG_SealData = addOrganizeTemplateSeal(ORG_ID);
		// 接口调用方(企业)签署盖章
		doSign_OrganizeByPath(ORG_ID, ORG_SealData, srcPdfPath,organizeSignedEntrustPath,"甲方盖章");
		// 接口调用方(平台方)签署盖章
		doSign_OrganizeByPath(ConfigConstant.ORG_ID, ConfigConstant.ORG_SealData, organizeSignedEntrustPath,platformSignedPdfPath,"乙方盖章");
	}
    //--------------------------------公有方法 end---------------------------------------
    //--------------------------------私有方法 start-------------------------------------

    // 当前程序所在文件目录
    private static final String ROOT_FOLDER = new File("").getAbsolutePath();
    //文件地址前缀拼接（可根据实际场景自定义）
    private static final String PATH_PREFEX = ROOT_FOLDER + File.separator + "pdf" + File.separator;
    
	/**
	 * @description 演示接口调用方(平台方)与其个人客户和企业客户进行合同签署(本地文件路径方式)
	 * @param //personAccountId
	 * 			{@link String} 个人账户ID
	 * @param //personSealData
	 * 			{@link String} 个人印章Base64
	 * @param //organizeAccountId
	 * 			{@link String} 企业账户ID
	 * @param //organizeSealData
	 * 			{@link String} 企业印章Base64
	 * @throws DefineException
	 */
	private static void doSign_PlatformWithPersonAndOrganize(String srcPdfPath,String platformSignedPdfPath) throws DefineException{
		
		// 待签署PDF文件路径

		// 文档名称,此文档名称用于在e签宝服务端记录签署日志时用,非签署后PDF文件中的文件名.若为空则取待签署PDF文件中的文件名称
		String signLogFileName = "";
		// 文档编辑密码,如果待签署PDF文件设置了编辑密码时需要填写编辑密码,否则请传入null
		String ownerPWD = null;

		// 接口调用方(平台方)签署后PDF文件路径
		// 接口调用方(平台方)签署盖章
		doSign_PlatformByPath(srcPdfPath, platformSignedPdfPath, signLogFileName, ownerPWD);

		// 个人客户签署后PDF文件路径
		//String personSignedPdfPath = PATH_PREFEX + "Signed_Person.pdf";

		// 个人客户签署盖章
		//doSign_PersonByPath(personAccountId, personSealData, platformSignedPdfPath,personSignedPdfPath, signLogFileName, ownerPWD);

		// 企业客户签署后PDF文件路径
		//String organizeSignedPdfPath = PATH_PREFEX + "Signed_Organize.pdf";

		// 企业客户签署盖章
		//doSign_OrganizeByPath(organizeAccountId, organizeSealData, personSignedPdfPath,organizeSignedPdfPath, signLogFileName, ownerPWD);
		// 验证一下所有人都签署后的PDF是否文件正常
		//verifyPDFHelper.localVerifyPdf(organizeSignedPdfPath);

	}
	
	/**
	 * @description 演示个人客户和企业客户进行合同签署（本地文件路径方式）
	 * @param personAccountId
	 * 			{@link String} 个人账户ID
	 * @param personSealData
	 * 			{@link String} 个人印章Base64
	 * @param organizeAccountId
	 * 			{@link String} 企业账户ID
	 * @param organizeSealData
	 * 			{@link String} 企业印章Base64
	 * @throws DefineException
	 * @date 2019年7月8日 上午9:30:47
	 */
	private static void doSign_PersonWithOrganize(String personAccountId,String personSealData,
			String organizeAccountId, String organizeSealData) throws DefineException{
			
		// 待签署PDF文件路径
		String srcPdfPath = PATH_PREFEX + "test.pdf";					
		// 签署后PDF文件名称
		String signedPdfName = "Signed_All.pdf";					
		// 签署后PDF文件保存路径
		String outSignedPdfPath = PATH_PREFEX + signedPdfName;

		// 文档名称,此文档名称用于在e签宝服务端记录签署日志时用,非签署后PDF文件中的文件名.若为空则取待签署PDF文件中的文件名称
		String signLogFileName = "如合同编号或合同名";
		// 文档编辑密码,如果待签署PDF文件设置了编辑密码时需要填写编辑密码,否则请传入null
		String ownerPWD = null;

		
		// 获取个人客户签署时待签署PDF文件的字节流
		byte[] srcPdfBytes = FileHelper.getFileBytes(srcPdfPath);
		
		// 个人客户签署盖章
		FileDigestSignResult personSignResult = doSign_PersonByPDFBytes(personAccountId, personSealData, srcPdfBytes,
				null, signLogFileName, ownerPWD);
		
		// 企业客户签署时的待签署PDF文件字节流,即个人客户签署盖章成功后的PDF文件字节流
		byte[] organizeSrcPdfBytes = personSignResult.getStream();
		
		// 企业客户签署盖章
		FileDigestSignResult organizeSignResult = doSign_OrganizeByPDFBytes(organizeAccountId, organizeSealData, organizeSrcPdfBytes,
				null, signLogFileName, ownerPWD);
		
		// 所有人签署完成后将PDF文件字节流保存为本地PDF文件
		byte[] AllSignedPdfBytes = organizeSignResult.getStream();
		FileHelper.saveFileByStream(AllSignedPdfBytes, outSignedPdfPath);

	}

	/***
	 * <ul>
	 * <li>方法名称：接口调用方(平台方)签署盖章</li>
	 * <li>文件方式：本地文件路径</li>
	 * <li>方法用途：演示接口调用方(平台方)加盖公章</li>
	 * <li>Demo封装方法：doSign_PlatformByPath</li>
	 * </ul>
	 * 
	 * @throws
	 */
	private static void doSign_PlatformByPath(String srcPdfPath, String outPdfPath,
			String signLogFileName, String ownerPWD) throws DefineException {
		// 设置接口调用方(平台方)签署PDF文档信息
		SignPDFFileBean signPDFFileBean = doSetSignPDFFileBean(srcPdfPath, outPdfPath, signLogFileName,
				ownerPWD);
		// 签章类型,Single-单页签章、Multi-多页签章、Edges-骑缝章、Key-关键字签章
		SignType signType = SignType.Single;

		// 签署页码,单页签署时页码格式为"1";若为多页签署时，支持页码格式"1-3,5,8"
		String page = "1";
		// 签署位置X坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面左端的横向移动距离,单位为px
		float posX = 410F;
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
		float posY = 134F;
		// 印章图片在PDF文件中的等比缩放大小,公章标准大小为4.2厘米即159px
		float widthScaling = 159F;

		// 接口调用方(平台方)的印章,请在www.tsign.cn官网中设置默认印章其sealId值为0
		int sealId = 0;
		// 设置接口调用方(平台方)签章位置信息
		PosBean posBean = setPosBean(signType, null, page, posX, posY, widthScaling);

		// 接口调用方(平台方)签署盖章
		signHelper.localSignPDF(signPDFFileBean, posBean, sealId, signType);
	}

	/***
	 * <ul>
	 * <li>方法名称：个人客户签署盖章</li>
	 * <li>文件方式：本地文件路径</li>
	 * <li>方法用途：演示个人客户加盖公章</li>
	 * <li>Demo封装方法：doSign_PersonByPath</li>
	 * </ul>
	 * 
	 * @throws
	 */
	private static void doSign_PersonByPath(String accountId, String sealData, String srcPdfPath,
			String outPdfPath, String signLogFileName, String ownerPWD) throws DefineException {
		// 设置个人客户签署PDF文档信息
		SignPDFFileBean signPDFFileBean = doSetSignPDFFileBean(srcPdfPath, outPdfPath, signLogFileName,
				ownerPWD);
		// 签章类型,Single-单页签章、Multi-多页签章、Edges-骑缝章、Key-关键字签章
		SignType signType = SignType.Key;
		// 关键字
		String key = "乙方签名";
		// 签署位置X坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面左端的横向移动距离,单位为px
		float posX = 105F;
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
		float posY = 5F;
		// 印章图片在PDF文件中的等比缩放大小,公章标准大小为4.2厘米即159px
		float widthScaling = 90F;
		// 印章SealData
		String personSealData = sealData;

		// 设置个人客户签章位置信息
		PosBean posBean = setPosBean(signType, key, null ,posX, posY, widthScaling);
		// 个人客户签署盖章
		signHelper.localSignPDF(accountId, personSealData, signPDFFileBean, posBean, signType);
	}

	/***
	 * <ul>
	 * <li>方法名称：企业客户签署盖章</li>
	 * <li>文件方式：本地文件路径</li>
	 * <li>方法用途：演示企业客户加盖公章</li>
	 * <li>Demo封装方法：doSign_OrganizeByPath</li>
	 * </ul>
	 * 
	 * @throws
	 */
	private static void doSign_OrganizeByPath(String accountId, String sealData,String srcPdfPath, 
			String outPdfPath, String signName) throws DefineException {
		// 文档名称,此文档名称用于在e签宝服务端记录签署日志时用,非签署后PDF文件中的文件名.若为空则取待签署PDF文件中的文件名称
		String signLogFileName = "";
		// 文档编辑密码,如果待签署PDF文件设置了编辑密码时需要填写编辑密码,否则请传入null
		String ownerPWD = null;
		// 设置企业客户签署PDF文档信息
		SignPDFFileBean signPDFFileBean = doSetSignPDFFileBean(srcPdfPath, outPdfPath, signLogFileName,
				ownerPWD);
		// 签章类型,Single-单页签章、Multi-多页签章、Edges-骑缝章、Key-关键字签章
		SignType signType = SignType.Key;
		// 签署页码,单页签署时页码格式为"1";若为多页签署时，支持页码格式"1-3,5,8"
		String page = "1";
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
		float posX = 0;
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
		float posY = 0;
		// 印章图片在PDF文件中的等比缩放大小,公章标准大小为4.2厘米即159px
		float widthScaling = 159F;
		// 印章SealData
		String organizeSealData = sealData;

		// 设置企业客户签章位置信息
		PosBean posBean = setPosBean(signType, signName, page, posX, posY, widthScaling);

		// 企业客户签署盖章
		signHelper.localSignPDF(accountId, organizeSealData, signPDFFileBean, posBean, signType);
	}

	/***
	 * <ul>
	 * <li>方法名称：接口调用方(平台方)签署盖章</li>
	 * <li>文件方式：PDF文件字节流</li>
	 * <li>方法用途：演示接口调用方(平台方)加盖公章</li>
	 * <li>Demo封装方法：doSign_PlatformByPDFBytes</li>
	 * </ul>
	 * 
	 * @return fileDigestSignResult
	 * @throws
	 */
	private static FileDigestSignResult doSign_PlatformByPDFBytes(byte[] pdfBytes, String outPdfPath,
			String signLogFileName, String ownerPWD) throws DefineException {

		// 签署后PDF文件本地保存路径,如果希望签署后依然返回PDF文件字节流时请设置该属性为空
		SignPDFStreamBean signPDFStreamBean = doSetSignPDFStreamBean(pdfBytes, outPdfPath, signLogFileName, ownerPWD);
		
		// 签章类型,Single-单页签章、Multi-多页签章、Edges-骑缝章、Key-关键字签章
		SignType signType = SignType.Single;

		// 签署页码,单页签署时页码格式为"1";若为多页签署时，支持页码格式"1-3,5,8"
		String page = "1";
		// 签署位置X坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面左端的横向移动距离,单位为px
		float posX = 170F;
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
		float posY = 714F;
		// 印章图片在PDF文件中的等比缩放大小,公章标准大小为4.2厘米即159px
		float widthScaling = 159F;

		// 接口调用方(平台方)的印章,请在www.tsign.cn官网中设置默认印章其sealId值为0
		int sealId = 0;
		// 设置接口调用方(平台方)签章位置信息
		PosBean posBean = setPosBean(signType, null, page, posX, posY, widthScaling);

		// 接口调用方(平台方)签署盖章
		FileDigestSignResult fileDigestSignResult = signHelper.localSignPDF(signPDFStreamBean, posBean, sealId, signType);
		return fileDigestSignResult;
	}
	
	/***
	 * <ul>
	 * <li>方法名称：个人客户签署盖章</li>
	 * <li>文件方式：PDF文件字节流</li>
	 * <li>方法用途：演示个人客户加盖公章</li>
	 * <li>Demo封装方法：doSign_PersonByPdfBytes</li>
	 * </ul>
	 * 
	 * @return fileDigestSignResult
	 * @throws
	 */
	private static FileDigestSignResult doSign_PersonByPDFBytes(String accountId, String sealData,
			byte[] pdfBytes, String outPdfPath, String signLogFileName, String ownerPWD) throws DefineException {
		
		// 签署后PDF文件本地保存路径,如果希望签署后依然返回PDF文件字节流时请设置该属性为空
		SignPDFStreamBean signPDFStreamBean = doSetSignPDFStreamBean(pdfBytes, outPdfPath, signLogFileName, ownerPWD);
		
		// 签章类型,Single-单页签章、Multi-多页签章、Edges-骑缝章、Key-关键字签章
		SignType signType = SignType.Key;
		// 关键字
		String key = "乙方签名";
		// 签署位置X坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面左端的横向移动距离,单位为px
		float posX = 105F;
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
		float posY = 5F;
		// 印章图片在PDF文件中的等比缩放大小,公章标准大小为4.2厘米即159px
		float widthScaling = 90F;
		// 印章SealData
		String personSealData = sealData;

		// 设置个人客户签章位置信息
		PosBean posBean = setPosBean(signType, key, null, posX, posY, widthScaling);
		// 个人客户签署盖章
		FileDigestSignResult fileDigestSignResult = signHelper
				.localSignPDF(accountId, personSealData, signPDFStreamBean, posBean, signType);
		return fileDigestSignResult;
	}
	
    
    
    /**
     * @description 企业客户签署盖章
     * @param accountId
     * 			{@link String} 签署人账号标识
     * @param sealData
     * 			{@link String} 印章base64
     * @param pdfBytes
     * 			{@link Array} pdf字节数组
     * @param outPdfPath
     * 			{@link String} 签署后目标路径
     * @param signLogFileName
     * 			{@link String} 文档名称
     * @param ownerPWD
     * 			{@link String} 文档密码
     * @return
     * @throws DefineException
     */
    private static FileDigestSignResult doSign_OrganizeByPDFBytes(String accountId, String sealData,
			byte[] pdfBytes, String outPdfPath, String signLogFileName, String ownerPWD) throws DefineException {
		
		// 签署后PDF文件本地保存路径,如果希望签署后依然返回PDF文件字节流时请设置该属性为空
		SignPDFStreamBean signPDFStreamBean = doSetSignPDFStreamBean(pdfBytes, outPdfPath, signLogFileName, ownerPWD);
		
		// 签章类型,Single-单页签章、Multi-多页签章、Edges-骑缝章、Key-关键字签章
		SignType signType = SignType.Single;
		// 签署页码,单页签署时页码格式为"1";若为多页签署时，支持页码格式"1-3,5,8"
		String page = "1";
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
		float posX = 120F;
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
		float posY = 100F;
		// 印章图片在PDF文件中的等比缩放大小,公章标准大小为4.2厘米即159px
		float widthScaling = 159F;
		// 印章SealData
		String organizeSealData = sealData;

		// 设置企业客户签章位置信息
		PosBean posBean = setPosBean(signType, null, page, posX, posY, widthScaling);

		// 企业客户签署盖章
		FileDigestSignResult fileDigestSignResult = signHelper.localSignPDF(accountId, organizeSealData, signPDFStreamBean, posBean, signType);
		return fileDigestSignResult;
	}
    
    /**
     * description 设置签章位置信息
     *
     * @param signType
     *           {@link SignType} 签署类型
     * @param page
     *          {@link String} 签署页码，单页签署为"1"，多页签署为类似“1-3,5，8"
     * @param posX
     *          {@link Float} 签署位置的X坐标，默认值0，以pdf页码的左下角为原点，
     *                         控制距离页码左端的横向移动距离，单位为px
     * @param posY
     *          {@link Float} 签署位置的Y坐标，默认值0，以pdf页码的左下角为原点，
     *                         控制距离页码左端的横向移动距离，单位为px
     * @param width
     *          {@link Float} 印章图片在pdf文件中的等比缩放大小，公章标准大小为4.2cm，即159px
     * @return
     *          {@link PosBean} 签署位置信息对象
     **/
    private static PosBean setPosBean(SignType signType, String key, String page, float posX, float posY,
                                      float width) {
        PosBean posBean = new PosBean();
        posBean.setPosType(SignType.Key == signType ? 1 : 0);
        posBean.setPosPage(page);
        posBean.setKey(key);
        posBean.setPosX(posX);
        posBean.setPosY(posY);
        posBean.setWidth(width);
        return posBean;
    }
    
    /**
     * @description 创建个人账户
     * @return
     * 		{@link String} 个人账户
     * @date 2019年7月5日 下午2:26:31
     * <AUTHOR>
     * @throws Exception 
     */
    private static String addPersonalAcct() throws DefineException {
    	PersonBean personBean = new PersonBean();
		// 姓名
		personBean.setName("张三");
		// 证件号码
		personBean.setIdNo("33010XXXXX1X");
		// 用于接收签署验证码的手机号码,可空
		// personBean.setMobile("");

		// 个人归属地：
		// MAINLAND-大陆身份证|HONGKONG-香港居民来往内地通行证|MACAO-澳门居民来往内地通行证|TAIWAN-台湾居民来往大陆通行证
		// PASSPORT-中国护照|FOREIGN-外籍证件|OTHER-其他
		personBean.setPersonArea(LegalAreaType.MAINLAND);

		// 所属公司,可空
		// personBean.setOrgan("XX有限公司");
		// 职位,可空
		// personBean.setTitle("部门经理");

		// 个人客户账户AccountId
        return accountHelper.addAccount(personBean);
    }
    
    /**
     * @description 创建企业账号
     * @return
     * 		{@link String} 企业账号
     * @throws Exception
     */
    private static String addOrganizeAcct(String name,String orgCode)throws DefineException{
    	OrganizeBean organizeBean = new OrganizeBean();
		// 企业名称
		organizeBean.setName(name);
		// 单位类型，0-普通企业，1-社会团体，2-事业单位，3-民办非企业单位，4-党政及国家机构
		organizeBean.setOrganType(0);
		// 企业注册类型，NORMAL:组织机构代码号，MERGE：多证合一，传递社会信用代码号,REGCODE:企业工商注册码,默认NORMAL
		organizeBean.setRegType(OrganRegType.MERGE);
		// 组织机构代码号、社会信用代码号或工商注册号
		organizeBean.setOrganCode(orgCode);//
		// 用于接收签署验证码的手机号码,可空
		// organizeBean.setMobile("");

		// 公司地址,可空
		// organizeBean.setAddress("杭州城落霞峰7号");
		// 经营范围,可空
		// organizeBean.setScope("");

		// 注册类型,1-代理人注册,2-法人注册,0-缺省注册无需法人或代理人信息
		int userType = 0;
		switch (userType) {
		case 0:
			// 0-缺省注册无需法人或代理人信息
			organizeBean.setUserType(0);
			break;
		case 1:
			// 1-代理人注册
			organizeBean.setUserType(1);
			// 代理人姓名，当注册类型为1时必填
			organizeBean.setAgentName("艾利");
			// 代理人身份证号，当注册类型为1时必填
			organizeBean.setAgentIdNo("2203011XXXX70035");
			break;
		case 2:
			// 2-法人注册
			organizeBean.setUserType(2);
			// 法定代表姓名，当注册类型为2时必填
			organizeBean.setLegalName("天云");
			// 法定代表人归属地,0-大陆，1-香港，2-澳门，3-台湾，4-外籍，默认0
			organizeBean.setLegalArea(0);
			// 法定代表身份证号/护照号，当注册类型为2时必填
			organizeBean.setLegalIdNo("22030XXXXX019");
			break;
		}

		// // 企业客户账户AccountId
    	return accountHelper.addAccount(organizeBean);
    }
    
    
    
    /**
     * @description 创建个人客户模板印章
     * @param accountId
     * 				{@link String} 个人账号标识 
     * @return
     * @throws Exception
     * @throws DefineException 
     */
    private static String addPersonTemplateSeal(String accountId) 
    		throws DefineException{
    	
    	// 印章模板类型,可选SQUARE-正方形印章 | RECTANGLE-矩形印章 | BORDERLESS-无框矩形印章
		PersonTemplateType personTemplateType = PersonTemplateType.RECTANGLE;

		// 印章颜色：RED-红色 | BLUE-蓝色 | BLACK-黑色
		SealColor sealColor = SealColor.BLACK;

		// 个人模板印章SealData
    	return sealHelper.addTemplateSeal(accountId, personTemplateType, sealColor);
    }
    
    
    
    /**
     * @description 创建企业客户模板印章
     * @param accountId
     * 			{@link String} 待创建印章的账户标识
     * @return
     * @throws DefineException
     */
    private static String addOrganizeTemplateSeal(String accountId) 
    		throws DefineException{
    	
    	// 印章模板类型,可选STAR-标准公章 | DEDICATED-圆形无五角星章 | OVAL-椭圆形印章
		OrganizeTemplateType organizeTemplateType = OrganizeTemplateType.STAR;

		// 印章颜色：RED-红色 | BLUE-蓝色 | BLACK-黑色
		SealColor sealColor = SealColor.RED;

		// hText 生成印章中的横向文内容 如“合同专用章、财务专用章”
		String hText = null;

		// qText 生成印章中的下弦文内容 如公章防伪码（一般为13位数字）
		String qText = null;

		// 企业模板印章SealData
    	return sealHelper.addTemplateSeal(accountId, organizeTemplateType, sealColor, 
    			hText, qText);
    }
    
    
    
    /**
     * @description 创建个人客户模板印章（本地）
     * @return
     * @throws DefineException
     */
    private static String addLocalPersonTemplateSeal() 
    		throws DefineException{
    	
		// 印章模板类型,可选SQUARE-正方形印章 | RECTANGLE-矩形印章 | BORDERLESS-无框矩形印章
		PersonTemplateType personTemplateType = PersonTemplateType.RECTANGLE;

		// 印章颜色：RED-红色 | BLUE-蓝色 | BLACK-黑色
		SealColor sealColor = SealColor.RED;

		// 待创建印章的内容文本(个人客户名称)
		String personName = "欣哲";

		// 个人模板印章SealData
    	
    	return sealHelper.addTemplateSeal(personTemplateType, personName, sealColor);
    }
    
    
    
    /**
     * @description 创建企业客户模板印章（本地）
     * @return
     * @throws DefineException
     */
    private static String addLocalOrganTemplateSeal() 
    		throws DefineException{
    	
    	// 印章模板类型,可选STAR-标准公章 | DEDICATED-圆形无五角星章 | OVAL-椭圆形印章
		OrganizeTemplateType organizeTemplateType = OrganizeTemplateType.STAR;

		// 印章颜色：RED-红色 | BLUE-蓝色 | BLACK-黑色
		SealColor sealColor = SealColor.RED;

		// roundText 生成印章中的上弦文(企业客户名称)
		String roundText = "天之云信息科技有限公司";

		// hText 生成印章中的横向文内容 如“合同专用章、财务专用章”
		String hText = "合同专用章";

		// qText 生成印章中的下弦文内容 如公章防伪码（一般为13位数字）
		String qText = "9101008613560";

		// 企业模板印章SealData
    	return sealHelper.addTemplateSeal(organizeTemplateType, roundText, hText, 
    			qText, sealColor);
    }
    
    
    /**
     * @description 设置签署PDF文档信息（文件流方式）
     * @param pdfBytes 
     * 			{@link Array} 待签署PDF文件字节流
     * @param outPdfPath
     * 			{@link String} 签署后PDF文件本地保存路径,如果希望签署后依然返回PDF文件字节流时请设置该属性为空
     * @param signLogFileName 
     * 			{@link String} 文档名称,此文档名称用于在e签宝服务端记录签署日志时用,非签署后PDF文件中
     * 							的文件名.若为空则取待签署PDF文件中的文件名称
     * @param ownerPWD
     * 			{@link String} 文档编辑密码,如果待签署PDF文件设置了编辑密码时需要填写编辑密码,否则请传入null
     * @return
     */
    private static SignPDFStreamBean doSetSignPDFStreamBean(byte[] pdfBytes, String outPdfPath, String signLogFileName,
			String ownerPWD) {
		SignPDFStreamBean signPDFStreamBean = new SignPDFStreamBean();
		signPDFStreamBean.setStream(pdfBytes);
		signPDFStreamBean.setDstPdfFile(outPdfPath);
		signPDFStreamBean.setFileName(signLogFileName);
		signPDFStreamBean.setOwnerPassword(ownerPWD);
		return signPDFStreamBean;
	}
    
    /**
     * @description 设置签署PDF文档信息
     * @param srcPdfPath
     * 			{@link String} 待签署PDF文件本地路径
     * @param outPdfPath
     * 			{@link String} 签署后PDF文件本地保存路径
     * @param signLogFileName
     * 			{@link String} 文档名称,此文档名称用于在e签宝服务端记录签署日志时用,
     * 							非签署后PDF文件中的文件名.若为空则取待签署PDF文件中的文件名称
     * @param ownerPWD
     * 			{@link String} 文档编辑密码,如果待签署PDF文件设置了编辑密码时需要填写编辑密码,否则请传入null
     * @return
     */
    private static SignPDFFileBean doSetSignPDFFileBean(String srcPdfPath, String outPdfPath, String signLogFileName,
			String ownerPWD) {
		SignPDFFileBean signPDFFileBean = new SignPDFFileBean();
		signPDFFileBean.setSrcPdfFile(srcPdfPath);
		signPDFFileBean.setDstPdfFile(outPdfPath);
		signPDFFileBean.setFileName(signLogFileName);
		signPDFFileBean.setOwnerPassword(ownerPWD);
		return signPDFFileBean;
	}
    //--------------------------------私有方法 end---------------------------------------

    
    
    
    
    
    

    /**
	 * @description 接口调用方(平台方)进行合同签署(本地文件路径方式)
	 * @param srcPdfPath
	 * 			{@link String} 待签署PDF文件路径
	 * @param platformSignedPdfPath
	 * 			{@link String} 签署后PDF文件路径
	 * 
	 * @throws DefineException
	 */
    public static void doSign_Platform_Custom(String srcPdfPath,String platformSignedPdfPath,String page,float posX,float posY) throws DefineException{
		// 待签署PDF文件路径
		// 文档名称,此文档名称用于在e签宝服务端记录签署日志时用,非签署后PDF文件中的文件名.若为空则取待签署PDF文件中的文件名称
		String signLogFileName = "";
		// 文档编辑密码,如果待签署PDF文件设置了编辑密码时需要填写编辑密码,否则请传入null
		String ownerPWD = null;
		// 接口调用方(平台方)签署后PDF文件路径
		// 接口调用方(平台方)签署盖章
		doSign_OrganizeByPath_Custom(ConfigConstant.ORG_ID, ConfigConstant.ORG_SealData, srcPdfPath,platformSignedPdfPath, "", page, posX, posY);
	}
    
    
    /***
	 * <ul>
	 * <li>方法名称：企业客户签署盖章</li>
	 * <li>文件方式：本地文件路径</li>
	 * <li>方法用途：演示企业客户加盖公章</li>
	 * <li>Demo封装方法：doSign_OrganizeByPath</li>
	 * </ul>
	 * 
	 * @throws
	 */
    private static void doSign_OrganizeByPath_Custom(String accountId, String sealData,String srcPdfPath, 
			String outPdfPath, String signName,String page,float posX,float posY) throws DefineException {
		// 文档名称,此文档名称用于在e签宝服务端记录签署日志时用,非签署后PDF文件中的文件名.若为空则取待签署PDF文件中的文件名称
		String signLogFileName = "";
		// 文档编辑密码,如果待签署PDF文件设置了编辑密码时需要填写编辑密码,否则请传入null
		String ownerPWD = null;
		// 设置企业客户签署PDF文档信息
		SignPDFFileBean signPDFFileBean = doSetSignPDFFileBean(srcPdfPath, outPdfPath, signLogFileName,
				ownerPWD);
		// 签章类型,Single-单页签章、Multi-多页签章、Edges-骑缝章、Key-关键字签章
		SignType signType = SignType.Single;
		// 签署页码,单页签署时页码格式为"1";若为多页签署时，支持页码格式"1-3,5,8"
//		String page = "1";
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
//		float posX = 0;
		// 签署位置Y坐标,默认值为0,以pdf页面的左下角作为原点,控制距离页面底端的纵向移动距离,单位为px
//		float posY = 0;
		// 印章图片在PDF文件中的等比缩放大小,公章标准大小为4.2厘米即159px
		float widthScaling = 159F;
		// 印章SealData
		String organizeSealData = sealData;

		// 设置企业客户签章位置信息
		PosBean posBean = setPosBean(signType, signName, page, posX, posY, widthScaling);

		// 企业客户签署盖章
		signHelper.localSignPDF(accountId, organizeSealData, signPDFFileBean, posBean, signType);
	}
}


