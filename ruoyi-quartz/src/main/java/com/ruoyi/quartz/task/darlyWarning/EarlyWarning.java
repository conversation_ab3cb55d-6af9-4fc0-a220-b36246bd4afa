package com.ruoyi.quartz.task.darlyWarning;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.SymbolEnum;
import com.ruoyi.quartz.util.MailUtil;
import com.ruoyi.system.domain.DEarlyWarning;
import com.ruoyi.system.service.IDEarlyWarningService;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.service.ITopNotifyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class EarlyWarning {

    private static final Logger log = LoggerFactory.getLogger(EarlyWarning.class);
    private String subject = "数据平台预警告警通知";


    @Autowired
    private ITopNotifyService topNotifyService;
    @Autowired
    IDEarlyWarningService idEarlyWarningService;
    @Autowired
    private ISysUserService Userservice;

    /**
     * 查询设置预警
     */
    public void setEarlyWarning() throws Exception {
        DEarlyWarning dEarlyWarning = new DEarlyWarning();
        dEarlyWarning.setStatus("0");
        List<DEarlyWarning> dEarlyWarnings = idEarlyWarningService.selectDEarlyWarningList(dEarlyWarning);
        List<Long> longIds = new ArrayList<>();
        List<Long> longIdsEc = new ArrayList<>();
        Long id = 0l;
        //返回值
        List<DEarlyWarning> dEarlyWarnings1 = new ArrayList<>();
        List<DEarlyWarning> dEarlyWarnings2 = new ArrayList<>();
        Set<String> stringSet = new HashSet<String>();
        String s = null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        Date date = new Date();
        String format = simpleDateFormat.format(date);
        String biaoti = subject + format;
        for (DEarlyWarning earlyWarning : dEarlyWarnings) {
            String querySql = earlyWarning.getQuerySql();
            String substring = querySql.substring(querySql.length() - 1, querySql.length());
            if (substring.equals(";")) {
                querySql = querySql.substring(0, querySql.length() - 1);
            }
            String criticalConditions = earlyWarning.getCriticalConditions();
            String criticalValue = earlyWarning.getCriticalValue();
            String symbolEnumInfo = SymbolEnum.getSymbolEnumInfo(criticalConditions);
            String sql = "SELECT IF((" + querySql + ")" + " " + symbolEnumInfo + " " + criticalValue + "," + "'true','forse');";
            id = earlyWarning.getId();
            longIds.add(id);
            try {
                s = idEarlyWarningService.querySql(sql);
                id = earlyWarning.getId();
                longIdsEc.add(id);
                if (s.equals("true")) {
                    Object sqlinfo = idEarlyWarningService.querySqlValue(querySql);
                    String value = sqlinfo.toString();
                    DEarlyWarning dEarlyWarning2 = new DEarlyWarning();
                    String remark = earlyWarning.getRemark();
                    /*  预警IDXX告警：“sql查询结果”+“，说明：”+“该预警规则备注”*/
                    String returned = "预警ID" + id + "告警:" + value + ",说明：" + remark + "。\r\n";
                    System.out.println(returned);
                    String address = earlyWarning.getAddress();
                    dEarlyWarning2.setRemark(returned);
                    dEarlyWarning2.setAddress(address);
                    dEarlyWarnings1.add(dEarlyWarning2);
                    stringSet.add(address);
                }
            } catch (Exception e) {
                log.error("sql运行异常  - ：", e);
                /*  throw    new RuntimeException(e);*/
            }
        }
        for (String warning : stringSet) {
            String remark = "";
            for (DEarlyWarning earlyWarning : dEarlyWarnings1) {
                if (warning.equals(earlyWarning.getAddress())) {
                    remark += earlyWarning.getRemark();
                }
            }
            DEarlyWarning dEarlyWarning2 = new DEarlyWarning();
            dEarlyWarning2.setRemark(remark);
            dEarlyWarning2.setAddress(warning);
            dEarlyWarnings2.add(dEarlyWarning2);
        }
        for (DEarlyWarning earlyWarning : dEarlyWarnings2) {
            MailUtil.sendMailBySSL(biaoti, earlyWarning.getRemark(), earlyWarning.getAddress());
        }
        String ids = "/";
        for (Long longId : longIds) {
            if (!longIdsEc.contains(longId)) {
                ids += longId.toString() + "/";
            }
        }
        if (!ids.equals("/")) {
            TopNotify topNotify = new TopNotify();
            topNotify.setStatus("0");
            topNotify.setNotifyType("1");
            topNotify.setViewFlag("0");
            //管理员
            SysUser sysUsers = Userservice.selectUserByUserName("admin");
            topNotify.setDisposeUser(sysUsers.getUserId());
            topNotify.setNotifyMsg("主键为" + ids + "的数据运行SQL时出现异常");
            topNotify.setCreateTime(new Date());
            topNotify.setUpdateTime(new Date());
            topNotify.setButtonType("1");
            //必填
            topNotify.setProjectId(0l);
            topNotify.setIncomeId(0l);
            topNotify.setUrl("/");
            topNotify.setNotifyModule("数据平台");
            topNotifyService.insertTopNotify(topNotify);
            ids = "/";
        }
        log.info("邮箱已发送");
    }

    private void setTopNotify() {
        TopNotify topNotify = new TopNotify();
        topNotify.setStatus("0");
        topNotify.setNotifyType("1");
        topNotify.setViewFlag("0");
        //管理员
        SysUser sysUsers = Userservice.selectUserByUserName("admin");
        topNotify.setDisposeUser(sysUsers.getUserId());
        topNotify.setNotifyMsg("wenan");
        topNotify.setCreateTime(new Date());
        topNotifyService.insertTopNotify(topNotify);
    }
}
