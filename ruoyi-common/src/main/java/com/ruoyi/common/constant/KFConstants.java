package com.ruoyi.common.constant;

/**
 * @Author: 左东冉
 * @Create: 2024-09-29 11:27
 * @Description: 客服管理常量
 **/
public class KFConstants {
    /**
     * 客服公司列表
     */
    public static final String COMPANY_LIST_DICT_TYPE = "kf_company_list";

    /**
     * 工单状态常量
     */
    // 待认领
    public static final String WORK_READY = "READY";
    // 处理中
    public static final String WORK_LOAD = "LOAD";
    // 已完成
    public static final String WORK_COMPLETE = "COMPLETE";
    // 不处理
    public static final String WORK_ABANDON = "ABANDON";

    /**
     * 逻辑删除状态
     */
    // 生效
    public static final int LJSC_STATUS_ZREO = 0;
    // 失效
    public static final int LJSC_STATUS_ONE = 1;

    /**
     * 工单记录常量
     */
    // 提交工单
    public static final int WORK_OPTION_TJGD = 1;
    // 认领工单
    public static final int WORK_OPTION_RLGD = 2;
    // 放弃认领
    public static final int WORK_OPTION_FQRL = 3;
    // 发布工作记录
    public static final int WORK_OPTION_FBGZJL = 4;
    // 不处理
    public static final int WORK_OPTION_BCL = 5;
    // 完成工单
    public static final int WORK_OPTION_WCGD = 6;
    // 客户备注
    public static final int WORK_OPTION_KHBZ = 7;


    /**
     * 工作记录和客户备注删除标志
     */
    // 工作记录删除标志
    public static final String WORK_DELETE_RECORD = "RECORD";
    // 客户备注删除备注
    public static final String WORK_DELETE_REMARK = "REMARK";


    /**
     * 会话ID redis存储前缀
     */
    public static final String AES_HHID = "AES_UUID:";
}
