package com.ruoyi.common.utils;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

public class ExcelVerifyUtils {
	
	/**
	 * 解析Excel 
	 * @param inputStream
	 * @return
	 * @throws Exception
	 */
    public static Map<String, Map<String, Map<String, String>>> analysisExcel(InputStream inputStream) throws Exception {
        Workbook workbook = WorkbookFactory.create(inputStream);
        Map<String, Map<String, Map<String, String>>> resultMap = new HashMap<>();
        Map<String, Map<String, String>> sheetDataMap = new HashMap<>();
        Map<String, String> dataMap = new HashMap<>();//通过指定行列获取单元格内容
        Map<String, Map<String, String>> sheetFieldMap = new HashMap<>();
        Map<String, String> fieldMap = new HashMap<>();//通过名称获取单元格行列
        Iterator<Sheet> sheetIterator = workbook.sheetIterator();
        Integer aa = 0;
        while (sheetIterator.hasNext()) {
        	aa++;
            Sheet sheet = sheetIterator.next();
            if (sheet == null) {
                if (aa == 1) {
                    throw new IOException("文件sheet不存在");
                }
                break;
            }
            dataMap = new HashMap<>();
            fieldMap = new HashMap<>();
            
            int rows = sheet.getPhysicalNumberOfRows();
            if (rows > 0) {
                //遍历所有行 所有列
                for (int i = 0; i < rows; i++) {
                    Row row = sheet.getRow(i);
                    int sss=0;
                    try {
//                        sss = row.getPhysicalNumberOfCells();
                        sss = row.getLastCellNum();
					} catch (Exception e) {
						continue;
					}
                    DataFormatter formatter = new DataFormatter();
                    for (int j = 0; j < sss; j++) {
                    	//判断是否为公式或数字，进行格式化处理，保留2位小数：
                    	if(row.getCell(j)!=null &&  (row.getCell(j).getCellType()==CellType.FORMULA || row.getCell(j).getCellType()==CellType.NUMERIC)){
                    		String s="";
                    		try {
                        		s=new BigDecimal(row.getCell(j).getNumericCellValue()).setScale(2, RoundingMode.HALF_UP)+"";
							} catch (Exception e) {
//								e.printStackTrace();
								s = formatter.formatCellValue(row.getCell(j, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL));
								//去除空格换行等特殊字符
	                            s=cleanerSpecialCharacter(s);
							}
//                        	System.out.println(">>>>>>>>>>");
//                        	System.out.println(String.valueOf(i+1)+","+String.valueOf(j+1) +"  :"+s);
                    		//“行，列”形式存储位置关系
                            dataMap.put(String.valueOf(i+1)+","+String.valueOf(j+1), s);
                            fieldMap.put(s, String.valueOf(i+1)+","+String.valueOf(j+1));
                    	}else {
                            String s = formatter.formatCellValue(row.getCell(j, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL));
                            //去除空格换行等特殊字符
                            s=cleanerSpecialCharacter(s);
                            //“行，列”形式存储位置关系
//                        	System.out.println(">>>>>>>>>>");
//                        	System.out.println(String.valueOf(i+1)+","+String.valueOf(j+1) +"  :"+s);
                            dataMap.put(String.valueOf(i+1)+","+String.valueOf(j+1), s);
                            fieldMap.put(s, String.valueOf(i+1)+","+String.valueOf(j+1));
                    	}
                    }
                }
            }
            sheetDataMap.put(cleanerSpecialCharacter(sheet.getSheetName()), dataMap);
            sheetFieldMap.put(cleanerSpecialCharacter(sheet.getSheetName()), fieldMap);
        }
        workbook.close();
        inputStream.close();
        
        resultMap.put("sheetDataMap", sheetDataMap);
        resultMap.put("sheetFieldMap", sheetFieldMap);
        return resultMap;
    }
    
    /**
	 * 去除特殊字符
	 * @param str
	 * @return
	 */
    public static String cleanerSpecialCharacter(String str) {
		str=str.replaceAll(" ", "");//去除空格
		str=str.replaceAll("　", "");//去除
		str=str.replaceAll("	", "");//去除table制表符
    	str=str.trim();//去除前后空格
		str=str.replaceAll("[\t\r\n]", "");//去除回车换行符
		return str;
	}
    
    
    /**
	 * 获取规则中sheet页名称
	 * @param str
	 * @return
	 */
    public static String getSheetName(String str) {
		return str.substring(0,str.indexOf("."));
	}
	/**
	 * 获取规则中单元格名称
	 * @param str
	 * @return
	 */
    public static String getCellName(String str) {
		return str.substring(str.indexOf(".")+1,str.indexOf("【"));
	}
	/**
	 * 获取规则中单元格名称后的取值偏移量
	 * @param str
	 * @return
	 */
    public static String getCellNumber(String str) {
		return str.substring(str.indexOf("【")+1,str.indexOf("】"));
	}
}
