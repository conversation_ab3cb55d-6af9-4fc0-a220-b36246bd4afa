package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import com.ruoyi.common.core.domain.BaseEntity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 角色表 sys_role
 *
 * <AUTHOR>
 */
public class SysRole extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 角色ID */
    @Excel(name = "角色序号", cellType = ColumnType.NUMERIC)
    private Long roleId;

    /** 角色名称 */
    @Excel(name = "角色名称")
    private String roleName;

    /** 角色权限 */
    @Excel(name = "权限字符")
    private String roleKey;

    /** 角色排序 */
    @Excel(name = "角色排序")
    private String roleSort;

    /** 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限） */
    @Excel(name = "数据范围", readConverterExp = "1=所有数据权限,2=自定义数据权限,3=本部门数据权限,4=本部门及以下数据权限,5=自定义部门权限,6=仅本人数据权限,7=本公司权限,8=自定义公司权限")
    private String dataScope;

    /** 菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示） */
    private boolean menuCheckStrictly;

    /** 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ） */
    private boolean deptCheckStrictly;

    /** OA流程树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示） */
    private boolean oaCheckStrictly;

    /** 角色状态（0正常 1停用） */
    @Excel(name = "角色状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 用户是否存在此角色标识 默认不存在 */
    private boolean flag = false;

    /** 菜单组 */
    private Long[] menuIds;


    /** OAFlow组 */
    private String[] oaflowIds;

    /** 部门组（数据权限） */
    private Long[] deptIds;

    /** 全量公司组（数据权限） */
    private Long[] unitIds;

    public Long[] getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(Long[] unitIds) {
        this.unitIds = unitIds;
    }

    /** 角色类型 0-菜单角色 1-数据角色 2-oa角色 */
    private String roleType;

    /** 排序字段 */
    private String orderField;

    /** 用户名称 */
    private String userName;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 开始时间
     */
    private String startTime;

    /** 外部系统数据授权数量 */
    private Map<String,Integer> systemDataCounts;

    /** 模块 */
    private List<SysMenu> menuList;
    /** 人员 */
    private List<SysUser> userList;
    /** 流程 */
    private List<SysRoleOa> roleOaList;

    /**
     * 部门
     */
    private List<SysRoleDept> roleDept;

    /**
     * 全量公司
     */
    private List<SysRoleUnit> roleUnit;

    private List<SysRoleData> tableData;


    public SysRole()
    {

    }

    public List<SysRoleUnit> getRoleUnit() {
        return roleUnit;
    }

    public void setRoleUnit(List<SysRoleUnit> roleUnit) {
        this.roleUnit = roleUnit;
    }

    public List<SysRoleDept> getRoleDept() {
        return roleDept;
    }

    public void setRoleDept(List<SysRoleDept> roleDept) {
        this.roleDept = roleDept;
    }

    public SysRole(Long roleId)
    {
        this.roleId = roleId;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public Long getRoleId()
    {
        return roleId;
    }

    public void setRoleId(Long roleId)
    {
        this.roleId = roleId;
    }

    public boolean isAdmin()
    {
        return isAdmin(this.roleId);
    }

    public static boolean isAdmin(Long roleId)
    {
        return roleId != null && 1L == roleId;
    }

    @NotBlank(message = "角色名称不能为空")
    @Size(min = 0, max = 30, message = "角色名称长度不能超过30个字符")
    public String getRoleName()
    {
        return roleName;
    }

    public void setRoleName(String roleName)
    {
        this.roleName = roleName;
    }

    @NotBlank(message = "权限字符不能为空")
    @Size(min = 0, max = 100, message = "权限字符长度不能超过100个字符")
    public String getRoleKey()
    {
        return roleKey;
    }

    public void setRoleKey(String roleKey)
    {
        this.roleKey = roleKey;
    }

    @NotBlank(message = "显示顺序不能为空")
    public String getRoleSort()
    {
        return roleSort;
    }

    public void setRoleSort(String roleSort)
    {
        this.roleSort = roleSort;
    }

    public String getDataScope()
    {
        return dataScope;
    }

    public void setDataScope(String dataScope)
    {
        this.dataScope = dataScope;
    }

    public boolean isMenuCheckStrictly()
    {
        return menuCheckStrictly;
    }

    public void setMenuCheckStrictly(boolean menuCheckStrictly)
    {
        this.menuCheckStrictly = menuCheckStrictly;
    }

    public boolean isDeptCheckStrictly()
    {
        return deptCheckStrictly;
    }

    public void setDeptCheckStrictly(boolean deptCheckStrictly)
    {
        this.deptCheckStrictly = deptCheckStrictly;
    }

    public boolean isOaCheckStrictly()
    {
        return oaCheckStrictly;
    }

    public void setOaCheckStrictly(boolean oaCheckStrictly)
    {
        this.oaCheckStrictly = oaCheckStrictly;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public boolean isFlag()
    {
        return flag;
    }

    public void setFlag(boolean flag)
    {
        this.flag = flag;
    }

    public Long[] getMenuIds()
    {
        return menuIds;
    }

    public void setMenuIds(Long[] menuIds)
    {
        this.menuIds = menuIds;
    }

    public Long[] getDeptIds()
    {
        return deptIds;
    }

    public void setDeptIds(Long[] deptIds)
    {
        this.deptIds = deptIds;
    }

    public String getRoleType() {
		return roleType;
	}

	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}

	public String getOrderField() {
		return orderField;
	}

	public void setOrderField(String orderField) {
		this.orderField = orderField;
	}

	public Map<String, Integer> getSystemDataCounts() {
		return systemDataCounts;
	}

	public void setSystemDataCounts(Map<String, Integer> systemDataCounts) {
		this.systemDataCounts = systemDataCounts;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public List<SysMenu> getMenuList() {
		return menuList;
	}

	public void setMenuList(List<SysMenu> menuList) {
		this.menuList = menuList;
	}

	public List<SysUser> getUserList() {
		return userList;
	}

	public void setUserList(List<SysUser> userList) {
		this.userList = userList;
	}

	public String[] getOaflowIds() {
		return oaflowIds;
	}

	public void setOaflowIds(String[] oaflowIds) {
		this.oaflowIds = oaflowIds;
	}

	public List<SysRoleOa> getRoleOaList() {
		return roleOaList;
	}

	public void setRoleOaList(List<SysRoleOa> roleOaList) {
		this.roleOaList = roleOaList;
	}

	public List<SysRoleData> getTableData() {
		return tableData;
	}

	public void setTableData(List<SysRoleData> tableData) {
		this.tableData = tableData;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("roleId", getRoleId())
            .append("roleName", getRoleName())
            .append("roleKey", getRoleKey())
            .append("roleSort", getRoleSort())
            .append("dataScope", getDataScope())
            .append("menuCheckStrictly", isMenuCheckStrictly())
            .append("deptCheckStrictly", isDeptCheckStrictly())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("roleType", getRoleType())
            .toString();
    }
}
