package com.ruoyi.common.core.domain.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * (NewsConfigCenter)实体类
 *
 * <AUTHOR>
 * @since 2024-03-08 11:23:40
 */
@Data
public class NewsConfigCenter {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 公司编码
     */
    private String companyCode;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 一级菜单名称
     */
    private String menuFirstName;
    /**
     * 一级菜单编码
     */
    private String menuFirstCode;
    /**
     * 二级菜单名称
     */
    private String menuSecondName;
    /**
     * 二级菜单编码
     */
    private String menuSecondCode;
    /**
     * 主要标题
     */
    private String majorTitle;
    /**
     * 次要标题
     */
    private String secondaryTitle;
    /**
     * 文本内容（html段落数据）
     */
    private String textContent;
    /**
     * 发布状态（0发布，1未发布）
     */
    private String releaseStatus;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 修改人
     */
    private String updateBy;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 备注
     */
    private String remark;


    // 页码
    private int pageNum = 1;

    // 页面大小
    private int pageSize = 10;
    // 网页搜索ID
    private String interMenuValue;
    // 网络菜单类型
    private String interMenuType;

}
