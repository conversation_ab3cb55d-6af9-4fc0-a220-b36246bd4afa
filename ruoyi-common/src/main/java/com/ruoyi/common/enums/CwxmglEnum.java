package com.ruoyi.common.enums;

/**
 * 财务项目管理枚举
 *
 * <AUTHOR>
 * 2022-11-11
 */
public enum CwxmglEnum {
    SUCCESS("200", "成功"),
    NO_SELECT_PARAM("400", "请确定参数后再进行查询！"),
    NOT_FOUND("404", "没有找到相关数据！"),
    NOT_FOUND_1("404", "没有找到相关数据，请刷新页面重新操作！");

    private final String code;
    private final String msg;

    CwxmglEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
