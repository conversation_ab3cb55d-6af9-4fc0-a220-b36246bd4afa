package com.ruoyi.common.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * add by nieyi 2024-04-25
 * 所属功能节点枚举
 */
public enum FunctionNodeEnum {

    USERMANAGE("USERMANAGE","用户管理","USERRE",1,"0"),
    POSTMANAGE("POSTMANAGE","岗位管理","POSTRE",2,"0"),
    IMPOWERPRO("IMPOWERPRO","通用授权-项目","",3,"0"),
    IMPOWERUNIT("IMPOWERUNIT","通用授权-公司","",4,"0"),
    IMPOWERALL("IMPOWERALL","通用授权-所有","",5,"0"),
    IMPOWERCASE("IMPOWERCASE","通用授权-个例","",6,"0"),
    PROJECTNA("PROJECTNA","项目名称","",7,"0"),
    PRODUCTIN("PRODUCTIN","产品信息","",8,"0"),
    UNITIN("UNITIN","公司信息","",9,"0"),
    LICENSEALL("LICENSEALL","所有证照","LICENSE",10,"0"),
    LICENSECATA("LICENSECATA","证照目录","LICENSE",11,"0"),
    LICENSEPEND("LICENSEPEND","待处理证照","LICENSE",12,"0"),
    LICENSEWARN("LICENSEWARN","证照预警","LICENSE",13,"0"),
    PERSONENTRY("PERSONENTRY","人员入职","PERSONNEL",14,"0"),
    PERSONREGULAR("PERSONREGULAR","转正申请","PERSONNEL",15,"0"),
    PERSONARCHIVES("PERSONARCHIVES","人员档案","PERSONNEL",16,"0"),
    PERSONMOVE("PERSONMOVE","人员调动","PERSONNEL",17,"0"),
    PERSONDIMI("PERSONDIMI","人员离职","PERSONNEL",18,"0"),

    DICTDATA("DICTDATA","字典数据","DICTDATATYPE",19,"0"),

    COMPANYTYPE("COMPANYTYPE","公司类型","COMPANYTYPE",20,"0"),
    COMPANYBUSINESSTYPE("COMPANYBUSINESSTYPE","公司支持业务类型","COMPANYBUSINESSTYPE",21,"0"),
    AUTHTEMPLATE("AUTHTEMPLATE","权限模板","AUTHTEMPLATE",22,"0"),
    AUTHTEMPLATETOPROJ("AUTHTEMPLATETOPROJ","权限模板-新增项目权限","AUTHTEMPLATE",22,"0"),
    AUTHTEMPLATETOUNIT("AUTHTEMPLATETOUNIT","权限模板-新增公司权限","AUTHTEMPLATE",22,"0"),
    AUTHTEMPLATETOALL("AUTHTEMPLATETOALL","权限模板-新增所有权限","AUTHTEMPLATE",22,"0"),

    INSPROJSETUP("INSERTPROJSETUP","项目立项管理", "PROJSETUP",23,"0"),
    PROJECTNAMEOATRADER("PROJECTNAMEOATRADER","项目信息-收付款信息","",24,"0"),
    PROJECTNAMECWPROJ("PROJECTNAMECWPROJ","项目信息-信息费信息","",25,"0"),
    OFFCATEGORYMAIN("OFFCATEGORYMAIN","办公用品类别","OFFSUPPLY",26,"0"),
    OFFSUPPLYMAIN("OFFSUPPLYMAIN","办公用品管理","OFFSUPPLY",27,"0"),
    OFFRECEIVEMAIN("OFFRECEIVEMAIN","办公用品领用","OFFSUPPLY",28,"0"),
    OFFPURCHASEMAIN("OFFPURCHASEMAIN","办公用品采购","OFFSUPPLY",29,"0"),

    ;

    /**
     * 功能节点代码
     */
    private final String code;

    /**
     * 所属模块
     * 详见 AuthModuleEnum枚举类
     * AuthModuleEnum 和 FunctionNodeEnum 是一对多关系，如 AuthModuleEnum枚举中人事模块，对应此类中人员入职、转正申请、人员档案、人员调动、人员离职
     */
    private final String parentModule;

    /**
     * 模块说明
     */
    private final String info;
    /**
     * 排序
     */
    private final Integer order;
    /**
     * 状态 0正常 1停用
     */
    private final String status;

    FunctionNodeEnum(String code, String info, String parentModule,  Integer order, String status)
    {
        this.code = code;
        this.info = info;
        this.parentModule = parentModule;
        this.order = order;
        this.status = status;
    }

    public String getCode()
    {
        return code;
    }

    public String getParentModule() {
        return parentModule;
    }

    public String getInfo()
    {
        return info;
    }
    public Integer getOrder()
    {
        return order;
    }
    public String getStatus()
    {
        return status;
    }


    public static List<Map<String, Object>> getList(){
        List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
        Map<String, Object> map = null;
        for (FunctionNodeEnum be : values()) {
            map = new HashMap<String, Object>();
            map.put("code", be.getCode());
            map.put("parentModule", be.getParentModule());
            map.put("info", be.getInfo());
            map.put("order", be.getOrder());
            map.put("status", be.getStatus());
            list.add(map);
        }
        return list;
    }
}
