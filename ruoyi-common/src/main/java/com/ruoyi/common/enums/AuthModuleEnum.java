package com.ruoyi.common.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模块划分枚举
 * <AUTHOR> add 20240402
 *
 */
public enum AuthModuleEnum {


    OALAUNCH("OALAUNCH","UNIT", "OA流程发起权限",1,"0"),
    OAVIEW("OAVIEW","DEPT", "OA流程查看权限",2,"0"),
    FINANCESYS("FINANCESYS","UNIT", "智慧财务系统",3,"0"),
    FINANCEPROJ("FINANCEPROJ","PROJ", "财务项目管理",4,"0"),
    CARBOOLMANGER("CARBOOLMANGER","PROJ", "车贷绿本出入库",5,"0"),
    PROJSETUP("PROJSETUP","PROJ", "项目立项管理",6,"0"),
    ARCHIVES("ARCHIVES","DEPT", "档案管理",7,"0"),
    JGINFORMATION("JGINFORMATION","PROJ", "监管报送资料",8,"0"),
    SXINFORMATION("SXINFORMATION","UNIT", "授信及贷后资料",8,"0"),
    PERSONNEL("PERSONNEL","DEPT", "人事管理",9,"0"),
    LICENSE("LICENSE","DEPT", "证照管理",10,"0"),
    ATTENDANCE("ATTENDANCE","DEPT", "考勤管理",11,"0"),
    PAYROLL("PAYROLL","DEPT", "薪酬中心",12,"0"),
    DATAREPORT("DATAREPORT","PROJ", "数据报表",13,"0"),
    ECHARTS("ECHARTS","PROJ", "Echarts",14,"0"),
    DATATOP("DATATOP","PROJ", "智慧数据系统首页",15,"0"),
    PROJNAME("PROJNAME","PROJ","项目名称",16,"0"),
    POSTRE("POSTRE","DEPT","岗位管理",17,"0"),
    USERRE("USERRE","DEPT","用户管理",18,"0"),
    DICTDATATYPE("DICTDATATYPE","DEPT","用户管理",19,"0"),
    //合并权限模板与授权人员关系至权限模块
    AUTHTEMPLATE("AUTHTEMPLATE","OTHER", "权限模板",20,"0"),
    COMPANY("COMPANY","UNIT", "公司管理",21,"0"),

    PRODUCT("PRODUCT","PROJ", "产品管理",22,"0"),
    CANCELAUTH("CANCELAUTH","OTHER", "取消权限",23,"0"),
    BADSYSTEM("BADSYSTEM","UNIT", "不良资产系统",16,"0"),
    /**
     * 后续新增条目到这个枚举的时候，如果新增的需要查询对应的新权限。枚举的order要小于17。
     * 因为新权限查询的时候，有通过order来进行取值获得权限的限定范围
     **/

    //通知公告  order别修改，就按照16，修改了可能会导致权限那边查不到
    NOTICE("NOTICE","UNIT", "通知公告",16,"0"),

    //绩效考核 20241009
    PERFORMANCE("PERFORMANCE","UNIT", "绩效考核",16,"0"),

    //办公用品领用 20250331
    OFFSUPPLY("OFFSUPPLY","UNIT", "办公用品领用",16,"0"),

    //业务信息配置
    BUSINESSDATACONFIG("BUSINESSDATACONFIG","OTHER", "业务信息配置",26,"0"),

    //经营分析
    BUSINESSANALYSIS("BUSINESSANALYSIS","OTHER", "经营分析",27,"0"),

    //项目信息-收付款信息
    PROJECTNAMEOATRADER("PROJECTNAMEOATRADER","OTHER", "项目信息-收付款信息",28,"0"),
    //项目信息-信息费信息
    PROJECTNAMECWPROJ("PROJECTNAMECWPROJ","OTHER", "项目信息-信息费信息",29,"0"),
    //内部公司
    INTERNALCOMPANY("INTERNALCOMPANY","OTHER", "内部公司",30,"0"),
    //外部公司
    EXTERNALCOMPANY("EXTERNALCOMPANY","OTHER", "外部公司",31,"0"),
    //创建项目名称规则
    PROJECTNAMERULE("PROJECTNAMERULE","OTHER", "创建项目名称规则",32,"0"),
    //债转管理
    DEBTCONVERSION("DEBTCONVERSION","OTHER", "债转管理",32,"0"),
    ;

    /**
	 * 模块代码
	 */
    private final String code;

    /**
     * 权限控制类型  详见 AuthPermissionEnum type字段
     */
    private final String permissionType;

    /**
     * 模块说明
     */
    private final String info;
    /**
     * 排序
     */
    private final Integer order;
    /**
     * 状态 0正常 1停用
     */
    private final String status;

    AuthModuleEnum(String code, String permissionType, String info,  Integer order, String status)
    {
        this.code = code;
        this.info = info;
        this.permissionType = permissionType;
        this.order = order;
        this.status = status;
    }

    public String getCode()
    {
        return code;
    }

    public String getPermissionType()
    {
        return permissionType;
    }
    public String getInfo()
    {
        return info;
    }
    public Integer getOrder()
    {
        return order;
    }
    public String getStatus()
    {
        return status;
    }


    public static List<Map<String, Object>> getList(){
		List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
		Map<String, Object> map = null;
		for (AuthModuleEnum be : values()) {
			map = new HashMap<String, Object>();
			map.put("code", be.getCode());
			map.put("permissionType", be.getPermissionType());
			map.put("info", be.getInfo());
			map.put("order", be.getOrder());
			map.put("status", be.getStatus());
			list.add(map);
		}
		return list;
	}







    /** 暂时不用
     * 模块类型
     *
     * 通用：
     * #a-1：OA办公-流程发起权限
     * #a-2：OA办公-流程查询数据权限
     * #a-3：待办事项
     * #a-4：工单管理
     *
     * 协同管理：
     * #b-1：协同管理-智慧数据首页
     * #b-2：协同管理-Echart
     * #b-3：协同管理-数据报表
     * #b-4：协同管理-项目管理
     * #b-5：协同管理-项目立项
     * #b-5：协同管理-报告
     * #b-5：协同管理-信息共享
     * #b-5：协同管理-对外资料
     * #b-5：协同管理-外部账号管理
     * #b-5：协同管理-车贷绿本出入库
     * #b-5：协同管理-视频讲解
     * #b-5：协同管理-
     * #b-5：协同管理-
     * #b-5：协同管理-
     * #b-5：协同管理-
     *
     * 业务管控：
     * #c-1：业务管控-业务管控
     * #c-2：业务管控-监管展示
     *
     * 行政管理：
     * #d-1：行政管理-人事管理
     * #d-1：行政管理-档案管理
     * #d-1：行政管理-证照管理
     * #d-1：行政管理-公告通知
     * #d-1：行政管理-会议管理
     * #d-1：行政管理-办公用品管理
     * #d-1：行政管理-资产管理
     * #d-1：行政管理-培训管理
     * #d-1：行政管理-网站新闻动态管理
     *
     *
     * 财务管理：
     * #e-1：财务管理-财务信息费管理
     * #e-2：财务管理-财务报表校验
     * #e-2：财务管理-智慧财务
     * #e-2：财务管理-发票管理
     * #e-2：财务管理-法催业务数据核对
     * #e-2：财务管理-运营报表生成
     * #e-2：财务管理-
     * #e-2：财务管理-
     */
    // private final String typeDetail;
}
