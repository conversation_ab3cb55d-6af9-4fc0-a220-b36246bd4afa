package com.ruoyi.common.enums;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.enums.IEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

public enum AckFlagStatus implements IEnum<String> {

    /** 状态未确认 */
    UNCONFIRMED("0", "未确认"),
    /** 状态已确认*/
    CONFIRMED("1", "已确认"),

    ;

    @Setter
    @Getter
    private String value;
    @Setter@Getter
    private String desc;
    @Setter@Getter
    private String name;

    AckFlagStatus(final String value, final String desc) {
        this.value = value;
        this.desc = desc;
        this.name = this.name();
    }

    @Override
    public String getValue() {
        return this.value;
    }
    public String getDesc() {
        return this.desc;
    }
    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        Map map =new HashMap();
        map.put("name",name);
        map.put("value",value);
        map.put("desc",desc);
        return  JSON.toJSONString(map);
    }
}
