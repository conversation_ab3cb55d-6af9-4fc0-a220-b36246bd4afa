<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.KfChannelInfoMapper">
    
    <resultMap type="com.ruoyi.system.domain.kf.KfChannelInfo" id="KfChannelInfoResult">
        <result property="id"    column="id"    />
        <result property="channelCode"    column="channel_code"    />
        <result property="channelName"    column="channel_name"    />
        <result property="companyCode"    column="company_code"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="lastUpdateTime"    column="last_update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectKfChannelInfoVo">
        select id, channel_code, channel_name, company_code, remark, status, create_time, create_by, last_update_time, update_by from kf_channel_info
    </sql>

    <select id="selectKfChannelInfoList" parameterType="com.ruoyi.system.domain.kf.KfChannelInfo" resultMap="KfChannelInfoResult">
        <include refid="selectKfChannelInfoVo"/>
        <where>  
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="channelName != null  and channelName != ''"> and channel_name like concat('%', #{channelName}, '%')</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="lastUpdateTime != null "> and last_update_time = #{lastUpdateTime}</if>
        </where>
    </select>
    
    <select id="selectKfChannelInfoById" parameterType="Long" resultMap="KfChannelInfoResult">
        <include refid="selectKfChannelInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectChannelByCompanyCodeAndQdCode" resultType="java.lang.Integer">
        select count(1)
        from kf_channel_info
        where company_code = #{companyCode}
          and ( channel_code = #{channelCode}  or channel_name =#{channelName})
    </select>
    <select id="selectChannelDict" resultType="java.util.Map">
        select max(id) , channel_code as value, max(channel_name) as label
        from kf_channel_info
        <where>
            1 = 1
              and status = 0
            <if test="companyCode != null and companyCode != ''">
                and company_code = #{companyCode}
            </if>
        </where>
        group by channel_code
    </select>
    <select id="selectChannelByCompanyCode" resultType="java.util.Map">
        select id, channel_name as channelName ,channel_code as channelCode from kf_channel_info where company_code = #{companyCode} and status = 0
    </select>

    <insert id="insertKfChannelInfo" parameterType="com.ruoyi.system.domain.kf.KfChannelInfo">
        insert into kf_channel_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="channelCode != null and channelCode != ''">channel_code,</if>
            <if test="channelName != null and channelName != ''">channel_name,</if>
            <if test="companyCode != null and companyCode != ''">company_code,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="lastUpdateTime != null">last_update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
            <if test="channelName != null and channelName != ''">#{channelName},</if>
            <if test="companyCode != null and companyCode != ''">#{companyCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="lastUpdateTime != null">#{lastUpdateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateKfChannelInfo" parameterType="com.ruoyi.system.domain.kf.KfChannelInfo">
        update kf_channel_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="channelCode != null and channelCode != ''">channel_code = #{channelCode},</if>
            <if test="channelName != null and channelName != ''">channel_name = #{channelName},</if>
            <if test="companyCode != null and companyCode != ''">company_code = #{companyCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="lastUpdateTime != null">last_update_time = #{lastUpdateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKfChannelInfoById" parameterType="Long">
        delete from kf_channel_info where id = #{id}
    </delete>

    <delete id="deleteKfChannelInfoByIds" parameterType="String">
        delete from kf_channel_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectChannelByCompanyCodeAndQdCodeAndId" resultType="java.lang.Integer">
        select count(1)
        from kf_channel_info
        where company_code = #{companyCode}
        and  channel_code = #{channelCode} and id != #{id}
    </select>

    <select id="tree" resultType="com.ruoyi.system.domain.dto.ChannelTreeDataDTO">
        SELECT
            kci.id AS companyId,
            kci.company_code AS companyCode,
            kci.company_name AS companyName,
            kli.id AS channelId,
            kli.channel_code AS channelCode,
            kli.channel_name AS channelName,
            ifnull(kti.count, 0 ) AS count
        FROM
            kf_company_info kci
                LEFT JOIN kf_channel_info kli ON kci.company_code = kli.company_code
                LEFT JOIN ( SELECT channel_id AS id, count( 1 ) AS count FROM kf_account_info WHERE `status` = 0 GROUP BY channel_id ) AS kti ON kli.id = kti.id
        WHERE
            kci.`status` = 0
          AND kli.`status` = 0
    </select>
    <select id="selectKfChannelInfoListByCompanyStatus" resultMap="KfChannelInfoResult">
        select kci.id as id , kci.channel_code as channel_code, kci.channel_name as channel_name,
               kci.company_code as company_code, kci.remark as remark, kci.status as status,
               kci.create_time as create_time, kci.create_by as create_by,
               kci.last_update_time as last_update_time, kci.update_by as update_by
        from kf_channel_info kci left join kf_company_info k on kci.company_code = k.company_code
        <where>
            1=1 and k.status = 0
            <if test="channelCode != null  and channelCode != ''"> and kci.channel_code = #{channelCode}</if>
            <if test="channelName != null  and channelName != ''"> and kci.channel_name like concat('%', #{channelName}, '%')</if>
            <if test="companyCode != null  and companyCode != ''"> and kci.company_code = #{companyCode}</if>
            <if test="status != null  and status != ''"> and kci.status = #{status}</if>
            <if test="lastUpdateTime != null "> and kci.last_update_time = #{lastUpdateTime}</if>
        </where>
    </select>
</mapper>