<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.KfWorkOrderMapper">
    <resultMap type="com.ruoyi.system.domain.kf.KfWorkOrder" id="KfWorkOrderResultMap">
        <result property="id" column="id"/>
        <result property="workOrderNum" column="work_order_num"/>
        <result property="companyNum" column="company_num"/>
        <result property="channelNum" column="channel_num"/>
        <result property="orderSourceNum" column="order_source_num"/>
        <result property="userPhone" column="user_phone"/>
        <result property="userName" column="user_name"/>
        <result property="userIdCardNum" column="user_id_card_num"/>
        <result property="workOrderText" column="work_order_text"/>
        <result property="claimUserId" column="claim_user_id"/>
        <result property="workOrderStatus" column="work_order_status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="lastUpdateTime" column="last_update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="table_field">
        id,
        work_order_num,
        company_num,
        channel_num,
        order_source_num,
        user_phone,
        user_name,
        user_id_card_num,
        work_order_text,
        claim_user_id,
        work_order_status,
        create_time,
        create_by,
        last_update_time,
        update_by
    </sql>

    <!--通过Id查询单个-->
    <select id="getById" resultMap="KfWorkOrderResultMap" parameterType="java.lang.Long">
        select
        <include refid="table_field"/>
        from kf_work_order
        where id = #{id}
    </select>


    <!--通过实体不为空的属性作为筛选条件查询列表-->
    <select id="listByEntity" resultMap="KfWorkOrderResultMap" parameterType="com.ruoyi.system.domain.kf.KfWorkOrder">
        select
        <include refid="table_field"/>
        from kf_work_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="workOrderNum != null">
                and work_order_num = #{workOrderNum}
            </if>
            <if test="companyNum != null">
                and company_num = #{companyNum}
            </if>
            <if test="channelNum != null">
                and channel_num = #{channelNum}
            </if>
            <if test="orderSourceNum != null">
                and order_source_num = #{orderSourceNum}
            </if>
            <if test="userPhone != null">
                and user_phone = #{userPhone}
            </if>
            <if test="userName != null">
                and user_name = #{userName}
            </if>
            <if test="userIdCardNum != null">
                and user_id_card_num = #{userIdCardNum}
            </if>
            <if test="workOrderText != null">
                and work_order_text = #{workOrderText}
            </if>
            <if test="claimUserId != null">
                and claim_user_id = #{claimUserId}
            </if>
            <if test="workOrderStatus != null">
                and work_order_status = #{workOrderStatus}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createBy != null">
                and create_by = #{createBy}
            </if>
            <if test="lastUpdateTime != null">
                and last_update_time = #{lastUpdateTime}
            </if>
            <if test="updateBy != null">
                and update_by = #{updateBy}
            </if>
        </where>
    </select>

    <!--通过实体不为空的属性作为筛选条件查询单个-->
    <select id="getByEntity" resultMap="KfWorkOrderResultMap" parameterType="com.ruoyi.system.domain.kf.KfWorkOrder">
        select
        <include refid="table_field"/>
        from kf_work_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="workOrderNum != null">
                and work_order_num = #{workOrderNum}
            </if>
            <if test="companyNum != null">
                and company_num = #{companyNum}
            </if>
            <if test="channelNum != null">
                and channel_num = #{channelNum}
            </if>
            <if test="orderSourceNum != null">
                and order_source_num = #{orderSourceNum}
            </if>
            <if test="userPhone != null">
                and user_phone = #{userPhone}
            </if>
            <if test="userName != null">
                and user_name = #{userName}
            </if>
            <if test="userIdCardNum != null">
                and user_id_card_num = #{userIdCardNum}
            </if>
            <if test="workOrderText != null">
                and work_order_text = #{workOrderText}
            </if>
            <if test="claimUserId != null">
                and claim_user_id = #{claimUserId}
            </if>
            <if test="workOrderStatus != null">
                and work_order_status = #{workOrderStatus}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createBy != null">
                and create_by = #{createBy}
            </if>
            <if test="lastUpdateTime != null">
                and last_update_time = #{lastUpdateTime}
            </if>
            <if test="updateBy != null">
                and update_by = #{updateBy}
            </if>
        </where>
    </select>

    <!--通过Id列表作为筛选条件查询列表，列表长度不为0-->
    <select id="listByIds" resultMap="KfWorkOrderResultMap" parameterType="list">
        select
        <include refid="table_field"/>
        from kf_work_order
        where id in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>

    <!--新增实体属性不为null的列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true" parameterType="com.ruoyi.system.domain.kf.KfWorkOrder">
        insert into kf_work_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="workOrderNum != null">
                work_order_num,
            </if>
            <if test="companyNum != null">
                company_num,
            </if>
            <if test="channelNum != null">
                channel_num,
            </if>
            <if test="orderSourceNum != null">
                order_source_num,
            </if>
            <if test="userPhone != null">
                user_phone,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="userIdCardNum != null">
                user_id_card_num,
            </if>
            <if test="workOrderText != null">
                work_order_text,
            </if>
            <if test="claimUserId != null">
                claim_user_id,
            </if>
            <if test="workOrderStatus != null">
                work_order_status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="lastUpdateTime != null">
                last_update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="workOrderNum != null">
                #{workOrderNum},
            </if>
            <if test="companyNum != null">
                #{companyNum},
            </if>
            <if test="channelNum != null">
                #{channelNum},
            </if>
            <if test="orderSourceNum != null">
                #{orderSourceNum},
            </if>
            <if test="userPhone != null">
                #{userPhone},
            </if>
            <if test="userName != null">
                #{userName},
            </if>
            <if test="userIdCardNum != null">
                #{userIdCardNum},
            </if>
            <if test="workOrderText != null">
                #{workOrderText},
            </if>
            <if test="claimUserId != null">
                #{claimUserId},
            </if>
            <if test="workOrderStatus != null">
                #{workOrderStatus},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
        </trim>
    </insert>

    <!--批量新增所有列，列表长度不能为0，且列表id统一为null或者统一不为null-->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true" parameterType="list">
        insert into kf_work_order
        (id, work_order_num, company_num, channel_num, order_source_num, user_phone, user_name, user_id_card_num,
         work_order_text,
         claim_user_id,
         work_order_status, create_time, create_by, last_update_time, update_by)
        values
        <foreach item="item" collection="list" separator="," open="" close="" index="index">
            (#{item.id}, #{item.workOrderNum}, #{item.companyNum}, #{item.channelNum}, #{item.orderSourceNum},
             #{item.userPhone}, #{item.userName},
             #{item.userIdCardNum}, #{item.workOrderText}, #{item.claimUserId}, #{item.workOrderStatus},
             #{item.createTime}, #{item.createBy}, #{item.lastUpdateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <!--通过主键修改实体属性不为null的列-->
    <update id="update" parameterType="com.ruoyi.system.domain.kf.KfWorkOrder">
        update kf_work_order
        <set>
            <if test="workOrderNum != null and workOrderNum != ''">
                work_order_num = #{workOrderNum},
            </if>
            <if test="companyNum != null and companyNum != ''">
                company_num = #{companyNum},
            </if>
            <if test="channelNum != null and channelNum != ''">
                channel_num = #{channelNum},
            </if>
            <if test="orderSourceNum != null and orderSourceNum != ''">
                order_source_num = #{orderSourceNum},
            </if>
            <if test="userPhone != null and userPhone != ''">
                user_phone = #{userPhone},
            </if>
            <if test="userName != null and userPhone != ''">
                user_name = #{userName},
            </if>
            <if test="userIdCardNum != null and userIdCardNum != ''">
                user_id_card_num = #{userIdCardNum},
            </if>
            <if test="workOrderText != null and workOrderText != ''">
                work_order_text = #{workOrderText},
            </if>
            <if test="claimUserId != null and claimUserId != ''">
                claim_user_id = #{claimUserId},
            </if>
            <if test="workOrderStatus != null and workOrderStatus != ''">
                work_order_status = #{workOrderStatus},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="lastUpdateTime != null">
                last_update_time = #{lastUpdateTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过表字段修改实体属性不为null的列-->
    <update id="updateByField">
        update kf_work_order
        <set>
            <if test="where.workOrderNum == null and set.workOrderNum != null and set.workOrderNum != ''">
                work_order_num = #{set.workOrderNum},
            </if>
            <if test="where.companyNum == null and set.companyNum != null and set.companyNum != ''">
                company_num = #{set.companyNum},
            </if>
            <if test="where.channelNum == null and set.channelNum != null and set.channelNum != ''">
                channel_num = #{set.channelNum},
            </if>
            <if test="where.orderSourceNum == null and set.orderSourceNum != null and set.orderSourceNum != ''">
                order_source_num = #{set.orderSourceNum},
            </if>
            <if test="where.userPhone == null and set.userPhone != null and set.userPhone != ''">
                user_phone = #{set.userPhone},
            </if>
            <if test="where.userName == null and set.userName != null and set.userName != ''">
                user_name = #{set.userName},
            </if>
            <if test="where.userIdCardNum == null and set.userIdCardNum != null and set.userIdCardNum != ''">
                user_id_card_num = #{set.userIdCardNum},
            </if>
            <if test="where.workOrderText == null and set.workOrderText != null and set.workOrderText != ''">
                work_order_text = #{set.workOrderText},
            </if>
            <if test="where.claimUserId == null and set.claimUserId != null and set.claimUserId != ''">
                claim_user_id = #{set.claimUserId},
            </if>
            <if test="where.workOrderStatus == null and set.workOrderStatus != null and set.workOrderStatus != ''">
                work_order_status = #{set.workOrderStatus},
            </if>
            <if test="where.createTime == null and set.createTime != null">
                create_time = #{set.createTime},
            </if>
            <if test="where.createBy == null and set.createBy != null and set.createBy != ''">
                create_by = #{set.createBy},
            </if>
            <if test="where.lastUpdateTime == null and set.lastUpdateTime != null">
                last_update_time = #{set.lastUpdateTime},
            </if>
            <if test="where.updateBy == null and set.updateBy != null and set.updateBy != ''">
                update_by = #{set.updateBy},
            </if>
        </set>
        <where>
            <if test="where.id != null">
                and id = #{where.id}
            </if>
            <if test="where.workOrderNum != null">
                and work_order_num = #{where.workOrderNum}
            </if>
            <if test="where.companyNum != null">
                and company_num = #{where.companyNum}
            </if>
            <if test="where.channelNum != null">
                and channel_num = #{where.channelNum}
            </if>
            <if test="where.orderSourceNum != null">
                and order_source_num = #{where.orderSourceNum}
            </if>
            <if test="where.userPhone != null">
                and user_phone = #{where.userPhone}
            </if>
            <if test="where.userName != null">
                and user_name = #{where.userName}
            </if>
            <if test="where.userIdCardNum != null">
                and user_id_card_num = #{where.userIdCardNum}
            </if>
            <if test="where.workOrderText != null">
                and work_order_text = #{where.workOrderText}
            </if>
            <if test="where.claimUserId != null">
                and claim_user_id = #{where.claimUserId}
            </if>
            <if test="where.workOrderStatus != null">
                and work_order_status = #{where.workOrderStatus}
            </if>
            <if test="where.createTime != null">
                and create_time = #{where.createTime}
            </if>
            <if test="where.createBy != null">
                and create_by = #{where.createBy}
            </if>
            <if test="where.lastUpdateTime != null">
                and last_update_time = #{where.lastUpdateTime}
            </if>
            <if test="where.updateBy != null">
                and update_by = #{where.updateBy}
            </if>
        </where>
    </update>

    <!--通过主键修改实体列表，列表长度不能为0，注意：当实体属性为null时，对应的列也会别更新为null-->
    <update id="updateBatch" parameterType="list">
        update kf_work_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="work_order_num = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.workOrderNum}
                </foreach>
            </trim>
            <trim prefix="company_num = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.companyNum}
                </foreach>
            </trim>
            <trim prefix="channel_num = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.channelNum}
                </foreach>
            </trim>
            <trim prefix="order_source_num = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.orderSourceNum}
                </foreach>
            </trim>
            <trim prefix="user_phone = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.userPhone}
                </foreach>
            </trim>
            <trim prefix="user_name = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.userName}
                </foreach>
            </trim>
            <trim prefix="user_id_card_num = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.userIdCardNum}
                </foreach>
            </trim>
            <trim prefix="work_order_text = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.workOrderText}
                </foreach>
            </trim>
            <trim prefix="claim_user_id = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.claimUserId}
                </foreach>
            </trim>
            <trim prefix="work_order_status = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.workOrderStatus}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.createTime}
                </foreach>
            </trim>
            <trim prefix="create_by = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.createBy}
                </foreach>
            </trim>
            <trim prefix="last_update_time = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.lastUpdateTime}
                </foreach>
            </trim>
            <trim prefix="update_by = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.updateBy}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    <update id="updateClaimAndStatusById">
        update kf_work_order
        set claim_user_id     = #{claimUserId},
            work_order_status = #{workReady}
        where id = #{id}
    </update>
    <update id="updatePhoneAndNameAndCardNo">
        update kf_work_order
        <set>
            user_phone = #{userPhone},
            user_name = #{userName},
            user_id_card_num = #{userIdCardNum},
            last_update_time = #{lastUpdateTime},
            update_by = #{updateBy}
        </set>
        where id = #{id}
    </update>
    <select id="workListCount" resultType="java.lang.Integer">
        select
            count(1)
        from kf_work_order
        <if test="kfWorkOrderDTO != null ">
            <where>
                1=1
                <if test="kfWorkOrderDTO.companyNum != null and kfWorkOrderDTO.companyNum != ''">
                    and company_num = #{kfWorkOrderDTO.companyNum}
                </if>
                <if test="kfWorkOrderDTO.orderSourceNum != null and kfWorkOrderDTO.orderSourceNum != ''">
                    and order_source_num = #{kfWorkOrderDTO.orderSourceNum}
                </if>
                <if test="kfWorkOrderDTO.channelNumList != null  and kfWorkOrderDTO.channelNumList.size() > 0">
                    and channel_num in
                    <foreach item="channelNum" collection="kfWorkOrderDTO.channelNumList" open="(" separator=","
                             close=")">
                        #{channelNum}
                    </foreach>
                </if>
                <if test="kfWorkOrderDTO.userPhone != null and kfWorkOrderDTO.userPhone != ''">
                    and user_phone = #{kfWorkOrderDTO.userPhone}
                </if>
                <if test="kfWorkOrderDTO.userName != null and kfWorkOrderDTO.userName != ''">
                    and user_name like concat('%',#{kfWorkOrderDTO.userName},'%')
                </if>
                <if test="kfWorkOrderDTO.userIdCardNum != null and kfWorkOrderDTO.userIdCardNum != ''">
                    and user_id_card_num = #{kfWorkOrderDTO.userIdCardNum}
                </if>
                <if test="kfWorkOrderDTO.workOrderStatus != null and kfWorkOrderDTO.workOrderStatus != '' and kfWorkOrderDTO.workOrderStatus == 'NOTFINISH'">
                    and work_order_status in ('READY', 'LOAD')
                </if>
                <if test="kfWorkOrderDTO.workOrderStatus != null and kfWorkOrderDTO.workOrderStatus != '' and kfWorkOrderDTO.workOrderStatus == 'DWHANDLER'">
                    and claim_user_id = #{accountName} and work_order_status = 'LOAD'
                </if>
                <if test="kfWorkOrderDTO.workOrderStatus != null and kfWorkOrderDTO.workOrderStatus != '' and kfWorkOrderDTO.workOrderStatus != 'NOTFINISH' and kfWorkOrderDTO.workOrderStatus != 'DWHANDLER'">
                    and work_order_status = #{kfWorkOrderDTO.workOrderStatus}
                </if>
                <if test="kfWorkOrderDTO.startTime != null">
                    and create_time >= #{kfWorkOrderDTO.startTime}
                </if>
                <if test="kfWorkOrderDTO.endTime != null">
                    and #{kfWorkOrderDTO.endTime} >= create_time
                </if>
                <if test="kfWorkOrderDTO.workOrderText != null and kfWorkOrderDTO.workOrderText != ''">
                    and work_order_text like concat('%' , #{kfWorkOrderDTO.workOrderText}, '%')
                </if>
                <if test="list!=null and list.size >0 ">
                    <if test="kfWorkOrderDTO.isSystemFlag != true">
                    and (
                    <foreach collection="list" item="item" separator="OR">
                        <if test="item.containsKey('companyNum') and item.containsKey('channelNum')">
                            (company_num =  #{item.companyNum} AND channel_num = #{item.channelNum})
                        </if>
                    </foreach>
                    )
                    </if>
                </if>
            </where>
        </if>
    </select>

    <!--通过主键删除-->
    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from kf_work_order
        where id = #{id}
    </delete>

    <!--通过实体非空属性删除-->
    <delete id="deleteByEntity" parameterType="com.ruoyi.system.domain.kf.KfWorkOrder">
        delete
        from kf_work_order
        <where>
            <if test="workOrderNum != null">
                and work_order_num = #{workOrderNum}
            </if>
            <if test="companyNum != null">
                and company_num = #{companyNum}
            </if>
            <if test="channelNum != null">
                and channel_num = #{channelNum}
            </if>
            <if test="orderSourceNum != null">
                and order_source_num = #{orderSourceNum}
            </if>
            <if test="userPhone != null">
                and user_phone = #{userPhone}
            </if>
            <if test="userName != null">
                and user_name = #{userName}
            </if>
            <if test="userIdCardNum != null">
                and user_id_card_num = #{userIdCardNum}
            </if>
            <if test="workOrderText != null">
                and work_order_text = #{workOrderText}
            </if>
            <if test="claimUserId != null">
                and claim_user_id = #{claimUserId}
            </if>
            <if test="workOrderStatus != null">
                and work_order_status = #{workOrderStatus}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createBy != null">
                and create_by = #{createBy}
            </if>
            <if test="lastUpdateTime != null">
                and last_update_time = #{lastUpdateTime}
            </if>
            <if test="updateBy != null">
                and update_by = #{updateBy}
            </if>
        </where>
    </delete>

    <!--通过主键列表删除，列表长度不能为0-->
    <delete id="deleteByIds" parameterType="list">
        delete
        from kf_work_order where id in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </delete>

    <select id="countAll" resultType="int">
        select count(id)
        from kf_work_order
    </select>

    <select id="countByEntity" parameterType="com.ruoyi.system.domain.kf.KfWorkOrder" resultType="int">
        select count(id)
        from kf_work_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="workOrderNum != null">
                and work_order_num = #{workOrderNum}
            </if>
            <if test="companyNum != null">
                and company_num = #{companyNum}
            </if>
            <if test="channelNum != null">
                and channel_num = #{channelNum}
            </if>
            <if test="orderSourceNum != null">
                and order_source_num = #{orderSourceNum}
            </if>
            <if test="userPhone != null">
                and user_phone = #{userPhone}
            </if>
            <if test="userName != null">
                and user_name = #{userName}
            </if>
            <if test="userIdCardNum != null">
                and user_id_card_num = #{userIdCardNum}
            </if>
            <if test="workOrderText != null">
                and work_order_text = #{workOrderText}
            </if>
            <if test="claimUserId != null">
                and claim_user_id = #{claimUserId}
            </if>
            <if test="workOrderStatus != null">
                and work_order_status = #{workOrderStatus}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="createBy != null">
                and create_by = #{createBy}
            </if>
            <if test="lastUpdateTime != null">
                and last_update_time = #{lastUpdateTime}
            </if>
            <if test="updateBy != null">
                and update_by = #{updateBy}
            </if>
        </where>
    </select>
    <select id="selectIncompleteCompanyList" resultType="java.util.Map">
        select company_num companyNum, channel_num as channelNum
        from kf_work_order
        where work_order_status in ('READY', 'LOAD')
    </select>
    <select id="workList" resultType="com.ruoyi.system.domain.vo.KfWorkOrderVo">
        select id,
               channel_num       as channelNum,
               company_num       as companyNum,
               work_order_num    as workOrderNum,
               order_source_num  as orderSourceNum,
               user_phone        as userPhone,
               user_name         as userName,
               user_id_card_num  as userIdCardNum,
               work_order_text   as workOrderText,
               claim_user_id     as claimUserId,
               work_order_status as workOrderStatus,
               create_time       as createTime,
               last_update_time  as lastUpdateTime
        from kf_work_order
        <if test="kfWorkOrderDTO != null">
            <where>
                1=1
                <if test="kfWorkOrderDTO.companyNum != null and kfWorkOrderDTO.companyNum != ''">
                    and company_num = #{kfWorkOrderDTO.companyNum}
                </if>
                <if test="kfWorkOrderDTO.orderSourceNum != null and kfWorkOrderDTO.orderSourceNum != ''">
                    and order_source_num = #{kfWorkOrderDTO.orderSourceNum}
                </if>
                <if test="kfWorkOrderDTO.channelNumList != null  and kfWorkOrderDTO.channelNumList.size() > 0">
                    and channel_num in
                    <foreach item="channelNum" collection="kfWorkOrderDTO.channelNumList" open="(" separator=","
                             close=")">
                        #{channelNum}
                    </foreach>
                </if>
                <if test="kfWorkOrderDTO.userPhone != null and kfWorkOrderDTO.userPhone != ''">
                    and user_phone = #{kfWorkOrderDTO.userPhone}
                </if>
                <if test="kfWorkOrderDTO.userName != null and kfWorkOrderDTO.userName != ''">
                    and user_name like concat('%',#{kfWorkOrderDTO.userName},'%')
                </if>
                <if test="kfWorkOrderDTO.userIdCardNum != null and kfWorkOrderDTO.userIdCardNum != ''">
                    and user_id_card_num = #{kfWorkOrderDTO.userIdCardNum}
                </if>
                <if test="kfWorkOrderDTO.workOrderStatus != null and kfWorkOrderDTO.workOrderStatus != '' and kfWorkOrderDTO.workOrderStatus == 'NOTFINISH'">
                    and work_order_status in ('READY', 'LOAD')
                </if>
                <if test="kfWorkOrderDTO.workOrderStatus != null and kfWorkOrderDTO.workOrderStatus != '' and kfWorkOrderDTO.workOrderStatus == 'DWHANDLER'">
                    and claim_user_id = #{accountName}
                    and work_order_status = 'LOAD'
                </if>
                <if test="kfWorkOrderDTO.workOrderStatus != null and kfWorkOrderDTO.workOrderStatus != '' and kfWorkOrderDTO.workOrderStatus != 'NOTFINISH' and kfWorkOrderDTO.workOrderStatus != 'DWHANDLER'">
                    and work_order_status = #{kfWorkOrderDTO.workOrderStatus}
                </if>
                <if test="kfWorkOrderDTO.startTime != null">
                    and create_time >= #{kfWorkOrderDTO.startTime}
                </if>
                <if test="kfWorkOrderDTO.endTime != null">
                    and #{kfWorkOrderDTO.endTime} >= create_time
                </if>
                <if test="kfWorkOrderDTO.wcStartTime != null">
                    and last_update_time >= #{kfWorkOrderDTO.wcStartTime}
                </if>
                <if test="kfWorkOrderDTO.wcEndTime != null">
                    and #{kfWorkOrderDTO.wcEndTime} >= last_update_time
                </if>
                <if test="kfWorkOrderDTO.workOrderText != null and kfWorkOrderDTO.workOrderText != ''">
                    and work_order_text like concat('%' , #{kfWorkOrderDTO.workOrderText}, '%')
                </if>
                <if test="list!=null and list.size >0 ">
                    <if test="kfWorkOrderDTO.isSystemFlag != true">
                        and (
                        <foreach collection="list" item="item" separator="OR">
                            <if test="item.containsKey('companyNum') and item.containsKey('channelNum')">
                                (company_num =  #{item.companyNum} AND channel_num = #{item.channelNum})
                            </if>
                        </foreach>
                        )
                    </if>
                </if>
            </where>
        </if>
        order by create_time desc, id desc
        limit #{kfWorkOrderDTO.pageNum } , #{kfWorkOrderDTO.pageSize}
    </select>
    <select id="selectByIdAndNum" resultMap="KfWorkOrderResultMap">
        select id,
                user_name,
               user_phone,
               user_id_card_num
        from kf_work_order
        where id = #{id}
          and work_order_num = #{workOrderNum}
    </select>
    <select id="queryCountByIdCard" resultType="java.lang.Integer">
        select count(1)
        from kf_work_order
        <where>
            1 = 1
            <if test="idCardNum != null and idCardNum != ''">
                and user_id_card_num = #{idCardNum}
            </if>
            <if test="phoneNum != null and phoneNum != ''">
                and user_phone = #{phoneNum}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            and work_order_status in ('READY', 'LOAD')
            and DAY(create_time) = DAY(
                    now())
        </where>
    </select>
    <select id="getVoById" resultType="com.ruoyi.system.domain.vo.KfWorkOrderVo">
        select kwo.id,
        kwo.channel_num       as channelNum,
        kwo.order_source_num  as orderSourceNum,
               kwo.company_num       as companyNum,
               kwo.work_order_num    as workOrderNum,
               kwo.user_phone        as userPhone,
               kwo.user_name         as userName,
               kwo.user_id_card_num  as userIdCardNum,
               kwo.work_order_text   as workOrderText,
               kwo.claim_user_id     as claimUserId,
               kwo.work_order_status as workOrderStatus,
               kwo.create_time       as createTime,
               kwo.last_update_time  as lastUpdateTime,
        kcui.count as kcount
        from kf_work_order kwo left join (select count(1) as `count`,work_order_num from kf_custom_update_info group by work_order_num) kcui on kwo.work_order_num = kcui.work_order_num
        where id = #{id}
    </select>
    <select id="getOrderStatusByWorkNum" resultType="java.lang.String">
        select work_order_status
        from kf_work_order
        WHERE work_order_num = #{workOrderNum}
    </select>
</mapper>






















