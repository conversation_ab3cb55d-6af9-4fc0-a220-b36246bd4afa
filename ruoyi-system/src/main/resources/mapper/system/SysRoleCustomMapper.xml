<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysRoleCustomMapper">

    <resultMap type="SysRoleData" id="SysRoleDataResult">
        <result property="customId"    column="custom_id"    />
        <result property="roleId"    column="role_id"    />
        <result property="platformNo"    column="platform_no"    />
        <result property="custNo"    column="cust_no"    />
        <result property="partnerNo"    column="partner_no"    />
        <result property="fundNo"    column="fund_no"    />
    </resultMap>

    <sql id="selectSysRoleCustomVo">
        select custom_id, role_id, platform_no, cust_no, partner_no, fund_no from sys_role_custom
    </sql>

    <select id="selectSysRoleCustomList" parameterType="SysRoleData" resultMap="SysRoleDataResult">
        <include refid="selectSysRoleCustomVo"/>
        <where>
            <if test="roleId != null "> and role_id = #{roleId}</if>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="custNo != null  and custNo != ''"> and cust_no = #{custNo}</if>
            <if test="partnerNo != null  and partnerNo != ''"> and partner_no = #{partnerNo}</if>
            <if test="fundNo != null  and fundNo != ''"> and fund_no = #{fundNo}</if>
        </where>
    </select>

    <select id="selectSysRoleCustomByCustomId" parameterType="Long" resultMap="SysRoleDataResult">
        <include refid="selectSysRoleCustomVo"/>
        where custom_id = #{customId}
    </select>

    <insert id="insertSysRoleCustom" parameterType="SysRoleData" useGeneratedKeys="true" keyProperty="customId">
        insert into sys_role_custom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">role_id,</if>
            <if test="platformNo != null">platform_no,</if>
            <if test="custNo != null">cust_no,</if>
            <if test="partnerNo != null">partner_no,</if>
            <if test="fundNo != null">fund_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">#{roleId},</if>
            <if test="platformNo != null">#{platformNo},</if>
            <if test="custNo != null">#{custNo},</if>
            <if test="partnerNo != null">#{partnerNo},</if>
            <if test="fundNo != null">#{fundNo},</if>
         </trim>
    </insert>

    <update id="updateSysRoleCustom" parameterType="SysRoleData">
        update sys_role_custom
        <trim prefix="SET" suffixOverrides=",">
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="platformNo != null">platform_no = #{platformNo},</if>
            <if test="custNo != null">cust_no = #{custNo},</if>
            <if test="partnerNo != null">partner_no = #{partnerNo},</if>
            <if test="fundNo != null">fund_no = #{fundNo},</if>
        </trim>
        where custom_id = #{customId}
    </update>

    <delete id="deleteSysRoleCustomByCustomId" parameterType="Long">
        delete from sys_role_custom where custom_id = #{customId}
    </delete>

    <delete id="deleteSysRoleCustomByCustomIds" parameterType="String">
        delete from sys_role_custom where custom_id in
        <foreach item="customId" collection="array" open="(" separator="," close=")">
            #{customId}
        </foreach>
    </delete>

    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update sys_role_custom
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="role_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.roleId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="platform_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.platformNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cust_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.custNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="partner_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.partnerNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fund_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.fundNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where custom_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.customId,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update sys_role_custom
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="role_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.roleId != null">
                        when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.roleId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="platform_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.platformNo != null">
                        when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.platformNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cust_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.custNo != null">
                        when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.custNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="partner_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.partnerNo != null">
                        when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.partnerNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fund_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fundNo != null">
                        when custom_id = #{item.customId,jdbcType=INTEGER} then #{item.fundNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where custom_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.customId,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="custom_id" keyProperty="customId" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sys_role_custom
        (role_id, platform_no, cust_no, partner_no, fund_no)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId,jdbcType=INTEGER}, #{item.platformNo,jdbcType=VARCHAR}, #{item.custNo,jdbcType=VARCHAR},
            #{item.partnerNo,jdbcType=VARCHAR}, #{item.fundNo,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <delete id="deleteByRoleId" parameterType="Long">
       delete from sys_role_custom where  role_id = #{roleId}
    </delete>

    <select id="getUserRole" parameterType="long" resultMap="SysRoleDataResult">
        SELECT rc.custom_id, rc.role_id, rc.platform_no, rc.cust_no, rc.partner_no, rc.fund_no  
        FROM sys_role_custom rc 
        LEFT JOIN sys_user_role ur ON ur.role_id = rc.role_id  
        LEFT JOIN sys_role r ON r.role_id = rc.role_id 
        WHERE r.status='0' and r.del_flag='0' and ur.user_id = #{userId}
    </select>

    <select id="getUserRoleByUserIdAndparams" resultMap="SysRoleDataResult">
        SELECT rc.custom_id, rc.role_id, rc.platform_no, rc.cust_no, rc.partner_no, rc.fund_no  FROM sys_user_role ur LEFT JOIN sys_role_custom rc ON ur.role_id = rc.role_id
        WHERE ur.user_id = #{userId}
        and rc.${dictType} in
        <foreach collection="dictValues" item="dictValue" open="(" close=")" separator=",">
            #{dictValue}
        </foreach>
    </select>
    <select id="getDataByRoleId" resultMap="SysRoleDataResult">
        <include refid="selectSysRoleCustomVo"/>
        where role_id = #{roleId};
    </select>
    
    
    <insert id="batchInsertData" keyColumn="custom_id" keyProperty="customId" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sys_role_custom
        (role_id, platform_no, cust_no, partner_no, fund_no)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId,jdbcType=INTEGER}, #{item.platformNo,jdbcType=VARCHAR}, #{item.custNo,jdbcType=VARCHAR},
            #{item.partnerNo,jdbcType=VARCHAR}, #{item.fundNo,jdbcType=VARCHAR})
        </foreach>
    </insert>


</mapper>