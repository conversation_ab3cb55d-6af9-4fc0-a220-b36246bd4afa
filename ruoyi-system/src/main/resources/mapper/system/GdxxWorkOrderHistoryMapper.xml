<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.GdxxWorkOrderHistoryMapper">
    
    <resultMap type="GdxxWorkOrderHistory" id="GdxxWorkOrderHistoryResult">
        <result property="id" column="id"/>
        <result property="workOrderMainId" column="work_order_main_id"/>
        <result property="workOrderTitle" column="work_order_title"/>
        <result property="workOrderType" column="work_order_type"/>
        <result property="requesterId" column="requester_id"/>
        <result property="requesterDepartmentId" column="requester_department_id"/>
        <result property="requirementBackground" column="requirement_background"/>
        <result property="requirementPurpose" column="requirement_purpose"/>
        <result property="requirementDescription" column="requirement_description"/>
        <result property="requirementSubmissionTime" column="requirement_submission_time"/>
        <result property="requirementPriority" column="requirement_priority"/>
        <result property="expectedCompletionDate" column="expected_completion_date"/>
        <result property="acceptanceTime" column="acceptance_time"/>
        <result property="requirementRemark" column="requirement_remark"/>
        <result property="workOrderStatus" column="work_order_status"/>
        <result property="rndProgress" column="rnd_progress"/>
        <result property="requirementImplementationSystem" column="requirement_implementation_system"/>
        <result property="currentExecutor" column="current_executor"/>
        <result property="systemFunctionModule" column="system_function_module"/>
        <result property="projectRisk" column="project_risk"/>
        <result property="externalStakeholderInfo" column="external_stakeholder_info"/>
        <result property="requirementScheduleStartDate" column="requirement_schedule_start_date"/>
        <result property="requirementScheduleEndDate" column="requirement_schedule_end_date"/>
        <result property="designScheduleStartDate" column="design_schedule_start_date"/>
        <result property="designScheduleEndDate" column="design_schedule_end_date"/>
        <result property="developmentScheduleStartDate" column="development_schedule_start_date"/>
        <result property="developmentScheduleEndDate" column="development_schedule_end_date"/>
        <result property="testingScheduleStartDate" column="testing_schedule_start_date"/>
        <result property="testingScheduleEndDate" column="testing_schedule_end_date"/>
        <result property="acceptanceTestingScheduleStartDate" column="acceptance_testing_schedule_start_date"/>
        <result property="acceptanceTestingScheduleEndDate" column="acceptance_testing_schedule_end_date"/>
        <result property="expectedGoLiveDate" column="expected_go_live_date"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creation_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modificationTime" column="modification_time"/>
    </resultMap>

    <sql id="selectGdxxWorkOrderHistoryVo">
        select id, work_order_main_id, work_order_title, work_order_type, requester_id, requester_department_id, requirement_background, requirement_purpose, requirement_description, requirement_submission_time, requirement_priority, expected_completion_date, acceptance_time,requirement_remark, work_order_status, rnd_progress, requirement_implementation_system, current_executor, system_function_module, project_risk, external_stakeholder_info, requirement_schedule_start_date, requirement_schedule_end_date, design_schedule_start_date, design_schedule_end_date, development_schedule_start_date, development_schedule_end_date, testing_schedule_start_date, testing_schedule_end_date, acceptance_testing_schedule_start_date, acceptance_testing_schedule_end_date, expected_go_live_date, status, creator, creation_time, modifier, modification_time from gdxx_work_order_history
    </sql>

    <select id="selectGdxxWorkOrderHistoryList" parameterType="GdxxWorkOrderHistory" resultMap="GdxxWorkOrderHistoryResult">
        <include refid="selectGdxxWorkOrderHistoryVo"/>
        <where>  
            <if test="workOrderMainId != null and workOrderMainId != ''"> and work_order_main_id = #{workOrderMainId}</if>
            <if test="workOrderTitle != null and workOrderTitle != ''"> and work_order_title like concat('%', #{workOrderTitle}, '%')</if>
            <if test="workOrderType != null and workOrderType != ''"> and work_order_type = #{workOrderType}</if>
            <if test="requesterId != null and requesterId != ''"> and requester_id = #{requesterId}</if>
            <if test="requesterDepartmentId != null and requesterDepartmentId != ''"> and requester_department_id = #{requesterDepartmentId}</if>
            <if test="requirementPriority != null and requirementPriority != ''"> and requirement_priority = #{requirementPriority}</if>
            <if test="workOrderStatus != null and workOrderStatus != ''"> and work_order_status = #{workOrderStatus}</if>
            <if test="rndProgress != null and rndProgress != ''"> and rnd_progress = #{rndProgress}</if>
            <if test="requirementImplementationSystem != null and requirementImplementationSystem != ''"> and requirement_implementation_system = #{requirementImplementationSystem}</if>
            <if test="currentExecutor != null and currentExecutor != ''"> and current_executor = #{currentExecutor}</if>
            <if test="projectRisk != null and projectRisk != ''"> and project_risk = #{projectRisk}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectGdxxWorkOrderHistoryById" parameterType="Long" resultMap="GdxxWorkOrderHistoryResult">
        <include refid="selectGdxxWorkOrderHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGdxxWorkOrderHistory" parameterType="GdxxWorkOrderHistory" useGeneratedKeys="true" keyProperty="id">
        insert into gdxx_work_order_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderMainId != null">work_order_main_id,</if>
            <if test="workOrderTitle != null">work_order_title,</if>
            <if test="workOrderType != null">work_order_type,</if>
            <if test="requesterId != null">requester_id,</if>
            <if test="requesterDepartmentId != null">requester_department_id,</if>
            <if test="requirementBackground != null">requirement_background,</if>
            <if test="requirementPurpose != null">requirement_purpose,</if>
            <if test="requirementDescription != null">requirement_description,</if>
            <if test="requirementSubmissionTime != null">requirement_submission_time,</if>
            <if test="requirementPriority != null">requirement_priority,</if>
            <if test="expectedCompletionDate != null">expected_completion_date,</if>
            <if test="acceptanceTime != null">acceptance_time,</if>
            <if test="requirementRemark != null">requirement_remark,</if>
            <if test="workOrderStatus != null">work_order_status,</if>
            <if test="rndProgress != null">rnd_progress,</if>
            <if test="requirementImplementationSystem != null">requirement_implementation_system,</if>
            <if test="currentExecutor != null">current_executor,</if>
            <if test="systemFunctionModule != null">system_function_module,</if>
            <if test="projectRisk != null">project_risk,</if>
            <if test="externalStakeholderInfo != null">external_stakeholder_info,</if>
            <if test="requirementScheduleStartDate != null">requirement_schedule_start_date,</if>
            <if test="requirementScheduleEndDate != null">requirement_schedule_end_date,</if>
            <if test="designScheduleStartDate != null">design_schedule_start_date,</if>
            <if test="designScheduleEndDate != null">design_schedule_end_date,</if>
            <if test="developmentScheduleStartDate != null">development_schedule_start_date,</if>
            <if test="developmentScheduleEndDate != null">development_schedule_end_date,</if>
            <if test="testingScheduleStartDate != null">testing_schedule_start_date,</if>
            <if test="testingScheduleEndDate != null">testing_schedule_end_date,</if>
            <if test="acceptanceTestingScheduleStartDate != null">acceptance_testing_schedule_start_date,</if>
            <if test="acceptanceTestingScheduleEndDate != null">acceptance_testing_schedule_end_date,</if>
            <if test="expectedGoLiveDate != null">expected_go_live_date,</if>
            <if test="status != null">status,</if>
            <if test="creator != null">creator,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="modifier != null">modifier,</if>
            <if test="modificationTime != null">modification_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderMainId != null">#{workOrderMainId},</if>
            <if test="workOrderTitle != null">#{workOrderTitle},</if>
            <if test="workOrderType != null">#{workOrderType},</if>
            <if test="requesterId != null">#{requesterId},</if>
            <if test="requesterDepartmentId != null">#{requesterDepartmentId},</if>
            <if test="requirementBackground != null">#{requirementBackground},</if>
            <if test="requirementPurpose != null">#{requirementPurpose},</if>
            <if test="requirementDescription != null">#{requirementDescription},</if>
            <if test="requirementSubmissionTime != null">#{requirementSubmissionTime},</if>
            <if test="requirementPriority != null">#{requirementPriority},</if>
            <if test="expectedCompletionDate != null">#{expectedCompletionDate},</if>
            <if test="acceptanceTime != null">#{acceptanceTime},</if>
            <if test="requirementRemark != null">#{requirementRemark},</if>
            <if test="workOrderStatus != null">#{workOrderStatus},</if>
            <if test="rndProgress != null">#{rndProgress},</if>
            <if test="requirementImplementationSystem != null">#{requirementImplementationSystem},</if>
            <if test="currentExecutor != null">#{currentExecutor},</if>
            <if test="systemFunctionModule != null">#{systemFunctionModule},</if>
            <if test="projectRisk != null">#{projectRisk},</if>
            <if test="externalStakeholderInfo != null">#{externalStakeholderInfo},</if>
            <if test="requirementScheduleStartDate != null">#{requirementScheduleStartDate},</if>
            <if test="requirementScheduleEndDate != null">#{requirementScheduleEndDate},</if>
            <if test="designScheduleStartDate != null">#{designScheduleStartDate},</if>
            <if test="designScheduleEndDate != null">#{designScheduleEndDate},</if>
            <if test="developmentScheduleStartDate != null">#{developmentScheduleStartDate},</if>
            <if test="developmentScheduleEndDate != null">#{developmentScheduleEndDate},</if>
            <if test="testingScheduleStartDate != null">#{testingScheduleStartDate},</if>
            <if test="testingScheduleEndDate != null">#{testingScheduleEndDate},</if>
            <if test="acceptanceTestingScheduleStartDate != null">#{acceptanceTestingScheduleStartDate},</if>
            <if test="acceptanceTestingScheduleEndDate != null">#{acceptanceTestingScheduleEndDate},</if>
            <if test="expectedGoLiveDate != null">#{expectedGoLiveDate},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null">#{creator},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="modifier != null">#{modifier},</if>
            <if test="modificationTime != null">#{modificationTime},</if>
         </trim>
    </insert>

    <update id="updateGdxxWorkOrderHistory" parameterType="GdxxWorkOrderHistory">
        update gdxx_work_order_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderMainId != null">work_order_main_id = #{workOrderMainId},</if>
            <if test="workOrderTitle != null">work_order_title = #{workOrderTitle},</if>
            <if test="workOrderType != null">work_order_type = #{workOrderType},</if>
            <if test="requesterId != null">requester_id = #{requesterId},</if>
            <if test="requesterDepartmentId != null">requester_department_id = #{requesterDepartmentId},</if>
            <if test="requirementBackground != null">requirement_background = #{requirementBackground},</if>
            <if test="requirementPurpose != null">requirement_purpose = #{requirementPurpose},</if>
            <if test="requirementDescription != null">requirement_description = #{requirementDescription},</if>
            <if test="requirementSubmissionTime != null">requirement_submission_time = #{requirementSubmissionTime},</if>
            <if test="requirementPriority != null">requirement_priority = #{requirementPriority},</if>
            <if test="expectedCompletionDate != null">expected_completion_date = #{expectedCompletionDate},</if>
            <if test="acceptanceTime != null">acceptance_time = #{acceptanceTime},</if>
            <if test="requirementRemark != null">requirement_remark = #{requirementRemark},</if>
            <if test="workOrderStatus != null">work_order_status = #{workOrderStatus},</if>
            <if test="rndProgress != null">rnd_progress = #{rndProgress},</if>
            <if test="requirementImplementationSystem != null">requirement_implementation_system = #{requirementImplementationSystem},</if>
            <if test="currentExecutor != null">current_executor = #{currentExecutor},</if>
            <if test="systemFunctionModule != null">system_function_module = #{systemFunctionModule},</if>
            <if test="projectRisk != null">project_risk = #{projectRisk},</if>
            <if test="externalStakeholderInfo != null">external_stakeholder_info = #{externalStakeholderInfo},</if>
            <if test="requirementScheduleStartDate != null">requirement_schedule_start_date = #{requirementScheduleStartDate},</if>
            <if test="requirementScheduleEndDate != null">requirement_schedule_end_date = #{requirementScheduleEndDate},</if>
            <if test="designScheduleStartDate != null">design_schedule_start_date = #{designScheduleStartDate},</if>
            <if test="designScheduleEndDate != null">design_schedule_end_date = #{designScheduleEndDate},</if>
            <if test="developmentScheduleStartDate != null">development_schedule_start_date = #{developmentScheduleStartDate},</if>
            <if test="developmentScheduleEndDate != null">development_schedule_end_date = #{developmentScheduleEndDate},</if>
            <if test="testingScheduleStartDate != null">testing_schedule_start_date = #{testingScheduleStartDate},</if>
            <if test="testingScheduleEndDate != null">testing_schedule_end_date = #{testingScheduleEndDate},</if>
            <if test="acceptanceTestingScheduleStartDate != null">acceptance_testing_schedule_start_date = #{acceptanceTestingScheduleStartDate},</if>
            <if test="acceptanceTestingScheduleEndDate != null">acceptance_testing_schedule_end_date = #{acceptanceTestingScheduleEndDate},</if>
            <if test="expectedGoLiveDate != null">expected_go_live_date = #{expectedGoLiveDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modificationTime != null">modification_time = #{modificationTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdxxWorkOrderHistoryById" parameterType="Long">
        delete from gdxx_work_order_history where id = #{id}
    </delete>

    <delete id="deleteGdxxWorkOrderHistoryByIds" parameterType="String">
        delete from gdxx_work_order_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 