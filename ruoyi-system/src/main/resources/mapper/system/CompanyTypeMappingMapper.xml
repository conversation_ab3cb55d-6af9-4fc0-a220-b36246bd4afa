<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CompanyTypeMappingMapper">

    <resultMap type="CompanyTypeMapping" id="CompanyTypeMappingResult">
        <result property="id"    column="id"    />
        <result property="companyId"    column="company_id"    />
        <result property="companyTypeCode"    column="company_type_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCompanyTypeMappingVo">
        select id, company_id, company_type_code, create_by, create_time, update_by, update_time from company_type_mapping
    </sql>

    <select id="selectCompanyTypeMappingList" parameterType="CompanyTypeMapping" resultMap="CompanyTypeMappingResult">
        <include refid="selectCompanyTypeMappingVo"/>
        <where>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="companyTypeCode != null  and companyTypeCode != ''"> and company_type_code = #{companyTypeCode}</if>
        </where>
    </select>

    <select id="selectCompanyTypeMappingById" parameterType="Long" resultMap="CompanyTypeMappingResult">
        <include refid="selectCompanyTypeMappingVo"/>
        where id = #{id}
    </select>

    <insert id="insertCompanyTypeMapping" parameterType="CompanyTypeMapping" useGeneratedKeys="true" keyProperty="id">
        insert into company_type_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">company_id,</if>
            <if test="companyTypeCode != null">company_type_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">#{companyId},</if>
            <if test="companyTypeCode != null">#{companyTypeCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCompanyTypeMapping" parameterType="CompanyTypeMapping">
        update company_type_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="companyTypeCode != null">company_type_code = #{companyTypeCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCompanyTypeMappingById" parameterType="Long">
        delete from company_type_mapping where id = #{id}
    </delete>

    <delete id="deleteCompanyTypeMappingByIds" parameterType="String">
        delete from company_type_mapping where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertCompanyTypeMapping" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into company_type_mapping (company_id, company_type_code, create_by)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.companyId},
            #{item.companyTypeCode},
            #{item.createBy}
            )
        </foreach>
    </insert>

    <delete id="deleteCompanyTypeMappingByCompanyId" parameterType="Long">
        delete from company_type_mapping where company_id = #{companyId}
    </delete>

</mapper>
