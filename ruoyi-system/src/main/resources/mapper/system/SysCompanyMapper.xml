<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysCompanyMapper">

    <resultMap type="SysCompanyVo" id="SysCompanyResult">
        <result property="id"    column="id"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyShortName"    column="company_short_name"    />
        <result property="isInside"    column="is_inside"    />
        <result property="status"    column="status"    />
        <result property="source"    column="source"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="companyCode"    column="company_code"    />
        <result property="companyNo"    column="company_no"    />
        <result property="linkman"    column="linkman"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="postcode"    column="postcode"    />
        <result property="businessAddress"    column="business_address"    />
        <result property="website"    column="website"    />
        <result property="registeredAddress"    column="registered_address"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="projectCount"    column="project_count"    />
        <result property="isDelete"    column="is_delete"    />
        <collection property="companyTypeMappingList" column = "id" select="selectCompanyTypeList"/>
        <collection property="companyBusinessTypeMappingList" column="id" select = "selectCompanyBusinessTypeList" />
    </resultMap>

    <resultMap type="CompanyTypeMappingVo" id="CompanyTypeMappingResult">
        <result property = "dictValue" column="dict_value"/>
        <result property = "dictLabel" column="dict_label"/>
        <result property = "dictCode" column="dict_code"/>
    </resultMap>

    <resultMap type="CompanyBusinessTypeMappingVo" id="CompanyBusinessTypeMappingResult">
        <result property = "dictValue" column="dict_value"/>
        <result property = "dictLabel" column="dict_label"/>
        <result property = "dictCode" column="dict_code"/>
    </resultMap>


    <resultMap id="companyTypeMap" type="CompanyTypeVo">
        <id property="dictCode" column="dict_code"/>
        <result property="dictValue" column="dict_value"/>
        <result property="dictSort" column="dict_sort"/>
        <result property="dictLabel" column="dict_label"/>
        <result property="dictType" column="dict_type"/>
        <result property="remark" column="remark"/>
        <result property="companyCount" column="company_count"/>
    </resultMap>

    <resultMap id="CompanyBusinessTypeMap" type="CompanyBusinessTypeVo">
        <id property="dictCode" column="dict_code"/>
        <result property="dictValue" column="dict_value"/>
        <result property="dictSort" column="dict_sort"/>
        <result property="dictLabel" column="dict_label"/>
        <result property="dictType" column="dict_type"/>
        <result property="remark" column="remark"/>
        <result property="companyCount" column="company_count"/>
        <result property="projectCount" column="project_count" />
    </resultMap>

    <sql id="selectSysCompanyVo">
        select id, company_name, company_short_name,company_code, is_inside, status, source,check_status,company_code, company_no, linkman, phone, email, postcode, business_address, website, registered_address , create_by, create_time, update_by, update_time,is_delete from sys_company
    </sql>

    <select id="selectSysCompanyList" parameterType="sysCompanyVo" resultMap="SysCompanyResult">
        select
            sc.id, sc.company_name, sc.company_short_name, sc.is_inside, sc.status, sc.source, sc.check_status ,company_code, company_no, linkman, phone, email, postcode, business_address, website, registered_address , sc.create_by, sc.create_time, sc.update_by, sc.update_time,
            pc.project_count,sc.is_delete
        from sys_company sc
        left join company_type_mapping ctm on ctm.company_id = sc.id
        left join sys_dict_data sdd on ctm.company_type_code = sdd.dict_code and sdd.dict_type = 'company_type'
        left join company_business_type_mapping cbtm on cbtm.company_id = sc.id
        left join sys_dict_data sddb on cbtm.company_business_type_code = sddb.dict_code and sddb.dict_type = 'company_business_type'
        left join (SELECT
                    unit_id,
                    COUNT(DISTINCT project_id) AS project_count
                    FROM
                    project_company_relevance prc
                    where prc.project_id in (select id from oa_project_deploy)
                    GROUP BY
                    unit_id) pc on sc.id = pc.unit_id
        <where>
            sc.is_delete = '0'
            <if test="companyName != null  and companyName != ''"> and (company_name like concat('%', #{companyName}, '%') or company_short_name like concat('%', #{companyName}, '%'))</if>
            <if test="isInside != null  and isInside != ''"> and is_inside = #{isInside}</if>
            <if test="status != null  and status != ''"> and sc.status = #{status}</if>
            <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
            <if test="auxiliaryField != null and auxiliaryField != ''"> and sdd.auxiliary_field = #{auxiliaryField}</if>
            <if test="companyTypeCode != null  and companyTypeCode != ''">and ctm.company_type_code = #{companyTypeCode}</if>
            <if test="companyTypeCodeNameList != null  and companyTypeCodeNameList.size() ">
                and sdd.dict_label IN
                <foreach collection="companyTypeCodeNameList" item="typeName" open="(" separator="," close=")">
                    #{typeName}
                </foreach>
            </if>
            <if test="companyBusinessTypeCode != null  and companyBusinessTypeCode != ''">and cbtm.company_business_type_code = #{companyBusinessTypeCode}</if>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND sc.id IN
                <foreach collection="companyIdList" item="companyId" open="(" separator="," close=")">
                    #{companyId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        GROUP BY sc.id
        ORDER BY sc.id desc
    </select>

    <select id="selectSysCompanyById" parameterType="Long" resultMap="SysCompanyResult">
        select
            sc.id, sc.company_name, sc.company_short_name, sc.company_code, sc.is_inside, sc.status, sc.source,sc.check_status ,sc.create_by, sc.create_time, sc.update_by, sc.update_time,
            pc.project_count,sc.is_delete
        from sys_company sc
                 left join company_type_mapping ctm on ctm.company_id = sc.id
                 left join sys_dict_data sdd on ctm.company_type_code = sdd.dict_code and sdd.dict_type = 'company_type'
                 left join company_business_type_mapping cbtm on cbtm.company_id = sc.id
                 left join sys_dict_data sddb on cbtm.company_business_type_code = sddb.dict_code and sddb.dict_type = 'company_business_type'
                 left join (SELECT
                                unit_id,
                                COUNT(DISTINCT project_id) AS project_count
                            FROM
                                project_company_relevance prc
                            where prc.project_id in (select id from oa_project_deploy)
                            GROUP BY
                                unit_id) pc on sc.id = pc.unit_id
        where sc.id = #{id}
        GROUP BY sc.id
    </select>

    <insert id="insertSysCompany" parameterType="SysCompanyVo" useGeneratedKeys="true" keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into sys_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="companyShortName != null">company_short_name,</if>
            <if test="isInside != null">is_inside,</if>
            <if test="status != null">status,</if>
            <if test="source != null">source,</if>
            <if test="checkStatus != null and checkStatus != ''">check_status,</if>
            <if test="companyCode != null">company_code,</if>
            <if test="companyNo != null">company_no,</if>
            <if test="linkman != null">linkman,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="postcode != null">postcode,</if>
            <if test="businessAddress != null">business_address,</if>
            <if test="website != null">website,</if>
            <if test="registeredAddress != null">registered_address,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="companyShortName != null">#{companyShortName},</if>
            <if test="isInside != null">#{isInside},</if>
            <if test="status != null">#{status},</if>
            <if test="source != null">#{source},</if>
            <if test="checkStatus != null and checkStatus != ''">#{checkStatus},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyNo != null">#{companyNo},</if>
            <if test="linkman != null">#{linkman},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="postcode != null">#{postcode},</if>
            <if test="businessAddress != null">#{businessAddress},</if>
            <if test="website != null">#{website},</if>
            <if test="registeredAddress != null">#{registeredAddress},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysCompany" parameterType="SysCompany">
        update sys_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyShortName != null">company_short_name = #{companyShortName},</if>
            <if test="isInside != null">is_inside = #{isInside},</if>
            <if test="status != null">status = #{status},</if>
            <if test="source != null">source = #{source},</if>
            <if test="checkStatus != null and checkStatus != ''">check_status = #{checkStatus},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
            <if test="companyNo != null">company_no = #{companyNo},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="postcode != null">postcode = #{postcode},</if>
            <if test="businessAddress != null">business_address = #{businessAddress},</if>
            <if test="website != null">website = #{website},</if>
            <if test="registeredAddress != null">registered_address = #{registeredAddress},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

<!--    <delete id="deleteSysCompanyById" parameterType="Long">-->
<!--        delete from sys_company where id = #{id}-->
<!--    </delete>-->

    <update id="deleteSysCompanyById" parameterType="Long">
        update sys_company
        set is_delete = '1'
        where id = #{id}
    </update>

    <delete id="deleteSysCompanyByIds" parameterType="String">
        delete from sys_company where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectSysCompanyListByCompanyName" parameterType="SysCompany" resultMap="SysCompanyResult">
        <include refid="selectSysCompanyVo"/>
        <where>
            is_delete = '0'
            <if test="companyName != null  and companyName != ''"> and company_name = #{companyName}</if>
        </where>
    </select>

    <select id="selectSysCompanyByCompanyShortName" parameterType="SysCompany" resultMap="SysCompanyResult">
        <include refid="selectSysCompanyVo"/>
        <where>
            is_delete = '0'
            <if test="companyShortName != null  and companyShortName != ''"> and company_short_name = #{companyShortName}</if>
        </where>
        limit 1
    </select>

    <select id="selectSysCompanyListByCheck" parameterType="SysCompany" resultMap="SysCompanyResult">
        <include refid="selectSysCompanyVo"/>
        <where>
            is_delete = '0'
            <if test="companyName != null or companyShortName != null or companyCode != null">
                AND (
                <trim prefixOverrides="OR">
                    <if test="companyName != null and companyName != ''">OR company_name = #{companyName}</if>
                    <if test="companyShortName != null and companyShortName != ''">OR company_short_name = #{companyShortName}</if>
                    <if test="companyCode != null and companyCode != ''">OR company_code = #{companyCode}</if>
                </trim>
                )
            </if>
        </where>
    </select>

    <select id="getCompanyTypeList" resultMap="companyTypeMap" parameterType="SysDictData">
        SELECT
            sdd.dict_code,
            sdd.dict_value,
            sdd.dict_label,
            sdd.dict_type,
            sdd.dict_sort,
            sdd.remark,
            COUNT(DISTINCT ctm.company_id) AS company_count
        FROM
            sys_dict_data sdd
        LEFT JOIN company_type_mapping ctm ON sdd.dict_code = ctm.company_type_code
        WHERE
            sdd.dict_type = 'company_type'
        <if test="dictLabel != null  and dictLabel != ''"> and sdd.dict_label like concat('%', #{dictLabel}, '%')</if>
        GROUP BY
            sdd.dict_code
        order by sdd.dict_sort asc,sdd.dict_code
    </select>

    <select id="getCompanyBusinessTypeList" resultMap="CompanyBusinessTypeMap" parameterType="SysDictData">
        SELECT
            sdd.dict_code,
            sdd.dict_value,
            sdd.dict_label,
            sdd.dict_type,
            sdd.dict_sort,
            sdd.remark,
            COUNT(DISTINCT ctm.company_id) AS company_count,
            COUNT(DISTINCT ptr.project_id) AS project_count
        FROM
            sys_dict_data sdd
        LEFT JOIN company_business_type_mapping ctm ON sdd.dict_code = ctm.company_business_type_code
        LEFT JOIN project_type_relevance ptr ON sdd.dict_code = ptr.type_id
        where sdd.dict_type = 'company_business_type'
        <if test="dictLabel != null  and dictLabel != ''"> and sdd.dict_label like concat('%', #{dictLabel}, '%')</if>
        GROUP BY
            sdd.dict_code
        order by sdd.dict_sort asc,sdd.dict_code
    </select>

    <select id="selectCompanyTypeList" resultMap="CompanyTypeMappingResult">
        select dict_value,dict_label,dict_code,dict_type from company_type_mapping ctm
        left join sys_dict_data sdd on ctm.company_type_code = sdd.dict_code and sdd.dict_type = 'company_type'
        where company_id = #{id}
    </select>

    <select id="selectCompanyBusinessTypeList" resultMap="CompanyBusinessTypeMappingResult">
        select
            odm.data_code as dict_value ,odm.data_name as dict_label,odm.id as dict_code,odm.first_data_code as dict_type
        from company_business_type_mapping cbtm
        left join oa_data_manage odm on cbtm.company_business_type_code = odm.id and odm.first_data_code = 'business_type'
        where cbtm.company_id = #{id}
    </select>

    <select id="getCompanyLogVoList" parameterType="SysOperLog" resultType="CompanyLogVo">
        select MAX(sol.oper_id) as oper_id,nick_name,oper_time,GROUP_CONCAT(sdd.dict_label SEPARATOR '、') AS business_type_string
        from sys_log_query sol
                 left join sys_user user on user.user_name = sol.oper_name
                 left join sys_dict_data sdd on sdd.dict_value = sol.business_type and sdd.dict_type = 'sys_oper_type'
        where sol.title = #{title}
          and sol.function_node = #{functionNode}
          and sol.relation_id = #{relationId}
        group by oper_time
        order by oper_time desc
    </select>

    <select id="selectSysCompanyListForAuthority" resultMap="SysCompanyResult">
        select
            sc.id, sc.company_name, sc.company_short_name, sc.is_inside, sc.status,sc.check_status,
            sc.company_code, sc.company_no, sc.linkman, sc.phone, sc.email, sc.postcode, sc.business_address, sc.website, sc.registered_address,
            sc.create_by, sc.create_time, sc.update_by, sc.update_time,
            pc.project_count,sc.is_delete
        from sys_company sc
        left join company_type_mapping ctm on ctm.company_id = sc.id
        left join sys_dict_data sdd on ctm.company_type_code = sdd.dict_code and sdd.dict_type = 'company_type'
        left join company_business_type_mapping cbtm on cbtm.company_id = sc.id
        left join sys_dict_data sddb on cbtm.company_business_type_code = sddb.dict_code and sddb.dict_type = 'company_business_type'
        left join (SELECT
                    unit_id,
                    COUNT(DISTINCT project_id) AS project_count
                    FROM
                    project_company_relevance prc
                    where prc.project_id in (select id from oa_project_deploy)
                    GROUP BY
                    unit_id) pc on sc.id = pc.unit_id
        <where>
            sc.is_delete = '0'
            <if test="sysCompany.companyName != null  and sysCompany.companyName != ''"> and company_name like concat('%', #{sysCompany.companyName}, '%')</if>
            <if test="sysCompany.companyShortName != null  and sysCompany.companyShortName != ''"> and company_short_name like concat('%', #{sysCompany.companyShortName}, '%')</if>
            <if test="sysCompany.isInside != null  and sysCompany.isInside != ''"> and is_inside = #{sysCompany.isInside}</if>
            <if test="sysCompany.status != null  and sysCompany.status != ''"> and sc.status = #{sysCompany.status}</if>
            <if test="sysCompany.companyTypeCode != null  and sysCompany.companyTypeCode != ''">and ctm.company_type_code = #{sysCompany.companyTypeCode}</if>
            <if test="sysCompany.companyBusinessTypeCode != null  and sysCompany.companyBusinessTypeCode != ''">and cbtm.company_business_type_code = #{sysCompany.companyBusinessTypeCode}</if>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND sc.id IN
                <foreach collection="companyIdList" item="companyId" open="(" separator="," close=")">
                    #{companyId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        GROUP BY sc.id
        ORDER BY sc.id desc
    </select>
    <select id="selectOaProcessClassNum" resultType="java.util.Map">
        select count(*) num from  oa_process_classification where company_id = #{id} and is_company = '0' and status = '0' and del_flag = '0'
    </select>
    <select id="getProjectNumByComId" resultType="java.util.Map">

        select count(*) num from project_company_relevance where  unit_id = #{id}
    </select>

    <select id="selectCompanyListByCompanyShortNames" parameterType="java.util.Set" resultType="SysCompanyVo">
        select
            id, company_name, company_short_name
        from  sys_company
        where is_delete = '0' and  company_short_name in
        <foreach collection="collection" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
    </select>
    <select id="selectCompanyDataByTypeId"  resultMap="SysCompanyResult">
        select
        sc.id, sc.company_name, sc.company_short_name, sc.is_inside, sc.status, sc.source, sc.check_status ,company_code, company_no, linkman, phone, email, postcode, business_address, website, registered_address , sc.create_by, sc.create_time, sc.update_by, sc.update_time,
       sc.is_delete
        from sys_company sc
        left join company_type_mapping ctm on ctm.company_id = sc.id
        where sc.is_delete = '0' and ctm.company_type_code in
        <foreach collection="companyTypeId" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        group by sc.id
    </select>

    <select id="selectSysCompanyListByCompanyNameList" resultMap="SysCompanyResult">
        <include refid="selectSysCompanyVo"/>
        <where>
            is_delete = '0'
            <if test="companyNameList != null and companyNameList.size() != 0">
            and company_name IN
                <foreach collection="companyNameList" item="companyName" open="(" separator="," close=")">
                    #{companyName,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectSysCompanyListForAuthorityNew" resultMap="SysCompanyResult">
        select
            sc.id, sc.company_name, sc.company_short_name, sc.is_inside, sc.status,sc.check_status,
            sc.company_code, sc.company_no, sc.linkman, sc.phone, sc.email, sc.postcode, sc.business_address, sc.website, sc.registered_address,
            sc.create_by, sc.create_time, sc.update_by, sc.update_time,
            pc.project_count,sc.is_delete
        from sys_company sc
        left join company_type_mapping ctm on ctm.company_id = sc.id
        left join sys_dict_data sdd on ctm.company_type_code = sdd.dict_code and sdd.dict_type = 'company_type'
        left join company_business_type_mapping cbtm on cbtm.company_id = sc.id
        left join sys_dict_data sddb on cbtm.company_business_type_code = sddb.dict_code and sddb.dict_type = 'company_business_type'
        left join (SELECT
                    unit_id,
                    COUNT(DISTINCT project_id) AS project_count
                    FROM
                    project_company_relevance prc
                    where prc.project_id in (select id from oa_project_deploy)
                    GROUP BY
                    unit_id) pc on sc.id = pc.unit_id
        <where>
            sc.is_delete = '0'
            <if test="sysCompany.companyName != null  and sysCompany.companyName != ''"> and company_name like concat('%', #{sysCompany.companyName}, '%')</if>
            <if test="sysCompany.companyShortName != null  and sysCompany.companyShortName != ''"> and company_short_name like concat('%', #{sysCompany.companyShortName}, '%')</if>
            <if test="sysCompany.isInside != null  and sysCompany.isInside != ''"> and is_inside = #{sysCompany.isInside}</if>
            <if test="sysCompany.status != null  and sysCompany.status != ''"> and sc.status = #{sysCompany.status}</if>

            and ctm.company_type_code in
            <foreach collection="companyTypeCode" item="companyTypeCode" open="(" separator="," close=")">
                #{companyTypeCode,jdbcType=BIGINT}
            </foreach>
            <if test="sysCompany.companyBusinessTypeCode != null  and sysCompany.companyBusinessTypeCode != ''">and cbtm.company_business_type_code = #{sysCompany.companyBusinessTypeCode}</if>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND sc.id IN
                <foreach collection="companyIdList" item="companyId" open="(" separator="," close=")">
                    #{companyId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        GROUP BY sc.id
        ORDER BY sc.id desc
    </select>

    <select id="selectSysCompanyListByCompanyIdList" resultType="com.ruoyi.system.domain.SysCompany">
        select id, company_short_name from sys_company where id in
        <foreach collection="companyIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and status = '0' and is_delete = '0'
    </select>
</mapper>
