<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysDeptMapper">

	<resultMap type="SysDept" id="SysDeptResult">
		<id     property="deptId"     column="dept_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="deptName"   column="dept_name"   />
		<result property="orderNum"   column="order_num"   />
		<result property="leader"     column="leader"      />
		<result property="phone"      column="phone"       />
		<result property="email"      column="email"       />
		<result property="status"     column="status"      />
		<result property="delFlag"    column="del_flag"    />
		<result property="parentName" column="parent_name" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
		<result property="nodeType" column="node_type" />
		<result property="unitId" column="unit_id" />
		<result property="leaderId" column="leader_id" />
		<result property="postCounts" column="post_counts" />
		<collection  property="postCounts"   javaType="java.lang.Integer" select="selectDeptPostCountsByDeptId" column="dept_id"/>
	</resultMap>

	<sql id="selectDeptVo">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.status, d.del_flag, d.create_by, d.create_time,
        d.node_type,d.unit_id, sc.company_short_name as 'companyShortName', d.leader_id
        from sys_dept d
		left join sys_company sc on d.unit_id = sc.id
    </sql>

	<select id="selectDeptList" parameterType="SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
		<if test="deptId != null and deptId != 0">
			AND dept_id = #{deptId}
		</if>
        <if test="parentId != null and parentId != 0">
			AND parent_id = #{parentId}
		</if>
		<if test="deptName != null and deptName != ''">
			AND dept_name like concat('%', #{deptName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND status = #{status}
		</if>
		<if test="unitId != null and unitId != ''">
			AND unit_id = #{unitId}
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
		order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptPostCountsByDeptId" resultType="java.lang.Integer">
	     select count(1)
		from  sys_post p
		where p.dept_id=#{id}
	</select>

    <select id="selectDeptListByRoleId" resultType="Long">
		select d.dept_id
		from sys_dept d
            left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
            <if test="deptCheckStrictly">
              and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id = rd.dept_id and rd.role_id = #{roleId})
            </if>
		order by d.parent_id, d.order_num
	</select>

    <select id="selectDeptById" parameterType="Long" resultMap="SysDeptResult">
		<include refid="selectDeptVo"/>
		where dept_id = #{deptId}
	</select>

	<select id="selectDeptByLeaderId" parameterType="Long" resultMap="SysDeptResult">
		<include refid="selectDeptVo"/>
		where leader_id = #{leaderId}
	</select>


    <select id="checkDeptExistUser" parameterType="Long" resultType="int">
		select count(1) from sys_user where dept_id = #{deptId} and del_flag = '0'
	</select>

	<select id="hasChildByDeptId" parameterType="Long" resultType="int">
		select count(1) from sys_dept
		where del_flag = '0' and parent_id = #{deptId} limit 1
	</select>

	<select id="selectChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
		select * from sys_dept where find_in_set(#{deptId}, ancestors)
	</select>

	<select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
		select count(*) from sys_dept where status = 0 and del_flag = '0' and find_in_set(#{deptId}, ancestors)
	</select>

	<select id="checkDeptNameUnique" resultMap="SysDeptResult">
	    <include refid="selectDeptVo"/>
		where dept_name=#{deptName} and parent_id = #{parentId} limit 1
	</select>

    <insert id="insertDept" parameterType="SysDept">
 		insert into sys_dept(
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="parentId != null and parentId != 0">parent_id,</if>
 			<if test="deptName != null and deptName != ''">dept_name,</if>
 			<if test="ancestors != null and ancestors != ''">ancestors,</if>
 			<if test="orderNum != null and orderNum != ''">order_num,</if>
 			<if test="leader != null and leader != ''">leader,</if>
 			<if test="phone != null and phone != ''">phone,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="status != null">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="nodeType != null and nodeType != ''">node_type,</if>
 			<if test="unitId != null and unitId != ''">unit_id,</if>
 			<if test="leaderId != null and leaderId != ''">leader_id,</if>
 			create_time
 		)values(
 			<if test="deptId != null and deptId != 0">#{deptId},</if>
 			<if test="parentId != null and parentId != 0">#{parentId},</if>
 			<if test="deptName != null and deptName != ''">#{deptName},</if>
 			<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
 			<if test="orderNum != null and orderNum != ''">#{orderNum},</if>
 			<if test="leader != null and leader != ''">#{leader},</if>
 			<if test="phone != null and phone != ''">#{phone},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="status != null">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="nodeType != null and nodeType != ''">#{nodeType},</if>
 			<if test="unitId != null and unitId != ''">#{unitId},</if>
 			<if test="leaderId != null and leaderId != ''">#{leaderId},</if>
 			sysdate()
 		)
	</insert>

	<update id="updateDept" parameterType="SysDept">
 		update sys_dept
 		<set>
 			<if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
 			<if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
 			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
 			<if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
 			<if test="leader != null">leader = #{leader},</if>
 			<if test="phone != null">phone = #{phone},</if>
 			<if test="email != null">email = #{email},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="leaderId != null and leaderId != ''">leader_id = #{leaderId},</if>
 			<if test="unitId != null and unitId != ''">unit_id = #{unitId},</if>
 			update_time = sysdate()
 		</set>
 		where dept_id = #{deptId}
	</update>

	<update id="updateDeptChildren" parameterType="java.util.List">
	    update sys_dept set ancestors =
	    <foreach collection="depts" item="item" index="index"
	        separator=" " open="case dept_id" close="end">
	        when #{item.deptId} then #{item.ancestors}
	    </foreach>,unit_id =
	    <foreach collection="depts" item="item" index="index"
	        separator=" " open="case dept_id" close="end">
	        when #{item.deptId} then #{item.unitId}
	    </foreach>
	    where dept_id in
	    <foreach collection="depts" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.deptId}
	    </foreach>
	</update>

	<update id="updateDeptStatusNormal" parameterType="Long">
 	    update sys_dept set status = '0' where dept_id in
 	    <foreach collection="array" item="deptId" open="(" separator="," close=")">
        	#{deptId}
        </foreach>
	</update>

	<delete id="deleteDeptById" parameterType="Long">
		update sys_dept set del_flag = '2' where dept_id = #{deptId}
	</delete>


	<select id="selectDeptListByPostIds" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/> LEFT JOIN sys_post p ON d.dept_id=p.dept_id
        <if test="list != null and list.size() != 0">
            WHERE p.post_id
            IN
            <foreach collection="list" item="postId" open="(" separator="," close=")">
                #{postId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="queryAssignDept" resultType="com.ruoyi.common.core.domain.entity.SysPostAO">
		select sp.* from sys_dept sd
			left join sys_post sp on sd.dept_id = sp.dept_id
		<if test="!deptType.equals('') and deptType != null">
			where dept_name like concat('%', #{deptType}, '%') and sd.unit_id = #{companyId}
		</if>
	</select>
	<select id="queryDeptInfo" resultType="com.ruoyi.common.core.domain.entity.SysDept">
		select * from sys_dept where dept_name like concat('%', #{deptName}, '%') and status = '0'
	</select>

	<select id="selectDeptListByParentId" resultType="com.ruoyi.common.core.domain.entity.SysDept">
		select * from sys_dept where dept_id = #{parentId}
	</select>

	<select id="selectDeptListByAncestors" resultType="com.ruoyi.common.core.domain.entity.SysDept">
		select * from sys_dept where ancestors = #{ancestors}
	</select>

	<select id="selectDeptByArrayIds" resultType="com.ruoyi.common.core.domain.entity.SysDept">
		<include refid="selectDeptVo"/>
			where dept_id in
			    <foreach collection="ids" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
	</select>

	<select id="queryDeptInfoByIds" resultType="com.ruoyi.common.core.domain.entity.SysDept">
		<include refid="selectDeptVo"/>
		where d.parent_id = 0 and d.node_type = 0
		<if test="collect.size() > 0">
			and d.dept_id not in
			<foreach collection="collect" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>


	<select id="queryDeptInfoByIdsStr" resultType="com.ruoyi.common.core.domain.entity.SysDept">
		<include refid="selectDeptVo"/>
		<if test="collect.size() > 0">
			and d.dept_id  in
			<foreach collection="collect" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>


	<select id="selectDeptListByCompanyIds" parameterType="java.util.Set" resultType="SysDept">
		select
		dept_id, parent_id,dept_name, unit_id, company_short_name
		from sys_dept dept
		left join sys_company company on dept.unit_id = company.id
		where unit_id in
		<foreach collection="collection" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectNoPermissionDeptList" parameterType="SysDept" resultMap="SysDeptResult">
		select dept_id, parent_id, ancestors, dept_name, order_num,node_type,unit_id
		from sys_dept
		where del_flag = '0'
		<if test="deptId != null and deptId != 0">
			AND dept_id = #{deptId}
		</if>
		<if test="parentId != null and parentId != 0">
			AND parent_id = #{parentId}
		</if>
		<if test="deptName != null and deptName != ''">
			AND dept_name like concat('%', #{deptName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND status = #{status}
		</if>
		<if test="unitId != null and unitId != ''">
			AND unit_id = #{unitId}
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
		order by parent_id, order_num
	</select>
</mapper>
