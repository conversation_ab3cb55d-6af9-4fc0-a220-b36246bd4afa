package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.dto.KfAccountRequestDTO;
import com.ruoyi.system.domain.kf.KfAccountInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客服人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-11
 */
public interface KfAccountInfoMapper 
{
    /**
     * 查询客服人员
     * 
     * @param id 客服人员主键
     * @return 客服人员
     */
    public KfAccountInfo selectKfAccountInfoById(Long id);

    /**
     * 查询客服人员列表
     * 
     * @param kfAccountInfo 客服人员
     * @return 客服人员集合
     */
    public List<KfAccountInfo> selectKfAccountInfoList(KfAccountRequestDTO kfAccountInfo);

    /**
     * 新增客服人员
     * 
     * @param kfAccountInfo 客服人员
     * @return 结果
     */
    public int insertKfAccountInfo(KfAccountInfo kfAccountInfo);

    /**
     * 修改客服人员
     * 
     * @param kfAccountInfo 客服人员
     * @return 结果
     */
    public int updateKfAccountInfo(KfAccountInfo kfAccountInfo);

    /**
     * 删除客服人员
     * 
     * @param id 客服人员主键
     * @return 结果
     */
    public int deleteKfAccountInfoById(Long id);

    /**
     * 批量删除客服人员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKfAccountInfoByIds(Long[] ids);

    /**
     * 根据渠道ID删除客服
     * @param id 渠道
     * @return 删除数量
     */
    Integer deleteByChannelId(@Param("id") Long id);

    /**
     * 根据渠道ID集合查询客服信息
     * @param channelId 渠道ID集合
     * @return 客服集合
     */
    List<Map<String,String>> selectAccountInfoByChannelIds(@Param("list") List<String> channelId);

    /**
     *
     * 通过渠道ID集合查询
     * @param channelIds 渠道ID
     * @return 数据集
     *
     */
    List<String> selectUserNameAndNickNameByChannelIds(@Param("list") List<String> channelIds);
}
