package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.kf.KfWorkOrderRecord;
import com.ruoyi.system.domain.vo.KfWorkOrderRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.util.List;

@Mapper
public interface KfWorkOrderRecordMapper {

    KfWorkOrderRecord getById(@NotNull Long id);

    List<KfWorkOrderRecord> listByEntity(KfWorkOrderRecord kfWorkOrderRecord);

    KfWorkOrderRecord getByEntity(KfWorkOrderRecord kfWorkOrderRecord);

    List<KfWorkOrderRecord> listByIds(List<Long> list);

    int insert(@NotNull KfWorkOrderRecord kfWorkOrderRecord);

    int insertBatch(List<KfWorkOrderRecord> list);

    int update(@NotNull KfWorkOrderRecord kfWorkOrderRecord);

    int updateByField(@NotNull @Param("where") KfWorkOrderRecord where, @NotNull @Param("set") KfWorkOrderRecord set);

    int updateBatch(List<KfWorkOrderRecord> list);

    int deleteById(@NotNull Long id);

    int deleteByEntity(@NotNull KfWorkOrderRecord kfWorkOrderRecord);

    int deleteByIds(List<Long> list);

    int countAll();

    int countByEntity(KfWorkOrderRecord kfWorkOrderRecord);

    List<KfWorkOrderRecordVo> queryRecordListByNum(@NotNull @Param("workOrderNum")String workOrderNum);

    /**
     * 更新状态
     * @param id ID
     * @param workOrderNum 工单编号
     * @return 更新条数
     */
    int updateStatus(@Param("id") Long id, @Param("workOrderNum") String workOrderNum,@Param("accountName") String accountName);


}
