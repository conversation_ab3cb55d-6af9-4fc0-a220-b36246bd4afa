package com.ruoyi.system.mapper;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.ruoyi.system.domain.GdxxWorkOrder;
import org.apache.ibatis.annotations.Param;

/**
 * 工单信息主表Mapper接口
 */
public interface GdxxWorkOrderMapper {
    /**
     * 查询工单信息主表
     * 
     * @param id 工单信息主表主键
     * @return 工单信息主表
     */
    public GdxxWorkOrder selectGdxxWorkOrderById(Long id);

    /**
     * 查询工单信息主表列表
     * 
     * @param gdxxWorkOrder 工单信息主表
     * @return 工单信息主表集合
     */
    public List<GdxxWorkOrder> selectGdxxWorkOrderList(GdxxWorkOrder gdxxWorkOrder);

    /**
     * 新增工单信息主表
     * 
     * @param gdxxWorkOrder 工单信息主表
     * @return 结果
     */
    public int insertGdxxWorkOrder(GdxxWorkOrder gdxxWorkOrder);

    /**
     * 修改工单信息主表
     * 
     * @param gdxxWorkOrder 工单信息主表
     * @return 结果
     */
    public int updateGdxxWorkOrder(GdxxWorkOrder gdxxWorkOrder);

    /**
     * 删除工单信息主表
     * 
     * @param id 工单信息主表主键
     * @return 结果
     */
    public int deleteGdxxWorkOrderById(Long id);

    /**
     * 批量删除工单信息主表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGdxxWorkOrderByIds(Long[] ids);

    Set<Long> getWorkOrderIds(@Param("workOrderTitle") String workOrderTitle,
                              @Param("requesterId")String requesterId,
                              @Param("currentExecutor")String currentExecutor,
                              @Param("workOrderStatus")String workOrderStatus,
                              @Param("rndProgress")String rndProgress,
                              @Param("requirementPriority")String requirementPriority,
                              @Param("requirementSubmissionTime")Date requirementSubmissionTime,
                              @Param("userId") Long userId,
                              @Param("isMeSubmmitFlag")String isMeSubmmitFlag,
                              @Param("isMeJoinFlag")String isMeJoinFlag,
                              @Param("isDoingFlag")String isDoingFlag,
                              @Param("isDaiShouLiFlag")String isDaiShouLiFlag,
                              @Param("completeTime")String completeTime);

    List<GdxxWorkOrder> selectGdxxWorkOrderListByIds(@Param("query") Set<Long> query);
    Long selectGdxxWorkOrderListByIdsCount(@Param("query") Set<Long> query);

    Set<Long> getWorkOrderIdsInCaoGao(@Param("workOrderTitle") String workOrderTitle,
                                      @Param("requesterId")String requesterId,
                                      @Param("currentExecutor")String currentExecutor,
                                      @Param("workOrderStatus")String workOrderStatus,
                                      @Param("rndProgress")String rndProgress,
                                      @Param("requirementPriority")String requirementPriority,
                                      @Param("requirementSubmissionTime")Date requirementSubmissionTime,
                                      @Param("userId") Long userId,
                                      @Param("isMeSubmmitFlag")String isMeSubmmitFlag,
                                      @Param("isMeJoinFlag")String isMeJoinFlag,
                                      @Param("isDoingFlag")String isDoingFlag,
                                      @Param("isDaiShouLiFlag")String isDaiShouLiFlag,
                                      @Param("completeTime")String completeTime);
}