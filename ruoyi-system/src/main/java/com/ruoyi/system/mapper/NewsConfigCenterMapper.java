package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.NewsConfigCenter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.util.List;

@Mapper
public interface NewsConfigCenterMapper {
    /**
     * 通过ID 获取配置信息
     *
     * @param id ID
     * @return 配置信息
     */
    NewsConfigCenter getById(@NotNull Integer id);
    /**
     * 查询数据
     *
     * @param newsConfigCenter 查询参数
     * @return 数据集合
     */
    List<NewsConfigCenter> listByEntity(@Param("newsConfigCenter") NewsConfigCenter newsConfigCenter,@Param("companyCodes") List<String > companyCodes);

    /**
     * 查询数据总数
     *
     * @param newsConfigCenter 查询参数
     * @return 数据集合
     */
    Long countByEntityAndCompanycodes(@Param("newsConfigCenter") NewsConfigCenter newsConfigCenter,@Param("companyCodes") List<String > companyCodes);

    /**
     * 插入数据
     *
     * @param newsConfigCenter 数据参数
     * @return 插入数量
     */
    int insert(@NotNull NewsConfigCenter newsConfigCenter);

    /**
     * 更新数据
     *
     * @param newsConfigCenter 数据参数
     * @return 更新数量
     */
    int update(@NotNull NewsConfigCenter newsConfigCenter);

    /**
     * 批量更新数据
     *
     * @param list 批量数据参数
     * @return 批量更新数量
     */
    int updateBatch(List<NewsConfigCenter> list);
    /**
     * 通过 ID 删除数据
     *
     * @param id ID
     * @return 删除数量
     */
    int deleteById(@NotNull Integer id);

    /**
     * 通过 ID 集合删除数据
     *
     * @param list ID集合
     * @return 删除数量
     */
    int deleteByIds(List<Integer> list);



    /**
     * 通过公司编码找到待发布数据
     *
     * @param companyCode 公司编码
     * @return 待发布数据
     */
    List<NewsConfigCenter> listByCompanyCode(@Param("companyCode") String companyCode);

    /**
     * 通过公司编码更新发布状态
     * @param companyCode 公司编码
     * @return 更新数量
     */
    Integer updateStatusByCompanyCode(@Param("companyCode") String companyCode);

}
