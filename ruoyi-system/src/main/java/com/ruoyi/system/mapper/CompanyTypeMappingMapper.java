package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CompanyTypeMapping;

/**
 * 公司类型映射Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
public interface CompanyTypeMappingMapper
{
    /**
     * 查询公司类型映射
     *
     * @param id 公司类型映射主键
     * @return 公司类型映射
     */
    public CompanyTypeMapping selectCompanyTypeMappingById(Long id);

    /**
     * 查询公司类型映射列表
     *
     * @param companyTypeMapping 公司类型映射
     * @return 公司类型映射集合
     */
    public List<CompanyTypeMapping> selectCompanyTypeMappingList(CompanyTypeMapping companyTypeMapping);

    /**
     * 新增公司类型映射
     *
     * @param companyTypeMapping 公司类型映射
     * @return 结果
     */
    public int insertCompanyTypeMapping(CompanyTypeMapping companyTypeMapping);

    /**
     * 修改公司类型映射
     *
     * @param companyTypeMapping 公司类型映射
     * @return 结果
     */
    public int updateCompanyTypeMapping(CompanyTypeMapping companyTypeMapping);

    /**
     * 删除公司类型映射
     *
     * @param id 公司类型映射主键
     * @return 结果
     */
    public int deleteCompanyTypeMappingById(Long id);

    /**
     * 批量删除公司类型映射
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCompanyTypeMappingByIds(Long[] ids);
    /**
    // 批量插入公司类型映射数据
     */
    int batchInsertCompanyTypeMapping(List<CompanyTypeMapping> companyTypeMappingList);

    public int deleteCompanyTypeMappingByCompanyId(Long companyId);
}
