package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.system.domain.vo.DaArchivistCataloguevVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 归档目录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-28
 */
public interface DDaArchivistCatalogueMapper
{


    /**
     * 新增归档目录
     * 
     * @param daArchivistCatalogue 归档目录
     * @return 结果
     */
    public int insertDaArchivistCatalogue(DaArchivistCataloguevVO daArchivistCatalogue);

    /**
     * 修改归档目录
     * 
     * @param daArchivistCatalogue 归档目录
     * @return 结果
     */
    public int updateDaArchivistCatalogue(DaArchivistCataloguevVO daArchivistCatalogue);

    /**
     * 生成系统编号
     *
     * @param createTime
     * @return 结果
     */
    public int getCountByCreateTime(@Param("createTime") String createTime);

    /**
     * 获取字典数据表中archivist_pertain所属档案库类型
     * @param archivistPertain
     * @return
     */
    List<SysDictData> selectDictDataList(String archivistPertain);
}
