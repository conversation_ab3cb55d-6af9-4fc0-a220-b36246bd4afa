package com.ruoyi.system.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.domain.dto.ChannelTreeDataDTO;
import com.ruoyi.system.domain.kf.KfChannelInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 渠道Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-10
 */
public interface KfChannelInfoMapper 
{
    /**
     * 查询渠道
     * 
     * @param id 渠道主键
     * @return 渠道
     */
    public KfChannelInfo selectKfChannelInfoById(Long id);

    /**
     * 查询渠道列表
     * 
     * @param kfChannelInfo 渠道
     * @return 渠道集合
     */
    public List<KfChannelInfo> selectKfChannelInfoList(KfChannelInfo kfChannelInfo);

    /**
     * 新增渠道
     * 
     * @param kfChannelInfo 渠道
     * @return 结果
     */
    public int insertKfChannelInfo(KfChannelInfo kfChannelInfo);

    /**
     * 修改渠道
     * 
     * @param kfChannelInfo 渠道
     * @return 结果
     */
    public int updateKfChannelInfo(KfChannelInfo kfChannelInfo);

    /**
     * 删除渠道
     * 
     * @param id 渠道主键
     * @return 结果
     */
    public int deleteKfChannelInfoById(Long id);

    /**
     * 批量删除渠道
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKfChannelInfoByIds(Long[] ids);

    /**
     * 查询数量根据公司编码和渠道编码
     * @param companyCode 公司编码
     * @param channelCode 渠道编码
     * @return 数量
     */
    Integer selectChannelByCompanyCodeAndQdCode(@Param("companyCode") String companyCode, @Param("channelCode")String channelCode,@Param("channelName")String channelName);

    /**
     * 查询渠道字典
     * @return 结果集
     */
    List<Map<String, String>> selectChannelDict(@Param("companyCode") String companyCode);

    /**
     * 通过公司编码查询数据
     * @param companyCode 公司编码
     * @return 渠道集合
     */
    List<Map<String, String>> selectChannelByCompanyCode(@Param("companyCode") String companyCode);

    /**
     * 通过公司和渠道信息和id查询数量
     * @param companyCode 公司编码
     * @param channelCode 渠道编码
     * @param id 主键ID
     * @return 查询数量
     */
    Integer selectChannelByCompanyCodeAndQdCodeAndId(@Param("companyCode") String companyCode, @Param("channelCode")String channelCode,@Param("id") Long id);

    /**
     * 按照渠道查询树形结构
     * @return 结构
     */
    List<ChannelTreeDataDTO> tree();


    List<KfChannelInfo> selectKfChannelInfoListByCompanyStatus(KfChannelInfo kfChannelInfo);
}
