package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysPostAO;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPostDeptVo;
import com.ruoyi.system.domain.SysPostMenu;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.SysUserPostVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 岗位信息 数据层
 *
 * <AUTHOR>
 */
public interface SysPostMapper
{
    /**
     * 查询岗位数据集合
     *
     * @param post 岗位信息
     * @return 岗位数据集合
     */
    public List<SysPost> selectPostList(SysPost post);

    /**
     * 查询所有岗位
     *
     * @return 岗位列表
     */
    public List<SysPost> selectPostAll();

    public List<SysPost> selectPostByleader(Long[] leader);

    /**
     * 通过岗位ID查询岗位信息
     *
     * @param postId 岗位ID
     * @return 角色对象信息
     */
    public SysPost selectPostById(Long postId);

    /**
     * 根据用户ID获取岗位选择框列表
     *
     * @param userId 用户ID
     * @return 选中岗位ID列表
     */
    public List<Long> selectPostListByUserId(Long userId);

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    public List<SysPost> selectPostsByUserName(String userName);

    /**
     * 删除岗位信息
     *
     * @param postId 岗位ID
     * @return 结果
     */
    public int deletePostById(Long postId);

    /**
     * 批量删除岗位信息
     *
     * @param postIds 需要删除的岗位ID
     * @return 结果
     */
    public int deletePostByIds(Long[] postIds);

    /**
     * 修改岗位信息
     *
     * @param post 岗位信息
     * @return 结果
     */
    public int updatePost(SysPost post);

    /**
     * 新增岗位信息
     *
     * @param post 岗位信息
     * @return 结果
     */
    public int insertPost(SysPost post);

    /**
     * 校验岗位名称
     *
     * @param post 岗位信息
     * @return 结果
     */
    public SysPost checkPostNameUnique(SysPost post);

    /**
     * 校验岗位编码
     *
     * @param postCode 岗位编码
     * @return 结果
     */
    public SysPost checkPostCodeUnique(String postCode);

    /**
     * 根据用户ID获取PostCode
     *
     * @param userId 用户ID
     * @return 选中岗位PostCode列表
     */
    Set<String> selectPostCodeByUserId(Long userId);
	/**
	 *获取岗位部门人员集合
	 * @return
	 */
	public List<SysUserPostVo> userPostSetList();

	/**
	 * 获取岗位信息集合（含公司及部门信息）
	 * @return
	 */
	public List<SysUserPostVo> postSetList();

    List<SysUserPostVo> getPostAuthorizationList(SysPost sysPost);

    //根据用户名查询用户的主岗位
    SysPost selectMainPostByUserName(String userName);

    /**
     * 根据用户id查询岗位信息集合
     * @param userId
     * @return
     */
    List<Long> selectPostInfoListByUserId(Long userId);
    /**
     * 通过username获取岗位列表
     * @return
     */
    public List<SysUserPostVo> getPostListByUserName(String[] userNames);

    /**
     * 通过userName获取该用户的主岗位信息
     * @param userName
     * @return
     */
    SysPost queryHomeSysPost(@Param("userName") String userName);

    /**
     * add by nieyi 根据岗位id查询菜单数据集
     * @return
     */
    List<SysMenu> selectPostMenuListByPostId(Long postId);

    /**
     * add by nieyi 根据岗位id删除与菜单的关联关系
     * @param postId
     */
    void deletePostMenuInfoByPostId(Long postId);

    /**
     * 通过用户ID获取用户的主岗位
     * @param userId 用户ID
     * @return 主岗位ID
     */
    Long selectHomePostByUserId(Long userId);

    List<SysPost> selectDataByUserId(@Param("userId")Long userId);

    /**
     * 根据用户ID查询岗位
     *
     * @param userId 用户ID
     * @return 岗位列表
     */
    List<SysPost> selectRolePermissionByUserId(Long userId);

    /**
     * 根据部门id查询所有的岗位
     * @param deptId
     * @return
     */
    List<SysUserPostDeptVo> selectPostListByDeptId(Long deptId);

    List<SysPost> selectAllPostInfo();

    /**
     * 根据岗位id和菜单id批量删除菜单岗位关联表数据
     * @param delMenuList
     * @param postId
     * @return
     */
    int deletePostMenuRelation(@Param("delMenuList") List<Long> delMenuList, @Param("postId") Long postId);

    List<SysPost> selectPostByCode(@Param("postCode") String postCode);

    /**
     * 根据岗位id查询用户列表
     * @param postId
     * @return
     */
    List<SysUser> selectPostUserByPostId(Long postId);

    List<SysPost> selectNoPermissionPostList(SysPost post);
}
