package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.parameters.P;

import java.util.Date;

/**
 * 客服工单表(com.ruoyi.system.domain.kf.KfWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2024-09-29 10:44:13
 */
@Data
public class KfWorkOrderVo {
    /**
     * 主键ID
     */
    private Long id ;
    /**
     * 公司编码
     */
    private String companyNum;
    /**
     * 渠道编码
     */
    private String channelNum;
    /**
     * 工单来源
     */
    private String orderSourceNum;
    /**
     * 工单编码
     */
    private String workOrderNum;
    /**
     * 客户手机号
     */
    private String userPhone;
    /**
     * 客户身份证号
     */
    private String userIdCardNum;

    private String userCompanyName;
    /**
     * 客户名称
     */
    private String userName;
    /**
     * 工单内容
     */
    private String workOrderText;
    /**
     * 认领人用户ID
     */
    private String claimUserId;
    /**
     * 认领人姓名
     */
    private String claimUserName;
    /**
     * 工单状态（READY待认领、LOAD处理中、COMPLETE
     * 已完成、ABANDON不处理）
     */
    private String workOrderStatus;

    // 编辑记录
    private String kcount;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private String createTime;


    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private String lastUpdateTime;

}
