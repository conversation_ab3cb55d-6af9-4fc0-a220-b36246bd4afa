package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 工单信息主表对象 gdxx_work_order
 */
@Data
public class GdxxWorkOrderQueryVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 工单主题 */
    private String workOrderTitle;

    /** 工单类型字典id */
    private String workOrderType;

    /** 需求提出人id */
    private String requesterId;

    /** 需求提出部门id */
    private String requesterDepartmentId;

    /** 需求背景 */
    private String requirementBackground;

    /** 需求目的 */
    private String requirementPurpose;

    /** 需求描述 */
    private String requirementDescription;

    /** 需求提出时间 */
    private String requirementSubmissionTime;
    /** 受理时间 */
    private String acceptanceTime;

    /** 需求优先级字典id */
    private String requirementPriority;

    /** 期望完成时间 */
    private String expectedCompletionDate;

    /** 需求备注 */
    private String requirementRemark;

    /** 工单状态字典id */
    private String workOrderStatus;

    /** 研发进度字典id */
    private String rndProgress;

    /** 需求实现系统字典id */
    private String requirementImplementationSystem;

    /** 系统功能模块 */
    private String systemFunctionModule;

    /** 项目风险字典ID */
    private String projectRisk;

    /** 需求外部相关人情况 */
    private String externalStakeholderInfo;

    /** 当前执行人id */
    private String currentExecutor;

    /** 需求排期-开始时间 */
    private String requirementScheduleStartDate;

    /** 需求排期-结束时间 */
    private String requirementScheduleEndDate;

    /** 设计排期-开始时间 */
    private String designScheduleStartDate;

    /** 设计排期-结束时间 */
    private String designScheduleEndDate;

    /** 开发排期-开始时间 */
    private String developmentScheduleStartDate;

    /** 开发排期-结束时间 */
    private String developmentScheduleEndDate;

    /** 测试排期-开始时间 */
    private String testingScheduleStartDate;

    /** 测试排期-结束时间 */
    private String testingScheduleEndDate;

    /** 验收测试排期-开始时间 */
    private String acceptanceTestingScheduleStartDate;

    /** 验收测试排期-结束时间 */
    private String acceptanceTestingScheduleEndDate;

    /** 预计上线时间 */
    private String expectedGoLiveDate;

    /** 状态（0正常 1停用） */
    private String status;
    /** 创建时间 */
    private Date creationTime;
    /** 创建人 */
    private String creator;
    /** 修改时间 */
    private Date modificationTime;
    /** 修改人 */
    private String modifier;

    // 我提出的(0 开，1和空为关 )
    private String isMeSubmmitFlag;
    // 我参与的(0 开，1和空为关 )
    private String isMeJoinFlag;
    // 进行中的(0 开，1和空为关 )
    private String isDoingFlag;
    // 待受理的(0 开，1和空为关 )
    private String isDaiShouLiFlag;
    // 完成时间
    private String completeTime;
}