package com.ruoyi.system.domain.vo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @Author: 左东冉
 * @Create: 2024-09-29 16:45
 * @Description: 共享详情视图
 **/
@Data
public class KfWorkOrderNumVo {
   // 工作记录表或者客户备注表ID
    private Long id;
    // 工单编码
    @NotBlank
    private String workOrderNum;
    // RECORD 记录表  REMARK 备注
    @NotBlank
    private String orderType;
  // 账号
    private String accountName;
    // 工单管理页面请求区别
    private Boolean isShow;

}
