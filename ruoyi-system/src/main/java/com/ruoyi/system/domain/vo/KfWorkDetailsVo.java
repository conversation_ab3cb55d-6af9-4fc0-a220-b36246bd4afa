package com.ruoyi.system.domain.vo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @Author: 左东冉
 * @Create: 2024-09-29 16:45
 * @Description: 共享详情视图
 **/
@Data
public class KfWorkDetailsVo {
    // ID
    @NotBlank
    private String id;
    // 工单编码
    @NotBlank
    private String workOrderNum;
    // 操作类型
    @NotBlank
    private String workOptionType;

    // 工作记录和客户备注
    @Length(min = 0, max = 500, message = "记录信息不能超过500字")
    private String infoText;
}
