package com.ruoyi.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 客服工单请求参数
 *
 * <AUTHOR>
 * @since 2024-09-29 10:44:13
 */
@Data
public class KfWorkOrderDTO  extends BaseEntity {
    private Long id ;

    private String workOrderNum;
    /**
     * 客户名称
     */
    private String userName;
    /**
     * 客户手机号
     */
    private String userPhone;
    /**
     * 客户身份证号
     */
    private String userIdCardNum;
    /**
     * 工单内容
     */
    private String workOrderText;

    /**
     * 工单来源编码
     */
    private String orderSourceNum;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 最后修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    /**
     * 公司编码
     */
    @NotBlank
    private String companyNum;

    /**
     * 渠道编码
     */
    private List<String> channelNumList;

    /**
     * 工单状态（READY待认领、LOAD处理中、COMPLETE
     * 已完成、ABANDON不处理）
     */
    private String workOrderStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date wcStartTime;

    /**
     * 最后修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date wcEndTime;


    // 判断是否从工单管理中请求
    private Boolean isSystemFlag = null;

    private Integer pageSize = 10;
    private Integer pageNum = 1;
}
