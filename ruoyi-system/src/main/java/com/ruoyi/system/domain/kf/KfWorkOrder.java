package com.ruoyi.system.domain.kf;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 客服工单表(com.ruoyi.system.domain.kf.KfWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2024-09-29 10:44:13
 */
@Data
public class KfWorkOrder {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 工单编码
     */
    private String workOrderNum;
    /**
     * 公司编码
     */
    private String companyNum;
    /**
     * 渠道编码
     */
    private String channelNum;
    /**
     * 工单来源编码
     */
    private String orderSourceNum;
    /**
     * 客户手机号
     */
    private String userPhone;
    /**
     * 客户姓名
     */
    private String userName;
    /**
     * 客户身份证号
     */
    private String userIdCardNum;

    private String userCompanyName;
    /**
     * 工单内容
     */
    private String workOrderText;
    /**
     * 认领人用户ID
     */
    private String claimUserId;
    /**
     * 工单状态（READY待认领、LOAD处理中、COMPLETE
     * 已完成、ABANDON不处理）
     */
    private String workOrderStatus;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 最后修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateTime;
    /**
     * 修改人
     */
    private String updateBy;

}
