package com.ruoyi.system.domain.kf;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 客服人员对象 kf_account_info
 * 
 * <AUTHOR>
 * @date 2024-10-11
 */
public class KfAccountInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 账号编码 */
    @Excel(name = "账号编码")
    private String accountCode;

    /** 账号姓名 */
    @Excel(name = "账号姓名")
    private String accountName;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    private String channelId;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 最后修改时间 */
    private Date lastUpdateTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAccountCode(String accountCode) 
    {
        this.accountCode = accountCode;
    }

    public String getAccountCode() 
    {
        return accountCode;
    }
    public void setAccountName(String accountName) 
    {
        this.accountName = accountName;
    }

    public String getAccountName() 
    {
        return accountName;
    }
    public void setChannelId(String channelId) 
    {
        this.channelId = channelId;
    }

    public String getChannelId() 
    {
        return channelId;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setLastUpdateTime(Date lastUpdateTime) 
    {
        this.lastUpdateTime = lastUpdateTime;
    }

    public Date getLastUpdateTime() 
    {
        return lastUpdateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("accountCode", getAccountCode())
            .append("accountName", getAccountName())
            .append("channelId", getChannelId())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("lastUpdateTime", getLastUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
