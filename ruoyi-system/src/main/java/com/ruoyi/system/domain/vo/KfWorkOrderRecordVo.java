package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: 左东冉
 * @Create: 2024-09-30 09:26
 * @Description: 工作记录查询类
 **/
@Data
public class KfWorkOrderRecordVo {
    // ID
    private Long id;

    // 账号名称
    private String accountName;

    // 用户名称
    private String userName;

    // 操作类型
    private String optionType;

    // 备注文本
    private String infoText;

    /*
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
}
