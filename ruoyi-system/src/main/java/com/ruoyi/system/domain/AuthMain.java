package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 权限主对象 auth_main
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Data
public class AuthMain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 授权的第三方类型：0未定义1用户2岗位3代理给其他人授权4代理给其他人工作 */
    @Excel(name = "授权的第三方类型：0未定义1用户2岗位")
    private String thirdType;

    /** 授权的第三方主键 */
    @Excel(name = "授权的第三方主键")
    private Long thirdId;

    /** 模块，详见枚举AuthModuleEnum */
    @Excel(name = "模块，详见枚举AuthModuleEnum")
    private String moduleType;

    /** 角色类型，详见枚举AuthRoleEnum */
    @Excel(name = "角色类型，详见枚举AuthRoleEnum")
    private String roleType;

    /** 权限范围，详见枚举AuthPermissionEnum */
    @Excel(name = "权限范围，详见枚举AuthPermissionEnum")
    private String permissionScope;

    /** 授权类型：0未定义1长期2有效期 */
    @Excel(name = "授权类型：0未定义1长期2有效期")
    private String permissionType;

    /** 授权有效期，长期有效时设置为9999-12-31 23:59:59 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "授权有效期，长期有效时设置为9999-12-31 23:59:59", width = 30, dateFormat = "yyyy-MM-dd")
    private Date permissionTime;

    /** 状态，0正常 1停用（失效） */
    @Excel(name = "状态，0正常 1停用", readConverterExp = "失=效")
    private String status;

    /** 代办通知(-1未发送 7(即将到期通知) 0(到期通知)) */
    private String sendNotify;

    /** 创建者id */
    @Excel(name = "创建者id")
    private Long createId;

    /** 创建者主岗部门id */
    @Excel(name = "创建者主岗部门id")
    private Long createDeptId;

    /** 创建者主岗公司id */
    @Excel(name = "创建者主岗公司id")
    private Long createUnitId;

    /** 创建者id */
    @Excel(name = "创建者id")
    private Long updateId;

    /** 创建者主岗部门id */
    @Excel(name = "创建者主岗部门id")
    private Long updateDeptId;

    /** 创建者主岗公司id */
    @Excel(name = "创建者主岗公司id")
    private Long updateUnitId;

}
