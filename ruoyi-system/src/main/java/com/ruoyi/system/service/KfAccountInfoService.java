package com.ruoyi.system.service;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.dto.KfAccountRequestDTO;
import com.ruoyi.system.domain.kf.KfAccountInfo;

import java.util.List;

/**
 * 客服人员Service接口
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
public interface KfAccountInfoService {
    /**
     * 查询客服人员
     *
     * @param id 客服人员主键
     * @return 客服人员
     */
    public KfAccountInfo selectKfAccountInfoById(Long id);

    /**
     * 查询客服人员列表
     *
     * @param kfAccountInfo 客服人员
     * @return 客服人员集合
     */
    public List<KfAccountInfo> selectKfAccountInfoList(KfAccountRequestDTO kfAccountInfo);

    /**
     * 新增客服人员
     *
     * @param kfAccountInfo 客服人员
     * @return 结果
     */
    public int insertKfAccountInfo(KfAccountInfo kfAccountInfo);

    /**
     * 修改客服人员
     *
     * @param kfAccountInfo 客服人员
     * @return 结果
     */
    public int updateKfAccountInfo(KfAccountInfo kfAccountInfo);

    /**
     * 批量删除客服人员
     *
     * @param ids 需要删除的客服人员主键集合
     * @return 结果
     */
    public int deleteKfAccountInfoByIds(Long[] ids);

    /**
     * 删除客服人员信息
     *
     * @param id 客服人员主键
     * @return 结果
     */
    public int deleteKfAccountInfoById(Long id);

    /**
     * 获取左侧树状图
     * @return 树状图
     */
    AjaxResult treeList();


    /**
     * 获取渠道列表
     * @param companyCode 公司
     * @return 公司配置的渠道
     */
    AjaxResult channelList(String companyCode);

    /**
     * 通过多个渠道IDs查询用户
     * @param channelIds 渠道IDs
     * @return 结果集
     */
    AjaxResult accountList(List<String> channelIds);
}

