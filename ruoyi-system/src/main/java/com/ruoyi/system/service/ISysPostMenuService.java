package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysPostMenu;

/**
 * 【岗位菜单关联关系】Service接口
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
public interface ISysPostMenuService
{
    /**
     * 查询【请填写功能名称】
     * @param postId 岗位主键
     * @return 【请填写功能名称】
     */
    public SysPostMenu selectSysPostMenuByMenuId(Long postId);

    /**
     * 新增岗位菜单关联关系
     * @param sysPostMenuList
     * @return 结果
     */
    public int insertSysPostMenu(List<SysPostMenu> sysPostMenuList);

    /**
     * 删除岗位菜单关联关系信息
     * @param postId 岗位主键
     * @return 结果
     */
    public int deleteSysPostMenuByMenuId(Long postId);
}
