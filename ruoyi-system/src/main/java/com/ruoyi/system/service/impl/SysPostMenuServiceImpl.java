package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ISysPostMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysPostMenuMapper;
import com.ruoyi.system.domain.SysPostMenu;

/**
 * 【岗位菜单关联关系】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-22
 */
@Service
public class SysPostMenuServiceImpl implements ISysPostMenuService
{
    @Autowired
    private SysPostMenuMapper sysPostMenuMapper;

    /**
     * 查询
     * @param postId 【岗位】主键
     * @return
     */
    @Override
    public SysPostMenu selectSysPostMenuByMenuId(Long postId)
    {
        return sysPostMenuMapper.selectSysPostMenuByMenuId(postId);
    }

    /**
     * 新增岗位菜单关联关系
     * @param sysPostMenuList 岗位菜单关联表
     * @return 结果
     */
    @Override
    public int insertSysPostMenu(List<SysPostMenu> sysPostMenuList)
    {
        return sysPostMenuMapper.insertSysPostMenu(sysPostMenuList);
    }

    /**
     * 删除岗位菜单关联关系信息
     * @param postId 【岗位】主键
     * @return 结果
     */
    @Override
    public int deleteSysPostMenuByMenuId(Long postId)
    {
        return sysPostMenuMapper.deleteSysPostMenuByMenuId(postId);
    }
}
