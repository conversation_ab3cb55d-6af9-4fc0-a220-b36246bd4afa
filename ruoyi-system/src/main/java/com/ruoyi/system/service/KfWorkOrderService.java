package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.dto.KfWorkDataRequestDTO;
import com.ruoyi.system.domain.dto.KfWorkOrderDTO;
import com.ruoyi.system.domain.kf.KfFileInfo;
import com.ruoyi.system.domain.kf.KfWorkOrder;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.mapper.KfWorkOrderMapper;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface KfWorkOrderService {

    KfWorkOrderMapper getKfWorkOrderMapper();

    KfWorkOrderVo getById(Long id);

    KfWorkOrder getByEntity(KfWorkOrder kfWorkOrder);

    List<KfWorkOrder> listByEntity(KfWorkOrder kfWorkOrder);

    List<KfWorkOrder> listByIds(List<Long> ids);

    int insert(KfWorkOrder kfWorkOrder);

    int insertBatch(List<KfWorkOrder> list);

    int update(KfWorkOrder kfWorkOrder);

    int updateBatch(List<KfWorkOrder> list);

    int deleteById(Long id);

    int deleteByEntity(KfWorkOrder kfWorkOrder);

    int deleteByIds(List<Long> list);

    int countAll();

    int countByEntity(KfWorkOrder kfWorkOrder);

    Map<String, Long> selectIncompleteCompanyList(Boolean flag);

    Map<String, String> undone();

    /**
     * 官网提交客诉
     * @param request 请求
     * @param files 附件
     * @return 响应
     */
    AjaxResult psDataRequest(KfWorkDataRequestDTO request,List<MultipartFile> files);

    List<KfWorkOrderVo> workList(KfWorkOrderDTO kfWorkOrderDTO);

    AjaxResult workStatusUpdate(KfWorkDetailsVo kfWorkDetailsVo);

    AjaxResult workRemarkInsert(KfWorkDetailsVo kfWorkDetailsVo);

    /**
     * 查询 工作记录和客户备注
     *
     * @param kfWorkDetailsVo 请求参数
     * @return 结果
     */
    AjaxResult workRemarkQuery(KfWorkOrderNumVo kfWorkDetailsVo);

    /**
     * 删除 工作记录和客户备注
     *
     * @param kfWorkDetailsVo 请求参数
     * @return 结果
     */
    AjaxResult workRemarkDelete(KfWorkOrderNumVo kfWorkDetailsVo);

    /**
     * 通过工单表ID和工单编码进行查询用户明文数据
     *
     * @param kfWorkDetailsVo 请求参数
     * @return 结果
     */
    AjaxResult customInfoQuery(KfWorkOrderNumVo kfWorkDetailsVo);

    /**
     * 获取新增数据结果
     *
     * @param request  请求参数
     * @param gdlyFlag 是否是官网提交
     * @param files 文件
     * @return 响应结果
     */
    AjaxResult getAjaxResult(KfWorkDataRequestDTO request, Boolean gdlyFlag, List<MultipartFile> files);

    /**
     * 获取工单数量
     *
     * @param kfWorkOrderDTO 参数
     * @return 数量
     */
    Integer workListCount(KfWorkOrderDTO kfWorkOrderDTO);

    /**
     * 工作记录
     *
     * @param kfWorkOrderDTO 工单记录
     * @return 返回
     */
    AjaxResult workUpdate(KfWorkOrderDTO kfWorkOrderDTO);


    /**
     * 通过工单编码获取所有文件信息
     *
     * @param kfWorkNumVo 工单信息
     * @return 数据集合
     */
    List<KfFileInfo> getFileListInfo(KfWorkNumVo kfWorkNumVo);

    /**
     * 下载文件并且压缩
     *
     * @param kfWorkNumVo 文件下载请求参数
     * @param response    响应类
     */
    void downloadFileZip(KfWorkFileVo kfWorkNumVo, HttpServletResponse response);

}
