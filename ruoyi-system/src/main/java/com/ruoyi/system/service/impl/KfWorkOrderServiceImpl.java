package com.ruoyi.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.KFConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PinYinUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.kf.KfRequestValidatedUtils;
import com.ruoyi.common.utils.oss.OssClientUtil;
import com.ruoyi.common.utils.sign.Sm2KFUtils;
import com.ruoyi.system.domain.dto.KfWorkDataRequestDTO;
import com.ruoyi.system.domain.dto.KfWorkOrderDTO;
import com.ruoyi.system.domain.kf.*;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.KfWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class KfWorkOrderServiceImpl implements KfWorkOrderService {

    @Resource(type = KfWorkOrderMapper.class)
    private KfWorkOrderMapper kfWorkOrderMapper;

    @Autowired
    private KfCustomUpdateInfoMapper kfCustomUpdateInfoMapper;

    @Autowired
    private KfWorkOrderRecordMapper recordMapper;

    @Autowired
    private KfWordOrderRemarkMapper remarkMapper;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private KfFileInfoMapper kfFileInfoMapper;

    @Autowired
    private OssClientUtil ossClientUtil;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SysDictTypeServiceImpl sysDictTypeService;
    @Autowired
    private KfChannelInfoMapper kfChannelInfoMapper;
    @Autowired
    private KfAccountInfoMapper kfAccountInfoMapper;
    @Autowired
    private KfCompanyInfoMapper kfCompanyInfoMapper;
    @Override
    public KfWorkOrderMapper getKfWorkOrderMapper() {
        return kfWorkOrderMapper;
    }

    @Override
    public KfWorkOrderVo getById(Long id) {
        KfWorkOrderVo byId = kfWorkOrderMapper.getVoById(id);
        if (byId == null ) return null;
        SysUser sysUser = sysUserMapper.selectUserByUserName(byId.getClaimUserId());

        byId.setUserPhone(KfRequestValidatedUtils.desensitizePhoneNumber(byId.getUserPhone()));
        byId.setUserIdCardNum(KfRequestValidatedUtils.desensitizeIDCard(byId.getUserIdCardNum()));
        byId.setClaimUserName(sysUser == null || StringUtils.isEmpty(sysUser.getNickName()) ? null : sysUser.getNickName());
        return byId;
    }

    @Override
    public KfWorkOrder getByEntity(KfWorkOrder kfWorkOrder) {
        return kfWorkOrderMapper.getByEntity(kfWorkOrder);
    }

    @Override
    public List<KfWorkOrder> listByEntity(KfWorkOrder kfWorkOrder) {
        return kfWorkOrderMapper.listByEntity(kfWorkOrder);
    }

    @Override
    public List<KfWorkOrder> listByIds(List<Long> ids) {
        return kfWorkOrderMapper.listByIds(ids);
    }

    @Override
    public int insert(KfWorkOrder kfWorkOrder) {
        Date date = new Date();
        kfWorkOrder.setCreateTime(date);
        kfWorkOrder.setLastUpdateTime(date);
        return kfWorkOrderMapper.insert(kfWorkOrder);
    }

    @Override
    public int insertBatch(List<KfWorkOrder> list) {
        return kfWorkOrderMapper.insertBatch(list);
    }

    @Override
    public int update(KfWorkOrder kfWorkOrder) {
        kfWorkOrder.setLastUpdateTime(new Date());
        return kfWorkOrderMapper.update(kfWorkOrder);
    }

    @Override
    public int updateBatch(List<KfWorkOrder> list) {
        return kfWorkOrderMapper.updateBatch(list);
    }

    @Override
    public int deleteById(Long id) {
        return kfWorkOrderMapper.deleteById(id);
    }

    @Override
    public int deleteByEntity(KfWorkOrder kfWorkOrder) {
        return kfWorkOrderMapper.deleteByEntity(kfWorkOrder);
    }

    @Override
    public int deleteByIds(List<Long> list) {
        return kfWorkOrderMapper.deleteByIds(list);
    }

    @Override
    public int countAll() {
        return kfWorkOrderMapper.countAll();
    }

    @Override
    public int countByEntity(KfWorkOrder kfWorkOrder) {
        return kfWorkOrderMapper.countByEntity(kfWorkOrder);
    }

    @Override
    public Map<String, Long> selectIncompleteCompanyList(Boolean flag) {
        Map<String, Long> combinedMap = new HashMap<>();
        if (null != flag && flag) {
            List<KfCompanyInfo> kfCompanyInfos = kfCompanyInfoMapper.selectKfCompanyInfoList(null);
            for (KfCompanyInfo kfCompanyInfo : kfCompanyInfos) {
                combinedMap.put(kfCompanyInfo.getCompanyCode(),0L);
            }
        } else {
            List<Map<String, String>> selectIncompleteCompanyList = kfWorkOrderMapper.selectIncompleteCompanyList();
            // 查询该用户的所有配置好的公司和渠道
            List<Map<String, String>> listMap = kfAccountInfoMapper.selectAllComAndChaInfoByUserName(SecurityUtils.getUsername());
            for (Map<String, String> stringMap : listMap) {
                String companyNumAll = stringMap.get("companyNum");
                String channelNumAll = stringMap.get("channelNum");
                combinedMap.putIfAbsent(companyNumAll, 0L);
                for (Map<String, String> map : selectIncompleteCompanyList) {
                    String companyNum = map.get("companyNum");
                    String channelNum = map.get("channelNum");
                    if (companyNum.equals(companyNumAll) && channelNum.equals(channelNumAll)) {
                        combinedMap.merge(companyNum, 1L, Long::sum);
                    }
                }
            }
        }
        return combinedMap;
    }

    @Override
    public Map<String, String> undone() {
        Map<String, String> map = new HashMap<>();
        String replaceStr = System.currentTimeMillis() + UUID.randomUUID().toString().replace("-", "");
        List<Object> cacheList = redisCache.getCacheList(KFConstants.SM2_HHID +replaceStr);
        while (CollectionUtils.isNotEmpty(cacheList)) {
            replaceStr = System.currentTimeMillis() + UUID.randomUUID().toString().replace("-", "");
            cacheList = redisCache.getCacheList(KFConstants.SM2_HHID +replaceStr);
        }
        Map<String, String> keys = Sm2KFUtils.generateCommonKey();
        redisCache.setCacheObject(KFConstants.SM2_HHID + replaceStr, keys, 30, TimeUnit.MINUTES);
        map.put("undone1", replaceStr);
        map.put("undone2", keys.get("publicKey"));
        log.info("获取会话ID和AES秘钥结果:{}", JSON.toJSONString(map));
        return map;
    }
    @Override
    public AjaxResult psDataRequest(KfWorkDataRequestDTO request, List<MultipartFile> files) {
        log.info("官网提交工单请求参数:{}", JSON.toJSONString(request));
        try {
            String flag = handleRequestNoSuccess(request);
            if (StringUtils.isNotEmpty(flag)) {
                return AjaxResult.error(flag);
            } else {
                return getAjaxResult(request,true, files);
            }
        } catch (Exception e) {
            redisCache.deleteObject(KFConstants.SM2_HHID + request.getWorkData4());
            throw e;
        }
    }

    /**
     * 抽取公用方法
     *
     * @param request 明文请求参数
     * @return 结果
     */
    @Override
    public AjaxResult getAjaxResult(KfWorkDataRequestDTO request, Boolean gdlyFlag, List<MultipartFile> files) {
        if (StringUtils.isEmpty(request.getWorkData2()) && StringUtils.isEmpty(request.getWorkData7()) && !gdlyFlag)  return AjaxResult.error("手机号码与姓名至少需要填写其中一项");
        boolean flag = handleVaRequestParam(request);
        if (flag) return AjaxResult.error("手机号码和身份证号码校验参数错误");
        String username = null;
        try {
            username = SecurityUtils.getUsername();
        } catch (Exception e) {
            username = "default";
        }
        int count = kfWorkOrderMapper.queryCountByIdCard(request.getWorkData1(), request.getWorkData2(),request.getWorkData7());
        if (count >= 5) {
            return AjaxResult.error("今日可发起工单数量超过限制！");
        }

        // 额外处理数据
        if ((KFConstants.KF_CHANNEL_PRE + "QT").equals(request.getWorkData6()) && StringUtils.isNotEmpty(request.getWorkData8())) {
            String s = sysDictDataMapper.selectDictValue(KFConstants.KF_CHANNEL_TYPE, request.getWorkData8());
            if (StringUtils.isNotEmpty(s)) return AjaxResult.error("您所选的渠道下拉框已经存在，请在下拉框中选择。");
            String pyCover = PinYinUtils.zwCoverPy(request.getWorkData8());
            String pyData = KFConstants.KF_CHANNEL_PRE + pyCover;
            if (StringUtils.isEmpty(pyCover)) return AjaxResult.error("输入的其他渠道没有汉字，请添加中文汉字。");
            pyData = pyData + UUID.randomUUID().toString();
            SysDictData dictData = new SysDictData();
            dictData.setListClass("default");
            dictData.setDictLabel(request.getWorkData8());
            dictData.setDictValue(pyData);
            dictData.setDictType(KFConstants.KF_CHANNEL_TYPE);
            dictData.setStatus("0");
            sysDictDataMapper.insertDictData(dictData);
            request.setWorkData6(pyData);
            sysDictTypeService.loadingDictCache();
            // 插入渠道表
            KfChannelInfo kfChannelInfo = new KfChannelInfo();
            kfChannelInfo.setChannelCode(pyData);
            kfChannelInfo.setChannelName(request.getWorkData8());
            kfChannelInfo.setCompanyCode(request.getWorkData5());
            kfChannelInfo.setStatus("0");
            kfChannelInfo.setCreateBy(SecurityUtils.getUsername());
            kfChannelInfo.setCreateTime(new Date());
            kfChannelInfoMapper.insertKfChannelInfo(kfChannelInfo);
        }

        String replaceStr = System.currentTimeMillis() + UUID.randomUUID().toString().replace("-", "");
        KfWorkOrder order = new KfWorkOrder();
        order.setWorkOrderNum(replaceStr);
        order.setCompanyNum(request.getWorkData5());
        order.setChannelNum(request.getWorkData6());
        order.setOrderSourceNum(gdlyFlag? KFConstants.KF_SOURCE_KH:KFConstants.KF_SOURCE_KF);
        order.setUserPhone(request.getWorkData2());
        order.setUserName(request.getWorkData7());
        order.setUserCompanyName(request.getWorkData9());
        order.setUserIdCardNum(request.getWorkData1());
        order.setWorkOrderText(request.getWorkData3());
        order.setWorkOrderStatus(KFConstants.WORK_READY);
        order.setCreateTime(new Date());
        order.setCreateBy(username);
        kfWorkOrderMapper.insert(order);

        KfWorkOrderRecord record = new KfWorkOrderRecord();
        record.setWorkOrderNum(replaceStr);
        record.setWorkOptionType(KFConstants.WORK_OPTION_TJGD);
        record.setStatus(KFConstants.LJSC_STATUS_ZREO);
        record.setCreateTime(new Date());
        record.setCreateBy(username);
        recordMapper.insert(record);

        if (CollectionUtil.isNotEmpty(files)) {
            for (MultipartFile file : files) {
                String ossPath = KFConstants.OSS_FILE_PATH_PRE + replaceStr + "/" + DateUtils.dateTimeNow() + "/" + UUID.randomUUID().toString();
                // 上传文件
                ossClientUtil.uploadFile(ossPath + file.getOriginalFilename(), file);
                KfFileInfo fileInfo = new KfFileInfo();
                fileInfo.setBusinessNum(replaceStr);
                fileInfo.setOssPath(ossPath);
                fileInfo.setFileName(file.getOriginalFilename());
                fileInfo.setCreateTime(new Date());
                fileInfo.setCreateBy(username);
                kfFileInfoMapper.insertSelective(fileInfo);
            }
        }
        return AjaxResult.success();
    }

    @Override
    public Integer workListCount(KfWorkOrderDTO kfWorkOrderDTO) {
        List<Map<String, String>> list = kfAccountInfoMapper.selectAllComAndChaInfoByUserName(SecurityUtils.getUsername());
        return   kfWorkOrderMapper.workListCount(kfWorkOrderDTO,SecurityUtils.getUsername(),list);
    }

    @Override
    public AjaxResult workUpdate(KfWorkOrderDTO kfWorkOrderDTO) {
        KfWorkOrder kfWorkOrder = new KfWorkOrder();
        kfWorkOrder.setId(kfWorkOrderDTO.getId());
        kfWorkOrder.setUserPhone(kfWorkOrderDTO.getUserPhone());
        kfWorkOrder.setUserName(kfWorkOrderDTO.getUserName());
        kfWorkOrder.setUserIdCardNum(kfWorkOrderDTO.getUserIdCardNum());
        kfWorkOrder.setLastUpdateTime(new Date());
        kfWorkOrder.setUpdateBy(SecurityUtils.getUsername());

        int updateCount = kfWorkOrderMapper.updatePhoneAndNameAndCardNo(kfWorkOrder);
        if (updateCount <= 0) {
            return AjaxResult.error("更新数据失败。");
        }
        KfCustomUpdateInfo customUpdateInfo = new KfCustomUpdateInfo();
        customUpdateInfo.setWorkOrderNum(kfWorkOrderDTO.getWorkOrderNum());
        customUpdateInfo.setUserName(kfWorkOrderDTO.getUserName());
        customUpdateInfo.setUserPhone(kfWorkOrderDTO.getUserPhone());
        customUpdateInfo.setUserIdCardNum(kfWorkOrderDTO.getUserIdCardNum());
        customUpdateInfo.setCreateTime(new Date());
        customUpdateInfo.setCreateBy(SecurityUtils.getUsername());
        int i = kfCustomUpdateInfoMapper.insertSelective(customUpdateInfo);
        return i > 0 ? AjaxResult.success() : AjaxResult.error("插入修改数据记录失败");
    }

    @Override
    public List<KfFileInfo> getFileListInfo(KfWorkNumVo kfWorkNumVo) {
       return kfFileInfoMapper.getFileListInfo(kfWorkNumVo.getWorkNum());
    }

    @Override
    public void downloadFileZip(KfWorkFileVo kfWorkFileVo, HttpServletResponse response) {
        List<KfFileInfo> list = kfFileInfoMapper.downloadFileZipFileList(kfWorkFileVo);
        if (CollectionUtil.isEmpty(list)) {
            throw new RuntimeException("当前工单无附件下载。");
        }

        List<Map<String, String>> mapList = list.stream().map(item -> {
            Map<String, String> map
                    = new HashMap<>();
            map.put("filePath", item.getOssPath());
            map.put("fileName", item.getFileName());
            return map;
        }).collect(Collectors.toList());

        try {
            byte[] bytes = ossClientUtil.downloadFilesAsZip(mapList);

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            String encodedFileName = URLEncoder.encode( "客服附件下载_" + UUID.randomUUID().toString().replace("-","") + ".zip", StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment;filename=" +encodedFileName);
            response.getOutputStream().write(bytes);
            response.getOutputStream().flush();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("下载客服工单附件异常，kfWorkFileVo:{}", JSON.toJSONString(kfWorkFileVo));
            throw new RuntimeException("下载文件异常，无法下载，请联系管理员进行查看");
        }
    }


    @Override
    public List<KfWorkOrderVo> workList(KfWorkOrderDTO kfWorkOrderDTO) {
        List<Map<String, String>> list = kfAccountInfoMapper.selectAllComAndChaInfoByUserName(SecurityUtils.getUsername());
        List<KfWorkOrderVo> maps = kfWorkOrderMapper.workList(kfWorkOrderDTO,SecurityUtils.getUsername(),list);
        Set<String> userIdList = maps.stream().map(KfWorkOrderVo::getClaimUserId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        List<Map<String, String>> userMap =new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userIdList)){userMap= sysUserMapper.selectUserNameByUserNames(userIdList);}
        Map<String, String> combinedMap = new HashMap<>();
        for (Map<String, String> entry : userMap) {
            combinedMap.put(String.valueOf(entry.get("userName")), entry.get("nickName"));
        }
        maps = maps.stream().peek(item -> {
            item.setUserPhone(KfRequestValidatedUtils.desensitizePhoneNumber(item.getUserPhone()));
            item.setUserIdCardNum(KfRequestValidatedUtils.desensitizeIDCard(item.getUserIdCardNum()));
            item.setClaimUserName(combinedMap.get(item.getClaimUserId()));
        }).collect(Collectors.toList());

        return maps;
    }

    @Override
    public AjaxResult workStatusUpdate(KfWorkDetailsVo kfWorkDetailsVo) {
        KfWorkOrder kfWorkOrder = kfWorkOrderMapper.getById(Long.valueOf(kfWorkDetailsVo.getId()));
        if (KFConstants.WORK_COMPLETE.equals(kfWorkOrder.getWorkOrderStatus()) || KFConstants.WORK_ABANDON.equals(kfWorkOrder.getWorkOrderStatus())) {
            return AjaxResult.error("当前工单已经完成或者不处理，不可进行操作。");
        }
        if (StringUtils.isEmpty(kfWorkOrder.getCompanyNum())){
            return AjaxResult.error("当前工单公司编码为空异常，不可进行操作。");
        }

        String companyStatus = kfCompanyInfoMapper.selectStatusByCompanyCode(kfWorkOrder.getCompanyNum());
        if (StringUtils.isEmpty(companyStatus)  || (! String.valueOf(KFConstants.LJSC_STATUS_ZREO).equals(companyStatus)) && KFConstants.WORK_OPTION_RLGD == Integer.parseInt(kfWorkDetailsVo.getWorkOptionType())){
            return AjaxResult.error("当前公司已经停用或者状态为空，不可进行认领操作。");
        }

        // 系统登录账号
        String username = SecurityUtils.getUsername();
        boolean flag = false;
        if (StringUtils.isNotEmpty(kfWorkOrder.getClaimUserId()) && !username.equals(kfWorkOrder.getClaimUserId()) &&
                KFConstants.WORK_OPTION_RLGD != Integer.parseInt(kfWorkDetailsVo.getWorkOptionType())) {
            return AjaxResult.error("非本人工单不允许操作工单修改状态。");

        }


        // 工单记录表实体
        KfWorkOrderRecord record = new KfWorkOrderRecord();
        record.setWorkOrderNum(kfWorkOrder.getWorkOrderNum());
        record.setWorkOptionType(Integer.parseInt(kfWorkDetailsVo.getWorkOptionType()));
        record.setStatus(KFConstants.LJSC_STATUS_ZREO);
        record.setCreateTime(new Date());
        record.setCreateBy(username);


        kfWorkOrder.setUpdateBy(username);
        kfWorkOrder.setLastUpdateTime(new Date());
        if (KFConstants.WORK_OPTION_RLGD == Integer.parseInt(kfWorkDetailsVo.getWorkOptionType())) {
            kfWorkOrder.setClaimUserId(username);
            kfWorkOrder.setWorkOrderStatus(KFConstants.WORK_LOAD);
            kfWorkOrderMapper.update(kfWorkOrder);
            flag = true;
        } else if (KFConstants.WORK_OPTION_FQRL == Integer.parseInt(kfWorkDetailsVo.getWorkOptionType())) {
//            kfWorkOrder.setClaimUserId(null);
//            kfWorkOrder.setWorkOrderStatus(KFConstants.WORK_READY);
            kfWorkOrderMapper.updateClaimAndStatusById(kfWorkOrder.getId(),"",KFConstants.WORK_READY);
            flag = true;
        } else if (KFConstants.WORK_OPTION_BCL == Integer.parseInt(kfWorkDetailsVo.getWorkOptionType())) {
            kfWorkOrder.setClaimUserId(username);
            kfWorkOrder.setWorkOrderStatus(KFConstants.WORK_ABANDON);
            kfWorkOrderMapper.update(kfWorkOrder);
            flag = true;

        } else if (KFConstants.WORK_OPTION_WCGD == Integer.parseInt(kfWorkDetailsVo.getWorkOptionType())) {
            kfWorkOrder.setClaimUserId(username);
            kfWorkOrder.setWorkOrderStatus(KFConstants.WORK_COMPLETE);
            kfWorkOrderMapper.update(kfWorkOrder);
            flag = true;

        } else if (!flag) {
            return AjaxResult.error("传输工单记录状态有误，请重新操作");
        }
        // 插入工作记录表
        recordMapper.insert(record);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult workRemarkInsert(KfWorkDetailsVo kfWorkDetailsVo) {
        KfWorkOrder kfWorkOrder = kfWorkOrderMapper.getById(Long.valueOf(kfWorkDetailsVo.getId()));
        if (kfWorkOrder == null || !KFConstants.WORK_LOAD.equals(kfWorkOrder.getWorkOrderStatus())) {
            return AjaxResult.error("工单不存在或者当前工单状态不是处理中，不可进行操作。");
        }
        // 系统登录账号
        String username = SecurityUtils.getUsername();
        boolean flag = false;

        if (KFConstants.WORK_OPTION_FBGZJL == Integer.parseInt(kfWorkDetailsVo.getWorkOptionType())) {
            // 工单记录表实体
            KfWorkOrderRecord record = new KfWorkOrderRecord();
            record.setWorkOrderNum(kfWorkOrder.getWorkOrderNum());
            record.setWorkOptionType(Integer.parseInt(kfWorkDetailsVo.getWorkOptionType()));
            record.setWorkRecord(kfWorkDetailsVo.getInfoText());
            record.setStatus(KFConstants.LJSC_STATUS_ZREO);
            record.setCreateTime(new Date());
            record.setCreateBy(username);
            recordMapper.insert(record);
            flag = true;
        } else if (KFConstants.WORK_OPTION_KHBZ == Integer.parseInt(kfWorkDetailsVo.getWorkOptionType())) {
            KfWordOrderRemark remark = new KfWordOrderRemark();
            remark.setWorkOrderNum(kfWorkOrder.getWorkOrderNum());
            remark.setRemarkTxt(kfWorkDetailsVo.getInfoText());
            remark.setStatus(KFConstants.LJSC_STATUS_ZREO);
            remark.setCreateTime(new Date());
            remark.setCreateBy(username);
            remarkMapper.insert(remark);
            flag = true;
        } else if (!flag) {
            return AjaxResult.error("传输工单记录状态有误，请重新操作");
        }
        return AjaxResult.success();

    }

    @Override
    public AjaxResult workRemarkQuery(KfWorkOrderNumVo kfWorkDetailsVo) {
        Map<String, Object> dataMap = new HashMap<>();
        // 工作记录
        List<KfWorkOrderRecordVo> workList = recordMapper.queryRecordListByNum(kfWorkDetailsVo.getWorkOrderNum()).stream().map(item->{
            if ("default".equals(item.getAccountName()) && StringUtils.isEmpty(item.getUserName())){
                item.setUserName("官方网站");
            }
            return item;
        }).collect(Collectors.toList());
        // 备注信息
        List<KfWorkOrderRecordVo> remarkList = remarkMapper.queryRemarkListByNum(kfWorkDetailsVo.getWorkOrderNum());

        dataMap.put("loginUserName", SecurityUtils.getUsername());
        dataMap.put("workList", workList);
        dataMap.put("remarkList", remarkList);
        return AjaxResult.success(dataMap);
    }

    @Override
    public AjaxResult workRemarkDelete(KfWorkOrderNumVo kfWorkDetailsVo) {
        if (KFConstants.WORK_DELETE_REMARK.equals(kfWorkDetailsVo.getOrderType()) && KFConstants.WORK_DELETE_RECORD.equals(kfWorkDetailsVo.getOrderType())) {
            return AjaxResult.error("删除信息类型错误");
        }
        int deleteCount = 0;

        String workStatus = kfWorkOrderMapper.getOrderStatusByWorkNum(kfWorkDetailsVo.getWorkOrderNum());
        if (StringUtils.isEmpty(workStatus) || !KFConstants.WORK_LOAD.equals(workStatus)){
            return AjaxResult.error("工单状态只有处理中才能进行删除。");
        }

        if (KFConstants.WORK_DELETE_REMARK.equals(kfWorkDetailsVo.getOrderType())) {
            deleteCount = remarkMapper.updateStatus(kfWorkDetailsVo.getId(), kfWorkDetailsVo.getWorkOrderNum(), SecurityUtils.getUsername());
        }

        if (KFConstants.WORK_DELETE_RECORD.equals(kfWorkDetailsVo.getOrderType())) {
            deleteCount = recordMapper.updateStatus(kfWorkDetailsVo.getId(), kfWorkDetailsVo.getWorkOrderNum(), SecurityUtils.getUsername());
        }

        return deleteCount > 0 ? AjaxResult.success() : AjaxResult.error("数据匹配失败");
    }

    @Override
    public AjaxResult customInfoQuery(KfWorkOrderNumVo kfWorkDetailsVo) {
        if (kfWorkDetailsVo == null || StringUtils.isEmpty(String.valueOf(kfWorkDetailsVo.getId())) || StringUtils.isEmpty(kfWorkDetailsVo.getWorkOrderNum()))
            return AjaxResult.error("请求参数格式不全，请重新选择。");
        KfWorkOrder kfWorkOrder = kfWorkOrderMapper.selectByIdAndNum(kfWorkDetailsVo.getId(), kfWorkDetailsVo.getWorkOrderNum());
        if (kfWorkDetailsVo.getIsShow() != null ){
            String cacheObject = redisCache.getCacheObject(CacheConstants.SYS_CONFIG_KEY +KFConstants.GDGL_FLAG);
            if (StringUtils.isNotEmpty(cacheObject) && "true".equals(cacheObject)){
                kfWorkOrder.setUserPhone(KfRequestValidatedUtils.desensitizePhoneNumber(kfWorkOrder.getUserPhone()));
                kfWorkOrder.setUserIdCardNum(KfRequestValidatedUtils.desensitizeIDCard(kfWorkOrder.getUserIdCardNum()));
            }
        }
        return AjaxResult.success(kfWorkOrder);
    }

    /**
     * 处理请求数据
     *
     * @param request 请求参数
     * @return 是否成功校验（true 失败   false 成功）
     */
    private String handleRequestNoSuccess(KfWorkDataRequestDTO request) {
        try {
            String workData4 = request.getWorkData4();
            // 获取AES秘钥
            Map<String,String> aesKey = redisCache.getCacheObject(KFConstants.SM2_HHID + workData4);
            if (null == aesKey || CollectionUtils.isEmpty(aesKey.entrySet())) {
                return "停止响应时间过长，会话已超时，请刷新页面。";
            }

            if (StringUtils.isNotEmpty(request.getWorkData2())) {
                // 手机号
                String phone = Sm2KFUtils.decryptData(request.getWorkData2(), aesKey.get("privateKey"), aesKey.get("publicKey"));
                boolean validPhoneNumber = KfRequestValidatedUtils.isValidPhoneNumber(phone);
                if (!validPhoneNumber || StringUtils.isEmpty(phone)) {
                    return "识别关键信息失败，请刷新后再进行提交。";

                }
                request.setWorkData2(phone);
            }

            if (StringUtils.isNotEmpty(request.getWorkData2())) {
                // 客户姓名
                String khName = Sm2KFUtils.decryptData(request.getWorkData7(), aesKey.get("privateKey"), aesKey.get("publicKey"));
                if (StringUtils.isEmpty(khName)) {
                    return "识别关键信息失败，请刷新后再进行提交。";

                }
                request.setWorkData7(khName);
            }

            if (StringUtils.isNotEmpty(request.getWorkData1())) {
                // 身份证
                String idCardNo = Sm2KFUtils.decryptData(request.getWorkData1(), aesKey.get("privateKey"), aesKey.get("publicKey"));
                boolean validIdCardNo = KfRequestValidatedUtils.isValidIDCard(idCardNo);
                if (!validIdCardNo || StringUtils.isEmpty(idCardNo)) {
                    return "识别关键信息失败，请刷新后再进行提交。";
                }
                request.setWorkData1(idCardNo);
            }

            // 内容
            String workText = Sm2KFUtils.decryptData(request.getWorkData3(), aesKey.get("privateKey"),aesKey.get("publicKey"));
            if (StringUtils.isEmpty(workText) || workText.length() > 1000) {
                return "识别关键内容失败，请刷新后再进行提交。";
            }
            request.setWorkData3(workText);
        } catch (Exception e) {
            e.printStackTrace();
            return "关键信息处理失败，请刷新后进行提交。";
        }

        return null;
    }


    /**
     * 校验请求参数是否合法
     *
     * @param request 请求参数
     * @return 结果
     */
    private boolean handleVaRequestParam(KfWorkDataRequestDTO request) {
        if (StringUtils.isNotEmpty(request.getWorkData2())) {
            boolean validPhoneNumber = KfRequestValidatedUtils.isValidPhoneNumber(request.getWorkData2());
            if (!validPhoneNumber) {
                return true;
            }
        }
        if (StringUtils.isNotEmpty(request.getWorkData1())) {
            boolean validIdCardNo = KfRequestValidatedUtils.isValidIDCard(request.getWorkData1());
            if (!validIdCardNo) {
                return true;
            }
        }
        if (StringUtils.isEmpty(request.getWorkData3()) || request.getWorkData3().length() > 1000) {
            return true;
        }
        return false;
    }
}
