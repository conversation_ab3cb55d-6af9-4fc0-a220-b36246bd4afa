package com.ruoyi.system.service.impl;

import java.text.Collator;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.kf.KfChannelInfo;
import com.ruoyi.system.domain.kf.KfCompanyInfo;
import com.ruoyi.system.mapper.KfChannelInfoMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.KfCompanyInfoMapper;
import com.ruoyi.system.service.KfCompanyInfoService;

/**
 * 客服-公司管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Service
public class KfCompanyInfoServiceImpl implements KfCompanyInfoService {
    @Autowired
    private KfCompanyInfoMapper kfCompanyInfoMapper;
    @Autowired
    private KfChannelInfoMapper kfChannelInfoMapper;

    /**
     * 查询客服-公司管理
     *
     * @param id 客服-公司管理主键
     * @return 客服-公司管理
     */
    @Override
    public KfCompanyInfo selectKfCompanyInfoById(Long id) {
        return kfCompanyInfoMapper.selectKfCompanyInfoById(id);
    }

    /**
     * 查询客服-公司管理列表
     *
     * @param kfCompanyInfo 客服-公司管理
     * @return 客服-公司管理
     */
    @Override
    public List<KfCompanyInfo> selectKfCompanyInfoList(KfCompanyInfo kfCompanyInfo) {
        List<KfCompanyInfo> kfCompanyInfos = kfCompanyInfoMapper.selectKfCompanyInfoList(kfCompanyInfo);
        Collator collator = Collator.getInstance();
        kfCompanyInfos.sort(Comparator.comparing(KfCompanyInfo::getCompanyName, collator));
        return kfCompanyInfos;
    }

    /**
     * 新增客服-公司管理
     *
     * @param kfCompanyInfo 客服-公司管理
     * @return 结果
     */
    @Override
    public AjaxResult insertKfCompanyInfo(KfCompanyInfo kfCompanyInfo) {
        Integer count = kfCompanyInfoMapper.selectCountByCode(kfCompanyInfo.getCompanyCode());
        if (count > 0) return AjaxResult.error("当前公司已经存在！");
        kfCompanyInfo.setCreateTime(DateUtils.getNowDate());
        kfCompanyInfo.setCreateBy(SecurityUtils.getUsername());
        return kfCompanyInfoMapper.insertKfCompanyInfo(kfCompanyInfo) > 0 ? AjaxResult.success() : AjaxResult.error("新增失败");
    }

    /**
     * 修改客服-公司管理
     *
     * @param kfCompanyInfo 客服-公司管理
     * @return 结果
     */
    @Override
    public AjaxResult updateKfCompanyInfo(KfCompanyInfo kfCompanyInfo) {
        KfCompanyInfo info = new KfCompanyInfo();
        info.setId(kfCompanyInfo.getId());
        info.setCompanyCode(kfCompanyInfo.getCompanyCode());
        List<KfCompanyInfo> kfCompanyInfos = kfCompanyInfoMapper.selectKfCompanyInfoListNotId(info);
        if (CollectionUtil.isNotEmpty(kfCompanyInfos)){
            return AjaxResult.error("修改后的公司信息已存在，无须更新。");
        }
        return kfCompanyInfoMapper.updateKfCompanyInfo(kfCompanyInfo)>0 ? AjaxResult.success():AjaxResult.error("更新失败");
    }

//    /**
//     * 批量删除客服-公司管理
//     *
//     * @param ids 需要删除的客服-公司管理主键
//     * @return 结果
//     */
//    @Override
//    public int deleteKfCompanyInfoByIds(Long[] ids) {
//        return kfCompanyInfoMapper.deleteKfCompanyInfoByIds(ids);
//    }

    /**
     * 删除客服-公司管理信息
     *
     * @param id 客服-公司管理主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteKfCompanyInfoById(Long id) {
        KfCompanyInfo kfCompanyInfo = kfCompanyInfoMapper.selectKfCompanyInfoById(id);
        KfChannelInfo kfChannelInfo = new KfChannelInfo();
        kfChannelInfo.setCompanyCode(kfCompanyInfo.getCompanyCode());
        List<KfChannelInfo> channelInfos = kfChannelInfoMapper.selectKfChannelInfoList(kfChannelInfo);
        if (CollectionUtils.isNotEmpty(channelInfos)) return AjaxResult.error("该公司下有已经配置的渠道，请先删除所有渠道再删除公司");
        return kfCompanyInfoMapper.deleteKfCompanyInfoById(id) > 0 ? AjaxResult.success() : AjaxResult.error("删除失败");
    }

    @Override
    public AjaxResult selectCompanyDict() {
        List<Map<String, String>> list = kfCompanyInfoMapper.selectCompanyDict();
        return AjaxResult.success(list);
    }
}
