package com.ruoyi.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.entity.NewInfoTree;
import com.ruoyi.common.core.domain.entity.NewsConfigCenter;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.exception.file.InvalidExtensionException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.system.handle.fatory.NewsFileHandleFactory;
import com.ruoyi.system.mapper.NewsConfigCenterMapper;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.service.NewsConfigCenterService;
import com.ruoyi.system.service.NewsUserConfigService;
import com.ruoyi.system.util.NewsFileHandleUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NewsConfigCenterServiceImpl implements NewsConfigCenterService {
    @Value("${qhct.imagePath}")
    private String qhctImagePath;
    @Value("${yndf.imagePath}")
    private String yndfImagePath;
    @Value("${hnzt.imagePath}")
    private String hnztImagePath;
    @Value("${hnzh.imagePath}")
    private String hnzhImagePath;
    @Value("${hbfc.imagePath}")
    private String hbfcImagePath;
    @Value("${jhrs.imagePath}")
    private String jhrsImagePath;
    @Value("${fjdy.imagePath}")
    private String fjdyImagePath;
    @Resource(type = NewsConfigCenterMapper.class)
    private NewsConfigCenterMapper newsConfigCenterMapper;
    @Autowired
    protected NewsFileHandleUtils fileHandleUtils;
    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    @Autowired
    private NewsUserConfigService userConfigService;
    @Value("${ruoyi.profile}")
    private String profile;
    @Override
    public NewsConfigCenter getById(Integer id) {
        return newsConfigCenterMapper.getById(id);
    }



    @Override
    public List<NewsConfigCenter> listByEntity(NewsConfigCenter newsConfigCenter,List<String > companyList) {
        newsConfigCenter.setPageNum((newsConfigCenter.getPageNum()-1)*newsConfigCenter.getPageSize());
        return newsConfigCenterMapper.listByEntity(newsConfigCenter, companyList);
    }

    @Override
    public int insert(NewsConfigCenter newsConfigCenter) {
        return newsConfigCenterMapper.insert(newsConfigCenter);
    }


    @Override
    public int update(NewsConfigCenter newsConfigCenter) {
        return newsConfigCenterMapper.update(newsConfigCenter);
    }

    @Override
    public int updateBatch(List<NewsConfigCenter> list) {
        return newsConfigCenterMapper.updateBatch(list);
    }

    @Override
    public int deleteById(Integer id) {
        return newsConfigCenterMapper.deleteById(id);
    }


    @Override
    public int deleteByIds(List<Integer> list) {
        return newsConfigCenterMapper.deleteByIds(list);
    }


    @Override
    public List<NewsConfigCenter> listByCompanyCode(String companyCode) {
        return newsConfigCenterMapper.listByCompanyCode(companyCode);
    }

    @Override
    public boolean releaseInfo(List<NewsConfigCenter> list, String companyCode, String devEnv) {
        NewsFileHandleFactory handle = fileHandleUtils.getHandle(companyCode);
        assert handle != null;
        try {
            handle.handleFileCreateAndUp(list, devEnv);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }

    @Override
    public Integer updateStatusByCompanyCode(String companyCode) {
        return newsConfigCenterMapper.updateStatusByCompanyCode(companyCode);
    }

    @Override
    public String upImage(String companyCode, MultipartFile file) throws IOException, InvalidExtensionException {
        String imagePath = "";
        switch (companyCode) {
            case "QHCT":
                imagePath = qhctImagePath;
                break;
            case "YNDF":
                imagePath = yndfImagePath;
                break;
            case "HNZT":
                imagePath = hnztImagePath;
                break;
            case "HNZH":
                imagePath = hnzhImagePath;
                break;
            case "HBFC":
                imagePath = hbfcImagePath;
                break;
            case "JHRS":
                imagePath = jhrsImagePath;
                break;
            case "FJDY":
                imagePath = fjdyImagePath;
                break;
            default:
                imagePath = null;
        }
        if (StrUtil.isEmptyIfStr(imagePath)) return null;
        log.info("companyCode:{}, file:{}", companyCode, file.getSize());
        // 上传文件路径
        log.info("上传路径：{}", profile+imagePath);
        // 上传并返回新文件名称
        String fileName = FileUploadUtils.uploadInter(profile+imagePath, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        log.info("文件名称：{}", fileName);
        return fileName;

    }

    @Override
    public Map<String, List<SysDictData>> menuList(String code, String type, String[] dictTypes) {
        Map<String, List<SysDictData>> map = new HashMap<>();
        if ("ALL".equals(type)) {
            map.put("companyList", sysDictDataMapper.selectDictDataByType(dictTypes[0]));
            map.put("menuFirst", sysDictDataMapper.selectDictDataByType(dictTypes[1]));
            map.put("menuSecond", sysDictDataMapper.selectDictDataByType(dictTypes[2]));
        } else if ("FIRST".equals(type)) {
            map.put("menuFirst", sysDictDataMapper.selectDictDataByTypeAndLikeValue(dictTypes[1], code));
        } else if ("SECOND".equals(type)) {
            map.put("menuSecond", sysDictDataMapper.selectDictDataByTypeAndLikeValue(dictTypes[2], code));
        }
        return map;
    }

    @Override
    public List<NewInfoTree> treeSelect(String str) {
        String[] split = str.split("&");
        Long userId = SecurityUtils.getUserId();
        List<String> companyCodes = userConfigService.getCompanyCodeByUserId(userId);

        List<NewInfoTree> sysDictDataCompany = sysDictDataMapper.selectDictDataByTypeForTree(split[0]).stream().filter(news -> companyCodes.contains(news.getValue())).collect(Collectors.toList());
        List<NewInfoTree> sysDictDataFirst = sysDictDataMapper.selectDictDataByTypeForTree(split[1]);
        List<NewInfoTree> sysDictDataSecond = sysDictDataMapper.selectDictDataByTypeForTree(split[2]);

        for (NewInfoTree firstInfo : sysDictDataFirst) {
            for (NewInfoTree secondInfo : sysDictDataSecond) {
                if (firstInfo.getValue() != null && secondInfo.getValue().contains(firstInfo.getValue())) {
                    secondInfo.setLevel(2);
                    firstInfo.getChildren().add(secondInfo);
                }
            }

        }
        for (NewInfoTree companyInfo : sysDictDataCompany) {
            for (NewInfoTree firstInfo : sysDictDataFirst) {
                if (companyInfo.getValue() != null && firstInfo.getValue().contains(companyInfo.getValue())) {
                    firstInfo.setLevel(1);
                    companyInfo.getChildren().add(firstInfo);
                }
            }
        }
        return sysDictDataCompany;
    }

    @Override
    public Long countByEntityAndCompanycodes(NewsConfigCenter center, List<String> companyList) {
       return newsConfigCenterMapper.countByEntityAndCompanycodes(center,companyList);
    }
}
