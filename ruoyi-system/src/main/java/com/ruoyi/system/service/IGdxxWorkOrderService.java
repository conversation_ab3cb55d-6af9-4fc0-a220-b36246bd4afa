package com.ruoyi.system.service;

import com.ruoyi.system.domain.GdxxWorkOrder;
import com.ruoyi.system.domain.GdxxWorkOrderExport;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

public interface IGdxxWorkOrderService {

    /**
     * 查询工单详情
     */
    GdxxWorkOrderDetailVO getWorkOrderDetail(Long id);

    /**
     * 获取工单历史详情
     * @param id 历史表id
     * @return 历史表Vo
     */
    GdxxWorkOrderHistoryDetailVO getWorkOrderHistoryDetail(Long id);

    /**
     * 查询工单列表
     */
    List<GdxxWorkOrderDetailVO> getWorkOrderDetailList(GdxxWorkOrder query);
    List<GdxxWorkOrderDetailVO> getWorkOrderDetailList(Set<Long> query, boolean pageFlag);

    /**
     * 创建工单
     */
    AjaxResult createWorkOrder(GdxxWorkOrderCreateVO vo,MultipartFile[] files);

    /**
     * 修改工单
     */
    AjaxResult updateWorkOrder(GdxxWorkOrderUpdateVO vo, MultipartFile[] files);

    /**
     * 修改工单状态并添加动态记录
     *
     * @param workOrderId 工单主表ID
     * @param dynamicType 工单动态字典编码
     * @return 操作结果
     */
    AjaxResult updateWorkOrderStatusAndAddDynamic(Long workOrderId, String dynamicType);

    /**
     * 查询工单动态列表
     */
    AjaxResult getWorkOrderDynamicList(Long workOrderId);

    /**
     * 发布工单动态
     */
    AjaxResult publishDynamic(GdxxWorkOrderDynamicAddVO vo,MultipartFile[]files);

    /**
     * 下载文件
     */
    void downloadFile(Long fileId, HttpServletResponse response);

    /**
     * 导出工单列表
     * 
     * @param query 查询条件
     * @param response HTTP响应对象
     */
    void exportWorkOrderList(GdxxWorkOrder query, HttpServletResponse response);

    Set<Long> getWorkOrderIds(GdxxWorkOrder query);

    //查询当前人的草稿
    Set<Long> getWorkOrderIdsInCaoGao(GdxxWorkOrder query);

    List<GdxxWorkOrderExport> getExportInfo(GdxxWorkOrderQueryVo gdxxWorkOrderQueryVo);
}