package com.ruoyi.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.ruoyi.common.constant.ServiceConstants;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.domain.DDataForDictRef;
import com.ruoyi.system.domain.SysDictDataRef;
import com.ruoyi.system.domain.SysRoleCustom;
import com.ruoyi.system.domain.vo.DictValueVO;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysDictDataRefMapper;
import com.ruoyi.system.mapper.SysRoleCustomMapper;
import com.ruoyi.system.util.UserRoles;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName SysDictDataRefServiceImpl
 * @Description 项目启动初始化进行存储数据
 * <AUTHOR> DongRan
 * @Date 2022/6/14 17:45
 * @Version 1.0
 */
@Service
@Slf4j
public class SysDictDataRefServiceImpl {

    private static RedisCache redisCache;

    static {
        redisCache = SpringUtils.getBean(RedisCache.class);
    }

    @Resource
    private SysDictDataRefMapper sysDictDataRefMapper;
    @Resource
    private SysDictDataRefServiceImpl sysDictDataRefServiceImpl;

    @Resource
    private SysDictDataMapper sysDictDataMapper;

    @Resource
    private SysRoleCustomMapper sysRoleCustomMapper;
    @PostConstruct
    private void init() {
        operationSysDictDataRef();
    }

    public void initSysDictDataRef() {
        // 全部数据
        List<SysDictDataRef> sysDictDataRefs = sysDictDataRefMapper.selectDictRefAndDictLabel();
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        Collection<String> keys = redisCache.keys(ServiceConstants.CASCADE_DATA_KEY + "*");
        redisCache.deleteObject(keys);
        // 所有外部系统编码都是父类 级联缓存
        for (SysDictDataRef dictDataRef : sysDictDataRefs) {
            if (!Strings.isNullOrEmpty(dictDataRef.getDictName())) {
                dictDataRef.setDictName(dictDataRef.getDictName());
                redisCache.setCacheObject(ServiceConstants.CASCADE_DATA_KEY + dictDataRef.getPDictType() + ServiceConstants.REDIS_FILE_SEPARATOR + dictDataRef.getPDictValue() + ServiceConstants.REDIS_FILE_SEPARATOR + dictDataRef.getDictType() + ServiceConstants.REDIS_FILE_SEPARATOR + dictDataRef.getDictValue(), dictDataRef);
            }
        }
        log.info("SysDictDataRefServiceImpl init 初始化级联缓存成功");
    }


    /**
     * 同步 d_data 表数据到 sys_dict_data_ref 数据 并更新缓存
     */
    public void operationSysDictDataRef() {
        DDataForDictRef dDataForDictRef = new DDataForDictRef();
        dDataForDictRef.setIsMapping("Y");
        // 查询出 映射成功的数据 并且过滤掉 外部系统编码 担保公司 合作方 资金方 产品 进行过滤
        List<DDataForDictRef> dDataList = sysDictDataRefMapper.queryDictRefDataByDData(dDataForDictRef);

        List<SysDictDataRef> dictDataRefList = new ArrayList<>();
        // 更新或新增字典表数据
        for (DDataForDictRef dataForDictRef : dDataList) {
            if (!Strings.isNullOrEmpty(dataForDictRef.getPlatformNo()) && !Strings.isNullOrEmpty(dataForDictRef.getCustNo()) && !Strings.isNullOrEmpty(dataForDictRef.getPartnerNo()) && !Strings.isNullOrEmpty(dataForDictRef.getFundNo()) && !Strings.isNullOrEmpty(dataForDictRef.getProductNo())) {
                setSysDictData(ServiceConstants.PARENT_TOP_CUT, ServiceConstants.PARENT_TOP_CUT, "platform_no", dataForDictRef.getPlatformNo(), dictDataRefList,dataForDictRef.getPlatformNo());
                setSysDictData("platform_no", dataForDictRef.getPlatformNo(), "cust_no", dataForDictRef.getCustNo(), dictDataRefList,dataForDictRef.getPlatformNo()+"_"+dataForDictRef.getCustNo());
                setSysDictData("cust_no", dataForDictRef.getCustNo(), "partner_no", dataForDictRef.getPartnerNo(), dictDataRefList,dataForDictRef.getPlatformNo()+"_"+dataForDictRef.getCustNo()+"_"+dataForDictRef.getPartnerNo());
                setSysDictData("partner_no", dataForDictRef.getPartnerNo(), "fund_no", dataForDictRef.getFundNo(), dictDataRefList,dataForDictRef.getPlatformNo()+"_"+dataForDictRef.getCustNo()+"_"+dataForDictRef.getPartnerNo()+"_"+dataForDictRef.getFundNo());
                setSysDictData("fund_no", dataForDictRef.getFundNo(), "product_no", dataForDictRef.getProductNo(), dictDataRefList,dataForDictRef.getPlatformNo()+"_"+dataForDictRef.getCustNo()+"_"+dataForDictRef.getPartnerNo()+"_"+dataForDictRef.getFundNo()+"_"+dataForDictRef.getProductNo());
            }
        }
        if (!CollectionUtils.isEmpty(dictDataRefList)) {
            sysDictDataRefMapper.insertOrUpdateBatch(dictDataRefList);
        }
        // 清除缓存 并且进行缓存数据
        sysDictDataRefServiceImpl.initSysDictDataRef();

        log.info("SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功");
    }

    /**
     * sys dict类型数据集
     *
     * @param pDictType  父字典类型
     * @param pDictValue 父字典类型值
     * @param dictType   字典类型
     * @param dictValue  字典值
     */
    private void setSysDictData(String pDictType, String pDictValue, String dictType, String dictValue, List<SysDictDataRef> dictDataRefList,String dictAllCode) {
        SysDictDataRef sysDictDataRef = new SysDictDataRef();
        sysDictDataRef.setPDictType(pDictType);
        sysDictDataRef.setPDictValue(pDictValue);
        sysDictDataRef.setDictType(dictType);
        sysDictDataRef.setDictValue(dictValue);
        sysDictDataRef.setDictAllCode(dictAllCode);
        sysDictDataRef.setStatus("0");
        sysDictDataRef.setCreateBy("System");
        sysDictDataRef.setCreateTime(new Date());
        sysDictDataRef.setUpdateBy("System");
        sysDictDataRef.setUpdateTime(new Date());
        dictDataRefList.add(sysDictDataRef);
    }


    /**
     * 获取级联数据
     *
     * @param dictTypes   dict类型
     * @param dictValues  字典数据
     * @param pDictTypes  p dict类型
     * @param pDictValues p dict类型值
     * @return {@link List}<{@link DictValueVO}>
     */
    public List<DictValueVO> getRefData(List<String> dictTypes, List<String> dictValues, List<String> pDictTypes, List<String> pDictValues) {
        Collection<String> keys = new ArrayList<>();

        for (String dictType : dictTypes) {
            for (String dictValue : dictValues) {
                for (String pDictType : pDictTypes) {
                    for (String pDictValue : pDictValues) {
                        if (ServiceConstants.DICT_VALUE_DEFAULT.equals(dictValue) && ServiceConstants.PARENT_TOP_CUT.equals(pDictType) && ServiceConstants.PARENT_TOP_CUT.equals(pDictValue)) {
                            keys.addAll(redisCache.keys(ServiceConstants.CASCADE_DATA_KEY + pDictType + ServiceConstants.REDIS_FILE_SEPARATOR + pDictValue + "*"));
                        } else if ((ServiceConstants.INIT_PLATFORM_NO_TOP_CUT.equals(pDictType) || ServiceConstants.INIT_CUST_NO_TOP_CUT.equals(pDictType) || ServiceConstants.INIT_FUND_NO_TOP_CUT.equals(pDictType) || ServiceConstants.INIT_PARTNER_NO_TOP_CUT.equals(pDictType)) && ServiceConstants.INIT_CUT.equals(pDictValue)) {
                            keys.addAll(redisCache.keys(ServiceConstants.CASCADE_DATA_KEY + pDictType + "*"));
                        } else {
                            keys.addAll(redisCache.keys(ServiceConstants.CASCADE_DATA_KEY + pDictType + ServiceConstants.REDIS_FILE_SEPARATOR + pDictValue + ServiceConstants.REDIS_FILE_SEPARATOR + dictType + "*"));
                        }
                    }
                }
            }
        }

        List<SysDictDataRef> list = redisCache.redisTemplate.opsForValue().multiGet(keys);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
//        log.debug("getRefData list:{}", JSON.toJSONString(list));
        return list.stream().map(this::coverSysDictData).filter(item -> item.getLabel() != null).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(item -> item.getLabel() + ";" + item.getValue()))), ArrayList::new));
    }


    /**
     * 转换sys dict类型数据
     *
     * @param sysDictDataRef sys dict数据参考
     * @return {@link DictValueVO}
     */
    private DictValueVO coverSysDictData(SysDictDataRef sysDictDataRef) {
        DictValueVO dictValueVO = new DictValueVO();
        dictValueVO.setDictType(sysDictDataRef.getDictType());
        dictValueVO.setValue(sysDictDataRef.getDictValue());
        dictValueVO.setLabel(sysDictDataRef.getDictName());
        dictValueVO.setPDictType(sysDictDataRef.getPDictType());
        dictValueVO.setPDictValue(sysDictDataRef.getPDictValue());
        return dictValueVO;
    }

    /**
     * 根据角色权限筛选下拉框数据
     *
     * @param dictTypes   dict类型
     * @param dictValues  dict类型值
     * @param pDictTypes  p dict类型
     * @param pDictValues p dict类型值
     * @return {@link Map}<{@link String}, {@link List}<{@link DictValueVO}>>
     */
    public Map<String,List<DictValueVO>> getUserRole(List<String> dictTypes, List<String> dictValues, List<String> pDictTypes, List<String> pDictValues, LoginUser loginUser,String selectDictDatas){
        Map<String,List<DictValueVO>> userRoleMap = new HashMap<>();

        if (!UserRoles.isAdmin(loginUser) && dictTypes.get(0).equals("")  && dictValues.get(0).equals("") && pDictTypes.get(0).equals("") && pDictValues.get(0).equals("")){

            Map<String, String> customRole = UserRoles.getCustomRole(loginUser);
            List<String> platformNos = Arrays.asList(customRole.get("platformNo").split(","));
            List<String> custNos = Arrays.asList(customRole.get("custNo").split(","));
            List<String> partnerNos = Arrays.asList(customRole.get("partnerNo").split(","));
            List<String> fundNos = Arrays.asList(customRole.get("fundNo").split(","));

            List<String> allCode = UserRoles.getAllCode(loginUser);
            userRoleMap.put("platform_no",this.getItselfData("platform_no", platformNos));

            userRoleMap.put("cust_no",this.getItselfRoleData(platformNos,custNos,"cust_no",allCode));
            userRoleMap.put("partner_no",this.getItselfRoleData(custNos,partnerNos,"partner_no",allCode));
            List<DictValueVO> partner_no = this.getItselfRoleData(custNos, partnerNos, "partner_no",allCode);
            ArrayList<String> objects = new ArrayList<>();
            for (DictValueVO dictValueVO : partner_no) {
                objects.add(dictValueVO.getValue());
            }
            userRoleMap.put("fund_no", this.getItselfRoleData(objects,fundNos,"fund_no",allCode));





            List<String> nullDictTypes = new ArrayList<>();
            nullDictTypes.add("product_no");

            List<String> nullDictValues = new ArrayList<>();
            nullDictValues.add("INIT_DEFAULT");

            List<String> pNullDictTypes = new ArrayList<>();
            pNullDictTypes.add("fund_no");

            List<DictValueVO> refData = this.getRefData(nullDictTypes, nullDictValues, pNullDictTypes, fundNos);
            userRoleMap.put("product_no",refData);
        }
//        else if(!UserRoles.isAdmin(loginUser)){
//            Map<String, String> customRole = UserRoles.getCustomRole(loginUser);
//            Map<String, List<DictValueVO>> selectData = this.getSelectData(dictTypes, dictValues, pDictTypes, pDictValues);
//
//            List<String> platformNos = Arrays.asList(customRole.get("platformNo").split(",")).stream().distinct().collect(Collectors.toList());
//            List<String> custNos = Arrays.asList(customRole.get("custNo").split(",")).stream().distinct().collect(Collectors.toList());
//            List<String> partnerNos = Arrays.asList(customRole.get("partnerNo").split(",")).stream().distinct().collect(Collectors.toList());
//            List<String> fundNos = Arrays.asList(customRole.get("fundNo").split(",")).stream().distinct().collect(Collectors.toList());
//
//            userRoleMap.put("platform_no",this.userRoleFilter(platformNos,selectData.get("platform_no")));
//            userRoleMap.put("cust_no",this.userRoleFilter(custNos,selectData.get("cust_no")));
//            userRoleMap.put("partner_no",this.userRoleFilter(partnerNos,selectData.get("partner_no")));
//            List<DictValueVO> fundDictValueVOS = this.userRoleFilter(fundNos, selectData.get("fund_no"));
//            userRoleMap.put("fund_no",fundDictValueVOS);
//
//
//            List<String> funds = new ArrayList<>();
//            for (DictValueVO fundDictValueVO : fundDictValueVOS) {
//                funds.add(fundDictValueVO.getValue());
//            }
//            List<String> nullDictTypes = new ArrayList<>();
//            nullDictTypes.add("product_no");
//
//            List<String> nullDictValues = new ArrayList<>();
//            nullDictValues.add("INIT_DEFAULT");
//
//            List<String> pNullDictTypes = new ArrayList<>();
//            pNullDictTypes.add("fund_no");
//
//            List<DictValueVO> refData = this.getRefData(nullDictTypes, nullDictValues, pNullDictTypes, funds);
//            userRoleMap.put("product_no",refData);
////进行二次过滤
//            String rolePlatformNo = "";
//            String roleCustNo = "";
//            String rolePartnerNo = "";
//            String roleFundNo = "";
//            //选择了产品他的父级只有一个所以直接取第一个就行
//            if(pDictTypes.get(0).equals("product_no")&& pDictValues.get(0)!="" ){
//                List<String> values = new ArrayList<>();
//                for (DictValueVO fundDictValueVO : fundDictValueVOS) {
//                    values.add(fundDictValueVO.getValue());
//                }
//
//                List<SysRoleCustom> userRoleByUserIdAndparams = sysRoleCustomMapper.getUserRoleByUserIdAndparams(loginUser.getUserId(), "fund_no", values);
//                for (SysRoleCustom userRoleByUserIdAndparam : userRoleByUserIdAndparams) {
//                    rolePlatformNo = rolePlatformNo+userRoleByUserIdAndparam.getPlatformNo()+",";
//                    roleCustNo = roleCustNo+userRoleByUserIdAndparam.getCustNo()+",";
//                    rolePartnerNo = rolePartnerNo+userRoleByUserIdAndparam.getPartnerNo()+",";
//                    roleFundNo = roleFundNo+userRoleByUserIdAndparam.getFundNo()+",";
//                }
//                userRoleMap.put("platform_no",this.getItselfData("platform_no",Arrays.asList(rolePlatformNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("cust_no",this.getItselfData("cust_no", Arrays.asList(roleCustNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("partner_no",this.getItselfData("partner_no", Arrays.asList(rolePartnerNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("fund_no",this.getItselfData("fund_no", Arrays.asList(roleFundNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//
//            }else if(pDictTypes.get(0).equals("fund_no") && pDictValues.get(0)!=""){
//                List<SysRoleCustom> userRoleByUserIdAndparams = sysRoleCustomMapper.getUserRoleByUserIdAndparams(loginUser.getUserId(), "fund_no", pDictValues);
//                for (SysRoleCustom userRoleByUserIdAndparam : userRoleByUserIdAndparams) {
//                    rolePlatformNo = rolePlatformNo+userRoleByUserIdAndparam.getPlatformNo()+",";
//                    roleCustNo = roleCustNo+userRoleByUserIdAndparam.getCustNo()+",";
//                    rolePartnerNo = rolePartnerNo+userRoleByUserIdAndparam.getPartnerNo()+",";
//                    roleFundNo = roleFundNo+userRoleByUserIdAndparam.getFundNo()+",";
//                }
//                userRoleMap.put("platform_no",this.getItselfData("platform_no",Arrays.asList(rolePlatformNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("cust_no",this.getItselfData("cust_no", Arrays.asList(roleCustNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("partner_no",this.getItselfData("partner_no", Arrays.asList(rolePartnerNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("fund_no",this.getItselfData("fund_no", Arrays.asList(roleFundNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//            }else if(pDictTypes.get(0).equals("partner_no") && pDictValues.get(0)!=""){
//                List<SysRoleCustom> userRoleByUserIdAndparams = sysRoleCustomMapper.getUserRoleByUserIdAndparams(loginUser.getUserId(), "partner_no", pDictValues);
//                for (SysRoleCustom userRoleByUserIdAndparam : userRoleByUserIdAndparams) {
//                    rolePlatformNo = rolePlatformNo+userRoleByUserIdAndparam.getPlatformNo()+",";
//                    roleCustNo = roleCustNo+userRoleByUserIdAndparam.getCustNo()+",";
//                    rolePartnerNo = rolePartnerNo+userRoleByUserIdAndparam.getPartnerNo()+",";
//                    roleFundNo = roleFundNo+userRoleByUserIdAndparam.getFundNo()+",";
//                }
//                userRoleMap.put("platform_no",this.getItselfData("platform_no",Arrays.asList(rolePlatformNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("cust_no",this.getItselfData("cust_no", Arrays.asList(roleCustNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("partner_no",this.getItselfData("partner_no", Arrays.asList(rolePartnerNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("fund_no",this.getItselfData("fund_no", Arrays.asList(roleFundNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//            }else if(pDictTypes.get(0).equals("cust_no") && pDictValues.get(0)!=""){
//                List<SysRoleCustom> userRoleByUserIdAndparams = sysRoleCustomMapper.getUserRoleByUserIdAndparams(loginUser.getUserId(), "cust_no", pDictValues);
//                for (SysRoleCustom userRoleByUserIdAndparam : userRoleByUserIdAndparams) {
//                    rolePlatformNo = rolePlatformNo+userRoleByUserIdAndparam.getPlatformNo()+",";
//                    roleCustNo = roleCustNo+userRoleByUserIdAndparam.getCustNo()+",";
//                    rolePartnerNo = rolePartnerNo+userRoleByUserIdAndparam.getPartnerNo()+",";
//                    roleFundNo = roleFundNo+userRoleByUserIdAndparam.getFundNo()+",";
//                }
//                userRoleMap.put("platform_no",this.getItselfData("platform_no",Arrays.asList(rolePlatformNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("cust_no",this.getItselfData("cust_no", Arrays.asList(roleCustNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("partner_no",this.getItselfData("partner_no", Arrays.asList(rolePartnerNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//                userRoleMap.put("fund_no",this.getItselfData("fund_no", Arrays.asList(roleFundNo.split(",")).stream().distinct().collect(Collectors.toList()) ));
//            }
//
//            String selectUserRoleSql = UserRoles.getSelectUserRoleSql(loginUser);
//            if(!selectDictDatas.equals("")){
//                List<SysDictDataRef> byDictAllCode = sysDictDataRefMapper.getByDictAllCode(selectDictDatas,selectUserRoleSql);
//                if(byDictAllCode.size()!=0){
//                    List<DictValueVO> dictValueVOS = this.dictDataRefCastDictValueVO(byDictAllCode);
//                    userRoleMap.put("product_no",dictValueVOS);
//                }
//            }
//            userRoleMap.put("platform_no",this.userRoleFilter(platformNos,selectData.get("platform_no")));
//            userRoleMap.put("cust_no",this.userRoleFilter(custNos,selectData.get("cust_no")));
//            userRoleMap.put("partner_no",this.userRoleFilter(partnerNos,selectData.get("partner_no")));
//            List<DictValueVO> fundDictValueVOS1 = this.userRoleFilter(fundNos, selectData.get("fund_no"));
//            userRoleMap.put("fund_no",fundDictValueVOS1);
//
//        }
        else  {
            userRoleMap = this.selectData(dictTypes, dictValues, pDictTypes, pDictValues,selectDictDatas,loginUser);
        }

        return userRoleMap;
    }

    private List<DictValueVO> getItselfRoleData(List<String> platformNos, List<String> custNos, String custNo,List<String> allCode) {
        List<DictValueVO> dictValueVOS = new ArrayList<>();
        List<DictValueVO> dictValueVOS2 = new ArrayList<>();
        for (int i = 0;i<custNos.size();i++){
            if(custNos.get(i).equals("all")){
                for (String platformNo : platformNos) {
                    String s = null;
                    if(!platformNo.equals("all")){
                        s = platformNo;
                    }
                    List<SysDictDataRef> dictDataRefList = sysDictDataRefMapper.querrByallCodeAndType(s, custNo);
                    List<DictValueVO> dictValueVOS1 = dictDataRefCastDictValueVO(dictDataRefList);
                    dictValueVOS.addAll(dictValueVOS1);
                }
                for (String s1 : allCode) {
                    for (DictValueVO roleFilter : dictValueVOS) {
                        if(roleFilter.getAllCode().contains(s1)){
                            dictValueVOS2.add(roleFilter);
                        }
                    }

                }

            }else {
                List<SysDictDataRef> dictDataRefList = sysDictDataRefMapper.querrBydictValueAndType(custNos.get(i), custNo);
                List<DictValueVO> dictValueVOS1 = dictDataRefCastDictValueVO(dictDataRefList);
                dictValueVOS2.addAll(dictValueVOS1);
            }
        }



        for (DictValueVO dictValueVO : dictValueVOS2) {
            dictValueVO.setAllCode("a");
        }

        List<DictValueVO> collect = dictValueVOS2.stream().distinct().collect(Collectors.toList());
        return collect;
    }

    /**
     * 根据用户角色权限进行过滤
     *
     * @param roles        角色
     * @param dictValueVOS dict vos价值
     * @return {@link List}<{@link DictValueVO}>
     */
    public  List<DictValueVO> userSysRoleFilter(List<String> roles,List<DictValueVO> dictValueVOS){
        List<DictValueVO> roleFilters = new ArrayList<>();

        for (String role : roles) {
            if(role.equals("all")){
               roleFilters.addAll(dictValueVOS);
            }else {
                for (DictValueVO dictValueVO : dictValueVOS) {
                    if(role.equals(dictValueVO.getValue())){
                        roleFilters.add(dictValueVO);
//
                    }
                }
            }
        }

        List<DictValueVO> collect = dictValueVOS.stream().distinct().collect(Collectors.toList());

        return collect;
    }

    public  List<DictValueVO> userNoSystemRoleFilter(List<String> system,List<String> roles,List<DictValueVO> dictValueVOS,List<String> allCode){
        List<DictValueVO> roleFilters = new ArrayList<>();
        List<DictValueVO> returnList = new ArrayList<>();
        for (int i =0;i<system.size();i++){

            if( roles.get(i).equals("all")){
                for (DictValueVO dictValueVO : dictValueVOS) {
//                    if(dictValueVO.getAllCode().contains(system.get(0))){
//                        roleFilters.add(dictValueVO);
//                    }
                    roleFilters.add(dictValueVO);
                }
                for (String s : allCode) {
                    for (DictValueVO roleFilter : roleFilters) {
                        if(roleFilter.getAllCode().contains(s)){
                            returnList.add(roleFilter);
                        }
                    }

                }
            }else  {
                for (DictValueVO dictValueVO : dictValueVOS) {
                    if(roles.get(i).equals(dictValueVO.getValue())){
                        returnList.add(dictValueVO);
                    }
                }

            }

        }

        for (DictValueVO roleFilter : returnList) {
            roleFilter.setAllCode("1");
        }


        List<DictValueVO> collect = returnList.stream().distinct().collect(Collectors.toList());

        return collect;
    }
    /**
     * 下拉框数据取 任意选择子节点获取其父节点以及其所有子节点
     *
     * @param dictTypes   dict类型
     * @param dictValues  dict类型值
     * @param pDictTypes  p dict类型
     * @param pDictValues p dict类型值
     * @return {@link Map}<{@link String}, {@link List}<{@link DictValueVO}>>
     */
    public Map<String,List<DictValueVO>> getSelectData(List<String> dictTypes, List<String> dictValues, List<String> pDictTypes, List<String> pDictValues){


        //获取子节点元素
        HashMap<String,List<DictValueVO>> selectData = new HashMap<>();
        List<String> nullDictTypes = new ArrayList<>();
        nullDictTypes.add("platform_no");

        List<String> nullDictValues = new ArrayList<>();
        nullDictValues.add("default");

        List<String> pNullDictTypes = new ArrayList<>();
        pNullDictTypes.add("TOP");

        List<String> pNullDictValues = new ArrayList<>();
        pNullDictValues.add("TOP");
        List<DictValueVO> refData = new ArrayList<>();
        List<DictValueVO> parentData = new ArrayList<>();
        //随便放进一个数组

        //如果传过来的value为null则证明没有选择就默认查询全部，如果传过来则查询上下级
        if(pDictValues.get(0).equals("") || StringUtils.isEmpty(pDictValues.get(0))){
            refData =  this.getRefData(nullDictTypes, nullDictValues, pNullDictTypes, pNullDictValues);
        }else if(pDictTypes.get(0).equals("product_no")){
            parentData = this.getParentData(pDictTypes.get(0), pDictValues);

            List<DictValueVO> itselfData = this.getItselfData(pDictTypes.get(0), pDictValues);
            selectData.put(pDictTypes.get(0),itselfData);
        } else {

            List<DictValueVO> itselfData = this.getItselfData(pDictTypes.get(0), pDictValues);
            selectData.put(pDictTypes.get(0),itselfData);

            refData =  this.getRefData(dictTypes, dictValues, pDictTypes, pDictValues);
            //获取父级节点元素
             parentData = this.getParentData(pDictTypes.get(0), pDictValues);
        }
        if(parentData.size()>0){
            selectData.put(parentData.get(0).getDictType(),parentData);
        }
        if(refData.size()>0){
            selectData.put(refData.get(0).getDictType(),refData);
        }

        boolean parentB = true;
        boolean b = true;

        String parentDictType = "";
        List<String>  parentDictValues = new ArrayList<>();

        if ( refData.size() ==0 ||refData.get(0).getDictType().equals("product_no")){
            b=false;
        }
        if(parentData.size()== 0 || parentData.get(0).getDictType().equals("platform_no")){
            parentB = false;
        }
        if(parentData.size()>0){
            parentDictType = parentData.get(0).getDictType();
            for (DictValueVO parentDatum : parentData) {
                parentDictValues.add(parentDatum.getValue());
            }
        }
        String dictType = "";
        String dictValue = "INIT_DEFAULT";
        String pDictType = "";
        List<String>  pDictValues1 = new ArrayList<>();

        if(refData.size()>0){
            pDictType = refData.get(0).getDictType();
            for (DictValueVO refDatum : refData) {
                pDictValues1.add(refDatum.getValue());
            }
        }

        //循环获取父级元素
        while (parentB){
            List<DictValueVO> whileParentData = this.getParentData(parentDictType, parentDictValues);
            if(whileParentData.get(0).getDictType().equals("platform_no")){
                parentB = false;
            }
            parentDictType = whileParentData.get(0).getDictType();
            parentDictValues.clear();
            for (DictValueVO whileParentDatum : whileParentData) {
                parentDictValues.add(whileParentDatum.getValue());
            }
            selectData.put(whileParentData.get(0).getDictType(),whileParentData);
        }

        while (b){

            if(pDictType.equals("platform_no")){
                dictType = "cust_no";
            }else if(pDictType.equals("cust_no")){
                dictType = "partner_no";
            }else if(pDictType.equals("partner_no")){
                dictType = "fund_no";
            }else if(pDictType.equals("fund_no")){
                dictType = "product_no";
            }
            List<DictValueVO> refDataOne = this.getRefDataOne(dictType, dictValue, pDictType, pDictValues1);
            if(refDataOne.get(0).getDictType().equals("product_no")){
                b=false;
            }
            pDictType = refDataOne.get(0).getDictType();
            pDictValues1.clear();
            for (DictValueVO dictValueVO : refDataOne) {
                pDictValues1.add(dictValueVO.getValue());
            }
            selectData.put(refDataOne.get(0).getDictType(),refDataOne);
        }
        return selectData;

    }

    /**
     *
     *  子级级联数据
     * @param dictType    dict类型
     * @param dictValue   东西价值
     * @param pDictType   p dict类型
     * @param pDictValues p dict类型值
     * @return {@link List}<{@link DictValueVO}>
     */
    public List<DictValueVO> getRefDataOne(String dictType, String dictValue, String pDictType, List<String> pDictValues) {
        Collection<String> keys = new ArrayList<>();
        for (String pDictValue : pDictValues) {
            if (ServiceConstants.DICT_VALUE_DEFAULT.equals(dictValue) && ServiceConstants.PARENT_TOP_CUT.equals(pDictType) && ServiceConstants.PARENT_TOP_CUT.equals(pDictValue)) {
                keys.addAll(redisCache.keys(ServiceConstants.CASCADE_DATA_KEY + pDictType + ServiceConstants.REDIS_FILE_SEPARATOR + pDictValue + "*"));
            } else if ((ServiceConstants.INIT_PLATFORM_NO_TOP_CUT.equals(pDictType) || ServiceConstants.INIT_CUST_NO_TOP_CUT.equals(pDictType) || ServiceConstants.INIT_FUND_NO_TOP_CUT.equals(pDictType) || ServiceConstants.INIT_PARTNER_NO_TOP_CUT.equals(pDictType)) && ServiceConstants.INIT_CUT.equals(pDictValue)) {
                keys.addAll(redisCache.keys(ServiceConstants.CASCADE_DATA_KEY + pDictType + "*"));
            } else {
                keys.addAll(redisCache.keys(ServiceConstants.CASCADE_DATA_KEY + pDictType + ServiceConstants.REDIS_FILE_SEPARATOR + pDictValue + ServiceConstants.REDIS_FILE_SEPARATOR + dictType + "*"));
            }
        }
        List<SysDictDataRef> list = redisCache.redisTemplate.opsForValue().multiGet(keys);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
//        log.debug("getRefData list:{}", JSON.toJSONString(list));
        return list.stream().map(this::coverSysDictData).filter(item -> item.getLabel() != null).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(item -> item.getLabel() + ";" + item.getValue()))), ArrayList::new));
    }


    /**
     * 获得父级数据
     *
     * @param dictType   dict类型
     * @param dictValues dict类型值
     * @return {@link List}<{@link DictValueVO}>
     */
    public List<DictValueVO> getParentData(String dictType,List<String> dictValues){
        List<DictValueVO>  dictValueVOS = new ArrayList<>();
        List<SysDictDataRef> parentDatas = sysDictDataRefMapper.getParentData(dictType, dictValues);
        for (SysDictDataRef parentDatum : parentDatas) {
            DictValueVO dictValueVO = new DictValueVO();
            dictValueVO.setLabel(parentDatum.getDictName());
            dictValueVO.setValue(parentDatum.getPDictValue());
            dictValueVO.setDictType(parentDatum.getPDictType());
            dictValueVOS.add(dictValueVO);
        }
        return dictValueVOS;
    }

    /**
     * 根据类型和dictvalue集合获取数据
     *
     * @param dictType    dict类型
     * @param pDictValues p dict类型值
     * @return {@link List}<{@link DictValueVO}>
     */
    public List<DictValueVO> getItselfData(String dictType,List<String> pDictValues){
        //传担保公司  如果为all 查询以担保公司为全编码 + 类型查询  不为all 则直接查询

        List<DictValueVO>  dictValueVOS = new ArrayList<>();
        List<SysDictData> dictLable = new ArrayList<>();
        if(pDictValues.get(0).equals("all")){
            ArrayList<String> objects = new ArrayList<>();
            dictLable = sysDictDataMapper.getDictLable(dictType, objects);
        }else {
            dictLable = sysDictDataMapper.getDictLable(dictType, pDictValues);
        }

        for (SysDictData sysDictData : dictLable) {
            DictValueVO dictValueVO = new DictValueVO();
            dictValueVO.setLabel(sysDictData.getDictLabel());
            dictValueVO.setValue(sysDictData.getDictValue());
            dictValueVO.setDictType(sysDictData.getDictType());
            dictValueVOS.add(dictValueVO);
        }
        return dictValueVOS;
    }

    /**
     * dict数据转换实体
     *
     * @param parentDatas 父数据
     * @return {@link List}<{@link DictValueVO}>
     */
    public List<DictValueVO> dictDataRefCastDictValueVO(List<SysDictDataRef> parentDatas){
        List<DictValueVO>  dictValueVOS = new ArrayList<>();
        for (SysDictDataRef parentData : parentDatas) {
            DictValueVO dictValueVO = new DictValueVO();
            dictValueVO.setLabel(parentData.getDictName());
            dictValueVO.setValue(parentData.getDictValue());
            dictValueVO.setDictType(parentData.getPDictType());
            dictValueVO.setAllCode(parentData.getDictAllCode());
            dictValueVOS.add(dictValueVO);
        }
        List<DictValueVO> methodquchong = methodquchong(dictValueVOS);
        List<DictValueVO> collect = methodquchong.stream().distinct().collect(Collectors.toList());
        return collect;
    }

    //根据传过来的查询全编码
    public Map <String,List<DictValueVO>>  selectData(List<String> dictTypes, List<String> dictValues, List<String> pDictTypes, List<String> pDictValues,String selectDictDatas,LoginUser loginUser){

        Map<String,List<DictValueVO>> userRoleMap = new HashMap<>();
        List<String> selectDictDataList = Arrays.asList(selectDictDatas.split(","));
        List<String> collect = pDictValues.stream().filter(item -> !StringUtils.isEmpty(item)).collect(Collectors.toList());


        List<String> DictDatacollect = selectDictDataList.stream().filter(item -> !StringUtils.isEmpty(item)).collect(Collectors.toList());
        List<String> dictAllList = new ArrayList<>();
        dictAllList.addAll(DictDatacollect);

        List<SysDictDataRef> sysDictDataRefs = sysDictDataRefMapper.queryAllCode(dictAllList);

        if(sysDictDataRefs.size()==0){
           List<SysDictDataRef> objects = new ArrayList<>();
            //把全编码根据下划线拆分后,根据dictvalue查询数据，再判断全编码中同时含有
            SysDictDataRef sysDictDataRef = new SysDictDataRef();
            sysDictDataRef.setDictAllCode(selectDictDatas);
            objects.add(sysDictDataRef);
            List<String> listString = this.getListString(objects);
            List<String> collect1 = listString.stream().filter(item -> !StringUtils.isEmpty(item)).collect(Collectors.toList());
         sysDictDataRefs = sysDictDataRefMapper.queryByAllCode(collect1);


//
//            dictAllList.addAll(collect);
//            sysDictDataRefs = sysDictDataRefMapper.queryAllCode(dictAllList);
        }


        List<SysDictDataRef> plarFormNos = new ArrayList<>();
        List<SysDictDataRef> custNos = new ArrayList<>();
        List<SysDictDataRef> partnerNos = new ArrayList<>();
        List<SysDictDataRef> fundNos = new ArrayList<>();
        List<SysDictDataRef> productNos = new ArrayList<>();


        List<SysDictDataRef> extraDatas = new ArrayList<>();
        //防止叉掉第一个选项后系统以及担保公司等下拉框没有值
        if(collect.size()==0 && DictDatacollect.size()>0){
            SysDictDataRef sysDictDataRefColl = new SysDictDataRef();
            for (String s : DictDatacollect) {
                sysDictDataRefColl.setDictAllCode(s);
                extraDatas.add(sysDictDataRefColl);
            }

        }
        if(sysDictDataRefs.size()>0){
            extraDatas.add(sysDictDataRefs.get(0));
        }

            for (SysDictDataRef sysDictDataRef : sysDictDataRefs) {
                if(sysDictDataRef.getDictType().equals("platform_no")){
                    plarFormNos.add(sysDictDataRef);
                }
                if(sysDictDataRef.getDictType().equals("cust_no")){
                    custNos.add(sysDictDataRef);
                }
                if(sysDictDataRef.getDictType().equals("partner_no")){
                    partnerNos.add(sysDictDataRef);
                }
                if(sysDictDataRef.getDictType().equals("fund_no")){
                    fundNos.add(sysDictDataRef);
                }
                if(sysDictDataRef.getDictType().equals("product_no")){
                    productNos.add(sysDictDataRef);
                }
                extraDatas.add(sysDictDataRef);
//                if(sysDictDataRef.getDictType().equals(dictTypes.get(0))){
//                    extraDatas.add(sysDictDataRef);
//                }
//                if(sysDictDataRef.getDictType().equals(pDictTypes.get(0))){
//                    extraDatas.add(sysDictDataRef);
//                }
            }
            //把额外数据的全编码拿去拆分
            if( extraDatas.size()!=0) {
                List<String> listString = this.getListString(extraDatas);
                List<String> listStringSteam = listString.stream().filter(item -> !StringUtils.isEmpty(item)).collect(Collectors.toList());
                List<SysDictDataRef> dictDataRefList = sysDictDataRefMapper.queryBydictValue(listStringSteam);
                if (dictDataRefList.size() > 0) {
                    for (SysDictDataRef sysDictDataRef : dictDataRefList) {
                        if (sysDictDataRef.getDictType().equals("platform_no")) {
                            plarFormNos.add(sysDictDataRef);
                        }
                        if (sysDictDataRef.getDictType().equals("cust_no")) {
                            custNos.add(sysDictDataRef);
                        }
                        if (sysDictDataRef.getDictType().equals("partner_no")) {
                            partnerNos.add(sysDictDataRef);
                        }
                        if (sysDictDataRef.getDictType().equals("fund_no")) {
                            fundNos.add(sysDictDataRef);
                        }
                        if (sysDictDataRef.getDictType().equals("product_no")) {
                            productNos.add(sysDictDataRef);
                        }
                    }
                }
            }
        List<DictValueVO> dictValueVOS = dictDataRefCastDictValueVO(plarFormNos);

        userRoleMap.put("platform_no",dictDataRefCastDictValueVO(plarFormNos));
        userRoleMap.put("cust_no",dictDataRefCastDictValueVO(custNos));
        userRoleMap.put("partner_no",dictDataRefCastDictValueVO(partnerNos));
        userRoleMap.put("fund_no",dictDataRefCastDictValueVO(fundNos));
        userRoleMap.put("product_no",dictDataRefCastDictValueVO(productNos));

        if(!UserRoles.isAdmin(loginUser)){
            Map<String, String> customRole = UserRoles.getCustomRole(loginUser);
//            Map<String, List<DictValueVO>> selectData = this.getSelectData(dictTypes, dictValues, pDictTypes, pDictValues);

            List<String> rolePlatformNos = Arrays.asList(customRole.get("platformNo").split(","));
            List<String> roleCustNos = Arrays.asList(customRole.get("custNo").split(","));
            List<String> rolePartnerNos = Arrays.asList(customRole.get("partnerNo").split(","));
            List<String> roleFundNos = Arrays.asList(customRole.get("fundNo").split(","));

//            userRoleMap.put("platform_no",this.getItselfData("platform_no", rolePlatformNos));
//            userRoleMap.put("cust_no",this.getItselfRoleData(rolePlatformNos,roleCustNos,"cust_no"));
//            userRoleMap.put("partner_no",this.getItselfRoleData(rolePlatformNos,rolePartnerNos,"partner_no"));
//            userRoleMap.put("fund_no", this.getItselfRoleData(rolePlatformNos,roleFundNos,"fund_no"));

            List<String> allCode = UserRoles.getAllCode(loginUser);
            userRoleMap.put("platform_no",this.userSysRoleFilter(rolePlatformNos,dictDataRefCastDictValueVO(plarFormNos)));
            userRoleMap.put("cust_no",this.userNoSystemRoleFilter(rolePlatformNos,roleCustNos,dictDataRefCastDictValueVO(custNos),allCode));
            userRoleMap.put("partner_no",this.userNoSystemRoleFilter(roleCustNos,rolePartnerNos,dictDataRefCastDictValueVO(partnerNos),allCode));
            userRoleMap.put("fund_no",this.userNoSystemRoleFilter(rolePartnerNos,roleFundNos,dictDataRefCastDictValueVO(fundNos),allCode ));
        }else {
            userRoleMap.put("platform_no", this.duplicate(userRoleMap.get("platform_no")));
            userRoleMap.put("cust_no", this.duplicate(userRoleMap.get("cust_no")));
            userRoleMap.put("partner_no", this.duplicate(userRoleMap.get("partner_no")));
            userRoleMap.put("fund_no", this.duplicate(userRoleMap.get("fund_no")));
            userRoleMap.put("product_no", this.duplicate(userRoleMap.get("product_no")));
        }
        return userRoleMap;
    }




    public List<String> getListString(List<SysDictDataRef> sysDictDataRefs){
        String s  = "";
        for (SysDictDataRef sysDictDataRef : sysDictDataRefs) {
            s = s+"_"+sysDictDataRef.getDictAllCode();
        }
        List<String> pDictValues = Arrays.asList(s.split("_"));
        return pDictValues;
    }


    public List<DictValueVO> duplicate(List<DictValueVO> data){
        for (DictValueVO datum : data) {
            datum.setAllCode("aaa");
        }
        List<DictValueVO> collect = data.stream().distinct().collect(Collectors.toList());
        return collect;
    }
    /**
     * 去重
     *
     * @param list 列表
     */
    public static List<DictValueVO> methodquchong(List<DictValueVO> list) {
        return list.stream().distinct().collect(Collectors.toList());

    }

}
