package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.kf.KfCompanyInfo;

import java.util.List;

/**
 * 客服-公司管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-10
 */
public interface KfCompanyInfoService
{
    /**
     * 查询客服-公司管理
     * 
     * @param id 客服-公司管理主键
     * @return 客服-公司管理
     */
    public KfCompanyInfo selectKfCompanyInfoById(Long id);

    /**
     * 查询客服-公司管理列表
     * 
     * @param kfCompanyInfo 客服-公司管理
     * @return 客服-公司管理集合
     */
    public List<KfCompanyInfo> selectKfCompanyInfoList(KfCompanyInfo kfCompanyInfo);

    /**
     * 新增客服-公司管理
     * 
     * @param kfCompanyInfo 客服-公司管理
     * @return 结果
     */
    public AjaxResult insertKfCompanyInfo(KfCompanyInfo kfCompanyInfo);

    /**
     * 修改客服-公司管理
     * 
     * @param kfCompanyInfo 客服-公司管理
     * @return 结果
     */
    public AjaxResult updateKfCompanyInfo(KfCompanyInfo kfCompanyInfo);

//    /**
//     * 批量删除客服-公司管理
//     *
//     * @param ids 需要删除的客服-公司管理主键集合
//     * @return 结果
//     */
//    public AjaxResult deleteKfCompanyInfoByIds(Long[] ids);

    /**
     * 删除客服-公司管理信息
     * 
     * @param id 客服-公司管理主键
     * @return 结果
     */
    public AjaxResult deleteKfCompanyInfoById(Long id);

    /**
     * 查询公司字典
     * @return 返回
     */
    AjaxResult selectCompanyDict();

}
