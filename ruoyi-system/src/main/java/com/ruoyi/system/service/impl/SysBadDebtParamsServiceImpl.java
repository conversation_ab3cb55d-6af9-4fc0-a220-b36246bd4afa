package com.ruoyi.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.domain.SysBadDebtParams;
import com.ruoyi.system.domain.SysBadDebtParamsRef;
import com.ruoyi.system.mapper.SysBadDebtParamsMapper;
import com.ruoyi.system.service.ISysBadDebtParamsService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-01-17
 */
@Service
public class SysBadDebtParamsServiceImpl implements ISysBadDebtParamsService
{
    @Autowired
    private SysBadDebtParamsMapper sysBadDebtParamsMapper;


    private static String REDUCE7_RULE_OF_25 = "3天内代偿（含3天）";
    private static String REDUCE7_RULE_OF_40 = "15天内代偿（含15天）";
    private static String REDUCE7_RULE_OF_55 = "30天内代偿（含30天）";
    private static String REDUCE7_RULE_OF_70 = "45天内代偿（含45天）";
    private static String REDUCE7_RULE_OF_80 = "60天内代偿（含60天）";
    private static String REDUCE7_RULE_OF_95 = "60天以上代偿";

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SysBadDebtParams selectSysBadDebtParamsById(Long id)
    {
        SysBadDebtParams sysBadDebtParams = sysBadDebtParamsMapper.selectSysBadDebtParamsById(id);
        List<SysBadDebtParamsRef> sysBadDebtParamsRef = sysBadDebtParamsMapper.selectSysBadDebtParamsRefByBadDebtId(id);
        sysBadDebtParams.setBadDebtList(sysBadDebtParamsRef);
        if (StringUtils.EMPTY.equals(sysBadDebtParams.getFundNo())) {
            sysBadDebtParams.setFundNo("ZIJINFANGNOTDEFINITION");
        }
        return sysBadDebtParams;
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sysBadDebtParams 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    @DataScope()
    public List<SysBadDebtParams> selectSysBadDebtParamsList(SysBadDebtParams sysBadDebtParams)
    {

        List<String> platforms = null;
        if (Strings.isNotEmpty(sysBadDebtParams.getPlatformNo())) {
            platforms = Arrays.asList(sysBadDebtParams.getPlatformNo().split(","));
        }
        List<String> custNos = null;
        if (Strings.isNotEmpty(sysBadDebtParams.getCustNo())) {
            custNos = Arrays.asList(sysBadDebtParams.getCustNo().split(","));
        }
        List<String> partnerNos = null;
        if (Strings.isNotEmpty(sysBadDebtParams.getPartnerNo())) {

            partnerNos = Arrays.asList(sysBadDebtParams.getPartnerNo().split(","));
        }
        List<String> fundNos = null;
        if (Strings.isNotEmpty(sysBadDebtParams.getFundNo())) {

            fundNos = Arrays.asList(sysBadDebtParams.getFundNo().split(","));
        }
        List<String> productCode = this.getProductCode(sysBadDebtParams);
        PageUtils.startPage();
        List<SysBadDebtParams> sysBadDebtParamsList = sysBadDebtParamsMapper.selectBadParamList(platforms, custNos, partnerNos, fundNos,productCode, sysBadDebtParams);
        List<SysBadDebtParamsRef> sysBadDebtParamsRef = sysBadDebtParamsMapper.selectAllSysBadDebtParamsRef();
        Date nowDate = DateUtils.getNowDate();
        for (SysBadDebtParams sbdp:sysBadDebtParamsList) {
            //判断是查询的关联表中是否有符合的日期存在
            List<SysBadDebtParamsRef> refList = sysBadDebtParamsRef.stream().filter(t -> t.getBadDebtId().equals(sbdp.getId())).filter(t -> DateUtil.isIn(nowDate, DateUtils.parseDate(t.getEffectiveTime()), DateUtils.parseDate(t.getFailureTime())) == true).collect(Collectors.toList());
            if (refList.size() == 0) {
                //说明没有符合条件的关联表在，那么选最后一个设定的关联表中的数据
                List<SysBadDebtParamsRef> collect = sysBadDebtParamsRef.stream().filter(t -> t.getBadDebtId().equals(sbdp.getId())).sorted(Comparator.comparing(SysBadDebtParamsRef::getFailureTime).reversed()).collect(Collectors.toList());
                if (collect.size() != 0) {
                    sbdp.setIsRepay8Data(collect.get(0).getIsRepay8Data());
                    sbdp.setReduce7Rule(collect.get(0).getReduce7Rule());
                    sbdp.setBadDebtRate(collect.get(0).getBadDebtRate());
                    sbdp.setaValueParam(collect.get(0).getaValueParam());
                } else {
                    sbdp.setIsRepay8Data(null);
                    sbdp.setReduce7Rule(null);
                    sbdp.setBadDebtRate(null);
                    sbdp.setaValueParam(null);
                }
//                sbdp.setBadDebtList(collect);
            }
            if (refList.size() == 1) {
                sbdp.setIsRepay8Data(refList.get(0).getIsRepay8Data());
                sbdp.setReduce7Rule(refList.get(0).getReduce7Rule());
                sbdp.setBadDebtRate(refList.get(0).getBadDebtRate());
                sbdp.setaValueParam(refList.get(0).getaValueParam());
//                sbdp.setBadDebtList(refList);
            }
            if (refList.size() > 1) {
                List<SysBadDebtParamsRef> collect = refList.stream().sorted(Comparator.comparing(SysBadDebtParamsRef::getFailureTime).reversed()).collect(Collectors.toList());
                sbdp.setIsRepay8Data(collect.get(0).getIsRepay8Data());
                sbdp.setReduce7Rule(collect.get(0).getReduce7Rule());
                sbdp.setBadDebtRate(collect.get(0).getBadDebtRate());
                sbdp.setaValueParam(collect.get(0).getaValueParam());
//                sbdp.setBadDebtList(refList.stream().sorted(Comparator.comparing(SysBadDebtParamsRef::getFailureTime).reversed()).collect(Collectors.toList()));
            }
        }
        return sysBadDebtParamsList;
    }

    public List<String> getProductCode(SysBadDebtParams sysBadDebtParams){
        List<String> platforms = null;
        if (Strings.isNotEmpty(sysBadDebtParams.getPlatformNo())) {
            platforms = Arrays.asList(sysBadDebtParams.getPlatformNo().split(","));
        }
        List<String> custNos = null;
        if (Strings.isNotEmpty(sysBadDebtParams.getCustNo())) {
            custNos = Arrays.asList(sysBadDebtParams.getCustNo().split(","));
        }
        List<String> partnerNos = null;
        if (Strings.isNotEmpty(sysBadDebtParams.getPartnerNo())) {

            partnerNos = Arrays.asList(sysBadDebtParams.getPartnerNo().split(","));
        }
        List<String> fundNos = null;
        if (Strings.isNotEmpty(sysBadDebtParams.getFundNo())) {

            fundNos = Arrays.asList(sysBadDebtParams.getFundNo().split(","));
        }
        List<String> products = null;
        if (Strings.isNotEmpty(sysBadDebtParams.getProductNo())) {
            products = Arrays.asList(sysBadDebtParams.getProductNo().split(","));
        }
        List<String> returnList = new ArrayList<>();
        List <String> productCodeList =   sysBadDebtParamsMapper.selectProductCodeList(platforms, custNos, partnerNos, fundNos);
        //并集
        if(null != products){
            returnList.addAll(products);
        }
        if(null != productCodeList){
            returnList.addAll(productCodeList);
        }
        List<String> listAllDistinct = new ArrayList<>();
        // 去重并集
        if(null != productCodeList){
            listAllDistinct = returnList.stream().distinct().collect(Collectors.toList());
        }

        return listAllDistinct;
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param sysBadDebtParams 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSysBadDebtParams(SysBadDebtParams sysBadDebtParams, LoginUser loginUser)
    {
        String userName = loginUser.getUsername();
        sysBadDebtParams.setCreateTime(DateUtils.getNowDate());
        if ("ZIJINFANGNOTDEFINITION".equals(sysBadDebtParams.getFundNo())) {
            sysBadDebtParams.setFundNo(StringUtils.EMPTY);
        }
        int i = sysBadDebtParamsMapper.insertSysBadDebtParams(sysBadDebtParams);
        Long id = sysBadDebtParams.getId();
        List<SysBadDebtParamsRef> badDebtList = sysBadDebtParams.getBadDebtList();
        //是否有追偿数据进行过滤
        List<SysBadDebtParamsRef> noCompensatoryList = badDebtList.stream().filter(t -> "N".equals(t.getIsRepay8Data())).collect(Collectors.toList());
        List<SysBadDebtParamsRef> haveCompensatoryList = badDebtList.stream().filter(t -> "Y".equals(t.getIsRepay8Data())).collect(Collectors.toList());
        //没有追偿数据的进行组装赋值
        noCompensatoryList.forEach(t -> {
            if ("25".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_25);
            } else if ("40".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_40);
            } else if ("55".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_55);
            } else if ("70".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_70);
            } else if ("80".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_80);
            } else if ("95".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_95);
            }
        });
        //有追偿数据的进行组装赋值
        haveCompensatoryList.forEach(t -> {
            t.setaValueParam(null);
            t.setReduce7Rule(null);
        });
        Date nowDate = DateUtils.getNowDate();
        //给所有的数据进行基本数据填充
        badDebtList.forEach(t -> {
            t.setBadDebtId(id);
            t.setCreateBy(userName);
            t.setCreateTime(nowDate);
            t.setUpdateBy(userName);
            t.setUpdateTime(nowDate);
            t.setStatus("0");
        });
        //新增
        for (SysBadDebtParamsRef sbdpr:badDebtList) {
            int i1 = sysBadDebtParamsMapper.insertSysBadDebtParamsRef(sbdpr);
        }
        return i;
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param sysBadDebtParams 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSysBadDebtParams(SysBadDebtParams sysBadDebtParams, LoginUser loginUser)
    {
        String userName = loginUser.getUsername();
//        System.out.println("sysBadDebtParams = " + sysBadDebtParams);
        List<SysBadDebtParamsRef> badDebtList = sysBadDebtParams.getBadDebtList();
        //是否有追偿数据进行过滤
        List<SysBadDebtParamsRef> noCompensatoryList = badDebtList.stream().filter(t -> "N".equals(t.getIsRepay8Data())).collect(Collectors.toList());
        List<SysBadDebtParamsRef> haveCompensatoryList = badDebtList.stream().filter(t -> "Y".equals(t.getIsRepay8Data())).collect(Collectors.toList());
        //没有追偿数据的进行组装赋值
        noCompensatoryList.forEach(t -> {
            if ("25".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_25);
            } else if ("40".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_40);
            } else if ("55".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_55);
            } else if ("70".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_70);
            } else if ("80".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_80);
            } else if ("95".equals(t.getaValueParam())) {
                t.setReduce7Rule(REDUCE7_RULE_OF_95);
            }
        });
        //有追偿数据的进行组装赋值
        haveCompensatoryList.forEach(t -> {
            t.setaValueParam(null);
            t.setReduce7Rule(null);
        });
        Date nowDate = DateUtils.getNowDate();
        //给所有的数据进行基本数据填充
        badDebtList.forEach(t -> {
            t.setBadDebtId(sysBadDebtParams.getId());
            t.setUpdateBy(userName);
            t.setUpdateTime(nowDate);
            t.setStatus("0");
            if (t.getCreateTime() == null) {
                t.setCreateBy(userName);
                t.setCreateTime(nowDate);
            }
        });
        //先删除
        int i = sysBadDebtParamsMapper.deleteSysBadDebtParamsRefByBadDebtId(sysBadDebtParams.getId());
        //再新增
        for (SysBadDebtParamsRef sbdpr:badDebtList) {
            int i1 = sysBadDebtParamsMapper.insertSysBadDebtParamsRef(sbdpr);
        }
        sysBadDebtParams.setUpdateTime(DateUtils.getNowDate());
        if ("ZIJINFANGNOTDEFINITION".equals(sysBadDebtParams.getFundNo())) {
            sysBadDebtParams.setFundNo(StringUtils.EMPTY);
        }
        return sysBadDebtParamsMapper.updateSysBadDebtParams(sysBadDebtParams);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysBadDebtParamsByIds(Long[] ids)
    {
        return sysBadDebtParamsMapper.deleteSysBadDebtParamsByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSysBadDebtParamsById(Long id)
    {
        return sysBadDebtParamsMapper.deleteSysBadDebtParamsById(id);
    }

    @Override
    public Boolean checkSysBadDebtParams(SysBadDebtParams sysBadDebtParams) {
        SysBadDebtParams obj = sysBadDebtParamsMapper.selectSysBadDebtByPlatformNoAndCustNoAndPartnerNoAndFundNoAndProductNo(sysBadDebtParams);
        if (obj == null) {
            return true;
        } else {
            return false;
        }
    }
}
